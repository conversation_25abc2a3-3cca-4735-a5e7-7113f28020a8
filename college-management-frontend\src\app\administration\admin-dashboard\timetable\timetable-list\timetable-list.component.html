<div class="timetable-list-container">
  <!-- Header -->
  <div class="page-header">
    <div class="header-content">
      <h1>
        <mat-icon>schedule</mat-icon>
        Current Timetables
      </h1>
      <p class="subtitle">Manage class schedules and timetable entries</p>
    </div>
    <div class="header-actions">
      <button mat-raised-button color="primary" (click)="createTimetable()" class="create-btn">
        <mat-icon>add</mat-icon>
        Create Timetable Entry
      </button>
    </div>
  </div>

  <!-- Filters Section -->
  <mat-card class="filters-card">
    <mat-card-content>
      <div class="filters-grid">
        <!-- Search -->
        <mat-form-field appearance="outline" class="search-field">
          <mat-label>Search</mat-label>
          <input matInput [(ngModel)]="searchQuery" (input)="applyFilters()" 
                 placeholder="Search by subject, teacher, program...">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <!-- Program Filter -->
        <mat-form-field appearance="outline">
          <mat-label>Program</mat-label>
          <mat-select [(ngModel)]="selectedProgram" (selectionChange)="applyFilters()">
            <mat-option value="">All Programs</mat-option>
            <mat-option *ngFor="let program of programs" [value]="program._id">
              {{ program.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- Department Filter -->
        <mat-form-field appearance="outline">
          <mat-label>Department</mat-label>
          <mat-select [(ngModel)]="selectedDepartment" (selectionChange)="applyFilters()">
            <mat-option value="">All Departments</mat-option>
            <mat-option *ngFor="let dept of departments" [value]="dept._id">
              {{ dept.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- Class Filter -->
        <mat-form-field appearance="outline">
          <mat-label>Class</mat-label>
          <mat-select [(ngModel)]="selectedClass" (selectionChange)="applyFilters()">
            <mat-option value="">All Classes</mat-option>
            <mat-option *ngFor="let cls of classes" [value]="cls._id">
              {{ getClassDisplayName(cls) }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- Day Filter -->
        <mat-form-field appearance="outline">
          <mat-label>Day</mat-label>
          <mat-select [(ngModel)]="selectedDay" (selectionChange)="applyFilters()">
            <mat-option value="">All Days</mat-option>
            <mat-option *ngFor="let day of daysOfWeek" [value]="day">
              {{ day }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- Semester Filter -->
        <mat-form-field appearance="outline">
          <mat-label>Semester</mat-label>
          <mat-select [(ngModel)]="selectedSemester" (selectionChange)="applyFilters()">
            <mat-option value="">All Semesters</mat-option>
            <mat-option *ngFor="let sem of semesters" [value]="sem">
              {{ sem }}{{ sem === 1 ? 'st' : sem === 2 ? 'nd' : sem === 3 ? 'rd' : 'th' }} Semester
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- Clear Filters -->
        <button mat-stroked-button (click)="clearFilters()" class="clear-filters-btn">
          <mat-icon>clear</mat-icon>
          Clear Filters
        </button>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Results Summary -->
  <div class="results-summary">
    <span class="results-count">
      Showing {{ paginatedTimetables.length }} of {{ totalItems }} timetable entries
    </span>
  </div>

  <!-- Timetable List -->
  <mat-card class="timetable-card">
    <mat-card-content>
      <div class="table-container">
        <table class="timetable-table">
          <thead>
            <tr>
              <th>Subject</th>
              <th>Teacher</th>
              <th>Class</th>
              <th>Day & Time</th>
              <th>Room</th>
              <th>Program</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let timetable of paginatedTimetables" 
                [class.inactive-row]="!timetable.isActive">
              <td class="subject-cell">
                <div class="subject-info">
                  <span class="subject-name">{{ timetable.subject?.subjectName || 'N/A' }}</span>
                  <span class="subject-code">{{ timetable.subject?.code || '' }}</span>
                </div>
              </td>
              <td class="teacher-cell">
                <div class="teacher-info">
                  <span class="teacher-name">{{ timetable.teacher?.name || 'N/A' }}</span>
                  <span class="teacher-email">{{ timetable.teacher?.email || '' }}</span>
                </div>
              </td>
              <td class="class-cell">
                <div class="class-info">
                  <span class="class-name">{{ getClassDisplayName(timetable.class) }}</span>
                  <span class="semester">Semester {{ timetable.semester }}</span>
                </div>
              </td>
              <td class="schedule-cell">
                <div class="schedule-info">
                  <span class="day">{{ timetable.dayOfWeek }}</span>
                  <span class="time">{{ getTimeDisplay(timetable.timeSlot) }}</span>
                </div>
              </td>
              <td class="room-cell">
                <span class="room">{{ timetable.room || 'Not specified' }}</span>
              </td>
              <td class="program-cell">
                <div class="program-info">
                  <span class="program-name">{{ timetable.program?.name || 'N/A' }}</span>
                  <span class="department-name">{{ timetable.department?.name || '' }}</span>
                </div>
              </td>
              <td class="status-cell">
                <mat-slide-toggle 
                  [checked]="timetable.isActive"
                  (change)="toggleActive(timetable)"
                  color="primary">
                </mat-slide-toggle>
                <span class="status-label">{{ timetable.isActive ? 'Active' : 'Inactive' }}</span>
              </td>
              <td class="actions-cell">
                <div class="action-buttons">
                  <button mat-icon-button color="primary" 
                          (click)="viewTimetable(timetable)"
                          matTooltip="View Details">
                    <mat-icon>visibility</mat-icon>
                  </button>
                  <button mat-icon-button color="accent" 
                          (click)="editTimetable(timetable)"
                          matTooltip="Edit">
                    <mat-icon>edit</mat-icon>
                  </button>
                  <button mat-icon-button color="warn" 
                          (click)="deleteTimetable(timetable)"
                          matTooltip="Delete">
                    <mat-icon>delete</mat-icon>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <!-- Loading State -->
        <div *ngIf="loading" class="loading-container">
          <mat-spinner diameter="50"></mat-spinner>
          <p>Loading timetables...</p>
        </div>

        <!-- Empty State -->
        <div *ngIf="!loading && filteredTimetables.length === 0" class="empty-state">
          <mat-icon class="empty-icon">schedule</mat-icon>
          <h3>No Timetable Entries Found</h3>
          <p>No timetable entries match your current filters.</p>
          <button mat-raised-button color="primary" (click)="createTimetable()">
            <mat-icon>add</mat-icon>
            Create First Entry
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Pagination -->
  <div class="pagination-container" *ngIf="totalPages > 1">
    <mat-paginator 
      [length]="totalItems"
      [pageSize]="itemsPerPage"
      [pageSizeOptions]="[5, 10, 25, 50]"
      [pageIndex]="currentPage - 1"
      (page)="goToPage($event.pageIndex + 1)"
      showFirstLastButtons>
    </mat-paginator>
  </div>
</div>
