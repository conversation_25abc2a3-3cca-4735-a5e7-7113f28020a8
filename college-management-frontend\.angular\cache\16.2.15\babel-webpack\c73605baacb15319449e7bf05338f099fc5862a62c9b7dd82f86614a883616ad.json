{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/role.service\";\nimport * as i4 from \"src/app/services/user.service\";\nimport * as i5 from \"@angular/common\";\nfunction LoginComponent_span_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sign in\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_span_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"span\", 31);\n    i0.ɵɵtext(2, \" Signing in... \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let LoginComponent = /*#__PURE__*/(() => {\n  class LoginComponent {\n    constructor(fb, router, roleService, userService) {\n      this.fb = fb;\n      this.router = router;\n      this.roleService = roleService;\n      this.userService = userService;\n      this.showPassword = false;\n      this.loginError = ''; // To store error messages\n      this.loading = false;\n    }\n    ngOnInit() {\n      // Initialize form with validation\n      this.loginForm = this.fb.group({\n        email: ['', [Validators.required, Validators.email]],\n        password: ['', [Validators.required, Validators.minLength(6)]],\n        remember: [false]\n      });\n    }\n    onSubmit() {\n      // Reset the error message\n      this.loginError = '';\n      this.loading = true;\n      if (this.loginForm.valid) {\n        // Capture form values\n        const {\n          email,\n          password\n        } = this.loginForm.value;\n        // Call the UserService's login method\n        this.userService.login({\n          email,\n          password\n        }).subscribe({\n          next: res => {\n            if (res.success) {\n              let role = res.user.role;\n              console.log(role);\n              this.loading = false;\n              // Check role and perform role-based login\n              if (role === 'Principal') {\n                this.roleService.setRole('Principal');\n                console.log('Principal logged in');\n              } else if (role === 'Teacher') {\n                this.roleService.setRole('teacher');\n                console.log('Teacher logged in');\n              } else if (role === 'Student') {\n                this.roleService.setRole('student');\n                console.log('Student logged in');\n              } else {\n                this.loginError = 'Invalid credentials for the selected role';\n                console.log('Invalid credentials');\n                return; // Stop further execution\n              }\n              // Redirect to the dashboard if role is set\n              this.router.navigate(['/dashboard']);\n            } else {\n              this.loginError = res.message || 'Login failed';\n              Swal.fire({\n                icon: 'error',\n                title: 'Login Failed',\n                text: this.loginError\n              });\n              this.loading = false;\n            }\n          },\n          error: error => {\n            console.error('Login failed', error);\n            Swal.fire({\n              icon: 'error',\n              title: 'Login Failed',\n              text: error.error.message\n            });\n            this.loginError = 'Server error, please try again later.';\n            this.loading = false;\n          }\n        });\n      } else {\n        // If the form is invalid, display an error message\n        this.loginError = 'Please fill in all fields correctly';\n        console.log('Form is invalid');\n        this.loading = false;\n      }\n    }\n    // Toggle the visibility of the password\n    togglePasswordVisibility() {\n      this.showPassword = !this.showPassword;\n    }\n    static #_ = this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.RoleService), i0.ɵɵdirectiveInject(i4.UserService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 48,\n      vars: 6,\n      consts: [[1, \"container-fluid\"], [1, \"row\", \"vh-100\"], [1, \"col-lg-5\", \"d-flex\", \"flex-column\", \"justify-content-center\", \"align-items-center\", \"p-3\", \"bg-white\"], [1, \"w-100\", 2, \"max-width\", \"400px\"], [1, \"mb-4\"], [\"src\", \"../../../assets/images/logo.jpeg\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"mb-3\"], [\"for\", \"email\", 1, \"form-label\"], [\"type\", \"email\", \"id\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter your email\", 1, \"form-control\"], [1, \"mb-3\", \"position-relative\"], [\"for\", \"password\", 1, \"form-label\"], [\"id\", \"password\", \"formControlName\", \"password\", \"placeholder\", \"Enter your password\", 1, \"form-control\", 3, \"type\"], [1, \"fas\", \"viewpassword\", 3, \"ngClass\", \"click\"], [1, \"form-check\", \"mb-3\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"type\", \"checkbox\", \"formControlName\", \"remember\", \"id\", \"remember\", 1, \"form-check-input\"], [\"for\", \"remember\", 1, \"form-check-label\"], [\"routerLink\", \"/auth/forgot-password\", 1, \"mt-0\"], [1, \"forgot\", 2, \"cursor\", \"pointer\"], [1, \"d-grid\", \"gap-2\"], [\"type\", \"submit\", 1, \"btn\", \"submit\", 3, \"disabled\"], [4, \"ngIf\"], [1, \"mt-3\"], [\"routerLink\", \"/auth/sign-up\"], [1, \"col-lg-7\", \"d-none\", \"d-lg-flex\", \"flex-column\", \"align-items-start\", \"bg-light\", \"position-relative\"], [1, \"text-start\", \"p-5\", \"w-100\"], [1, \"blockquote\"], [1, \"blockquote-footer\"], [\"title\", \"Source Title\"], [1, \"image-container\"], [\"src\", \"../../../assets/images/background.jpeg\", \"alt\", \"Stats graph\", 1, \"img-fluid\", \"position-absolute\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h2\", 4);\n          i0.ɵɵelement(5, \"img\", 5);\n          i0.ɵɵtext(6, \" GPGC (Swabi)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"h1\", 4);\n          i0.ɵɵtext(8, \"Log in\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"p\");\n          i0.ɵɵtext(10, \"Welcome back! Please enter your details.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"form\", 6);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_11_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(12, \"div\", 7)(13, \"label\", 8);\n          i0.ɵɵtext(14, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(15, \"input\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 10)(17, \"label\", 11);\n          i0.ɵɵtext(18, \"Password(123456)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(19, \"input\", 12);\n          i0.ɵɵelementStart(20, \"i\", 13);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_i_click_20_listener() {\n            return ctx.togglePasswordVisibility();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"div\", 14)(22, \"div\");\n          i0.ɵɵelement(23, \"input\", 15);\n          i0.ɵɵelementStart(24, \"label\", 16);\n          i0.ɵɵtext(25, \"Remember me\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"p\", 17)(27, \"a\", 18);\n          i0.ɵɵtext(28, \"Forgot password?\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(29, \"div\", 19)(30, \"button\", 20);\n          i0.ɵɵtemplate(31, LoginComponent_span_31_Template, 2, 0, \"span\", 21);\n          i0.ɵɵtemplate(32, LoginComponent_span_32_Template, 3, 0, \"span\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"p\", 22);\n          i0.ɵɵtext(34, \"Don't have an account? \");\n          i0.ɵɵelementStart(35, \"a\", 23);\n          i0.ɵɵtext(36, \"Sign up\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(37, \"div\", 24)(38, \"div\", 25)(39, \"blockquote\", 26)(40, \"h2\", 4);\n          i0.ɵɵtext(41, \"College management system Login page\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"footer\", 27);\n          i0.ɵɵtext(43, \"Name\");\n          i0.ɵɵelementStart(44, \"cite\", 28);\n          i0.ɵɵtext(45, \"Owner ~ GPGC SWABI\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(46, \"div\", 29);\n          i0.ɵɵelement(47, \"img\", 30);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"type\", ctx.showPassword ? \"text\" : \"password\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngClass\", ctx.showPassword ? \"fa-eye-slash\" : \"fa-eye\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"disabled\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgIf, i2.RouterLink, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n      styles: [\"body[_ngcontent-%COMP%], html[_ngcontent-%COMP%]{margin:0;padding:0;overflow-x:hidden;height:100%}.image-container[_ngcontent-%COMP%]{width:100%;height:100%;position:relative}.image-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:80%;height:auto;position:absolute;right:0;bottom:0;object-fit:contain;border-radius:20px}.submit[_ngcontent-%COMP%]{background-color:#29578c;color:#fff}.forgot[_ngcontent-%COMP%]{color:#29578c;text-decoration:none;font-weight:700}a[_ngcontent-%COMP%]{cursor:pointer;color:#29578c;font-weight:700}.viewpassword[_ngcontent-%COMP%]{position:absolute;right:10px;transform:translateY(-50%);cursor:pointer;bottom:7%}\"]\n    });\n  }\n  return LoginComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}