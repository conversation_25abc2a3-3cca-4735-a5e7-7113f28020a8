{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { MainComponent } from './main/main.component';\nimport { ChartsComponent } from './charts/charts.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: MainComponent,\n  children: [{\n    path: '',\n    component: ChartsComponent\n  }, {\n    path: 'admin',\n    loadChildren: () => import('./admin-dashboard/admin-dashboard.module').then(m => m.AdminDashboardModule)\n  }, {\n    path: 'teacher',\n    loadChildren: () => import('./teacher-dashboard/teacher-dashboard.module').then(m => m.TeacherDashboardModule)\n  }, {\n    path: 'student',\n    loadChildren: () => import('./student-dashboard/student-dashboard.module').then(m => m.StudentDashboardModule)\n  }]\n}];\nexport let AdministrationRoutingModule = /*#__PURE__*/(() => {\n  class AdministrationRoutingModule {\n    static #_ = this.ɵfac = function AdministrationRoutingModule_Factory(t) {\n      return new (t || AdministrationRoutingModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AdministrationRoutingModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n  return AdministrationRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}