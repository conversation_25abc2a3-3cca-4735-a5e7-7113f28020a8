{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\n// Angular Material Modules\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatChipsModule } from '@angular/material/chips';\n// Components\nimport { TimetableListComponent } from './timetable-list/timetable-list.component';\nimport { TimetableCreateComponent } from './timetable-create/timetable-create.component';\nimport { TimetableComponent } from './timetable.component';\n// Routing\nimport { TimetableRoutingModule } from './timetable-routing.module';\nimport * as i0 from \"@angular/core\";\nexport class TimetableModule {\n  static #_ = this.ɵfac = function TimetableModule_Factory(t) {\n    return new (t || TimetableModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: TimetableModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, ReactiveFormsModule, FormsModule, TimetableRoutingModule,\n    // Angular Material\n    MatCardModule, MatButtonModule, MatIconModule, MatFormFieldModule, MatInputModule, MatSelectModule, MatDatepickerModule, MatNativeDateModule, MatTableModule, MatPaginatorModule, MatSortModule, MatProgressSpinnerModule, MatSnackBarModule, MatTooltipModule, MatSlideToggleModule, MatChipsModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(TimetableModule, {\n    declarations: [TimetableListComponent, TimetableCreateComponent, TimetableComponent],\n    imports: [CommonModule, ReactiveFormsModule, FormsModule, TimetableRoutingModule,\n    // Angular Material\n    MatCardModule, MatButtonModule, MatIconModule, MatFormFieldModule, MatInputModule, MatSelectModule, MatDatepickerModule, MatNativeDateModule, MatTableModule, MatPaginatorModule, MatSortModule, MatProgressSpinnerModule, MatSnackBarModule, MatTooltipModule, MatSlideToggleModule, MatChipsModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "FormsModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatInputModule", "MatSelectModule", "MatDatepickerModule", "MatNativeDateModule", "MatTableModule", "MatPaginatorModule", "MatSortModule", "MatProgressSpinnerModule", "MatSnackBarModule", "MatTooltipModule", "MatSlideToggleModule", "MatChipsModule", "TimetableListComponent", "TimetableCreateComponent", "TimetableComponent", "TimetableRoutingModule", "TimetableModule", "_", "_2", "_3", "declarations", "imports"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\timetable\\timetable.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\r\n\r\n// Angular Material Modules\r\nimport { MatCardModule } from '@angular/material/card';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { MatSelectModule } from '@angular/material/select';\r\nimport { MatDatepickerModule } from '@angular/material/datepicker';\r\nimport { MatNativeDateModule } from '@angular/material/core';\r\nimport { MatTableModule } from '@angular/material/table';\r\nimport { MatPaginatorModule } from '@angular/material/paginator';\r\nimport { MatSortModule } from '@angular/material/sort';\r\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\r\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\r\nimport { MatTooltipModule } from '@angular/material/tooltip';\r\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\r\nimport { MatChipsModule } from '@angular/material/chips';\r\n\r\n// Components\r\nimport { TimetableListComponent } from './timetable-list/timetable-list.component';\r\nimport { TimetableCreateComponent } from './timetable-create/timetable-create.component';\r\nimport { TimetableComponent } from './timetable.component';\r\n\r\n// Routing\r\nimport { TimetableRoutingModule } from './timetable-routing.module';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    TimetableListComponent,\r\n    TimetableCreateComponent,\r\n    TimetableComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    FormsModule,\r\n    TimetableRoutingModule,\r\n    \r\n    // Angular Material\r\n    MatCardModule,\r\n    MatButtonModule,\r\n    MatIconModule,\r\n    MatFormFieldModule,\r\n    MatInputModule,\r\n    MatSelectModule,\r\n    MatDatepickerModule,\r\n    MatNativeDateModule,\r\n    MatTableModule,\r\n    MatPaginatorModule,\r\n    MatSortModule,\r\n    MatProgressSpinnerModule,\r\n    MatSnackBarModule,\r\n    MatTooltipModule,\r\n    MatSlideToggleModule,\r\n    MatChipsModule\r\n  ]\r\n})\r\nexport class TimetableModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AAEjE;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,cAAc,QAAQ,yBAAyB;AAExD;AACA,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,kBAAkB,QAAQ,uBAAuB;AAE1D;AACA,SAASC,sBAAsB,QAAQ,4BAA4B;;AAiCnE,OAAM,MAAOC,eAAe;EAAA,QAAAC,CAAA,G;qBAAfD,eAAe;EAAA;EAAA,QAAAE,EAAA,G;UAAfF;EAAe;EAAA,QAAAG,EAAA,G;cAxBxB1B,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXoB,sBAAsB;IAEtB;IACAnB,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,eAAe,EACfC,mBAAmB,EACnBC,mBAAmB,EACnBC,cAAc,EACdC,kBAAkB,EAClBC,aAAa,EACbC,wBAAwB,EACxBC,iBAAiB,EACjBC,gBAAgB,EAChBC,oBAAoB,EACpBC,cAAc;EAAA;;;2EAGLK,eAAe;IAAAI,YAAA,GA7BxBR,sBAAsB,EACtBC,wBAAwB,EACxBC,kBAAkB;IAAAO,OAAA,GAGlB5B,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXoB,sBAAsB;IAEtB;IACAnB,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,eAAe,EACfC,mBAAmB,EACnBC,mBAAmB,EACnBC,cAAc,EACdC,kBAAkB,EAClBC,aAAa,EACbC,wBAAwB,EACxBC,iBAAiB,EACjBC,gBAAgB,EAChBC,oBAAoB,EACpBC,cAAc;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}