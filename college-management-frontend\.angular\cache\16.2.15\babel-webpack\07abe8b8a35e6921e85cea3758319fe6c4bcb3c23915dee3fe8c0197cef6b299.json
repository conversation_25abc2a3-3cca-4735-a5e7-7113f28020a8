{"ast": null, "code": "import { ProgramDialogComponent } from './program-dialog/program-dialog.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/program.service\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/chips\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/progress-spinner\";\nimport * as i10 from \"@angular/material/table\";\nimport * as i11 from \"@angular/material/tooltip\";\nfunction ProgramsComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading programs...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProgramsComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"mat-icon\", 10);\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 4);\n    i0.ɵɵlistener(\"click\", function ProgramsComponent_div_13_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.refreshData());\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" Retry \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction ProgramsComponent_div_14_th_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 24);\n    i0.ɵɵtext(1, \"Program Code\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProgramsComponent_div_14_td_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 25)(1, \"span\", 26);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const program_r20 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(program_r20.name);\n  }\n}\nfunction ProgramsComponent_div_14_th_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 24);\n    i0.ɵɵtext(1, \"Full Name\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProgramsComponent_div_14_td_14_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const program_r21 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(program_r21.description);\n  }\n}\nfunction ProgramsComponent_div_14_td_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 25)(1, \"div\", 27)(2, \"span\", 28);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ProgramsComponent_div_14_td_14_span_4_Template, 2, 1, \"span\", 29);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const program_r21 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(program_r21.fullName);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", program_r21.description);\n  }\n}\nfunction ProgramsComponent_div_14_th_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 24);\n    i0.ɵɵtext(1, \"Duration\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProgramsComponent_div_14_td_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const program_r24 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", program_r24.duration, \" \", program_r24.durationUnit, \" \");\n  }\n}\nfunction ProgramsComponent_div_14_th_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 24);\n    i0.ɵɵtext(1, \"Semesters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProgramsComponent_div_14_td_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const program_r25 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", program_r25.totalSemesters, \" \");\n  }\n}\nfunction ProgramsComponent_div_14_th_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 24);\n    i0.ɵɵtext(1, \"Status\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProgramsComponent_div_14_td_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 25)(1, \"mat-chip-set\")(2, \"mat-chip\", 31);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const program_r26 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"color\", program_r26.isActive ? \"primary\" : \"warn\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", program_r26.isActive ? \"Active\" : \"Inactive\", \" \");\n  }\n}\nfunction ProgramsComponent_div_14_th_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 24);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProgramsComponent_div_14_td_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 25)(1, \"div\", 32)(2, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function ProgramsComponent_div_14_td_26_Template_button_click_2_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r29);\n      const program_r27 = restoredCtx.$implicit;\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.editProgram(program_r27));\n    });\n    i0.ɵɵelementStart(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function ProgramsComponent_div_14_td_26_Template_button_click_5_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r29);\n      const program_r27 = restoredCtx.$implicit;\n      const ctx_r30 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r30.toggleProgramStatus(program_r27));\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function ProgramsComponent_div_14_td_26_Template_button_click_8_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r29);\n      const program_r27 = restoredCtx.$implicit;\n      const ctx_r31 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r31.deleteProgram(program_r27));\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"delete\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const program_r27 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"matTooltip\", program_r27.isActive ? \"Deactivate Program\" : \"Activate Program\")(\"color\", program_r27.isActive ? \"warn\" : \"primary\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(program_r27.isActive ? \"toggle_off\" : \"toggle_on\");\n  }\n}\nfunction ProgramsComponent_div_14_tr_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 36);\n  }\n}\nfunction ProgramsComponent_div_14_tr_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 37);\n  }\n}\nfunction ProgramsComponent_div_14_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"school\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No Programs Found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Start by creating your first academic program.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 4);\n    i0.ɵɵlistener(\"click\", function ProgramsComponent_div_14_div_29_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r33 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r33.createProgram());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Create Program \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProgramsComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"mat-card\")(2, \"mat-card-header\")(3, \"mat-card-title\");\n    i0.ɵɵtext(4, \"All Programs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-card-subtitle\");\n    i0.ɵɵtext(6, \"Manage academic programs\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"table\", 12);\n    i0.ɵɵelementContainerStart(9, 13);\n    i0.ɵɵtemplate(10, ProgramsComponent_div_14_th_10_Template, 2, 0, \"th\", 14);\n    i0.ɵɵtemplate(11, ProgramsComponent_div_14_td_11_Template, 3, 1, \"td\", 15);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(12, 16);\n    i0.ɵɵtemplate(13, ProgramsComponent_div_14_th_13_Template, 2, 0, \"th\", 14);\n    i0.ɵɵtemplate(14, ProgramsComponent_div_14_td_14_Template, 5, 2, \"td\", 15);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(15, 17);\n    i0.ɵɵtemplate(16, ProgramsComponent_div_14_th_16_Template, 2, 0, \"th\", 14);\n    i0.ɵɵtemplate(17, ProgramsComponent_div_14_td_17_Template, 2, 2, \"td\", 15);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(18, 18);\n    i0.ɵɵtemplate(19, ProgramsComponent_div_14_th_19_Template, 2, 0, \"th\", 14);\n    i0.ɵɵtemplate(20, ProgramsComponent_div_14_td_20_Template, 2, 1, \"td\", 15);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(21, 19);\n    i0.ɵɵtemplate(22, ProgramsComponent_div_14_th_22_Template, 2, 0, \"th\", 14);\n    i0.ɵɵtemplate(23, ProgramsComponent_div_14_td_23_Template, 4, 2, \"td\", 15);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(24, 20);\n    i0.ɵɵtemplate(25, ProgramsComponent_div_14_th_25_Template, 2, 0, \"th\", 14);\n    i0.ɵɵtemplate(26, ProgramsComponent_div_14_td_26_Template, 11, 3, \"td\", 15);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(27, ProgramsComponent_div_14_tr_27_Template, 1, 0, \"tr\", 21);\n    i0.ɵɵtemplate(28, ProgramsComponent_div_14_tr_28_Template, 1, 0, \"tr\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(29, ProgramsComponent_div_14_div_29_Template, 11, 0, \"div\", 23);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"dataSource\", ctx_r2.programs);\n    i0.ɵɵadvance(19);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r2.displayedColumns);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r2.displayedColumns);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.programs.length === 0);\n  }\n}\nexport class ProgramsComponent {\n  constructor(programService, dialog, snackBar) {\n    this.programService = programService;\n    this.dialog = dialog;\n    this.snackBar = snackBar;\n    this.programs = [];\n    this.loading = true;\n    this.error = null;\n    this.displayedColumns = ['name', 'fullName', 'duration', 'totalSemesters', 'isActive', 'actions'];\n  }\n  ngOnInit() {\n    this.loadPrograms();\n  }\n  loadPrograms() {\n    this.loading = true;\n    this.error = null;\n    this.programService.getAllPrograms().subscribe({\n      next: response => {\n        if (response.success) {\n          this.programs = response.programs;\n        } else {\n          this.error = 'Failed to load programs';\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading programs:', error);\n        this.error = 'Error loading programs';\n        this.loading = false;\n      }\n    });\n  }\n  createProgram() {\n    const dialogRef = this.dialog.open(ProgramDialogComponent, {\n      width: '600px',\n      data: {}\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result?.success) {\n        this.snackBar.open('Program created successfully', 'Close', {\n          duration: 3000\n        });\n        this.loadPrograms();\n      } else if (result?.error) {\n        this.snackBar.open(result.error, 'Close', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  editProgram(program) {\n    const dialogRef = this.dialog.open(ProgramDialogComponent, {\n      width: '600px',\n      data: {\n        program\n      }\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result?.success) {\n        this.snackBar.open('Program updated successfully', 'Close', {\n          duration: 3000\n        });\n        this.loadPrograms();\n      } else if (result?.error) {\n        this.snackBar.open(result.error, 'Close', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  deleteProgram(program) {\n    if (confirm(`Are you sure you want to delete the program \"${program.name}\"?`)) {\n      this.programService.deleteProgram(program._id).subscribe({\n        next: response => {\n          if (response.success) {\n            this.snackBar.open('Program deleted successfully', 'Close', {\n              duration: 3000\n            });\n            this.loadPrograms();\n          } else {\n            this.snackBar.open('Failed to delete program', 'Close', {\n              duration: 3000\n            });\n          }\n        },\n        error: error => {\n          console.error('Error deleting program:', error);\n          this.snackBar.open('Error deleting program', 'Close', {\n            duration: 3000\n          });\n        }\n      });\n    }\n  }\n  toggleProgramStatus(program) {\n    const updatedProgram = {\n      ...program,\n      isActive: !program.isActive\n    };\n    this.programService.updateProgram(program._id, updatedProgram).subscribe({\n      next: response => {\n        if (response.success) {\n          this.snackBar.open(`Program ${updatedProgram.isActive ? 'activated' : 'deactivated'} successfully`, 'Close', {\n            duration: 3000\n          });\n          this.loadPrograms();\n        } else {\n          this.snackBar.open('Failed to update program status', 'Close', {\n            duration: 3000\n          });\n        }\n      },\n      error: error => {\n        console.error('Error updating program status:', error);\n        this.snackBar.open('Error updating program status', 'Close', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  refreshData() {\n    this.loadPrograms();\n  }\n  static #_ = this.ɵfac = function ProgramsComponent_Factory(t) {\n    return new (t || ProgramsComponent)(i0.ɵɵdirectiveInject(i1.ProgramService), i0.ɵɵdirectiveInject(i2.MatDialog), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProgramsComponent,\n    selectors: [[\"app-programs\"]],\n    decls: 15,\n    vars: 3,\n    consts: [[1, \"programs-container\"], [1, \"page-header\"], [1, \"header-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Refresh\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"table-container\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"error-container\"], [\"color\", \"warn\"], [1, \"table-container\"], [\"mat-table\", \"\", 1, \"programs-table\", 3, \"dataSource\"], [\"matColumnDef\", \"name\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"fullName\"], [\"matColumnDef\", \"duration\"], [\"matColumnDef\", \"totalSemesters\"], [\"matColumnDef\", \"isActive\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"class\", \"no-data\", 4, \"ngIf\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [1, \"program-code\"], [1, \"program-info\"], [1, \"program-name\"], [\"class\", \"program-description\", 4, \"ngIf\"], [1, \"program-description\"], [\"selected\", \"\", 3, \"color\"], [1, \"action-buttons\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Edit Program\", \"color\", \"primary\", 3, \"click\"], [\"mat-icon-button\", \"\", 3, \"matTooltip\", \"color\", \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Delete Program\", \"color\", \"warn\", 3, \"click\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"], [1, \"no-data\"]],\n    template: function ProgramsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n        i0.ɵɵtext(3, \"Programs Management\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\", 2)(5, \"button\", 3);\n        i0.ɵɵlistener(\"click\", function ProgramsComponent_Template_button_click_5_listener() {\n          return ctx.refreshData();\n        });\n        i0.ɵɵelementStart(6, \"mat-icon\");\n        i0.ɵɵtext(7, \"refresh\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(8, \"button\", 4);\n        i0.ɵɵlistener(\"click\", function ProgramsComponent_Template_button_click_8_listener() {\n          return ctx.createProgram();\n        });\n        i0.ɵɵelementStart(9, \"mat-icon\");\n        i0.ɵɵtext(10, \"add\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(11, \" Add Program \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(12, ProgramsComponent_div_12_Template, 4, 0, \"div\", 5);\n        i0.ɵɵtemplate(13, ProgramsComponent_div_13_Template, 9, 1, \"div\", 6);\n        i0.ɵɵtemplate(14, ProgramsComponent_div_14_Template, 30, 4, \"div\", 7);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(12);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error);\n      }\n    },\n    dependencies: [i4.NgIf, i5.MatButton, i5.MatIconButton, i6.MatCard, i6.MatCardContent, i6.MatCardHeader, i6.MatCardSubtitle, i6.MatCardTitle, i7.MatChip, i7.MatChipSet, i8.MatIcon, i9.MatProgressSpinner, i10.MatTable, i10.MatHeaderCellDef, i10.MatHeaderRowDef, i10.MatColumnDef, i10.MatCellDef, i10.MatRowDef, i10.MatHeaderCell, i10.MatCell, i10.MatHeaderRow, i10.MatRow, i11.MatTooltip],\n    styles: [\".programs-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background-color: #f5f5f5;\\n  min-height: 100vh;\\n}\\n\\n.page-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 30px;\\n  background: white;\\n  padding: 20px;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\\n}\\n\\n.page-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #333;\\n  font-weight: 500;\\n}\\n\\n.header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  align-items: center;\\n}\\n\\n.header-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.loading-container[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 400px;\\n  text-align: center;\\n  background: white;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\\n}\\n\\n.error-container[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  margin-bottom: 16px;\\n}\\n\\n.table-container[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\\n}\\n\\n.programs-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.program-code[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #1976d2;\\n  background-color: #e3f2fd;\\n  padding: 4px 8px;\\n  border-radius: 4px;\\n  font-size: 0.9rem;\\n}\\n\\n.program-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n}\\n\\n.program-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n}\\n\\n.program-description[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  color: #666;\\n  font-style: italic;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 4px;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 40px;\\n}\\n\\n.no-data[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 60px 20px;\\n  text-align: center;\\n  color: #666;\\n}\\n\\n.no-data[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  height: 64px;\\n  width: 64px;\\n  margin-bottom: 16px;\\n  color: #ccc;\\n}\\n\\n.no-data[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #333;\\n}\\n\\n.no-data[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 24px 0;\\n  color: #666;\\n}\\n\\n.no-data[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n\\n\\n.mat-table[_ngcontent-%COMP%] {\\n  background: transparent;\\n}\\n\\n.mat-header-cell[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #333;\\n  border-bottom: 2px solid #e0e0e0;\\n}\\n\\n.mat-cell[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n\\n.mat-row[_ngcontent-%COMP%]:hover {\\n  background-color: #f9f9f9;\\n}\\n\\n\\n\\n.mat-chip-set[_ngcontent-%COMP%] {\\n  margin: 0;\\n}\\n\\n.mat-chip[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  min-height: 24px;\\n  line-height: 24px;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .programs-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  \\n  .page-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 16px;\\n    align-items: stretch;\\n  }\\n  \\n  .header-actions[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n  \\n  .programs-table[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  \\n  .action-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 2px;\\n  }\\n}\\nth[_ngcontent-%COMP%]{\\n  color: green !important;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["ProgramDialogComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ProgramsComponent_div_13_Template_button_click_5_listener", "ɵɵrestoreView", "_r4", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "refreshData", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "error", "program_r20", "name", "program_r21", "description", "ɵɵtemplate", "ProgramsComponent_div_14_td_14_span_4_Template", "fullName", "ɵɵproperty", "ɵɵtextInterpolate2", "program_r24", "duration", "durationUnit", "ɵɵtextInterpolate1", "program_r25", "totalSemesters", "program_r26", "isActive", "ProgramsComponent_div_14_td_26_Template_button_click_2_listener", "restoredCtx", "_r29", "program_r27", "$implicit", "ctx_r28", "editProgram", "ProgramsComponent_div_14_td_26_Template_button_click_5_listener", "ctx_r30", "toggleProgramStatus", "ProgramsComponent_div_14_td_26_Template_button_click_8_listener", "ctx_r31", "deleteProgram", "ProgramsComponent_div_14_div_29_Template_button_click_7_listener", "_r34", "ctx_r33", "createProgram", "ɵɵelementContainerStart", "ProgramsComponent_div_14_th_10_Template", "ProgramsComponent_div_14_td_11_Template", "ɵɵelementContainerEnd", "ProgramsComponent_div_14_th_13_Template", "ProgramsComponent_div_14_td_14_Template", "ProgramsComponent_div_14_th_16_Template", "ProgramsComponent_div_14_td_17_Template", "ProgramsComponent_div_14_th_19_Template", "ProgramsComponent_div_14_td_20_Template", "ProgramsComponent_div_14_th_22_Template", "ProgramsComponent_div_14_td_23_Template", "ProgramsComponent_div_14_th_25_Template", "ProgramsComponent_div_14_td_26_Template", "ProgramsComponent_div_14_tr_27_Template", "ProgramsComponent_div_14_tr_28_Template", "ProgramsComponent_div_14_div_29_Template", "ctx_r2", "programs", "displayedColumns", "length", "ProgramsComponent", "constructor", "programService", "dialog", "snackBar", "loading", "ngOnInit", "loadPrograms", "getAllPrograms", "subscribe", "next", "response", "success", "console", "dialogRef", "open", "width", "data", "afterClosed", "result", "program", "confirm", "_id", "updatedProgram", "updateProgram", "_", "ɵɵdirectiveInject", "i1", "ProgramService", "i2", "MatDialog", "i3", "MatSnackBar", "_2", "selectors", "decls", "vars", "consts", "template", "ProgramsComponent_Template", "rf", "ctx", "ProgramsComponent_Template_button_click_5_listener", "ProgramsComponent_Template_button_click_8_listener", "ProgramsComponent_div_12_Template", "ProgramsComponent_div_13_Template", "ProgramsComponent_div_14_Template"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\programs\\programs.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\programs\\programs.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { MatDialog } from '@angular/material/dialog';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { ProgramService } from '../../../services/program.service';\r\nimport { Program } from '../../../models/user';\r\nimport { ProgramDialogComponent } from './program-dialog/program-dialog.component';\r\n\r\n@Component({\r\n  selector: 'app-programs',\r\n  templateUrl: './programs.component.html',\r\n  styleUrls: ['./programs.component.css']\r\n})\r\nexport class ProgramsComponent implements OnInit {\r\n  programs: Program[] = [];\r\n  loading = true;\r\n  error: string | null = null;\r\n\r\n  displayedColumns: string[] = ['name', 'fullName', 'duration', 'totalSemesters', 'isActive', 'actions'];\r\n\r\n  constructor(\r\n    private programService: ProgramService,\r\n    private dialog: MatDialog,\r\n    private snackBar: MatSnackBar\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loadPrograms();\r\n  }\r\n\r\n  loadPrograms(): void {\r\n    this.loading = true;\r\n    this.error = null;\r\n\r\n    this.programService.getAllPrograms().subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.programs = response.programs;\r\n        } else {\r\n          this.error = 'Failed to load programs';\r\n        }\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading programs:', error);\r\n        this.error = 'Error loading programs';\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  createProgram(): void {\r\n    const dialogRef = this.dialog.open(ProgramDialogComponent, {\r\n      width: '600px',\r\n      data: {}\r\n    });\r\n\r\n    dialogRef.afterClosed().subscribe(result => {\r\n      if (result?.success) {\r\n        this.snackBar.open('Program created successfully', 'Close', {\r\n          duration: 3000\r\n        });\r\n        this.loadPrograms();\r\n      } else if (result?.error) {\r\n        this.snackBar.open(result.error, 'Close', {\r\n          duration: 3000\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  editProgram(program: Program): void {\r\n    const dialogRef = this.dialog.open(ProgramDialogComponent, {\r\n      width: '600px',\r\n      data: { program }\r\n    });\r\n\r\n    dialogRef.afterClosed().subscribe(result => {\r\n      if (result?.success) {\r\n        this.snackBar.open('Program updated successfully', 'Close', {\r\n          duration: 3000\r\n        });\r\n        this.loadPrograms();\r\n      } else if (result?.error) {\r\n        this.snackBar.open(result.error, 'Close', {\r\n          duration: 3000\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  deleteProgram(program: Program): void {\r\n    if (confirm(`Are you sure you want to delete the program \"${program.name}\"?`)) {\r\n      this.programService.deleteProgram(program._id).subscribe({\r\n        next: (response) => {\r\n          if (response.success) {\r\n            this.snackBar.open('Program deleted successfully', 'Close', {\r\n              duration: 3000\r\n            });\r\n            this.loadPrograms();\r\n          } else {\r\n            this.snackBar.open('Failed to delete program', 'Close', {\r\n              duration: 3000\r\n            });\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('Error deleting program:', error);\r\n          this.snackBar.open('Error deleting program', 'Close', {\r\n            duration: 3000\r\n          });\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  toggleProgramStatus(program: Program): void {\r\n    const updatedProgram = { ...program, isActive: !program.isActive };\r\n    \r\n    this.programService.updateProgram(program._id, updatedProgram).subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.snackBar.open(\r\n            `Program ${updatedProgram.isActive ? 'activated' : 'deactivated'} successfully`, \r\n            'Close', \r\n            { duration: 3000 }\r\n          );\r\n          this.loadPrograms();\r\n        } else {\r\n          this.snackBar.open('Failed to update program status', 'Close', {\r\n            duration: 3000\r\n          });\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error updating program status:', error);\r\n        this.snackBar.open('Error updating program status', 'Close', {\r\n          duration: 3000\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  refreshData(): void {\r\n    this.loadPrograms();\r\n  }\r\n}\r\n", "<div class=\"programs-container\">\r\n  <!-- Header -->\r\n  <div class=\"page-header\">\r\n    <h1>Programs Management</h1>\r\n    <div class=\"header-actions\">\r\n      <button mat-icon-button (click)=\"refreshData()\" matTooltip=\"Refresh\">\r\n        <mat-icon>refresh</mat-icon>\r\n      </button>\r\n      <button mat-raised-button color=\"primary\" (click)=\"createProgram()\">\r\n        <mat-icon>add</mat-icon>\r\n        Add Program\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Loading State -->\r\n  <div *ngIf=\"loading\" class=\"loading-container\">\r\n    <mat-spinner></mat-spinner>\r\n    <p>Loading programs...</p>\r\n  </div>\r\n\r\n  <!-- Error State -->\r\n  <div *ngIf=\"error && !loading\" class=\"error-container\">\r\n    <mat-icon color=\"warn\">error</mat-icon>\r\n    <p>{{ error }}</p>\r\n    <button mat-raised-button color=\"primary\" (click)=\"refreshData()\">\r\n      <mat-icon>refresh</mat-icon>\r\n      Retry\r\n    </button>\r\n  </div>\r\n\r\n  <!-- Programs Table -->\r\n  <div *ngIf=\"!loading && !error\" class=\"table-container\">\r\n    <mat-card>\r\n      <mat-card-header>\r\n        <mat-card-title>All Programs</mat-card-title>\r\n        <mat-card-subtitle>Manage academic programs</mat-card-subtitle>\r\n      </mat-card-header>\r\n      <mat-card-content>\r\n        <table mat-table [dataSource]=\"programs\" class=\"programs-table\">\r\n          <!-- Name Column -->\r\n          <ng-container matColumnDef=\"name\">\r\n            <th mat-header-cell *matHeaderCellDef>Program Code</th>\r\n            <td mat-cell *matCellDef=\"let program\">\r\n              <span class=\"program-code\">{{ program.name }}</span>\r\n            </td>\r\n          </ng-container>\r\n\r\n          <!-- Full Name Column -->\r\n          <ng-container matColumnDef=\"fullName\">\r\n            <th mat-header-cell *matHeaderCellDef>Full Name</th>\r\n            <td mat-cell *matCellDef=\"let program\">\r\n              <div class=\"program-info\">\r\n                <span class=\"program-name\">{{ program.fullName }}</span>\r\n                <span class=\"program-description\" *ngIf=\"program.description\">{{ program.description }}</span>\r\n              </div>\r\n            </td>\r\n          </ng-container>\r\n\r\n          <!-- Duration Column -->\r\n          <ng-container matColumnDef=\"duration\">\r\n            <th mat-header-cell *matHeaderCellDef>Duration</th>\r\n            <td mat-cell *matCellDef=\"let program\">\r\n              {{ program.duration }} {{ program.durationUnit }}\r\n            </td>\r\n          </ng-container>\r\n\r\n          <!-- Total Semesters Column -->\r\n          <ng-container matColumnDef=\"totalSemesters\">\r\n            <th mat-header-cell *matHeaderCellDef>Semesters</th>\r\n            <td mat-cell *matCellDef=\"let program\">\r\n              {{ program.totalSemesters }}\r\n            </td>\r\n          </ng-container>\r\n\r\n          <!-- Status Column -->\r\n          <ng-container matColumnDef=\"isActive\">\r\n            <th mat-header-cell *matHeaderCellDef>Status</th>\r\n            <td mat-cell *matCellDef=\"let program\">\r\n              <mat-chip-set>\r\n                <mat-chip [color]=\"program.isActive ? 'primary' : 'warn'\" selected>\r\n                  {{ program.isActive ? 'Active' : 'Inactive' }}\r\n                </mat-chip>\r\n              </mat-chip-set>\r\n            </td>\r\n          </ng-container>\r\n\r\n          <!-- Actions Column -->\r\n          <ng-container matColumnDef=\"actions\">\r\n            <th mat-header-cell *matHeaderCellDef>Actions</th>\r\n            <td mat-cell *matCellDef=\"let program\">\r\n              <div class=\"action-buttons\">\r\n                <button mat-icon-button \r\n                        (click)=\"editProgram(program)\" \r\n                        matTooltip=\"Edit Program\"\r\n                        color=\"primary\">\r\n                  <mat-icon>edit</mat-icon>\r\n                </button>\r\n                \r\n                <button mat-icon-button \r\n                        (click)=\"toggleProgramStatus(program)\" \r\n                        [matTooltip]=\"program.isActive ? 'Deactivate Program' : 'Activate Program'\"\r\n                        [color]=\"program.isActive ? 'warn' : 'primary'\">\r\n                  <mat-icon>{{ program.isActive ? 'toggle_off' : 'toggle_on' }}</mat-icon>\r\n                </button>\r\n                \r\n                <button mat-icon-button \r\n                        (click)=\"deleteProgram(program)\" \r\n                        matTooltip=\"Delete Program\"\r\n                        color=\"warn\">\r\n                  <mat-icon>delete</mat-icon>\r\n                </button>\r\n              </div>\r\n            </td>\r\n          </ng-container>\r\n\r\n          <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\r\n          <tr mat-row *matRowDef=\"let row; columns: displayedColumns;\"></tr>\r\n        </table>\r\n\r\n        <!-- No Data State -->\r\n        <div *ngIf=\"programs.length === 0\" class=\"no-data\">\r\n          <mat-icon>school</mat-icon>\r\n          <h3>No Programs Found</h3>\r\n          <p>Start by creating your first academic program.</p>\r\n          <button mat-raised-button color=\"primary\" (click)=\"createProgram()\">\r\n            <mat-icon>add</mat-icon>\r\n            Create Program\r\n          </button>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAKA,SAASA,sBAAsB,QAAQ,2CAA2C;;;;;;;;;;;;;;;ICWhFC,EAAA,CAAAC,cAAA,aAA+C;IAC7CD,EAAA,CAAAE,SAAA,kBAA2B;IAC3BF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,0BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;;IAI5BJ,EAAA,CAAAC,cAAA,aAAuD;IAC9BD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACvCJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAW;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAClBJ,EAAA,CAAAC,cAAA,gBAAkE;IAAxBD,EAAA,CAAAK,UAAA,mBAAAC,0DAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAC/DZ,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC5BJ,EAAA,CAAAG,MAAA,cACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAJNJ,EAAA,CAAAa,SAAA,GAAW;IAAXb,EAAA,CAAAc,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IAkBNhB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,mBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;IACvDJ,EAAA,CAAAC,cAAA,aAAuC;IACVD,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAAzBJ,EAAA,CAAAa,SAAA,GAAkB;IAAlBb,EAAA,CAAAc,iBAAA,CAAAG,WAAA,CAAAC,IAAA,CAAkB;;;;;IAM/ClB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;IAIhDJ,EAAA,CAAAC,cAAA,eAA8D;IAAAD,EAAA,CAAAG,MAAA,GAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAAhCJ,EAAA,CAAAa,SAAA,GAAyB;IAAzBb,EAAA,CAAAc,iBAAA,CAAAK,WAAA,CAAAC,WAAA,CAAyB;;;;;IAH3FpB,EAAA,CAAAC,cAAA,aAAuC;IAERD,EAAA,CAAAG,MAAA,GAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACxDJ,EAAA,CAAAqB,UAAA,IAAAC,8CAAA,mBAA8F;IAChGtB,EAAA,CAAAI,YAAA,EAAM;;;;IAFuBJ,EAAA,CAAAa,SAAA,GAAsB;IAAtBb,EAAA,CAAAc,iBAAA,CAAAK,WAAA,CAAAI,QAAA,CAAsB;IACdvB,EAAA,CAAAa,SAAA,GAAyB;IAAzBb,EAAA,CAAAwB,UAAA,SAAAL,WAAA,CAAAC,WAAA,CAAyB;;;;;IAOhEpB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;IACnDJ,EAAA,CAAAC,cAAA,aAAuC;IACrCD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;IADHJ,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAyB,kBAAA,MAAAC,WAAA,CAAAC,QAAA,OAAAD,WAAA,CAAAE,YAAA,MACF;;;;;IAKA5B,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;IACpDJ,EAAA,CAAAC,cAAA,aAAuC;IACrCD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;IADHJ,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA6B,kBAAA,MAAAC,WAAA,CAAAC,cAAA,MACF;;;;;IAKA/B,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;IACjDJ,EAAA,CAAAC,cAAA,aAAuC;IAGjCD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAW;;;;IAFDJ,EAAA,CAAAa,SAAA,GAA+C;IAA/Cb,EAAA,CAAAwB,UAAA,UAAAQ,WAAA,CAAAC,QAAA,sBAA+C;IACvDjC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA6B,kBAAA,MAAAG,WAAA,CAAAC,QAAA,8BACF;;;;;IAOJjC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;;IAClDJ,EAAA,CAAAC,cAAA,aAAuC;IAG3BD,EAAA,CAAAK,UAAA,mBAAA6B,gEAAA;MAAA,MAAAC,WAAA,GAAAnC,EAAA,CAAAO,aAAA,CAAA6B,IAAA;MAAA,MAAAC,WAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAAvC,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAA4B,OAAA,CAAAC,WAAA,CAAAH,WAAA,CAAoB;IAAA,EAAC;IAGpCrC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAG3BJ,EAAA,CAAAC,cAAA,iBAGwD;IAFhDD,EAAA,CAAAK,UAAA,mBAAAoC,gEAAA;MAAA,MAAAN,WAAA,GAAAnC,EAAA,CAAAO,aAAA,CAAA6B,IAAA;MAAA,MAAAC,WAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAI,OAAA,GAAA1C,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAA+B,OAAA,CAAAC,mBAAA,CAAAN,WAAA,CAA4B;IAAA,EAAC;IAG5CrC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAG,MAAA,GAAmD;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAG1EJ,EAAA,CAAAC,cAAA,iBAGqB;IAFbD,EAAA,CAAAK,UAAA,mBAAAuC,gEAAA;MAAA,MAAAT,WAAA,GAAAnC,EAAA,CAAAO,aAAA,CAAA6B,IAAA;MAAA,MAAAC,WAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAO,OAAA,GAAA7C,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAkC,OAAA,CAAAC,aAAA,CAAAT,WAAA,CAAsB;IAAA,EAAC;IAGtCrC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAW;;;;IATrBJ,EAAA,CAAAa,SAAA,GAA2E;IAA3Eb,EAAA,CAAAwB,UAAA,eAAAa,WAAA,CAAAJ,QAAA,6CAA2E,UAAAI,WAAA,CAAAJ,QAAA;IAEvEjC,EAAA,CAAAa,SAAA,GAAmD;IAAnDb,EAAA,CAAAc,iBAAA,CAAAuB,WAAA,CAAAJ,QAAA,8BAAmD;;;;;IAarEjC,EAAA,CAAAE,SAAA,aAA4D;;;;;IAC5DF,EAAA,CAAAE,SAAA,aAAkE;;;;;;IAIpEF,EAAA,CAAAC,cAAA,cAAmD;IACvCD,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC3BJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,wBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC1BJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,qDAA8C;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACrDJ,EAAA,CAAAC,cAAA,gBAAoE;IAA1BD,EAAA,CAAAK,UAAA,mBAAA0C,iEAAA;MAAA/C,EAAA,CAAAO,aAAA,CAAAyC,IAAA;MAAA,MAAAC,OAAA,GAAAjD,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAsC,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IACjElD,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAG,MAAA,UAAG;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACxBJ,EAAA,CAAAG,MAAA,wBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;IAhGjBJ,EAAA,CAAAC,cAAA,cAAwD;IAGlCD,EAAA,CAAAG,MAAA,mBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IAC7CJ,EAAA,CAAAC,cAAA,wBAAmB;IAAAD,EAAA,CAAAG,MAAA,+BAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAoB;IAEjEJ,EAAA,CAAAC,cAAA,uBAAkB;IAGdD,EAAA,CAAAmD,uBAAA,OAAkC;IAChCnD,EAAA,CAAAqB,UAAA,KAAA+B,uCAAA,iBAAuD;IACvDpD,EAAA,CAAAqB,UAAA,KAAAgC,uCAAA,iBAEK;IACPrD,EAAA,CAAAsD,qBAAA,EAAe;IAGftD,EAAA,CAAAmD,uBAAA,QAAsC;IACpCnD,EAAA,CAAAqB,UAAA,KAAAkC,uCAAA,iBAAoD;IACpDvD,EAAA,CAAAqB,UAAA,KAAAmC,uCAAA,iBAKK;IACPxD,EAAA,CAAAsD,qBAAA,EAAe;IAGftD,EAAA,CAAAmD,uBAAA,QAAsC;IACpCnD,EAAA,CAAAqB,UAAA,KAAAoC,uCAAA,iBAAmD;IACnDzD,EAAA,CAAAqB,UAAA,KAAAqC,uCAAA,iBAEK;IACP1D,EAAA,CAAAsD,qBAAA,EAAe;IAGftD,EAAA,CAAAmD,uBAAA,QAA4C;IAC1CnD,EAAA,CAAAqB,UAAA,KAAAsC,uCAAA,iBAAoD;IACpD3D,EAAA,CAAAqB,UAAA,KAAAuC,uCAAA,iBAEK;IACP5D,EAAA,CAAAsD,qBAAA,EAAe;IAGftD,EAAA,CAAAmD,uBAAA,QAAsC;IACpCnD,EAAA,CAAAqB,UAAA,KAAAwC,uCAAA,iBAAiD;IACjD7D,EAAA,CAAAqB,UAAA,KAAAyC,uCAAA,iBAMK;IACP9D,EAAA,CAAAsD,qBAAA,EAAe;IAGftD,EAAA,CAAAmD,uBAAA,QAAqC;IACnCnD,EAAA,CAAAqB,UAAA,KAAA0C,uCAAA,iBAAkD;IAClD/D,EAAA,CAAAqB,UAAA,KAAA2C,uCAAA,kBAuBK;IACPhE,EAAA,CAAAsD,qBAAA,EAAe;IAEftD,EAAA,CAAAqB,UAAA,KAAA4C,uCAAA,iBAA4D;IAC5DjE,EAAA,CAAAqB,UAAA,KAAA6C,uCAAA,iBAAkE;IACpElE,EAAA,CAAAI,YAAA,EAAQ;IAGRJ,EAAA,CAAAqB,UAAA,KAAA8C,wCAAA,mBAQM;IACRnE,EAAA,CAAAI,YAAA,EAAmB;;;;IA3FAJ,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAwB,UAAA,eAAA4C,MAAA,CAAAC,QAAA,CAAuB;IA6ElBrE,EAAA,CAAAa,SAAA,IAAiC;IAAjCb,EAAA,CAAAwB,UAAA,oBAAA4C,MAAA,CAAAE,gBAAA,CAAiC;IACpBtE,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAwB,UAAA,qBAAA4C,MAAA,CAAAE,gBAAA,CAA0B;IAIvDtE,EAAA,CAAAa,SAAA,GAA2B;IAA3Bb,EAAA,CAAAwB,UAAA,SAAA4C,MAAA,CAAAC,QAAA,CAAAE,MAAA,OAA2B;;;AD7GzC,OAAM,MAAOC,iBAAiB;EAO5BC,YACUC,cAA8B,EAC9BC,MAAiB,EACjBC,QAAqB;IAFrB,KAAAF,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IATlB,KAAAP,QAAQ,GAAc,EAAE;IACxB,KAAAQ,OAAO,GAAG,IAAI;IACd,KAAA7D,KAAK,GAAkB,IAAI;IAE3B,KAAAsD,gBAAgB,GAAa,CAAC,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,gBAAgB,EAAE,UAAU,EAAE,SAAS,CAAC;EAMnG;EAEHQ,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAA,YAAYA,CAAA;IACV,IAAI,CAACF,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC7D,KAAK,GAAG,IAAI;IAEjB,IAAI,CAAC0D,cAAc,CAACM,cAAc,EAAE,CAACC,SAAS,CAAC;MAC7CC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACf,QAAQ,GAAGc,QAAQ,CAACd,QAAQ;SAClC,MAAM;UACL,IAAI,CAACrD,KAAK,GAAG,yBAAyB;;QAExC,IAAI,CAAC6D,OAAO,GAAG,KAAK;MACtB,CAAC;MACD7D,KAAK,EAAGA,KAAK,IAAI;QACfqE,OAAO,CAACrE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACA,KAAK,GAAG,wBAAwB;QACrC,IAAI,CAAC6D,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA3B,aAAaA,CAAA;IACX,MAAMoC,SAAS,GAAG,IAAI,CAACX,MAAM,CAACY,IAAI,CAACxF,sBAAsB,EAAE;MACzDyF,KAAK,EAAE,OAAO;MACdC,IAAI,EAAE;KACP,CAAC;IAEFH,SAAS,CAACI,WAAW,EAAE,CAACT,SAAS,CAACU,MAAM,IAAG;MACzC,IAAIA,MAAM,EAAEP,OAAO,EAAE;QACnB,IAAI,CAACR,QAAQ,CAACW,IAAI,CAAC,8BAA8B,EAAE,OAAO,EAAE;UAC1D5D,QAAQ,EAAE;SACX,CAAC;QACF,IAAI,CAACoD,YAAY,EAAE;OACpB,MAAM,IAAIY,MAAM,EAAE3E,KAAK,EAAE;QACxB,IAAI,CAAC4D,QAAQ,CAACW,IAAI,CAACI,MAAM,CAAC3E,KAAK,EAAE,OAAO,EAAE;UACxCW,QAAQ,EAAE;SACX,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEAa,WAAWA,CAACoD,OAAgB;IAC1B,MAAMN,SAAS,GAAG,IAAI,CAACX,MAAM,CAACY,IAAI,CAACxF,sBAAsB,EAAE;MACzDyF,KAAK,EAAE,OAAO;MACdC,IAAI,EAAE;QAAEG;MAAO;KAChB,CAAC;IAEFN,SAAS,CAACI,WAAW,EAAE,CAACT,SAAS,CAACU,MAAM,IAAG;MACzC,IAAIA,MAAM,EAAEP,OAAO,EAAE;QACnB,IAAI,CAACR,QAAQ,CAACW,IAAI,CAAC,8BAA8B,EAAE,OAAO,EAAE;UAC1D5D,QAAQ,EAAE;SACX,CAAC;QACF,IAAI,CAACoD,YAAY,EAAE;OACpB,MAAM,IAAIY,MAAM,EAAE3E,KAAK,EAAE;QACxB,IAAI,CAAC4D,QAAQ,CAACW,IAAI,CAACI,MAAM,CAAC3E,KAAK,EAAE,OAAO,EAAE;UACxCW,QAAQ,EAAE;SACX,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEAmB,aAAaA,CAAC8C,OAAgB;IAC5B,IAAIC,OAAO,CAAC,gDAAgDD,OAAO,CAAC1E,IAAI,IAAI,CAAC,EAAE;MAC7E,IAAI,CAACwD,cAAc,CAAC5B,aAAa,CAAC8C,OAAO,CAACE,GAAG,CAAC,CAACb,SAAS,CAAC;QACvDC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;YACpB,IAAI,CAACR,QAAQ,CAACW,IAAI,CAAC,8BAA8B,EAAE,OAAO,EAAE;cAC1D5D,QAAQ,EAAE;aACX,CAAC;YACF,IAAI,CAACoD,YAAY,EAAE;WACpB,MAAM;YACL,IAAI,CAACH,QAAQ,CAACW,IAAI,CAAC,0BAA0B,EAAE,OAAO,EAAE;cACtD5D,QAAQ,EAAE;aACX,CAAC;;QAEN,CAAC;QACDX,KAAK,EAAGA,KAAK,IAAI;UACfqE,OAAO,CAACrE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAC/C,IAAI,CAAC4D,QAAQ,CAACW,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE;YACpD5D,QAAQ,EAAE;WACX,CAAC;QACJ;OACD,CAAC;;EAEN;EAEAgB,mBAAmBA,CAACiD,OAAgB;IAClC,MAAMG,cAAc,GAAG;MAAE,GAAGH,OAAO;MAAE3D,QAAQ,EAAE,CAAC2D,OAAO,CAAC3D;IAAQ,CAAE;IAElE,IAAI,CAACyC,cAAc,CAACsB,aAAa,CAACJ,OAAO,CAACE,GAAG,EAAEC,cAAc,CAAC,CAACd,SAAS,CAAC;MACvEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACR,QAAQ,CAACW,IAAI,CAChB,WAAWQ,cAAc,CAAC9D,QAAQ,GAAG,WAAW,GAAG,aAAa,eAAe,EAC/E,OAAO,EACP;YAAEN,QAAQ,EAAE;UAAI,CAAE,CACnB;UACD,IAAI,CAACoD,YAAY,EAAE;SACpB,MAAM;UACL,IAAI,CAACH,QAAQ,CAACW,IAAI,CAAC,iCAAiC,EAAE,OAAO,EAAE;YAC7D5D,QAAQ,EAAE;WACX,CAAC;;MAEN,CAAC;MACDX,KAAK,EAAGA,KAAK,IAAI;QACfqE,OAAO,CAACrE,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAAC4D,QAAQ,CAACW,IAAI,CAAC,+BAA+B,EAAE,OAAO,EAAE;UAC3D5D,QAAQ,EAAE;SACX,CAAC;MACJ;KACD,CAAC;EACJ;EAEAf,WAAWA,CAAA;IACT,IAAI,CAACmE,YAAY,EAAE;EACrB;EAAC,QAAAkB,CAAA,G;qBApIUzB,iBAAiB,EAAAxE,EAAA,CAAAkG,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAApG,EAAA,CAAAkG,iBAAA,CAAAG,EAAA,CAAAC,SAAA,GAAAtG,EAAA,CAAAkG,iBAAA,CAAAK,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAjBjC,iBAAiB;IAAAkC,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCZ9BhH,EAAA,CAAAC,cAAA,aAAgC;QAGxBD,EAAA,CAAAG,MAAA,0BAAmB;QAAAH,EAAA,CAAAI,YAAA,EAAK;QAC5BJ,EAAA,CAAAC,cAAA,aAA4B;QACFD,EAAA,CAAAK,UAAA,mBAAA6G,mDAAA;UAAA,OAASD,GAAA,CAAArG,WAAA,EAAa;QAAA,EAAC;QAC7CZ,EAAA,CAAAC,cAAA,eAAU;QAAAD,EAAA,CAAAG,MAAA,cAAO;QAAAH,EAAA,CAAAI,YAAA,EAAW;QAE9BJ,EAAA,CAAAC,cAAA,gBAAoE;QAA1BD,EAAA,CAAAK,UAAA,mBAAA8G,mDAAA;UAAA,OAASF,GAAA,CAAA/D,aAAA,EAAe;QAAA,EAAC;QACjElD,EAAA,CAAAC,cAAA,eAAU;QAAAD,EAAA,CAAAG,MAAA,WAAG;QAAAH,EAAA,CAAAI,YAAA,EAAW;QACxBJ,EAAA,CAAAG,MAAA,qBACF;QAAAH,EAAA,CAAAI,YAAA,EAAS;QAKbJ,EAAA,CAAAqB,UAAA,KAAA+F,iCAAA,iBAGM;QAGNpH,EAAA,CAAAqB,UAAA,KAAAgG,iCAAA,iBAOM;QAGNrH,EAAA,CAAAqB,UAAA,KAAAiG,iCAAA,kBAoGM;QACRtH,EAAA,CAAAI,YAAA,EAAM;;;QArHEJ,EAAA,CAAAa,SAAA,IAAa;QAAbb,EAAA,CAAAwB,UAAA,SAAAyF,GAAA,CAAApC,OAAA,CAAa;QAMb7E,EAAA,CAAAa,SAAA,GAAuB;QAAvBb,EAAA,CAAAwB,UAAA,SAAAyF,GAAA,CAAAjG,KAAA,KAAAiG,GAAA,CAAApC,OAAA,CAAuB;QAUvB7E,EAAA,CAAAa,SAAA,GAAwB;QAAxBb,EAAA,CAAAwB,UAAA,UAAAyF,GAAA,CAAApC,OAAA,KAAAoC,GAAA,CAAAjG,KAAA,CAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}