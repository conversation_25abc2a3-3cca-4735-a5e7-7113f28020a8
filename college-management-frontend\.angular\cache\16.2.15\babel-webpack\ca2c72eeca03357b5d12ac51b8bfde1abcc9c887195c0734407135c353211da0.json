{"ast": null, "code": "import { Chart, registerables } from 'chart.js';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/user.service\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@angular/common\";\nChart.register(...registerables);\nexport let ChartsComponent = /*#__PURE__*/(() => {\n  class ChartsComponent {\n    constructor(userService) {\n      this.userService = userService;\n      this.profileName = '<PERSON>';\n      this.currentDate = new Date();\n      this.selectedPeriod = 'month';\n      this.charts = [];\n    }\n    ngOnInit() {\n      const user = this.userService.getUserFromLocalStorage().user;\n      this.profileName = user.name;\n      this.createCharts();\n    }\n    ngOnDestroy() {\n      // Destroy all charts to prevent memory leaks\n      this.charts.forEach(chart => chart.destroy());\n    }\n    updateCharts() {\n      // This would be where you'd update chart data based on selected period\n      // For now, we'll just recreate them\n      this.charts.forEach(chart => chart.destroy());\n      this.createCharts();\n    }\n    createCharts() {\n      const chartConfig = {\n        responsive: true,\n        maintainAspectRatio: false,\n        plugins: {\n          legend: {\n            position: 'top',\n            labels: {\n              color: '#515154',\n              font: {\n                family: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif',\n                size: 12\n              }\n            }\n          },\n          tooltip: {\n            backgroundColor: '#11418e',\n            titleFont: {\n              family: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif',\n              size: 12\n            },\n            bodyFont: {\n              family: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif',\n              size: 12\n            },\n            padding: 10,\n            cornerRadius: 8\n          }\n        },\n        scales: {\n          x: {\n            grid: {\n              display: false\n            },\n            ticks: {\n              color: '#515154'\n            }\n          },\n          y: {\n            grid: {\n              color: '#eaeaea'\n            },\n            ticks: {\n              color: '#515154'\n            }\n          }\n        }\n      };\n      // Class 11 Attendance Chart\n      const class11AttendanceChart = new Chart(document.getElementById('class11AttendanceChart'), {\n        type: 'doughnut',\n        data: {\n          labels: ['Present', 'Absent'],\n          datasets: [{\n            data: [170, 30],\n            backgroundColor: ['#11418e', '#515154'],\n            borderWidth: 0\n          }]\n        },\n        options: chartConfig\n      });\n      this.charts.push(class11AttendanceChart);\n      // Class 12 Attendance Chart\n      // const class12AttendanceChart = new Chart(\n      //   document.getElementById('class12AttendanceChart') as HTMLCanvasElement,\n      //   {\n      //     type: 'doughnut',\n      //     data: {\n      //       labels: ['Present', 'Absent'],\n      //       datasets: [{\n      //         data: [160, 20],\n      //         backgroundColor: ['#11418e', '#515154'],\n      //         borderWidth: 0\n      //       }]\n      //     },\n      //     options: chartConfig\n      //   }\n      // );\n      // this.charts.push(class12AttendanceChart);\n      // Class 11 Department Chart\n      const class11DepartmentChart = new Chart(document.getElementById('class11DepartmentChart'), {\n        type: 'bar',\n        data: {\n          labels: ['Pre-Medical', 'Pre-Engineering', 'Computer Science', 'Arts'],\n          datasets: [{\n            data: [50, 60, 30, 60],\n            backgroundColor: ['#11418e', '#3a6ba6', '#5c7fb3', '#7e93c1'],\n            borderWidth: 0,\n            borderRadius: 6\n          }]\n        },\n        options: chartConfig\n      });\n      this.charts.push(class11DepartmentChart);\n      // Class 12 Department Chart\n      // const class12DepartmentChart = new Chart(\n      //   document.getElementById('class12DepartmentChart') as HTMLCanvasElement,\n      //   {\n      //     type: 'bar',\n      //     data: {\n      //       labels: ['Pre-Medical', 'Pre-Engineering', 'Computer Science', 'Arts'],\n      //       datasets: [{\n      //         data: [45, 50, 35, 50],\n      //         backgroundColor: ['#11418e', '#3a6ba6', '#5c7fb3', '#7e93c1'],\n      //         borderWidth: 0,\n      //         borderRadius: 6\n      //       }]\n      //     },\n      //     options: chartConfig\n      //   }\n      // );\n      // this.charts.push(class12DepartmentChart);\n      // Teachers by Department Chart\n      const teachersDepartmentChart = new Chart(document.getElementById('teachersDepartmentChart'), {\n        type: 'bar',\n        data: {\n          labels: ['Pre-Medical', 'Pre-Engineering', 'Computer Science', 'Arts'],\n          datasets: [{\n            data: [8, 10, 6, 6],\n            backgroundColor: ['#11418e', '#3a6ba6', '#5c7fb3', '#7e93c1'],\n            borderWidth: 0,\n            borderRadius: 6\n          }]\n        },\n        options: {\n          ...chartConfig,\n          indexAxis: 'y'\n        }\n      });\n      this.charts.push(teachersDepartmentChart);\n    }\n    static #_ = this.ɵfac = function ChartsComponent_Factory(t) {\n      return new (t || ChartsComponent)(i0.ɵɵdirectiveInject(i1.UserService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ChartsComponent,\n      selectors: [[\"app-charts\"]],\n      decls: 91,\n      vars: 6,\n      consts: [[1, \"dashboard-container\"], [1, \"dashboard-header\"], [1, \"header-content\"], [1, \"subtitle\"], [1, \"last-updated\"], [1, \"badge\"], [1, \"stats-grid\"], [1, \"stat-card\"], [1, \"stat-content\"], [1, \"stat-info\"], [1, \"stat-value\"], [1, \"stat-detail\"], [1, \"stat-icon\"], [1, \"fas\", \"fa-users\"], [1, \"fas\", \"fa-chalkboard-teacher\"], [1, \"fas\", \"fa-check-circle\"], [1, \"fas\", \"fa-exclamation-circle\"], [1, \"charts-section\"], [1, \"chart-card\"], [1, \"chart-header\"], [1, \"chart-controls\"], [1, \"form-select\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"value\", \"month\"], [\"value\", \"week\"], [\"value\", \"today\"], [\"value\", \"year\"], [1, \"chart-body\"], [\"id\", \"class11AttendanceChart\"], [\"id\", \"class11DepartmentChart\"], [1, \"chart-card\", \"full-width\"], [1, \"btn-view-all\"], [\"id\", \"teachersDepartmentChart\"]],\n      template: function ChartsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\")(4, \"h1\");\n          i0.ɵɵtext(5, \"Welcome back, \");\n          i0.ɵɵelementStart(6, \"span\");\n          i0.ɵɵtext(7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(8, \" \\uD83D\\uDC4B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"p\", 3);\n          i0.ɵɵtext(10, \"Track and manage college attendance efficiently\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 4)(12, \"span\", 5);\n          i0.ɵɵtext(13);\n          i0.ɵɵpipe(14, \"date\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(15, \"div\", 6)(16, \"div\", 7)(17, \"div\", 8)(18, \"div\", 9)(19, \"h3\");\n          i0.ɵɵtext(20, \"Total Students\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"p\", 10);\n          i0.ɵɵtext(22, \"380\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"p\", 11);\n          i0.ɵɵtext(24, \"Class 11: 200 | Class 12: 180\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"div\", 12);\n          i0.ɵɵelement(26, \"i\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(27, \"div\", 7)(28, \"div\", 8)(29, \"div\", 9)(30, \"h3\");\n          i0.ɵɵtext(31, \"Total Teachers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"p\", 10);\n          i0.ɵɵtext(33, \"30\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"p\", 11);\n          i0.ɵɵtext(35, \"5 Departments\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"div\", 12);\n          i0.ɵɵelement(37, \"i\", 14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(38, \"div\", 7)(39, \"div\", 8)(40, \"div\", 9)(41, \"h3\");\n          i0.ɵɵtext(42, \"Present Today\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"p\", 10);\n          i0.ɵɵtext(44, \"330\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"p\", 11);\n          i0.ɵɵtext(46, \"Class 11: 170 | Class 12: 160\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(47, \"div\", 12);\n          i0.ɵɵelement(48, \"i\", 15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(49, \"div\", 7)(50, \"div\", 8)(51, \"div\", 9)(52, \"h3\");\n          i0.ɵɵtext(53, \"Absent Today\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"p\", 10);\n          i0.ɵɵtext(55, \"50\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"p\", 11);\n          i0.ɵɵtext(57, \"Class 11: 30 | Class 12: 20\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(58, \"div\", 12);\n          i0.ɵɵelement(59, \"i\", 16);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(60, \"div\", 17)(61, \"div\", 18)(62, \"div\", 19)(63, \"h3\");\n          i0.ɵɵtext(64, \"Attendance Overview\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"div\", 20)(66, \"select\", 21);\n          i0.ɵɵlistener(\"ngModelChange\", function ChartsComponent_Template_select_ngModelChange_66_listener($event) {\n            return ctx.selectedPeriod = $event;\n          })(\"change\", function ChartsComponent_Template_select_change_66_listener() {\n            return ctx.updateCharts();\n          });\n          i0.ɵɵelementStart(67, \"option\", 22);\n          i0.ɵɵtext(68, \"This Month\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"option\", 23);\n          i0.ɵɵtext(70, \"This Week\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"option\", 24);\n          i0.ɵɵtext(72, \"Today\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"option\", 25);\n          i0.ɵɵtext(74, \"This Year\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(75, \"div\", 26);\n          i0.ɵɵelement(76, \"canvas\", 27);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(77, \"div\", 18)(78, \"div\", 19)(79, \"h3\");\n          i0.ɵɵtext(80, \"Department Distribution\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(81, \"div\", 26);\n          i0.ɵɵelement(82, \"canvas\", 28);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(83, \"div\", 29)(84, \"div\", 19)(85, \"h3\");\n          i0.ɵɵtext(86, \"Teachers by Department\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"button\", 30);\n          i0.ɵɵtext(88, \"View All\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(89, \"div\", 26);\n          i0.ɵɵelement(90, \"canvas\", 31);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate(ctx.profileName);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\"Last updated: \", i0.ɵɵpipeBind2(14, 3, ctx.currentDate, \"medium\"), \"\");\n          i0.ɵɵadvance(53);\n          i0.ɵɵproperty(\"ngModel\", ctx.selectedPeriod);\n        }\n      },\n      dependencies: [i2.NgSelectOption, i2.ɵNgSelectMultipleOption, i2.SelectControlValueAccessor, i2.NgControlStatus, i2.NgModel, i3.DatePipe],\n      styles: [\".dashboard-container[_ngcontent-%COMP%]{padding:1.5rem;max-width:100%;overflow-x:hidden}.dashboard-header[_ngcontent-%COMP%]{background-color:#fff;border-radius:12px;padding:1.5rem;margin-bottom:1.5rem;box-shadow:0 2px 8px #0000000d}.header-content[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;flex-wrap:wrap;gap:1rem}.dashboard-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{color:#11418e;font-size:1.8rem;font-weight:600;margin-bottom:.5rem}.dashboard-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#515154;font-weight:500}.subtitle[_ngcontent-%COMP%]{color:#515154;margin-bottom:0;font-size:.95rem}.last-updated[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{background-color:#f0f4f9;color:#515154;font-weight:500;padding:.5rem 1rem;border-radius:8px;font-size:.85rem}.stats-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(240px,1fr));gap:1.5rem;margin-bottom:1.5rem}.stat-card[_ngcontent-%COMP%]{background-color:#fff;border-radius:12px;padding:1.5rem;box-shadow:0 2px 8px #0000000d;transition:transform .3s ease,box-shadow .3s ease}.stat-card[_ngcontent-%COMP%]:hover{transform:translateY(-5px);box-shadow:0 8px 16px #0000001a}.stat-content[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}.stat-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#515154;font-size:1rem;font-weight:500;margin-bottom:.5rem}.stat-value[_ngcontent-%COMP%]{color:#11418e;font-size:1.8rem;font-weight:600;margin-bottom:.25rem}.stat-detail[_ngcontent-%COMP%]{color:#515154;font-size:.85rem;margin-bottom:0;opacity:.8}.stat-icon[_ngcontent-%COMP%]{width:50px;height:50px;background-color:#f0f4f9;border-radius:50%;display:flex;align-items:center;justify-content:center;color:#11418e;font-size:1.2rem}.charts-section[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(450px,1fr));gap:1.5rem}.chart-card[_ngcontent-%COMP%]{background-color:#fff;border-radius:12px;box-shadow:0 2px 8px #0000000d;padding:1.5rem}.chart-card.full-width[_ngcontent-%COMP%]{grid-column:1 / -1}.chart-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:1.5rem}.chart-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#11418e;font-size:1.1rem;font-weight:600;margin:0}.chart-controls[_ngcontent-%COMP%]   .form-select[_ngcontent-%COMP%]{border:1px solid #e0e0e0;border-radius:8px;padding:.4rem 1rem;font-size:.85rem;color:#515154;background-color:#f8f9fa;max-width:150px}.btn-view-all[_ngcontent-%COMP%]{background-color:#f0f4f9;color:#11418e;border:none;padding:.5rem 1rem;border-radius:8px;font-size:.85rem;font-weight:500;cursor:pointer;transition:all .2s}.btn-view-all[_ngcontent-%COMP%]:hover{background-color:#e0e8f0}.chart-body[_ngcontent-%COMP%]   canvas[_ngcontent-%COMP%]{width:100%!important;height:100%!important;display:block}@media (max-width: 1199.98px){.charts-section[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fit,minmax(300px,1fr))}}@media (max-width: 991.98px){.dashboard-container[_ngcontent-%COMP%]{padding:1rem}.stats-grid[_ngcontent-%COMP%]{grid-template-columns:1fr 1fr}.charts-section[_ngcontent-%COMP%]{grid-template-columns:1fr}.dashboard-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:1.5rem}.chart-body[_ngcontent-%COMP%]{height:250px}}@media (max-width: 767.98px){.stats-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.header-content[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:.5rem}.last-updated[_ngcontent-%COMP%]{width:100%}.last-updated[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{width:100%;text-align:center}.charts-section[_ngcontent-%COMP%]{grid-template-columns:1fr}}@media (max-width: 575.98px){.dashboard-container[_ngcontent-%COMP%]{padding:0}.dashboard-header[_ngcontent-%COMP%], .stat-card[_ngcontent-%COMP%], .chart-card[_ngcontent-%COMP%]{padding:1rem}.chart-header[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:.5rem}.chart-controls[_ngcontent-%COMP%]   .form-select[_ngcontent-%COMP%]{max-width:100%;width:100%}.charts-section[_ngcontent-%COMP%]{grid-template-columns:1fr}}\"]\n    });\n  }\n  return ChartsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}