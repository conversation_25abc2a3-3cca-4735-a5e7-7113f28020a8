.program-dialog {
  min-width: 500px;
  max-width: 600px;
}

.program-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px 0;
}

.full-width {
  width: 100%;
}

.form-row {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.half-width {
  flex: 1;
}

.checkbox-container {
  margin: 16px 0;
}

.spinner {
  margin-right: 8px;
}

mat-dialog-content {
  max-height: 70vh;
  overflow-y: auto;
}

mat-dialog-actions {
  padding: 16px 24px;
  margin: 0;
}

/* Form field styling */
mat-form-field {
  margin-bottom: 8px;
}

/* Error styling */
mat-error {
  font-size: 12px;
  margin-top: 4px;
}

/* Button styling */
button[mat-raised-button] {
  min-width: 100px;
}

/* Responsive design */
@media (max-width: 600px) {
  .program-dialog {
    min-width: 90vw;
    max-width: 95vw;
  }
  
  .form-row {
    flex-direction: column;
    gap: 8px;
  }
  
  .half-width {
    width: 100%;
  }
}
