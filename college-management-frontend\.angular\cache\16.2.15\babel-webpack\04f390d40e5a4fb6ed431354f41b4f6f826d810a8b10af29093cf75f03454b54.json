{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/services/user.service\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/card\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/form-field\";\nimport * as i9 from \"@angular/material/input\";\nimport * as i10 from \"@angular/material/tabs\";\nimport * as i11 from \"@angular/material/slide-toggle\";\nimport * as i12 from \"@angular/material/progress-spinner\";\nfunction SettingsComponent_mat_error_24_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingsComponent_mat_error_24_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Name must be at least 2 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingsComponent_mat_error_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtemplate(1, SettingsComponent_mat_error_24_span_1_Template, 2, 0, \"span\", 13);\n    i0.ɵɵtemplate(2, SettingsComponent_mat_error_24_span_2_Template, 2, 0, \"span\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.name == null ? null : ctx_r0.name.errors == null ? null : ctx_r0.name.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.name == null ? null : ctx_r0.name.errors == null ? null : ctx_r0.name.errors[\"minlength\"]);\n  }\n}\nfunction SettingsComponent_mat_error_29_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Email is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingsComponent_mat_error_29_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Please enter a valid email\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingsComponent_mat_error_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtemplate(1, SettingsComponent_mat_error_29_span_1_Template, 2, 0, \"span\", 13);\n    i0.ɵɵtemplate(2, SettingsComponent_mat_error_29_span_2_Template, 2, 0, \"span\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.email == null ? null : ctx_r1.email.errors == null ? null : ctx_r1.email.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.email == null ? null : ctx_r1.email.errors == null ? null : ctx_r1.email.errors[\"email\"]);\n  }\n}\nfunction SettingsComponent_mat_error_34_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Contact number is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingsComponent_mat_error_34_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Please enter a valid contact number\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingsComponent_mat_error_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtemplate(1, SettingsComponent_mat_error_34_span_1_Template, 2, 0, \"span\", 13);\n    i0.ɵɵtemplate(2, SettingsComponent_mat_error_34_span_2_Template, 2, 0, \"span\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.contact == null ? null : ctx_r2.contact.errors == null ? null : ctx_r2.contact.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.contact == null ? null : ctx_r2.contact.errors == null ? null : ctx_r2.contact.errors[\"pattern\"]);\n  }\n}\nfunction SettingsComponent_mat_spinner_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 52);\n  }\n}\nfunction SettingsComponent_mat_error_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Current password is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingsComponent_mat_error_71_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"New password is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingsComponent_mat_error_71_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Password must be at least 6 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingsComponent_mat_error_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtemplate(1, SettingsComponent_mat_error_71_span_1_Template, 2, 0, \"span\", 13);\n    i0.ɵɵtemplate(2, SettingsComponent_mat_error_71_span_2_Template, 2, 0, \"span\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.newPassword == null ? null : ctx_r5.newPassword.errors == null ? null : ctx_r5.newPassword.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.newPassword == null ? null : ctx_r5.newPassword.errors == null ? null : ctx_r5.newPassword.errors[\"minlength\"]);\n  }\n}\nfunction SettingsComponent_mat_error_76_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Please confirm your password\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingsComponent_mat_error_76_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Passwords do not match\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingsComponent_mat_error_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtemplate(1, SettingsComponent_mat_error_76_span_1_Template, 2, 0, \"span\", 13);\n    i0.ɵɵtemplate(2, SettingsComponent_mat_error_76_span_2_Template, 2, 0, \"span\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.confirmPassword == null ? null : ctx_r6.confirmPassword.errors == null ? null : ctx_r6.confirmPassword.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.confirmPassword == null ? null : ctx_r6.confirmPassword.errors == null ? null : ctx_r6.confirmPassword.errors[\"passwordMismatch\"]);\n  }\n}\nfunction SettingsComponent_mat_spinner_82_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 52);\n  }\n}\nfunction SettingsComponent_mat_spinner_156_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 52);\n  }\n}\nexport class SettingsComponent {\n  constructor(fb, userService, snackBar) {\n    this.fb = fb;\n    this.userService = userService;\n    this.snackBar = snackBar;\n    this.loading = false;\n    // Settings tabs\n    this.selectedTab = 0;\n    // System settings\n    this.systemSettings = {\n      instituteName: 'Government Post Graduate College',\n      instituteAddress: 'Swabi, KPK, Pakistan',\n      contactEmail: '<EMAIL>',\n      contactPhone: '+92-938-222222',\n      academicYear: '2024-2025',\n      currentSemester: 'Fall 2024',\n      allowStudentRegistration: true,\n      allowTeacherRegistration: false,\n      enableNotifications: true,\n      enableEmailNotifications: true,\n      enableSMSNotifications: false,\n      maintenanceMode: false\n    };\n    this.profileForm = this.createProfileForm();\n    this.passwordForm = this.createPasswordForm();\n    this.systemForm = this.createSystemForm();\n  }\n  ngOnInit() {\n    this.loadCurrentUser();\n    this.loadSystemSettings();\n  }\n  createProfileForm() {\n    return this.fb.group({\n      name: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      contact: ['', [Validators.required, Validators.pattern(/^[0-9]{10,15}$/)]],\n      address: [''],\n      qualification: [''],\n      experience: [''],\n      joiningDate: ['']\n    });\n  }\n  createPasswordForm() {\n    return this.fb.group({\n      currentPassword: ['', [Validators.required]],\n      newPassword: ['', [Validators.required, Validators.minLength(6)]],\n      confirmPassword: ['', [Validators.required]]\n    }, {\n      validators: this.passwordMatchValidator\n    });\n  }\n  createSystemForm() {\n    return this.fb.group({\n      instituteName: [this.systemSettings.instituteName, Validators.required],\n      instituteAddress: [this.systemSettings.instituteAddress, Validators.required],\n      contactEmail: [this.systemSettings.contactEmail, [Validators.required, Validators.email]],\n      contactPhone: [this.systemSettings.contactPhone, Validators.required],\n      academicYear: [this.systemSettings.academicYear, Validators.required],\n      currentSemester: [this.systemSettings.currentSemester, Validators.required],\n      allowStudentRegistration: [this.systemSettings.allowStudentRegistration],\n      allowTeacherRegistration: [this.systemSettings.allowTeacherRegistration],\n      enableNotifications: [this.systemSettings.enableNotifications],\n      enableEmailNotifications: [this.systemSettings.enableEmailNotifications],\n      enableSMSNotifications: [this.systemSettings.enableSMSNotifications],\n      maintenanceMode: [this.systemSettings.maintenanceMode]\n    });\n  }\n  passwordMatchValidator(form) {\n    const newPassword = form.get('newPassword');\n    const confirmPassword = form.get('confirmPassword');\n    if (newPassword && confirmPassword && newPassword.value !== confirmPassword.value) {\n      confirmPassword.setErrors({\n        passwordMismatch: true\n      });\n      return {\n        passwordMismatch: true\n      };\n    }\n    return null;\n  }\n  loadCurrentUser() {\n    const userData = this.userService.getUserFromLocalStorage();\n    if (userData && userData.user) {\n      this.currentUser = userData.user;\n      this.profileForm.patchValue({\n        name: this.currentUser.name,\n        email: this.currentUser.email,\n        contact: this.currentUser.contact,\n        address: this.currentUser.address,\n        qualification: this.currentUser.qualification,\n        experience: this.currentUser.experience,\n        joiningDate: this.currentUser.joiningDate\n      });\n    }\n  }\n  loadSystemSettings() {\n    // In a real application, you would load these from a backend service\n    // For now, we'll use the default values\n    this.systemForm.patchValue(this.systemSettings);\n  }\n  onTabChange(index) {\n    this.selectedTab = index;\n  }\n  updateProfile() {\n    if (this.profileForm.invalid) {\n      this.markFormGroupTouched(this.profileForm);\n      this.showError('Please fill in all required fields correctly');\n      return;\n    }\n    this.loading = true;\n    const profileData = this.profileForm.value;\n    this.userService.updateUserProfile(this.currentUser._id, profileData).subscribe({\n      next: response => {\n        if (response.success) {\n          this.showSuccess('Profile updated successfully!');\n          // Update local storage\n          const userData = this.userService.getUserFromLocalStorage();\n          if (userData) {\n            userData.user = {\n              ...userData.user,\n              ...profileData\n            };\n            localStorage.setItem('user', JSON.stringify(userData));\n          }\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error updating profile:', error);\n        this.showError('Failed to update profile');\n        this.loading = false;\n      }\n    });\n  }\n  changePassword() {\n    if (this.passwordForm.invalid) {\n      this.markFormGroupTouched(this.passwordForm);\n      this.showError('Please fill in all required fields correctly');\n      return;\n    }\n    this.loading = true;\n    const passwordData = {\n      currentPassword: this.passwordForm.value.currentPassword,\n      newPassword: this.passwordForm.value.newPassword\n    };\n    this.userService.changePassword(this.currentUser._id, passwordData).subscribe({\n      next: response => {\n        if (response.success) {\n          this.showSuccess('Password changed successfully!');\n          this.passwordForm.reset();\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error changing password:', error);\n        this.showError(error.error?.message || 'Failed to change password');\n        this.loading = false;\n      }\n    });\n  }\n  updateSystemSettings() {\n    if (this.systemForm.invalid) {\n      this.markFormGroupTouched(this.systemForm);\n      this.showError('Please fill in all required fields correctly');\n      return;\n    }\n    this.loading = true;\n    const systemData = this.systemForm.value;\n    // In a real application, you would save these to a backend service\n    // For now, we'll just update the local object and show success\n    this.systemSettings = {\n      ...this.systemSettings,\n      ...systemData\n    };\n    setTimeout(() => {\n      this.showSuccess('System settings updated successfully!');\n      this.loading = false;\n    }, 1000);\n  }\n  resetSystemSettings() {\n    this.systemForm.patchValue(this.systemSettings);\n    this.showSuccess('Settings reset to saved values');\n  }\n  exportData() {\n    this.showSuccess('Data export feature will be implemented soon');\n  }\n  importData() {\n    this.showSuccess('Data import feature will be implemented soon');\n  }\n  backupDatabase() {\n    this.showSuccess('Database backup feature will be implemented soon');\n  }\n  // Utility methods\n  markFormGroupTouched(formGroup) {\n    Object.keys(formGroup.controls).forEach(key => {\n      formGroup.get(key)?.markAsTouched();\n    });\n  }\n  showSuccess(message) {\n    this.snackBar.open(message, 'Close', {\n      duration: 3000,\n      panelClass: ['success-snackbar']\n    });\n  }\n  showError(message) {\n    this.snackBar.open(message, 'Close', {\n      duration: 5000,\n      panelClass: ['error-snackbar']\n    });\n  }\n  // Form getters for validation\n  get name() {\n    return this.profileForm.get('name');\n  }\n  get email() {\n    return this.profileForm.get('email');\n  }\n  get contact() {\n    return this.profileForm.get('contact');\n  }\n  get currentPassword() {\n    return this.passwordForm.get('currentPassword');\n  }\n  get newPassword() {\n    return this.passwordForm.get('newPassword');\n  }\n  get confirmPassword() {\n    return this.passwordForm.get('confirmPassword');\n  }\n  static #_ = this.ɵfac = function SettingsComponent_Factory(t) {\n    return new (t || SettingsComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: SettingsComponent,\n    selectors: [[\"app-settings\"]],\n    decls: 207,\n    vars: 16,\n    consts: [[1, \"settings-container\"], [1, \"page-header\"], [1, \"header-content\"], [1, \"subtitle\"], [1, \"settings-card\"], [3, \"selectedIndex\", \"selectedIndexChange\", \"selectedTabChange\"], [\"label\", \"Profile\"], [1, \"tab-content\"], [1, \"section-header\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"form-grid\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"formControlName\", \"name\", \"placeholder\", \"Enter your full name\"], [4, \"ngIf\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter your email\"], [\"matInput\", \"\", \"formControlName\", \"contact\", \"placeholder\", \"Enter your contact number\"], [\"matInput\", \"\", \"formControlName\", \"address\", \"rows\", \"3\", \"placeholder\", \"Enter your address\"], [\"matInput\", \"\", \"formControlName\", \"qualification\", \"placeholder\", \"Enter your qualification\"], [\"matInput\", \"\", \"formControlName\", \"experience\", \"placeholder\", \"Enter your experience\"], [1, \"form-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [\"diameter\", \"20\", \"class\", \"spinner\", 4, \"ngIf\"], [\"label\", \"Password\"], [1, \"form-grid\", \"password-grid\"], [\"matInput\", \"\", \"type\", \"password\", \"formControlName\", \"currentPassword\", \"placeholder\", \"Enter current password\"], [\"matInput\", \"\", \"type\", \"password\", \"formControlName\", \"newPassword\", \"placeholder\", \"Enter new password\"], [\"matInput\", \"\", \"type\", \"password\", \"formControlName\", \"confirmPassword\", \"placeholder\", \"Confirm new password\"], [\"label\", \"System\"], [1, \"settings-section\"], [\"matInput\", \"\", \"formControlName\", \"instituteName\"], [\"matInput\", \"\", \"formControlName\", \"instituteAddress\", \"rows\", \"2\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"contactEmail\"], [\"matInput\", \"\", \"formControlName\", \"contactPhone\"], [\"matInput\", \"\", \"formControlName\", \"academicYear\"], [\"matInput\", \"\", \"formControlName\", \"currentSemester\"], [1, \"toggle-grid\"], [\"formControlName\", \"allowStudentRegistration\", \"color\", \"primary\"], [\"formControlName\", \"allowTeacherRegistration\", \"color\", \"primary\"], [\"formControlName\", \"enableNotifications\", \"color\", \"primary\"], [\"formControlName\", \"enableEmailNotifications\", \"color\", \"primary\"], [\"formControlName\", \"enableSMSNotifications\", \"color\", \"primary\"], [\"formControlName\", \"maintenanceMode\", \"color\", \"warn\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 3, \"click\"], [\"label\", \"Data\"], [1, \"data-actions\"], [1, \"action-card\"], [1, \"action-content\"], [1, \"action-icon\"], [1, \"action-info\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", 3, \"click\"], [\"diameter\", \"20\", 1, \"spinner\"]],\n    template: function SettingsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\")(4, \"mat-icon\");\n        i0.ɵɵtext(5, \"settings\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(6, \" Settings \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"p\", 3);\n        i0.ɵɵtext(8, \"Manage your profile and system settings\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(9, \"mat-card\", 4)(10, \"mat-tab-group\", 5);\n        i0.ɵɵlistener(\"selectedIndexChange\", function SettingsComponent_Template_mat_tab_group_selectedIndexChange_10_listener($event) {\n          return ctx.selectedTab = $event;\n        })(\"selectedTabChange\", function SettingsComponent_Template_mat_tab_group_selectedTabChange_10_listener($event) {\n          return ctx.onTabChange($event);\n        });\n        i0.ɵɵelementStart(11, \"mat-tab\", 6)(12, \"div\", 7)(13, \"div\", 8)(14, \"h2\");\n        i0.ɵɵtext(15, \"Profile Information\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(16, \"p\");\n        i0.ɵɵtext(17, \"Update your personal information and contact details\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(18, \"form\", 9);\n        i0.ɵɵlistener(\"ngSubmit\", function SettingsComponent_Template_form_ngSubmit_18_listener() {\n          return ctx.updateProfile();\n        });\n        i0.ɵɵelementStart(19, \"div\", 10)(20, \"mat-form-field\", 11)(21, \"mat-label\");\n        i0.ɵɵtext(22, \"Full Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(23, \"input\", 12);\n        i0.ɵɵtemplate(24, SettingsComponent_mat_error_24_Template, 3, 2, \"mat-error\", 13);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"mat-form-field\", 11)(26, \"mat-label\");\n        i0.ɵɵtext(27, \"Email Address\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(28, \"input\", 14);\n        i0.ɵɵtemplate(29, SettingsComponent_mat_error_29_Template, 3, 2, \"mat-error\", 13);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(30, \"mat-form-field\", 11)(31, \"mat-label\");\n        i0.ɵɵtext(32, \"Contact Number\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(33, \"input\", 15);\n        i0.ɵɵtemplate(34, SettingsComponent_mat_error_34_Template, 3, 2, \"mat-error\", 13);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(35, \"mat-form-field\", 11)(36, \"mat-label\");\n        i0.ɵɵtext(37, \"Address\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(38, \"textarea\", 16);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(39, \"mat-form-field\", 11)(40, \"mat-label\");\n        i0.ɵɵtext(41, \"Qualification\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(42, \"input\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(43, \"mat-form-field\", 11)(44, \"mat-label\");\n        i0.ɵɵtext(45, \"Experience\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(46, \"input\", 18);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(47, \"div\", 19)(48, \"button\", 20)(49, \"mat-icon\");\n        i0.ɵɵtext(50, \"save\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(51, \" Update Profile \");\n        i0.ɵɵtemplate(52, SettingsComponent_mat_spinner_52_Template, 1, 0, \"mat-spinner\", 21);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(53, \"mat-tab\", 22)(54, \"div\", 7)(55, \"div\", 8)(56, \"h2\");\n        i0.ɵɵtext(57, \"Change Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(58, \"p\");\n        i0.ɵɵtext(59, \"Update your account password for security\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(60, \"form\", 9);\n        i0.ɵɵlistener(\"ngSubmit\", function SettingsComponent_Template_form_ngSubmit_60_listener() {\n          return ctx.changePassword();\n        });\n        i0.ɵɵelementStart(61, \"div\", 23)(62, \"mat-form-field\", 11)(63, \"mat-label\");\n        i0.ɵɵtext(64, \"Current Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(65, \"input\", 24);\n        i0.ɵɵtemplate(66, SettingsComponent_mat_error_66_Template, 2, 0, \"mat-error\", 13);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(67, \"mat-form-field\", 11)(68, \"mat-label\");\n        i0.ɵɵtext(69, \"New Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(70, \"input\", 25);\n        i0.ɵɵtemplate(71, SettingsComponent_mat_error_71_Template, 3, 2, \"mat-error\", 13);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(72, \"mat-form-field\", 11)(73, \"mat-label\");\n        i0.ɵɵtext(74, \"Confirm New Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(75, \"input\", 26);\n        i0.ɵɵtemplate(76, SettingsComponent_mat_error_76_Template, 3, 2, \"mat-error\", 13);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(77, \"div\", 19)(78, \"button\", 20)(79, \"mat-icon\");\n        i0.ɵɵtext(80, \"lock\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(81, \" Change Password \");\n        i0.ɵɵtemplate(82, SettingsComponent_mat_spinner_82_Template, 1, 0, \"mat-spinner\", 21);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(83, \"mat-tab\", 27)(84, \"div\", 7)(85, \"div\", 8)(86, \"h2\");\n        i0.ɵɵtext(87, \"System Configuration\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(88, \"p\");\n        i0.ɵɵtext(89, \"Configure system-wide settings and preferences\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(90, \"form\", 9);\n        i0.ɵɵlistener(\"ngSubmit\", function SettingsComponent_Template_form_ngSubmit_90_listener() {\n          return ctx.updateSystemSettings();\n        });\n        i0.ɵɵelementStart(91, \"div\", 28)(92, \"h3\");\n        i0.ɵɵtext(93, \"Institute Information\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(94, \"div\", 10)(95, \"mat-form-field\", 11)(96, \"mat-label\");\n        i0.ɵɵtext(97, \"Institute Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(98, \"input\", 29);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(99, \"mat-form-field\", 11)(100, \"mat-label\");\n        i0.ɵɵtext(101, \"Institute Address\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(102, \"textarea\", 30);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(103, \"mat-form-field\", 11)(104, \"mat-label\");\n        i0.ɵɵtext(105, \"Contact Email\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(106, \"input\", 31);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(107, \"mat-form-field\", 11)(108, \"mat-label\");\n        i0.ɵɵtext(109, \"Contact Phone\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(110, \"input\", 32);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(111, \"div\", 28)(112, \"h3\");\n        i0.ɵɵtext(113, \"Academic Settings\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(114, \"div\", 10)(115, \"mat-form-field\", 11)(116, \"mat-label\");\n        i0.ɵɵtext(117, \"Academic Year\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(118, \"input\", 33);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(119, \"mat-form-field\", 11)(120, \"mat-label\");\n        i0.ɵɵtext(121, \"Current Semester\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(122, \"input\", 34);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(123, \"div\", 28)(124, \"h3\");\n        i0.ɵɵtext(125, \"Registration Settings\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(126, \"div\", 35)(127, \"mat-slide-toggle\", 36);\n        i0.ɵɵtext(128, \" Allow Student Registration \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(129, \"mat-slide-toggle\", 37);\n        i0.ɵɵtext(130, \" Allow Teacher Registration \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(131, \"div\", 28)(132, \"h3\");\n        i0.ɵɵtext(133, \"Notification Settings\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(134, \"div\", 35)(135, \"mat-slide-toggle\", 38);\n        i0.ɵɵtext(136, \" Enable Notifications \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(137, \"mat-slide-toggle\", 39);\n        i0.ɵɵtext(138, \" Enable Email Notifications \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(139, \"mat-slide-toggle\", 40);\n        i0.ɵɵtext(140, \" Enable SMS Notifications \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(141, \"div\", 28)(142, \"h3\");\n        i0.ɵɵtext(143, \"System Maintenance\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(144, \"div\", 35)(145, \"mat-slide-toggle\", 41);\n        i0.ɵɵtext(146, \" Maintenance Mode \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(147, \"div\", 19)(148, \"button\", 42);\n        i0.ɵɵlistener(\"click\", function SettingsComponent_Template_button_click_148_listener() {\n          return ctx.resetSystemSettings();\n        });\n        i0.ɵɵelementStart(149, \"mat-icon\");\n        i0.ɵɵtext(150, \"refresh\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(151, \" Reset \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(152, \"button\", 20)(153, \"mat-icon\");\n        i0.ɵɵtext(154, \"save\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(155, \" Save Settings \");\n        i0.ɵɵtemplate(156, SettingsComponent_mat_spinner_156_Template, 1, 0, \"mat-spinner\", 21);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(157, \"mat-tab\", 43)(158, \"div\", 7)(159, \"div\", 8)(160, \"h2\");\n        i0.ɵɵtext(161, \"Data Management\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(162, \"p\");\n        i0.ɵɵtext(163, \"Export, import, and backup your data\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(164, \"div\", 44)(165, \"mat-card\", 45)(166, \"mat-card-content\")(167, \"div\", 46)(168, \"mat-icon\", 47);\n        i0.ɵɵtext(169, \"download\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(170, \"div\", 48)(171, \"h3\");\n        i0.ɵɵtext(172, \"Export Data\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(173, \"p\");\n        i0.ɵɵtext(174, \"Download all system data as backup files\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(175, \"button\", 49);\n        i0.ɵɵlistener(\"click\", function SettingsComponent_Template_button_click_175_listener() {\n          return ctx.exportData();\n        });\n        i0.ɵɵelementStart(176, \"mat-icon\");\n        i0.ɵɵtext(177, \"download\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(178, \" Export \");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(179, \"mat-card\", 45)(180, \"mat-card-content\")(181, \"div\", 46)(182, \"mat-icon\", 47);\n        i0.ɵɵtext(183, \"upload\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(184, \"div\", 48)(185, \"h3\");\n        i0.ɵɵtext(186, \"Import Data\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(187, \"p\");\n        i0.ɵɵtext(188, \"Import data from backup files\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(189, \"button\", 50);\n        i0.ɵɵlistener(\"click\", function SettingsComponent_Template_button_click_189_listener() {\n          return ctx.importData();\n        });\n        i0.ɵɵelementStart(190, \"mat-icon\");\n        i0.ɵɵtext(191, \"upload\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(192, \" Import \");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(193, \"mat-card\", 45)(194, \"mat-card-content\")(195, \"div\", 46)(196, \"mat-icon\", 47);\n        i0.ɵɵtext(197, \"backup\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(198, \"div\", 48)(199, \"h3\");\n        i0.ɵɵtext(200, \"Database Backup\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(201, \"p\");\n        i0.ɵɵtext(202, \"Create a complete database backup\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(203, \"button\", 51);\n        i0.ɵɵlistener(\"click\", function SettingsComponent_Template_button_click_203_listener() {\n          return ctx.backupDatabase();\n        });\n        i0.ɵɵelementStart(204, \"mat-icon\");\n        i0.ɵɵtext(205, \"backup\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(206, \" Backup \");\n        i0.ɵɵelementEnd()()()()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"selectedIndex\", ctx.selectedTab);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"formGroup\", ctx.profileForm);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngIf\", (ctx.name == null ? null : ctx.name.invalid) && (ctx.name == null ? null : ctx.name.touched));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", (ctx.email == null ? null : ctx.email.invalid) && (ctx.email == null ? null : ctx.email.touched));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", (ctx.contact == null ? null : ctx.contact.invalid) && (ctx.contact == null ? null : ctx.contact.touched));\n        i0.ɵɵadvance(14);\n        i0.ɵɵproperty(\"disabled\", ctx.loading || ctx.profileForm.invalid);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"formGroup\", ctx.passwordForm);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngIf\", (ctx.currentPassword == null ? null : ctx.currentPassword.invalid) && (ctx.currentPassword == null ? null : ctx.currentPassword.touched));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", (ctx.newPassword == null ? null : ctx.newPassword.invalid) && (ctx.newPassword == null ? null : ctx.newPassword.touched));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", (ctx.confirmPassword == null ? null : ctx.confirmPassword.invalid) && (ctx.confirmPassword == null ? null : ctx.confirmPassword.touched));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"disabled\", ctx.loading || ctx.passwordForm.invalid);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"formGroup\", ctx.systemForm);\n        i0.ɵɵadvance(62);\n        i0.ɵɵproperty(\"disabled\", ctx.loading || ctx.systemForm.invalid);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n      }\n    },\n    dependencies: [i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i5.MatCard, i5.MatCardContent, i6.MatButton, i7.MatIcon, i8.MatFormField, i8.MatLabel, i8.MatError, i9.MatInput, i10.MatTab, i10.MatTabGroup, i11.MatSlideToggle, i12.MatProgressSpinner],\n    styles: [\".settings-container[_ngcontent-%COMP%] {\\n  padding: 24px;\\n  background-color: #f5f5f5;\\n  min-height: 100vh;\\n}\\n\\n\\n\\n.page-header[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n  background: white;\\n  padding: 24px;\\n  border-radius: 12px;\\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\\n}\\n\\n.header-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  margin: 0 0 8px 0;\\n  color: #333;\\n  font-size: 1.8rem;\\n  font-weight: 600;\\n}\\n\\n.header-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n}\\n\\n.subtitle[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 1rem;\\n}\\n\\n\\n\\n.settings-card[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\\n  overflow: hidden;\\n}\\n\\n\\n\\n.tab-content[_ngcontent-%COMP%] {\\n  padding: 32px;\\n  max-width: 800px;\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n  padding-bottom: 16px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #333;\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n}\\n\\n.section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 0.95rem;\\n}\\n\\n\\n\\n.form-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 24px;\\n  margin-bottom: 32px;\\n}\\n\\n.password-grid[_ngcontent-%COMP%] {\\n  grid-template-columns: 1fr;\\n  max-width: 400px;\\n}\\n\\n.form-grid[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.form-grid[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]:last-child:nth-child(odd) {\\n  grid-column: 1 / -1;\\n}\\n\\n.form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  justify-content: flex-end;\\n  padding-top: 24px;\\n  border-top: 1px solid #e0e0e0;\\n}\\n\\n.form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 140px;\\n  height: 44px;\\n  font-weight: 500;\\n}\\n\\n.spinner[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n}\\n\\n\\n\\n.settings-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n  padding: 24px;\\n  background: #f8f9fa;\\n  border-radius: 8px;\\n  border-left: 4px solid #1976d2;\\n}\\n\\n.settings-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  color: #333;\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n}\\n\\n.toggle-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 16px;\\n}\\n\\n.toggle-grid[_ngcontent-%COMP%]   mat-slide-toggle[_ngcontent-%COMP%] {\\n  font-size: 0.95rem;\\n}\\n\\n\\n\\n.data-actions[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr;\\n  gap: 24px;\\n}\\n\\n.action-card[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\\n}\\n\\n.action-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(0,0,0,0.15);\\n}\\n\\n.action-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 20px;\\n  padding: 8px;\\n}\\n\\n.action-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  width: 48px;\\n  height: 48px;\\n  color: #1976d2;\\n  flex-shrink: 0;\\n}\\n\\n.action-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.action-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #333;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n}\\n\\n.action-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 0.9rem;\\n}\\n\\n.action-content[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 120px;\\n  height: 40px;\\n  flex-shrink: 0;\\n}\\n\\n\\n\\n  .mat-tab-group {\\n  background: white;\\n}\\n\\n  .mat-tab-header {\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n  .mat-tab-label {\\n  min-width: 120px;\\n  padding: 0 24px;\\n  font-weight: 500;\\n}\\n\\n  .mat-tab-label-active {\\n  color: #1976d2;\\n}\\n\\n  .mat-ink-bar {\\n  background-color: #1976d2;\\n  height: 3px;\\n}\\n\\n\\n\\n  .mat-form-field-appearance-outline .mat-form-field-outline {\\n  color: #e0e0e0;\\n}\\n\\n  .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {\\n  color: #1976d2;\\n}\\n\\n  .mat-form-field-appearance-outline .mat-form-field-label {\\n  color: #666;\\n}\\n\\n  .mat-form-field-appearance-outline.mat-focused .mat-form-field-label {\\n  color: #1976d2;\\n}\\n\\n\\n\\n  .mat-slide-toggle.mat-checked .mat-slide-toggle-thumb {\\n  background-color: #1976d2;\\n}\\n\\n  .mat-slide-toggle.mat-checked .mat-slide-toggle-bar {\\n  background-color: rgba(25, 118, 210, 0.5);\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .settings-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n\\n  .tab-content[_ngcontent-%COMP%] {\\n    padding: 24px 16px;\\n  }\\n\\n  .form-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 16px;\\n  }\\n\\n  .toggle-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n\\n  .form-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n  }\\n\\n  .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n\\n  .action-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n    gap: 16px;\\n  }\\n\\n  .action-content[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n\\n  .settings-section[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n}\\n\\n@media (max-width: 480px) {\\n  .page-header[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n\\n  .header-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  .section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n    font-size: 1.3rem;\\n  }\\n\\n    .mat-tab-label {\\n    min-width: 80px;\\n    padding: 0 12px;\\n    font-size: 0.9rem;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "SettingsComponent_mat_error_24_span_1_Template", "SettingsComponent_mat_error_24_span_2_Template", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "name", "errors", "SettingsComponent_mat_error_29_span_1_Template", "SettingsComponent_mat_error_29_span_2_Template", "ctx_r1", "email", "SettingsComponent_mat_error_34_span_1_Template", "SettingsComponent_mat_error_34_span_2_Template", "ctx_r2", "contact", "ɵɵelement", "SettingsComponent_mat_error_71_span_1_Template", "SettingsComponent_mat_error_71_span_2_Template", "ctx_r5", "newPassword", "SettingsComponent_mat_error_76_span_1_Template", "SettingsComponent_mat_error_76_span_2_Template", "ctx_r6", "confirmPassword", "SettingsComponent", "constructor", "fb", "userService", "snackBar", "loading", "selectedTab", "systemSettings", "instituteName", "instituteAddress", "contactEmail", "contactPhone", "academicYear", "currentSemester", "allowStudentRegistration", "allowTeacherRegistration", "enableNotifications", "enableEmailNotifications", "enableSMSNotifications", "maintenanceMode", "profileForm", "createProfileForm", "passwordForm", "createPasswordForm", "systemForm", "createSystemForm", "ngOnInit", "loadCurrentUser", "loadSystemSettings", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "pattern", "address", "qualification", "experience", "joiningDate", "currentPassword", "validators", "passwordMatchValidator", "form", "get", "value", "setErrors", "passwordMismatch", "userData", "getUserFromLocalStorage", "user", "currentUser", "patchValue", "onTabChange", "index", "updateProfile", "invalid", "markFormGroupTouched", "showError", "profileData", "updateUserProfile", "_id", "subscribe", "next", "response", "success", "showSuccess", "localStorage", "setItem", "JSON", "stringify", "error", "console", "changePassword", "passwordData", "reset", "message", "updateSystemSettings", "systemData", "setTimeout", "resetSystemSettings", "exportData", "importData", "backupDatabase", "formGroup", "Object", "keys", "controls", "for<PERSON>ach", "key", "<PERSON><PERSON><PERSON><PERSON>ched", "open", "duration", "panelClass", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "UserService", "i3", "MatSnackBar", "_2", "selectors", "decls", "vars", "consts", "template", "SettingsComponent_Template", "rf", "ctx", "ɵɵlistener", "SettingsComponent_Template_mat_tab_group_selectedIndexChange_10_listener", "$event", "SettingsComponent_Template_mat_tab_group_selectedTabChange_10_listener", "SettingsComponent_Template_form_ngSubmit_18_listener", "SettingsComponent_mat_error_24_Template", "SettingsComponent_mat_error_29_Template", "SettingsComponent_mat_error_34_Template", "SettingsComponent_mat_spinner_52_Template", "SettingsComponent_Template_form_ngSubmit_60_listener", "SettingsComponent_mat_error_66_Template", "SettingsComponent_mat_error_71_Template", "SettingsComponent_mat_error_76_Template", "SettingsComponent_mat_spinner_82_Template", "SettingsComponent_Template_form_ngSubmit_90_listener", "SettingsComponent_Template_button_click_148_listener", "SettingsComponent_mat_spinner_156_Template", "SettingsComponent_Template_button_click_175_listener", "SettingsComponent_Template_button_click_189_listener", "SettingsComponent_Template_button_click_203_listener", "touched"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\settings\\settings.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\settings\\settings.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { UserService } from 'src/app/services/user.service';\r\n\r\n@Component({\r\n  selector: 'app-settings',\r\n  templateUrl: './settings.component.html',\r\n  styleUrls: ['./settings.component.css']\r\n})\r\nexport class SettingsComponent implements OnInit {\r\n  profileForm: FormGroup;\r\n  passwordForm: FormGroup;\r\n  systemForm: FormGroup;\r\n  \r\n  loading = false;\r\n  currentUser: any;\r\n  \r\n  // Settings tabs\r\n  selectedTab = 0;\r\n  \r\n  // System settings\r\n  systemSettings = {\r\n    instituteName: 'Government Post Graduate College',\r\n    instituteAddress: 'Swabi, KPK, Pakistan',\r\n    contactEmail: '<EMAIL>',\r\n    contactPhone: '+92-938-222222',\r\n    academicYear: '2024-2025',\r\n    currentSemester: 'Fall 2024',\r\n    allowStudentRegistration: true,\r\n    allowTeacherRegistration: false,\r\n    enableNotifications: true,\r\n    enableEmailNotifications: true,\r\n    enableSMSNotifications: false,\r\n    maintenanceMode: false\r\n  };\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private userService: UserService,\r\n    private snackBar: MatSnackBar\r\n  ) {\r\n    this.profileForm = this.createProfileForm();\r\n    this.passwordForm = this.createPasswordForm();\r\n    this.systemForm = this.createSystemForm();\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.loadCurrentUser();\r\n    this.loadSystemSettings();\r\n  }\r\n\r\n  createProfileForm(): FormGroup {\r\n    return this.fb.group({\r\n      name: ['', [Validators.required, Validators.minLength(2)]],\r\n      email: ['', [Validators.required, Validators.email]],\r\n      contact: ['', [Validators.required, Validators.pattern(/^[0-9]{10,15}$/)]],\r\n      address: [''],\r\n      qualification: [''],\r\n      experience: [''],\r\n      joiningDate: ['']\r\n    });\r\n  }\r\n\r\n  createPasswordForm(): FormGroup {\r\n    return this.fb.group({\r\n      currentPassword: ['', [Validators.required]],\r\n      newPassword: ['', [Validators.required, Validators.minLength(6)]],\r\n      confirmPassword: ['', [Validators.required]]\r\n    }, { validators: this.passwordMatchValidator });\r\n  }\r\n\r\n  createSystemForm(): FormGroup {\r\n    return this.fb.group({\r\n      instituteName: [this.systemSettings.instituteName, Validators.required],\r\n      instituteAddress: [this.systemSettings.instituteAddress, Validators.required],\r\n      contactEmail: [this.systemSettings.contactEmail, [Validators.required, Validators.email]],\r\n      contactPhone: [this.systemSettings.contactPhone, Validators.required],\r\n      academicYear: [this.systemSettings.academicYear, Validators.required],\r\n      currentSemester: [this.systemSettings.currentSemester, Validators.required],\r\n      allowStudentRegistration: [this.systemSettings.allowStudentRegistration],\r\n      allowTeacherRegistration: [this.systemSettings.allowTeacherRegistration],\r\n      enableNotifications: [this.systemSettings.enableNotifications],\r\n      enableEmailNotifications: [this.systemSettings.enableEmailNotifications],\r\n      enableSMSNotifications: [this.systemSettings.enableSMSNotifications],\r\n      maintenanceMode: [this.systemSettings.maintenanceMode]\r\n    });\r\n  }\r\n\r\n  passwordMatchValidator(form: FormGroup) {\r\n    const newPassword = form.get('newPassword');\r\n    const confirmPassword = form.get('confirmPassword');\r\n    \r\n    if (newPassword && confirmPassword && newPassword.value !== confirmPassword.value) {\r\n      confirmPassword.setErrors({ passwordMismatch: true });\r\n      return { passwordMismatch: true };\r\n    }\r\n    \r\n    return null;\r\n  }\r\n\r\n  loadCurrentUser(): void {\r\n    const userData = this.userService.getUserFromLocalStorage();\r\n    if (userData && userData.user) {\r\n      this.currentUser = userData.user;\r\n      this.profileForm.patchValue({\r\n        name: this.currentUser.name,\r\n        email: this.currentUser.email,\r\n        contact: this.currentUser.contact,\r\n        address: this.currentUser.address,\r\n        qualification: this.currentUser.qualification,\r\n        experience: this.currentUser.experience,\r\n        joiningDate: this.currentUser.joiningDate\r\n      });\r\n    }\r\n  }\r\n\r\n  loadSystemSettings(): void {\r\n    // In a real application, you would load these from a backend service\r\n    // For now, we'll use the default values\r\n    this.systemForm.patchValue(this.systemSettings);\r\n  }\r\n\r\n  onTabChange(index: any): void {\r\n    this.selectedTab = index;\r\n  }\r\n\r\n  updateProfile(): void {\r\n    if (this.profileForm.invalid) {\r\n      this.markFormGroupTouched(this.profileForm);\r\n      this.showError('Please fill in all required fields correctly');\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n    const profileData = this.profileForm.value;\r\n\r\n    this.userService.updateUserProfile(this.currentUser._id, profileData).subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.showSuccess('Profile updated successfully!');\r\n          // Update local storage\r\n          const userData = this.userService.getUserFromLocalStorage();\r\n          if (userData) {\r\n            userData.user = { ...userData.user, ...profileData };\r\n            localStorage.setItem('user', JSON.stringify(userData));\r\n          }\r\n        }\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error updating profile:', error);\r\n        this.showError('Failed to update profile');\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  changePassword(): void {\r\n    if (this.passwordForm.invalid) {\r\n      this.markFormGroupTouched(this.passwordForm);\r\n      this.showError('Please fill in all required fields correctly');\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n    const passwordData = {\r\n      currentPassword: this.passwordForm.value.currentPassword,\r\n      newPassword: this.passwordForm.value.newPassword\r\n    };\r\n\r\n    this.userService.changePassword(this.currentUser._id, passwordData).subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.showSuccess('Password changed successfully!');\r\n          this.passwordForm.reset();\r\n        }\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error changing password:', error);\r\n        this.showError(error.error?.message || 'Failed to change password');\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  updateSystemSettings(): void {\r\n    if (this.systemForm.invalid) {\r\n      this.markFormGroupTouched(this.systemForm);\r\n      this.showError('Please fill in all required fields correctly');\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n    const systemData = this.systemForm.value;\r\n\r\n    // In a real application, you would save these to a backend service\r\n    // For now, we'll just update the local object and show success\r\n    this.systemSettings = { ...this.systemSettings, ...systemData };\r\n    \r\n    setTimeout(() => {\r\n      this.showSuccess('System settings updated successfully!');\r\n      this.loading = false;\r\n    }, 1000);\r\n  }\r\n\r\n  resetSystemSettings(): void {\r\n    this.systemForm.patchValue(this.systemSettings);\r\n    this.showSuccess('Settings reset to saved values');\r\n  }\r\n\r\n  exportData(): void {\r\n    this.showSuccess('Data export feature will be implemented soon');\r\n  }\r\n\r\n  importData(): void {\r\n    this.showSuccess('Data import feature will be implemented soon');\r\n  }\r\n\r\n  backupDatabase(): void {\r\n    this.showSuccess('Database backup feature will be implemented soon');\r\n  }\r\n\r\n  // Utility methods\r\n  private markFormGroupTouched(formGroup: FormGroup): void {\r\n    Object.keys(formGroup.controls).forEach(key => {\r\n      formGroup.get(key)?.markAsTouched();\r\n    });\r\n  }\r\n\r\n  private showSuccess(message: string): void {\r\n    this.snackBar.open(message, 'Close', {\r\n      duration: 3000,\r\n      panelClass: ['success-snackbar']\r\n    });\r\n  }\r\n\r\n  private showError(message: string): void {\r\n    this.snackBar.open(message, 'Close', {\r\n      duration: 5000,\r\n      panelClass: ['error-snackbar']\r\n    });\r\n  }\r\n\r\n  // Form getters for validation\r\n  get name() { return this.profileForm.get('name'); }\r\n  get email() { return this.profileForm.get('email'); }\r\n  get contact() { return this.profileForm.get('contact'); }\r\n  get currentPassword() { return this.passwordForm.get('currentPassword'); }\r\n  get newPassword() { return this.passwordForm.get('newPassword'); }\r\n  get confirmPassword() { return this.passwordForm.get('confirmPassword'); }\r\n}\r\n", "<div class=\"settings-container\">\r\n  <!-- Header -->\r\n  <div class=\"page-header\">\r\n    <div class=\"header-content\">\r\n      <h1>\r\n        <mat-icon>settings</mat-icon>\r\n        Settings\r\n      </h1>\r\n      <p class=\"subtitle\">Manage your profile and system settings</p>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Settings Tabs -->\r\n  <mat-card class=\"settings-card\">\r\n    <mat-tab-group [(selectedIndex)]=\"selectedTab\" (selectedTabChange)=\"onTabChange($event)\">\r\n      \r\n      <!-- Profile Settings Tab -->\r\n      <mat-tab label=\"Profile\">\r\n        <div class=\"tab-content\">\r\n          <div class=\"section-header\">\r\n            <h2>Profile Information</h2>\r\n            <p>Update your personal information and contact details</p>\r\n          </div>\r\n\r\n          <form [formGroup]=\"profileForm\" (ngSubmit)=\"updateProfile()\">\r\n            <div class=\"form-grid\">\r\n              <mat-form-field appearance=\"outline\">\r\n                <mat-label>Full Name</mat-label>\r\n                <input matInput formControlName=\"name\" placeholder=\"Enter your full name\">\r\n                <mat-error *ngIf=\"name?.invalid && name?.touched\">\r\n                  <span *ngIf=\"name?.errors?.['required']\">Name is required</span>\r\n                  <span *ngIf=\"name?.errors?.['minlength']\">Name must be at least 2 characters</span>\r\n                </mat-error>\r\n              </mat-form-field>\r\n\r\n              <mat-form-field appearance=\"outline\">\r\n                <mat-label>Email Address</mat-label>\r\n                <input matInput type=\"email\" formControlName=\"email\" placeholder=\"Enter your email\">\r\n                <mat-error *ngIf=\"email?.invalid && email?.touched\">\r\n                  <span *ngIf=\"email?.errors?.['required']\">Email is required</span>\r\n                  <span *ngIf=\"email?.errors?.['email']\">Please enter a valid email</span>\r\n                </mat-error>\r\n              </mat-form-field>\r\n\r\n              <mat-form-field appearance=\"outline\">\r\n                <mat-label>Contact Number</mat-label>\r\n                <input matInput formControlName=\"contact\" placeholder=\"Enter your contact number\">\r\n                <mat-error *ngIf=\"contact?.invalid && contact?.touched\">\r\n                  <span *ngIf=\"contact?.errors?.['required']\">Contact number is required</span>\r\n                  <span *ngIf=\"contact?.errors?.['pattern']\">Please enter a valid contact number</span>\r\n                </mat-error>\r\n              </mat-form-field>\r\n\r\n              <mat-form-field appearance=\"outline\">\r\n                <mat-label>Address</mat-label>\r\n                <textarea matInput formControlName=\"address\" rows=\"3\" placeholder=\"Enter your address\"></textarea>\r\n              </mat-form-field>\r\n\r\n              <mat-form-field appearance=\"outline\">\r\n                <mat-label>Qualification</mat-label>\r\n                <input matInput formControlName=\"qualification\" placeholder=\"Enter your qualification\">\r\n              </mat-form-field>\r\n\r\n              <mat-form-field appearance=\"outline\">\r\n                <mat-label>Experience</mat-label>\r\n                <input matInput formControlName=\"experience\" placeholder=\"Enter your experience\">\r\n              </mat-form-field>\r\n            </div>\r\n\r\n            <div class=\"form-actions\">\r\n              <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"loading || profileForm.invalid\">\r\n                <mat-icon>save</mat-icon>\r\n                Update Profile\r\n                <mat-spinner *ngIf=\"loading\" diameter=\"20\" class=\"spinner\"></mat-spinner>\r\n              </button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </mat-tab>\r\n\r\n      <!-- Password Settings Tab -->\r\n      <mat-tab label=\"Password\">\r\n        <div class=\"tab-content\">\r\n          <div class=\"section-header\">\r\n            <h2>Change Password</h2>\r\n            <p>Update your account password for security</p>\r\n          </div>\r\n\r\n          <form [formGroup]=\"passwordForm\" (ngSubmit)=\"changePassword()\">\r\n            <div class=\"form-grid password-grid\">\r\n              <mat-form-field appearance=\"outline\">\r\n                <mat-label>Current Password</mat-label>\r\n                <input matInput type=\"password\" formControlName=\"currentPassword\" placeholder=\"Enter current password\">\r\n                <mat-error *ngIf=\"currentPassword?.invalid && currentPassword?.touched\">\r\n                  Current password is required\r\n                </mat-error>\r\n              </mat-form-field>\r\n\r\n              <mat-form-field appearance=\"outline\">\r\n                <mat-label>New Password</mat-label>\r\n                <input matInput type=\"password\" formControlName=\"newPassword\" placeholder=\"Enter new password\">\r\n                <mat-error *ngIf=\"newPassword?.invalid && newPassword?.touched\">\r\n                  <span *ngIf=\"newPassword?.errors?.['required']\">New password is required</span>\r\n                  <span *ngIf=\"newPassword?.errors?.['minlength']\">Password must be at least 6 characters</span>\r\n                </mat-error>\r\n              </mat-form-field>\r\n\r\n              <mat-form-field appearance=\"outline\">\r\n                <mat-label>Confirm New Password</mat-label>\r\n                <input matInput type=\"password\" formControlName=\"confirmPassword\" placeholder=\"Confirm new password\">\r\n                <mat-error *ngIf=\"confirmPassword?.invalid && confirmPassword?.touched\">\r\n                  <span *ngIf=\"confirmPassword?.errors?.['required']\">Please confirm your password</span>\r\n                  <span *ngIf=\"confirmPassword?.errors?.['passwordMismatch']\">Passwords do not match</span>\r\n                </mat-error>\r\n              </mat-form-field>\r\n            </div>\r\n\r\n            <div class=\"form-actions\">\r\n              <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"loading || passwordForm.invalid\">\r\n                <mat-icon>lock</mat-icon>\r\n                Change Password\r\n                <mat-spinner *ngIf=\"loading\" diameter=\"20\" class=\"spinner\"></mat-spinner>\r\n              </button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </mat-tab>\r\n\r\n      <!-- System Settings Tab -->\r\n      <mat-tab label=\"System\">\r\n        <div class=\"tab-content\">\r\n          <div class=\"section-header\">\r\n            <h2>System Configuration</h2>\r\n            <p>Configure system-wide settings and preferences</p>\r\n          </div>\r\n\r\n          <form [formGroup]=\"systemForm\" (ngSubmit)=\"updateSystemSettings()\">\r\n            <!-- Institute Information -->\r\n            <div class=\"settings-section\">\r\n              <h3>Institute Information</h3>\r\n              <div class=\"form-grid\">\r\n                <mat-form-field appearance=\"outline\">\r\n                  <mat-label>Institute Name</mat-label>\r\n                  <input matInput formControlName=\"instituteName\">\r\n                </mat-form-field>\r\n\r\n                <mat-form-field appearance=\"outline\">\r\n                  <mat-label>Institute Address</mat-label>\r\n                  <textarea matInput formControlName=\"instituteAddress\" rows=\"2\"></textarea>\r\n                </mat-form-field>\r\n\r\n                <mat-form-field appearance=\"outline\">\r\n                  <mat-label>Contact Email</mat-label>\r\n                  <input matInput type=\"email\" formControlName=\"contactEmail\">\r\n                </mat-form-field>\r\n\r\n                <mat-form-field appearance=\"outline\">\r\n                  <mat-label>Contact Phone</mat-label>\r\n                  <input matInput formControlName=\"contactPhone\">\r\n                </mat-form-field>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Academic Settings -->\r\n            <div class=\"settings-section\">\r\n              <h3>Academic Settings</h3>\r\n              <div class=\"form-grid\">\r\n                <mat-form-field appearance=\"outline\">\r\n                  <mat-label>Academic Year</mat-label>\r\n                  <input matInput formControlName=\"academicYear\">\r\n                </mat-form-field>\r\n\r\n                <mat-form-field appearance=\"outline\">\r\n                  <mat-label>Current Semester</mat-label>\r\n                  <input matInput formControlName=\"currentSemester\">\r\n                </mat-form-field>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Registration Settings -->\r\n            <div class=\"settings-section\">\r\n              <h3>Registration Settings</h3>\r\n              <div class=\"toggle-grid\">\r\n                <mat-slide-toggle formControlName=\"allowStudentRegistration\" color=\"primary\">\r\n                  Allow Student Registration\r\n                </mat-slide-toggle>\r\n                <mat-slide-toggle formControlName=\"allowTeacherRegistration\" color=\"primary\">\r\n                  Allow Teacher Registration\r\n                </mat-slide-toggle>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Notification Settings -->\r\n            <div class=\"settings-section\">\r\n              <h3>Notification Settings</h3>\r\n              <div class=\"toggle-grid\">\r\n                <mat-slide-toggle formControlName=\"enableNotifications\" color=\"primary\">\r\n                  Enable Notifications\r\n                </mat-slide-toggle>\r\n                <mat-slide-toggle formControlName=\"enableEmailNotifications\" color=\"primary\">\r\n                  Enable Email Notifications\r\n                </mat-slide-toggle>\r\n                <mat-slide-toggle formControlName=\"enableSMSNotifications\" color=\"primary\">\r\n                  Enable SMS Notifications\r\n                </mat-slide-toggle>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- System Maintenance -->\r\n            <div class=\"settings-section\">\r\n              <h3>System Maintenance</h3>\r\n              <div class=\"toggle-grid\">\r\n                <mat-slide-toggle formControlName=\"maintenanceMode\" color=\"warn\">\r\n                  Maintenance Mode\r\n                </mat-slide-toggle>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"form-actions\">\r\n              <button mat-stroked-button type=\"button\" (click)=\"resetSystemSettings()\">\r\n                <mat-icon>refresh</mat-icon>\r\n                Reset\r\n              </button>\r\n              <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"loading || systemForm.invalid\">\r\n                <mat-icon>save</mat-icon>\r\n                Save Settings\r\n                <mat-spinner *ngIf=\"loading\" diameter=\"20\" class=\"spinner\"></mat-spinner>\r\n              </button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </mat-tab>\r\n\r\n      <!-- Data Management Tab -->\r\n      <mat-tab label=\"Data\">\r\n        <div class=\"tab-content\">\r\n          <div class=\"section-header\">\r\n            <h2>Data Management</h2>\r\n            <p>Export, import, and backup your data</p>\r\n          </div>\r\n\r\n          <div class=\"data-actions\">\r\n            <mat-card class=\"action-card\">\r\n              <mat-card-content>\r\n                <div class=\"action-content\">\r\n                  <mat-icon class=\"action-icon\">download</mat-icon>\r\n                  <div class=\"action-info\">\r\n                    <h3>Export Data</h3>\r\n                    <p>Download all system data as backup files</p>\r\n                  </div>\r\n                  <button mat-raised-button color=\"primary\" (click)=\"exportData()\">\r\n                    <mat-icon>download</mat-icon>\r\n                    Export\r\n                  </button>\r\n                </div>\r\n              </mat-card-content>\r\n            </mat-card>\r\n\r\n            <mat-card class=\"action-card\">\r\n              <mat-card-content>\r\n                <div class=\"action-content\">\r\n                  <mat-icon class=\"action-icon\">upload</mat-icon>\r\n                  <div class=\"action-info\">\r\n                    <h3>Import Data</h3>\r\n                    <p>Import data from backup files</p>\r\n                  </div>\r\n                  <button mat-raised-button color=\"accent\" (click)=\"importData()\">\r\n                    <mat-icon>upload</mat-icon>\r\n                    Import\r\n                  </button>\r\n                </div>\r\n              </mat-card-content>\r\n            </mat-card>\r\n\r\n            <mat-card class=\"action-card\">\r\n              <mat-card-content>\r\n                <div class=\"action-content\">\r\n                  <mat-icon class=\"action-icon\">backup</mat-icon>\r\n                  <div class=\"action-info\">\r\n                    <h3>Database Backup</h3>\r\n                    <p>Create a complete database backup</p>\r\n                  </div>\r\n                  <button mat-raised-button color=\"warn\" (click)=\"backupDatabase()\">\r\n                    <mat-icon>backup</mat-icon>\r\n                    Backup\r\n                  </button>\r\n                </div>\r\n              </mat-card-content>\r\n            </mat-card>\r\n          </div>\r\n        </div>\r\n      </mat-tab>\r\n\r\n    </mat-tab-group>\r\n  </mat-card>\r\n</div>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;;IC6BjDC,EAAA,CAAAC,cAAA,WAAyC;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAChEH,EAAA,CAAAC,cAAA,WAA0C;IAAAD,EAAA,CAAAE,MAAA,yCAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFrFH,EAAA,CAAAC,cAAA,gBAAkD;IAChDD,EAAA,CAAAI,UAAA,IAAAC,8CAAA,mBAAgE;IAChEL,EAAA,CAAAI,UAAA,IAAAE,8CAAA,mBAAmF;IACrFN,EAAA,CAAAG,YAAA,EAAY;;;;IAFHH,EAAA,CAAAO,SAAA,GAAgC;IAAhCP,EAAA,CAAAQ,UAAA,SAAAC,MAAA,CAAAC,IAAA,kBAAAD,MAAA,CAAAC,IAAA,CAAAC,MAAA,kBAAAF,MAAA,CAAAC,IAAA,CAAAC,MAAA,aAAgC;IAChCX,EAAA,CAAAO,SAAA,GAAiC;IAAjCP,EAAA,CAAAQ,UAAA,SAAAC,MAAA,CAAAC,IAAA,kBAAAD,MAAA,CAAAC,IAAA,CAAAC,MAAA,kBAAAF,MAAA,CAAAC,IAAA,CAAAC,MAAA,cAAiC;;;;;IAQxCX,EAAA,CAAAC,cAAA,WAA0C;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAClEH,EAAA,CAAAC,cAAA,WAAuC;IAAAD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAF1EH,EAAA,CAAAC,cAAA,gBAAoD;IAClDD,EAAA,CAAAI,UAAA,IAAAQ,8CAAA,mBAAkE;IAClEZ,EAAA,CAAAI,UAAA,IAAAS,8CAAA,mBAAwE;IAC1Eb,EAAA,CAAAG,YAAA,EAAY;;;;IAFHH,EAAA,CAAAO,SAAA,GAAiC;IAAjCP,EAAA,CAAAQ,UAAA,SAAAM,MAAA,CAAAC,KAAA,kBAAAD,MAAA,CAAAC,KAAA,CAAAJ,MAAA,kBAAAG,MAAA,CAAAC,KAAA,CAAAJ,MAAA,aAAiC;IACjCX,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAAQ,UAAA,SAAAM,MAAA,CAAAC,KAAA,kBAAAD,MAAA,CAAAC,KAAA,CAAAJ,MAAA,kBAAAG,MAAA,CAAAC,KAAA,CAAAJ,MAAA,UAA8B;;;;;IAQrCX,EAAA,CAAAC,cAAA,WAA4C;IAAAD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC7EH,EAAA,CAAAC,cAAA,WAA2C;IAAAD,EAAA,CAAAE,MAAA,0CAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFvFH,EAAA,CAAAC,cAAA,gBAAwD;IACtDD,EAAA,CAAAI,UAAA,IAAAY,8CAAA,mBAA6E;IAC7EhB,EAAA,CAAAI,UAAA,IAAAa,8CAAA,mBAAqF;IACvFjB,EAAA,CAAAG,YAAA,EAAY;;;;IAFHH,EAAA,CAAAO,SAAA,GAAmC;IAAnCP,EAAA,CAAAQ,UAAA,SAAAU,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAR,MAAA,kBAAAO,MAAA,CAAAC,OAAA,CAAAR,MAAA,aAAmC;IACnCX,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAQ,UAAA,SAAAU,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAR,MAAA,kBAAAO,MAAA,CAAAC,OAAA,CAAAR,MAAA,YAAkC;;;;;IAwB3CX,EAAA,CAAAoB,SAAA,sBAAyE;;;;;IAoBzEpB,EAAA,CAAAC,cAAA,gBAAwE;IACtED,EAAA,CAAAE,MAAA,qCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAOVH,EAAA,CAAAC,cAAA,WAAgD;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC/EH,EAAA,CAAAC,cAAA,WAAiD;IAAAD,EAAA,CAAAE,MAAA,6CAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFhGH,EAAA,CAAAC,cAAA,gBAAgE;IAC9DD,EAAA,CAAAI,UAAA,IAAAiB,8CAAA,mBAA+E;IAC/ErB,EAAA,CAAAI,UAAA,IAAAkB,8CAAA,mBAA8F;IAChGtB,EAAA,CAAAG,YAAA,EAAY;;;;IAFHH,EAAA,CAAAO,SAAA,GAAuC;IAAvCP,EAAA,CAAAQ,UAAA,SAAAe,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAb,MAAA,kBAAAY,MAAA,CAAAC,WAAA,CAAAb,MAAA,aAAuC;IACvCX,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAQ,UAAA,SAAAe,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAb,MAAA,kBAAAY,MAAA,CAAAC,WAAA,CAAAb,MAAA,cAAwC;;;;;IAQ/CX,EAAA,CAAAC,cAAA,WAAoD;IAAAD,EAAA,CAAAE,MAAA,mCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACvFH,EAAA,CAAAC,cAAA,WAA4D;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAF3FH,EAAA,CAAAC,cAAA,gBAAwE;IACtED,EAAA,CAAAI,UAAA,IAAAqB,8CAAA,mBAAuF;IACvFzB,EAAA,CAAAI,UAAA,IAAAsB,8CAAA,mBAAyF;IAC3F1B,EAAA,CAAAG,YAAA,EAAY;;;;IAFHH,EAAA,CAAAO,SAAA,GAA2C;IAA3CP,EAAA,CAAAQ,UAAA,SAAAmB,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAjB,MAAA,kBAAAgB,MAAA,CAAAC,eAAA,CAAAjB,MAAA,aAA2C;IAC3CX,EAAA,CAAAO,SAAA,GAAmD;IAAnDP,EAAA,CAAAQ,UAAA,SAAAmB,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAjB,MAAA,kBAAAgB,MAAA,CAAAC,eAAA,CAAAjB,MAAA,qBAAmD;;;;;IAS5DX,EAAA,CAAAoB,SAAA,sBAAyE;;;;;IAyGzEpB,EAAA,CAAAoB,SAAA,sBAAyE;;;ADxNzF,OAAM,MAAOS,iBAAiB;EA2B5BC,YACUC,EAAe,EACfC,WAAwB,EACxBC,QAAqB;IAFrB,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAzBlB,KAAAC,OAAO,GAAG,KAAK;IAGf;IACA,KAAAC,WAAW,GAAG,CAAC;IAEf;IACA,KAAAC,cAAc,GAAG;MACfC,aAAa,EAAE,kCAAkC;MACjDC,gBAAgB,EAAE,sBAAsB;MACxCC,YAAY,EAAE,uBAAuB;MACrCC,YAAY,EAAE,gBAAgB;MAC9BC,YAAY,EAAE,WAAW;MACzBC,eAAe,EAAE,WAAW;MAC5BC,wBAAwB,EAAE,IAAI;MAC9BC,wBAAwB,EAAE,KAAK;MAC/BC,mBAAmB,EAAE,IAAI;MACzBC,wBAAwB,EAAE,IAAI;MAC9BC,sBAAsB,EAAE,KAAK;MAC7BC,eAAe,EAAE;KAClB;IAOC,IAAI,CAACC,WAAW,GAAG,IAAI,CAACC,iBAAiB,EAAE;IAC3C,IAAI,CAACC,YAAY,GAAG,IAAI,CAACC,kBAAkB,EAAE;IAC7C,IAAI,CAACC,UAAU,GAAG,IAAI,CAACC,gBAAgB,EAAE;EAC3C;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEAP,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACnB,EAAE,CAAC2B,KAAK,CAAC;MACnBhD,IAAI,EAAE,CAAC,EAAE,EAAE,CAACX,UAAU,CAAC4D,QAAQ,EAAE5D,UAAU,CAAC6D,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1D7C,KAAK,EAAE,CAAC,EAAE,EAAE,CAAChB,UAAU,CAAC4D,QAAQ,EAAE5D,UAAU,CAACgB,KAAK,CAAC,CAAC;MACpDI,OAAO,EAAE,CAAC,EAAE,EAAE,CAACpB,UAAU,CAAC4D,QAAQ,EAAE5D,UAAU,CAAC8D,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC;MAC1EC,OAAO,EAAE,CAAC,EAAE,CAAC;MACbC,aAAa,EAAE,CAAC,EAAE,CAAC;MACnBC,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,WAAW,EAAE,CAAC,EAAE;KACjB,CAAC;EACJ;EAEAb,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACrB,EAAE,CAAC2B,KAAK,CAAC;MACnBQ,eAAe,EAAE,CAAC,EAAE,EAAE,CAACnE,UAAU,CAAC4D,QAAQ,CAAC,CAAC;MAC5CnC,WAAW,EAAE,CAAC,EAAE,EAAE,CAACzB,UAAU,CAAC4D,QAAQ,EAAE5D,UAAU,CAAC6D,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACjEhC,eAAe,EAAE,CAAC,EAAE,EAAE,CAAC7B,UAAU,CAAC4D,QAAQ,CAAC;KAC5C,EAAE;MAAEQ,UAAU,EAAE,IAAI,CAACC;IAAsB,CAAE,CAAC;EACjD;EAEAd,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACvB,EAAE,CAAC2B,KAAK,CAAC;MACnBrB,aAAa,EAAE,CAAC,IAAI,CAACD,cAAc,CAACC,aAAa,EAAEtC,UAAU,CAAC4D,QAAQ,CAAC;MACvErB,gBAAgB,EAAE,CAAC,IAAI,CAACF,cAAc,CAACE,gBAAgB,EAAEvC,UAAU,CAAC4D,QAAQ,CAAC;MAC7EpB,YAAY,EAAE,CAAC,IAAI,CAACH,cAAc,CAACG,YAAY,EAAE,CAACxC,UAAU,CAAC4D,QAAQ,EAAE5D,UAAU,CAACgB,KAAK,CAAC,CAAC;MACzFyB,YAAY,EAAE,CAAC,IAAI,CAACJ,cAAc,CAACI,YAAY,EAAEzC,UAAU,CAAC4D,QAAQ,CAAC;MACrElB,YAAY,EAAE,CAAC,IAAI,CAACL,cAAc,CAACK,YAAY,EAAE1C,UAAU,CAAC4D,QAAQ,CAAC;MACrEjB,eAAe,EAAE,CAAC,IAAI,CAACN,cAAc,CAACM,eAAe,EAAE3C,UAAU,CAAC4D,QAAQ,CAAC;MAC3EhB,wBAAwB,EAAE,CAAC,IAAI,CAACP,cAAc,CAACO,wBAAwB,CAAC;MACxEC,wBAAwB,EAAE,CAAC,IAAI,CAACR,cAAc,CAACQ,wBAAwB,CAAC;MACxEC,mBAAmB,EAAE,CAAC,IAAI,CAACT,cAAc,CAACS,mBAAmB,CAAC;MAC9DC,wBAAwB,EAAE,CAAC,IAAI,CAACV,cAAc,CAACU,wBAAwB,CAAC;MACxEC,sBAAsB,EAAE,CAAC,IAAI,CAACX,cAAc,CAACW,sBAAsB,CAAC;MACpEC,eAAe,EAAE,CAAC,IAAI,CAACZ,cAAc,CAACY,eAAe;KACtD,CAAC;EACJ;EAEAoB,sBAAsBA,CAACC,IAAe;IACpC,MAAM7C,WAAW,GAAG6C,IAAI,CAACC,GAAG,CAAC,aAAa,CAAC;IAC3C,MAAM1C,eAAe,GAAGyC,IAAI,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAEnD,IAAI9C,WAAW,IAAII,eAAe,IAAIJ,WAAW,CAAC+C,KAAK,KAAK3C,eAAe,CAAC2C,KAAK,EAAE;MACjF3C,eAAe,CAAC4C,SAAS,CAAC;QAAEC,gBAAgB,EAAE;MAAI,CAAE,CAAC;MACrD,OAAO;QAAEA,gBAAgB,EAAE;MAAI,CAAE;;IAGnC,OAAO,IAAI;EACb;EAEAjB,eAAeA,CAAA;IACb,MAAMkB,QAAQ,GAAG,IAAI,CAAC1C,WAAW,CAAC2C,uBAAuB,EAAE;IAC3D,IAAID,QAAQ,IAAIA,QAAQ,CAACE,IAAI,EAAE;MAC7B,IAAI,CAACC,WAAW,GAAGH,QAAQ,CAACE,IAAI;MAChC,IAAI,CAAC3B,WAAW,CAAC6B,UAAU,CAAC;QAC1BpE,IAAI,EAAE,IAAI,CAACmE,WAAW,CAACnE,IAAI;QAC3BK,KAAK,EAAE,IAAI,CAAC8D,WAAW,CAAC9D,KAAK;QAC7BI,OAAO,EAAE,IAAI,CAAC0D,WAAW,CAAC1D,OAAO;QACjC2C,OAAO,EAAE,IAAI,CAACe,WAAW,CAACf,OAAO;QACjCC,aAAa,EAAE,IAAI,CAACc,WAAW,CAACd,aAAa;QAC7CC,UAAU,EAAE,IAAI,CAACa,WAAW,CAACb,UAAU;QACvCC,WAAW,EAAE,IAAI,CAACY,WAAW,CAACZ;OAC/B,CAAC;;EAEN;EAEAR,kBAAkBA,CAAA;IAChB;IACA;IACA,IAAI,CAACJ,UAAU,CAACyB,UAAU,CAAC,IAAI,CAAC1C,cAAc,CAAC;EACjD;EAEA2C,WAAWA,CAACC,KAAU;IACpB,IAAI,CAAC7C,WAAW,GAAG6C,KAAK;EAC1B;EAEAC,aAAaA,CAAA;IACX,IAAI,IAAI,CAAChC,WAAW,CAACiC,OAAO,EAAE;MAC5B,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAAClC,WAAW,CAAC;MAC3C,IAAI,CAACmC,SAAS,CAAC,8CAA8C,CAAC;MAC9D;;IAGF,IAAI,CAAClD,OAAO,GAAG,IAAI;IACnB,MAAMmD,WAAW,GAAG,IAAI,CAACpC,WAAW,CAACsB,KAAK;IAE1C,IAAI,CAACvC,WAAW,CAACsD,iBAAiB,CAAC,IAAI,CAACT,WAAW,CAACU,GAAG,EAAEF,WAAW,CAAC,CAACG,SAAS,CAAC;MAC9EC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACC,WAAW,CAAC,+BAA+B,CAAC;UACjD;UACA,MAAMlB,QAAQ,GAAG,IAAI,CAAC1C,WAAW,CAAC2C,uBAAuB,EAAE;UAC3D,IAAID,QAAQ,EAAE;YACZA,QAAQ,CAACE,IAAI,GAAG;cAAE,GAAGF,QAAQ,CAACE,IAAI;cAAE,GAAGS;YAAW,CAAE;YACpDQ,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACtB,QAAQ,CAAC,CAAC;;;QAG1D,IAAI,CAACxC,OAAO,GAAG,KAAK;MACtB,CAAC;MACD+D,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACb,SAAS,CAAC,0BAA0B,CAAC;QAC1C,IAAI,CAAClD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAiE,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAChD,YAAY,CAAC+B,OAAO,EAAE;MAC7B,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAAChC,YAAY,CAAC;MAC5C,IAAI,CAACiC,SAAS,CAAC,8CAA8C,CAAC;MAC9D;;IAGF,IAAI,CAAClD,OAAO,GAAG,IAAI;IACnB,MAAMkE,YAAY,GAAG;MACnBlC,eAAe,EAAE,IAAI,CAACf,YAAY,CAACoB,KAAK,CAACL,eAAe;MACxD1C,WAAW,EAAE,IAAI,CAAC2B,YAAY,CAACoB,KAAK,CAAC/C;KACtC;IAED,IAAI,CAACQ,WAAW,CAACmE,cAAc,CAAC,IAAI,CAACtB,WAAW,CAACU,GAAG,EAAEa,YAAY,CAAC,CAACZ,SAAS,CAAC;MAC5EC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACC,WAAW,CAAC,gCAAgC,CAAC;UAClD,IAAI,CAACzC,YAAY,CAACkD,KAAK,EAAE;;QAE3B,IAAI,CAACnE,OAAO,GAAG,KAAK;MACtB,CAAC;MACD+D,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,IAAI,CAACb,SAAS,CAACa,KAAK,CAACA,KAAK,EAAEK,OAAO,IAAI,2BAA2B,CAAC;QACnE,IAAI,CAACpE,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAqE,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAAClD,UAAU,CAAC6B,OAAO,EAAE;MAC3B,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAAC9B,UAAU,CAAC;MAC1C,IAAI,CAAC+B,SAAS,CAAC,8CAA8C,CAAC;MAC9D;;IAGF,IAAI,CAAClD,OAAO,GAAG,IAAI;IACnB,MAAMsE,UAAU,GAAG,IAAI,CAACnD,UAAU,CAACkB,KAAK;IAExC;IACA;IACA,IAAI,CAACnC,cAAc,GAAG;MAAE,GAAG,IAAI,CAACA,cAAc;MAAE,GAAGoE;IAAU,CAAE;IAE/DC,UAAU,CAAC,MAAK;MACd,IAAI,CAACb,WAAW,CAAC,uCAAuC,CAAC;MACzD,IAAI,CAAC1D,OAAO,GAAG,KAAK;IACtB,CAAC,EAAE,IAAI,CAAC;EACV;EAEAwE,mBAAmBA,CAAA;IACjB,IAAI,CAACrD,UAAU,CAACyB,UAAU,CAAC,IAAI,CAAC1C,cAAc,CAAC;IAC/C,IAAI,CAACwD,WAAW,CAAC,gCAAgC,CAAC;EACpD;EAEAe,UAAUA,CAAA;IACR,IAAI,CAACf,WAAW,CAAC,8CAA8C,CAAC;EAClE;EAEAgB,UAAUA,CAAA;IACR,IAAI,CAAChB,WAAW,CAAC,8CAA8C,CAAC;EAClE;EAEAiB,cAAcA,CAAA;IACZ,IAAI,CAACjB,WAAW,CAAC,kDAAkD,CAAC;EACtE;EAEA;EACQT,oBAAoBA,CAAC2B,SAAoB;IAC/CC,MAAM,CAACC,IAAI,CAACF,SAAS,CAACG,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MAC5CL,SAAS,CAACxC,GAAG,CAAC6C,GAAG,CAAC,EAAEC,aAAa,EAAE;IACrC,CAAC,CAAC;EACJ;EAEQxB,WAAWA,CAACU,OAAe;IACjC,IAAI,CAACrE,QAAQ,CAACoF,IAAI,CAACf,OAAO,EAAE,OAAO,EAAE;MACnCgB,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,CAAC,kBAAkB;KAChC,CAAC;EACJ;EAEQnC,SAASA,CAACkB,OAAe;IAC/B,IAAI,CAACrE,QAAQ,CAACoF,IAAI,CAACf,OAAO,EAAE,OAAO,EAAE;MACnCgB,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,CAAC,gBAAgB;KAC9B,CAAC;EACJ;EAEA;EACA,IAAI7G,IAAIA,CAAA;IAAK,OAAO,IAAI,CAACuC,WAAW,CAACqB,GAAG,CAAC,MAAM,CAAC;EAAE;EAClD,IAAIvD,KAAKA,CAAA;IAAK,OAAO,IAAI,CAACkC,WAAW,CAACqB,GAAG,CAAC,OAAO,CAAC;EAAE;EACpD,IAAInD,OAAOA,CAAA;IAAK,OAAO,IAAI,CAAC8B,WAAW,CAACqB,GAAG,CAAC,SAAS,CAAC;EAAE;EACxD,IAAIJ,eAAeA,CAAA;IAAK,OAAO,IAAI,CAACf,YAAY,CAACmB,GAAG,CAAC,iBAAiB,CAAC;EAAE;EACzE,IAAI9C,WAAWA,CAAA;IAAK,OAAO,IAAI,CAAC2B,YAAY,CAACmB,GAAG,CAAC,aAAa,CAAC;EAAE;EACjE,IAAI1C,eAAeA,CAAA;IAAK,OAAO,IAAI,CAACuB,YAAY,CAACmB,GAAG,CAAC,iBAAiB,CAAC;EAAE;EAAC,QAAAkD,CAAA,G;qBAjP/D3F,iBAAiB,EAAA7B,EAAA,CAAAyH,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA3H,EAAA,CAAAyH,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA7H,EAAA,CAAAyH,iBAAA,CAAAK,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAjBnG,iBAAiB;IAAAoG,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCV9BvI,EAAA,CAAAC,cAAA,aAAgC;QAKdD,EAAA,CAAAE,MAAA,eAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC7BH,EAAA,CAAAE,MAAA,iBACF;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAC,cAAA,WAAoB;QAAAD,EAAA,CAAAE,MAAA,8CAAuC;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAKnEH,EAAA,CAAAC,cAAA,kBAAgC;QACfD,EAAA,CAAAyI,UAAA,iCAAAC,yEAAAC,MAAA;UAAA,OAAAH,GAAA,CAAArG,WAAA,GAAAwG,MAAA;QAAA,EAA+B,+BAAAC,uEAAAD,MAAA;UAAA,OAAsBH,GAAA,CAAAzD,WAAA,CAAA4D,MAAA,CAAmB;QAAA,EAAzC;QAG5C3I,EAAA,CAAAC,cAAA,kBAAyB;QAGfD,EAAA,CAAAE,MAAA,2BAAmB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC5BH,EAAA,CAAAC,cAAA,SAAG;QAAAD,EAAA,CAAAE,MAAA,4DAAoD;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAG7DH,EAAA,CAAAC,cAAA,eAA6D;QAA7BD,EAAA,CAAAyI,UAAA,sBAAAI,qDAAA;UAAA,OAAYL,GAAA,CAAAvD,aAAA,EAAe;QAAA,EAAC;QAC1DjF,EAAA,CAAAC,cAAA,eAAuB;QAERD,EAAA,CAAAE,MAAA,iBAAS;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAChCH,EAAA,CAAAoB,SAAA,iBAA0E;QAC1EpB,EAAA,CAAAI,UAAA,KAAA0I,uCAAA,wBAGY;QACd9I,EAAA,CAAAG,YAAA,EAAiB;QAEjBH,EAAA,CAAAC,cAAA,0BAAqC;QACxBD,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACpCH,EAAA,CAAAoB,SAAA,iBAAoF;QACpFpB,EAAA,CAAAI,UAAA,KAAA2I,uCAAA,wBAGY;QACd/I,EAAA,CAAAG,YAAA,EAAiB;QAEjBH,EAAA,CAAAC,cAAA,0BAAqC;QACxBD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACrCH,EAAA,CAAAoB,SAAA,iBAAkF;QAClFpB,EAAA,CAAAI,UAAA,KAAA4I,uCAAA,wBAGY;QACdhJ,EAAA,CAAAG,YAAA,EAAiB;QAEjBH,EAAA,CAAAC,cAAA,0BAAqC;QACxBD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC9BH,EAAA,CAAAoB,SAAA,oBAAkG;QACpGpB,EAAA,CAAAG,YAAA,EAAiB;QAEjBH,EAAA,CAAAC,cAAA,0BAAqC;QACxBD,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACpCH,EAAA,CAAAoB,SAAA,iBAAuF;QACzFpB,EAAA,CAAAG,YAAA,EAAiB;QAEjBH,EAAA,CAAAC,cAAA,0BAAqC;QACxBD,EAAA,CAAAE,MAAA,kBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACjCH,EAAA,CAAAoB,SAAA,iBAAiF;QACnFpB,EAAA,CAAAG,YAAA,EAAiB;QAGnBH,EAAA,CAAAC,cAAA,eAA0B;QAEZD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACzBH,EAAA,CAAAE,MAAA,wBACA;QAAAF,EAAA,CAAAI,UAAA,KAAA6I,yCAAA,0BAAyE;QAC3EjJ,EAAA,CAAAG,YAAA,EAAS;QAOjBH,EAAA,CAAAC,cAAA,mBAA0B;QAGhBD,EAAA,CAAAE,MAAA,uBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACxBH,EAAA,CAAAC,cAAA,SAAG;QAAAD,EAAA,CAAAE,MAAA,iDAAyC;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAGlDH,EAAA,CAAAC,cAAA,eAA+D;QAA9BD,EAAA,CAAAyI,UAAA,sBAAAS,qDAAA;UAAA,OAAYV,GAAA,CAAArC,cAAA,EAAgB;QAAA,EAAC;QAC5DnG,EAAA,CAAAC,cAAA,eAAqC;QAEtBD,EAAA,CAAAE,MAAA,wBAAgB;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACvCH,EAAA,CAAAoB,SAAA,iBAAuG;QACvGpB,EAAA,CAAAI,UAAA,KAAA+I,uCAAA,wBAEY;QACdnJ,EAAA,CAAAG,YAAA,EAAiB;QAEjBH,EAAA,CAAAC,cAAA,0BAAqC;QACxBD,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACnCH,EAAA,CAAAoB,SAAA,iBAA+F;QAC/FpB,EAAA,CAAAI,UAAA,KAAAgJ,uCAAA,wBAGY;QACdpJ,EAAA,CAAAG,YAAA,EAAiB;QAEjBH,EAAA,CAAAC,cAAA,0BAAqC;QACxBD,EAAA,CAAAE,MAAA,4BAAoB;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC3CH,EAAA,CAAAoB,SAAA,iBAAqG;QACrGpB,EAAA,CAAAI,UAAA,KAAAiJ,uCAAA,wBAGY;QACdrJ,EAAA,CAAAG,YAAA,EAAiB;QAGnBH,EAAA,CAAAC,cAAA,eAA0B;QAEZD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACzBH,EAAA,CAAAE,MAAA,yBACA;QAAAF,EAAA,CAAAI,UAAA,KAAAkJ,yCAAA,0BAAyE;QAC3EtJ,EAAA,CAAAG,YAAA,EAAS;QAOjBH,EAAA,CAAAC,cAAA,mBAAwB;QAGdD,EAAA,CAAAE,MAAA,4BAAoB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC7BH,EAAA,CAAAC,cAAA,SAAG;QAAAD,EAAA,CAAAE,MAAA,sDAA8C;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAGvDH,EAAA,CAAAC,cAAA,eAAmE;QAApCD,EAAA,CAAAyI,UAAA,sBAAAc,qDAAA;UAAA,OAAYf,GAAA,CAAAjC,oBAAA,EAAsB;QAAA,EAAC;QAEhEvG,EAAA,CAAAC,cAAA,eAA8B;QACxBD,EAAA,CAAAE,MAAA,6BAAqB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC9BH,EAAA,CAAAC,cAAA,eAAuB;QAERD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACrCH,EAAA,CAAAoB,SAAA,iBAAgD;QAClDpB,EAAA,CAAAG,YAAA,EAAiB;QAEjBH,EAAA,CAAAC,cAAA,0BAAqC;QACxBD,EAAA,CAAAE,MAAA,0BAAiB;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACxCH,EAAA,CAAAoB,SAAA,qBAA0E;QAC5EpB,EAAA,CAAAG,YAAA,EAAiB;QAEjBH,EAAA,CAAAC,cAAA,2BAAqC;QACxBD,EAAA,CAAAE,MAAA,sBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACpCH,EAAA,CAAAoB,SAAA,kBAA4D;QAC9DpB,EAAA,CAAAG,YAAA,EAAiB;QAEjBH,EAAA,CAAAC,cAAA,2BAAqC;QACxBD,EAAA,CAAAE,MAAA,sBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACpCH,EAAA,CAAAoB,SAAA,kBAA+C;QACjDpB,EAAA,CAAAG,YAAA,EAAiB;QAKrBH,EAAA,CAAAC,cAAA,gBAA8B;QACxBD,EAAA,CAAAE,MAAA,0BAAiB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC1BH,EAAA,CAAAC,cAAA,gBAAuB;QAERD,EAAA,CAAAE,MAAA,sBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACpCH,EAAA,CAAAoB,SAAA,kBAA+C;QACjDpB,EAAA,CAAAG,YAAA,EAAiB;QAEjBH,EAAA,CAAAC,cAAA,2BAAqC;QACxBD,EAAA,CAAAE,MAAA,yBAAgB;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACvCH,EAAA,CAAAoB,SAAA,kBAAkD;QACpDpB,EAAA,CAAAG,YAAA,EAAiB;QAKrBH,EAAA,CAAAC,cAAA,gBAA8B;QACxBD,EAAA,CAAAE,MAAA,8BAAqB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC9BH,EAAA,CAAAC,cAAA,gBAAyB;QAErBD,EAAA,CAAAE,MAAA,qCACF;QAAAF,EAAA,CAAAG,YAAA,EAAmB;QACnBH,EAAA,CAAAC,cAAA,6BAA6E;QAC3ED,EAAA,CAAAE,MAAA,qCACF;QAAAF,EAAA,CAAAG,YAAA,EAAmB;QAKvBH,EAAA,CAAAC,cAAA,gBAA8B;QACxBD,EAAA,CAAAE,MAAA,8BAAqB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC9BH,EAAA,CAAAC,cAAA,gBAAyB;QAErBD,EAAA,CAAAE,MAAA,+BACF;QAAAF,EAAA,CAAAG,YAAA,EAAmB;QACnBH,EAAA,CAAAC,cAAA,6BAA6E;QAC3ED,EAAA,CAAAE,MAAA,qCACF;QAAAF,EAAA,CAAAG,YAAA,EAAmB;QACnBH,EAAA,CAAAC,cAAA,6BAA2E;QACzED,EAAA,CAAAE,MAAA,mCACF;QAAAF,EAAA,CAAAG,YAAA,EAAmB;QAKvBH,EAAA,CAAAC,cAAA,gBAA8B;QACxBD,EAAA,CAAAE,MAAA,2BAAkB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC3BH,EAAA,CAAAC,cAAA,gBAAyB;QAErBD,EAAA,CAAAE,MAAA,2BACF;QAAAF,EAAA,CAAAG,YAAA,EAAmB;QAIvBH,EAAA,CAAAC,cAAA,gBAA0B;QACiBD,EAAA,CAAAyI,UAAA,mBAAAe,qDAAA;UAAA,OAAShB,GAAA,CAAA9B,mBAAA,EAAqB;QAAA,EAAC;QACtE1G,EAAA,CAAAC,cAAA,iBAAU;QAAAD,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC5BH,EAAA,CAAAE,MAAA,gBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,mBAAmG;QACvFD,EAAA,CAAAE,MAAA,aAAI;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACzBH,EAAA,CAAAE,MAAA,wBACA;QAAAF,EAAA,CAAAI,UAAA,MAAAqJ,0CAAA,0BAAyE;QAC3EzJ,EAAA,CAAAG,YAAA,EAAS;QAOjBH,EAAA,CAAAC,cAAA,oBAAsB;QAGZD,EAAA,CAAAE,MAAA,wBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACxBH,EAAA,CAAAC,cAAA,UAAG;QAAAD,EAAA,CAAAE,MAAA,6CAAoC;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAG7CH,EAAA,CAAAC,cAAA,gBAA0B;QAIYD,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACjDH,EAAA,CAAAC,cAAA,gBAAyB;QACnBD,EAAA,CAAAE,MAAA,oBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACpBH,EAAA,CAAAC,cAAA,UAAG;QAAAD,EAAA,CAAAE,MAAA,iDAAwC;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAEjDH,EAAA,CAAAC,cAAA,mBAAiE;QAAvBD,EAAA,CAAAyI,UAAA,mBAAAiB,qDAAA;UAAA,OAASlB,GAAA,CAAA7B,UAAA,EAAY;QAAA,EAAC;QAC9D3G,EAAA,CAAAC,cAAA,iBAAU;QAAAD,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC7BH,EAAA,CAAAE,MAAA,iBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAKfH,EAAA,CAAAC,cAAA,qBAA8B;QAGMD,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC/CH,EAAA,CAAAC,cAAA,gBAAyB;QACnBD,EAAA,CAAAE,MAAA,oBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACpBH,EAAA,CAAAC,cAAA,UAAG;QAAAD,EAAA,CAAAE,MAAA,sCAA6B;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAEtCH,EAAA,CAAAC,cAAA,mBAAgE;QAAvBD,EAAA,CAAAyI,UAAA,mBAAAkB,qDAAA;UAAA,OAASnB,GAAA,CAAA5B,UAAA,EAAY;QAAA,EAAC;QAC7D5G,EAAA,CAAAC,cAAA,iBAAU;QAAAD,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC3BH,EAAA,CAAAE,MAAA,iBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAKfH,EAAA,CAAAC,cAAA,qBAA8B;QAGMD,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC/CH,EAAA,CAAAC,cAAA,gBAAyB;QACnBD,EAAA,CAAAE,MAAA,wBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACxBH,EAAA,CAAAC,cAAA,UAAG;QAAAD,EAAA,CAAAE,MAAA,0CAAiC;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAE1CH,EAAA,CAAAC,cAAA,mBAAkE;QAA3BD,EAAA,CAAAyI,UAAA,mBAAAmB,qDAAA;UAAA,OAASpB,GAAA,CAAA3B,cAAA,EAAgB;QAAA,EAAC;QAC/D7G,EAAA,CAAAC,cAAA,iBAAU;QAAAD,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC3BH,EAAA,CAAAE,MAAA,iBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;;;QA/QRH,EAAA,CAAAO,SAAA,IAA+B;QAA/BP,EAAA,CAAAQ,UAAA,kBAAAgI,GAAA,CAAArG,WAAA,CAA+B;QAUlCnC,EAAA,CAAAO,SAAA,GAAyB;QAAzBP,EAAA,CAAAQ,UAAA,cAAAgI,GAAA,CAAAvF,WAAA,CAAyB;QAKbjD,EAAA,CAAAO,SAAA,GAAoC;QAApCP,EAAA,CAAAQ,UAAA,UAAAgI,GAAA,CAAA9H,IAAA,kBAAA8H,GAAA,CAAA9H,IAAA,CAAAwE,OAAA,MAAAsD,GAAA,CAAA9H,IAAA,kBAAA8H,GAAA,CAAA9H,IAAA,CAAAmJ,OAAA,EAAoC;QASpC7J,EAAA,CAAAO,SAAA,GAAsC;QAAtCP,EAAA,CAAAQ,UAAA,UAAAgI,GAAA,CAAAzH,KAAA,kBAAAyH,GAAA,CAAAzH,KAAA,CAAAmE,OAAA,MAAAsD,GAAA,CAAAzH,KAAA,kBAAAyH,GAAA,CAAAzH,KAAA,CAAA8I,OAAA,EAAsC;QAStC7J,EAAA,CAAAO,SAAA,GAA0C;QAA1CP,EAAA,CAAAQ,UAAA,UAAAgI,GAAA,CAAArH,OAAA,kBAAAqH,GAAA,CAAArH,OAAA,CAAA+D,OAAA,MAAAsD,GAAA,CAAArH,OAAA,kBAAAqH,GAAA,CAAArH,OAAA,CAAA0I,OAAA,EAA0C;QAuBA7J,EAAA,CAAAO,SAAA,IAA2C;QAA3CP,EAAA,CAAAQ,UAAA,aAAAgI,GAAA,CAAAtG,OAAA,IAAAsG,GAAA,CAAAvF,WAAA,CAAAiC,OAAA,CAA2C;QAGnFlF,EAAA,CAAAO,SAAA,GAAa;QAAbP,EAAA,CAAAQ,UAAA,SAAAgI,GAAA,CAAAtG,OAAA,CAAa;QAe3BlC,EAAA,CAAAO,SAAA,GAA0B;QAA1BP,EAAA,CAAAQ,UAAA,cAAAgI,GAAA,CAAArF,YAAA,CAA0B;QAKdnD,EAAA,CAAAO,SAAA,GAA0D;QAA1DP,EAAA,CAAAQ,UAAA,UAAAgI,GAAA,CAAAtE,eAAA,kBAAAsE,GAAA,CAAAtE,eAAA,CAAAgB,OAAA,MAAAsD,GAAA,CAAAtE,eAAA,kBAAAsE,GAAA,CAAAtE,eAAA,CAAA2F,OAAA,EAA0D;QAQ1D7J,EAAA,CAAAO,SAAA,GAAkD;QAAlDP,EAAA,CAAAQ,UAAA,UAAAgI,GAAA,CAAAhH,WAAA,kBAAAgH,GAAA,CAAAhH,WAAA,CAAA0D,OAAA,MAAAsD,GAAA,CAAAhH,WAAA,kBAAAgH,GAAA,CAAAhH,WAAA,CAAAqI,OAAA,EAAkD;QASlD7J,EAAA,CAAAO,SAAA,GAA0D;QAA1DP,EAAA,CAAAQ,UAAA,UAAAgI,GAAA,CAAA5G,eAAA,kBAAA4G,GAAA,CAAA5G,eAAA,CAAAsD,OAAA,MAAAsD,GAAA,CAAA5G,eAAA,kBAAA4G,GAAA,CAAA5G,eAAA,CAAAiI,OAAA,EAA0D;QAQhB7J,EAAA,CAAAO,SAAA,GAA4C;QAA5CP,EAAA,CAAAQ,UAAA,aAAAgI,GAAA,CAAAtG,OAAA,IAAAsG,GAAA,CAAArF,YAAA,CAAA+B,OAAA,CAA4C;QAGpFlF,EAAA,CAAAO,SAAA,GAAa;QAAbP,EAAA,CAAAQ,UAAA,SAAAgI,GAAA,CAAAtG,OAAA,CAAa;QAe3BlC,EAAA,CAAAO,SAAA,GAAwB;QAAxBP,EAAA,CAAAQ,UAAA,cAAAgI,GAAA,CAAAnF,UAAA,CAAwB;QAuF8BrD,EAAA,CAAAO,SAAA,IAA0C;QAA1CP,EAAA,CAAAQ,UAAA,aAAAgI,GAAA,CAAAtG,OAAA,IAAAsG,GAAA,CAAAnF,UAAA,CAAA6B,OAAA,CAA0C;QAGlFlF,EAAA,CAAAO,SAAA,GAAa;QAAbP,EAAA,CAAAQ,UAAA,SAAAgI,GAAA,CAAAtG,OAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}