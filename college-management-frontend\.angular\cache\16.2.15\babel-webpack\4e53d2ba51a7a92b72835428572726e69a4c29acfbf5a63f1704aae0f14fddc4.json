{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AttendanceComponent } from './attendance/attendance.component';\nimport { AddStudentComponent } from './add-student/add-student.component';\nimport { AdminStudentListComponent } from './admin-student-list/admin-student-list.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: AttendanceComponent\n}, {\n  path: 'add-student',\n  component: AddStudentComponent\n}, {\n  path: 'student-list',\n  component: AdminStudentListComponent\n}, {\n  path: 'attendenace',\n  component: AttendanceComponent\n}];\nexport let StudentsRoutingModule = /*#__PURE__*/(() => {\n  class StudentsRoutingModule {\n    static #_ = this.ɵfac = function StudentsRoutingModule_Factory(t) {\n      return new (t || StudentsRoutingModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: StudentsRoutingModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n  return StudentsRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}