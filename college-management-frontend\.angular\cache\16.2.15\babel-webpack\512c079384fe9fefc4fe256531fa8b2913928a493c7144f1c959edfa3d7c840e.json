{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/timetable.service\";\nimport * as i2 from \"src/app/services/user.service\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/core\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/form-field\";\nimport * as i10 from \"@angular/material/progress-spinner\";\nimport * as i11 from \"@angular/material/select\";\nfunction TeacherTimetableComponent_mat_option_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const year_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", year_r7);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", year_r7, \" \");\n  }\n}\nfunction TeacherTimetableComponent_mat_option_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", day_r8);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", day_r8, \" \");\n  }\n}\nfunction TeacherTimetableComponent_div_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading your timetable...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TeacherTimetableComponent_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Error Loading Timetable\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function TeacherTimetableComponent_div_79_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.refreshTimetable());\n    });\n    i0.ɵɵtext(8, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r3.error);\n  }\n}\nfunction TeacherTimetableComponent_div_80_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r13 = ctx.$implicit;\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"current-day\", day_r13.toLowerCase() === ctx_r11.getCurrentDay());\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", day_r13, \" \");\n  }\n}\nfunction TeacherTimetableComponent_div_80_div_13_div_3_div_1_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r16 = i0.ɵɵnextContext(2).$implicit;\n    const slot_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"Room: \", slot_r14[day_r16.toLowerCase()] == null ? null : slot_r14[day_r16.toLowerCase()].room, \"\");\n  }\n}\nfunction TeacherTimetableComponent_div_80_div_13_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"div\", 48);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 49)(4, \"span\", 50);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 51);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 52)(9, \"span\", 53);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, TeacherTimetableComponent_div_80_div_13_div_3_div_1_span_11_Template, 2, 1, \"span\", 54);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const day_r16 = i0.ɵɵnextContext().$implicit;\n    const slot_r14 = i0.ɵɵnextContext().$implicit;\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(slot_r14[day_r16.toLowerCase()] == null ? null : slot_r14[day_r16.toLowerCase()].subject.subjectName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r17.getClassDisplayName(slot_r14[day_r16.toLowerCase()] == null ? null : slot_r14[day_r16.toLowerCase()].class));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(slot_r14[day_r16.toLowerCase()] == null ? null : slot_r14[day_r16.toLowerCase()].program.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r17.getSemesterDisplayText(slot_r14[day_r16.toLowerCase()] == null ? null : slot_r14[day_r16.toLowerCase()].semester, slot_r14[day_r16.toLowerCase()] == null ? null : slot_r14[day_r16.toLowerCase()].program.name));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", slot_r14[day_r16.toLowerCase()] == null ? null : slot_r14[day_r16.toLowerCase()].room);\n  }\n}\nfunction TeacherTimetableComponent_div_80_div_13_div_3_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"span\", 57);\n    i0.ɵɵtext(2, \"Free\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TeacherTimetableComponent_div_80_div_13_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtemplate(1, TeacherTimetableComponent_div_80_div_13_div_3_div_1_Template, 12, 5, \"div\", 45);\n    i0.ɵɵtemplate(2, TeacherTimetableComponent_div_80_div_13_div_3_ng_template_2_Template, 3, 0, \"ng-template\", null, 46, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r16 = ctx.$implicit;\n    const _r18 = i0.ɵɵreference(3);\n    const slot_r14 = i0.ɵɵnextContext().$implicit;\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"current-day\", day_r16.toLowerCase() === ctx_r15.getCurrentDay())(\"has-class\", slot_r14[day_r16.toLowerCase()]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", slot_r14[day_r16.toLowerCase()])(\"ngIfElse\", _r18);\n  }\n}\nfunction TeacherTimetableComponent_div_80_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, TeacherTimetableComponent_div_80_div_13_div_3_Template, 4, 6, \"div\", 43);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const slot_r14 = ctx.$implicit;\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"current-time\", ctx_r12.isCurrentTimeSlot(slot_r14));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(slot_r14.time);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r12.weekDays);\n  }\n}\nfunction TeacherTimetableComponent_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"mat-card\", 34)(2, \"mat-card-header\")(3, \"mat-card-title\");\n    i0.ɵɵtext(4, \"Weekly Schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-card-subtitle\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"div\", 35)(9, \"div\", 36)(10, \"div\", 37);\n    i0.ɵɵtext(11, \"Time\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, TeacherTimetableComponent_div_80_div_12_Template, 2, 3, \"div\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, TeacherTimetableComponent_div_80_div_13_Template, 4, 4, \"div\", 39);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"Your teaching schedule for \", ctx_r4.selectedAcademicYear, \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.weekDays);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.timetableGrid);\n  }\n}\nfunction TeacherTimetableComponent_div_81_div_10_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const entry_r28 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(entry_r28.room);\n  }\n}\nfunction TeacherTimetableComponent_div_81_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"div\", 64)(2, \"span\", 65);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 49)(5, \"h4\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"small\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, TeacherTimetableComponent_div_81_div_10_div_11_Template, 5, 1, \"div\", 66);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const entry_r28 = ctx.$implicit;\n    const ctx_r26 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", entry_r28.timeSlot.startTime, \" - \", entry_r28.timeSlot.endTime, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(entry_r28.subject.subjectName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r26.getClassDisplayName(entry_r28.class), \" - \", entry_r28.program.name, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r26.getSemesterDisplayText(entry_r28.semester, entry_r28.program.name));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", entry_r28.room);\n  }\n}\nfunction TeacherTimetableComponent_div_81_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"free_breakfast\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No classes scheduled for today!\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TeacherTimetableComponent_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"mat-card\", 59)(2, \"mat-card-header\")(3, \"mat-card-title\");\n    i0.ɵɵtext(4, \"Today's Classes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-card-subtitle\");\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"mat-card-content\")(9, \"div\", 60);\n    i0.ɵɵtemplate(10, TeacherTimetableComponent_div_81_div_10_Template, 12, 7, \"div\", 61);\n    i0.ɵɵtemplate(11, TeacherTimetableComponent_div_81_div_11_Template, 5, 0, \"div\", 62);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(7, 3, ctx_r5.getCurrentDate(), \"fullDate\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.getTodayClasses());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.getTodayClasses().length === 0);\n  }\n}\nfunction TeacherTimetableComponent_div_82_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"mat-card\", 70)(2, \"mat-card-content\")(3, \"div\", 71)(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h3\");\n    i0.ɵɵtext(7, \"No Timetable Found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9, \"You don't have any classes scheduled for the selected academic year.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\");\n    i0.ɵɵtext(11, \"Please contact the administration if you believe this is an error.\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nexport class TeacherTimetableComponent {\n  constructor(timetableService, userService, snackBar) {\n    this.timetableService = timetableService;\n    this.userService = userService;\n    this.snackBar = snackBar;\n    this.timetableEntries = [];\n    this.timetableGrid = [];\n    this.loading = false;\n    this.error = null;\n    this.weekDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];\n    this.timeSlots = [{\n      start: '08:00',\n      end: '09:00'\n    }, {\n      start: '09:00',\n      end: '10:00'\n    }, {\n      start: '10:00',\n      end: '11:00'\n    }, {\n      start: '11:00',\n      end: '12:00'\n    }, {\n      start: '12:00',\n      end: '13:00'\n    }, {\n      start: '13:00',\n      end: '14:00'\n    }, {\n      start: '14:00',\n      end: '15:00'\n    }, {\n      start: '15:00',\n      end: '16:00'\n    }, {\n      start: '16:00',\n      end: '17:00'\n    }, {\n      start: '17:00',\n      end: '18:00'\n    }];\n    // Filter options\n    this.selectedAcademicYear = '2024-2025';\n    this.selectedDay = '';\n    this.availableAcademicYears = ['2024-2025', '2023-2024', '2022-2023'];\n    // Statistics\n    this.totalClasses = 0;\n    this.totalSubjects = 0;\n    this.totalHours = 0;\n  }\n  ngOnInit() {\n    this.currentUser = this.userService.getUserFromLocalStorage()?.user;\n    if (this.currentUser) {\n      this.loadTeacherTimetable();\n    } else {\n      this.error = 'User not found. Please login again.';\n    }\n  }\n  loadTeacherTimetable() {\n    this.loading = true;\n    this.error = null;\n    this.timetableService.getTimetableByTeacher(this.currentUser._id, this.selectedAcademicYear, this.selectedDay).subscribe({\n      next: response => {\n        this.loading = false;\n        if (response.success) {\n          this.timetableEntries = response.timetable;\n          this.generateTimetableGrid();\n          this.calculateStatistics();\n        } else {\n          this.error = 'Failed to load timetable';\n        }\n      },\n      error: error => {\n        this.loading = false;\n        console.error('Error loading teacher timetable:', error);\n        this.error = 'Error loading timetable data';\n        this.snackBar.open('Failed to load timetable', 'Close', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  generateTimetableGrid() {\n    this.timetableGrid = this.timeSlots.map(slot => {\n      const timeSlot = {\n        time: `${slot.start} - ${slot.end}`,\n        startTime: slot.start,\n        endTime: slot.end\n      };\n      // Initialize all days as null\n      this.weekDays.forEach(day => {\n        timeSlot[day.toLowerCase()] = null;\n      });\n      // Fill in the actual timetable entries\n      this.timetableEntries.forEach(entry => {\n        const entryStartTime = entry.timeSlot.startTime;\n        const dayKey = entry.dayOfWeek.toLowerCase();\n        if (entryStartTime === slot.start && this.weekDays.map(d => d.toLowerCase()).includes(dayKey)) {\n          timeSlot[dayKey] = entry;\n        }\n      });\n      return timeSlot;\n    });\n  }\n  calculateStatistics() {\n    this.totalClasses = this.timetableEntries.length;\n    // Count unique subjects\n    const uniqueSubjects = new Set(this.timetableEntries.map(entry => entry.subject._id));\n    this.totalSubjects = uniqueSubjects.size;\n    // Calculate total hours per week\n    this.totalHours = this.timetableEntries.reduce((total, entry) => {\n      return total + entry.timeSlot.duration / 60; // Convert minutes to hours\n    }, 0);\n  }\n  onFilterChange() {\n    this.loadTeacherTimetable();\n  }\n  getCurrentDay() {\n    const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];\n    return days[new Date().getDay()];\n  }\n  getCurrentDate() {\n    return new Date();\n  }\n  isCurrentTimeSlot(timeSlot) {\n    const now = new Date();\n    const currentTime = now.getHours().toString().padStart(2, '0') + ':' + now.getMinutes().toString().padStart(2, '0');\n    return currentTime >= timeSlot.startTime && currentTime < timeSlot.endTime;\n  }\n  getClassDisplayName(classData) {\n    if (!classData) return '';\n    return `${classData.className}${classData.section ? ' - ' + classData.section : ''}`;\n  }\n  getSemesterDisplayText(semester, programName) {\n    if (programName === 'Intermediate') {\n      return semester === 1 ? '1st Year' : '2nd Year';\n    } else {\n      const semesterMap = {\n        1: '1st',\n        2: '2nd',\n        3: '3rd',\n        4: '4th',\n        5: '5th',\n        6: '6th',\n        7: '7th',\n        8: '8th'\n      };\n      return `${semesterMap[semester] || semester} Semester`;\n    }\n  }\n  refreshTimetable() {\n    this.loadTeacherTimetable();\n  }\n  exportTimetable() {\n    // Implementation for exporting timetable (PDF, Excel, etc.)\n    this.snackBar.open('Export functionality coming soon!', 'Close', {\n      duration: 3000\n    });\n  }\n  getTodayClasses() {\n    const today = this.getCurrentDay();\n    return this.timetableEntries.filter(entry => entry.dayOfWeek.toLowerCase() === today).sort((a, b) => a.timeSlot.startTime.localeCompare(b.timeSlot.startTime));\n  }\n  static #_ = this.ɵfac = function TeacherTimetableComponent_Factory(t) {\n    return new (t || TeacherTimetableComponent)(i0.ɵɵdirectiveInject(i1.TimetableService), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: TeacherTimetableComponent,\n    selectors: [[\"app-teacher-timetable\"]],\n    decls: 83,\n    vars: 19,\n    consts: [[1, \"teacher-timetable-container\"], [1, \"page-header\"], [1, \"header-content\"], [1, \"page-title\"], [1, \"title-icon\"], [1, \"page-subtitle\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", 3, \"click\"], [1, \"filters-card\"], [1, \"filters-row\"], [\"appearance\", \"outline\"], [3, \"value\", \"valueChange\", \"selectionChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"value\", \"\"], [1, \"stats-section\"], [1, \"stats-row\"], [1, \"stat-card\"], [1, \"stat-content\"], [1, \"stat-icon\"], [1, \"stat-info\"], [1, \"current-day-info\"], [1, \"current-day-card\"], [1, \"current-day-content\"], [1, \"day-info\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"timetable-wrapper\", 4, \"ngIf\"], [\"class\", \"today-summary\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [3, \"value\"], [1, \"loading-container\"], [1, \"error-container\"], [1, \"timetable-wrapper\"], [1, \"timetable-card\"], [1, \"timetable-grid\"], [1, \"timetable-header\"], [1, \"time-header\"], [\"class\", \"day-header\", 3, \"current-day\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"timetable-row\", 3, \"current-time\", 4, \"ngFor\", \"ngForOf\"], [1, \"day-header\"], [1, \"timetable-row\"], [1, \"time-slot\"], [\"class\", \"schedule-cell\", 3, \"current-day\", \"has-class\", 4, \"ngFor\", \"ngForOf\"], [1, \"schedule-cell\"], [\"class\", \"class-info\", 4, \"ngIf\", \"ngIfElse\"], [\"emptySlot\", \"\"], [1, \"class-info\"], [1, \"subject-name\"], [1, \"class-details\"], [1, \"class-name\"], [1, \"program-info\"], [1, \"additional-info\"], [1, \"semester\"], [\"class\", \"room\", 4, \"ngIf\"], [1, \"room\"], [1, \"empty-slot\"], [1, \"free-time\"], [1, \"today-summary\"], [1, \"summary-card\"], [1, \"today-classes\"], [\"class\", \"today-class-item\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"no-classes\", 4, \"ngIf\"], [1, \"today-class-item\"], [1, \"time-info\"], [1, \"time\"], [\"class\", \"room-info\", 4, \"ngIf\"], [1, \"room-info\"], [1, \"no-classes\"], [1, \"empty-state\"], [1, \"empty-card\"], [1, \"empty-content\"]],\n    template: function TeacherTimetableComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3)(4, \"mat-icon\", 4);\n        i0.ɵɵtext(5, \"schedule\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(6, \" My Timetable \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"p\", 5);\n        i0.ɵɵtext(8, \"Your weekly teaching schedule\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(9, \"div\", 6)(10, \"button\", 7);\n        i0.ɵɵlistener(\"click\", function TeacherTimetableComponent_Template_button_click_10_listener() {\n          return ctx.refreshTimetable();\n        });\n        i0.ɵɵelementStart(11, \"mat-icon\");\n        i0.ɵɵtext(12, \"refresh\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(13, \" Refresh \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(14, \"button\", 8);\n        i0.ɵɵlistener(\"click\", function TeacherTimetableComponent_Template_button_click_14_listener() {\n          return ctx.exportTimetable();\n        });\n        i0.ɵɵelementStart(15, \"mat-icon\");\n        i0.ɵɵtext(16, \"download\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(17, \" Export \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(18, \"mat-card\", 9)(19, \"mat-card-content\")(20, \"div\", 10)(21, \"mat-form-field\", 11)(22, \"mat-label\");\n        i0.ɵɵtext(23, \"Academic Year\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(24, \"mat-select\", 12);\n        i0.ɵɵlistener(\"valueChange\", function TeacherTimetableComponent_Template_mat_select_valueChange_24_listener($event) {\n          return ctx.selectedAcademicYear = $event;\n        })(\"selectionChange\", function TeacherTimetableComponent_Template_mat_select_selectionChange_24_listener() {\n          return ctx.onFilterChange();\n        });\n        i0.ɵɵtemplate(25, TeacherTimetableComponent_mat_option_25_Template, 2, 2, \"mat-option\", 13);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(26, \"mat-form-field\", 11)(27, \"mat-label\");\n        i0.ɵɵtext(28, \"Filter by Day\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(29, \"mat-select\", 12);\n        i0.ɵɵlistener(\"valueChange\", function TeacherTimetableComponent_Template_mat_select_valueChange_29_listener($event) {\n          return ctx.selectedDay = $event;\n        })(\"selectionChange\", function TeacherTimetableComponent_Template_mat_select_selectionChange_29_listener() {\n          return ctx.onFilterChange();\n        });\n        i0.ɵɵelementStart(30, \"mat-option\", 14);\n        i0.ɵɵtext(31, \"All Days\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(32, TeacherTimetableComponent_mat_option_32_Template, 2, 2, \"mat-option\", 13);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(33, \"div\", 15)(34, \"div\", 16)(35, \"mat-card\", 17)(36, \"mat-card-content\")(37, \"div\", 18)(38, \"mat-icon\", 19);\n        i0.ɵɵtext(39, \"class\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(40, \"div\", 20)(41, \"h3\");\n        i0.ɵɵtext(42);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(43, \"p\");\n        i0.ɵɵtext(44, \"Total Classes\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(45, \"mat-card\", 17)(46, \"mat-card-content\")(47, \"div\", 18)(48, \"mat-icon\", 19);\n        i0.ɵɵtext(49, \"subject\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(50, \"div\", 20)(51, \"h3\");\n        i0.ɵɵtext(52);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(53, \"p\");\n        i0.ɵɵtext(54, \"Subjects\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(55, \"mat-card\", 17)(56, \"mat-card-content\")(57, \"div\", 18)(58, \"mat-icon\", 19);\n        i0.ɵɵtext(59, \"access_time\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(60, \"div\", 20)(61, \"h3\");\n        i0.ɵɵtext(62);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(63, \"p\");\n        i0.ɵɵtext(64, \"Hours/Week\");\n        i0.ɵɵelementEnd()()()()()()();\n        i0.ɵɵelementStart(65, \"div\", 21)(66, \"mat-card\", 22)(67, \"mat-card-content\")(68, \"div\", 23)(69, \"mat-icon\");\n        i0.ɵɵtext(70, \"today\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(71, \"div\", 24)(72, \"h3\");\n        i0.ɵɵtext(73);\n        i0.ɵɵpipe(74, \"titlecase\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(75, \"p\");\n        i0.ɵɵtext(76);\n        i0.ɵɵpipe(77, \"date\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵtemplate(78, TeacherTimetableComponent_div_78_Template, 4, 0, \"div\", 25);\n        i0.ɵɵtemplate(79, TeacherTimetableComponent_div_79_Template, 9, 1, \"div\", 26);\n        i0.ɵɵtemplate(80, TeacherTimetableComponent_div_80_Template, 14, 3, \"div\", 27);\n        i0.ɵɵtemplate(81, TeacherTimetableComponent_div_81_Template, 12, 6, \"div\", 28);\n        i0.ɵɵtemplate(82, TeacherTimetableComponent_div_82_Template, 12, 0, \"div\", 29);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(24);\n        i0.ɵɵproperty(\"value\", ctx.selectedAcademicYear);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngForOf\", ctx.availableAcademicYears);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"value\", ctx.selectedDay);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.weekDays);\n        i0.ɵɵadvance(10);\n        i0.ɵɵtextInterpolate(ctx.totalClasses);\n        i0.ɵɵadvance(10);\n        i0.ɵɵtextInterpolate(ctx.totalSubjects);\n        i0.ɵɵadvance(10);\n        i0.ɵɵtextInterpolate(ctx.totalHours);\n        i0.ɵɵadvance(11);\n        i0.ɵɵtextInterpolate1(\"Today is \", i0.ɵɵpipeBind1(74, 14, ctx.getCurrentDay()), \"\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(77, 16, ctx.getCurrentDate(), \"fullDate\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error && ctx.timetableEntries.length > 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error && ctx.timetableEntries.length === 0);\n      }\n    },\n    dependencies: [i4.NgForOf, i4.NgIf, i5.MatOption, i6.MatButton, i7.MatCard, i7.MatCardContent, i7.MatCardHeader, i7.MatCardSubtitle, i7.MatCardTitle, i8.MatIcon, i9.MatFormField, i9.MatLabel, i10.MatProgressSpinner, i11.MatSelect, i4.TitleCasePipe, i4.DatePipe],\n    styles: [\".teacher-timetable-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  max-width: 1400px;\\n  margin: 0 auto;\\n}\\n\\n.page-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 30px;\\n  padding-bottom: 20px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.header-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  color: #333;\\n}\\n\\n.title-icon[_ngcontent-%COMP%] {\\n  color: #2196f3;\\n}\\n\\n.page-subtitle[_ngcontent-%COMP%] {\\n  margin: 5px 0 0 0;\\n  color: #666;\\n  font-size: 14px;\\n}\\n\\n.header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n}\\n\\n.filters-card[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.filters-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n  align-items: center;\\n}\\n\\n.filters-row[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n  min-width: 200px;\\n}\\n\\n.stats-section[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.stats-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 20px;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n}\\n\\n.stat-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n}\\n\\n.stat-icon[_ngcontent-%COMP%] {\\n  font-size: 36px;\\n  width: 36px;\\n  height: 36px;\\n  opacity: 0.8;\\n}\\n\\n.stat-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 28px;\\n  font-weight: bold;\\n}\\n\\n.stat-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n  opacity: 0.9;\\n}\\n\\n.current-day-info[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.current-day-card[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\\n  color: white;\\n}\\n\\n.current-day-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n}\\n\\n.current-day-content[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  width: 32px;\\n  height: 32px;\\n}\\n\\n.day-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 20px;\\n}\\n\\n.day-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  opacity: 0.9;\\n}\\n\\n.loading-container[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 60px;\\n  text-align: center;\\n}\\n\\n.error-container[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  width: 64px;\\n  height: 64px;\\n  color: #f44336;\\n  margin-bottom: 20px;\\n}\\n\\n.timetable-wrapper[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.timetable-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 120px repeat(6, 1fr);\\n  gap: 1px;\\n  background-color: #e0e0e0;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n\\n.timetable-header[_ngcontent-%COMP%] {\\n  display: contents;\\n}\\n\\n.time-header[_ngcontent-%COMP%], .day-header[_ngcontent-%COMP%] {\\n  background-color: #3f51b5;\\n  color: white;\\n  padding: 15px 10px;\\n  font-weight: bold;\\n  text-align: center;\\n  font-size: 14px;\\n}\\n\\n.current-day[_ngcontent-%COMP%] {\\n  background-color: #ff9800 !important;\\n}\\n\\n.timetable-row[_ngcontent-%COMP%] {\\n  display: contents;\\n}\\n\\n.current-time[_ngcontent-%COMP%]   .time-slot[_ngcontent-%COMP%], .current-time[_ngcontent-%COMP%]   .schedule-cell[_ngcontent-%COMP%] {\\n  background-color: #fff3e0 !important;\\n  border-left: 4px solid #ff9800;\\n}\\n\\n.time-slot[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  padding: 15px 10px;\\n  font-weight: 500;\\n  text-align: center;\\n  font-size: 12px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.schedule-cell[_ngcontent-%COMP%] {\\n  background-color: white;\\n  padding: 8px;\\n  min-height: 80px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n}\\n\\n.schedule-cell.has-class[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  border-left: 4px solid #4caf50;\\n}\\n\\n.class-info[_ngcontent-%COMP%] {\\n  width: 100%;\\n  text-align: center;\\n}\\n\\n.subject-name[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  color: #2e7d32;\\n  font-size: 13px;\\n  margin-bottom: 4px;\\n}\\n\\n.class-details[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #666;\\n  margin-bottom: 4px;\\n}\\n\\n.class-name[_ngcontent-%COMP%] {\\n  display: block;\\n  font-weight: 500;\\n}\\n\\n.program-info[_ngcontent-%COMP%] {\\n  display: block;\\n  font-style: italic;\\n}\\n\\n.additional-info[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  color: #888;\\n}\\n\\n.semester[_ngcontent-%COMP%], .room[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n\\n.empty-slot[_ngcontent-%COMP%] {\\n  width: 100%;\\n  text-align: center;\\n  color: #ccc;\\n  font-style: italic;\\n  font-size: 12px;\\n}\\n\\n.today-summary[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.today-classes[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 15px;\\n}\\n\\n.today-class-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n  padding: 15px;\\n  background-color: #f8f9fa;\\n  border-radius: 8px;\\n  border-left: 4px solid #2196f3;\\n}\\n\\n.time-info[_ngcontent-%COMP%] {\\n  min-width: 120px;\\n}\\n\\n.time[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  color: #2196f3;\\n}\\n\\n.class-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 5px 0;\\n  color: #333;\\n}\\n\\n.class-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 3px 0;\\n  color: #666;\\n}\\n\\n.class-details[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  color: #888;\\n}\\n\\n.room-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 5px;\\n  color: #666;\\n  font-size: 12px;\\n}\\n\\n.no-classes[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px;\\n  color: #666;\\n}\\n\\n.no-classes[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  width: 48px;\\n  height: 48px;\\n  margin-bottom: 15px;\\n  color: #4caf50;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  margin-top: 40px;\\n}\\n\\n.empty-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px;\\n}\\n\\n.empty-content[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  width: 64px;\\n  height: 64px;\\n  color: #ccc;\\n  margin-bottom: 20px;\\n}\\n\\n.empty-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 15px 0;\\n  color: #333;\\n}\\n\\n.empty-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 10px 0;\\n  color: #666;\\n}\\n\\n@media (max-width: 768px) {\\n  .page-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 15px;\\n  }\\n  \\n  .filters-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n  }\\n  \\n  .stats-row[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  \\n  .timetable-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 80px repeat(6, 1fr);\\n    font-size: 10px;\\n  }\\n  \\n  .today-class-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "year_r7", "ɵɵadvance", "ɵɵtextInterpolate1", "day_r8", "ɵɵelement", "ɵɵlistener", "TeacherTimetableComponent_div_79_Template_button_click_7_listener", "ɵɵrestoreView", "_r10", "ctx_r9", "ɵɵnextContext", "ɵɵresetView", "refreshTimetable", "ɵɵtextInterpolate", "ctx_r3", "error", "ɵɵclassProp", "day_r13", "toLowerCase", "ctx_r11", "getCurrentDay", "slot_r14", "day_r16", "room", "ɵɵtemplate", "TeacherTimetableComponent_div_80_div_13_div_3_div_1_span_11_Template", "subject", "subjectName", "ctx_r17", "getClassDisplayName", "class", "program", "name", "getSemesterDisplayText", "semester", "TeacherTimetableComponent_div_80_div_13_div_3_div_1_Template", "TeacherTimetableComponent_div_80_div_13_div_3_ng_template_2_Template", "ɵɵtemplateRefExtractor", "ctx_r15", "_r18", "TeacherTimetableComponent_div_80_div_13_div_3_Template", "ctx_r12", "isCurrentTimeSlot", "time", "weekDays", "TeacherTimetableComponent_div_80_div_12_Template", "TeacherTimetableComponent_div_80_div_13_Template", "ctx_r4", "selectedAcademicYear", "timetableGrid", "entry_r28", "TeacherTimetableComponent_div_81_div_10_div_11_Template", "ɵɵtextInterpolate2", "timeSlot", "startTime", "endTime", "ctx_r26", "TeacherTimetableComponent_div_81_div_10_Template", "TeacherTimetableComponent_div_81_div_11_Template", "ɵɵpipeBind2", "ctx_r5", "getCurrentDate", "getTodayClasses", "length", "TeacherTimetableComponent", "constructor", "timetableService", "userService", "snackBar", "timetableEntries", "loading", "timeSlots", "start", "end", "selected<PERSON>ay", "availableAcademicYears", "totalClasses", "totalSubjects", "totalHours", "ngOnInit", "currentUser", "getUserFromLocalStorage", "user", "loadTeacherTimetable", "getTimetableByTeacher", "_id", "subscribe", "next", "response", "success", "timetable", "generateTimetableGrid", "calculateStatistics", "console", "open", "duration", "map", "slot", "for<PERSON>ach", "day", "entry", "entryStartTime", "<PERSON><PERSON><PERSON>", "dayOfWeek", "d", "includes", "uniqueSubjects", "Set", "size", "reduce", "total", "onFilterChange", "days", "Date", "getDay", "now", "currentTime", "getHours", "toString", "padStart", "getMinutes", "classData", "className", "section", "programName", "semesterMap", "exportTimetable", "today", "filter", "sort", "a", "b", "localeCompare", "_", "ɵɵdirectiveInject", "i1", "TimetableService", "i2", "UserService", "i3", "MatSnackBar", "_2", "selectors", "decls", "vars", "consts", "template", "TeacherTimetableComponent_Template", "rf", "ctx", "TeacherTimetableComponent_Template_button_click_10_listener", "TeacherTimetableComponent_Template_button_click_14_listener", "TeacherTimetableComponent_Template_mat_select_valueChange_24_listener", "$event", "TeacherTimetableComponent_Template_mat_select_selectionChange_24_listener", "TeacherTimetableComponent_mat_option_25_Template", "TeacherTimetableComponent_Template_mat_select_valueChange_29_listener", "TeacherTimetableComponent_Template_mat_select_selectionChange_29_listener", "TeacherTimetableComponent_mat_option_32_Template", "TeacherTimetableComponent_div_78_Template", "TeacherTimetableComponent_div_79_Template", "TeacherTimetableComponent_div_80_Template", "TeacherTimetableComponent_div_81_Template", "TeacherTimetableComponent_div_82_Template", "ɵɵpipeBind1"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\teacher-dashboard\\teacher-timetable\\teacher-timetable.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\teacher-dashboard\\teacher-timetable\\teacher-timetable.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { TimetableService } from 'src/app/services/timetable.service';\r\nimport { UserService } from 'src/app/services/user.service';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\n\r\ninterface TimetableEntry {\r\n  _id: string;\r\n  dayOfWeek: string;\r\n  timeSlot: {\r\n    startTime: string;\r\n    endTime: string;\r\n    duration: number;\r\n  };\r\n  subject: {\r\n    _id: string;\r\n    subjectName: string;\r\n    code: string;\r\n  };\r\n  class: {\r\n    _id: string;\r\n    className: string;\r\n    section: string;\r\n  };\r\n  program: {\r\n    _id: string;\r\n    name: string;\r\n  };\r\n  department: {\r\n    _id: string;\r\n    name: string;\r\n  };\r\n  room?: string;\r\n  semester: number;\r\n  academicYear: string;\r\n  notes?: string;\r\n}\r\n\r\ninterface TimeSlot {\r\n  time: string;\r\n  startTime: string;\r\n  endTime: string;\r\n  monday?: TimetableEntry | null;\r\n  tuesday?: TimetableEntry | null;\r\n  wednesday?: TimetableEntry | null;\r\n  thursday?: TimetableEntry | null;\r\n  friday?: TimetableEntry | null;\r\n  saturday?: TimetableEntry | null;\r\n  [key: string]: any; // Allow dynamic indexing\r\n}\r\n\r\n@Component({\r\n  selector: 'app-teacher-timetable',\r\n  templateUrl: './teacher-timetable.component.html',\r\n  styleUrls: ['./teacher-timetable.component.css']\r\n})\r\nexport class TeacherTimetableComponent implements OnInit {\r\n  currentUser: any;\r\n  timetableEntries: TimetableEntry[] = [];\r\n  timetableGrid: TimeSlot[] = [];\r\n  loading = false;\r\n  error: string | null = null;\r\n  \r\n  weekDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];\r\n  timeSlots = [\r\n    { start: '08:00', end: '09:00' },\r\n    { start: '09:00', end: '10:00' },\r\n    { start: '10:00', end: '11:00' },\r\n    { start: '11:00', end: '12:00' },\r\n    { start: '12:00', end: '13:00' },\r\n    { start: '13:00', end: '14:00' },\r\n    { start: '14:00', end: '15:00' },\r\n    { start: '15:00', end: '16:00' },\r\n    { start: '16:00', end: '17:00' },\r\n    { start: '17:00', end: '18:00' }\r\n  ];\r\n\r\n  // Filter options\r\n  selectedAcademicYear = '2024-2025';\r\n  selectedDay = '';\r\n  availableAcademicYears = ['2024-2025', '2023-2024', '2022-2023'];\r\n\r\n  // Statistics\r\n  totalClasses = 0;\r\n  totalSubjects = 0;\r\n  totalHours = 0;\r\n\r\n  constructor(\r\n    private timetableService: TimetableService,\r\n    private userService: UserService,\r\n    private snackBar: MatSnackBar\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.currentUser = this.userService.getUserFromLocalStorage()?.user;\r\n    if (this.currentUser) {\r\n      this.loadTeacherTimetable();\r\n    } else {\r\n      this.error = 'User not found. Please login again.';\r\n    }\r\n  }\r\n\r\n  loadTeacherTimetable(): void {\r\n    this.loading = true;\r\n    this.error = null;\r\n\r\n    this.timetableService.getTimetableByTeacher(\r\n      this.currentUser._id, \r\n      this.selectedAcademicYear, \r\n      this.selectedDay\r\n    ).subscribe({\r\n      next: (response) => {\r\n        this.loading = false;\r\n        if (response.success) {\r\n          this.timetableEntries = response.timetable;\r\n          this.generateTimetableGrid();\r\n          this.calculateStatistics();\r\n        } else {\r\n          this.error = 'Failed to load timetable';\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.loading = false;\r\n        console.error('Error loading teacher timetable:', error);\r\n        this.error = 'Error loading timetable data';\r\n        this.snackBar.open('Failed to load timetable', 'Close', { duration: 3000 });\r\n      }\r\n    });\r\n  }\r\n\r\n  generateTimetableGrid(): void {\r\n    this.timetableGrid = this.timeSlots.map(slot => {\r\n      const timeSlot: TimeSlot = {\r\n        time: `${slot.start} - ${slot.end}`,\r\n        startTime: slot.start,\r\n        endTime: slot.end\r\n      };\r\n\r\n      // Initialize all days as null\r\n      this.weekDays.forEach(day => {\r\n        timeSlot[day.toLowerCase() as keyof TimeSlot] = null;\r\n      });\r\n\r\n      // Fill in the actual timetable entries\r\n      this.timetableEntries.forEach(entry => {\r\n        const entryStartTime = entry.timeSlot.startTime;\r\n        const dayKey = entry.dayOfWeek.toLowerCase() as keyof TimeSlot;\r\n        \r\n        if (entryStartTime === slot.start && this.weekDays.map(d => d.toLowerCase()).includes(dayKey as string)) {\r\n          timeSlot[dayKey] = entry;\r\n        }\r\n      });\r\n\r\n      return timeSlot;\r\n    });\r\n  }\r\n\r\n  calculateStatistics(): void {\r\n    this.totalClasses = this.timetableEntries.length;\r\n    \r\n    // Count unique subjects\r\n    const uniqueSubjects = new Set(this.timetableEntries.map(entry => entry.subject._id));\r\n    this.totalSubjects = uniqueSubjects.size;\r\n    \r\n    // Calculate total hours per week\r\n    this.totalHours = this.timetableEntries.reduce((total, entry) => {\r\n      return total + (entry.timeSlot.duration / 60); // Convert minutes to hours\r\n    }, 0);\r\n  }\r\n\r\n  onFilterChange(): void {\r\n    this.loadTeacherTimetable();\r\n  }\r\n\r\n  getCurrentDay(): string {\r\n    const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];\r\n    return days[new Date().getDay()];\r\n  }\r\n\r\n  getCurrentDate(): Date {\r\n    return new Date();\r\n  }\r\n\r\n  isCurrentTimeSlot(timeSlot: TimeSlot): boolean {\r\n    const now = new Date();\r\n    const currentTime = now.getHours().toString().padStart(2, '0') + ':' + \r\n                       now.getMinutes().toString().padStart(2, '0');\r\n    \r\n    return currentTime >= timeSlot.startTime && currentTime < timeSlot.endTime;\r\n  }\r\n\r\n  getClassDisplayName(classData: any): string {\r\n    if (!classData) return '';\r\n    return `${classData.className}${classData.section ? ' - ' + classData.section : ''}`;\r\n  }\r\n\r\n  getSemesterDisplayText(semester: number, programName: string): string {\r\n    if (programName === 'Intermediate') {\r\n      return semester === 1 ? '1st Year' : '2nd Year';\r\n    } else {\r\n      const semesterMap: { [key: number]: string } = {\r\n        1: '1st', 2: '2nd', 3: '3rd', 4: '4th',\r\n        5: '5th', 6: '6th', 7: '7th', 8: '8th'\r\n      };\r\n      return `${semesterMap[semester] || semester} Semester`;\r\n    }\r\n  }\r\n\r\n  refreshTimetable(): void {\r\n    this.loadTeacherTimetable();\r\n  }\r\n\r\n  exportTimetable(): void {\r\n    // Implementation for exporting timetable (PDF, Excel, etc.)\r\n    this.snackBar.open('Export functionality coming soon!', 'Close', { duration: 3000 });\r\n  }\r\n\r\n  getTodayClasses(): TimetableEntry[] {\r\n    const today = this.getCurrentDay();\r\n    return this.timetableEntries.filter(entry =>\r\n      entry.dayOfWeek.toLowerCase() === today\r\n    ).sort((a, b) => a.timeSlot.startTime.localeCompare(b.timeSlot.startTime));\r\n  }\r\n}\r\n", "<div class=\"teacher-timetable-container\">\r\n  <!-- Header -->\r\n  <div class=\"page-header\">\r\n    <div class=\"header-content\">\r\n      <h1 class=\"page-title\">\r\n        <mat-icon class=\"title-icon\">schedule</mat-icon>\r\n        My Timetable\r\n      </h1>\r\n      <p class=\"page-subtitle\">Your weekly teaching schedule</p>\r\n    </div>\r\n    <div class=\"header-actions\">\r\n      <button mat-raised-button color=\"primary\" (click)=\"refreshTimetable()\">\r\n        <mat-icon>refresh</mat-icon>\r\n        Refresh\r\n      </button>\r\n      <button mat-raised-button color=\"accent\" (click)=\"exportTimetable()\">\r\n        <mat-icon>download</mat-icon>\r\n        Export\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Filters -->\r\n  <mat-card class=\"filters-card\">\r\n    <mat-card-content>\r\n      <div class=\"filters-row\">\r\n        <mat-form-field appearance=\"outline\">\r\n          <mat-label>Academic Year</mat-label>\r\n          <mat-select [(value)]=\"selectedAcademicYear\" (selectionChange)=\"onFilterChange()\">\r\n            <mat-option *ngFor=\"let year of availableAcademicYears\" [value]=\"year\">\r\n              {{ year }}\r\n            </mat-option>\r\n          </mat-select>\r\n        </mat-form-field>\r\n\r\n        <mat-form-field appearance=\"outline\">\r\n          <mat-label>Filter by Day</mat-label>\r\n          <mat-select [(value)]=\"selectedDay\" (selectionChange)=\"onFilterChange()\">\r\n            <mat-option value=\"\">All Days</mat-option>\r\n            <mat-option *ngFor=\"let day of weekDays\" [value]=\"day\">\r\n              {{ day }}\r\n            </mat-option>\r\n          </mat-select>\r\n        </mat-form-field>\r\n      </div>\r\n    </mat-card-content>\r\n  </mat-card>\r\n\r\n  <!-- Statistics Cards -->\r\n  <div class=\"stats-section\">\r\n    <div class=\"stats-row\">\r\n      <mat-card class=\"stat-card\">\r\n        <mat-card-content>\r\n          <div class=\"stat-content\">\r\n            <mat-icon class=\"stat-icon\">class</mat-icon>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ totalClasses }}</h3>\r\n              <p>Total Classes</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n\r\n      <mat-card class=\"stat-card\">\r\n        <mat-card-content>\r\n          <div class=\"stat-content\">\r\n            <mat-icon class=\"stat-icon\">subject</mat-icon>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ totalSubjects }}</h3>\r\n              <p>Subjects</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n\r\n      <mat-card class=\"stat-card\">\r\n        <mat-card-content>\r\n          <div class=\"stat-content\">\r\n            <mat-icon class=\"stat-icon\">access_time</mat-icon>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ totalHours }}</h3>\r\n              <p>Hours/Week</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Current Day Highlight -->\r\n  <div class=\"current-day-info\">\r\n    <mat-card class=\"current-day-card\">\r\n      <mat-card-content>\r\n        <div class=\"current-day-content\">\r\n          <mat-icon>today</mat-icon>\r\n          <div class=\"day-info\">\r\n            <h3>Today is {{ getCurrentDay() | titlecase }}</h3>\r\n            <p>{{ getCurrentDate() | date:'fullDate' }}</p>\r\n          </div>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n  </div>\r\n\r\n  <!-- Loading State -->\r\n  <div *ngIf=\"loading\" class=\"loading-container\">\r\n    <mat-spinner></mat-spinner>\r\n    <p>Loading your timetable...</p>\r\n  </div>\r\n\r\n  <!-- Error State -->\r\n  <div *ngIf=\"error && !loading\" class=\"error-container\">\r\n    <mat-icon>error</mat-icon>\r\n    <h3>Error Loading Timetable</h3>\r\n    <p>{{ error }}</p>\r\n    <button mat-raised-button color=\"primary\" (click)=\"refreshTimetable()\">\r\n      Try Again\r\n    </button>\r\n  </div>\r\n\r\n  <!-- Timetable Grid -->\r\n  <div *ngIf=\"!loading && !error\" class=\"timetable-wrapper\">\r\n    <mat-card class=\"timetable-card\">\r\n      <mat-card-header>\r\n        <mat-card-title>Weekly Schedule</mat-card-title>\r\n        <mat-card-subtitle>Your teaching schedule for {{ selectedAcademicYear }}</mat-card-subtitle>\r\n      </mat-card-header>\r\n      \r\n      <mat-card-content>\r\n        <div class=\"timetable-grid\">\r\n          <!-- Header Row -->\r\n          <div class=\"timetable-header\">\r\n            <div class=\"time-header\">Time</div>\r\n            <div *ngFor=\"let day of weekDays\" class=\"day-header\" \r\n                 [class.current-day]=\"day.toLowerCase() === getCurrentDay()\">\r\n              {{ day }}\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Time Slots -->\r\n          <div *ngFor=\"let slot of timetableGrid\" class=\"timetable-row\"\r\n               [class.current-time]=\"isCurrentTimeSlot(slot)\">\r\n            <div class=\"time-slot\">{{ slot.time }}</div>\r\n            \r\n            <div *ngFor=\"let day of weekDays\" class=\"schedule-cell\"\r\n                 [class.current-day]=\"day.toLowerCase() === getCurrentDay()\"\r\n                 [class.has-class]=\"slot[day.toLowerCase()]\">\r\n              \r\n              <div *ngIf=\"slot[day.toLowerCase()]; else emptySlot\" class=\"class-info\">\r\n                <div class=\"subject-name\">{{ slot[day.toLowerCase()]?.subject.subjectName }}</div>\r\n                <div class=\"class-details\">\r\n                  <span class=\"class-name\">{{ getClassDisplayName(slot[day.toLowerCase()]?.class) }}</span>\r\n                  <span class=\"program-info\">{{ slot[day.toLowerCase()]?.program.name }}</span>\r\n                </div>\r\n                <div class=\"additional-info\">\r\n                  <span class=\"semester\">{{ getSemesterDisplayText(slot[day.toLowerCase()]?.semester, slot[day.toLowerCase()]?.program.name) }}</span>\r\n                  <span class=\"room\" *ngIf=\"slot[day.toLowerCase()]?.room\">Room: {{ slot[day.toLowerCase()]?.room }}</span>\r\n                </div>\r\n              </div>\r\n              \r\n              <ng-template #emptySlot>\r\n                <div class=\"empty-slot\">\r\n                  <span class=\"free-time\">Free</span>\r\n                </div>\r\n              </ng-template>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n  </div>\r\n\r\n  <!-- Today's Classes Summary -->\r\n  <div *ngIf=\"!loading && !error && timetableEntries.length > 0\" class=\"today-summary\">\r\n    <mat-card class=\"summary-card\">\r\n      <mat-card-header>\r\n        <mat-card-title>Today's Classes</mat-card-title>\r\n        <mat-card-subtitle>{{ getCurrentDate() | date:'fullDate' }}</mat-card-subtitle>\r\n      </mat-card-header>\r\n      <mat-card-content>\r\n        <div class=\"today-classes\">\r\n          <div *ngFor=\"let entry of getTodayClasses()\" class=\"today-class-item\">\r\n            <div class=\"time-info\">\r\n              <span class=\"time\">{{ entry.timeSlot.startTime }} - {{ entry.timeSlot.endTime }}</span>\r\n            </div>\r\n            <div class=\"class-details\">\r\n              <h4>{{ entry.subject.subjectName }}</h4>\r\n              <p>{{ getClassDisplayName(entry.class) }} - {{ entry.program.name }}</p>\r\n              <small>{{ getSemesterDisplayText(entry.semester, entry.program.name) }}</small>\r\n            </div>\r\n            <div class=\"room-info\" *ngIf=\"entry.room\">\r\n              <mat-icon>location_on</mat-icon>\r\n              <span>{{ entry.room }}</span>\r\n            </div>\r\n          </div>\r\n          <div *ngIf=\"getTodayClasses().length === 0\" class=\"no-classes\">\r\n            <mat-icon>free_breakfast</mat-icon>\r\n            <p>No classes scheduled for today!</p>\r\n          </div>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n  </div>\r\n\r\n  <!-- Empty State -->\r\n  <div *ngIf=\"!loading && !error && timetableEntries.length === 0\" class=\"empty-state\">\r\n    <mat-card class=\"empty-card\">\r\n      <mat-card-content>\r\n        <div class=\"empty-content\">\r\n          <mat-icon>schedule</mat-icon>\r\n          <h3>No Timetable Found</h3>\r\n          <p>You don't have any classes scheduled for the selected academic year.</p>\r\n          <p>Please contact the administration if you believe this is an error.</p>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n  </div>\r\n</div>\r\n"], "mappings": ";;;;;;;;;;;;;;IC6BYA,EAAA,CAAAC,cAAA,qBAAuE;IACrED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF2CH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IACpEL,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,OAAA,MACF;;;;;IAQAL,EAAA,CAAAC,cAAA,qBAAuD;IACrDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF4BH,EAAA,CAAAI,UAAA,UAAAI,MAAA,CAAa;IACpDR,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAC,MAAA,MACF;;;;;IAgEVR,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAS,SAAA,kBAA2B;IAC3BT,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,gCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;IAIlCH,EAAA,CAAAC,cAAA,cAAuD;IAC3CD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAClBH,EAAA,CAAAC,cAAA,gBAAuE;IAA7BD,EAAA,CAAAU,UAAA,mBAAAC,kEAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,IAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAF,MAAA,CAAAG,gBAAA,EAAkB;IAAA,EAAC;IACpEjB,EAAA,CAAAE,MAAA,kBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAHNH,EAAA,CAAAM,SAAA,GAAW;IAAXN,EAAA,CAAAkB,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IAmBNpB,EAAA,CAAAC,cAAA,cACiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAFDH,EAAA,CAAAqB,WAAA,gBAAAC,OAAA,CAAAC,WAAA,OAAAC,OAAA,CAAAC,aAAA,GAA2D;IAC9DzB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAe,OAAA,MACF;;;;;IAoBMtB,EAAA,CAAAC,cAAA,eAAyD;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAAhDH,EAAA,CAAAM,SAAA,GAAyC;IAAzCN,EAAA,CAAAO,kBAAA,WAAAmB,QAAA,CAAAC,OAAA,CAAAJ,WAAA,qBAAAG,QAAA,CAAAC,OAAA,CAAAJ,WAAA,IAAAK,IAAA,KAAyC;;;;;IARtG5B,EAAA,CAAAC,cAAA,cAAwE;IAC5CD,EAAA,CAAAE,MAAA,GAAkD;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAClFH,EAAA,CAAAC,cAAA,cAA2B;IACAD,EAAA,CAAAE,MAAA,GAAyD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzFH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE/EH,EAAA,CAAAC,cAAA,cAA6B;IACJD,EAAA,CAAAE,MAAA,IAAsG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpIH,EAAA,CAAA6B,UAAA,KAAAC,oEAAA,mBAAyG;IAC3G9B,EAAA,CAAAG,YAAA,EAAM;;;;;;IARoBH,EAAA,CAAAM,SAAA,GAAkD;IAAlDN,EAAA,CAAAkB,iBAAA,CAAAQ,QAAA,CAAAC,OAAA,CAAAJ,WAAA,qBAAAG,QAAA,CAAAC,OAAA,CAAAJ,WAAA,IAAAQ,OAAA,CAAAC,WAAA,CAAkD;IAEjDhC,EAAA,CAAAM,SAAA,GAAyD;IAAzDN,EAAA,CAAAkB,iBAAA,CAAAe,OAAA,CAAAC,mBAAA,CAAAR,QAAA,CAAAC,OAAA,CAAAJ,WAAA,qBAAAG,QAAA,CAAAC,OAAA,CAAAJ,WAAA,IAAAY,KAAA,EAAyD;IACvDnC,EAAA,CAAAM,SAAA,GAA2C;IAA3CN,EAAA,CAAAkB,iBAAA,CAAAQ,QAAA,CAAAC,OAAA,CAAAJ,WAAA,qBAAAG,QAAA,CAAAC,OAAA,CAAAJ,WAAA,IAAAa,OAAA,CAAAC,IAAA,CAA2C;IAG/CrC,EAAA,CAAAM,SAAA,GAAsG;IAAtGN,EAAA,CAAAkB,iBAAA,CAAAe,OAAA,CAAAK,sBAAA,CAAAZ,QAAA,CAAAC,OAAA,CAAAJ,WAAA,qBAAAG,QAAA,CAAAC,OAAA,CAAAJ,WAAA,IAAAgB,QAAA,EAAAb,QAAA,CAAAC,OAAA,CAAAJ,WAAA,qBAAAG,QAAA,CAAAC,OAAA,CAAAJ,WAAA,IAAAa,OAAA,CAAAC,IAAA,EAAsG;IACzGrC,EAAA,CAAAM,SAAA,GAAmC;IAAnCN,EAAA,CAAAI,UAAA,SAAAsB,QAAA,CAAAC,OAAA,CAAAJ,WAAA,qBAAAG,QAAA,CAAAC,OAAA,CAAAJ,WAAA,IAAAK,IAAA,CAAmC;;;;;IAKzD5B,EAAA,CAAAC,cAAA,cAAwB;IACED,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAlBzCH,EAAA,CAAAC,cAAA,cAEiD;IAE/CD,EAAA,CAAA6B,UAAA,IAAAW,4DAAA,mBAUM;IAENxC,EAAA,CAAA6B,UAAA,IAAAY,oEAAA,iCAAAzC,EAAA,CAAA0C,sBAAA,CAIc;IAChB1C,EAAA,CAAAG,YAAA,EAAM;;;;;;;IApBDH,EAAA,CAAAqB,WAAA,gBAAAM,OAAA,CAAAJ,WAAA,OAAAoB,OAAA,CAAAlB,aAAA,GAA2D,cAAAC,QAAA,CAAAC,OAAA,CAAAJ,WAAA;IAGxDvB,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAsB,QAAA,CAAAC,OAAA,CAAAJ,WAAA,IAA+B,aAAAqB,IAAA;;;;;IARzC5C,EAAA,CAAAC,cAAA,cACoD;IAC3BD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAE5CH,EAAA,CAAA6B,UAAA,IAAAgB,sDAAA,kBAqBM;IACR7C,EAAA,CAAAG,YAAA,EAAM;;;;;IAzBDH,EAAA,CAAAqB,WAAA,iBAAAyB,OAAA,CAAAC,iBAAA,CAAArB,QAAA,EAA8C;IAC1B1B,EAAA,CAAAM,SAAA,GAAe;IAAfN,EAAA,CAAAkB,iBAAA,CAAAQ,QAAA,CAAAsB,IAAA,CAAe;IAEjBhD,EAAA,CAAAM,SAAA,GAAW;IAAXN,EAAA,CAAAI,UAAA,YAAA0C,OAAA,CAAAG,QAAA,CAAW;;;;;IAvB1CjD,EAAA,CAAAC,cAAA,cAA0D;IAGpCD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAChDH,EAAA,CAAAC,cAAA,wBAAmB;IAAAD,EAAA,CAAAE,MAAA,GAAqD;IAAAF,EAAA,CAAAG,YAAA,EAAoB;IAG9FH,EAAA,CAAAC,cAAA,uBAAkB;IAIaD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACnCH,EAAA,CAAA6B,UAAA,KAAAqB,gDAAA,kBAGM;IACRlD,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAA6B,UAAA,KAAAsB,gDAAA,kBA0BM;IACRnD,EAAA,CAAAG,YAAA,EAAM;;;;IA1CaH,EAAA,CAAAM,SAAA,GAAqD;IAArDN,EAAA,CAAAO,kBAAA,gCAAA6C,MAAA,CAAAC,oBAAA,KAAqD;IAQ/CrD,EAAA,CAAAM,SAAA,GAAW;IAAXN,EAAA,CAAAI,UAAA,YAAAgD,MAAA,CAAAH,QAAA,CAAW;IAOZjD,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAI,UAAA,YAAAgD,MAAA,CAAAE,aAAA,CAAgB;;;;;IAkDpCtD,EAAA,CAAAC,cAAA,cAA0C;IAC9BD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAvBH,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAkB,iBAAA,CAAAqC,SAAA,CAAA3B,IAAA,CAAgB;;;;;IAX1B5B,EAAA,CAAAC,cAAA,cAAsE;IAE/CD,EAAA,CAAAE,MAAA,GAA6D;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEzFH,EAAA,CAAAC,cAAA,cAA2B;IACrBD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAiE;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACxEH,EAAA,CAAAC,cAAA,YAAO;IAAAD,EAAA,CAAAE,MAAA,IAAgE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAEjFH,EAAA,CAAA6B,UAAA,KAAA2B,uDAAA,kBAGM;IACRxD,EAAA,CAAAG,YAAA,EAAM;;;;;IAXiBH,EAAA,CAAAM,SAAA,GAA6D;IAA7DN,EAAA,CAAAyD,kBAAA,KAAAF,SAAA,CAAAG,QAAA,CAAAC,SAAA,SAAAJ,SAAA,CAAAG,QAAA,CAAAE,OAAA,KAA6D;IAG5E5D,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAkB,iBAAA,CAAAqC,SAAA,CAAAxB,OAAA,CAAAC,WAAA,CAA+B;IAChChC,EAAA,CAAAM,SAAA,GAAiE;IAAjEN,EAAA,CAAAyD,kBAAA,KAAAI,OAAA,CAAA3B,mBAAA,CAAAqB,SAAA,CAAApB,KAAA,UAAAoB,SAAA,CAAAnB,OAAA,CAAAC,IAAA,KAAiE;IAC7DrC,EAAA,CAAAM,SAAA,GAAgE;IAAhEN,EAAA,CAAAkB,iBAAA,CAAA2C,OAAA,CAAAvB,sBAAA,CAAAiB,SAAA,CAAAhB,QAAA,EAAAgB,SAAA,CAAAnB,OAAA,CAAAC,IAAA,EAAgE;IAEjDrC,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAI,UAAA,SAAAmD,SAAA,CAAA3B,IAAA,CAAgB;;;;;IAK1C5B,EAAA,CAAAC,cAAA,cAA+D;IACnDD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACnCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,sCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAxBhDH,EAAA,CAAAC,cAAA,cAAqF;IAG/DD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAChDH,EAAA,CAAAC,cAAA,wBAAmB;IAAAD,EAAA,CAAAE,MAAA,GAAwC;;IAAAF,EAAA,CAAAG,YAAA,EAAoB;IAEjFH,EAAA,CAAAC,cAAA,uBAAkB;IAEdD,EAAA,CAAA6B,UAAA,KAAAiC,gDAAA,mBAaM;IACN9D,EAAA,CAAA6B,UAAA,KAAAkC,gDAAA,kBAGM;IACR/D,EAAA,CAAAG,YAAA,EAAM;;;;IAtBaH,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAkB,iBAAA,CAAAlB,EAAA,CAAAgE,WAAA,OAAAC,MAAA,CAAAC,cAAA,gBAAwC;IAIlClE,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAI,UAAA,YAAA6D,MAAA,CAAAE,eAAA,GAAoB;IAcrCnE,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAAI,UAAA,SAAA6D,MAAA,CAAAE,eAAA,GAAAC,MAAA,OAAoC;;;;;IAUlDpE,EAAA,CAAAC,cAAA,cAAqF;IAInED,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,2EAAoE;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC3EH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,0EAAkE;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;AD7JnF,OAAM,MAAOkE,yBAAyB;EA+BpCC,YACUC,gBAAkC,EAClCC,WAAwB,EACxBC,QAAqB;IAFrB,KAAAF,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAhClB,KAAAC,gBAAgB,GAAqB,EAAE;IACvC,KAAApB,aAAa,GAAe,EAAE;IAC9B,KAAAqB,OAAO,GAAG,KAAK;IACf,KAAAvD,KAAK,GAAkB,IAAI;IAE3B,KAAA6B,QAAQ,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,CAAC;IAC/E,KAAA2B,SAAS,GAAG,CACV;MAAEC,KAAK,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAO,CAAE,EAChC;MAAED,KAAK,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAO,CAAE,EAChC;MAAED,KAAK,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAO,CAAE,EAChC;MAAED,KAAK,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAO,CAAE,EAChC;MAAED,KAAK,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAO,CAAE,EAChC;MAAED,KAAK,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAO,CAAE,EAChC;MAAED,KAAK,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAO,CAAE,EAChC;MAAED,KAAK,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAO,CAAE,EAChC;MAAED,KAAK,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAO,CAAE,EAChC;MAAED,KAAK,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAO,CAAE,CACjC;IAED;IACA,KAAAzB,oBAAoB,GAAG,WAAW;IAClC,KAAA0B,WAAW,GAAG,EAAE;IAChB,KAAAC,sBAAsB,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,CAAC;IAEhE;IACA,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,UAAU,GAAG,CAAC;EAMX;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,GAAG,IAAI,CAACb,WAAW,CAACc,uBAAuB,EAAE,EAAEC,IAAI;IACnE,IAAI,IAAI,CAACF,WAAW,EAAE;MACpB,IAAI,CAACG,oBAAoB,EAAE;KAC5B,MAAM;MACL,IAAI,CAACpE,KAAK,GAAG,qCAAqC;;EAEtD;EAEAoE,oBAAoBA,CAAA;IAClB,IAAI,CAACb,OAAO,GAAG,IAAI;IACnB,IAAI,CAACvD,KAAK,GAAG,IAAI;IAEjB,IAAI,CAACmD,gBAAgB,CAACkB,qBAAqB,CACzC,IAAI,CAACJ,WAAW,CAACK,GAAG,EACpB,IAAI,CAACrC,oBAAoB,EACzB,IAAI,CAAC0B,WAAW,CACjB,CAACY,SAAS,CAAC;MACVC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAClB,OAAO,GAAG,KAAK;QACpB,IAAIkB,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACpB,gBAAgB,GAAGmB,QAAQ,CAACE,SAAS;UAC1C,IAAI,CAACC,qBAAqB,EAAE;UAC5B,IAAI,CAACC,mBAAmB,EAAE;SAC3B,MAAM;UACL,IAAI,CAAC7E,KAAK,GAAG,0BAA0B;;MAE3C,CAAC;MACDA,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACuD,OAAO,GAAG,KAAK;QACpBuB,OAAO,CAAC9E,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,IAAI,CAACA,KAAK,GAAG,8BAA8B;QAC3C,IAAI,CAACqD,QAAQ,CAAC0B,IAAI,CAAC,0BAA0B,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MAC7E;KACD,CAAC;EACJ;EAEAJ,qBAAqBA,CAAA;IACnB,IAAI,CAAC1C,aAAa,GAAG,IAAI,CAACsB,SAAS,CAACyB,GAAG,CAACC,IAAI,IAAG;MAC7C,MAAM5C,QAAQ,GAAa;QACzBV,IAAI,EAAE,GAAGsD,IAAI,CAACzB,KAAK,MAAMyB,IAAI,CAACxB,GAAG,EAAE;QACnCnB,SAAS,EAAE2C,IAAI,CAACzB,KAAK;QACrBjB,OAAO,EAAE0C,IAAI,CAACxB;OACf;MAED;MACA,IAAI,CAAC7B,QAAQ,CAACsD,OAAO,CAACC,GAAG,IAAG;QAC1B9C,QAAQ,CAAC8C,GAAG,CAACjF,WAAW,EAAoB,CAAC,GAAG,IAAI;MACtD,CAAC,CAAC;MAEF;MACA,IAAI,CAACmD,gBAAgB,CAAC6B,OAAO,CAACE,KAAK,IAAG;QACpC,MAAMC,cAAc,GAAGD,KAAK,CAAC/C,QAAQ,CAACC,SAAS;QAC/C,MAAMgD,MAAM,GAAGF,KAAK,CAACG,SAAS,CAACrF,WAAW,EAAoB;QAE9D,IAAImF,cAAc,KAAKJ,IAAI,CAACzB,KAAK,IAAI,IAAI,CAAC5B,QAAQ,CAACoD,GAAG,CAACQ,CAAC,IAAIA,CAAC,CAACtF,WAAW,EAAE,CAAC,CAACuF,QAAQ,CAACH,MAAgB,CAAC,EAAE;UACvGjD,QAAQ,CAACiD,MAAM,CAAC,GAAGF,KAAK;;MAE5B,CAAC,CAAC;MAEF,OAAO/C,QAAQ;IACjB,CAAC,CAAC;EACJ;EAEAuC,mBAAmBA,CAAA;IACjB,IAAI,CAAChB,YAAY,GAAG,IAAI,CAACP,gBAAgB,CAACN,MAAM;IAEhD;IACA,MAAM2C,cAAc,GAAG,IAAIC,GAAG,CAAC,IAAI,CAACtC,gBAAgB,CAAC2B,GAAG,CAACI,KAAK,IAAIA,KAAK,CAAC1E,OAAO,CAAC2D,GAAG,CAAC,CAAC;IACrF,IAAI,CAACR,aAAa,GAAG6B,cAAc,CAACE,IAAI;IAExC;IACA,IAAI,CAAC9B,UAAU,GAAG,IAAI,CAACT,gBAAgB,CAACwC,MAAM,CAAC,CAACC,KAAK,EAAEV,KAAK,KAAI;MAC9D,OAAOU,KAAK,GAAIV,KAAK,CAAC/C,QAAQ,CAAC0C,QAAQ,GAAG,EAAG,CAAC,CAAC;IACjD,CAAC,EAAE,CAAC,CAAC;EACP;EAEAgB,cAAcA,CAAA;IACZ,IAAI,CAAC5B,oBAAoB,EAAE;EAC7B;EAEA/D,aAAaA,CAAA;IACX,MAAM4F,IAAI,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,CAAC;IAC3F,OAAOA,IAAI,CAAC,IAAIC,IAAI,EAAE,CAACC,MAAM,EAAE,CAAC;EAClC;EAEArD,cAAcA,CAAA;IACZ,OAAO,IAAIoD,IAAI,EAAE;EACnB;EAEAvE,iBAAiBA,CAACW,QAAkB;IAClC,MAAM8D,GAAG,GAAG,IAAIF,IAAI,EAAE;IACtB,MAAMG,WAAW,GAAGD,GAAG,CAACE,QAAQ,EAAE,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,GACjDJ,GAAG,CAACK,UAAU,EAAE,CAACF,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAE/D,OAAOH,WAAW,IAAI/D,QAAQ,CAACC,SAAS,IAAI8D,WAAW,GAAG/D,QAAQ,CAACE,OAAO;EAC5E;EAEA1B,mBAAmBA,CAAC4F,SAAc;IAChC,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IACzB,OAAO,GAAGA,SAAS,CAACC,SAAS,GAAGD,SAAS,CAACE,OAAO,GAAG,KAAK,GAAGF,SAAS,CAACE,OAAO,GAAG,EAAE,EAAE;EACtF;EAEA1F,sBAAsBA,CAACC,QAAgB,EAAE0F,WAAmB;IAC1D,IAAIA,WAAW,KAAK,cAAc,EAAE;MAClC,OAAO1F,QAAQ,KAAK,CAAC,GAAG,UAAU,GAAG,UAAU;KAChD,MAAM;MACL,MAAM2F,WAAW,GAA8B;QAC7C,CAAC,EAAE,KAAK;QAAE,CAAC,EAAE,KAAK;QAAE,CAAC,EAAE,KAAK;QAAE,CAAC,EAAE,KAAK;QACtC,CAAC,EAAE,KAAK;QAAE,CAAC,EAAE,KAAK;QAAE,CAAC,EAAE,KAAK;QAAE,CAAC,EAAE;OAClC;MACD,OAAO,GAAGA,WAAW,CAAC3F,QAAQ,CAAC,IAAIA,QAAQ,WAAW;;EAE1D;EAEAtB,gBAAgBA,CAAA;IACd,IAAI,CAACuE,oBAAoB,EAAE;EAC7B;EAEA2C,eAAeA,CAAA;IACb;IACA,IAAI,CAAC1D,QAAQ,CAAC0B,IAAI,CAAC,mCAAmC,EAAE,OAAO,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE,CAAC;EACtF;EAEAjC,eAAeA,CAAA;IACb,MAAMiE,KAAK,GAAG,IAAI,CAAC3G,aAAa,EAAE;IAClC,OAAO,IAAI,CAACiD,gBAAgB,CAAC2D,MAAM,CAAC5B,KAAK,IACvCA,KAAK,CAACG,SAAS,CAACrF,WAAW,EAAE,KAAK6G,KAAK,CACxC,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC7E,QAAQ,CAACC,SAAS,CAAC8E,aAAa,CAACD,CAAC,CAAC9E,QAAQ,CAACC,SAAS,CAAC,CAAC;EAC5E;EAAC,QAAA+E,CAAA,G;qBAtKUrE,yBAAyB,EAAArE,EAAA,CAAA2I,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAA7I,EAAA,CAAA2I,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA/I,EAAA,CAAA2I,iBAAA,CAAAK,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAzB7E,yBAAyB;IAAA8E,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCvDtCzJ,EAAA,CAAAC,cAAA,aAAyC;QAKJD,EAAA,CAAAE,MAAA,eAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAChDH,EAAA,CAAAE,MAAA,qBACF;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAC,cAAA,WAAyB;QAAAD,EAAA,CAAAE,MAAA,oCAA6B;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAE5DH,EAAA,CAAAC,cAAA,aAA4B;QACgBD,EAAA,CAAAU,UAAA,mBAAAiJ,4DAAA;UAAA,OAASD,GAAA,CAAAzI,gBAAA,EAAkB;QAAA,EAAC;QACpEjB,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC5BH,EAAA,CAAAE,MAAA,iBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,iBAAqE;QAA5BD,EAAA,CAAAU,UAAA,mBAAAkJ,4DAAA;UAAA,OAASF,GAAA,CAAAvB,eAAA,EAAiB;QAAA,EAAC;QAClEnI,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC7BH,EAAA,CAAAE,MAAA,gBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAKbH,EAAA,CAAAC,cAAA,mBAA+B;QAIZD,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACpCH,EAAA,CAAAC,cAAA,sBAAkF;QAAtED,EAAA,CAAAU,UAAA,yBAAAmJ,sEAAAC,MAAA;UAAA,OAAAJ,GAAA,CAAArG,oBAAA,GAAAyG,MAAA;QAAA,EAAgC,6BAAAC,0EAAA;UAAA,OAAoBL,GAAA,CAAAtC,cAAA,EAAgB;QAAA,EAApC;QAC1CpH,EAAA,CAAA6B,UAAA,KAAAmI,gDAAA,yBAEa;QACfhK,EAAA,CAAAG,YAAA,EAAa;QAGfH,EAAA,CAAAC,cAAA,0BAAqC;QACxBD,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACpCH,EAAA,CAAAC,cAAA,sBAAyE;QAA7DD,EAAA,CAAAU,UAAA,yBAAAuJ,sEAAAH,MAAA;UAAA,OAAAJ,GAAA,CAAA3E,WAAA,GAAA+E,MAAA;QAAA,EAAuB,6BAAAI,0EAAA;UAAA,OAAoBR,GAAA,CAAAtC,cAAA,EAAgB;QAAA,EAApC;QACjCpH,EAAA,CAAAC,cAAA,sBAAqB;QAAAD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAa;QAC1CH,EAAA,CAAA6B,UAAA,KAAAsI,gDAAA,yBAEa;QACfnK,EAAA,CAAAG,YAAA,EAAa;QAOrBH,EAAA,CAAAC,cAAA,eAA2B;QAKWD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC5CH,EAAA,CAAAC,cAAA,eAAuB;QACjBD,EAAA,CAAAE,MAAA,IAAkB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC3BH,EAAA,CAAAC,cAAA,SAAG;QAAAD,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAM5BH,EAAA,CAAAC,cAAA,oBAA4B;QAGMD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC9CH,EAAA,CAAAC,cAAA,eAAuB;QACjBD,EAAA,CAAAE,MAAA,IAAmB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC5BH,EAAA,CAAAC,cAAA,SAAG;QAAAD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAMvBH,EAAA,CAAAC,cAAA,oBAA4B;QAGMD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAClDH,EAAA,CAAAC,cAAA,eAAuB;QACjBD,EAAA,CAAAE,MAAA,IAAgB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACzBH,EAAA,CAAAC,cAAA,SAAG;QAAAD,EAAA,CAAAE,MAAA,kBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAS7BH,EAAA,CAAAC,cAAA,eAA8B;QAIZD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC1BH,EAAA,CAAAC,cAAA,eAAsB;QAChBD,EAAA,CAAAE,MAAA,IAA0C;;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACnDH,EAAA,CAAAC,cAAA,SAAG;QAAAD,EAAA,CAAAE,MAAA,IAAwC;;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAQzDH,EAAA,CAAA6B,UAAA,KAAAuI,yCAAA,kBAGM;QAGNpK,EAAA,CAAA6B,UAAA,KAAAwI,yCAAA,kBAOM;QAGNrK,EAAA,CAAA6B,UAAA,KAAAyI,yCAAA,mBAiDM;QAGNtK,EAAA,CAAA6B,UAAA,KAAA0I,yCAAA,mBA6BM;QAGNvK,EAAA,CAAA6B,UAAA,KAAA2I,yCAAA,mBAWM;QACRxK,EAAA,CAAAG,YAAA,EAAM;;;QA7LgBH,EAAA,CAAAM,SAAA,IAAgC;QAAhCN,EAAA,CAAAI,UAAA,UAAAsJ,GAAA,CAAArG,oBAAA,CAAgC;QACbrD,EAAA,CAAAM,SAAA,GAAyB;QAAzBN,EAAA,CAAAI,UAAA,YAAAsJ,GAAA,CAAA1E,sBAAA,CAAyB;QAQ5ChF,EAAA,CAAAM,SAAA,GAAuB;QAAvBN,EAAA,CAAAI,UAAA,UAAAsJ,GAAA,CAAA3E,WAAA,CAAuB;QAEL/E,EAAA,CAAAM,SAAA,GAAW;QAAXN,EAAA,CAAAI,UAAA,YAAAsJ,GAAA,CAAAzG,QAAA,CAAW;QAiBjCjD,EAAA,CAAAM,SAAA,IAAkB;QAAlBN,EAAA,CAAAkB,iBAAA,CAAAwI,GAAA,CAAAzE,YAAA,CAAkB;QAYlBjF,EAAA,CAAAM,SAAA,IAAmB;QAAnBN,EAAA,CAAAkB,iBAAA,CAAAwI,GAAA,CAAAxE,aAAA,CAAmB;QAYnBlF,EAAA,CAAAM,SAAA,IAAgB;QAAhBN,EAAA,CAAAkB,iBAAA,CAAAwI,GAAA,CAAAvE,UAAA,CAAgB;QAgBlBnF,EAAA,CAAAM,SAAA,IAA0C;QAA1CN,EAAA,CAAAO,kBAAA,cAAAP,EAAA,CAAAyK,WAAA,SAAAf,GAAA,CAAAjI,aAAA,QAA0C;QAC3CzB,EAAA,CAAAM,SAAA,GAAwC;QAAxCN,EAAA,CAAAkB,iBAAA,CAAAlB,EAAA,CAAAgE,WAAA,SAAA0F,GAAA,CAAAxF,cAAA,gBAAwC;QAQ/ClE,EAAA,CAAAM,SAAA,GAAa;QAAbN,EAAA,CAAAI,UAAA,SAAAsJ,GAAA,CAAA/E,OAAA,CAAa;QAMb3E,EAAA,CAAAM,SAAA,GAAuB;QAAvBN,EAAA,CAAAI,UAAA,SAAAsJ,GAAA,CAAAtI,KAAA,KAAAsI,GAAA,CAAA/E,OAAA,CAAuB;QAUvB3E,EAAA,CAAAM,SAAA,GAAwB;QAAxBN,EAAA,CAAAI,UAAA,UAAAsJ,GAAA,CAAA/E,OAAA,KAAA+E,GAAA,CAAAtI,KAAA,CAAwB;QAoDxBpB,EAAA,CAAAM,SAAA,GAAuD;QAAvDN,EAAA,CAAAI,UAAA,UAAAsJ,GAAA,CAAA/E,OAAA,KAAA+E,GAAA,CAAAtI,KAAA,IAAAsI,GAAA,CAAAhF,gBAAA,CAAAN,MAAA,KAAuD;QAgCvDpE,EAAA,CAAAM,SAAA,GAAyD;QAAzDN,EAAA,CAAAI,UAAA,UAAAsJ,GAAA,CAAA/E,OAAA,KAAA+E,GAAA,CAAAtI,KAAA,IAAAsI,GAAA,CAAAhF,gBAAA,CAAAN,MAAA,OAAyD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}