import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { TimetableService } from 'src/app/services/timetable.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import Swal from 'sweetalert2';

interface TimetableEntry {
  _id: string;
  program: any;
  department: any;
  class: any;
  subject: any;
  teacher: any;
  dayOfWeek: string;
  timeSlot: {
    startTime: string;
    endTime: string;
    duration: number;
  };
  room?: string;
  semester: number;
  academicYear: string;
  isActive: boolean;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

@Component({
  selector: 'app-timetable-list',
  templateUrl: './timetable-list.component.html',
  styleUrls: ['./timetable-list.component.css']
})
export class TimetableListComponent implements OnInit {
  timetables: TimetableEntry[] = [];
  filteredTimetables: TimetableEntry[] = [];
  loading = false;
  
  // Filters
  searchQuery = '';
  selectedProgram = '';
  selectedDepartment = '';
  selectedClass = '';
  selectedDay = '';
  selectedSemester = '';
  
  // Filter options
  programs: any[] = [];
  departments: any[] = [];
  classes: any[] = [];
  daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  semesters = [1, 2, 3, 4, 5, 6, 7, 8];

  // Pagination
  currentPage = 1;
  itemsPerPage = 10;
  totalItems = 0;

  constructor(
    private timetableService: TimetableService,
    private router: Router,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadTimetables();
  }

  loadTimetables(): void {
    this.loading = true;
    this.timetableService.getAllTimetableEntries().subscribe({
      next: (response) => {
        if (response.success) {
          this.timetables = response.timetables || [];
          this.filteredTimetables = [...this.timetables];
          this.totalItems = this.timetables.length;
          this.extractFilterOptions();
          this.applyFilters();
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading timetables:', error);
        this.showError('Failed to load timetables');
        this.loading = false;
      }
    });
  }

  extractFilterOptions(): void {
    // Extract unique programs, departments, and classes
    const programsSet = new Set();
    const departmentsSet = new Set();
    const classesSet = new Set();

    this.timetables.forEach(timetable => {
      if (timetable.program) {
        programsSet.add(JSON.stringify({
          _id: timetable.program._id,
          name: timetable.program.name
        }));
      }
      if (timetable.department) {
        departmentsSet.add(JSON.stringify({
          _id: timetable.department._id,
          name: timetable.department.name
        }));
      }
      if (timetable.class) {
        classesSet.add(JSON.stringify({
          _id: timetable.class._id,
          className: timetable.class.className,
          section: timetable.class.section
        }));
      }
    });

    this.programs = Array.from(programsSet).map(p => JSON.parse(p as string));
    this.departments = Array.from(departmentsSet).map(d => JSON.parse(d as string));
    this.classes = Array.from(classesSet).map(c => JSON.parse(c as string));
  }

  applyFilters(): void {
    let filtered = [...this.timetables];

    // Apply search filter
    if (this.searchQuery.trim()) {
      const query = this.searchQuery.toLowerCase();
      filtered = filtered.filter(timetable =>
        timetable.subject?.subjectName?.toLowerCase().includes(query) ||
        timetable.teacher?.name?.toLowerCase().includes(query) ||
        timetable.program?.name?.toLowerCase().includes(query) ||
        timetable.department?.name?.toLowerCase().includes(query) ||
        timetable.class?.className?.toLowerCase().includes(query) ||
        timetable.room?.toLowerCase().includes(query)
      );
    }

    // Apply other filters
    if (this.selectedProgram) {
      filtered = filtered.filter(t => t.program?._id === this.selectedProgram);
    }
    if (this.selectedDepartment) {
      filtered = filtered.filter(t => t.department?._id === this.selectedDepartment);
    }
    if (this.selectedClass) {
      filtered = filtered.filter(t => t.class?._id === this.selectedClass);
    }
    if (this.selectedDay) {
      filtered = filtered.filter(t => t.dayOfWeek === this.selectedDay);
    }
    if (this.selectedSemester) {
      filtered = filtered.filter(t => t.semester === parseInt(this.selectedSemester));
    }

    this.filteredTimetables = filtered;
    this.totalItems = filtered.length;
    this.currentPage = 1; // Reset to first page when filtering
  }

  clearFilters(): void {
    this.searchQuery = '';
    this.selectedProgram = '';
    this.selectedDepartment = '';
    this.selectedClass = '';
    this.selectedDay = '';
    this.selectedSemester = '';
    this.applyFilters();
  }

  // Navigation methods
  createTimetable(): void {
    this.router.navigate(['/dashboard/admin/timetable/create']);
  }

  editTimetable(timetable: TimetableEntry): void {
    this.router.navigate(['/dashboard/admin/timetable/edit', timetable._id]);
  }

  viewTimetable(timetable: TimetableEntry): void {
    this.router.navigate(['/dashboard/admin/timetable/view', timetable._id]);
  }

  deleteTimetable(timetable: TimetableEntry): void {
    Swal.fire({
      title: 'Are you sure?',
      text: `Delete timetable entry for ${timetable.subject?.subjectName} on ${timetable.dayOfWeek}?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (result.isConfirmed) {
        this.performDelete(timetable);
      }
    });
  }

  private performDelete(timetable: TimetableEntry): void {
    this.timetableService.deleteTimetableEntry(timetable._id).subscribe({
      next: (response) => {
        if (response.success) {
          Swal.fire('Deleted!', 'Timetable entry has been deleted.', 'success');
          this.loadTimetables(); // Reload the list
        }
      },
      error: (error) => {
        console.error('Error deleting timetable:', error);
        Swal.fire('Error', 'Failed to delete timetable entry.', 'error');
      }
    });
  }

  toggleActive(timetable: TimetableEntry): void {
    const newStatus = !timetable.isActive;
    this.timetableService.updateTimetableStatus(timetable._id, newStatus).subscribe({
      next: (response) => {
        if (response.success) {
          timetable.isActive = newStatus;
          this.showSuccess(`Timetable entry ${newStatus ? 'activated' : 'deactivated'}`);
        }
      },
      error: (error) => {
        console.error('Error updating timetable status:', error);
        this.showError('Failed to update timetable status');
      }
    });
  }

  // Pagination methods
  get paginatedTimetables(): TimetableEntry[] {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    return this.filteredTimetables.slice(startIndex, startIndex + this.itemsPerPage);
  }

  get totalPages(): number {
    return Math.ceil(this.totalItems / this.itemsPerPage);
  }

  nextPage(): void {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
    }
  }

  previousPage(): void {
    if (this.currentPage > 1) {
      this.currentPage--;
    }
  }

  goToPage(page: number): void {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
    }
  }

  // Utility methods
  getClassDisplayName(classObj: any): string {
    if (!classObj) return 'N/A';
    return `${classObj.className}${classObj.section ? ' - ' + classObj.section : ''}`;
  }

  getTimeDisplay(timeSlot: any): string {
    if (!timeSlot) return 'N/A';
    return `${timeSlot.startTime} - ${timeSlot.endTime}`;
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }
}
