import { Component, Inject, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ProgramService } from 'src/app/services/program.service';
import { Program } from 'src/app/models/user';

@Component({
  selector: 'app-program-dialog',
  templateUrl: './program-dialog.component.html',
  styleUrls: ['./program-dialog.component.css']
})
export class ProgramDialogComponent implements OnInit {
  programForm: FormGroup;
  isLoading = false;
  isEditMode = false;

  constructor(
    private fb: FormBuilder,
    private programService: ProgramService,
    public dialogRef: MatDialogRef<ProgramDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { program?: Program }
  ) {
    this.isEditMode = !!data?.program;
    
    this.programForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      fullName: ['', [Validators.required, Validators.minLength(5)]],
      description: [''],
      duration: [1, [Validators.required, Validators.min(1), Validators.max(10)]],
      durationUnit: ['years', Validators.required],
      totalSemesters: [2, [Validators.required, Validators.min(1), Validators.max(20)]],
      isActive: [true]
    });
  }

  ngOnInit(): void {
    if (this.isEditMode && this.data.program) {
      this.programForm.patchValue(this.data.program);
    }

    // Set up form value changes to handle program type logic
    this.programForm.get('name')?.valueChanges.subscribe(programName => {
      this.handleProgramTypeChange(programName);
    });
  }

  handleProgramTypeChange(programName: string): void {
    if (programName === 'Intermediate') {
      // For Intermediate programs: 2 years, no semesters
      this.programForm.patchValue({
        duration: 2,
        durationUnit: 'years',
        totalSemesters: 2 // 2 years = 2 academic years
      });
    } else if (programName === 'BS') {
      // For BS programs: 4 years, 8 semesters
      this.programForm.patchValue({
        duration: 4,
        durationUnit: 'years',
        totalSemesters: 8
      });
    } else if (programName === 'MS') {
      // For MS programs: 2 years, 4 semesters
      this.programForm.patchValue({
        duration: 2,
        durationUnit: 'years',
        totalSemesters: 4
      });
    } else if (programName === 'PhD') {
      // For PhD programs: 3-5 years, 6-10 semesters
      this.programForm.patchValue({
        duration: 4,
        durationUnit: 'years',
        totalSemesters: 8
      });
    }
  }

  isIntermediateProgram(): boolean {
    return this.programForm.get('name')?.value === 'Intermediate';
  }

  onSubmit(): void {
    if (this.programForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.isLoading = true;
    const programData = this.programForm.value;

    const request = this.isEditMode
      ? this.programService.updateProgram(this.data.program!._id, programData)
      : this.programService.createProgram(programData);

    request.subscribe({
      next: (response) => {
        if (response.success) {
          this.dialogRef.close({ success: true, program: response.program });
        } else {
          this.dialogRef.close({ success: false, error: response.message });
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error saving program:', error);
        this.dialogRef.close({ success: false, error: 'Failed to save program' });
        this.isLoading = false;
      }
    });
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  private markFormGroupTouched(): void {
    Object.keys(this.programForm.controls).forEach(key => {
      const control = this.programForm.get(key);
      control?.markAsTouched();
    });
  }

  getErrorMessage(fieldName: string): string {
    const control = this.programForm.get(fieldName);
    if (control?.hasError('required')) {
      return `${fieldName} is required`;
    }
    if (control?.hasError('minlength')) {
      const minLength = control.errors?.['minlength']?.requiredLength;
      return `${fieldName} must be at least ${minLength} characters`;
    }
    if (control?.hasError('min')) {
      const min = control.errors?.['min']?.min;
      return `${fieldName} must be at least ${min}`;
    }
    if (control?.hasError('max')) {
      const max = control.errors?.['max']?.max;
      return `${fieldName} must not exceed ${max}`;
    }
    return '';
  }
}
