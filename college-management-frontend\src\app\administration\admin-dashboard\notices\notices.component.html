<div class="notices-container">
  <!-- Compact Header -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">
        <mat-icon class="title-icon">campaign</mat-icon>
        Notice Management
      </h1>
      <p class="page-subtitle">Create and manage notices for students and teachers</p>
    </div>
    <div class="header-actions">
      <button mat-icon-button (click)="loadNotices()" matTooltip="Refresh" class="refresh-btn">
        <mat-icon>refresh</mat-icon>
      </button>
      <button mat-raised-button color="primary" (click)="toggleForm()" class="create-btn">
        <mat-icon>{{ showForm ? 'close' : 'add' }}</mat-icon>
        <span class="d-none d-md-inline">{{ showForm ? 'Cancel' : 'Create Notice' }}</span>
      </button>
    </div>
  </div>

  <!-- Filters -->
  <mat-card class="filters-card">
    <mat-card-content>
      <div class="filters-grid">
        <mat-form-field appearance="outline">
          <mat-label>Search</mat-label>
          <input matInput [(ngModel)]="searchQuery" placeholder="Search notices...">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Category</mat-label>
          <mat-select [(ngModel)]="filterCategory">
            <mat-option value="">All Categories</mat-option>
            <mat-option *ngFor="let category of categories" [value]="category">
              {{ category }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Priority</mat-label>
          <mat-select [(ngModel)]="filterPriority">
            <mat-option value="">All Priorities</mat-option>
            <mat-option *ngFor="let priority of priorities" [value]="priority">
              {{ priority }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Status</mat-label>
          <mat-select [(ngModel)]="filterStatus">
            <mat-option value="">All Status</mat-option>
            <mat-option value="Active">Active</mat-option>
            <mat-option value="Draft">Draft</mat-option>
            <mat-option value="Expired">Expired</mat-option>
            <mat-option value="Scheduled">Scheduled</mat-option>
          </mat-select>
        </mat-form-field>

        <div class="filter-actions">
          <button mat-button (click)="applyFilters()">Apply</button>
          <button mat-button (click)="clearFilters()">Clear</button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Create/Edit Form -->
  <mat-card *ngIf="showForm" class="form-card">
    <mat-card-header>
      <mat-card-title>{{ isEditing ? 'Edit Notice' : 'Create New Notice' }}</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <form [formGroup]="noticeForm" (ngSubmit)="onSubmit()">
        <div class="form-grid">
          <!-- Title -->
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Title *</mat-label>
            <input matInput formControlName="title" placeholder="Enter notice title">
            <mat-error *ngIf="noticeForm.get('title')?.hasError('required')">
              Title is required
            </mat-error>
            <mat-error *ngIf="noticeForm.get('title')?.hasError('minlength')">
              Title must be at least 3 characters
            </mat-error>
          </mat-form-field>

          <!-- Content -->
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Content *</mat-label>
            <textarea matInput formControlName="content" rows="4" placeholder="Enter notice content"></textarea>
            <mat-error *ngIf="noticeForm.get('content')?.hasError('required')">
              Content is required
            </mat-error>
            <mat-error *ngIf="noticeForm.get('content')?.hasError('minlength')">
              Content must be at least 10 characters
            </mat-error>
          </mat-form-field>

          <!-- Category and Priority -->
          <mat-form-field appearance="outline">
            <mat-label>Category</mat-label>
            <mat-select formControlName="category">
              <mat-option *ngFor="let category of categories" [value]="category">
                {{ category }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Priority</mat-label>
            <mat-select formControlName="priority">
              <mat-option *ngFor="let priority of priorities" [value]="priority">
                {{ priority }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <!-- Target Audience -->
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Target Audience</mat-label>
            <mat-select formControlName="targetAudience" multiple>
              <mat-option *ngFor="let audience of audiences" [value]="audience">
                {{ audience }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <!-- Target Programs -->
          <mat-form-field appearance="outline">
            <mat-label>Target Programs</mat-label>
            <mat-select formControlName="targetPrograms" multiple>
              <mat-option *ngFor="let program of programs" [value]="program._id">
                {{ program.name }} - {{ program.fullName }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <!-- Target Departments -->
          <mat-form-field appearance="outline">
            <mat-label>Target Departments</mat-label>
            <mat-select formControlName="targetDepartments" multiple>
              <mat-option *ngFor="let department of departments" [value]="department._id">
                {{ department.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <!-- Publish Date -->
          <mat-form-field appearance="outline">
            <mat-label>Publish Date</mat-label>
            <input matInput [matDatepicker]="publishPicker" formControlName="publishDate">
            <mat-datepicker-toggle matSuffix [for]="publishPicker"></mat-datepicker-toggle>
            <mat-datepicker #publishPicker></mat-datepicker>
          </mat-form-field>

          <!-- Expiry Date -->
          <mat-form-field appearance="outline">
            <mat-label>Expiry Date (Optional)</mat-label>
            <input matInput [matDatepicker]="expiryPicker" formControlName="expiryDate">
            <mat-datepicker-toggle matSuffix [for]="expiryPicker"></mat-datepicker-toggle>
            <mat-datepicker #expiryPicker></mat-datepicker>
          </mat-form-field>

          <!-- Checkboxes -->
          <div class="checkbox-group">
            <mat-checkbox formControlName="isPublished">Publish Immediately</mat-checkbox>
            <mat-checkbox formControlName="isPinned">Pin Notice</mat-checkbox>
          </div>
        </div>

        <div class="form-actions">
          <button mat-button type="button" (click)="resetForm()">Cancel</button>
          <button mat-raised-button color="primary" type="submit" 
                  [disabled]="!noticeForm.valid || submitting">
            {{ submitting ? 'Saving...' : (isEditing ? 'Update' : 'Create') }}
          </button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>

  <!-- Loading State -->
  <div *ngIf="loading" class="loading-container">
    <mat-spinner></mat-spinner>
    <p>Loading notices...</p>
  </div>

  <!-- Notices List -->
  <div *ngIf="!loading" class="notices-list">
    <mat-card *ngFor="let notice of filteredNotices" class="notice-card">
      <mat-card-header>
        <div class="notice-header">
          <div class="notice-title-section">
            <h3>{{ notice.title }}</h3>
            <div class="notice-badges">
              <span class="badge category-badge">{{ notice.category }}</span>
              <span class="badge priority-badge" [ngClass]="getPriorityClass(notice.priority)">
                {{ notice.priority }}
              </span>
              <span class="badge status-badge" [ngClass]="getStatusClass(notice.status ?? '')">
                {{ notice.status }}
              </span>
              <span *ngIf="notice.isPinned" class="badge pinned-badge">
                <mat-icon>push_pin</mat-icon> Pinned
              </span>
            </div>
          </div>
          <div class="notice-actions">
            <button mat-icon-button (click)="editNotice(notice)" matTooltip="Edit">
              <mat-icon>edit</mat-icon>
            </button>
            <button mat-icon-button (click)="togglePublish(notice)" 
                    [matTooltip]="notice.isPublished ? 'Unpublish' : 'Publish'">
              <mat-icon>{{ notice.isPublished ? 'visibility_off' : 'visibility' }}</mat-icon>
            </button>
            <button mat-icon-button (click)="togglePin(notice)" 
                    [matTooltip]="notice.isPinned ? 'Unpin' : 'Pin'">
              <mat-icon>{{ notice.isPinned ? 'push_pin' : 'push_pin' }}</mat-icon>
            </button>
            <button mat-icon-button (click)="deleteNotice(notice)" matTooltip="Delete" color="warn">
              <mat-icon>delete</mat-icon>
            </button>
          </div>
        </div>
      </mat-card-header>
      <mat-card-content>
        <p class="notice-content">{{ notice.content }}</p>
        <div class="notice-meta">
          <div class="meta-item">
            <mat-icon>people</mat-icon>
            <span>{{ notice.targetAudience.join(', ') }}</span>
          </div>
          <div class="meta-item">
            <mat-icon>calendar_today</mat-icon>
            <span>{{ formatDate(notice.publishDate) }}</span>
          </div>
          <div class="meta-item" *ngIf="notice.expiryDate">
            <mat-icon>event_busy</mat-icon>
            <span>Expires: {{ formatDate(notice.expiryDate) }}</span>
          </div>
          <div class="meta-item">
            <mat-icon>visibility</mat-icon>
            <span>{{ notice.views }} views</span>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Empty State -->
    <div *ngIf="filteredNotices.length === 0" class="empty-state">
      <mat-icon>notifications_none</mat-icon>
      <h3>No notices found</h3>
      <p>Create your first notice to get started.</p>
      <button mat-raised-button color="primary" (click)="showForm = true">
        <mat-icon>add</mat-icon>
        Create Notice
      </button>
    </div>
  </div>
</div>
