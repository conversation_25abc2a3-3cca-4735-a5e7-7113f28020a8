{"ast": null, "code": "import Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/classes.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/checkbox\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/forms\";\nfunction ClassTableComponent_tr_33_li_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const subject_r3 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(subject_r3.subjectName);\n  }\n}\nfunction ClassTableComponent_tr_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 19)(2, \"mat-checkbox\", 20);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\", 21);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\", 22);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\")(9, \"ul\");\n    i0.ɵɵtemplate(10, ClassTableComponent_tr_33_li_10_Template, 2, 1, \"li\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"td\", 23)(12, \"div\", 24)(13, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function ClassTableComponent_tr_33_Template_button_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r5);\n      const class_r1 = restoredCtx.$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.deleteClass(class_r1._id));\n    });\n    i0.ɵɵelementStart(14, \"mat-icon\");\n    i0.ɵɵtext(15, \"delete\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function ClassTableComponent_tr_33_Template_button_click_16_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r5);\n      const class_r1 = restoredCtx.$implicit;\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.navigateToEdit(class_r1._id));\n    });\n    i0.ɵɵelementStart(17, \"mat-icon\");\n    i0.ɵɵtext(18, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function ClassTableComponent_tr_33_Template_button_click_19_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r5);\n      const class_r1 = restoredCtx.$implicit;\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.GoTOStudentClass(class_r1._id));\n    });\n    i0.ɵɵelementStart(20, \"mat-icon\");\n    i0.ɵɵtext(21, \"remove_red_eye\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const class_r1 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(class_r1.className);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(class_r1.section);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(class_r1.department);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", class_r1.subjects);\n  }\n}\nexport let ClassTableComponent = /*#__PURE__*/(() => {\n  class ClassTableComponent {\n    constructor(classService, router) {\n      this.classService = classService;\n      this.router = router;\n      this.currentPage = 1;\n      this.itemsPerPage = 5;\n      this.searchQuery = '';\n      this.classes = []; // Holds the classes data\n      this.isChecked = false;\n      this.isIndeterminate = true;\n    }\n    ngOnInit() {\n      this.getClasses(); // Fetch classes when component initializes\n    }\n    // Fetch all classes from the API\n    getClasses() {\n      this.classService.getClasses().subscribe(response => {\n        if (response.success) {\n          this.classes = response.classes;\n          console.log(\"this is clases\", this.classes);\n        }\n      }, error => {\n        console.error('Error fetching classes:', error);\n      });\n    }\n    searchclass() {\n      if (this.searchQuery) {\n        this.classes = this.classes.filter(res => {\n          return res.className.toLowerCase().includes(this.searchQuery.toLowerCase()) || res.section.toLowerCase().includes(this.searchQuery);\n        });\n      } else {\n        this.classes = this.classes;\n      }\n      this.currentPage = 1;\n    }\n    get painatedclasses() {\n      const startIndex = (this.currentPage - 1) * this.itemsPerPage;\n      const endIndex = startIndex + this.itemsPerPage;\n      return this.classes.slice(startIndex, endIndex);\n    }\n    get totalPages() {\n      return Math.ceil(this.classes.length / this.itemsPerPage);\n    }\n    nextPage() {\n      if (this.currentPage < this.totalPages) {\n        this.currentPage++;\n      }\n    }\n    previousPage() {\n      if (this.currentPage > 1) {\n        this.currentPage--;\n      }\n    }\n    toggleCheckbox() {\n      if (this.isIndeterminate) {\n        this.isIndeterminate = false;\n        this.isChecked = true;\n      } else if (this.isChecked) {\n        this.isChecked = false;\n      } else {\n        this.isIndeterminate = true;\n      }\n    }\n    GoTOStudentClass(id) {\n      this.router.navigate(['/dashboard/admin/classes/student-by-class/' + '' + id]);\n    }\n    deleteClass(classId) {\n      Swal.fire({\n        title: 'Are you sure?',\n        text: 'This class will be permanently deleted!',\n        icon: 'warning',\n        showCancelButton: true,\n        confirmButtonColor: '#d33',\n        cancelButtonColor: '#3085d6',\n        confirmButtonText: 'Yes, delete it!'\n      }).then(result => {\n        if (result.isConfirmed) {\n          this.classService.deleteClass(classId).subscribe(res => {\n            Swal.fire('Deleted!', 'The class has been deleted.', 'success');\n            this.getClasses(); // Refresh list\n          }, err => {\n            Swal.fire('Error!', 'Something went wrong while deleting.', 'error');\n            console.error('Delete failed', err);\n          });\n        }\n      });\n    }\n    navigateToEdit(classId) {\n      this.router.navigate(['/dashboard/admin/classes/add-class', classId]);\n    }\n    static #_ = this.ɵfac = function ClassTableComponent_Factory(t) {\n      return new (t || ClassTableComponent)(i0.ɵɵdirectiveInject(i1.ClassesService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ClassTableComponent,\n      selectors: [[\"app-class-table\"]],\n      decls: 43,\n      vars: 8,\n      consts: [[1, \"maindiv\"], [1, \"secondarydiv\"], [1, \"my-3\", \"d-flex\", \"justify-content-between\", \"searchAndtab\"], [1, \"d-flex\", \"flex-wrap\", \"gap-3\"], [\"routerLink\", \"/dashboard/admin/classes/add-class\", 1, \"btn\", \"btn-top\", \"border\", \"d-flex\", \"align-items-center\"], [1, \"search-container\"], [1, \"search-input-wrapper\"], [\"type\", \"text\", \"placeholder\", \"Search\", 1, \"search-input\", 3, \"ngModel\", \"input\", \"ngModelChange\"], [1, \"search-icon\"], [1, \"table-container\", \"mb-4\"], [1, \"table-responsive\"], [1, \"tablehead\"], [1, \"one\"], [\"color\", \"primary\", 3, \"ngModel\", \"indeterminate\", \"ngModelChange\", \"change\"], [1, \"two\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"p-3\", \"align-items-center\", \"justify-content-between\"], [1, \"btn\", \"btn-top\", \"border\", 3, \"disabled\", \"click\"], [1, \"page\"], [\"data-label\", \"Class Name\"], [\"color\", \"primary\"], [\"data-label\", \"Section\"], [\"data-label\", \"Department\"], [\"data-label\", \"Actions\"], [1, \"d-flex\", \"gap-2\"], [1, \"btn\", \"btndelete\", 3, \"click\"], [1, \"btn\", \"btnedit\", 3, \"click\"]],\n      template: function ClassTableComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"button\", 4)(5, \"mat-icon\");\n          i0.ɵɵtext(6, \"add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(7, \"\\u00A0 Add New Class\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\")(9, \"div\", 5)(10, \"div\", 6)(11, \"input\", 7);\n          i0.ɵɵlistener(\"input\", function ClassTableComponent_Template_input_input_11_listener() {\n            return ctx.searchclass();\n          })(\"ngModelChange\", function ClassTableComponent_Template_input_ngModelChange_11_listener($event) {\n            return ctx.searchQuery = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"mat-icon\", 8);\n          i0.ɵɵtext(13, \"search\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(14, \"div\", 9)(15, \"div\", 10)(16, \"table\")(17, \"thead\")(18, \"tr\", 11)(19, \"th\", 12)(20, \"mat-checkbox\", 13);\n          i0.ɵɵlistener(\"ngModelChange\", function ClassTableComponent_Template_mat_checkbox_ngModelChange_20_listener($event) {\n            return ctx.isChecked = $event;\n          })(\"change\", function ClassTableComponent_Template_mat_checkbox_change_20_listener() {\n            return ctx.toggleCheckbox();\n          });\n          i0.ɵɵtext(21, \" Class Name \");\n          i0.ɵɵelementStart(22, \"mat-icon\");\n          i0.ɵɵtext(23, \"arrow_downward\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(24, \"th\");\n          i0.ɵɵtext(25, \"Section\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"th\");\n          i0.ɵɵtext(27, \"Department\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"th\");\n          i0.ɵɵtext(29, \"Subjects\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"th\", 14);\n          i0.ɵɵtext(31, \"Actions\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(32, \"tbody\");\n          i0.ɵɵtemplate(33, ClassTableComponent_tr_33_Template, 22, 4, \"tr\", 15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(34, \"div\", 16)(35, \"div\")(36, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function ClassTableComponent_Template_button_click_36_listener() {\n            return ctx.previousPage();\n          });\n          i0.ɵɵtext(37, \"Previous\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"div\", 18);\n          i0.ɵɵtext(39);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"div\")(41, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function ClassTableComponent_Template_button_click_41_listener() {\n            return ctx.nextPage();\n          });\n          i0.ɵɵtext(42, \"Next\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngModel\", ctx.isChecked)(\"indeterminate\", ctx.isIndeterminate);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngForOf\", ctx.painatedclasses);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.currentPage === 1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate2(\"Page \", ctx.currentPage, \" of \", ctx.totalPages, \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.currentPage === ctx.totalPages);\n        }\n      },\n      dependencies: [i3.NgForOf, i2.RouterLink, i4.MatCheckbox, i5.MatIcon, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgModel],\n      styles: [\".students[_ngcontent-%COMP%]{background-color:#f5fbff;color:#00a8d1;font-size:14px;font-weight:600;line-height:20px}\"]\n    });\n  }\n  return ClassTableComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}