const mongoose = require('mongoose');

const complaintSchema = new mongoose.Schema({
    title: {
        type: String,
        required: [true, 'Complaint title is required'],
        trim: true
    },
    description: {
        type: String,
        required: [true, 'Complaint description is required'],
        trim: true
    },
    complainant: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: [true, 'Complainant is required']
    },
    complainantRole: {
        type: String,
        enum: ['Student', 'Teacher', 'Principal'],
        required: [true, 'Complainant role is required']
    },
    category: {
        type: String,
        enum: ['Academic', 'Administrative', 'Infrastructure', 'Disciplinary', 'Other'],
        default: 'Other'
    },
    priority: {
        type: String,
        enum: ['Low', 'Medium', 'High', 'Critical'],
        default: 'Medium'
    },
    status: {
        type: String,
        enum: ['Pending', 'In Progress', 'Resolved', 'Closed', 'Rejected'],
        default: 'Pending'
    },
    assignedTo: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    },
    resolution: {
        type: String,
        trim: true
    },
    resolvedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    },
    resolvedAt: {
        type: Date
    },
    attachments: [{
        filename: String,
        originalName: String,
        path: String,
        size: Number,
        mimetype: String
    }],
    comments: [{
        user: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
            required: true
        },
        comment: {
            type: String,
            required: true,
            trim: true
        },
        createdAt: {
            type: Date,
            default: Date.now
        }
    }],
    isActive: {
        type: Boolean,
        default: true
    }
}, { 
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Virtual for complaint age in days
complaintSchema.virtual('ageInDays').get(function() {
    return Math.floor((Date.now() - this.createdAt) / (1000 * 60 * 60 * 24));
});

// Index for better query performance
complaintSchema.index({ complainant: 1, status: 1 });
complaintSchema.index({ assignedTo: 1, status: 1 });
complaintSchema.index({ createdAt: -1 });

module.exports = mongoose.model('Complaint', complaintSchema);
