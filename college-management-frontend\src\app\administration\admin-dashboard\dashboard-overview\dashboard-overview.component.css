.dashboard-overview {
  padding: 20px;
  /* background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); */
  min-height: 100vh;
  animation: fadeIn 0.6s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  text-align: center;
}

.error-container mat-icon {
  font-size: 48px;
  height: 48px;
  width: 48px;
  margin-bottom: 16px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.dashboard-header h1 {
  margin: 0;
  color: #333;
  font-weight: 500;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  transition: all 0.3s ease-in-out;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  background: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  transform: scaleX(0);
  transition: transform 0.3s ease-in-out;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stat-card:hover::before {
  transform: scaleX(1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  color: white;
}

.stat-card.students .stat-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card.teachers .stat-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card.programs .stat-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card.departments .stat-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-card.classes .stat-icon {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stat-card.subjects .stat-icon {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.stat-icon mat-icon {
  font-size: 28px;
  height: 28px;
  width: 28px;
}

.stat-info h3 {
  margin: 0;
  font-size: 2rem;
  font-weight: 600;
  color: #333;
}

.stat-info p {
  margin: 4px 0 0 0;
  color: #666;
  font-size: 0.9rem;
}

.charts-section {
  margin-bottom: 30px;
}

.chart-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.chart-card {
  height: 400px;
  max-height: 400px;
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  overflow: hidden;
}

.chart-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0,0,0,0.15);
}

.chart-card mat-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.chart-card mat-card-subtitle {
  color: #666;
  font-size: 0.9rem;
  margin-top: 4px;
}

.chart-container {
  height: 280px;
  max-height: 280px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.chart-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}

.chart-wrapper canvas {
  max-height: 100% !important;
  max-width: 100% !important;
}

.simple-chart {
  width: 100%;
}

.chart-item {
  display: grid;
  grid-template-columns: 1fr 2fr auto;
  gap: 12px;
  align-items: center;
  margin-bottom: 12px;
}

.chart-label {
  font-size: 0.9rem;
  color: #666;
}

.chart-bar {
  height: 20px;
  background-color: #e0e0e0;
  border-radius: 10px;
  overflow: hidden;
}

.chart-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px;
  transition: width 0.3s ease;
}

.chart-value {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

/* Enhanced Attendance Card Styles */
.attendance-card {
  /* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); */
  color: black;
  border-radius: 16px;
  overflow: hidden;
  position: relative;
}

.attendance-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  z-index: 0;
}

.attendance-card mat-card-header,
.attendance-card mat-card-content {
  position: relative;
  z-index: 1;
}



.attendance-summary {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  align-items: center;
  height: 100%;
}

.attendance-percentage {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.percentage-circle {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.percentage-value {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  width: 120px;
  height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.percentage-value::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    from 0deg,
    transparent 0deg,
    rgba(255, 255, 255, 0.1) 90deg,
    transparent 180deg
  );
  animation: rotate 3s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.percentage-value h2 {
  margin: 0;
  font-size: 2.2rem;
  font-weight: 700;
  position: relative;
  z-index: 1;
}

.percentage-value p {
  margin: 4px 0 0 0;
  font-size: 0.8rem;
  opacity: 0.9;
  position: relative;
  z-index: 1;
}

.attendance-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(4px);
}

.stat-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.stat-indicator.status-present {
  background: linear-gradient(135deg, #4CAF50, #45a049);
}

.stat-indicator.status-absent {
  background: linear-gradient(135deg, #F44336, #d32f2f);
}

.stat-indicator.status-late {
  background: linear-gradient(135deg, #FF9800, #f57c00);
}

.stat-indicator.status-excused {
  background: linear-gradient(135deg, #9C27B0, #7b1fa2);
}

.stat-label {
  flex: 1;
  font-weight: 500;
  color: white;
}

.stat-count {
  font-weight: 600;
  color: white;
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.9rem;
}

.attendance-breakdown {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.attendance-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.status-indicator.present {
  background-color: #4caf50;
}

.status-indicator.absent {
  background-color: #f44336;
}

.status-indicator.late {
  background-color: #ff9800;
}

.no-data {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.no-data mat-icon {
  font-size: 48px;
  height: 48px;
  width: 48px;
  opacity: 0.5;
}

.no-data p {
  margin: 0;
  font-size: 1rem;
}

.quick-actions {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  border: 1px solid rgba(0,0,0,0.05);
  transition: all 0.3s ease-in-out;
}

.quick-actions:hover {
  box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.quick-actions h2 {
  margin: 0 0 20px 0;
  color: #333;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.action-buttons button {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Enhanced responsive design */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (max-width: 768px) {
  .dashboard-overview {
    padding: 16px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .chart-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .chart-card {
    min-height: 350px;
  }

  .chart-container {
    height: 250px;
  }

  .attendance-summary {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .percentage-value {
    width: 100px;
    height: 100px;
  }

  .percentage-value h2 {
    font-size: 1.8rem;
  }

  .attendance-stats {
    margin-top: 16px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .action-buttons button {
    width: 100%;
    justify-content: center;
  }

  .dashboard-header h1 {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .dashboard-overview {
    padding: 12px;
  }

  .chart-card {
    min-height: 300px;
  }

  .chart-container {
    height: 200px;
  }

  .stat-content {
    gap: 12px;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
  }

  .stat-icon mat-icon {
    font-size: 24px;
    height: 24px;
    width: 24px;
  }

  .stat-info h3 {
    font-size: 1.5rem;
  }
}
