import { Component, OnInit } from '@angular/core';
import { DashboardService } from '../../../services/dashboard.service';
import { UserService } from '../../../services/user.service';
import { StudentDashboard } from '../../../models/dashboard';

@Component({
  selector: 'app-student-overview',
  templateUrl: './student-overview.component.html',
  styleUrls: ['./student-overview.component.css']
})
export class StudentOverviewComponent implements OnInit {
  studentDashboard: StudentDashboard | null = null;
  loading = true;
  error: string | null = null;
  currentUser: any;
  currentDate = new Date();

  constructor(
    private dashboardService: DashboardService,
    private userService: UserService
  ) {}

  ngOnInit(): void {
    this.currentUser = this.userService.getUserFromLocalStorage()?.user;
    if (this.currentUser) {
      this.loadStudentDashboard();
    } else {
      this.error = 'User not found';
      this.loading = false;
    }
  }

  loadStudentDashboard(): void {
    this.loading = true;
    this.error = null;

    this.dashboardService.getStudentDashboard(this.currentUser._id).subscribe({
      next: (response) => {
        if (response.success) {
          this.studentDashboard = response.dashboard;
        } else {
          this.error = 'Failed to load student dashboard';
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading student dashboard:', error);
        this.error = 'Error loading dashboard data';
        this.loading = false;
      }
    });
  }

  refreshData(): void {
    this.loadStudentDashboard();
  }

  getTodayClasses(): any[] {
    if (!this.studentDashboard?.timetable) return [];
    
    const today = new Date().toLocaleDateString('en-US', { weekday: 'long' });
    return this.studentDashboard.timetable.filter(entry => entry.dayOfWeek === today);
  }

  getUpcomingClass(): any {
    const todayClasses = this.getTodayClasses();
    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();

    return todayClasses.find(classItem => {
      const [hours, minutes] = classItem.timeSlot.startTime.split(':').map(Number);
      const classTime = hours * 60 + minutes;
      return classTime > currentTime;
    });
  }

  getOverallAttendancePercentage(): number {
    if (!this.studentDashboard?.attendanceStats.length) return 0;
    
    const totalAttendance = this.studentDashboard.attendanceStats.reduce((sum, stat) => sum + stat.count, 0);
    const presentCount = this.studentDashboard.attendanceStats.find(stat => stat._id === 'present')?.count || 0;
    
    return totalAttendance > 0 ? Math.round((presentCount / totalAttendance) * 100) : 0;
  }

  getTotalClasses(): number {
    if (!this.studentDashboard?.attendanceStats.length) return 0;
    return this.studentDashboard.attendanceStats.reduce((sum, stat) => sum + stat.count, 0);
  }

  getPresentClasses(): number {
    if (!this.studentDashboard?.attendanceStats.length) return 0;
    return this.studentDashboard.attendanceStats.find(stat => stat._id === 'present')?.count || 0;
  }

  getAbsentClasses(): number {
    if (!this.studentDashboard?.attendanceStats.length) return 0;
    return this.studentDashboard.attendanceStats.find(stat => stat._id === 'absent')?.count || 0;
  }
}
