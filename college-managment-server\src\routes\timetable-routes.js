const express = require('express');
const router = express.Router();

const {
    createTimetableEntry,
    getAllTimetableEntries,
    getTimetableByTeacher,
    getTimetableByClass,
    getTimetableByStudent,
    updateTimetableEntry,
    deleteTimetableEntry,
    getTimetableEntryById
} = require('../controllers/timetable-controller.js');

// Routes

// Create a new timetable entry
router.post('/timetable', createTimetableEntry);

// Get all timetable entries
router.get('/timetables', getAllTimetableEntries);

// Get timetable by teacher
router.get('/timetable/teacher/:teacherId', getTimetableByTeacher);

// Get timetable by class
router.get('/timetable/class/:classId', getTimetableByClass);

// Get timetable by student
router.get('/timetable/student/:studentId', getTimetableByStudent);

// Get timetable entry by ID
router.get('/timetable/:id', getTimetableEntryById);

// Update timetable entry
router.put('/timetable/:id', updateTimetableEntry);

// Delete timetable entry
router.delete('/timetable/:id', deleteTimetableEntry);

module.exports = router;
