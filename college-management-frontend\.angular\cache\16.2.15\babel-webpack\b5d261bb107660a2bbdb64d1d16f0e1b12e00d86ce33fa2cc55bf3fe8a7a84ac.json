{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { AdministrationRoutingModule } from './administration-routing.module';\nimport { MainComponent } from './main/main.component';\nimport { SidebarComponent } from './sidebar/sidebar.component';\nimport { HeaderComponent } from './header/header.component';\nimport { MaterialModule } from '../material';\nimport { ChartsComponent } from './charts/charts.component';\nimport { ProfileComponent } from './profile/profile.component';\nimport * as i0 from \"@angular/core\";\nexport class AdministrationModule {\n  static #_ = this.ɵfac = function AdministrationModule_Factory(t) {\n    return new (t || AdministrationModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AdministrationModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, AdministrationRoutingModule, MaterialModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AdministrationModule, {\n    declarations: [MainComponent, SidebarComponent, HeaderComponent, ChartsComponent, ProfileComponent],\n    imports: [CommonModule, AdministrationRoutingModule, MaterialModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "AdministrationRoutingModule", "MainComponent", "SidebarComponent", "HeaderComponent", "MaterialModule", "ChartsComponent", "ProfileComponent", "AdministrationModule", "_", "_2", "_3", "declarations", "imports"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\administration.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { AdministrationRoutingModule } from './administration-routing.module';\r\nimport { MainComponent } from './main/main.component';\r\nimport { SidebarComponent } from './sidebar/sidebar.component';\r\nimport { HeaderComponent } from './header/header.component';\r\nimport { MaterialModule } from '../material';\r\nimport { ChartsComponent } from './charts/charts.component';\r\nimport { ProfileComponent } from './profile/profile.component';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    MainComponent,\r\n    SidebarComponent,\r\n    HeaderComponent,\r\n    ChartsComponent,\r\n    ProfileComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    AdministrationRoutingModule,\r\n    MaterialModule\r\n  ]\r\n})\r\nexport class AdministrationModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,2BAA2B,QAAQ,iCAAiC;AAC7E,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,cAAc,QAAQ,aAAa;AAC5C,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,gBAAgB,QAAQ,6BAA6B;;AAiB9D,OAAM,MAAOC,oBAAoB;EAAA,QAAAC,CAAA,G;qBAApBD,oBAAoB;EAAA;EAAA,QAAAE,EAAA,G;UAApBF;EAAoB;EAAA,QAAAG,EAAA,G;cAL7BX,YAAY,EACZC,2BAA2B,EAC3BI,cAAc;EAAA;;;2EAGLG,oBAAoB;IAAAI,YAAA,GAZ7BV,aAAa,EACbC,gBAAgB,EAChBC,eAAe,EACfE,eAAe,EACfC,gBAAgB;IAAAM,OAAA,GAGhBb,YAAY,EACZC,2BAA2B,EAC3BI,cAAc;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}