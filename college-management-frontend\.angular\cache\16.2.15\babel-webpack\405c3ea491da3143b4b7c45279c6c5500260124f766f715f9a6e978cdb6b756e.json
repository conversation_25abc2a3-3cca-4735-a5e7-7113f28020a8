{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MaterialModule } from 'src/app/material';\nimport { TeacherDashboardRoutingModule } from './teacher-dashboard-routing.module';\nimport { TeacherOverviewComponent } from './teacher-overview/teacher-overview.component';\nimport { TeacherAttendnaceComponent } from './teacher-attendnace/teacher-attendnace.component';\nimport { TeacherSubjectsComponent } from './teacher-subjects/teacher-subjects.component';\nimport { TeacherStudentsComponent } from './teacher-students/teacher-students.component';\nimport { TeacherAllStudentsComponent } from './teacher-all-students/teacher-all-students.component';\nimport { TeacherClassesComponent } from './teacher-classes/teacher-classes.component';\nimport { StudentDetailComponent } from './student-detail/student-detail.component';\nimport { TeacherNoticeComponent } from './teacher-notice/teacher-notice.component';\nimport { TeacherComplaintComponent } from './teacher-complaint/teacher-complaint.component';\nimport { TeacherTimetableComponent } from './teacher-timetable/teacher-timetable.component';\nimport * as i0 from \"@angular/core\";\nexport class TeacherDashboardModule {\n  static #_ = this.ɵfac = function TeacherDashboardModule_Factory(t) {\n    return new (t || TeacherDashboardModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: TeacherDashboardModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, TeacherDashboardRoutingModule, MaterialModule, FormsModule, ReactiveFormsModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(TeacherDashboardModule, {\n    declarations: [TeacherOverviewComponent, TeacherAttendnaceComponent, TeacherSubjectsComponent, TeacherStudentsComponent, TeacherAllStudentsComponent, TeacherClassesComponent, StudentDetailComponent, TeacherNoticeComponent, TeacherComplaintComponent, TeacherTimetableComponent],\n    imports: [CommonModule, TeacherDashboardRoutingModule, MaterialModule, FormsModule, ReactiveFormsModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "MaterialModule", "TeacherDashboardRoutingModule", "TeacherOverviewComponent", "TeacherAttendnaceComponent", "TeacherSubjectsComponent", "TeacherStudentsComponent", "TeacherAllStudentsComponent", "TeacherClassesComponent", "StudentDetailComponent", "TeacherNoticeComponent", "TeacherComplaintComponent", "TeacherTimetableComponent", "TeacherDashboardModule", "_", "_2", "_3", "declarations", "imports"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\teacher-dashboard\\teacher-dashboard.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { MaterialModule } from 'src/app/material';\r\nimport { TeacherDashboardRoutingModule } from './teacher-dashboard-routing.module';\r\nimport { TeacherOverviewComponent } from './teacher-overview/teacher-overview.component';\r\nimport { TeacherAttendnaceComponent } from './teacher-attendnace/teacher-attendnace.component';\r\nimport { TeacherSubjectsComponent } from './teacher-subjects/teacher-subjects.component';\r\nimport { TeacherStudentsComponent } from './teacher-students/teacher-students.component';\r\nimport { TeacherAllStudentsComponent } from './teacher-all-students/teacher-all-students.component';\r\nimport { TeacherClassesComponent } from './teacher-classes/teacher-classes.component';\r\nimport { StudentDetailComponent } from './student-detail/student-detail.component';\r\nimport { TeacherNoticeComponent } from './teacher-notice/teacher-notice.component';\r\nimport { TeacherComplaintComponent } from './teacher-complaint/teacher-complaint.component';\r\nimport { TeacherTimetableComponent } from './teacher-timetable/teacher-timetable.component';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    TeacherOverviewComponent,\r\n    TeacherAttendnaceComponent,\r\n    TeacherSubjectsComponent,\r\n    TeacherStudentsComponent,\r\n    TeacherAllStudentsComponent,\r\n    TeacherClassesComponent,\r\n    StudentDetailComponent,\r\n    TeacherNoticeComponent,\r\n    TeacherComplaintComponent,\r\n    TeacherTimetableComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    TeacherDashboardRoutingModule,\r\n    MaterialModule,\r\n    FormsModule,\r\n    ReactiveFormsModule\r\n  ]\r\n})\r\nexport class TeacherDashboardModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,6BAA6B,QAAQ,oCAAoC;AAClF,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,0BAA0B,QAAQ,mDAAmD;AAC9F,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,2BAA2B,QAAQ,uDAAuD;AACnG,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,yBAAyB,QAAQ,iDAAiD;AAC3F,SAASC,yBAAyB,QAAQ,iDAAiD;;AAuB3F,OAAM,MAAOC,sBAAsB;EAAA,QAAAC,CAAA,G;qBAAtBD,sBAAsB;EAAA;EAAA,QAAAE,EAAA,G;UAAtBF;EAAsB;EAAA,QAAAG,EAAA,G;cAP/BlB,YAAY,EACZI,6BAA6B,EAC7BD,cAAc,EACdF,WAAW,EACXC,mBAAmB;EAAA;;;2EAGVa,sBAAsB;IAAAI,YAAA,GAnB/Bd,wBAAwB,EACxBC,0BAA0B,EAC1BC,wBAAwB,EACxBC,wBAAwB,EACxBC,2BAA2B,EAC3BC,uBAAuB,EACvBC,sBAAsB,EACtBC,sBAAsB,EACtBC,yBAAyB,EACzBC,yBAAyB;IAAAM,OAAA,GAGzBpB,YAAY,EACZI,6BAA6B,EAC7BD,cAAc,EACdF,WAAW,EACXC,mBAAmB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}