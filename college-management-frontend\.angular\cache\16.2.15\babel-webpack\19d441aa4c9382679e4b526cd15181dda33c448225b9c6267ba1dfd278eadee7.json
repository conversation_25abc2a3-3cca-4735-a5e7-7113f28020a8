{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs';\nimport { UX_TEXT_STANDARDS } from 'src/app/shared/constants/ux-text-standards';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/role.service\";\nimport * as i2 from \"src/app/services/user.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/icon\";\nfunction SidebarComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"img\", 9);\n    i0.ɵɵelementStart(2, \"h3\");\n    i0.ɵɵtext(3, \"GPGC (Swabi)\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SidebarComponent_button_6_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r3.label);\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    exact: a0\n  };\n};\nfunction SidebarComponent_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 10)(1, \"mat-icon\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SidebarComponent_button_6_span_3_Template, 2, 1, \"span\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"routerLink\", item_r3.route)(\"routerLinkActiveOptions\", i0.ɵɵpureFunction1(4, _c0, item_r3.isExact));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r3.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isSidebarOpen);\n  }\n}\nfunction SidebarComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13);\n    i0.ɵɵelement(2, \"img\", 14);\n    i0.ɵɵelementStart(3, \"div\", 15)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_div_7_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.logout());\n    });\n    i0.ɵɵelement(9, \"i\", 17);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.profileName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.profileEmail);\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"fa-times\": a0,\n    \"fa-bars\": a1\n  };\n};\nexport class SidebarComponent {\n  constructor(roleService, userService, router) {\n    this.roleService = roleService;\n    this.userService = userService;\n    this.router = router;\n    this.isSidebarOpen = true;\n    this.toggle = new EventEmitter();\n    this.sidebarItems = [];\n    this.profileName = 'Muhammad Luqman';\n    this.profileEmail = '<EMAIL>';\n  }\n  ngOnInit() {\n    const role = this.roleService.getRole();\n    const user = this.userService.getUserFromLocalStorage().user;\n    this.profileName = user.name;\n    this.profileEmail = user.email;\n    // Configure sidebar items based on user role with user-friendly labels\n    if (role === 'Principal') {\n      this.sidebarItems = [{\n        label: UX_TEXT_STANDARDS.NAVIGATION.DASHBOARD,\n        icon: 'dashboard',\n        route: '/dashboard/admin/overview',\n        isExact: false\n      }, {\n        label: UX_TEXT_STANDARDS.NAVIGATION.PROGRAMS,\n        icon: 'library_books',\n        route: '/dashboard/admin/programs',\n        isExact: false\n      }, {\n        label: UX_TEXT_STANDARDS.NAVIGATION.DEPARTMENTS,\n        icon: 'business',\n        route: '/dashboard/admin/department',\n        isExact: false\n      }, {\n        label: UX_TEXT_STANDARDS.NAVIGATION.SUBJECTS,\n        icon: 'subject',\n        route: '/dashboard/admin/subjects',\n        isExact: false\n      }, {\n        label: UX_TEXT_STANDARDS.NAVIGATION.CLASSES,\n        icon: 'class',\n        route: '/dashboard/admin/classes',\n        isExact: false\n      }, {\n        label: UX_TEXT_STANDARDS.NAVIGATION.TIMETABLE,\n        icon: 'schedule',\n        route: '/dashboard/admin/timetable',\n        isExact: false\n      }, {\n        label: UX_TEXT_STANDARDS.NAVIGATION.TEACHERS,\n        icon: 'person',\n        route: '/dashboard/admin/teacher',\n        isExact: false\n      }, {\n        label: UX_TEXT_STANDARDS.NAVIGATION.STUDENTS,\n        icon: 'school',\n        route: '/dashboard/admin/students',\n        isExact: false\n      }, {\n        label: UX_TEXT_STANDARDS.NAVIGATION.ATTENDANCE,\n        icon: 'fact_check',\n        route: '/dashboard/admin/students/attendance',\n        isExact: false\n      }, {\n        label: UX_TEXT_STANDARDS.NAVIGATION.NOTICES,\n        icon: 'campaign',\n        route: '/dashboard/admin/notices',\n        isExact: false\n      }\n      // { label: 'Users', icon: 'manage_accounts', route: '/dashboard/admin/users', isExact: false },\n      ];\n    } else if (role === 'Teacher') {\n      this.sidebarItems = [{\n        label: UX_TEXT_STANDARDS.NAVIGATION.DASHBOARD,\n        icon: 'dashboard',\n        route: '/dashboard/teacher',\n        isExact: true\n      }, {\n        label: UX_TEXT_STANDARDS.NAVIGATION.MY_CLASSES,\n        icon: 'class',\n        route: '/dashboard/teacher/classes',\n        isExact: false\n      }, {\n        label: UX_TEXT_STANDARDS.NAVIGATION.MY_STUDENTS,\n        icon: 'school',\n        route: '/dashboard/teacher/students',\n        isExact: false\n      }, {\n        label: UX_TEXT_STANDARDS.NAVIGATION.ATTENDANCE,\n        icon: 'fact_check',\n        route: '/dashboard/teacher/attendance',\n        isExact: false\n      }, {\n        label: UX_TEXT_STANDARDS.NAVIGATION.MY_SUBJECTS,\n        icon: 'subject',\n        route: '/dashboard/teacher/subjects',\n        isExact: false\n      }, {\n        label: UX_TEXT_STANDARDS.NAVIGATION.NOTICES,\n        icon: 'campaign',\n        route: '/dashboard/teacher/notice',\n        isExact: false\n      }, {\n        label: UX_TEXT_STANDARDS.NAVIGATION.COMPLAINTS,\n        icon: 'chat',\n        route: '/dashboard/teacher/complaint',\n        isExact: false\n      }];\n    } else if (role === 'Student') {\n      this.sidebarItems = [{\n        label: UX_TEXT_STANDARDS.NAVIGATION.DASHBOARD,\n        icon: 'dashboard',\n        route: '/dashboard/student',\n        isExact: true\n      }, {\n        label: UX_TEXT_STANDARDS.NAVIGATION.MY_CLASSES,\n        icon: 'class',\n        route: '/dashboard/student/classes',\n        isExact: false\n      }, {\n        label: UX_TEXT_STANDARDS.NAVIGATION.MY_SUBJECTS,\n        icon: 'subject',\n        route: '/dashboard/student/subjects',\n        isExact: false\n      }, {\n        label: 'My Teachers',\n        icon: 'person',\n        route: '/dashboard/student/teachers',\n        isExact: false\n      }, {\n        label: UX_TEXT_STANDARDS.NAVIGATION.MY_ATTENDANCE,\n        icon: 'fact_check',\n        route: '/dashboard/student/attendance',\n        isExact: false\n      }, {\n        label: UX_TEXT_STANDARDS.NAVIGATION.TIMETABLE,\n        icon: 'schedule',\n        route: '/dashboard/student/timetable',\n        isExact: false\n      }];\n    }\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(() => {\n      if (window.innerWidth <= 992) {\n        this.toggleSidebar();\n      }\n    });\n  }\n  toggleSidebar() {\n    this.toggle.emit();\n  }\n  logout() {\n    this.roleService.logout();\n    this.router.navigate(['/auth']);\n  }\n  static #_ = this.ɵfac = function SidebarComponent_Factory(t) {\n    return new (t || SidebarComponent)(i0.ɵɵdirectiveInject(i1.RoleService), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.Router));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: SidebarComponent,\n    selectors: [[\"app-sidebar\"]],\n    inputs: {\n      isSidebarOpen: \"isSidebarOpen\"\n    },\n    outputs: {\n      toggle: \"toggle\"\n    },\n    decls: 8,\n    vars: 7,\n    consts: [[1, \"sidebar\"], [1, \"sidebar-header\"], [1, \"toggle-btn\", 3, \"click\"], [1, \"fas\", 3, \"ngClass\"], [\"class\", \"brand\", 4, \"ngIf\"], [1, \"sidebar-menu\"], [\"class\", \"menu-item\", \"routerLinkActive\", \"active\", 3, \"routerLink\", \"routerLinkActiveOptions\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"sidebar-footer\", 4, \"ngIf\"], [1, \"brand\"], [\"src\", \"../../../assets/images/logo.jpeg\", \"alt\", \"Logo\"], [\"routerLinkActive\", \"active\", 1, \"menu-item\", 3, \"routerLink\", \"routerLinkActiveOptions\"], [4, \"ngIf\"], [1, \"sidebar-footer\"], [1, \"user-profile\"], [\"src\", \"../../../assets/images/logo.jpeg\", \"alt\", \"Profile\"], [1, \"user-info\"], [1, \"logout-btn\", 3, \"click\"], [1, \"fas\", \"fa-sign-out-alt\"]],\n    template: function SidebarComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"button\", 2);\n        i0.ɵɵlistener(\"click\", function SidebarComponent_Template_button_click_2_listener() {\n          return ctx.toggleSidebar();\n        });\n        i0.ɵɵelement(3, \"i\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(4, SidebarComponent_div_4_Template, 4, 0, \"div\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"div\", 5);\n        i0.ɵɵtemplate(6, SidebarComponent_button_6_Template, 4, 6, \"button\", 6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(7, SidebarComponent_div_7_Template, 10, 2, \"div\", 7);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(4, _c1, ctx.isSidebarOpen, !ctx.isSidebarOpen));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isSidebarOpen);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.sidebarItems);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isSidebarOpen);\n      }\n    },\n    dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i3.RouterLink, i3.RouterLinkActive, i5.MatIcon],\n    styles: [\".sidebar[_ngcontent-%COMP%] {\\n  background: linear-gradient(180deg, var(--white) 0%, var(--gray-50) 100%);\\n  width: 250px;\\n  height: 100vh;\\n  position: relative;\\n  box-shadow: var(--shadow-xl);\\n  transition: var(--transition-normal);\\n  display: flex;\\n  flex-direction: column;\\n  border-right: 2px solid var(--gray-200);\\n  overflow: hidden;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.sidebar-header[_ngcontent-%COMP%] {\\n  padding: var(--spacing-lg);\\n  display: flex;\\n  align-items: center;\\n  border-bottom: 2px solid var(--gray-200);\\n  height: 70px;\\n  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.sidebar-header[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);\\n  animation: _ngcontent-%COMP%_shimmer 3s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_shimmer {\\n  0% { transform: translateX(-100%); }\\n  100% { transform: translateX(100%); }\\n}\\n\\n.toggle-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  color: var(--white);\\n  font-size: 1.2rem;\\n  cursor: pointer;\\n  padding: var(--spacing-sm);\\n  border-radius: var(--border-radius-md);\\n  transition: var(--transition-fast);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.toggle-btn[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n  transform: scale(1.05);\\n  box-shadow: var(--shadow-md);\\n}\\n\\n.brand[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-left: var(--spacing-lg);\\n  color: var(--white);\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.brand[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 35px;\\n  height: 35px;\\n  margin-right: var(--spacing-md);\\n  border-radius: var(--border-radius-full);\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  box-shadow: var(--shadow-md);\\n  transition: var(--transition-fast);\\n}\\n\\n.brand[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n  border-color: rgba(255, 255, 255, 0.6);\\n}\\n\\n.brand[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: var(--font-size-lg);\\n  font-weight: var(--font-weight-bold);\\n  color: var(--white);\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n  font-family: var(--font-family-primary);\\n  letter-spacing: 0.5px;\\n}\\n\\n.sidebar-menu[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: var(--spacing-lg) 0;\\n  \\n\\n  scrollbar-width: thin;\\n  scrollbar-color: var(--gray-400) transparent;\\n}\\n\\n.sidebar-menu[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n.sidebar-menu[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n\\n.sidebar-menu[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background-color: var(--gray-400);\\n  border-radius: var(--border-radius-full);\\n}\\n\\n.sidebar-menu[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background-color: var(--gray-500);\\n}\\n\\n.menu-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  width: 100% !important;\\n  padding: var(--spacing-md) var(--spacing-xl);\\n  \\n\\n  background: none;\\n  border: none;\\n  color: var(--gray-700);\\n  text-decoration: none;\\n  transition: var(--transition-normal);\\n  font-size: var(--font-size-sm);\\n  font-weight: var(--font-weight-medium);\\n  white-space: nowrap;\\n  border-radius: var(--border-radius-lg);\\n  position: relative;\\n  overflow: hidden;\\n  font-family: var(--font-family-primary);\\n}\\n\\n.menu-item[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  left: 0;\\n  top: 0;\\n  bottom: 0;\\n  width: 4px;\\n  background: linear-gradient(180deg, var(--primary-color) 0%, var(--primary-dark) 100%);\\n  transform: scaleY(0);\\n  transition: var(--transition-fast);\\n}\\n\\n.menu-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: var(--spacing-md);\\n  font-size: 1.3rem;\\n  width: 24px;\\n  height: 24px;\\n  transition: var(--transition-fast);\\n}\\n\\n.menu-item[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, var(--primary-lightest) 0%, rgba(41, 87, 140, 0.1) 100%);\\n  color: var(--primary-dark);\\n  transform: translateX(4px);\\n  box-shadow: var(--shadow-md);\\n}\\n\\n.menu-item[_ngcontent-%COMP%]:hover::before {\\n  transform: scaleY(1);\\n}\\n\\n.menu-item[_ngcontent-%COMP%]:hover   mat-icon[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  transform: scale(1.1);\\n}\\n\\n.menu-item.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);\\n  color: var(--white);\\n  transform: translateX(8px);\\n  box-shadow: var(--shadow-lg);\\n  font-weight: var(--font-weight-semibold);\\n}\\n\\n.menu-item.active[_ngcontent-%COMP%]::before {\\n  transform: scaleY(1);\\n  background: linear-gradient(180deg, var(--white) 0%, rgba(255, 255, 255, 0.8) 100%);\\n}\\n\\n.menu-item.active[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: var(--white);\\n  transform: scale(1.15);\\n}\\n\\n.menu-item.active[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);\\n  transform: translateX(8px);\\n}\\n\\n.sidebar-footer[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  border-top: 1px solid #eaeaea;\\n  background-color: #f8f9fa;\\n}\\n\\n.user-profile[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.user-profile[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  margin-right: 10px;\\n  object-fit: cover;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: hidden;\\n}\\n\\n.user-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  margin: 0;\\n  color: #11418e;\\n  font-weight: 600;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n.user-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  margin: 0;\\n  color: #515154;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n.logout-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #515154;\\n  cursor: pointer;\\n  padding: 0.5rem;\\n  border-radius: 6px;\\n  transition: all 0.2s;\\n}\\n\\n.logout-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #e0e8f0;\\n  color: #11418e;\\n}\\n\\n\\n\\n.sidebar-closed[_nghost-%COMP%]   .sidebar[_ngcontent-%COMP%], .sidebar-closed   [_nghost-%COMP%]   .sidebar[_ngcontent-%COMP%] {\\n  width: 70px;\\n}\\n\\n.sidebar-closed[_nghost-%COMP%]   .menu-item[_ngcontent-%COMP%], .sidebar-closed   [_nghost-%COMP%]   .menu-item[_ngcontent-%COMP%] {\\n  justify-content: center;\\n  padding: 0.8rem 0;\\n}\\n\\n.sidebar-closed[_nghost-%COMP%]   .menu-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%], .sidebar-closed   [_nghost-%COMP%]   .menu-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 0;\\n}\\n\\n\\n\\n@media (max-width: 991.98px) {\\n  .sidebar[_ngcontent-%COMP%] {\\n    width: 250px;\\n    position: fixed;\\n    z-index: 1050;\\n  }\\n\\n  .sidebar-closed[_nghost-%COMP%]   .sidebar[_ngcontent-%COMP%], .sidebar-closed   [_nghost-%COMP%]   .sidebar[_ngcontent-%COMP%] {\\n    transform: translateX(-100%);\\n  }\\n\\n  .sidebar-open[_nghost-%COMP%]   .sidebar[_ngcontent-%COMP%], .sidebar-open   [_nghost-%COMP%]   .sidebar[_ngcontent-%COMP%] {\\n    transform: translateX(0);\\n  }\\n}\\n\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "NavigationEnd", "filter", "UX_TEXT_STANDARDS", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "item_r3", "label", "ɵɵtemplate", "SidebarComponent_button_6_span_3_Template", "ɵɵproperty", "route", "ɵɵpureFunction1", "_c0", "isExact", "icon", "ctx_r1", "isSidebarOpen", "ɵɵlistener", "SidebarComponent_div_7_Template_button_click_8_listener", "ɵɵrestoreView", "_r7", "ctx_r6", "ɵɵnextContext", "ɵɵresetView", "logout", "ctx_r2", "profileName", "profileEmail", "SidebarComponent", "constructor", "roleService", "userService", "router", "toggle", "sidebarItems", "ngOnInit", "role", "getRole", "user", "getUserFromLocalStorage", "name", "email", "NAVIGATION", "DASHBOARD", "PROGRAMS", "DEPARTMENTS", "SUBJECTS", "CLASSES", "TIMETABLE", "TEACHERS", "STUDENTS", "ATTENDANCE", "NOTICES", "MY_CLASSES", "MY_STUDENTS", "MY_SUBJECTS", "COMPLAINTS", "MY_ATTENDANCE", "events", "pipe", "event", "subscribe", "window", "innerWidth", "toggleSidebar", "emit", "navigate", "_", "ɵɵdirectiveInject", "i1", "RoleService", "i2", "UserService", "i3", "Router", "_2", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "SidebarComponent_Template", "rf", "ctx", "SidebarComponent_Template_button_click_2_listener", "SidebarComponent_div_4_Template", "SidebarComponent_button_6_Template", "SidebarComponent_div_7_Template", "ɵɵpureFunction2", "_c1"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\sidebar\\sidebar.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\sidebar\\sidebar.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';\r\nimport { NavigationEnd, Router } from '@angular/router';\r\nimport { filter } from 'rxjs';\r\nimport { RoleService } from 'src/app/services/role.service';\r\nimport { UserService } from 'src/app/services/user.service';\r\nimport { UX_TEXT_STANDARDS } from 'src/app/shared/constants/ux-text-standards';\r\n\r\n@Component({\r\n  selector: 'app-sidebar',\r\n  templateUrl: './sidebar.component.html',\r\n  styleUrls: ['./sidebar.component.css']\r\n})\r\nexport class SidebarComponent implements OnInit {\r\n  @Input() isSidebarOpen: boolean = true;\r\n  @Output() toggle = new EventEmitter<void>();\r\n\r\n  sidebarItems: any[] = [];\r\n  profileName: string = '<PERSON>';\r\n  profileEmail: string = '<EMAIL>';\r\n\r\n  constructor(\r\n    private roleService: RoleService,\r\n    private userService: UserService,\r\n    private router: Router\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    const role = this.roleService.getRole();\r\n    const user = this.userService.getUserFromLocalStorage().user;\r\n    this.profileName = user.name;\r\n    this.profileEmail = user.email;\r\n\r\n    // Configure sidebar items based on user role with user-friendly labels\r\n    if (role === 'Principal') {\r\n      this.sidebarItems = [\r\n        { label: UX_TEXT_STANDARDS.NAVIGATION.DASHBOARD, icon: 'dashboard', route: '/dashboard/admin/overview', isExact: false },\r\n        { label: UX_TEXT_STANDARDS.NAVIGATION.PROGRAMS, icon: 'library_books', route: '/dashboard/admin/programs', isExact: false },\r\n        { label: UX_TEXT_STANDARDS.NAVIGATION.DEPARTMENTS, icon: 'business', route: '/dashboard/admin/department', isExact: false },\r\n        { label: UX_TEXT_STANDARDS.NAVIGATION.SUBJECTS, icon: 'subject', route: '/dashboard/admin/subjects', isExact: false },\r\n        { label: UX_TEXT_STANDARDS.NAVIGATION.CLASSES, icon: 'class', route: '/dashboard/admin/classes', isExact: false },\r\n        { label: UX_TEXT_STANDARDS.NAVIGATION.TIMETABLE, icon: 'schedule', route: '/dashboard/admin/timetable', isExact: false },\r\n        { label: UX_TEXT_STANDARDS.NAVIGATION.TEACHERS, icon: 'person', route: '/dashboard/admin/teacher', isExact: false },\r\n        { label: UX_TEXT_STANDARDS.NAVIGATION.STUDENTS, icon: 'school', route: '/dashboard/admin/students', isExact: false },\r\n        { label: UX_TEXT_STANDARDS.NAVIGATION.ATTENDANCE, icon: 'fact_check', route: '/dashboard/admin/students/attendance', isExact: false },\r\n        { label: UX_TEXT_STANDARDS.NAVIGATION.NOTICES, icon: 'campaign', route: '/dashboard/admin/notices', isExact: false },\r\n        // { label: 'Users', icon: 'manage_accounts', route: '/dashboard/admin/users', isExact: false },\r\n      ];\r\n    } else if (role === 'Teacher') {\r\n      this.sidebarItems = [\r\n        { label: UX_TEXT_STANDARDS.NAVIGATION.DASHBOARD, icon: 'dashboard', route: '/dashboard/teacher', isExact: true },\r\n        { label: UX_TEXT_STANDARDS.NAVIGATION.MY_CLASSES, icon: 'class', route: '/dashboard/teacher/classes', isExact: false },\r\n        { label: UX_TEXT_STANDARDS.NAVIGATION.MY_STUDENTS, icon: 'school', route: '/dashboard/teacher/students', isExact: false },\r\n        { label: UX_TEXT_STANDARDS.NAVIGATION.ATTENDANCE, icon: 'fact_check', route: '/dashboard/teacher/attendance', isExact: false },\r\n        { label: UX_TEXT_STANDARDS.NAVIGATION.MY_SUBJECTS, icon: 'subject', route: '/dashboard/teacher/subjects', isExact: false },\r\n        { label: UX_TEXT_STANDARDS.NAVIGATION.NOTICES, icon: 'campaign', route: '/dashboard/teacher/notice', isExact: false },\r\n        { label: UX_TEXT_STANDARDS.NAVIGATION.COMPLAINTS, icon: 'chat', route: '/dashboard/teacher/complaint', isExact: false }\r\n      ];\r\n    } else if (role === 'Student') {\r\n      this.sidebarItems = [\r\n        { label: UX_TEXT_STANDARDS.NAVIGATION.DASHBOARD, icon: 'dashboard', route: '/dashboard/student', isExact: true },\r\n        { label: UX_TEXT_STANDARDS.NAVIGATION.MY_CLASSES, icon: 'class', route: '/dashboard/student/classes', isExact: false },\r\n        { label: UX_TEXT_STANDARDS.NAVIGATION.MY_SUBJECTS, icon: 'subject', route: '/dashboard/student/subjects', isExact: false },\r\n        { label: 'My Teachers', icon: 'person', route: '/dashboard/student/teachers', isExact: false },\r\n        { label: UX_TEXT_STANDARDS.NAVIGATION.MY_ATTENDANCE, icon: 'fact_check', route: '/dashboard/student/attendance', isExact: false },\r\n        { label: UX_TEXT_STANDARDS.NAVIGATION.TIMETABLE, icon: 'schedule', route: '/dashboard/student/timetable', isExact: false }\r\n      ];\r\n    }\r\n\r\n    this.router.events\r\n      .pipe(filter(event => event instanceof NavigationEnd))\r\n      .subscribe(() => {\r\n        if (window.innerWidth <= 992) {\r\n          this.toggleSidebar();\r\n        }\r\n      });\r\n  }\r\n\r\n  toggleSidebar() {\r\n    this.toggle.emit();\r\n  }\r\n\r\n  logout(): void {\r\n    this.roleService.logout();\r\n    this.router.navigate(['/auth']);\r\n  }\r\n}", "<div class=\"sidebar\">\r\n  <div class=\"sidebar-header\">\r\n    <button class=\"toggle-btn\" (click)=\"toggleSidebar()\">\r\n      <i class=\"fas\" [ngClass]=\"{'fa-times': isSidebarOpen, 'fa-bars': !isSidebarOpen}\"></i>\r\n    </button>\r\n    <div class=\"brand\" *ngIf=\"isSidebarOpen\">\r\n      <img src=\"../../../assets/images/logo.jpeg\" alt=\"Logo\">\r\n      <h3>GPGC (Swabi)</h3>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"sidebar-menu\">\r\n    <button *ngFor=\"let item of sidebarItems\" class=\"menu-item\" \r\n            [routerLink]=\"item.route\" \r\n            routerLinkActive=\"active\"\r\n            [routerLinkActiveOptions]=\"{exact: item.isExact}\">\r\n      <mat-icon>{{item.icon}}</mat-icon>\r\n      <span *ngIf=\"isSidebarOpen\">{{item.label}}</span>\r\n    </button>\r\n  </div>\r\n\r\n  <div class=\"sidebar-footer\" *ngIf=\"isSidebarOpen\">\r\n    <div class=\"user-profile\">\r\n      <img src=\"../../../assets/images/logo.jpeg\" alt=\"Profile\">\r\n      <div class=\"user-info\">\r\n        <h4>{{profileName}}</h4>\r\n        <p>{{profileEmail}}</p>\r\n      </div>\r\n      <button class=\"logout-btn\" (click)=\"logout()\">\r\n        <i class=\"fas fa-sign-out-alt\"></i>\r\n      </button>\r\n    </div>\r\n  </div>\r\n</div>"], "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;AAC9E,SAASC,aAAa,QAAgB,iBAAiB;AACvD,SAASC,MAAM,QAAQ,MAAM;AAG7B,SAASC,iBAAiB,QAAQ,4CAA4C;;;;;;;;;ICA1EC,EAAA,CAAAC,cAAA,aAAyC;IACvCD,EAAA,CAAAE,SAAA,aAAuD;IACvDF,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,mBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;IAUrBJ,EAAA,CAAAC,cAAA,WAA4B;IAAAD,EAAA,CAAAG,MAAA,GAAc;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAArBJ,EAAA,CAAAK,SAAA,GAAc;IAAdL,EAAA,CAAAM,iBAAA,CAAAC,OAAA,CAAAC,KAAA,CAAc;;;;;;;;;;IAL5CR,EAAA,CAAAC,cAAA,iBAG0D;IAC9CD,EAAA,CAAAG,MAAA,GAAa;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAClCJ,EAAA,CAAAS,UAAA,IAAAC,yCAAA,mBAAiD;IACnDV,EAAA,CAAAI,YAAA,EAAS;;;;;IALDJ,EAAA,CAAAW,UAAA,eAAAJ,OAAA,CAAAK,KAAA,CAAyB,4BAAAZ,EAAA,CAAAa,eAAA,IAAAC,GAAA,EAAAP,OAAA,CAAAQ,OAAA;IAGrBf,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAAM,iBAAA,CAAAC,OAAA,CAAAS,IAAA,CAAa;IAChBhB,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAW,UAAA,SAAAM,MAAA,CAAAC,aAAA,CAAmB;;;;;;IAI9BlB,EAAA,CAAAC,cAAA,cAAkD;IAE9CD,EAAA,CAAAE,SAAA,cAA0D;IAC1DF,EAAA,CAAAC,cAAA,cAAuB;IACjBD,EAAA,CAAAG,MAAA,GAAe;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACxBJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAEzBJ,EAAA,CAAAC,cAAA,iBAA8C;IAAnBD,EAAA,CAAAmB,UAAA,mBAAAC,wDAAA;MAAApB,EAAA,CAAAqB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAvB,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAAF,MAAA,CAAAG,MAAA,EAAQ;IAAA,EAAC;IAC3C1B,EAAA,CAAAE,SAAA,YAAmC;IACrCF,EAAA,CAAAI,YAAA,EAAS;;;;IALHJ,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAM,iBAAA,CAAAqB,MAAA,CAAAC,WAAA,CAAe;IAChB5B,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAM,iBAAA,CAAAqB,MAAA,CAAAE,YAAA,CAAgB;;;;;;;;;ADd3B,OAAM,MAAOC,gBAAgB;EAQ3BC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc;IAFd,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAVP,KAAAhB,aAAa,GAAY,IAAI;IAC5B,KAAAiB,MAAM,GAAG,IAAIvC,YAAY,EAAQ;IAE3C,KAAAwC,YAAY,GAAU,EAAE;IACxB,KAAAR,WAAW,GAAW,iBAAiB;IACvC,KAAAC,YAAY,GAAW,wBAAwB;EAM3C;EAEJQ,QAAQA,CAAA;IACN,MAAMC,IAAI,GAAG,IAAI,CAACN,WAAW,CAACO,OAAO,EAAE;IACvC,MAAMC,IAAI,GAAG,IAAI,CAACP,WAAW,CAACQ,uBAAuB,EAAE,CAACD,IAAI;IAC5D,IAAI,CAACZ,WAAW,GAAGY,IAAI,CAACE,IAAI;IAC5B,IAAI,CAACb,YAAY,GAAGW,IAAI,CAACG,KAAK;IAE9B;IACA,IAAIL,IAAI,KAAK,WAAW,EAAE;MACxB,IAAI,CAACF,YAAY,GAAG,CAClB;QAAE5B,KAAK,EAAET,iBAAiB,CAAC6C,UAAU,CAACC,SAAS;QAAE7B,IAAI,EAAE,WAAW;QAAEJ,KAAK,EAAE,2BAA2B;QAAEG,OAAO,EAAE;MAAK,CAAE,EACxH;QAAEP,KAAK,EAAET,iBAAiB,CAAC6C,UAAU,CAACE,QAAQ;QAAE9B,IAAI,EAAE,eAAe;QAAEJ,KAAK,EAAE,2BAA2B;QAAEG,OAAO,EAAE;MAAK,CAAE,EAC3H;QAAEP,KAAK,EAAET,iBAAiB,CAAC6C,UAAU,CAACG,WAAW;QAAE/B,IAAI,EAAE,UAAU;QAAEJ,KAAK,EAAE,6BAA6B;QAAEG,OAAO,EAAE;MAAK,CAAE,EAC3H;QAAEP,KAAK,EAAET,iBAAiB,CAAC6C,UAAU,CAACI,QAAQ;QAAEhC,IAAI,EAAE,SAAS;QAAEJ,KAAK,EAAE,2BAA2B;QAAEG,OAAO,EAAE;MAAK,CAAE,EACrH;QAAEP,KAAK,EAAET,iBAAiB,CAAC6C,UAAU,CAACK,OAAO;QAAEjC,IAAI,EAAE,OAAO;QAAEJ,KAAK,EAAE,0BAA0B;QAAEG,OAAO,EAAE;MAAK,CAAE,EACjH;QAAEP,KAAK,EAAET,iBAAiB,CAAC6C,UAAU,CAACM,SAAS;QAAElC,IAAI,EAAE,UAAU;QAAEJ,KAAK,EAAE,4BAA4B;QAAEG,OAAO,EAAE;MAAK,CAAE,EACxH;QAAEP,KAAK,EAAET,iBAAiB,CAAC6C,UAAU,CAACO,QAAQ;QAAEnC,IAAI,EAAE,QAAQ;QAAEJ,KAAK,EAAE,0BAA0B;QAAEG,OAAO,EAAE;MAAK,CAAE,EACnH;QAAEP,KAAK,EAAET,iBAAiB,CAAC6C,UAAU,CAACQ,QAAQ;QAAEpC,IAAI,EAAE,QAAQ;QAAEJ,KAAK,EAAE,2BAA2B;QAAEG,OAAO,EAAE;MAAK,CAAE,EACpH;QAAEP,KAAK,EAAET,iBAAiB,CAAC6C,UAAU,CAACS,UAAU;QAAErC,IAAI,EAAE,YAAY;QAAEJ,KAAK,EAAE,sCAAsC;QAAEG,OAAO,EAAE;MAAK,CAAE,EACrI;QAAEP,KAAK,EAAET,iBAAiB,CAAC6C,UAAU,CAACU,OAAO;QAAEtC,IAAI,EAAE,UAAU;QAAEJ,KAAK,EAAE,0BAA0B;QAAEG,OAAO,EAAE;MAAK;MAClH;MAAA,CACD;KACF,MAAM,IAAIuB,IAAI,KAAK,SAAS,EAAE;MAC7B,IAAI,CAACF,YAAY,GAAG,CAClB;QAAE5B,KAAK,EAAET,iBAAiB,CAAC6C,UAAU,CAACC,SAAS;QAAE7B,IAAI,EAAE,WAAW;QAAEJ,KAAK,EAAE,oBAAoB;QAAEG,OAAO,EAAE;MAAI,CAAE,EAChH;QAAEP,KAAK,EAAET,iBAAiB,CAAC6C,UAAU,CAACW,UAAU;QAAEvC,IAAI,EAAE,OAAO;QAAEJ,KAAK,EAAE,4BAA4B;QAAEG,OAAO,EAAE;MAAK,CAAE,EACtH;QAAEP,KAAK,EAAET,iBAAiB,CAAC6C,UAAU,CAACY,WAAW;QAAExC,IAAI,EAAE,QAAQ;QAAEJ,KAAK,EAAE,6BAA6B;QAAEG,OAAO,EAAE;MAAK,CAAE,EACzH;QAAEP,KAAK,EAAET,iBAAiB,CAAC6C,UAAU,CAACS,UAAU;QAAErC,IAAI,EAAE,YAAY;QAAEJ,KAAK,EAAE,+BAA+B;QAAEG,OAAO,EAAE;MAAK,CAAE,EAC9H;QAAEP,KAAK,EAAET,iBAAiB,CAAC6C,UAAU,CAACa,WAAW;QAAEzC,IAAI,EAAE,SAAS;QAAEJ,KAAK,EAAE,6BAA6B;QAAEG,OAAO,EAAE;MAAK,CAAE,EAC1H;QAAEP,KAAK,EAAET,iBAAiB,CAAC6C,UAAU,CAACU,OAAO;QAAEtC,IAAI,EAAE,UAAU;QAAEJ,KAAK,EAAE,2BAA2B;QAAEG,OAAO,EAAE;MAAK,CAAE,EACrH;QAAEP,KAAK,EAAET,iBAAiB,CAAC6C,UAAU,CAACc,UAAU;QAAE1C,IAAI,EAAE,MAAM;QAAEJ,KAAK,EAAE,8BAA8B;QAAEG,OAAO,EAAE;MAAK,CAAE,CACxH;KACF,MAAM,IAAIuB,IAAI,KAAK,SAAS,EAAE;MAC7B,IAAI,CAACF,YAAY,GAAG,CAClB;QAAE5B,KAAK,EAAET,iBAAiB,CAAC6C,UAAU,CAACC,SAAS;QAAE7B,IAAI,EAAE,WAAW;QAAEJ,KAAK,EAAE,oBAAoB;QAAEG,OAAO,EAAE;MAAI,CAAE,EAChH;QAAEP,KAAK,EAAET,iBAAiB,CAAC6C,UAAU,CAACW,UAAU;QAAEvC,IAAI,EAAE,OAAO;QAAEJ,KAAK,EAAE,4BAA4B;QAAEG,OAAO,EAAE;MAAK,CAAE,EACtH;QAAEP,KAAK,EAAET,iBAAiB,CAAC6C,UAAU,CAACa,WAAW;QAAEzC,IAAI,EAAE,SAAS;QAAEJ,KAAK,EAAE,6BAA6B;QAAEG,OAAO,EAAE;MAAK,CAAE,EAC1H;QAAEP,KAAK,EAAE,aAAa;QAAEQ,IAAI,EAAE,QAAQ;QAAEJ,KAAK,EAAE,6BAA6B;QAAEG,OAAO,EAAE;MAAK,CAAE,EAC9F;QAAEP,KAAK,EAAET,iBAAiB,CAAC6C,UAAU,CAACe,aAAa;QAAE3C,IAAI,EAAE,YAAY;QAAEJ,KAAK,EAAE,+BAA+B;QAAEG,OAAO,EAAE;MAAK,CAAE,EACjI;QAAEP,KAAK,EAAET,iBAAiB,CAAC6C,UAAU,CAACM,SAAS;QAAElC,IAAI,EAAE,UAAU;QAAEJ,KAAK,EAAE,8BAA8B;QAAEG,OAAO,EAAE;MAAK,CAAE,CAC3H;;IAGH,IAAI,CAACmB,MAAM,CAAC0B,MAAM,CACfC,IAAI,CAAC/D,MAAM,CAACgE,KAAK,IAAIA,KAAK,YAAYjE,aAAa,CAAC,CAAC,CACrDkE,SAAS,CAAC,MAAK;MACd,IAAIC,MAAM,CAACC,UAAU,IAAI,GAAG,EAAE;QAC5B,IAAI,CAACC,aAAa,EAAE;;IAExB,CAAC,CAAC;EACN;EAEAA,aAAaA,CAAA;IACX,IAAI,CAAC/B,MAAM,CAACgC,IAAI,EAAE;EACpB;EAEAzC,MAAMA,CAAA;IACJ,IAAI,CAACM,WAAW,CAACN,MAAM,EAAE;IACzB,IAAI,CAACQ,MAAM,CAACkC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACjC;EAAC,QAAAC,CAAA,G;qBAxEUvC,gBAAgB,EAAA9B,EAAA,CAAAsE,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxE,EAAA,CAAAsE,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA1E,EAAA,CAAAsE,iBAAA,CAAAK,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAhB/C,gBAAgB;IAAAgD,SAAA;IAAAC,MAAA;MAAA7D,aAAA;IAAA;IAAA8D,OAAA;MAAA7C,MAAA;IAAA;IAAA8C,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCZ7BtF,EAAA,CAAAC,cAAA,aAAqB;QAEUD,EAAA,CAAAmB,UAAA,mBAAAqE,kDAAA;UAAA,OAASD,GAAA,CAAArB,aAAA,EAAe;QAAA,EAAC;QAClDlE,EAAA,CAAAE,SAAA,WAAsF;QACxFF,EAAA,CAAAI,YAAA,EAAS;QACTJ,EAAA,CAAAS,UAAA,IAAAgF,+BAAA,iBAGM;QACRzF,EAAA,CAAAI,YAAA,EAAM;QAENJ,EAAA,CAAAC,cAAA,aAA0B;QACxBD,EAAA,CAAAS,UAAA,IAAAiF,kCAAA,oBAMS;QACX1F,EAAA,CAAAI,YAAA,EAAM;QAENJ,EAAA,CAAAS,UAAA,IAAAkF,+BAAA,kBAWM;QACR3F,EAAA,CAAAI,YAAA,EAAM;;;QA9BeJ,EAAA,CAAAK,SAAA,GAAkE;QAAlEL,EAAA,CAAAW,UAAA,YAAAX,EAAA,CAAA4F,eAAA,IAAAC,GAAA,EAAAN,GAAA,CAAArE,aAAA,GAAAqE,GAAA,CAAArE,aAAA,EAAkE;QAE/DlB,EAAA,CAAAK,SAAA,GAAmB;QAAnBL,EAAA,CAAAW,UAAA,SAAA4E,GAAA,CAAArE,aAAA,CAAmB;QAOdlB,EAAA,CAAAK,SAAA,GAAe;QAAfL,EAAA,CAAAW,UAAA,YAAA4E,GAAA,CAAAnD,YAAA,CAAe;QASbpC,EAAA,CAAAK,SAAA,GAAmB;QAAnBL,EAAA,CAAAW,UAAA,SAAA4E,GAAA,CAAArE,aAAA,CAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}