const express = require("express");
require('dotenv').config();

console.log("Starting test server...");
console.log("PORT:", process.env.PORT);
console.log("MONGO_URL:", process.env.MONGO_URL ? "Set" : "Not set");

const app = express();
const PORT = process.env.PORT || 5003;

app.get("/", (req, res) => {
    res.json({ message: "Test server is working!" });
});

app.listen(PORT, () => {
    console.log(`Test server is running on port ${PORT}`);
});
