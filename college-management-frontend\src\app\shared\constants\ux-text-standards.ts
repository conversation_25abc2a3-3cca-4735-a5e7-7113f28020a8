/**
 * User Experience Text Standards
 * This file contains standardized, user-friendly text for labels, messages, and tooltips
 * to ensure consistent and clear communication across the application.
 */

export const UX_TEXT_STANDARDS = {
  // Form Labels - Simple and Clear
  FORM_LABELS: {
    FULL_NAME: 'Full Name',
    EMAIL_ADDRESS: 'Email Address',
    PHONE_NUMBER: 'Phone Number',
    HOME_ADDRESS: 'Home Address',
    FATHER_NAME: 'Father\'s Name',
    STUDENT_ID: 'Student ID',
    REGISTRATION_NUMBER: 'Registration Number',
    ROLL_NUMBER: 'Roll Number',
    PASSWORD: 'Password',
    CONFIRM_PASSWORD: 'Confirm Password',
    DEPARTMENT: 'Department',
    PROGRAM: 'Program',
    CLASS_YEAR: 'Class/Year',
    SECTION: 'Section',
    SEMESTER: 'Semester',
    SUBJECT: 'Subject',
    DESIGNATION: 'Job Title',
    POSITION: 'Position',
    IS_VISITING: 'Visiting Faculty',
    IS_ACTIVE: 'Active Status'
  },

  // Helpful Tooltips
  TOOLTIPS: {
    FULL_NAME: 'Enter your complete name as it appears on official documents',
    EMAIL_ADDRESS: 'Use your college email address or personal email for notifications',
    PHONE_NUMBER: 'Enter a 10-15 digit phone number (e.g., 03001234567)',
    STUDENT_ID: 'Your unique student identification number assigned by the college',
    REGISTRATION_NUMBER: 'Official registration number from college records',
    ROLL_NUMBER: 'Your class roll number for attendance and exams',
    PASSWORD: 'Choose a strong password with at least 6 characters',
    DEPARTMENT: 'Select the academic department you belong to',
    PROGRAM: 'Choose your study program (BS, Intermediate, etc.)',
    CLASS_YEAR: 'Select your current academic year or class level',
    SECTION: 'Choose your class section (A, B, C, etc.)',
    SEMESTER: 'Select your current semester number',
    VISITING_FACULTY: 'Check if this teacher is a visiting faculty member',
    ACTIVE_STATUS: 'Uncheck to temporarily disable this account',
    REFRESH_DATA: 'Click to reload the latest information',
    MARK_ATTENDANCE: 'Record student attendance for today\'s class',
    VIEW_DETAILS: 'Click to see complete information',
    EDIT_RECORD: 'Click to modify this information',
    DELETE_RECORD: 'Permanently remove this record (cannot be undone)'
  },

  // User-Friendly Error Messages
  ERROR_MESSAGES: {
    REQUIRED_FIELD: 'This field is required',
    INVALID_EMAIL: 'Please enter a valid email address (e.g., <EMAIL>)',
    INVALID_PHONE: 'Please enter a valid phone number (10-15 digits)',
    PASSWORD_TOO_SHORT: 'Password must be at least 6 characters long',
    PASSWORDS_DONT_MATCH: 'Passwords do not match. Please try again',
    NAME_TOO_SHORT: 'Name must be at least 2 characters long',
    INVALID_SELECTION: 'Please select a valid option from the list',
    NETWORK_ERROR: 'Unable to connect to the server. Please check your internet connection and try again',
    UNAUTHORIZED: 'Your session has expired. Please log in again',
    FORBIDDEN: 'You don\'t have permission to perform this action',
    NOT_FOUND: 'The requested information could not be found',
    SERVER_ERROR: 'Something went wrong on our end. Please try again in a few minutes',
    VALIDATION_ERROR: 'Please check your information and try again'
  },

  // Encouraging Success Messages
  SUCCESS_MESSAGES: {
    USER_CREATED: 'Great! New user account has been created successfully',
    USER_UPDATED: 'Perfect! User information has been updated',
    USER_DELETED: 'User account has been removed successfully',
    LOGIN_SUCCESS: 'Welcome back! You have logged in successfully',
    LOGOUT_SUCCESS: 'You have been logged out safely',
    PASSWORD_CHANGED: 'Your password has been updated successfully',
    ATTENDANCE_MARKED: 'Attendance has been recorded for all students',
    NOTICE_PUBLISHED: 'Your notice has been published and students will be notified',
    COMPLAINT_SUBMITTED: 'Your complaint has been submitted. We will review it soon',
    DATA_SAVED: 'All changes have been saved successfully',
    EMAIL_SENT: 'Email has been sent successfully',
    PROFILE_UPDATED: 'Your profile has been updated successfully'
  },

  // Loading Messages
  LOADING_MESSAGES: {
    SIGNING_IN: 'Signing you in...',
    LOADING_DASHBOARD: 'Loading your dashboard...',
    SAVING_DATA: 'Saving your information...',
    LOADING_STUDENTS: 'Loading student information...',
    LOADING_TEACHERS: 'Loading teacher information...',
    LOADING_CLASSES: 'Loading class information...',
    LOADING_SUBJECTS: 'Loading subjects...',
    LOADING_ATTENDANCE: 'Loading attendance records...',
    LOADING_NOTICES: 'Loading notices...',
    PROCESSING_REQUEST: 'Processing your request...',
    UPLOADING_FILE: 'Uploading file...',
    GENERATING_REPORT: 'Generating your report...'
  },

  // Navigation Labels
  NAVIGATION: {
    DASHBOARD: 'Dashboard',
    MY_PROFILE: 'My Profile',
    STUDENTS: 'Students',
    TEACHERS: 'Teachers',
    CLASSES: 'Classes',
    SUBJECTS: 'Subjects',
    ATTENDANCE: 'Attendance',
    NOTICES: 'Notices & Announcements',
    REPORTS: 'Reports',
    SETTINGS: 'Settings',
    LOGOUT: 'Sign Out',
    MY_SUBJECTS: 'My Subjects',
    MY_CLASSES: 'My Classes',
    MY_STUDENTS: 'My Students',
    MY_ATTENDANCE: 'My Attendance',
    COMPLAINTS: 'Complaints & Feedback',
    TIMETABLE: 'Class Schedule',
    PROGRAMS: 'Academic Programs',
    DEPARTMENTS: 'Departments'
  },

  // Button Labels
  BUTTONS: {
    SAVE: 'Save Changes',
    CANCEL: 'Cancel',
    DELETE: 'Delete',
    EDIT: 'Edit',
    ADD: 'Add New',
    SUBMIT: 'Submit',
    RESET: 'Reset Form',
    SEARCH: 'Search',
    FILTER: 'Filter Results',
    EXPORT: 'Export Data',
    PRINT: 'Print',
    REFRESH: 'Refresh',
    BACK: 'Go Back',
    NEXT: 'Next',
    PREVIOUS: 'Previous',
    CONFIRM: 'Confirm',
    MARK_PRESENT: 'Mark Present',
    MARK_ABSENT: 'Mark Absent',
    MARK_ATTENDANCE: 'Record Attendance',
    VIEW_DETAILS: 'View Details',
    ADD_STUDENT: 'Add New Student',
    ADD_TEACHER: 'Add New Teacher',
    ADD_CLASS: 'Create New Class',
    ADD_SUBJECT: 'Add New Subject',
    PUBLISH_NOTICE: 'Publish Notice',
    SEND_MESSAGE: 'Send Message'
  },

  // Status Labels
  STATUS: {
    ACTIVE: 'Active',
    INACTIVE: 'Inactive',
    PRESENT: 'Present',
    ABSENT: 'Absent',
    LATE: 'Late',
    EXCUSED: 'Excused',
    PUBLISHED: 'Published',
    DRAFT: 'Draft',
    PENDING: 'Pending',
    APPROVED: 'Approved',
    REJECTED: 'Rejected',
    COMPLETED: 'Completed',
    IN_PROGRESS: 'In Progress'
  },

  // Placeholder Text
  PLACEHOLDERS: {
    SEARCH_USERS: 'Search by name, email, or ID...',
    SEARCH_STUDENTS: 'Search students by name or roll number...',
    SEARCH_TEACHERS: 'Search teachers by name or department...',
    ENTER_NAME: 'Enter full name',
    ENTER_EMAIL: 'Enter email address',
    ENTER_PHONE: 'e.g., 03001234567',
    ENTER_ADDRESS: 'Enter complete address',
    SELECT_DEPARTMENT: 'Choose department',
    SELECT_PROGRAM: 'Choose program',
    SELECT_CLASS: 'Choose class',
    SELECT_SECTION: 'Choose section',
    ENTER_SUBJECT_NAME: 'Enter subject name',
    ENTER_NOTICE_TITLE: 'Enter notice title',
    ENTER_NOTICE_CONTENT: 'Write your notice content here...',
    ENTER_COMPLAINT: 'Describe your complaint or feedback...'
  }
};

// Role-specific text variations
export const ROLE_SPECIFIC_TEXT = {
  PRINCIPAL: {
    DASHBOARD_WELCOME: 'Welcome to your Administrative Dashboard',
    QUICK_ACTIONS_TITLE: 'Quick Administrative Actions',
    OVERVIEW_TITLE: 'College Overview'
  },
  TEACHER: {
    DASHBOARD_WELCOME: 'Welcome to your Teaching Dashboard',
    QUICK_ACTIONS_TITLE: 'Quick Teaching Actions',
    OVERVIEW_TITLE: 'My Teaching Overview'
  },
  STUDENT: {
    DASHBOARD_WELCOME: 'Welcome to your Student Portal',
    QUICK_ACTIONS_TITLE: 'Quick Student Actions',
    OVERVIEW_TITLE: 'My Academic Overview'
  }
};

// Confirmation Messages
export const CONFIRMATION_MESSAGES = {
  DELETE_USER: 'Are you sure you want to delete this user? This action cannot be undone.',
  DELETE_STUDENT: 'Are you sure you want to remove this student? This will delete all their records.',
  DELETE_TEACHER: 'Are you sure you want to remove this teacher? This will affect their assigned classes.',
  DELETE_CLASS: 'Are you sure you want to delete this class? This will affect all enrolled students.',
  DELETE_SUBJECT: 'Are you sure you want to delete this subject? This will affect all related records.',
  DELETE_NOTICE: 'Are you sure you want to delete this notice? Students will no longer see it.',
  MARK_ATTENDANCE: 'Are you sure you want to submit attendance for all students?',
  PUBLISH_NOTICE: 'Are you sure you want to publish this notice? All targeted users will be notified.',
  LOGOUT: 'Are you sure you want to sign out?'
};
