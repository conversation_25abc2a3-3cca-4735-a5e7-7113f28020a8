{"ast": null, "code": "import { MatAutocompleteModule } from '@angular/material/autocomplete';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatButtonToggleModule } from '@angular/material/button-toggle';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatOptionModule, MatRippleModule } from '@angular/material/core';\nimport { MatGridListModule } from '@angular/material/grid-list';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatSliderModule } from '@angular/material/slider';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { DragDropModule } from '@angular/cdk/drag-drop';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatListModule } from '@angular/material/list';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { PortalModule } from '@angular/cdk/portal';\nimport { MatTreeModule } from '@angular/material/tree';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nexport let MaterialModule = /*#__PURE__*/(() => {\n  class MaterialModule {\n    static #_ = this.ɵfac = function MaterialModule_Factory(t) {\n      return new (t || MaterialModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: MaterialModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [MatAutocompleteModule, MatButtonModule, MatButtonToggleModule, MatCardModule, MatCheckboxModule, MatChipsModule, MatDatepickerModule, MatDialogModule, MatExpansionModule, MatGridListModule, MatIconModule, MatInputModule, MatListModule, MatMenuModule, MatNativeDateModule, FormsModule, ReactiveFormsModule, MatPaginatorModule, MatProgressBarModule, MatProgressSpinnerModule, MatRadioModule, MatRippleModule, MatSelectModule, MatSidenavModule, MatSliderModule, MatSlideToggleModule, MatSnackBarModule, MatSortModule, MatTableModule, MatTabsModule, MatToolbarModule, MatTooltipModule, MatOptionModule, DragDropModule, MatDividerModule, PortalModule, MatTreeModule, MatBadgeModule, MatFormFieldModule, MatAutocompleteModule, MatButtonModule, MatButtonToggleModule, MatCardModule, MatCheckboxModule, MatChipsModule, MatDatepickerModule, MatDialogModule, MatExpansionModule, MatGridListModule, MatIconModule, MatInputModule, MatListModule, MatMenuModule, MatNativeDateModule, ReactiveFormsModule, FormsModule, MatPaginatorModule, MatProgressBarModule, MatProgressSpinnerModule, MatRadioModule, MatRippleModule, MatSelectModule, MatSidenavModule, MatSliderModule, MatSlideToggleModule, MatSnackBarModule, MatSortModule, MatTableModule, MatTabsModule, MatToolbarModule, MatTooltipModule, MatOptionModule, DragDropModule, MatDividerModule, PortalModule, MatTreeModule, MatBadgeModule, MatFormFieldModule]\n    });\n  }\n  return MaterialModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}