<div class="student-classes-container">
  <!-- Header -->
  <div class="page-header">
    <div class="header-content">
      <h1>My Classes</h1>
      <p>View your enrolled classes and class information</p>
    </div>
    <button mat-icon-button (click)="refreshData()" matTooltip="Refresh Data">
      <mat-icon>refresh</mat-icon>
    </button>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="loading-container">
    <mat-spinner></mat-spinner>
    <p>Loading your classes...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !loading" class="error-container">
    <mat-icon>error</mat-icon>
    <h3>Error Loading Classes</h3>
    <p>{{ error }}</p>
    <button mat-raised-button color="primary" (click)="refreshData()">
      <mat-icon>refresh</mat-icon>
      Try Again
    </button>
  </div>

  <!-- Content -->
  <div *ngIf="studentDashboard && !loading && !error" class="classes-content">
    <!-- Current Class Information -->
    <mat-card class="class-info-card">
      <mat-card-header>
        <mat-card-title>Current Class Information</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="class-details">
          <div class="detail-item">
            <mat-icon>class</mat-icon>
            <div class="detail-content">
              <h4>Class</h4>
              <p>{{ studentDashboard.student.class?.name || 'Not Assigned' }}</p>
            </div>
          </div>
          <div class="detail-item">
            <mat-icon>business</mat-icon>
            <div class="detail-content">
              <h4>Department</h4>
              <p>{{ studentDashboard.student.department?.name || 'Not Assigned' }}</p>
            </div>
          </div>
          <div class="detail-item">
            <mat-icon>library_books</mat-icon>
            <div class="detail-content">
              <h4>Program</h4>
              <p>{{ studentDashboard.student.program?.name || 'Not Assigned' }}</p>
            </div>
          </div>
          <div class="detail-item">
            <mat-icon>school</mat-icon>
            <div class="detail-content">
              <h4>Semester</h4>
              <p>{{ studentDashboard.student.semester || 'Not Assigned' }}</p>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Class Statistics -->
    <div class="stats-grid">
      <mat-card class="stat-card">
        <mat-card-content>
          <div class="stat-content">
            <div class="stat-icon">
              <mat-icon>subject</mat-icon>
            </div>
            <div class="stat-info">
              <h3>{{ studentDashboard.subjectAttendance?.length || 0 }}</h3>
              <p>Total Subjects</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card">
        <mat-card-content>
          <div class="stat-content">
            <div class="stat-icon">
              <mat-icon>person</mat-icon>
            </div>
            <div class="stat-info">
              <h3>{{ studentDashboard.teachers?.length || 0 }}</h3>
              <p>Teachers</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card">
        <mat-card-content>
          <div class="stat-content">
            <div class="stat-icon">
              <mat-icon>group</mat-icon>
            </div>
            <div class="stat-info">
              <h3>{{ studentDashboard.classmates?.length || 0 }}</h3>
              <p>Classmates</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Subjects List -->
    <mat-card class="subjects-card" *ngIf="studentDashboard.subjectAttendance?.length > 0">
      <mat-card-header>
        <mat-card-title>My Subjects</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="subjects-list">
          <div *ngFor="let subject of studentDashboard.subjectAttendance" class="subject-item">
            <div class="subject-info">
              <h4>{{ subject.subjectName }}</h4>
              <p>{{ subject.present }}/{{ subject.total }} classes attended</p>
            </div>
            <div class="subject-stats">
              <div class="attendance-percentage" 
                   [class.good]="subject.percentage >= 75"
                   [class.warning]="subject.percentage >= 60 && subject.percentage < 75"
                   [class.danger]="subject.percentage < 60">
                {{ subject.percentage | number:'1.0-0' }}%
              </div>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Quick Actions -->
    <div class="quick-actions">
      <h3>Quick Actions</h3>
      <div class="action-buttons">
        <button mat-raised-button color="primary" routerLink="/dashboard/student/attendance">
          <mat-icon>fact_check</mat-icon>
          View Attendance
        </button>
        <button mat-raised-button color="accent" routerLink="/dashboard/student/teachers">
          <mat-icon>person</mat-icon>
          View Teachers
        </button>
        <button mat-raised-button color="primary" routerLink="/dashboard/student/subjects">
          <mat-icon>subject</mat-icon>
          View Subjects
        </button>
      </div>
    </div>
  </div>
</div>
