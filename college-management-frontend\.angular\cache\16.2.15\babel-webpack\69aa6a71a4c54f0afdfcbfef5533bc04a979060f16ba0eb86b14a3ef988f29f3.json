{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/checkbox\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/forms\";\nfunction StudentNoticeComponent_tr_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 11)(2, \"mat-checkbox\", 12);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\", 13);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\", 13);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const notice_r1 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", notice_r1.user, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", notice_r1.notice, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", notice_r1.date, \" \");\n  }\n}\nexport let StudentNoticeComponent = /*#__PURE__*/(() => {\n  class StudentNoticeComponent {\n    constructor(router) {\n      this.router = router;\n      this.isChecked = false;\n      this.isIndeterminate = true;\n      this.studentstable = false;\n      this.notices = [{\n        user: 'Muhammad Luqman',\n        notice: 'lorem ipsum dor ssi sjas jasds doaoww djasdad',\n        date: '2024'\n      }, {\n        user: 'Saad Khan',\n        notice: 'lorem ipsum dor ssi sjas jasds doaoww djasdad',\n        date: '2024'\n      }];\n    }\n    toggleCheckbox() {\n      if (this.isIndeterminate) {\n        this.isIndeterminate = false;\n        this.isChecked = true;\n      } else if (this.isChecked) {\n        this.isChecked = false;\n      } else {\n        this.isIndeterminate = true;\n      }\n    }\n    static #_ = this.ɵfac = function StudentNoticeComponent_Factory(t) {\n      return new (t || StudentNoticeComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StudentNoticeComponent,\n      selectors: [[\"app-student-notice\"]],\n      decls: 27,\n      vars: 3,\n      consts: [[1, \"fw-bold\"], [1, \"table-container\", \"mb-4\"], [1, \"table-responsive\"], [1, \"tablehead\"], [1, \"one\"], [\"color\", \"primary\", 3, \"ngModel\", \"indeterminate\", \"ngModelChange\", \"change\"], [1, \"two\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"p-3\", \"align-items-center\", \"justify-content-between\"], [1, \"btn\", \"btn-top\", \"border\"], [1, \"page\"], [1, \"name\"], [\"color\", \"primary\"], [1, \"para\"]],\n      template: function StudentNoticeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"h1\", 0);\n          i0.ɵɵtext(1, \"Notices\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"div\", 1)(3, \"div\", 2)(4, \"table\")(5, \"thead\")(6, \"tr\", 3)(7, \"th\", 4)(8, \"mat-checkbox\", 5);\n          i0.ɵɵlistener(\"ngModelChange\", function StudentNoticeComponent_Template_mat_checkbox_ngModelChange_8_listener($event) {\n            return ctx.isChecked = $event;\n          })(\"change\", function StudentNoticeComponent_Template_mat_checkbox_change_8_listener() {\n            return ctx.toggleCheckbox();\n          });\n          i0.ɵɵtext(9, \" User \");\n          i0.ɵɵelementStart(10, \"mat-icon\");\n          i0.ɵɵtext(11, \"arrow_downward\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(12, \"th\");\n          i0.ɵɵtext(13, \"Notice \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"th\", 6);\n          i0.ɵɵtext(15, \"Date \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"tbody\");\n          i0.ɵɵtemplate(17, StudentNoticeComponent_tr_17_Template, 8, 3, \"tr\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"div\", 8)(19, \"div\")(20, \"button\", 9);\n          i0.ɵɵtext(21, \"Previous\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"div\", 10);\n          i0.ɵɵtext(23, \"Page 1 of 1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"div\")(25, \"button\", 9);\n          i0.ɵɵtext(26, \"Next\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngModel\", ctx.isChecked)(\"indeterminate\", ctx.isIndeterminate);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngForOf\", ctx.notices);\n        }\n      },\n      dependencies: [i2.NgForOf, i3.MatCheckbox, i4.MatIcon, i5.NgControlStatus, i5.NgModel]\n    });\n  }\n  return StudentNoticeComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}