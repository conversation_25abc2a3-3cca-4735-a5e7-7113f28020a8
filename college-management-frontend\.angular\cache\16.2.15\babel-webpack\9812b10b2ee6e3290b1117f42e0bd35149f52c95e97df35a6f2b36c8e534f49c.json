{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/services/department.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/icon\";\nfunction DepartmentFormComponent_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"save\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Save \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction DepartmentFormComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 14);\n    i0.ɵɵtext(1, \" Saving... \");\n  }\n}\nexport let DepartmentFormComponent = /*#__PURE__*/(() => {\n  class DepartmentFormComponent {\n    constructor(fb, departmentservice, router, ActivatedRoute) {\n      this.fb = fb;\n      this.departmentservice = departmentservice;\n      this.router = router;\n      this.ActivatedRoute = ActivatedRoute;\n      this.editingdepartmentId = null;\n      this.isLoading = false;\n      this.departmentForm = this.fb.group({\n        name: ['', Validators.required],\n        note: ['']\n      });\n    }\n    ngOnInit() {\n      this.editingdepartmentId = this.ActivatedRoute.snapshot.paramMap.get('id');\n      if (this.editingdepartmentId) {\n        this.loaddepartmentData(this.editingdepartmentId);\n      }\n    }\n    // Load department data for edit\n    loaddepartmentData(id) {\n      this.departmentservice.getdepartmentById(id).subscribe(department => {\n        console.log('Department data:', department);\n        this.departmentForm.patchValue({\n          name: department?.dep?.name,\n          note: department?.dep?.note\n        });\n      }, error => {\n        console.error('Error fetching department:', error);\n      });\n    }\n    // Submit form\n    onSubmit() {\n      if (this.departmentForm.invalid) return;\n      this.isLoading = true;\n      const departmentData = this.departmentForm.value;\n      const request = this.editingdepartmentId ? this.departmentservice.updatedepartment(this.editingdepartmentId, departmentData) : this.departmentservice.adddepartment(departmentData);\n      request.subscribe(res => {\n        this.isLoading = false;\n        Swal.fire({\n          title: res.message,\n          icon: 'success',\n          confirmButtonColor: '#3085d6',\n          timer: 1500\n        }).then(() => {\n          this.router.navigate(['/dashboard/admin/department']);\n          this.clearForm();\n        });\n      }, error => {\n        this.isLoading = false;\n        console.error('Submission error:', error);\n        Swal.fire('Error', 'Something went wrong.', 'error');\n      });\n    }\n    // Navigate back\n    goBack() {\n      this.router.navigate(['/dashboard/admin/department']);\n    }\n    // Clear form\n    clearForm() {\n      this.departmentForm.reset();\n      this.editingdepartmentId = null;\n    }\n    static #_ = this.ɵfac = function DepartmentFormComponent_Factory(t) {\n      return new (t || DepartmentFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.DepartmentService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DepartmentFormComponent,\n      selectors: [[\"app-department-form\"]],\n      decls: 23,\n      vars: 5,\n      consts: [[1, \"form-container\"], [1, \"form-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"d-flex\", \"gap-2\"], [1, \"btn-back\", 3, \"click\"], [1, \"btn\", \"btn-primary\", \"btn-save\", 3, \"disabled\", \"click\"], [4, \"ngIf\", \"ngIfElse\"], [\"loading\", \"\"], [3, \"formGroup\"], [1, \"form-grid\"], [1, \"form-group\", \"mb-3\"], [\"for\", \"name\"], [\"type\", \"text\", \"placeholder\", \"Enter department Name\", \"formControlName\", \"name\", 1, \"form-control\"], [\"for\", \"note\"], [\"type\", \"text\", \"placeholder\", \"Enter  note\", \"formControlName\", \"note\", 1, \"form-control\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"text-light\", \"me-1\"]],\n      template: function DepartmentFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 2)(5, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function DepartmentFormComponent_Template_button_click_5_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵelementStart(6, \"mat-icon\");\n          i0.ɵɵtext(7, \"arrow_back\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(8, \" Back \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function DepartmentFormComponent_Template_button_click_9_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtemplate(10, DepartmentFormComponent_ng_container_10_Template, 4, 0, \"ng-container\", 5);\n          i0.ɵɵtemplate(11, DepartmentFormComponent_ng_template_11_Template, 2, 0, \"ng-template\", null, 6, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"form\", 7)(14, \"div\", 8)(15, \"div\", 9)(16, \"label\", 10);\n          i0.ɵɵtext(17, \"Department Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(18, \"input\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 9)(20, \"label\", 12);\n          i0.ɵɵtext(21, \"note (optional)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(22, \"input\", 13);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const _r1 = i0.ɵɵreference(12);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.editingdepartmentId ? \"Update Department\" : \"Add Department\");\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", ctx.departmentForm.invalid || ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading)(\"ngIfElse\", _r1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"formGroup\", ctx.departmentForm);\n        }\n      },\n      dependencies: [i4.NgIf, i5.MatIcon, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName]\n    });\n  }\n  return DepartmentFormComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}