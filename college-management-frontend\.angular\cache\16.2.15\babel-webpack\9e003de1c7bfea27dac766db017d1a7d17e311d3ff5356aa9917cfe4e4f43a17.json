{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction ResetPasswordComponent_div_24_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Password is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_24_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Password must be at least 6 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtemplate(1, ResetPasswordComponent_div_24_small_1_Template, 2, 0, \"small\", 32);\n    i0.ɵɵtemplate(2, ResetPasswordComponent_div_24_small_2_Template, 2, 0, \"small\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r0.resetPasswordForm.get(\"password\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r0.resetPasswordForm.get(\"password\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"minlength\"]);\n  }\n}\nfunction ResetPasswordComponent_div_32_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Confirm password is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_32_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Passwords do not match\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtemplate(1, ResetPasswordComponent_div_32_small_1_Template, 2, 0, \"small\", 32);\n    i0.ɵɵtemplate(2, ResetPasswordComponent_div_32_small_2_Template, 2, 0, \"small\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r1.resetPasswordForm.get(\"confirmPassword\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r1.resetPasswordForm.get(\"confirmPassword\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"passwordMismatch\"]);\n  }\n}\nfunction ResetPasswordComponent_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 33);\n  }\n}\nexport let ResetPasswordComponent = /*#__PURE__*/(() => {\n  class ResetPasswordComponent {\n    constructor(fb, authService, router) {\n      this.fb = fb;\n      this.authService = authService;\n      this.router = router;\n      this.loading = false;\n      this.showPassword = false;\n      this.showConfirmPassword = false;\n      this.user = null;\n    }\n    ngOnInit() {\n      // Get verified user data from localStorage\n      const verifiedUser = localStorage.getItem('verifiedUser');\n      if (verifiedUser) {\n        this.user = JSON.parse(verifiedUser);\n      } else {\n        // If no verified user data, redirect to login\n        this.router.navigate(['/auth']);\n        return;\n      }\n      this.resetPasswordForm = this.fb.group({\n        password: ['', [Validators.required, Validators.minLength(6)]],\n        confirmPassword: ['', [Validators.required]]\n      }, {\n        validators: this.passwordMatchValidator\n      });\n    }\n    passwordMatchValidator(form) {\n      const password = form.get('password');\n      const confirmPassword = form.get('confirmPassword');\n      if (password && confirmPassword && password.value !== confirmPassword.value) {\n        confirmPassword.setErrors({\n          passwordMismatch: true\n        });\n        return {\n          passwordMismatch: true\n        };\n      }\n      return null;\n    }\n    onSubmit() {\n      if (this.resetPasswordForm.valid && this.user) {\n        this.loading = true;\n        const password = this.resetPasswordForm.value.password;\n        this.authService.resetPassword(this.user._id, password).subscribe({\n          next: response => {\n            this.loading = false;\n            if (response.success) {\n              // Clear stored user data\n              localStorage.removeItem('resetUser');\n              localStorage.removeItem('signupUser');\n              localStorage.removeItem('verifiedUser');\n              Swal.fire({\n                title: '<h1 class=\"mb-4\">Password Reset Successfully!</h1>',\n                html: '<p>Your password has been successfully reset. <br> Click below to log in with your new password.</p>',\n                icon: 'success',\n                confirmButtonText: 'Log in',\n                confirmButtonColor: '#29578c'\n              }).then(result => {\n                if (result.isConfirmed) {\n                  this.router.navigate(['/auth']);\n                }\n              });\n            }\n          },\n          error: error => {\n            this.loading = false;\n            console.error('Reset password error:', error);\n            const errorMessage = error.error?.message || 'Failed to reset password. Please try again.';\n            Swal.fire({\n              title: 'Reset Failed',\n              text: errorMessage,\n              icon: 'error',\n              confirmButtonColor: '#29578c'\n            });\n          }\n        });\n      } else {\n        // Mark all fields as touched to show validation errors\n        Object.keys(this.resetPasswordForm.controls).forEach(key => {\n          this.resetPasswordForm.get(key)?.markAsTouched();\n        });\n      }\n    }\n    togglePasswordVisibility() {\n      this.showPassword = !this.showPassword;\n    }\n    toggleConfirmPasswordVisibility() {\n      this.showConfirmPassword = !this.showConfirmPassword;\n    }\n    // Legacy method for backward compatibility\n    resetpassword() {\n      this.onSubmit();\n    }\n    static #_ = this.ɵfac = function ResetPasswordComponent_Factory(t) {\n      return new (t || ResetPasswordComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ResetPasswordComponent,\n      selectors: [[\"app-reset-password\"]],\n      decls: 53,\n      vars: 16,\n      consts: [[1, \"container-fluid\"], [1, \"row\", \"vh-100\"], [1, \"col-lg-5\", \"d-flex\", \"flex-column\", \"justify-content-center\", \"align-items-center\", \"p-3\", \"bg-white\"], [1, \"w-100\", 2, \"max-width\", \"400px\"], [1, \"text-center\"], [1, \"mb-4\"], [\"src\", \"../../../assets/images/logo.jpeg\"], [1, \"d-flex\", \"justify-content-center\", \"mt-5\", \"mb-2\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"fa-check\", \"text-success\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"mb-3\"], [\"for\", \"password\", 1, \"form-label\"], [1, \"input-group\"], [\"id\", \"password\", \"formControlName\", \"password\", \"placeholder\", \"Enter your password\", 1, \"form-control\", 3, \"type\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"for\", \"confirmPassword\", 1, \"form-label\"], [\"id\", \"confirmPassword\", \"formControlName\", \"confirmPassword\", \"placeholder\", \"Enter confirm password\", 1, \"form-control\", 3, \"type\"], [1, \"d-grid\", \"gap-2\"], [\"type\", \"submit\", 1, \"btn\", \"submit\", 3, \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", \"role\", \"status\", \"aria-hidden\", \"true\", 4, \"ngIf\"], [1, \"mt-3\", \"text-center\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"fa-arrow-left\"], [\"routerLink\", \"/auth\"], [1, \"col-lg-7\", \"d-none\", \"d-lg-flex\", \"flex-column\", \"align-items-start\", \"bg-light\", \"position-relative\"], [1, \"text-start\", \"p-5\", \"w-100\"], [1, \"blockquote\"], [1, \"blockquote-footer\"], [\"title\", \"Source Title\"], [1, \"image-container\"], [\"src\", \"../../../assets/images/background.jpeg\", \"alt\", \"Stats graph\", 1, \"img-fluid\", \"position-absolute\"], [1, \"invalid-feedback\"], [4, \"ngIf\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"]],\n      template: function ResetPasswordComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"h2\", 5);\n          i0.ɵɵelement(6, \"img\", 6);\n          i0.ɵɵtext(7, \" GPGC (Swabi)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 7);\n          i0.ɵɵelement(9, \"i\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"h1\", 5);\n          i0.ɵɵtext(11, \"Set new password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"p\");\n          i0.ɵɵtext(13, \"Your new password must be different\");\n          i0.ɵɵelement(14, \"br\");\n          i0.ɵɵtext(15, \" to previously used passwords.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"form\", 9);\n          i0.ɵɵlistener(\"ngSubmit\", function ResetPasswordComponent_Template_form_ngSubmit_16_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(17, \"div\", 10)(18, \"label\", 11);\n          i0.ɵɵtext(19, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\", 12);\n          i0.ɵɵelement(21, \"input\", 13);\n          i0.ɵɵelementStart(22, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function ResetPasswordComponent_Template_button_click_22_listener() {\n            return ctx.togglePasswordVisibility();\n          });\n          i0.ɵɵelement(23, \"i\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(24, ResetPasswordComponent_div_24_Template, 3, 2, \"div\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"div\", 10)(26, \"label\", 16);\n          i0.ɵɵtext(27, \"Confirm Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"div\", 12);\n          i0.ɵɵelement(29, \"input\", 17);\n          i0.ɵɵelementStart(30, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function ResetPasswordComponent_Template_button_click_30_listener() {\n            return ctx.toggleConfirmPasswordVisibility();\n          });\n          i0.ɵɵelement(31, \"i\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(32, ResetPasswordComponent_div_32_Template, 3, 2, \"div\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"div\", 18)(34, \"button\", 19);\n          i0.ɵɵtemplate(35, ResetPasswordComponent_span_35_Template, 1, 0, \"span\", 20);\n          i0.ɵɵtext(36);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"p\", 21);\n          i0.ɵɵelement(38, \"i\", 22);\n          i0.ɵɵtext(39, \" \\u00A0\");\n          i0.ɵɵelementStart(40, \"a\", 23);\n          i0.ɵɵtext(41, \"Back to log in \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(42, \"div\", 24)(43, \"div\", 25)(44, \"blockquote\", 26)(45, \"h2\", 5);\n          i0.ɵɵtext(46, \"College management system Login page\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"footer\", 27);\n          i0.ɵɵtext(48, \"Name\");\n          i0.ɵɵelementStart(49, \"cite\", 28);\n          i0.ɵɵtext(50, \"Owner ~ GPGC SWABI\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(51, \"div\", 29);\n          i0.ɵɵelement(52, \"img\", 30);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_4_0;\n          let tmp_5_0;\n          let tmp_8_0;\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"formGroup\", ctx.resetPasswordForm);\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_1_0 = ctx.resetPasswordForm.get(\"password\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.resetPasswordForm.get(\"password\")) == null ? null : tmp_1_0.touched));\n          i0.ɵɵproperty(\"type\", ctx.showPassword ? \"text\" : \"password\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassMap(ctx.showPassword ? \"fa fa-eye-slash\" : \"fa fa-eye\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.resetPasswordForm.get(\"password\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.resetPasswordForm.get(\"password\")) == null ? null : tmp_4_0.touched));\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_5_0 = ctx.resetPasswordForm.get(\"confirmPassword\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.resetPasswordForm.get(\"confirmPassword\")) == null ? null : tmp_5_0.touched));\n          i0.ɵɵproperty(\"type\", ctx.showConfirmPassword ? \"text\" : \"password\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassMap(ctx.showConfirmPassword ? \"fa fa-eye-slash\" : \"fa fa-eye\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx.resetPasswordForm.get(\"confirmPassword\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx.resetPasswordForm.get(\"confirmPassword\")) == null ? null : tmp_8_0.touched));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.loading || ctx.resetPasswordForm.invalid);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.loading ? \"Resetting...\" : \"Reset password\", \" \");\n        }\n      },\n      dependencies: [i4.NgIf, i3.RouterLink, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n      styles: [\"body[_ngcontent-%COMP%], html[_ngcontent-%COMP%]{margin:0;padding:0;overflow-x:hidden;height:100%}.image-container[_ngcontent-%COMP%]{width:100%;height:100%;position:relative}.image-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:80%;height:auto;position:absolute;right:0;bottom:0;object-fit:contain}.submit[_ngcontent-%COMP%]{background-color:#29578c;color:#fff}.forgot[_ngcontent-%COMP%]{color:#29578c;text-decoration:none;font-weight:700}a[_ngcontent-%COMP%]{cursor:pointer;text-decoration:none;color:#000;font-weight:600}.key[_ngcontent-%COMP%]{background-color:#f4ebff!important;border-radius:50%;height:50px;width:50px;display:flex;justify-content:center;align-items:center;color:#29578c}.resend[_ngcontent-%COMP%]{background-color:none;color:#29578c;font-weight:700;font-family:cursive}\"]\n    });\n  }\n  return ResetPasswordComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}