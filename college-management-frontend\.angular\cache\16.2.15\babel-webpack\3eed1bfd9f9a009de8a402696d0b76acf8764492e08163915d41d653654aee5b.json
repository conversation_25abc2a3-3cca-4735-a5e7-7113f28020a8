{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ClassesService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = environment.apiUrl;\n  }\n  // Get all classes with filters\n  getAllClasses(filters) {\n    let params = new HttpParams();\n    if (filters) {\n      Object.keys(filters).forEach(key => {\n        const value = filters[key];\n        if (value !== undefined && value !== null) {\n          params = params.set(key, value.toString());\n        }\n      });\n    }\n    return this.http.get(`${this.apiUrl}/get-classes`, {\n      params\n    });\n  }\n  // Get classes by program\n  getClassesByProgram(programId, isActive) {\n    let params = new HttpParams();\n    if (isActive !== undefined) params = params.set('isActive', isActive.toString());\n    return this.http.get(`${this.apiUrl}/classes/program/${programId}`, {\n      params\n    });\n  }\n  // Get classes by department\n  getClassesByDepartment(departmentId, isActive) {\n    let params = new HttpParams();\n    if (isActive !== undefined) params = params.set('isActive', isActive.toString());\n    return this.http.get(`${this.apiUrl}/classes/department/${departmentId}`, {\n      params\n    });\n  }\n  // Get classes by program and department\n  getClassesByProgramAndDepartment(programId, departmentId, isActive) {\n    let params = new HttpParams();\n    if (isActive !== undefined) params = params.set('isActive', isActive.toString());\n    return this.http.get(`${this.apiUrl}/classes/program/${programId}/department/${departmentId}`, {\n      params\n    });\n  }\n  // Get class by ID\n  getClassById(classId) {\n    return this.http.get(`${this.apiUrl}/class/${classId}`);\n  }\n  // Create a new class\n  createClass(classData) {\n    return this.http.post(`${this.apiUrl}/add-class`, classData);\n  }\n  // Update an existing class\n  updateClass(id, classData) {\n    return this.http.put(`${this.apiUrl}/update-class/${id}`, classData);\n  }\n  // Delete a class (soft delete)\n  deleteClass(id) {\n    return this.http.delete(`${this.apiUrl}/delete-class/${id}`);\n  }\n  // Get students in a class\n  getStudentsInClass(classId) {\n    return this.http.get(`${this.apiUrl}/class/${classId}/students`);\n  }\n  // Assign teacher to subject in class\n  assignTeacherToSubject(classId, subjectId, teacherId) {\n    return this.http.post(`${this.apiUrl}/class/${classId}/assign-teacher`, {\n      subjectId,\n      teacherId\n    });\n  }\n  // Legacy methods for backward compatibility\n  addClass(classData) {\n    return this.createClass(classData);\n  }\n  getClasses() {\n    return this.getAllClasses();\n  }\n  // Get all subjects (for dynamic subject selection)\n  getSubjects() {\n    return this.http.get(`${this.apiUrl}/subjects`);\n  }\n  static #_ = this.ɵfac = function ClassesService_Factory(t) {\n    return new (t || ClassesService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ClassesService,\n    factory: ClassesService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpParams", "environment", "ClassesService", "constructor", "http", "apiUrl", "getAllClasses", "filters", "params", "Object", "keys", "for<PERSON>ach", "key", "value", "undefined", "set", "toString", "get", "getClassesByProgram", "programId", "isActive", "getClassesByDepartment", "departmentId", "getClassesByProgramAndDepartment", "getClassById", "classId", "createClass", "classData", "post", "updateClass", "id", "put", "deleteClass", "delete", "getStudentsInClass", "assignTeacherToSubject", "subjectId", "teacherId", "addClass", "getClasses", "getSubjects", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\services\\classes.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { environment } from 'src/environments/environment';\r\nimport { Class } from '../models/user';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ClassesService {\r\n  private apiUrl = environment.apiUrl;\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  // Get all classes with filters\r\n  getAllClasses(filters?: {\r\n    program?: string;\r\n    department?: string;\r\n    semester?: number;\r\n    academicYear?: string;\r\n    isActive?: boolean;\r\n  }): Observable<any> {\r\n    let params = new HttpParams();\r\n\r\n    if (filters) {\r\n      Object.keys(filters).forEach(key => {\r\n        const value = (filters as any)[key];\r\n        if (value !== undefined && value !== null) {\r\n          params = params.set(key, value.toString());\r\n        }\r\n      });\r\n    }\r\n\r\n    return this.http.get<any>(`${this.apiUrl}/get-classes`, { params });\r\n  }\r\n\r\n  // Get classes by program\r\n  getClassesByProgram(programId: string, isActive?: boolean): Observable<any> {\r\n    let params = new HttpParams();\r\n    if (isActive !== undefined) params = params.set('isActive', isActive.toString());\r\n\r\n    return this.http.get<any>(`${this.apiUrl}/classes/program/${programId}`, { params });\r\n  }\r\n\r\n  // Get classes by department\r\n  getClassesByDepartment(departmentId: string, isActive?: boolean): Observable<any> {\r\n    let params = new HttpParams();\r\n    if (isActive !== undefined) params = params.set('isActive', isActive.toString());\r\n\r\n    return this.http.get<any>(`${this.apiUrl}/classes/department/${departmentId}`, { params });\r\n  }\r\n\r\n  // Get classes by program and department\r\n  getClassesByProgramAndDepartment(programId: string, departmentId: string, isActive?: boolean): Observable<any> {\r\n    let params = new HttpParams();\r\n    if (isActive !== undefined) params = params.set('isActive', isActive.toString());\r\n\r\n    return this.http.get<any>(`${this.apiUrl}/classes/program/${programId}/department/${departmentId}`, { params });\r\n  }\r\n\r\n  // Get class by ID\r\n  getClassById(classId: string): Observable<any> {\r\n    return this.http.get<any>(`${this.apiUrl}/class/${classId}`);\r\n  }\r\n\r\n  // Create a new class\r\n  createClass(classData: Partial<Class>): Observable<any> {\r\n    return this.http.post<any>(`${this.apiUrl}/add-class`, classData);\r\n  }\r\n\r\n  // Update an existing class\r\n  updateClass(id: string, classData: Partial<Class>): Observable<any> {\r\n    return this.http.put<any>(`${this.apiUrl}/update-class/${id}`, classData);\r\n  }\r\n\r\n  // Delete a class (soft delete)\r\n  deleteClass(id: string): Observable<any> {\r\n    return this.http.delete<any>(`${this.apiUrl}/delete-class/${id}`);\r\n  }\r\n\r\n  // Get students in a class\r\n  getStudentsInClass(classId: string): Observable<any> {\r\n    return this.http.get<any>(`${this.apiUrl}/class/${classId}/students`);\r\n  }\r\n\r\n  // Assign teacher to subject in class\r\n  assignTeacherToSubject(classId: string, subjectId: string, teacherId: string): Observable<any> {\r\n    return this.http.post<any>(`${this.apiUrl}/class/${classId}/assign-teacher`, {\r\n      subjectId,\r\n      teacherId\r\n    });\r\n  }\r\n\r\n  // Legacy methods for backward compatibility\r\n  addClass(classData: any): Observable<any> {\r\n    return this.createClass(classData);\r\n  }\r\n\r\n  getClasses(): Observable<any> {\r\n    return this.getAllClasses();\r\n  }\r\n\r\n  // Get all subjects (for dynamic subject selection)\r\n  getSubjects(): Observable<any> {\r\n    return this.http.get(`${this.apiUrl}/subjects`);\r\n  }\r\n}\r\n"], "mappings": "AACA,SAAkCA,UAAU,QAAQ,sBAAsB;AAE1E,SAASC,WAAW,QAAQ,8BAA8B;;;AAM1D,OAAM,MAAOC,cAAc;EAGzBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAGJ,WAAW,CAACI,MAAM;EAEI;EAEvC;EACAC,aAAaA,CAACC,OAMb;IACC,IAAIC,MAAM,GAAG,IAAIR,UAAU,EAAE;IAE7B,IAAIO,OAAO,EAAE;MACXE,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC,CAACI,OAAO,CAACC,GAAG,IAAG;QACjC,MAAMC,KAAK,GAAIN,OAAe,CAACK,GAAG,CAAC;QACnC,IAAIC,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,EAAE;UACzCL,MAAM,GAAGA,MAAM,CAACO,GAAG,CAACH,GAAG,EAAEC,KAAK,CAACG,QAAQ,EAAE,CAAC;;MAE9C,CAAC,CAAC;;IAGJ,OAAO,IAAI,CAACZ,IAAI,CAACa,GAAG,CAAM,GAAG,IAAI,CAACZ,MAAM,cAAc,EAAE;MAAEG;IAAM,CAAE,CAAC;EACrE;EAEA;EACAU,mBAAmBA,CAACC,SAAiB,EAAEC,QAAkB;IACvD,IAAIZ,MAAM,GAAG,IAAIR,UAAU,EAAE;IAC7B,IAAIoB,QAAQ,KAAKN,SAAS,EAAEN,MAAM,GAAGA,MAAM,CAACO,GAAG,CAAC,UAAU,EAAEK,QAAQ,CAACJ,QAAQ,EAAE,CAAC;IAEhF,OAAO,IAAI,CAACZ,IAAI,CAACa,GAAG,CAAM,GAAG,IAAI,CAACZ,MAAM,oBAAoBc,SAAS,EAAE,EAAE;MAAEX;IAAM,CAAE,CAAC;EACtF;EAEA;EACAa,sBAAsBA,CAACC,YAAoB,EAAEF,QAAkB;IAC7D,IAAIZ,MAAM,GAAG,IAAIR,UAAU,EAAE;IAC7B,IAAIoB,QAAQ,KAAKN,SAAS,EAAEN,MAAM,GAAGA,MAAM,CAACO,GAAG,CAAC,UAAU,EAAEK,QAAQ,CAACJ,QAAQ,EAAE,CAAC;IAEhF,OAAO,IAAI,CAACZ,IAAI,CAACa,GAAG,CAAM,GAAG,IAAI,CAACZ,MAAM,uBAAuBiB,YAAY,EAAE,EAAE;MAAEd;IAAM,CAAE,CAAC;EAC5F;EAEA;EACAe,gCAAgCA,CAACJ,SAAiB,EAAEG,YAAoB,EAAEF,QAAkB;IAC1F,IAAIZ,MAAM,GAAG,IAAIR,UAAU,EAAE;IAC7B,IAAIoB,QAAQ,KAAKN,SAAS,EAAEN,MAAM,GAAGA,MAAM,CAACO,GAAG,CAAC,UAAU,EAAEK,QAAQ,CAACJ,QAAQ,EAAE,CAAC;IAEhF,OAAO,IAAI,CAACZ,IAAI,CAACa,GAAG,CAAM,GAAG,IAAI,CAACZ,MAAM,oBAAoBc,SAAS,eAAeG,YAAY,EAAE,EAAE;MAAEd;IAAM,CAAE,CAAC;EACjH;EAEA;EACAgB,YAAYA,CAACC,OAAe;IAC1B,OAAO,IAAI,CAACrB,IAAI,CAACa,GAAG,CAAM,GAAG,IAAI,CAACZ,MAAM,UAAUoB,OAAO,EAAE,CAAC;EAC9D;EAEA;EACAC,WAAWA,CAACC,SAAyB;IACnC,OAAO,IAAI,CAACvB,IAAI,CAACwB,IAAI,CAAM,GAAG,IAAI,CAACvB,MAAM,YAAY,EAAEsB,SAAS,CAAC;EACnE;EAEA;EACAE,WAAWA,CAACC,EAAU,EAAEH,SAAyB;IAC/C,OAAO,IAAI,CAACvB,IAAI,CAAC2B,GAAG,CAAM,GAAG,IAAI,CAAC1B,MAAM,iBAAiByB,EAAE,EAAE,EAAEH,SAAS,CAAC;EAC3E;EAEA;EACAK,WAAWA,CAACF,EAAU;IACpB,OAAO,IAAI,CAAC1B,IAAI,CAAC6B,MAAM,CAAM,GAAG,IAAI,CAAC5B,MAAM,iBAAiByB,EAAE,EAAE,CAAC;EACnE;EAEA;EACAI,kBAAkBA,CAACT,OAAe;IAChC,OAAO,IAAI,CAACrB,IAAI,CAACa,GAAG,CAAM,GAAG,IAAI,CAACZ,MAAM,UAAUoB,OAAO,WAAW,CAAC;EACvE;EAEA;EACAU,sBAAsBA,CAACV,OAAe,EAAEW,SAAiB,EAAEC,SAAiB;IAC1E,OAAO,IAAI,CAACjC,IAAI,CAACwB,IAAI,CAAM,GAAG,IAAI,CAACvB,MAAM,UAAUoB,OAAO,iBAAiB,EAAE;MAC3EW,SAAS;MACTC;KACD,CAAC;EACJ;EAEA;EACAC,QAAQA,CAACX,SAAc;IACrB,OAAO,IAAI,CAACD,WAAW,CAACC,SAAS,CAAC;EACpC;EAEAY,UAAUA,CAAA;IACR,OAAO,IAAI,CAACjC,aAAa,EAAE;EAC7B;EAEA;EACAkC,WAAWA,CAAA;IACT,OAAO,IAAI,CAACpC,IAAI,CAACa,GAAG,CAAC,GAAG,IAAI,CAACZ,MAAM,WAAW,CAAC;EACjD;EAAC,QAAAoC,CAAA,G;qBAhGUvC,cAAc,EAAAwC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAd5C,cAAc;IAAA6C,OAAA,EAAd7C,cAAc,CAAA8C,IAAA;IAAAC,UAAA,EAFb;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}