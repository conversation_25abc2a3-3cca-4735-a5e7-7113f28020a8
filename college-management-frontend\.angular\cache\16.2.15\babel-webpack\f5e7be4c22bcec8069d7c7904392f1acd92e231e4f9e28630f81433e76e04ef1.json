{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'department',\n  loadChildren: () => import('./department/department.module').then(m => m.DepartmentModule)\n}, {\n  path: 'classes',\n  loadChildren: () => import('./classes/classes.module').then(m => m.ClassesModule)\n}, {\n  path: 'teachers',\n  loadChildren: () => import('./teacher/teacher.module').then(m => m.TeacherModule)\n}, {\n  path: 'students',\n  loadChildren: () => import('./students/students.module').then(m => m.StudentsModule)\n}, {\n  path: 'subjects',\n  loadChildren: () => import('./subject/subject.module').then(m => m.SubjectModule)\n}, {\n  path: 'users',\n  loadChildren: () => import('./users/users.module').then(m => m.UsersModule)\n}];\nexport let AdminDashboardRoutingModule = /*#__PURE__*/(() => {\n  class AdminDashboardRoutingModule {\n    static #_ = this.ɵfac = function AdminDashboardRoutingModule_Factory(t) {\n      return new (t || AdminDashboardRoutingModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AdminDashboardRoutingModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n  return AdminDashboardRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}