{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction EnterCodeManuallyComponent_p_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"We sent a verification code to \");\n    i0.ɵɵelementStart(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" to complete your registration.\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.user.email);\n  }\n}\nfunction EnterCodeManuallyComponent_div_18_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"OTP is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EnterCodeManuallyComponent_div_18_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Please enter a valid 6-digit OTP\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EnterCodeManuallyComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtemplate(1, EnterCodeManuallyComponent_div_18_small_1_Template, 2, 0, \"small\", 9);\n    i0.ɵɵtemplate(2, EnterCodeManuallyComponent_div_18_small_2_Template, 2, 0, \"small\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r1.otpForm.get(\"otp\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r1.otpForm.get(\"otp\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"pattern\"]);\n  }\n}\nfunction EnterCodeManuallyComponent_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 31);\n  }\n}\nfunction EnterCodeManuallyComponent_span_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 32);\n  }\n}\nexport class EnterCodeManuallyComponent {\n  constructor(fb, authService, router) {\n    this.fb = fb;\n    this.authService = authService;\n    this.router = router;\n    this.loading = false;\n    this.resendLoading = false;\n    this.user = null;\n    this.otpId = null;\n  }\n  ngOnInit() {\n    // This component is now only for signup verification\n    const signupUser = localStorage.getItem('signupUser');\n    if (signupUser) {\n      this.user = JSON.parse(signupUser);\n    } else {\n      // If no user data, redirect to signup\n      Swal.fire({\n        title: 'Session Expired',\n        text: 'Please complete the signup process first.',\n        icon: 'warning',\n        confirmButtonColor: '#29578c'\n      }).then(() => {\n        this.router.navigate(['/auth/sign-up']);\n      });\n      return;\n    }\n    this.otpForm = this.fb.group({\n      otp: ['', [Validators.required, Validators.pattern(/^\\d{6}$/)]]\n    });\n  }\n  verifyOtp() {\n    if (this.otpForm.valid && this.user) {\n      this.loading = true;\n      const otp = this.otpForm.value.otp;\n      this.authService.verifyOtp(this.user._id, otp).subscribe({\n        next: response => {\n          this.loading = false;\n          if (response.success) {\n            Swal.fire({\n              title: '<h1 class=\"mb-4\">Email Verified!</h1>',\n              html: '<p>Your email has been successfully verified. <br> You can now log in to your account.</p>',\n              icon: 'success',\n              confirmButtonText: 'Go to Login',\n              confirmButtonColor: '#29578c'\n            }).then(result => {\n              if (result.isConfirmed) {\n                // Clear signup data and go to login\n                localStorage.removeItem('signupUser');\n                this.router.navigate(['/auth']);\n              }\n            });\n          }\n        },\n        error: error => {\n          this.loading = false;\n          console.error('OTP verification error:', error);\n          const errorMessage = error.error?.error || error.error?.message || 'Invalid OTP. Please try again.';\n          Swal.fire({\n            title: 'Verification Failed',\n            text: errorMessage,\n            icon: 'error',\n            confirmButtonColor: '#29578c'\n          });\n        }\n      });\n    } else {\n      Swal.fire({\n        title: 'Invalid OTP',\n        text: 'Please enter a valid 6-digit OTP.',\n        icon: 'warning',\n        confirmButtonColor: '#29578c'\n      });\n    }\n  }\n  resendOtp() {\n    if (!this.user) return;\n    this.resendLoading = true;\n    const resendData = {\n      userId: this.user._id,\n      ...(this.otpId && {\n        otpId: this.otpId\n      })\n    };\n    this.authService.resendOtp(resendData).subscribe({\n      next: response => {\n        this.resendLoading = false;\n        if (response.success) {\n          if (response.otpId) {\n            this.otpId = response.otpId;\n          }\n          Swal.fire({\n            title: 'OTP Resent!',\n            text: 'A new OTP has been sent to your email.',\n            icon: 'success',\n            confirmButtonColor: '#29578c'\n          });\n        }\n      },\n      error: error => {\n        this.resendLoading = false;\n        console.error('Resend OTP error:', error);\n        const errorMessage = error.error?.message || 'Failed to resend OTP. Please try again.';\n        Swal.fire({\n          title: 'Resend Failed',\n          text: errorMessage,\n          icon: 'error',\n          confirmButtonColor: '#29578c'\n        });\n      }\n    });\n  }\n  // Legacy method for backward compatibility\n  verifyemail() {\n    this.verifyOtp();\n  }\n  static #_ = this.ɵfac = function EnterCodeManuallyComponent_Factory(t) {\n    return new (t || EnterCodeManuallyComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: EnterCodeManuallyComponent,\n    selectors: [[\"app-enter-code-manually\"]],\n    decls: 44,\n    vars: 11,\n    consts: [[1, \"container-fluid\"], [1, \"row\", \"vh-100\"], [1, \"col-lg-5\", \"d-flex\", \"flex-column\", \"justify-content-center\", \"align-items-center\", \"p-3\", \"bg-white\"], [1, \"w-100\", 2, \"max-width\", \"400px\"], [1, \"text-center\"], [1, \"mb-4\"], [\"src\", \"../../../assets/images/logo.jpeg\"], [1, \"d-flex\", \"justify-content-center\", \"mt-5\", \"mb-2\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"fa-envelope\", \"key\"], [4, \"ngIf\"], [3, \"formGroup\", \"ngSubmit\"], [\"for\", \"otp\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"otp\", \"formControlName\", \"otp\", \"placeholder\", \"000000\", \"maxlength\", \"6\", 1, \"form-control\", \"text-center\", 2, \"font-size\", \"1.5rem\", \"letter-spacing\", \"0.5rem\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [1, \"d-grid\", \"gap-2\"], [\"type\", \"submit\", 1, \"btn\", \"submit\", 3, \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", \"role\", \"status\", \"aria-hidden\", \"true\", 4, \"ngIf\"], [1, \"mb-3\", \"mt-3\", \"text-center\"], [\"type\", \"button\", 1, \"btn\", \"resend\", 3, \"disabled\", \"click\"], [\"class\", \"spinner-border spinner-border-sm me-1\", \"role\", \"status\", \"aria-hidden\", \"true\", 4, \"ngIf\"], [1, \"mt-3\", \"text-center\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"fa-arrow-left\"], [\"routerLink\", \"/auth\"], [1, \"col-lg-7\", \"d-none\", \"d-lg-flex\", \"flex-column\", \"align-items-start\", \"bg-light\", \"position-relative\"], [1, \"text-start\", \"p-5\", \"w-100\"], [1, \"blockquote\"], [1, \"blockquote-footer\"], [\"title\", \"Source Title\"], [1, \"image-container\"], [\"src\", \"../../../assets/images/background.jpeg\", \"alt\", \"Stats graph\", 1, \"img-fluid\", \"position-absolute\"], [1, \"invalid-feedback\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-1\"]],\n    template: function EnterCodeManuallyComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"h2\", 5);\n        i0.ɵɵelement(6, \"img\", 6);\n        i0.ɵɵtext(7, \" GPGC (Swabi)\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"div\", 7);\n        i0.ɵɵelement(9, \"i\", 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"h1\", 5);\n        i0.ɵɵtext(11, \"Verify Your Email\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(12, EnterCodeManuallyComponent_p_12_Template, 5, 1, \"p\", 9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"form\", 10);\n        i0.ɵɵlistener(\"ngSubmit\", function EnterCodeManuallyComponent_Template_form_ngSubmit_13_listener() {\n          return ctx.verifyOtp();\n        });\n        i0.ɵɵelementStart(14, \"div\", 5)(15, \"label\", 11);\n        i0.ɵɵtext(16, \"Enter 6-digit OTP\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(17, \"input\", 12);\n        i0.ɵɵtemplate(18, EnterCodeManuallyComponent_div_18_Template, 3, 2, \"div\", 13);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(19, \"div\", 14)(20, \"button\", 15);\n        i0.ɵɵtemplate(21, EnterCodeManuallyComponent_span_21_Template, 1, 0, \"span\", 16);\n        i0.ɵɵtext(22);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(23, \"p\", 17);\n        i0.ɵɵtext(24, \" Don't receive the email? \");\n        i0.ɵɵelementStart(25, \"button\", 18);\n        i0.ɵɵlistener(\"click\", function EnterCodeManuallyComponent_Template_button_click_25_listener() {\n          return ctx.resendOtp();\n        });\n        i0.ɵɵtemplate(26, EnterCodeManuallyComponent_span_26_Template, 1, 0, \"span\", 19);\n        i0.ɵɵtext(27);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(28, \"p\", 20);\n        i0.ɵɵelement(29, \"i\", 21);\n        i0.ɵɵtext(30, \" \\u00A0 \");\n        i0.ɵɵelementStart(31, \"a\", 22);\n        i0.ɵɵtext(32, \"Back to log in\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(33, \"div\", 23)(34, \"div\", 24)(35, \"blockquote\", 25)(36, \"h2\", 5);\n        i0.ɵɵtext(37, \"College management system Login page\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(38, \"footer\", 26);\n        i0.ɵɵtext(39, \"Name\");\n        i0.ɵɵelementStart(40, \"cite\", 27);\n        i0.ɵɵtext(41, \"Owner ~ GPGC SWABI\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(42, \"div\", 28);\n        i0.ɵɵelement(43, \"img\", 29);\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        let tmp_2_0;\n        let tmp_3_0;\n        i0.ɵɵadvance(12);\n        i0.ɵɵproperty(\"ngIf\", ctx.user);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"formGroup\", ctx.otpForm);\n        i0.ɵɵadvance(4);\n        i0.ɵɵclassProp(\"is-invalid\", ((tmp_2_0 = ctx.otpForm.get(\"otp\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.otpForm.get(\"otp\")) == null ? null : tmp_2_0.touched));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx.otpForm.get(\"otp\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.otpForm.get(\"otp\")) == null ? null : tmp_3_0.touched));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"disabled\", ctx.loading || ctx.otpForm.invalid);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate1(\" \", ctx.loading ? \"Verifying...\" : \"Verify Email\", \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"disabled\", ctx.resendLoading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.resendLoading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate1(\" \", ctx.resendLoading ? \"Sending...\" : \"Click to resend\", \" \");\n      }\n    },\n    dependencies: [i4.NgIf, i3.RouterLink, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MaxLengthValidator, i1.FormGroupDirective, i1.FormControlName],\n    styles: [\"body[_ngcontent-%COMP%], html[_ngcontent-%COMP%] {\\n    margin: 0;\\n    padding: 0;\\n    overflow-x: hidden; \\n\\n    height: 100%;\\n}\\n\\n\\n\\n.image-container[_ngcontent-%COMP%] {\\n    width: 100%;\\n    height: 100%;\\n    position: relative;\\n}\\n\\n.image-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    max-width: 80%;\\n    height: auto;\\n    position: absolute;\\n    right: 0;\\n    bottom: 0;\\n    object-fit: contain; \\n\\n}\\n.submit[_ngcontent-%COMP%]{\\n    background-color: #29578c;\\n    color: white;\\n}\\n.forgot[_ngcontent-%COMP%]{\\n    color: #29578c;\\n    text-decoration: none;\\n    font-weight: 700;\\n}\\na[_ngcontent-%COMP%]{\\n    cursor: pointer;\\n    text-decoration: none;\\n    color: black;\\n    font-weight: 600;\\n}\\n.key[_ngcontent-%COMP%]{\\n    background-color: #F4EBFF !important;\\n    border-radius: 50%;\\n    height: 50px;\\n    width: 50px;\\n    display: flex;\\n    justify-content: center;\\n    align-items: center;\\n    color: #29578c;\\n}\\n.resend[_ngcontent-%COMP%]{\\n    background-color: none;\\n    color:#29578c ;\\n    font-weight: 700;\\n    font-family: serif ;\\n}\\n.number-box[_ngcontent-%COMP%] {\\n    border: 2px solid #29578c;\\n    border-radius: 8px;\\n    text-align: center;\\n    font-size: 2rem;\\n    color: #29578c;\\n    padding: 10px;\\n  }\\n  \\n  .number-box[_ngcontent-%COMP%]:hover {\\n    box-shadow: 0 0 10px 3px rgba(138, 43, 178, 0.5);\\n  }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "user", "email", "ɵɵtemplate", "EnterCodeManuallyComponent_div_18_small_1_Template", "EnterCodeManuallyComponent_div_18_small_2_Template", "ɵɵproperty", "tmp_0_0", "ctx_r1", "otpForm", "get", "errors", "tmp_1_0", "ɵɵelement", "EnterCodeManuallyComponent", "constructor", "fb", "authService", "router", "loading", "resendLoading", "otpId", "ngOnInit", "signupUser", "localStorage", "getItem", "JSON", "parse", "fire", "title", "text", "icon", "confirmButtonColor", "then", "navigate", "group", "otp", "required", "pattern", "verifyOtp", "valid", "value", "_id", "subscribe", "next", "response", "success", "html", "confirmButtonText", "result", "isConfirmed", "removeItem", "error", "console", "errorMessage", "message", "resendOtp", "resendData", "userId", "verifyemail", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "_2", "selectors", "decls", "vars", "consts", "template", "EnterCodeManuallyComponent_Template", "rf", "ctx", "EnterCodeManuallyComponent_p_12_Template", "ɵɵlistener", "EnterCodeManuallyComponent_Template_form_ngSubmit_13_listener", "EnterCodeManuallyComponent_div_18_Template", "EnterCodeManuallyComponent_span_21_Template", "EnterCodeManuallyComponent_Template_button_click_25_listener", "EnterCodeManuallyComponent_span_26_Template", "ɵɵclassProp", "tmp_2_0", "invalid", "touched", "tmp_3_0", "ɵɵtextInterpolate1"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\auth\\enter-code-manually\\enter-code-manually.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\auth\\enter-code-manually\\enter-code-manually.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { AuthService } from 'src/app/services/auth.service';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-enter-code-manually',\r\n  templateUrl: './enter-code-manually.component.html',\r\n  styleUrls: ['./enter-code-manually.component.css']\r\n})\r\nexport class EnterCodeManuallyComponent implements OnInit {\r\n  otpForm!: FormGroup;\r\n  loading = false;\r\n  resendLoading = false;\r\n  user: any = null;\r\n  otpId: string | null = null;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private authService: AuthService,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    // This component is now only for signup verification\r\n    const signupUser = localStorage.getItem('signupUser');\r\n\r\n    if (signupUser) {\r\n      this.user = JSON.parse(signupUser);\r\n    } else {\r\n      // If no user data, redirect to signup\r\n      Swal.fire({\r\n        title: 'Session Expired',\r\n        text: 'Please complete the signup process first.',\r\n        icon: 'warning',\r\n        confirmButtonColor: '#29578c'\r\n      }).then(() => {\r\n        this.router.navigate(['/auth/sign-up']);\r\n      });\r\n      return;\r\n    }\r\n\r\n    this.otpForm = this.fb.group({\r\n      otp: ['', [Validators.required, Validators.pattern(/^\\d{6}$/)]]\r\n    });\r\n  }\r\n\r\n  verifyOtp(): void {\r\n    if (this.otpForm.valid && this.user) {\r\n      this.loading = true;\r\n      const otp = this.otpForm.value.otp;\r\n\r\n      this.authService.verifyOtp(this.user._id, otp).subscribe({\r\n        next: (response) => {\r\n          this.loading = false;\r\n          if (response.success) {\r\n            Swal.fire({\r\n              title: '<h1 class=\"mb-4\">Email Verified!</h1>',\r\n              html: '<p>Your email has been successfully verified. <br> You can now log in to your account.</p>',\r\n              icon: 'success',\r\n              confirmButtonText: 'Go to Login',\r\n              confirmButtonColor: '#29578c'\r\n            }).then((result) => {\r\n              if (result.isConfirmed) {\r\n                // Clear signup data and go to login\r\n                localStorage.removeItem('signupUser');\r\n                this.router.navigate(['/auth']);\r\n              }\r\n            });\r\n          }\r\n        },\r\n        error: (error) => {\r\n          this.loading = false;\r\n          console.error('OTP verification error:', error);\r\n\r\n          const errorMessage = error.error?.error || error.error?.message || 'Invalid OTP. Please try again.';\r\n\r\n          Swal.fire({\r\n            title: 'Verification Failed',\r\n            text: errorMessage,\r\n            icon: 'error',\r\n            confirmButtonColor: '#29578c'\r\n          });\r\n        }\r\n      });\r\n    } else {\r\n      Swal.fire({\r\n        title: 'Invalid OTP',\r\n        text: 'Please enter a valid 6-digit OTP.',\r\n        icon: 'warning',\r\n        confirmButtonColor: '#29578c'\r\n      });\r\n    }\r\n  }\r\n\r\n  resendOtp(): void {\r\n    if (!this.user) return;\r\n\r\n    this.resendLoading = true;\r\n    const resendData = {\r\n      userId: this.user._id,\r\n      ...(this.otpId && { otpId: this.otpId })\r\n    };\r\n\r\n    this.authService.resendOtp(resendData).subscribe({\r\n      next: (response) => {\r\n        this.resendLoading = false;\r\n        if (response.success) {\r\n          if (response.otpId) {\r\n            this.otpId = response.otpId;\r\n          }\r\n\r\n          Swal.fire({\r\n            title: 'OTP Resent!',\r\n            text: 'A new OTP has been sent to your email.',\r\n            icon: 'success',\r\n            confirmButtonColor: '#29578c'\r\n          });\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.resendLoading = false;\r\n        console.error('Resend OTP error:', error);\r\n\r\n        const errorMessage = error.error?.message || 'Failed to resend OTP. Please try again.';\r\n\r\n        Swal.fire({\r\n          title: 'Resend Failed',\r\n          text: errorMessage,\r\n          icon: 'error',\r\n          confirmButtonColor: '#29578c'\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  // Legacy method for backward compatibility\r\n  verifyemail(): void {\r\n    this.verifyOtp();\r\n  }\r\n}\r\n", "<div class=\"container-fluid\">\r\n    <div class=\"row vh-100\">\r\n        <div class=\"col-lg-5 d-flex flex-column justify-content-center align-items-center p-3 bg-white\">\r\n            <div class=\"w-100\" style=\"max-width: 400px;\">\r\n                <div class=\"text-center\">\r\n                <h2 class=\"mb-4\"><img src=\"../../../assets/images/logo.jpeg\"/> GPGC (Swabi)</h2>\r\n                <div class=\"d-flex justify-content-center mt-5 mb-2\">\r\n                    <i class=\"fa fa-envelope key\" aria-hidden=\"true\" ></i>\r\n                </div>\r\n\r\n                <h1 class=\"mb-4\">Verify Your Email</h1>\r\n                <p *ngIf=\"user\">We sent a verification code to <strong>{{ user.email }}</strong> to complete your registration.</p>\r\n            </div>\r\n\r\n            <form [formGroup]=\"otpForm\" (ngSubmit)=\"verifyOtp()\">\r\n                <div class=\"mb-4\">\r\n                    <label for=\"otp\" class=\"form-label\">Enter 6-digit OTP</label>\r\n                    <input\r\n                        type=\"text\"\r\n                        class=\"form-control text-center\"\r\n                        id=\"otp\"\r\n                        formControlName=\"otp\"\r\n                        placeholder=\"000000\"\r\n                        maxlength=\"6\"\r\n                        style=\"font-size: 1.5rem; letter-spacing: 0.5rem;\"\r\n                        [class.is-invalid]=\"otpForm.get('otp')?.invalid && otpForm.get('otp')?.touched\">\r\n                    <div class=\"invalid-feedback\" *ngIf=\"otpForm.get('otp')?.invalid && otpForm.get('otp')?.touched\">\r\n                        <small *ngIf=\"otpForm.get('otp')?.errors?.['required']\">OTP is required</small>\r\n                        <small *ngIf=\"otpForm.get('otp')?.errors?.['pattern']\">Please enter a valid 6-digit OTP</small>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"d-grid gap-2\">\r\n                    <button\r\n                        type=\"submit\"\r\n                        class=\"btn submit\"\r\n                        [disabled]=\"loading || otpForm.invalid\">\r\n                        <span *ngIf=\"loading\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\r\n                        {{ loading ? 'Verifying...' : 'Verify Email' }}\r\n                    </button>\r\n                </div>\r\n\r\n                <p class=\"mb-3 mt-3 text-center\">\r\n                    Don't receive the email?\r\n                    <button\r\n                        type=\"button\"\r\n                        class=\"btn resend\"\r\n                        (click)=\"resendOtp()\"\r\n                        [disabled]=\"resendLoading\">\r\n                        <span *ngIf=\"resendLoading\" class=\"spinner-border spinner-border-sm me-1\" role=\"status\" aria-hidden=\"true\"></span>\r\n                        {{ resendLoading ? 'Sending...' : 'Click to resend' }}\r\n                    </button>\r\n                </p>\r\n\r\n                <p class=\"mt-3 text-center\">\r\n                    <i class=\"fa fa-arrow-left\" aria-hidden=\"true\"></i> &nbsp;\r\n                    <a routerLink=\"/auth\">Back to log in</a>\r\n                </p>\r\n            </form>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-lg-7 d-none d-lg-flex flex-column align-items-start bg-light position-relative\">\r\n            <div class=\"text-start p-5 w-100\">\r\n                <blockquote class=\"blockquote\">\r\n                    <h2 class=\"mb-4\">College management system Login page</h2>\r\n                    <footer class=\"blockquote-footer\">Name<cite title=\"Source Title\">Owner ~ GPGC SWABI</cite></footer>\r\n                </blockquote>\r\n            </div>\r\n            <div class=\"image-container\">\r\n                <img src=\"../../../assets/images/background.jpeg\" class=\"img-fluid position-absolute\" alt=\"Stats graph\">\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAGnE,OAAOC,IAAI,MAAM,aAAa;;;;;;;;ICOdC,EAAA,CAAAC,cAAA,QAAgB;IAAAD,EAAA,CAAAE,MAAA,sCAA+B;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,sCAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAA5DH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAC,KAAA,CAAgB;;;;;IAgB/DR,EAAA,CAAAC,cAAA,YAAwD;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAC/EH,EAAA,CAAAC,cAAA,YAAuD;IAAAD,EAAA,CAAAE,MAAA,uCAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAFnGH,EAAA,CAAAC,cAAA,cAAiG;IAC7FD,EAAA,CAAAS,UAAA,IAAAC,kDAAA,mBAA+E;IAC/EV,EAAA,CAAAS,UAAA,IAAAE,kDAAA,mBAA+F;IACnGX,EAAA,CAAAG,YAAA,EAAM;;;;;;IAFMH,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAY,UAAA,UAAAC,OAAA,GAAAC,MAAA,CAAAC,OAAA,CAAAC,GAAA,0BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAA8C;IAC9CjB,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAY,UAAA,UAAAM,OAAA,GAAAJ,MAAA,CAAAC,OAAA,CAAAC,GAAA,0BAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,YAA6C;;;;;IASrDjB,EAAA,CAAAmB,SAAA,eAA4G;;;;;IAY5GnB,EAAA,CAAAmB,SAAA,eAAkH;;;ADtC1I,OAAM,MAAOC,0BAA0B;EAOrCC,YACUC,EAAe,EACfC,WAAwB,EACxBC,MAAc;IAFd,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IARhB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAnB,IAAI,GAAQ,IAAI;IAChB,KAAAoB,KAAK,GAAkB,IAAI;EAMxB;EAEHC,QAAQA,CAAA;IACN;IACA,MAAMC,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAErD,IAAIF,UAAU,EAAE;MACd,IAAI,CAACtB,IAAI,GAAGyB,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC;KACnC,MAAM;MACL;MACA9B,IAAI,CAACmC,IAAI,CAAC;QACRC,KAAK,EAAE,iBAAiB;QACxBC,IAAI,EAAE,2CAA2C;QACjDC,IAAI,EAAE,SAAS;QACfC,kBAAkB,EAAE;OACrB,CAAC,CAACC,IAAI,CAAC,MAAK;QACX,IAAI,CAACf,MAAM,CAACgB,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;MACzC,CAAC,CAAC;MACF;;IAGF,IAAI,CAACzB,OAAO,GAAG,IAAI,CAACO,EAAE,CAACmB,KAAK,CAAC;MAC3BC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC5C,UAAU,CAAC6C,QAAQ,EAAE7C,UAAU,CAAC8C,OAAO,CAAC,SAAS,CAAC,CAAC;KAC/D,CAAC;EACJ;EAEAC,SAASA,CAAA;IACP,IAAI,IAAI,CAAC9B,OAAO,CAAC+B,KAAK,IAAI,IAAI,CAACvC,IAAI,EAAE;MACnC,IAAI,CAACkB,OAAO,GAAG,IAAI;MACnB,MAAMiB,GAAG,GAAG,IAAI,CAAC3B,OAAO,CAACgC,KAAK,CAACL,GAAG;MAElC,IAAI,CAACnB,WAAW,CAACsB,SAAS,CAAC,IAAI,CAACtC,IAAI,CAACyC,GAAG,EAAEN,GAAG,CAAC,CAACO,SAAS,CAAC;QACvDC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAC1B,OAAO,GAAG,KAAK;UACpB,IAAI0B,QAAQ,CAACC,OAAO,EAAE;YACpBrD,IAAI,CAACmC,IAAI,CAAC;cACRC,KAAK,EAAE,uCAAuC;cAC9CkB,IAAI,EAAE,4FAA4F;cAClGhB,IAAI,EAAE,SAAS;cACfiB,iBAAiB,EAAE,aAAa;cAChChB,kBAAkB,EAAE;aACrB,CAAC,CAACC,IAAI,CAAEgB,MAAM,IAAI;cACjB,IAAIA,MAAM,CAACC,WAAW,EAAE;gBACtB;gBACA1B,YAAY,CAAC2B,UAAU,CAAC,YAAY,CAAC;gBACrC,IAAI,CAACjC,MAAM,CAACgB,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;;YAEnC,CAAC,CAAC;;QAEN,CAAC;QACDkB,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACjC,OAAO,GAAG,KAAK;UACpBkC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAE/C,MAAME,YAAY,GAAGF,KAAK,CAACA,KAAK,EAAEA,KAAK,IAAIA,KAAK,CAACA,KAAK,EAAEG,OAAO,IAAI,gCAAgC;UAEnG9D,IAAI,CAACmC,IAAI,CAAC;YACRC,KAAK,EAAE,qBAAqB;YAC5BC,IAAI,EAAEwB,YAAY;YAClBvB,IAAI,EAAE,OAAO;YACbC,kBAAkB,EAAE;WACrB,CAAC;QACJ;OACD,CAAC;KACH,MAAM;MACLvC,IAAI,CAACmC,IAAI,CAAC;QACRC,KAAK,EAAE,aAAa;QACpBC,IAAI,EAAE,mCAAmC;QACzCC,IAAI,EAAE,SAAS;QACfC,kBAAkB,EAAE;OACrB,CAAC;;EAEN;EAEAwB,SAASA,CAAA;IACP,IAAI,CAAC,IAAI,CAACvD,IAAI,EAAE;IAEhB,IAAI,CAACmB,aAAa,GAAG,IAAI;IACzB,MAAMqC,UAAU,GAAG;MACjBC,MAAM,EAAE,IAAI,CAACzD,IAAI,CAACyC,GAAG;MACrB,IAAI,IAAI,CAACrB,KAAK,IAAI;QAAEA,KAAK,EAAE,IAAI,CAACA;MAAK,CAAE;KACxC;IAED,IAAI,CAACJ,WAAW,CAACuC,SAAS,CAACC,UAAU,CAAC,CAACd,SAAS,CAAC;MAC/CC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACzB,aAAa,GAAG,KAAK;QAC1B,IAAIyB,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAID,QAAQ,CAACxB,KAAK,EAAE;YAClB,IAAI,CAACA,KAAK,GAAGwB,QAAQ,CAACxB,KAAK;;UAG7B5B,IAAI,CAACmC,IAAI,CAAC;YACRC,KAAK,EAAE,aAAa;YACpBC,IAAI,EAAE,wCAAwC;YAC9CC,IAAI,EAAE,SAAS;YACfC,kBAAkB,EAAE;WACrB,CAAC;;MAEN,CAAC;MACDoB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAChC,aAAa,GAAG,KAAK;QAC1BiC,OAAO,CAACD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;QAEzC,MAAME,YAAY,GAAGF,KAAK,CAACA,KAAK,EAAEG,OAAO,IAAI,yCAAyC;QAEtF9D,IAAI,CAACmC,IAAI,CAAC;UACRC,KAAK,EAAE,eAAe;UACtBC,IAAI,EAAEwB,YAAY;UAClBvB,IAAI,EAAE,OAAO;UACbC,kBAAkB,EAAE;SACrB,CAAC;MACJ;KACD,CAAC;EACJ;EAEA;EACA2B,WAAWA,CAAA;IACT,IAAI,CAACpB,SAAS,EAAE;EAClB;EAAC,QAAAqB,CAAA,G;qBAjIU9C,0BAA0B,EAAApB,EAAA,CAAAmE,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAArE,EAAA,CAAAmE,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAvE,EAAA,CAAAmE,iBAAA,CAAAK,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA1BtD,0BAA0B;IAAAuD,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCXvCjF,EAAA,CAAAC,cAAA,aAA6B;QAKID,EAAA,CAAAmB,SAAA,aAA6C;QAACnB,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAChFH,EAAA,CAAAC,cAAA,aAAqD;QACjDD,EAAA,CAAAmB,SAAA,WAAsD;QAC1DnB,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,aAAiB;QAAAD,EAAA,CAAAE,MAAA,yBAAiB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACvCH,EAAA,CAAAS,UAAA,KAAA0E,wCAAA,eAAmH;QACvHnF,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,gBAAqD;QAAzBD,EAAA,CAAAoF,UAAA,sBAAAC,8DAAA;UAAA,OAAYH,GAAA,CAAArC,SAAA,EAAW;QAAA,EAAC;QAChD7C,EAAA,CAAAC,cAAA,cAAkB;QACsBD,EAAA,CAAAE,MAAA,yBAAiB;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC7DH,EAAA,CAAAmB,SAAA,iBAQoF;QACpFnB,EAAA,CAAAS,UAAA,KAAA6E,0CAAA,kBAGM;QACVtF,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,eAA0B;QAKlBD,EAAA,CAAAS,UAAA,KAAA8E,2CAAA,mBAA4G;QAC5GvF,EAAA,CAAAE,MAAA,IACJ;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAGbH,EAAA,CAAAC,cAAA,aAAiC;QAC7BD,EAAA,CAAAE,MAAA,kCACA;QAAAF,EAAA,CAAAC,cAAA,kBAI+B;QAD3BD,EAAA,CAAAoF,UAAA,mBAAAI,6DAAA;UAAA,OAASN,GAAA,CAAApB,SAAA,EAAW;QAAA,EAAC;QAErB9D,EAAA,CAAAS,UAAA,KAAAgF,2CAAA,mBAAkH;QAClHzF,EAAA,CAAAE,MAAA,IACJ;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAGbH,EAAA,CAAAC,cAAA,aAA4B;QACxBD,EAAA,CAAAmB,SAAA,aAAmD;QAACnB,EAAA,CAAAE,MAAA,gBACpD;QAAAF,EAAA,CAAAC,cAAA,aAAsB;QAAAD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAKpDH,EAAA,CAAAC,cAAA,eAAgG;QAGnED,EAAA,CAAAE,MAAA,4CAAoC;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC1DH,EAAA,CAAAC,cAAA,kBAAkC;QAAAD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAC,cAAA,gBAA2B;QAAAD,EAAA,CAAAE,MAAA,0BAAkB;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAGlGH,EAAA,CAAAC,cAAA,eAA6B;QACzBD,EAAA,CAAAmB,SAAA,eAAwG;QAC5GnB,EAAA,CAAAG,YAAA,EAAM;;;;;QA3DEH,EAAA,CAAAI,SAAA,IAAU;QAAVJ,EAAA,CAAAY,UAAA,SAAAsE,GAAA,CAAA3E,IAAA,CAAU;QAGZP,EAAA,CAAAI,SAAA,GAAqB;QAArBJ,EAAA,CAAAY,UAAA,cAAAsE,GAAA,CAAAnE,OAAA,CAAqB;QAWff,EAAA,CAAAI,SAAA,GAA+E;QAA/EJ,EAAA,CAAA0F,WAAA,iBAAAC,OAAA,GAAAT,GAAA,CAAAnE,OAAA,CAAAC,GAAA,0BAAA2E,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAT,GAAA,CAAAnE,OAAA,CAAAC,GAAA,0BAAA2E,OAAA,CAAAE,OAAA,EAA+E;QACpD7F,EAAA,CAAAI,SAAA,GAAgE;QAAhEJ,EAAA,CAAAY,UAAA,WAAAkF,OAAA,GAAAZ,GAAA,CAAAnE,OAAA,CAAAC,GAAA,0BAAA8E,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAAZ,GAAA,CAAAnE,OAAA,CAAAC,GAAA,0BAAA8E,OAAA,CAAAD,OAAA,EAAgE;QAU3F7F,EAAA,CAAAI,SAAA,GAAuC;QAAvCJ,EAAA,CAAAY,UAAA,aAAAsE,GAAA,CAAAzD,OAAA,IAAAyD,GAAA,CAAAnE,OAAA,CAAA6E,OAAA,CAAuC;QAChC5F,EAAA,CAAAI,SAAA,GAAa;QAAbJ,EAAA,CAAAY,UAAA,SAAAsE,GAAA,CAAAzD,OAAA,CAAa;QACpBzB,EAAA,CAAAI,SAAA,GACJ;QADIJ,EAAA,CAAA+F,kBAAA,MAAAb,GAAA,CAAAzD,OAAA,wCACJ;QASIzB,EAAA,CAAAI,SAAA,GAA0B;QAA1BJ,EAAA,CAAAY,UAAA,aAAAsE,GAAA,CAAAxD,aAAA,CAA0B;QACnB1B,EAAA,CAAAI,SAAA,GAAmB;QAAnBJ,EAAA,CAAAY,UAAA,SAAAsE,GAAA,CAAAxD,aAAA,CAAmB;QAC1B1B,EAAA,CAAAI,SAAA,GACJ;QADIJ,EAAA,CAAA+F,kBAAA,MAAAb,GAAA,CAAAxD,aAAA,yCACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}