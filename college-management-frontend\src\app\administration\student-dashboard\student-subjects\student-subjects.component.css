.student-subjects-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.header-content h1 {
  margin: 0;
  color: #333;
  font-size: 2rem;
  font-weight: 500;
}

.header-content p {
  margin: 5px 0 0 0;
  color: #666;
  font-size: 1rem;
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-container p, .error-container p {
  margin-top: 20px;
  color: #666;
}

.error-container mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  color: #f44336;
  margin-bottom: 20px;
}

.subjects-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon mat-icon {
  font-size: 32px;
  width: 32px;
  height: 32px;
  opacity: 0.8;
}

.stat-info h3 {
  margin: 0 0 5px 0;
  font-size: 1.8rem;
  font-weight: 600;
}

.stat-info p {
  margin: 0;
  opacity: 0.9;
  font-size: 0.9rem;
}

.subjects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 25px;
}

.subject-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.subject-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.subject-avatar {
  background: #2196f3;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.subject-avatar mat-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
}

.subject-details {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.attendance-summary {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.attendance-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1rem;
  color: white;
  flex-shrink: 0;
}

.attendance-circle.good {
  background: linear-gradient(135deg, #4caf50, #2e7d32);
}

.attendance-circle.warning {
  background: linear-gradient(135deg, #ff9800, #f57c00);
}

.attendance-circle.danger {
  background: linear-gradient(135deg, #f44336, #d32f2f);
}

.attendance-info h4 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 500;
}

.attendance-info p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.progress-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-fill.good {
  background: linear-gradient(90deg, #4caf50, #2e7d32);
}

.progress-fill.warning {
  background: linear-gradient(90deg, #ff9800, #f57c00);
}

.progress-fill.danger {
  background: linear-gradient(90deg, #f44336, #d32f2f);
}

.progress-labels {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: #666;
}

.subject-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.stat-item mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  color: #2196f3;
}

.stat-details {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-weight: 600;
  font-size: 1.1rem;
  color: #333;
}

.stat-label {
  font-size: 0.8rem;
  color: #666;
}

.no-data-card {
  margin: 40px 0;
}

.no-data-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 40px 20px;
}

.no-data-content mat-icon {
  font-size: 64px;
  width: 64px;
  height: 64px;
  color: #ccc;
  margin-bottom: 20px;
}

.no-data-content h3 {
  margin: 0 0 10px 0;
  color: #666;
  font-size: 1.3rem;
}

.no-data-content p {
  margin: 0;
  color: #999;
  font-size: 1rem;
}

.quick-actions {
  margin-top: 30px;
}

.quick-actions h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.3rem;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.action-buttons button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
}

.action-buttons button mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
}

@media (max-width: 768px) {
  .student-subjects-container {
    padding: 15px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .summary-stats {
    grid-template-columns: 1fr;
  }
  
  .subjects-grid {
    grid-template-columns: 1fr;
  }
  
  .attendance-summary {
    flex-direction: column;
    text-align: center;
  }
  
  .subject-stats {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons button {
    width: 100%;
    justify-content: center;
  }
}
