{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ClassTableComponent } from './class-table/class-table.component';\nimport { ClassFormComponent } from './class-form/class-form.component';\nimport { ViewClassComponent } from './view-class/view-class.component';\nimport { StudentbyclassComponent } from './studentbyclass/studentbyclass.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ClassTableComponent\n}, {\n  path: 'add-class',\n  component: ClassFormComponent\n}, {\n  path: 'view-class',\n  component: ViewClassComponent\n}, {\n  path: 'student-by-class/:id',\n  component: StudentbyclassComponent\n}];\nexport let ClassesRoutingModule = /*#__PURE__*/(() => {\n  class ClassesRoutingModule {\n    static #_ = this.ɵfac = function ClassesRoutingModule_Factory(t) {\n      return new (t || ClassesRoutingModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ClassesRoutingModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n  return ClassesRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}