const Timetable = require('../models/timetable.js');
const User = require('../models/userModel.js');
const Program = require('../models/program.js');
const Department = require('../models/department.js');
const Class = require('../models/classes.js');
const Subject = require('../models/subject.js');

// Create a new timetable entry
const createTimetableEntry = async (req, res) => {
    try {
        const {
            program,
            department,
            class: classId,
            subject,
            teacher,
            dayOfWeek,
            timeSlot,
            room,
            semester,
            academicYear,
            notes
        } = req.body;

        // Validate required fields
        if (!program || !department || !classId || !subject || !teacher || !dayOfWeek || !timeSlot || !semester || !academicYear) {
            return res.status(400).json({
                success: false,
                message: 'All required fields must be provided'
            });
        }

        // Validate program exists
        const programData = await Program.findById(program);
        if (!programData) {
            return res.status(400).json({
                success: false,
                message: 'Invalid program selected'
            });
        }

        // Validate department exists and belongs to program
        const departmentData = await Department.findById(department);
        if (!departmentData || departmentData.program.toString() !== program) {
            return res.status(400).json({
                success: false,
                message: 'Invalid department selected for this program'
            });
        }

        // Validate class exists and belongs to program/department
        const classData = await Class.findById(classId);
        if (!classData || classData.program.toString() !== program || classData.department.toString() !== department) {
            return res.status(400).json({
                success: false,
                message: 'Invalid class selected for this program and department'
            });
        }

        // Validate subject exists and belongs to program/department
        const subjectData = await Subject.findById(subject);
        if (!subjectData || subjectData.program.toString() !== program || subjectData.department.toString() !== department) {
            return res.status(400).json({
                success: false,
                message: 'Invalid subject selected for this program and department'
            });
        }

        // Validate teacher exists and has Teacher role
        const teacherUser = await User.findById(teacher);
        if (!teacherUser || teacherUser.role !== 'Teacher') {
            return res.status(400).json({
                success: false,
                message: 'Invalid teacher selected'
            });
        }

        // Validate teacher is assigned to this program/department
        if (teacherUser.program && teacherUser.program.toString() !== program) {
            return res.status(400).json({
                success: false,
                message: 'Teacher is not assigned to this program'
            });
        }

        if (teacherUser.department && teacherUser.department.toString() !== department) {
            return res.status(400).json({
                success: false,
                message: 'Teacher is not assigned to this department'
            });
        }

        // Helper functions for time calculations
        const calculateDuration = (start, end) => {
            const [startHour, startMin] = start.split(':').map(Number);
            const [endHour, endMin] = end.split(':').map(Number);
            return (endHour * 60 + endMin) - (startHour * 60 + startMin);
        };

        const calculateEndTime = (start, durationMinutes) => {
            const [startHour, startMin] = start.split(':').map(Number);
            const totalMinutes = startHour * 60 + startMin + durationMinutes;
            const endHour = Math.floor(totalMinutes / 60);
            const endMin = totalMinutes % 60;
            return `${endHour.toString().padStart(2, '0')}:${endMin.toString().padStart(2, '0')}`;
        };

        // Ensure we have all required timeSlot fields
        let duration, endTime;

        if (timeSlot.endTime && timeSlot.startTime) {
            // If both start and end times are provided, calculate duration
            duration = timeSlot.duration || calculateDuration(timeSlot.startTime, timeSlot.endTime);
            endTime = timeSlot.endTime;
        } else if (timeSlot.startTime && timeSlot.duration) {
            // If start time and duration are provided, calculate end time
            duration = timeSlot.duration;
            endTime = calculateEndTime(timeSlot.startTime, duration);
        } else {
            return res.status(400).json({
                success: false,
                message: 'Either provide startTime and endTime, or startTime and duration'
            });
        }

        // Check for class conflicts (same class can't have multiple subjects at same time)
        const classConflict = await Timetable.findOne({
            class: classId,
            dayOfWeek,
            academicYear,
            isActive: true,
            $or: [
                {
                    $and: [
                        { 'timeSlot.startTime': { $lt: endTime } },
                        { 'timeSlot.endTime': { $gt: timeSlot.startTime } }
                    ]
                }
            ]
        });

        if (classConflict) {
            return res.status(400).json({
                success: false,
                message: `Class already has a subject scheduled from ${classConflict.timeSlot.startTime} to ${classConflict.timeSlot.endTime} on ${dayOfWeek}`
            });
        }

        // Check for teacher time conflicts
        const teacherConflict = await Timetable.findOne({
            teacher,
            dayOfWeek,
            academicYear,
            isActive: true,
            $or: [
                {
                    $and: [
                        { 'timeSlot.startTime': { $lt: endTime } },
                        { 'timeSlot.endTime': { $gt: timeSlot.startTime } }
                    ]
                }
            ]
        });

        if (teacherConflict) {
            return res.status(400).json({
                success: false,
                message: `Teacher already has a class scheduled from ${teacherConflict.timeSlot.startTime} to ${teacherConflict.timeSlot.endTime} on ${dayOfWeek}`
            });
        }

        // Check for room conflicts if room is specified
        if (room) {
            const roomConflict = await Timetable.findOne({
                room,
                dayOfWeek,
                academicYear,
                isActive: true,
                $or: [
                    {
                        $and: [
                            { 'timeSlot.startTime': { $lt: endTime } },
                            { 'timeSlot.endTime': { $gt: timeSlot.startTime } }
                        ]
                    }
                ]
            });

            if (roomConflict) {
                return res.status(400).json({
                    success: false,
                    message: `Room ${room} is already booked from ${roomConflict.timeSlot.startTime} to ${roomConflict.timeSlot.endTime} on ${dayOfWeek}`
                });
            }
        }

        // Program data is already validated above, use the existing programData variable

        // Validate duration based on program type
        const validDurations = {
            'Intermediate': [40, 60], // 40 minutes and 1 hour for Intermediate
            'BS': [60, 90, 120],      // 1 hour, 1.5 hours, 2 hours for BS
            'MS': [60, 90, 120],      // 1 hour, 1.5 hours, 2 hours for MS
            'PhD': [90, 120, 180]     // 1.5 hours, 2 hours, 3 hours for PhD
        };

        const allowedDurations = validDurations[programData.name] || [60];
        if (!allowedDurations.includes(duration)) {
            return res.status(400).json({
                success: false,
                message: `Invalid duration for ${programData.name} program. Allowed durations: ${allowedDurations.join(', ')} minutes`
            });
        }
        
        const timetableEntry = new Timetable({
            program,
            department,
            class: classId,
            subject,
            teacher,
            dayOfWeek,
            timeSlot: {
                startTime: timeSlot.startTime,
                endTime: endTime,
                duration: duration
            },
            room,
            semester,
            academicYear,
            notes
        });

        await timetableEntry.save();
        
        // Populate references for response
        await timetableEntry.populate([
            { path: 'program' },
            { path: 'department' },
            { path: 'class' },
            { path: 'subject' },
            { path: 'teacher', select: 'name email' }
        ]);

        res.status(201).json({
            success: true,
            message: 'Timetable entry created successfully',
            timetable: timetableEntry
        });
    } catch (error) {
        console.error('Error creating timetable entry:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

// Get all timetable entries with filters
const getAllTimetableEntries = async (req, res) => {
    try {
        const {
            program,
            department,
            class: classId,
            teacher,
            dayOfWeek,
            academicYear,
            semester,
            isActive
        } = req.query;

        const filter = {};
        
        if (program) filter.program = program;
        if (department) filter.department = department;
        if (classId) filter.class = classId;
        if (teacher) filter.teacher = teacher;
        if (dayOfWeek) filter.dayOfWeek = dayOfWeek;
        if (academicYear) filter.academicYear = academicYear;
        if (semester) filter.semester = parseInt(semester);
        if (isActive !== undefined) filter.isActive = isActive === 'true';

        const timetableEntries = await Timetable.find(filter)
            .populate([
                { path: 'program' },
                { path: 'department' },
                { path: 'class' },
                { path: 'subject' },
                { path: 'teacher', select: 'name email' }
            ])
            .sort({ dayOfWeek: 1, 'timeSlot.startTime': 1 });

        res.status(200).json({
            success: true,
            timetable: timetableEntries,
            count: timetableEntries.length
        });
    } catch (error) {
        console.error('Error fetching timetable entries:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

// Get timetable by teacher
const getTimetableByTeacher = async (req, res) => {
    try {
        const { teacherId } = req.params;
        const { academicYear, dayOfWeek } = req.query;

        const filter = { 
            teacher: teacherId, 
            isActive: true 
        };
        
        if (academicYear) filter.academicYear = academicYear;
        if (dayOfWeek) filter.dayOfWeek = dayOfWeek;

        const timetableEntries = await Timetable.find(filter)
            .populate([
                { path: 'program' },
                { path: 'department' },
                { path: 'class' },
                { path: 'subject' }
            ])
            .sort({ dayOfWeek: 1, 'timeSlot.startTime': 1 });

        res.status(200).json({
            success: true,
            timetable: timetableEntries,
            count: timetableEntries.length
        });
    } catch (error) {
        console.error('Error fetching teacher timetable:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

// Get timetable by class
const getTimetableByClass = async (req, res) => {
    try {
        const { classId } = req.params;
        const { academicYear, dayOfWeek } = req.query;

        const filter = { 
            class: classId, 
            isActive: true 
        };
        
        if (academicYear) filter.academicYear = academicYear;
        if (dayOfWeek) filter.dayOfWeek = dayOfWeek;

        const timetableEntries = await Timetable.find(filter)
            .populate([
                { path: 'program' },
                { path: 'department' },
                { path: 'subject' },
                { path: 'teacher', select: 'name email' }
            ])
            .sort({ dayOfWeek: 1, 'timeSlot.startTime': 1 });

        res.status(200).json({
            success: true,
            timetable: timetableEntries,
            count: timetableEntries.length
        });
    } catch (error) {
        console.error('Error fetching class timetable:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

// Update timetable entry
const updateTimetableEntry = async (req, res) => {
    try {
        const { id } = req.params;
        const updateData = req.body;

        // If updating time slot, check for conflicts
        if (updateData.timeSlot || updateData.teacher || updateData.dayOfWeek) {
            const existingEntry = await Timetable.findById(id);
            if (!existingEntry) {
                return res.status(404).json({
                    success: false,
                    message: 'Timetable entry not found'
                });
            }

            const timeSlot = updateData.timeSlot || existingEntry.timeSlot;
            const teacher = updateData.teacher || existingEntry.teacher;
            const dayOfWeek = updateData.dayOfWeek || existingEntry.dayOfWeek;
            const academicYear = updateData.academicYear || existingEntry.academicYear;

            // Check for teacher conflicts (excluding current entry)
            const teacherConflict = await Timetable.findOne({
                _id: { $ne: id },
                teacher,
                dayOfWeek,
                academicYear,
                isActive: true,
                $or: [
                    {
                        $and: [
                            { 'timeSlot.startTime': { $lt: timeSlot.endTime } },
                            { 'timeSlot.endTime': { $gt: timeSlot.startTime } }
                        ]
                    }
                ]
            });

            if (teacherConflict) {
                return res.status(400).json({
                    success: false,
                    message: `Teacher already has a class scheduled from ${teacherConflict.timeSlot.startTime} to ${teacherConflict.timeSlot.endTime} on ${dayOfWeek}`
                });
            }
        }

        // Calculate duration if time slot is being updated
        if (updateData.timeSlot) {
            const calculateDuration = (start, end) => {
                const [startHour, startMin] = start.split(':').map(Number);
                const [endHour, endMin] = end.split(':').map(Number);
                return (endHour * 60 + endMin) - (startHour * 60 + startMin);
            };

            updateData.timeSlot.duration = calculateDuration(
                updateData.timeSlot.startTime,
                updateData.timeSlot.endTime
            );
        }

        const updatedEntry = await Timetable.findByIdAndUpdate(
            id,
            updateData,
            { new: true, runValidators: true }
        ).populate([
            { path: 'program' },
            { path: 'department' },
            { path: 'class' },
            { path: 'subject' },
            { path: 'teacher', select: 'name email' }
        ]);

        if (!updatedEntry) {
            return res.status(404).json({
                success: false,
                message: 'Timetable entry not found'
            });
        }

        res.status(200).json({
            success: true,
            message: 'Timetable entry updated successfully',
            timetable: updatedEntry
        });
    } catch (error) {
        console.error('Error updating timetable entry:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

// Delete timetable entry
const deleteTimetableEntry = async (req, res) => {
    try {
        const { id } = req.params;

        const deletedEntry = await Timetable.findByIdAndUpdate(
            id,
            { isActive: false },
            { new: true }
        );

        if (!deletedEntry) {
            return res.status(404).json({
                success: false,
                message: 'Timetable entry not found'
            });
        }

        res.status(200).json({
            success: true,
            message: 'Timetable entry deleted successfully'
        });
    } catch (error) {
        console.error('Error deleting timetable entry:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

// Get timetable entry by ID
const getTimetableEntryById = async (req, res) => {
    try {
        const { id } = req.params;

        const timetableEntry = await Timetable.findById(id)
            .populate([
                { path: 'program' },
                { path: 'department' },
                { path: 'class' },
                { path: 'subject' },
                { path: 'teacher', select: 'name email' }
            ]);

        if (!timetableEntry) {
            return res.status(404).json({
                success: false,
                message: 'Timetable entry not found'
            });
        }

        res.status(200).json({
            success: true,
            timetable: timetableEntry
        });
    } catch (error) {
        console.error('Error fetching timetable entry:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

// Get timetable by student (based on their class)
const getTimetableByStudent = async (req, res) => {
    try {
        const { studentId } = req.params;
        const { academicYear, dayOfWeek } = req.query;

        // Get student information
        const student = await User.findById(studentId)
            .populate('classId')
            .populate('program')
            .populate('department');

        if (!student || student.role !== 'Student') {
            return res.status(404).json({
                success: false,
                message: 'Student not found'
            });
        }

        if (!student.classId) {
            return res.status(400).json({
                success: false,
                message: 'Student is not assigned to any class'
            });
        }

        const filter = {
            class: student.classId._id,
            isActive: true
        };

        if (academicYear) filter.academicYear = academicYear;
        if (dayOfWeek) filter.dayOfWeek = dayOfWeek;

        const timetableEntries = await Timetable.find(filter)
            .populate([
                { path: 'program' },
                { path: 'department' },
                { path: 'class' },
                { path: 'subject' },
                { path: 'teacher', select: 'name email designation' }
            ])
            .sort({ dayOfWeek: 1, 'timeSlot.startTime': 1 });

        res.status(200).json({
            success: true,
            timetable: timetableEntries,
            count: timetableEntries.length,
            studentInfo: {
                name: student.name,
                class: student.classId.className,
                section: student.classId.section,
                program: student.program?.name,
                department: student.department?.name,
                semester: student.semester
            }
        });
    } catch (error) {
        console.error('Error fetching student timetable:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

module.exports = {
    createTimetableEntry,
    getAllTimetableEntries,
    getTimetableByTeacher,
    getTimetableByClass,
    getTimetableByStudent,
    updateTimetableEntry,
    deleteTimetableEntry,
    getTimetableEntryById
};
