{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/auth.service\";\nimport * as i4 from \"src/app/services/user.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/icon\";\nfunction UserFormComponent_form_9_div_7_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserFormComponent_form_9_div_7_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Name must be at least 2 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserFormComponent_form_9_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtemplate(1, UserFormComponent_form_9_div_7_small_1_Template, 2, 0, \"small\", 32);\n    i0.ɵɵtemplate(2, UserFormComponent_form_9_div_7_small_2_Template, 2, 0, \"small\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r2.userForm.get(\"name\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r2.userForm.get(\"name\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"minlength\"]);\n  }\n}\nfunction UserFormComponent_form_9_div_13_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Email is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserFormComponent_form_9_div_13_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Please enter a valid email\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserFormComponent_form_9_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtemplate(1, UserFormComponent_form_9_div_13_small_1_Template, 2, 0, \"small\", 32);\n    i0.ɵɵtemplate(2, UserFormComponent_form_9_div_13_small_2_Template, 2, 0, \"small\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r3.userForm.get(\"email\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r3.userForm.get(\"email\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"email\"]);\n  }\n}\nfunction UserFormComponent_form_9_div_14_div_9_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Password is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserFormComponent_form_9_div_14_div_9_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Password must be at least 6 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserFormComponent_form_9_div_14_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtemplate(1, UserFormComponent_form_9_div_14_div_9_small_1_Template, 2, 0, \"small\", 32);\n    i0.ɵɵtemplate(2, UserFormComponent_form_9_div_14_div_9_small_2_Template, 2, 0, \"small\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(3);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r21.userForm.get(\"password\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r21.userForm.get(\"password\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"minlength\"]);\n  }\n}\nfunction UserFormComponent_form_9_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"label\", 33);\n    i0.ɵɵtext(3, \"Password *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 34);\n    i0.ɵɵelement(5, \"input\", 35);\n    i0.ɵɵelementStart(6, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function UserFormComponent_form_9_div_14_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r24 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r24.togglePasswordVisibility());\n    });\n    i0.ɵɵelementStart(7, \"mat-icon\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(9, UserFormComponent_form_9_div_14_div_9_Template, 3, 2, \"div\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_3_0;\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_0_0 = ctx_r4.userForm.get(\"password\")) == null ? null : tmp_0_0.invalid) && ((tmp_0_0 = ctx_r4.userForm.get(\"password\")) == null ? null : tmp_0_0.touched));\n    i0.ɵɵproperty(\"type\", ctx_r4.showPassword ? \"text\" : \"password\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r4.showPassword ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r4.userForm.get(\"password\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r4.userForm.get(\"password\")) == null ? null : tmp_3_0.touched));\n  }\n}\nfunction UserFormComponent_form_9_option_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const role_r26 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", role_r26);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(role_r26);\n  }\n}\nfunction UserFormComponent_form_9_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"small\");\n    i0.ɵɵtext(2, \"Role is required\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserFormComponent_form_9_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"small\");\n    i0.ɵɵtext(2, \"Please enter a valid contact number (10-15 digits)\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserFormComponent_form_9_div_35_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"small\");\n    i0.ɵɵtext(2, \"Registration number is required for students\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserFormComponent_form_9_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"label\", 38);\n    i0.ɵɵtext(3, \"Registration Number *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 39);\n    i0.ɵɵtemplate(5, UserFormComponent_form_9_div_35_div_5_Template, 3, 0, \"div\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_0_0 = ctx_r8.userForm.get(\"regNo\")) == null ? null : tmp_0_0.invalid) && ((tmp_0_0 = ctx_r8.userForm.get(\"regNo\")) == null ? null : tmp_0_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r8.userForm.get(\"regNo\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r8.userForm.get(\"regNo\")) == null ? null : tmp_1_0.touched));\n  }\n}\nfunction UserFormComponent_form_9_div_36_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"small\");\n    i0.ɵɵtext(2, \"Roll number is required for students\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserFormComponent_form_9_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"label\", 40);\n    i0.ɵɵtext(3, \"Roll Number *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 41);\n    i0.ɵɵtemplate(5, UserFormComponent_form_9_div_36_div_5_Template, 3, 0, \"div\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_0_0 = ctx_r9.userForm.get(\"rollNo\")) == null ? null : tmp_0_0.invalid) && ((tmp_0_0 = ctx_r9.userForm.get(\"rollNo\")) == null ? null : tmp_0_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r9.userForm.get(\"rollNo\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r9.userForm.get(\"rollNo\")) == null ? null : tmp_1_0.touched));\n  }\n}\nfunction UserFormComponent_form_9_div_37_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"small\");\n    i0.ɵɵtext(2, \"Father's name is required for students\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserFormComponent_form_9_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"label\", 42);\n    i0.ɵɵtext(3, \"Father's Name *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 43);\n    i0.ɵɵtemplate(5, UserFormComponent_form_9_div_37_div_5_Template, 3, 0, \"div\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_0_0 = ctx_r10.userForm.get(\"father_name\")) == null ? null : tmp_0_0.invalid) && ((tmp_0_0 = ctx_r10.userForm.get(\"father_name\")) == null ? null : tmp_0_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r10.userForm.get(\"father_name\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r10.userForm.get(\"father_name\")) == null ? null : tmp_1_0.touched));\n  }\n}\nfunction UserFormComponent_form_9_div_38_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"small\");\n    i0.ɵɵtext(2, \"Designation is required\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserFormComponent_form_9_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"label\", 44);\n    i0.ɵɵtext(3, \"Designation *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 45);\n    i0.ɵɵtemplate(5, UserFormComponent_form_9_div_38_div_5_Template, 3, 0, \"div\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_0_0 = ctx_r11.userForm.get(\"designation\")) == null ? null : tmp_0_0.invalid) && ((tmp_0_0 = ctx_r11.userForm.get(\"designation\")) == null ? null : tmp_0_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r11.userForm.get(\"designation\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r11.userForm.get(\"designation\")) == null ? null : tmp_1_0.touched));\n  }\n}\nfunction UserFormComponent_form_9_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"label\", 46);\n    i0.ɵɵtext(3, \"Position\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 47);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserFormComponent_form_9_div_40_option_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dept_r33 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", dept_r33);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(dept_r33);\n  }\n}\nfunction UserFormComponent_form_9_div_40_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"small\");\n    i0.ɵɵtext(2, \"Department is required\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserFormComponent_form_9_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"label\", 48);\n    i0.ɵɵtext(3, \"Department *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"select\", 49)(5, \"option\", 18);\n    i0.ɵɵtext(6, \"Select Department\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, UserFormComponent_form_9_div_40_option_7_Template, 2, 2, \"option\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, UserFormComponent_form_9_div_40_div_8_Template, 3, 0, \"div\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_2_0;\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_0_0 = ctx_r13.userForm.get(\"department\")) == null ? null : tmp_0_0.invalid) && ((tmp_0_0 = ctx_r13.userForm.get(\"department\")) == null ? null : tmp_0_0.touched));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r13.departments);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r13.userForm.get(\"department\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r13.userForm.get(\"department\")) == null ? null : tmp_2_0.touched));\n  }\n}\nfunction UserFormComponent_form_9_div_41_option_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const cls_r35 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", cls_r35);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(cls_r35);\n  }\n}\nfunction UserFormComponent_form_9_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"label\", 50);\n    i0.ɵɵtext(3, \"Class\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"select\", 51)(5, \"option\", 18);\n    i0.ɵɵtext(6, \"Select Class\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, UserFormComponent_form_9_div_41_option_7_Template, 2, 2, \"option\", 19);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r14.classes);\n  }\n}\nfunction UserFormComponent_form_9_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"div\", 24);\n    i0.ɵɵelement(3, \"input\", 52);\n    i0.ɵɵelementStart(4, \"label\", 53);\n    i0.ɵɵtext(5, \" Visiting Faculty \");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction UserFormComponent_form_9_span_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 54);\n  }\n}\nfunction UserFormComponent_form_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 6);\n    i0.ɵɵlistener(\"ngSubmit\", function UserFormComponent_form_9_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.onSubmit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 7)(2, \"div\", 8)(3, \"div\", 9)(4, \"label\", 10);\n    i0.ɵɵtext(5, \"Full Name *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"input\", 11);\n    i0.ɵɵtemplate(7, UserFormComponent_form_9_div_7_Template, 3, 2, \"div\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 8)(9, \"div\", 9)(10, \"label\", 13);\n    i0.ɵɵtext(11, \"Email *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"input\", 14);\n    i0.ɵɵtemplate(13, UserFormComponent_form_9_div_13_Template, 3, 2, \"div\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(14, UserFormComponent_form_9_div_14_Template, 10, 5, \"div\", 15);\n    i0.ɵɵelementStart(15, \"div\", 8)(16, \"div\", 9)(17, \"label\", 16);\n    i0.ɵɵtext(18, \"Role *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"select\", 17)(20, \"option\", 18);\n    i0.ɵɵtext(21, \"Select Role\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, UserFormComponent_form_9_option_22_Template, 2, 2, \"option\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(23, UserFormComponent_form_9_div_23_Template, 3, 0, \"div\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 8)(25, \"div\", 9)(26, \"label\", 20);\n    i0.ɵɵtext(27, \"Contact Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(28, \"input\", 21);\n    i0.ɵɵtemplate(29, UserFormComponent_form_9_div_29_Template, 3, 0, \"div\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 8)(31, \"div\", 9)(32, \"label\", 22);\n    i0.ɵɵtext(33, \"Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(34, \"textarea\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(35, UserFormComponent_form_9_div_35_Template, 6, 3, \"div\", 15);\n    i0.ɵɵtemplate(36, UserFormComponent_form_9_div_36_Template, 6, 3, \"div\", 15);\n    i0.ɵɵtemplate(37, UserFormComponent_form_9_div_37_Template, 6, 3, \"div\", 15);\n    i0.ɵɵtemplate(38, UserFormComponent_form_9_div_38_Template, 6, 3, \"div\", 15);\n    i0.ɵɵtemplate(39, UserFormComponent_form_9_div_39_Template, 5, 0, \"div\", 15);\n    i0.ɵɵtemplate(40, UserFormComponent_form_9_div_40_Template, 9, 4, \"div\", 15);\n    i0.ɵɵtemplate(41, UserFormComponent_form_9_div_41_Template, 8, 1, \"div\", 15);\n    i0.ɵɵelementStart(42, \"div\", 8)(43, \"div\", 9)(44, \"div\", 24);\n    i0.ɵɵelement(45, \"input\", 25);\n    i0.ɵɵelementStart(46, \"label\", 26);\n    i0.ɵɵtext(47, \" Active User \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(48, UserFormComponent_form_9_div_48_Template, 6, 0, \"div\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 27)(50, \"button\", 28);\n    i0.ɵɵtemplate(51, UserFormComponent_form_9_span_51_Template, 1, 0, \"span\", 29);\n    i0.ɵɵtext(52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function UserFormComponent_form_9_Template_button_click_53_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.cancel());\n    });\n    i0.ɵɵtext(54, \" Cancel \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    let tmp_6_0;\n    let tmp_8_0;\n    let tmp_9_0;\n    let tmp_10_0;\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.userForm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_1_0 = ctx_r0.userForm.get(\"name\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r0.userForm.get(\"name\")) == null ? null : tmp_1_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r0.userForm.get(\"name\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r0.userForm.get(\"name\")) == null ? null : tmp_2_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_3_0 = ctx_r0.userForm.get(\"email\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r0.userForm.get(\"email\")) == null ? null : tmp_3_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r0.userForm.get(\"email\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx_r0.userForm.get(\"email\")) == null ? null : tmp_4_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isEditMode);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_6_0 = ctx_r0.userForm.get(\"role\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx_r0.userForm.get(\"role\")) == null ? null : tmp_6_0.touched));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.roles);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx_r0.userForm.get(\"role\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx_r0.userForm.get(\"role\")) == null ? null : tmp_8_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_9_0 = ctx_r0.userForm.get(\"contact\")) == null ? null : tmp_9_0.invalid) && ((tmp_9_0 = ctx_r0.userForm.get(\"contact\")) == null ? null : tmp_9_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_10_0 = ctx_r0.userForm.get(\"contact\")) == null ? null : tmp_10_0.invalid) && ((tmp_10_0 = ctx_r0.userForm.get(\"contact\")) == null ? null : tmp_10_0.touched));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isStudent);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isStudent);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isStudent);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isTeacher || ctx_r0.isPrincipal);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isTeacher || ctx_r0.isPrincipal);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isStudent || ctx_r0.isTeacher || ctx_r0.isPrincipal);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isStudent);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isTeacher);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.loading || ctx_r0.userForm.invalid);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.loading ? \"Saving...\" : ctx_r0.isEditMode ? \"Update User\" : \"Create User\", \" \");\n  }\n}\nfunction UserFormComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 56)(2, \"span\", 57);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 58);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.isEditMode ? \"Loading user data...\" : \"Creating user...\");\n  }\n}\nexport let UserFormComponent = /*#__PURE__*/(() => {\n  class UserFormComponent {\n    constructor(fb, route, router, authService, userService) {\n      this.fb = fb;\n      this.route = route;\n      this.router = router;\n      this.authService = authService;\n      this.userService = userService;\n      this.isEditMode = false;\n      this.userId = null;\n      this.loading = false;\n      this.showPassword = false;\n      this.roles = ['Student', 'Teacher', 'Principal', 'Admin'];\n      this.departments = ['Pre-Medical', 'Pre-Engineering', 'Computer Science', 'Arts', 'Commerce'];\n      this.classes = ['1st Year', '2nd Year'];\n      this.sections = ['A', 'B', 'C'];\n    }\n    ngOnInit() {\n      this.userId = this.route.snapshot.paramMap.get('id');\n      this.isEditMode = !!this.userId;\n      this.initializeForm();\n      if (this.isEditMode) {\n        this.loadUser();\n      }\n    }\n    initializeForm() {\n      this.userForm = this.fb.group({\n        name: ['', [Validators.required, Validators.minLength(2)]],\n        email: ['', [Validators.required, Validators.email]],\n        password: [this.isEditMode ? '' : '', this.isEditMode ? [] : [Validators.required, Validators.minLength(6)]],\n        role: ['', Validators.required],\n        address: [''],\n        regNo: [''],\n        rollNo: [''],\n        contact: ['', [Validators.pattern(/^\\d{10,15}$/)]],\n        father_name: [''],\n        department: [''],\n        designation: [''],\n        position: [''],\n        isVisiting: [false],\n        classId: [''],\n        isActive: [true]\n      });\n      // Add conditional validators based on role\n      this.userForm.get('role')?.valueChanges.subscribe(role => {\n        this.updateValidatorsBasedOnRole(role);\n      });\n    }\n    updateValidatorsBasedOnRole(role) {\n      const regNoControl = this.userForm.get('regNo');\n      const rollNoControl = this.userForm.get('rollNo');\n      const departmentControl = this.userForm.get('department');\n      const fatherNameControl = this.userForm.get('father_name');\n      const designationControl = this.userForm.get('designation');\n      // Clear existing validators\n      regNoControl?.clearValidators();\n      rollNoControl?.clearValidators();\n      departmentControl?.clearValidators();\n      fatherNameControl?.clearValidators();\n      designationControl?.clearValidators();\n      if (role === 'Student') {\n        regNoControl?.setValidators([Validators.required]);\n        rollNoControl?.setValidators([Validators.required]);\n        departmentControl?.setValidators([Validators.required]);\n        fatherNameControl?.setValidators([Validators.required]);\n      } else if (role === 'Teacher' || role === 'Principal') {\n        departmentControl?.setValidators([Validators.required]);\n        designationControl?.setValidators([Validators.required]);\n      }\n      // Update validity\n      regNoControl?.updateValueAndValidity();\n      rollNoControl?.updateValueAndValidity();\n      departmentControl?.updateValueAndValidity();\n      fatherNameControl?.updateValueAndValidity();\n      designationControl?.updateValueAndValidity();\n    }\n    loadUser() {\n      if (!this.userId) return;\n      this.loading = true;\n      // Since we don't have a get single user API, we'll get all users and find the one we need\n      this.authService.getAllUsers().subscribe({\n        next: response => {\n          this.loading = false;\n          if (response.success) {\n            const user = response.users.find(u => u._id === this.userId);\n            if (user) {\n              this.populateForm(user);\n            } else {\n              this.showError('User not found');\n              this.router.navigate(['/dashboard/admin/users']);\n            }\n          }\n        },\n        error: error => {\n          this.loading = false;\n          console.error('Error loading user:', error);\n          this.showError('Failed to load user data');\n        }\n      });\n    }\n    populateForm(user) {\n      this.userForm.patchValue({\n        name: user.name,\n        email: user.email,\n        role: user.role,\n        address: user.address || '',\n        regNo: user.regNo || '',\n        rollNo: user.rollNo || '',\n        contact: user.contact || '',\n        father_name: user.father_name || '',\n        department: user.department || '',\n        designation: user.designation || '',\n        position: user.position || '',\n        isVisiting: user.isVisiting || false,\n        classId: user.classId || '',\n        isActive: user.isActive !== false\n      });\n    }\n    onSubmit() {\n      if (this.userForm.valid) {\n        this.loading = true;\n        const formData = this.userForm.value;\n        // Remove password if it's empty in edit mode\n        if (this.isEditMode && !formData.password) {\n          delete formData.password;\n        }\n        if (this.isEditMode) {\n          this.updateUser(formData);\n        } else {\n          this.createUser(formData);\n        }\n      } else {\n        this.markFormGroupTouched();\n      }\n    }\n    createUser(userData) {\n      this.authService.signup(userData).subscribe({\n        next: response => {\n          this.loading = false;\n          if (response.success) {\n            Swal.fire({\n              title: 'Success!',\n              text: 'User created successfully.',\n              icon: 'success',\n              confirmButtonColor: '#29578c'\n            }).then(() => {\n              this.router.navigate(['/dashboard/admin/users']);\n            });\n          }\n        },\n        error: error => {\n          this.loading = false;\n          console.error('Error creating user:', error);\n          const errorMessage = error.error?.message || 'Failed to create user';\n          this.showError(errorMessage);\n        }\n      });\n    }\n    updateUser(userData) {\n      // For now, we'll use the register endpoint to update user data\n      // In a real application, you would have a dedicated update endpoint\n      this.loading = false;\n      Swal.fire({\n        title: 'Feature Not Available',\n        text: 'User update functionality needs a dedicated API endpoint. Please contact the administrator.',\n        icon: 'info',\n        confirmButtonColor: '#29578c'\n      }).then(() => {\n        this.router.navigate(['/dashboard/admin/users']);\n      });\n    }\n    togglePasswordVisibility() {\n      this.showPassword = !this.showPassword;\n    }\n    cancel() {\n      this.router.navigate(['/dashboard/admin/users']);\n    }\n    markFormGroupTouched() {\n      Object.keys(this.userForm.controls).forEach(key => {\n        this.userForm.get(key)?.markAsTouched();\n      });\n    }\n    showError(message) {\n      Swal.fire({\n        title: 'Error',\n        text: message,\n        icon: 'error',\n        confirmButtonColor: '#29578c'\n      });\n    }\n    // Getter methods for template\n    get isStudent() {\n      return this.userForm.get('role')?.value === 'Student';\n    }\n    get isTeacher() {\n      return this.userForm.get('role')?.value === 'Teacher';\n    }\n    get isPrincipal() {\n      return this.userForm.get('role')?.value === 'Principal';\n    }\n    static #_ = this.ɵfac = function UserFormComponent_Factory(t) {\n      return new (t || UserFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.UserService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: UserFormComponent,\n      selectors: [[\"app-user-form\"]],\n      decls: 11,\n      vars: 3,\n      consts: [[1, \"maindiv\"], [1, \"secondarydiv\"], [1, \"header-section\"], [1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [\"class\", \"text-center py-5\", 4, \"ngIf\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"row\"], [1, \"col-md-6\"], [1, \"form-group\"], [\"for\", \"name\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"name\", \"formControlName\", \"name\", 1, \"form-control\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"for\", \"email\", 1, \"form-label\"], [\"type\", \"email\", \"id\", \"email\", \"formControlName\", \"email\", 1, \"form-control\"], [\"class\", \"col-md-6\", 4, \"ngIf\"], [\"for\", \"role\", 1, \"form-label\"], [\"id\", \"role\", \"formControlName\", \"role\", 1, \"form-control\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"contact\", 1, \"form-label\"], [\"type\", \"tel\", \"id\", \"contact\", \"formControlName\", \"contact\", \"placeholder\", \"e.g., 03001234567\", 1, \"form-control\"], [\"for\", \"address\", 1, \"form-label\"], [\"id\", \"address\", \"formControlName\", \"address\", \"rows\", \"2\", \"placeholder\", \"Enter full address\", 1, \"form-control\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"id\", \"isActive\", \"formControlName\", \"isActive\", 1, \"form-check-input\"], [\"for\", \"isActive\", 1, \"form-check-label\"], [1, \"form-actions\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", \"role\", \"status\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", \"ms-2\", 3, \"click\"], [1, \"invalid-feedback\"], [4, \"ngIf\"], [\"for\", \"password\", 1, \"form-label\"], [1, \"input-group\"], [\"id\", \"password\", \"formControlName\", \"password\", 1, \"form-control\", 3, \"type\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [3, \"value\"], [\"for\", \"regNo\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"regNo\", \"formControlName\", \"regNo\", 1, \"form-control\"], [\"for\", \"rollNo\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"rollNo\", \"formControlName\", \"rollNo\", 1, \"form-control\"], [\"for\", \"father_name\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"father_name\", \"formControlName\", \"father_name\", 1, \"form-control\"], [\"for\", \"designation\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"designation\", \"formControlName\", \"designation\", \"placeholder\", \"e.g., Assistant Professor, Lecturer\", 1, \"form-control\"], [\"for\", \"position\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"position\", \"formControlName\", \"position\", \"placeholder\", \"e.g., Head of Department\", 1, \"form-control\"], [\"for\", \"department\", 1, \"form-label\"], [\"id\", \"department\", \"formControlName\", \"department\", 1, \"form-control\"], [\"for\", \"classId\", 1, \"form-label\"], [\"id\", \"classId\", \"formControlName\", \"classId\", 1, \"form-control\"], [\"type\", \"checkbox\", \"id\", \"isVisiting\", \"formControlName\", \"isVisiting\", 1, \"form-check-input\"], [\"for\", \"isVisiting\", 1, \"form-check-label\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"], [1, \"text-center\", \"py-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-2\"]],\n      template: function UserFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\");\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function UserFormComponent_Template_button_click_5_listener() {\n            return ctx.cancel();\n          });\n          i0.ɵɵelementStart(6, \"mat-icon\");\n          i0.ɵɵtext(7, \"arrow_back\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(8, \" Back to Users \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(9, UserFormComponent_form_9_Template, 55, 26, \"form\", 4);\n          i0.ɵɵtemplate(10, UserFormComponent_div_10_Template, 6, 1, \"div\", 5);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.isEditMode ? \"Edit User\" : \"Add New User\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.MatIcon],\n      styles: [\".maindiv[_ngcontent-%COMP%]{padding:20px;background-color:#f8f9fa;min-height:100vh}.secondarydiv[_ngcontent-%COMP%]{background:white;border-radius:8px;padding:30px;box-shadow:0 2px 4px #0000001a;max-width:1200px;margin:0 auto}.header-section[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:30px;padding-bottom:20px;border-bottom:1px solid #dee2e6}.header-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0;color:#29578c;font-weight:600}.form-group[_ngcontent-%COMP%]{margin-bottom:20px}.form-label[_ngcontent-%COMP%]{font-weight:500;color:#495057;margin-bottom:8px;display:block}.form-control[_ngcontent-%COMP%]{padding:10px 12px;border:1px solid #ced4da;border-radius:4px;font-size:14px;transition:border-color .15s ease-in-out,box-shadow .15s ease-in-out}.form-control[_ngcontent-%COMP%]:focus{border-color:#29578c;box-shadow:0 0 0 .2rem #29578c40;outline:0}.form-control.is-invalid[_ngcontent-%COMP%]{border-color:#dc3545}.form-control.is-invalid[_ngcontent-%COMP%]:focus{border-color:#dc3545;box-shadow:0 0 0 .2rem #dc354540}.invalid-feedback[_ngcontent-%COMP%]{display:block;width:100%;margin-top:5px;font-size:12px;color:#dc3545}.input-group[_ngcontent-%COMP%]{display:flex}.input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]{border-top-right-radius:0;border-bottom-right-radius:0}.input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{border-top-left-radius:0;border-bottom-left-radius:0;border-left:0}.form-check[_ngcontent-%COMP%]{padding-left:1.5rem}.form-check-input[_ngcontent-%COMP%]{margin-left:-1.5rem;margin-top:.25rem}.form-check-label[_ngcontent-%COMP%]{font-weight:500;color:#495057}.form-actions[_ngcontent-%COMP%]{margin-top:40px;padding-top:20px;border-top:1px solid #dee2e6;display:flex;gap:10px}.btn[_ngcontent-%COMP%]{padding:10px 20px;border-radius:4px;font-size:14px;font-weight:500;border:1px solid transparent;cursor:pointer;transition:all .15s ease-in-out}.btn-primary[_ngcontent-%COMP%]{background-color:#29578c;border-color:#29578c;color:#fff}.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled){background-color:#1e3f63;border-color:#1e3f63}.btn-primary[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed}.btn-outline-secondary[_ngcontent-%COMP%]{color:#6c757d;border-color:#6c757d;background-color:transparent}.btn-outline-secondary[_ngcontent-%COMP%]:hover{color:#fff;background-color:#6c757d;border-color:#6c757d}.spinner-border-sm[_ngcontent-%COMP%]{width:1rem;height:1rem}.spinner-border[_ngcontent-%COMP%]{width:3rem;height:3rem}.row[_ngcontent-%COMP%]{margin-left:-15px;margin-right:-15px}.col-md-6[_ngcontent-%COMP%]{padding-left:15px;padding-right:15px}textarea.form-control[_ngcontent-%COMP%]{resize:vertical;min-height:60px}select.form-control[_ngcontent-%COMP%]{background-image:url(\\\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e\\\");background-position:right 8px center;background-repeat:no-repeat;background-size:16px 12px;padding-right:40px;appearance:none}@media (max-width: 768px){.header-section[_ngcontent-%COMP%]{flex-direction:column;gap:15px;text-align:center}.form-actions[_ngcontent-%COMP%]{flex-direction:column}.form-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{width:100%}.col-md-6[_ngcontent-%COMP%]{padding-left:0;padding-right:0}.row[_ngcontent-%COMP%]{margin-left:0;margin-right:0}}\"]\n    });\n  }\n  return UserFormComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}