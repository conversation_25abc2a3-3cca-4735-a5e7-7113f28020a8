{"ast": null, "code": "import * as i1 from '@angular/cdk/tree';\nimport { CdkTreeNode, CdkTreeNodeDef, CdkNestedTreeNode, CDK_TREE_NODE_OUTLET_NODE, CdkTreeNodePadding, CdkTreeNodeOutlet, CdkTree, CdkTreeNodeToggle, CdkTreeModule } from '@angular/cdk/tree';\nimport * as i0 from '@angular/core';\nimport { Directive, Attribute, Input, Inject, Optional, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, NgModule } from '@angular/core';\nimport { mixinTabIndex, mixinDisabled, MatCommonModule } from '@angular/material/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { DataSource } from '@angular/cdk/collections';\nimport { BehaviorSubject, merge } from 'rxjs';\nimport { take, map } from 'rxjs/operators';\nconst _MatTreeNodeBase = /*#__PURE__*/mixinTabIndex( /*#__PURE__*/mixinDisabled(CdkTreeNode));\n/**\n * Wrapper for the CdkTree node with Material design styles.\n */\nlet MatTreeNode = /*#__PURE__*/(() => {\n  class MatTreeNode extends _MatTreeNodeBase {\n    constructor(elementRef, tree, tabIndex) {\n      super(elementRef, tree);\n      this.tabIndex = Number(tabIndex) || 0;\n    }\n    // This is a workaround for https://github.com/angular/angular/issues/23091\n    // In aot mode, the lifecycle hooks from parent class are not called.\n    ngOnInit() {\n      super.ngOnInit();\n    }\n    ngOnDestroy() {\n      super.ngOnDestroy();\n    }\n    static #_ = this.ɵfac = function MatTreeNode_Factory(t) {\n      return new (t || MatTreeNode)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.CdkTree), i0.ɵɵinjectAttribute('tabindex'));\n    };\n    static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatTreeNode,\n      selectors: [[\"mat-tree-node\"]],\n      hostAttrs: [1, \"mat-tree-node\"],\n      inputs: {\n        role: \"role\",\n        disabled: \"disabled\",\n        tabIndex: \"tabIndex\"\n      },\n      exportAs: [\"matTreeNode\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkTreeNode,\n        useExisting: MatTreeNode\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return MatTreeNode;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Wrapper for the CdkTree node definition with Material design styles.\n * Captures the node's template and a when predicate that describes when this node should be used.\n */\nlet MatTreeNodeDef = /*#__PURE__*/(() => {\n  class MatTreeNodeDef extends CdkTreeNodeDef {\n    static #_ = this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatTreeNodeDef_BaseFactory;\n      return function MatTreeNodeDef_Factory(t) {\n        return (ɵMatTreeNodeDef_BaseFactory || (ɵMatTreeNodeDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatTreeNodeDef)))(t || MatTreeNodeDef);\n      };\n    }();\n    static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatTreeNodeDef,\n      selectors: [[\"\", \"matTreeNodeDef\", \"\"]],\n      inputs: {\n        when: [\"matTreeNodeDefWhen\", \"when\"],\n        data: [\"matTreeNode\", \"data\"]\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkTreeNodeDef,\n        useExisting: MatTreeNodeDef\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return MatTreeNodeDef;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Wrapper for the CdkTree nested node with Material design styles.\n */\nlet MatNestedTreeNode = /*#__PURE__*/(() => {\n  class MatNestedTreeNode extends CdkNestedTreeNode {\n    /** Whether the node is disabled. */\n    get disabled() {\n      return this._disabled;\n    }\n    set disabled(value) {\n      this._disabled = coerceBooleanProperty(value);\n    }\n    /** Tabindex for the node. */\n    get tabIndex() {\n      return this.disabled ? -1 : this._tabIndex;\n    }\n    set tabIndex(value) {\n      // If the specified tabIndex value is null or undefined, fall back to the default value.\n      this._tabIndex = value != null ? value : 0;\n    }\n    constructor(elementRef, tree, differs, tabIndex) {\n      super(elementRef, tree, differs);\n      this._disabled = false;\n      this.tabIndex = Number(tabIndex) || 0;\n    }\n    // This is a workaround for https://github.com/angular/angular/issues/19145\n    // In aot mode, the lifecycle hooks from parent class are not called.\n    // TODO(tinayuangao): Remove when the angular issue #19145 is fixed\n    ngOnInit() {\n      super.ngOnInit();\n    }\n    ngAfterContentInit() {\n      super.ngAfterContentInit();\n    }\n    ngOnDestroy() {\n      super.ngOnDestroy();\n    }\n    static #_ = this.ɵfac = function MatNestedTreeNode_Factory(t) {\n      return new (t || MatNestedTreeNode)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.CdkTree), i0.ɵɵdirectiveInject(i0.IterableDiffers), i0.ɵɵinjectAttribute('tabindex'));\n    };\n    static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatNestedTreeNode,\n      selectors: [[\"mat-nested-tree-node\"]],\n      hostAttrs: [1, \"mat-nested-tree-node\"],\n      inputs: {\n        role: \"role\",\n        disabled: \"disabled\",\n        tabIndex: \"tabIndex\",\n        node: [\"matNestedTreeNode\", \"node\"]\n      },\n      exportAs: [\"matNestedTreeNode\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkNestedTreeNode,\n        useExisting: MatNestedTreeNode\n      }, {\n        provide: CdkTreeNode,\n        useExisting: MatNestedTreeNode\n      }, {\n        provide: CDK_TREE_NODE_OUTLET_NODE,\n        useExisting: MatNestedTreeNode\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return MatNestedTreeNode;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Wrapper for the CdkTree padding with Material design styles.\n */\nlet MatTreeNodePadding = /*#__PURE__*/(() => {\n  class MatTreeNodePadding extends CdkTreeNodePadding {\n    /** The level of depth of the tree node. The padding will be `level * indent` pixels. */\n    get level() {\n      return this._level;\n    }\n    set level(value) {\n      this._setLevelInput(value);\n    }\n    /** The indent for each level. Default number 40px from material design menu sub-menu spec. */\n    get indent() {\n      return this._indent;\n    }\n    set indent(indent) {\n      this._setIndentInput(indent);\n    }\n    static #_ = this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatTreeNodePadding_BaseFactory;\n      return function MatTreeNodePadding_Factory(t) {\n        return (ɵMatTreeNodePadding_BaseFactory || (ɵMatTreeNodePadding_BaseFactory = i0.ɵɵgetInheritedFactory(MatTreeNodePadding)))(t || MatTreeNodePadding);\n      };\n    }();\n    static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatTreeNodePadding,\n      selectors: [[\"\", \"matTreeNodePadding\", \"\"]],\n      inputs: {\n        level: [\"matTreeNodePadding\", \"level\"],\n        indent: [\"matTreeNodePaddingIndent\", \"indent\"]\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkTreeNodePadding,\n        useExisting: MatTreeNodePadding\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return MatTreeNodePadding;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Outlet for nested CdkNode. Put `[matTreeNodeOutlet]` on a tag to place children dataNodes\n * inside the outlet.\n */\nlet MatTreeNodeOutlet = /*#__PURE__*/(() => {\n  class MatTreeNodeOutlet {\n    constructor(viewContainer, _node) {\n      this.viewContainer = viewContainer;\n      this._node = _node;\n    }\n    static #_ = this.ɵfac = function MatTreeNodeOutlet_Factory(t) {\n      return new (t || MatTreeNodeOutlet)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(CDK_TREE_NODE_OUTLET_NODE, 8));\n    };\n    static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatTreeNodeOutlet,\n      selectors: [[\"\", \"matTreeNodeOutlet\", \"\"]],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkTreeNodeOutlet,\n        useExisting: MatTreeNodeOutlet\n      }])]\n    });\n  }\n  return MatTreeNodeOutlet;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Wrapper for the CdkTable with Material design styles.\n */\nlet MatTree = /*#__PURE__*/(() => {\n  class MatTree extends CdkTree {\n    constructor() {\n      super(...arguments);\n      // Outlets within the tree's template where the dataNodes will be inserted.\n      // We need an initializer here to avoid a TS error. The value will be set in `ngAfterViewInit`.\n      this._nodeOutlet = undefined;\n    }\n    static #_ = this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatTree_BaseFactory;\n      return function MatTree_Factory(t) {\n        return (ɵMatTree_BaseFactory || (ɵMatTree_BaseFactory = i0.ɵɵgetInheritedFactory(MatTree)))(t || MatTree);\n      };\n    }();\n    static #_2 = this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatTree,\n      selectors: [[\"mat-tree\"]],\n      viewQuery: function MatTree_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(MatTreeNodeOutlet, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nodeOutlet = _t.first);\n        }\n      },\n      hostAttrs: [\"role\", \"tree\", 1, \"mat-tree\"],\n      exportAs: [\"matTree\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkTree,\n        useExisting: MatTree\n      }]), i0.ɵɵInheritDefinitionFeature],\n      decls: 1,\n      vars: 0,\n      consts: [[\"matTreeNodeOutlet\", \"\"]],\n      template: function MatTree_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementContainer(0, 0);\n        }\n      },\n      dependencies: [MatTreeNodeOutlet],\n      styles: [\".mat-tree{display:block}.mat-tree-node{display:flex;align-items:center;flex:1;word-wrap:break-word}.mat-nested-tree-node{border-bottom-width:0}\"],\n      encapsulation: 2\n    });\n  }\n  return MatTree;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Wrapper for the CdkTree's toggle with Material design styles.\n */\nlet MatTreeNodeToggle = /*#__PURE__*/(() => {\n  class MatTreeNodeToggle extends CdkTreeNodeToggle {\n    static #_ = this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatTreeNodeToggle_BaseFactory;\n      return function MatTreeNodeToggle_Factory(t) {\n        return (ɵMatTreeNodeToggle_BaseFactory || (ɵMatTreeNodeToggle_BaseFactory = i0.ɵɵgetInheritedFactory(MatTreeNodeToggle)))(t || MatTreeNodeToggle);\n      };\n    }();\n    static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatTreeNodeToggle,\n      selectors: [[\"\", \"matTreeNodeToggle\", \"\"]],\n      inputs: {\n        recursive: [\"matTreeNodeToggleRecursive\", \"recursive\"]\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkTreeNodeToggle,\n        useExisting: MatTreeNodeToggle\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return MatTreeNodeToggle;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst MAT_TREE_DIRECTIVES = [MatNestedTreeNode, MatTreeNodeDef, MatTreeNodePadding, MatTreeNodeToggle, MatTree, MatTreeNode, MatTreeNodeOutlet];\nlet MatTreeModule = /*#__PURE__*/(() => {\n  class MatTreeModule {\n    static #_ = this.ɵfac = function MatTreeModule_Factory(t) {\n      return new (t || MatTreeModule)();\n    };\n    static #_2 = this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatTreeModule\n    });\n    static #_3 = this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CdkTreeModule, MatCommonModule, MatCommonModule]\n    });\n  }\n  return MatTreeModule;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Tree flattener to convert a normal type of node to node with children & level information.\n * Transform nested nodes of type `T` to flattened nodes of type `F`.\n *\n * For example, the input data of type `T` is nested, and contains its children data:\n *   SomeNode: {\n *     key: 'Fruits',\n *     children: [\n *       NodeOne: {\n *         key: 'Apple',\n *       },\n *       NodeTwo: {\n *        key: 'Pear',\n *      }\n *    ]\n *  }\n *  After flattener flatten the tree, the structure will become\n *  SomeNode: {\n *    key: 'Fruits',\n *    expandable: true,\n *    level: 1\n *  },\n *  NodeOne: {\n *    key: 'Apple',\n *    expandable: false,\n *    level: 2\n *  },\n *  NodeTwo: {\n *   key: 'Pear',\n *   expandable: false,\n *   level: 2\n * }\n * and the output flattened type is `F` with additional information.\n */\nclass MatTreeFlattener {\n  constructor(transformFunction, getLevel, isExpandable, getChildren) {\n    this.transformFunction = transformFunction;\n    this.getLevel = getLevel;\n    this.isExpandable = isExpandable;\n    this.getChildren = getChildren;\n  }\n  _flattenNode(node, level, resultNodes, parentMap) {\n    const flatNode = this.transformFunction(node, level);\n    resultNodes.push(flatNode);\n    if (this.isExpandable(flatNode)) {\n      const childrenNodes = this.getChildren(node);\n      if (childrenNodes) {\n        if (Array.isArray(childrenNodes)) {\n          this._flattenChildren(childrenNodes, level, resultNodes, parentMap);\n        } else {\n          childrenNodes.pipe(take(1)).subscribe(children => {\n            this._flattenChildren(children, level, resultNodes, parentMap);\n          });\n        }\n      }\n    }\n    return resultNodes;\n  }\n  _flattenChildren(children, level, resultNodes, parentMap) {\n    children.forEach((child, index) => {\n      let childParentMap = parentMap.slice();\n      childParentMap.push(index != children.length - 1);\n      this._flattenNode(child, level + 1, resultNodes, childParentMap);\n    });\n  }\n  /**\n   * Flatten a list of node type T to flattened version of node F.\n   * Please note that type T may be nested, and the length of `structuredData` may be different\n   * from that of returned list `F[]`.\n   */\n  flattenNodes(structuredData) {\n    let resultNodes = [];\n    structuredData.forEach(node => this._flattenNode(node, 0, resultNodes, []));\n    return resultNodes;\n  }\n  /**\n   * Expand flattened node with current expansion status.\n   * The returned list may have different length.\n   */\n  expandFlattenedNodes(nodes, treeControl) {\n    let results = [];\n    let currentExpand = [];\n    currentExpand[0] = true;\n    nodes.forEach(node => {\n      let expand = true;\n      for (let i = 0; i <= this.getLevel(node); i++) {\n        expand = expand && currentExpand[i];\n      }\n      if (expand) {\n        results.push(node);\n      }\n      if (this.isExpandable(node)) {\n        currentExpand[this.getLevel(node) + 1] = treeControl.isExpanded(node);\n      }\n    });\n    return results;\n  }\n}\n/**\n * Data source for flat tree.\n * The data source need to handle expansion/collapsion of the tree node and change the data feed\n * to `MatTree`.\n * The nested tree nodes of type `T` are flattened through `MatTreeFlattener`, and converted\n * to type `F` for `MatTree` to consume.\n */\nclass MatTreeFlatDataSource extends DataSource {\n  get data() {\n    return this._data.value;\n  }\n  set data(value) {\n    this._data.next(value);\n    this._flattenedData.next(this._treeFlattener.flattenNodes(this.data));\n    this._treeControl.dataNodes = this._flattenedData.value;\n  }\n  constructor(_treeControl, _treeFlattener, initialData) {\n    super();\n    this._treeControl = _treeControl;\n    this._treeFlattener = _treeFlattener;\n    this._flattenedData = new BehaviorSubject([]);\n    this._expandedData = new BehaviorSubject([]);\n    this._data = new BehaviorSubject([]);\n    if (initialData) {\n      // Assign the data through the constructor to ensure that all of the logic is executed.\n      this.data = initialData;\n    }\n  }\n  connect(collectionViewer) {\n    return merge(collectionViewer.viewChange, this._treeControl.expansionModel.changed, this._flattenedData).pipe(map(() => {\n      this._expandedData.next(this._treeFlattener.expandFlattenedNodes(this._flattenedData.value, this._treeControl));\n      return this._expandedData.value;\n    }));\n  }\n  disconnect() {\n    // no op\n  }\n}\n\n/**\n * Data source for nested tree.\n *\n * The data source for nested tree doesn't have to consider node flattener, or the way to expand\n * or collapse. The expansion/collapsion will be handled by TreeControl and each non-leaf node.\n */\nclass MatTreeNestedDataSource extends DataSource {\n  constructor() {\n    super(...arguments);\n    this._data = new BehaviorSubject([]);\n  }\n  /**\n   * Data for the nested tree\n   */\n  get data() {\n    return this._data.value;\n  }\n  set data(value) {\n    this._data.next(value);\n  }\n  connect(collectionViewer) {\n    return merge(...[collectionViewer.viewChange, this._data]).pipe(map(() => this.data));\n  }\n  disconnect() {\n    // no op\n  }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MatNestedTreeNode, MatTree, MatTreeFlatDataSource, MatTreeFlattener, MatTreeModule, MatTreeNestedDataSource, MatTreeNode, MatTreeNodeDef, MatTreeNodeOutlet, MatTreeNodePadding, MatTreeNodeToggle };\n//# sourceMappingURL=tree.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}