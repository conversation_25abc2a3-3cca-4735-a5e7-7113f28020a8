.maindiv {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.secondarydiv {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.searchAndtab {
  margin-bottom: 20px;
}

.btn-top {
  background-color: #29578c;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
}

.btn-top:hover {
  background-color: #1e3f63;
  color: white;
}

.search-container {
  position: relative;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  padding: 8px 40px 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 250px;
  font-size: 14px;
}

.search-icon {
  position: absolute;
  right: 12px;
  color: #666;
  font-size: 18px;
}

.table-container {
  overflow-x: auto;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

.tablehead {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
}

.tablehead th {
  padding: 12px 8px;
  text-align: left;
  font-weight: 600;
  color: #495057;
  font-size: 14px;
}

tbody tr {
  border-bottom: 1px solid #dee2e6;
}

tbody tr:hover {
  background-color: #f8f9fa;
}

tbody td {
  padding: 12px 8px;
  font-size: 14px;
  color: #495057;
}

.badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.badge-student {
  background-color: #e3f2fd;
  color: #1976d2;
}

.badge-teacher {
  background-color: #f3e5f5;
  color: #7b1fa2;
}

.badge-principal {
  background-color: #fff3e0;
  color: #f57c00;
}

.badge-admin {
  background-color: #ffebee;
  color: #d32f2f;
}

.badge-active {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.badge-inactive {
  background-color: #ffebee;
  color: #d32f2f;
}

.action-buttons {
  display: flex;
  gap: 5px;
}

.action-buttons .btn {
  padding: 4px 8px;
  border-radius: 4px;
}

.action-buttons .btn mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

.pagination-container {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #dee2e6;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 10px;
}

.page-info {
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  font-size: 14px;
}

.form-select {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background-color: white;
}

.spinner-border {
  width: 3rem;
  height: 3rem;
}

@media (max-width: 768px) {
  .searchAndtab {
    flex-direction: column;
    gap: 15px;
  }
  
  .search-input {
    width: 100%;
  }
  
  .pagination-container {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
}
