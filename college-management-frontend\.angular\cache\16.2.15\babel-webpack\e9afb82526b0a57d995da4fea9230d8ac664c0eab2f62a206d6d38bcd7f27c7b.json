{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/icon\";\nexport let StudentComplaintComponent = /*#__PURE__*/(() => {\n  class StudentComplaintComponent {\n    static #_ = this.ɵfac = function StudentComplaintComponent_Factory(t) {\n      return new (t || StudentComplaintComponent)();\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StudentComplaintComponent,\n      selectors: [[\"app-student-complaint\"]],\n      decls: 17,\n      vars: 0,\n      consts: [[1, \"d-flex\", \"justify-content-center\", \"align-items-center\"], [1, \"card\"], [1, \"text-center\", \"fw-bold\"], [1, \"mb-3\"], [\"for\", \"date\", 1, \"mb-2\"], [\"type\", \"date\", 1, \"form-control\"], [\"for\", \"complaint\", 1, \"mb-2\"], [\"type\", \"text\", \"rows\", \"5\", 1, \"form-control\"], [1, \"mb-2\", \"d-flex\", \"justify-content-center\"], [1, \"btn\", \"save\"]],\n      template: function StudentComplaintComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\", 2);\n          i0.ɵɵtext(3, \"Complaint Form\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 3)(5, \"label\", 4);\n          i0.ɵɵtext(6, \"Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(7, \"input\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 3)(9, \"label\", 6);\n          i0.ɵɵtext(10, \"Complaint\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(11, \"textarea\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 8)(13, \"button\", 9)(14, \"mat-icon\");\n          i0.ɵɵtext(15, \"send\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(16, \" Send\");\n          i0.ɵɵelementEnd()()()();\n        }\n      },\n      dependencies: [i1.MatIcon],\n      styles: [\".card[_ngcontent-%COMP%]{background-color:#eae7e7;border-radius:16px;width:25rem;padding:12px}\"]\n    });\n  }\n  return StudentComplaintComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}