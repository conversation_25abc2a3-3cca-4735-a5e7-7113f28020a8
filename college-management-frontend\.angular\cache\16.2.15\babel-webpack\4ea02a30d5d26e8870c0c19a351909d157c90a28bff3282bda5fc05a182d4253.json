{"ast": null, "code": "import * as i1 from '@angular/cdk/a11y';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, EventEmitter, Directive, Optional, Inject, ContentChildren, Input, Output, Component, ViewEncapsulation, ChangeDetectionStrategy, Attribute, ViewChild, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i2 from '@angular/material/core';\nimport { mixinDisableRipple, MatCommonModule, MatRippleModule } from '@angular/material/core';\n\n/**\n * Injection token that can be used to configure the\n * default options for all button toggles within an app.\n */\nconst _c0 = [\"button\"];\nconst _c1 = [\"*\"];\nconst MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS');\n/**\n * Injection token that can be used to reference instances of `MatButtonToggleGroup`.\n * It serves as alternative token to the actual `MatButtonToggleGroup` class which\n * could cause unnecessary retention of the class and its component metadata.\n */\nconst MAT_BUTTON_TOGGLE_GROUP = /*#__PURE__*/new InjectionToken('MatButtonToggleGroup');\n/**\n * Provider Expression that allows mat-button-toggle-group to register as a ControlValueAccessor.\n * This allows it to support [(ngModel)].\n * @docs-private\n */\nconst MAT_BUTTON_TOGGLE_GROUP_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: /*#__PURE__*/forwardRef(() => MatButtonToggleGroup),\n  multi: true\n};\n// Counter used to generate unique IDs.\nlet uniqueIdCounter = 0;\n/** Change event object emitted by button toggle. */\nclass MatButtonToggleChange {\n  constructor( /** The button toggle that emits the event. */\n  source, /** The value assigned to the button toggle. */\n  value) {\n    this.source = source;\n    this.value = value;\n  }\n}\n/** Exclusive selection button toggle group that behaves like a radio-button group. */\nlet MatButtonToggleGroup = /*#__PURE__*/(() => {\n  class MatButtonToggleGroup {\n    /** `name` attribute for the underlying `input` element. */\n    get name() {\n      return this._name;\n    }\n    set name(value) {\n      this._name = value;\n      this._markButtonsForCheck();\n    }\n    /** Whether the toggle group is vertical. */\n    get vertical() {\n      return this._vertical;\n    }\n    set vertical(value) {\n      this._vertical = coerceBooleanProperty(value);\n    }\n    /** Value of the toggle group. */\n    get value() {\n      const selected = this._selectionModel ? this._selectionModel.selected : [];\n      if (this.multiple) {\n        return selected.map(toggle => toggle.value);\n      }\n      return selected[0] ? selected[0].value : undefined;\n    }\n    set value(newValue) {\n      this._setSelectionByValue(newValue);\n      this.valueChange.emit(this.value);\n    }\n    /** Selected button toggles in the group. */\n    get selected() {\n      const selected = this._selectionModel ? this._selectionModel.selected : [];\n      return this.multiple ? selected : selected[0] || null;\n    }\n    /** Whether multiple button toggles can be selected. */\n    get multiple() {\n      return this._multiple;\n    }\n    set multiple(value) {\n      this._multiple = coerceBooleanProperty(value);\n      this._markButtonsForCheck();\n    }\n    /** Whether multiple button toggle group is disabled. */\n    get disabled() {\n      return this._disabled;\n    }\n    set disabled(value) {\n      this._disabled = coerceBooleanProperty(value);\n      this._markButtonsForCheck();\n    }\n    constructor(_changeDetector, defaultOptions) {\n      this._changeDetector = _changeDetector;\n      this._vertical = false;\n      this._multiple = false;\n      this._disabled = false;\n      /**\n       * The method to be called in order to update ngModel.\n       * Now `ngModel` binding is not supported in multiple selection mode.\n       */\n      this._controlValueAccessorChangeFn = () => {};\n      /** onTouch function registered via registerOnTouch (ControlValueAccessor). */\n      this._onTouched = () => {};\n      this._name = `mat-button-toggle-group-${uniqueIdCounter++}`;\n      /**\n       * Event that emits whenever the value of the group changes.\n       * Used to facilitate two-way data binding.\n       * @docs-private\n       */\n      this.valueChange = new EventEmitter();\n      /** Event emitted when the group's value changes. */\n      this.change = new EventEmitter();\n      this.appearance = defaultOptions && defaultOptions.appearance ? defaultOptions.appearance : 'standard';\n    }\n    ngOnInit() {\n      this._selectionModel = new SelectionModel(this.multiple, undefined, false);\n    }\n    ngAfterContentInit() {\n      this._selectionModel.select(...this._buttonToggles.filter(toggle => toggle.checked));\n    }\n    /**\n     * Sets the model value. Implemented as part of ControlValueAccessor.\n     * @param value Value to be set to the model.\n     */\n    writeValue(value) {\n      this.value = value;\n      this._changeDetector.markForCheck();\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnChange(fn) {\n      this._controlValueAccessorChangeFn = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnTouched(fn) {\n      this._onTouched = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    setDisabledState(isDisabled) {\n      this.disabled = isDisabled;\n    }\n    /** Dispatch change event with current selection and group value. */\n    _emitChangeEvent(toggle) {\n      const event = new MatButtonToggleChange(toggle, this.value);\n      this._rawValue = event.value;\n      this._controlValueAccessorChangeFn(event.value);\n      this.change.emit(event);\n    }\n    /**\n     * Syncs a button toggle's selected state with the model value.\n     * @param toggle Toggle to be synced.\n     * @param select Whether the toggle should be selected.\n     * @param isUserInput Whether the change was a result of a user interaction.\n     * @param deferEvents Whether to defer emitting the change events.\n     */\n    _syncButtonToggle(toggle, select, isUserInput = false, deferEvents = false) {\n      // Deselect the currently-selected toggle, if we're in single-selection\n      // mode and the button being toggled isn't selected at the moment.\n      if (!this.multiple && this.selected && !toggle.checked) {\n        this.selected.checked = false;\n      }\n      if (this._selectionModel) {\n        if (select) {\n          this._selectionModel.select(toggle);\n        } else {\n          this._selectionModel.deselect(toggle);\n        }\n      } else {\n        deferEvents = true;\n      }\n      // We need to defer in some cases in order to avoid \"changed after checked errors\", however\n      // the side-effect is that we may end up updating the model value out of sequence in others\n      // The `deferEvents` flag allows us to decide whether to do it on a case-by-case basis.\n      if (deferEvents) {\n        Promise.resolve().then(() => this._updateModelValue(toggle, isUserInput));\n      } else {\n        this._updateModelValue(toggle, isUserInput);\n      }\n    }\n    /** Checks whether a button toggle is selected. */\n    _isSelected(toggle) {\n      return this._selectionModel && this._selectionModel.isSelected(toggle);\n    }\n    /** Determines whether a button toggle should be checked on init. */\n    _isPrechecked(toggle) {\n      if (typeof this._rawValue === 'undefined') {\n        return false;\n      }\n      if (this.multiple && Array.isArray(this._rawValue)) {\n        return this._rawValue.some(value => toggle.value != null && value === toggle.value);\n      }\n      return toggle.value === this._rawValue;\n    }\n    /** Updates the selection state of the toggles in the group based on a value. */\n    _setSelectionByValue(value) {\n      this._rawValue = value;\n      if (!this._buttonToggles) {\n        return;\n      }\n      if (this.multiple && value) {\n        if (!Array.isArray(value) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n          throw Error('Value must be an array in multiple-selection mode.');\n        }\n        this._clearSelection();\n        value.forEach(currentValue => this._selectValue(currentValue));\n      } else {\n        this._clearSelection();\n        this._selectValue(value);\n      }\n    }\n    /** Clears the selected toggles. */\n    _clearSelection() {\n      this._selectionModel.clear();\n      this._buttonToggles.forEach(toggle => toggle.checked = false);\n    }\n    /** Selects a value if there's a toggle that corresponds to it. */\n    _selectValue(value) {\n      const correspondingOption = this._buttonToggles.find(toggle => {\n        return toggle.value != null && toggle.value === value;\n      });\n      if (correspondingOption) {\n        correspondingOption.checked = true;\n        this._selectionModel.select(correspondingOption);\n      }\n    }\n    /** Syncs up the group's value with the model and emits the change event. */\n    _updateModelValue(toggle, isUserInput) {\n      // Only emit the change event for user input.\n      if (isUserInput) {\n        this._emitChangeEvent(toggle);\n      }\n      // Note: we emit this one no matter whether it was a user interaction, because\n      // it is used by Angular to sync up the two-way data binding.\n      this.valueChange.emit(this.value);\n    }\n    /** Marks all of the child button toggles to be checked. */\n    _markButtonsForCheck() {\n      this._buttonToggles?.forEach(toggle => toggle._markForCheck());\n    }\n    static #_ = this.ɵfac = function MatButtonToggleGroup_Factory(t) {\n      return new (t || MatButtonToggleGroup)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS, 8));\n    };\n    static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatButtonToggleGroup,\n      selectors: [[\"mat-button-toggle-group\"]],\n      contentQueries: function MatButtonToggleGroup_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatButtonToggle, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._buttonToggles = _t);\n        }\n      },\n      hostAttrs: [\"role\", \"group\", 1, \"mat-button-toggle-group\"],\n      hostVars: 5,\n      hostBindings: function MatButtonToggleGroup_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-disabled\", ctx.disabled);\n          i0.ɵɵclassProp(\"mat-button-toggle-vertical\", ctx.vertical)(\"mat-button-toggle-group-appearance-standard\", ctx.appearance === \"standard\");\n        }\n      },\n      inputs: {\n        appearance: \"appearance\",\n        name: \"name\",\n        vertical: \"vertical\",\n        value: \"value\",\n        multiple: \"multiple\",\n        disabled: \"disabled\"\n      },\n      outputs: {\n        valueChange: \"valueChange\",\n        change: \"change\"\n      },\n      exportAs: [\"matButtonToggleGroup\"],\n      features: [i0.ɵɵProvidersFeature([MAT_BUTTON_TOGGLE_GROUP_VALUE_ACCESSOR, {\n        provide: MAT_BUTTON_TOGGLE_GROUP,\n        useExisting: MatButtonToggleGroup\n      }])]\n    });\n  }\n  return MatButtonToggleGroup;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n// Boilerplate for applying mixins to the MatButtonToggle class.\n/** @docs-private */\nconst _MatButtonToggleBase = /*#__PURE__*/mixinDisableRipple(class {});\n/** Single button inside of a toggle group. */\nlet MatButtonToggle = /*#__PURE__*/(() => {\n  class MatButtonToggle extends _MatButtonToggleBase {\n    /** Unique ID for the underlying `button` element. */\n    get buttonId() {\n      return `${this.id}-button`;\n    }\n    /** The appearance style of the button. */\n    get appearance() {\n      return this.buttonToggleGroup ? this.buttonToggleGroup.appearance : this._appearance;\n    }\n    set appearance(value) {\n      this._appearance = value;\n    }\n    /** Whether the button is checked. */\n    get checked() {\n      return this.buttonToggleGroup ? this.buttonToggleGroup._isSelected(this) : this._checked;\n    }\n    set checked(value) {\n      const newValue = coerceBooleanProperty(value);\n      if (newValue !== this._checked) {\n        this._checked = newValue;\n        if (this.buttonToggleGroup) {\n          this.buttonToggleGroup._syncButtonToggle(this, this._checked);\n        }\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n    /** Whether the button is disabled. */\n    get disabled() {\n      return this._disabled || this.buttonToggleGroup && this.buttonToggleGroup.disabled;\n    }\n    set disabled(value) {\n      this._disabled = coerceBooleanProperty(value);\n    }\n    constructor(toggleGroup, _changeDetectorRef, _elementRef, _focusMonitor, defaultTabIndex, defaultOptions) {\n      super();\n      this._changeDetectorRef = _changeDetectorRef;\n      this._elementRef = _elementRef;\n      this._focusMonitor = _focusMonitor;\n      this._checked = false;\n      /**\n       * Users can specify the `aria-labelledby` attribute which will be forwarded to the input element\n       */\n      this.ariaLabelledby = null;\n      this._disabled = false;\n      /** Event emitted when the group value changes. */\n      this.change = new EventEmitter();\n      const parsedTabIndex = Number(defaultTabIndex);\n      this.tabIndex = parsedTabIndex || parsedTabIndex === 0 ? parsedTabIndex : null;\n      this.buttonToggleGroup = toggleGroup;\n      this.appearance = defaultOptions && defaultOptions.appearance ? defaultOptions.appearance : 'standard';\n    }\n    ngOnInit() {\n      const group = this.buttonToggleGroup;\n      this.id = this.id || `mat-button-toggle-${uniqueIdCounter++}`;\n      if (group) {\n        if (group._isPrechecked(this)) {\n          this.checked = true;\n        } else if (group._isSelected(this) !== this._checked) {\n          // As side effect of the circular dependency between the toggle group and the button,\n          // we may end up in a state where the button is supposed to be checked on init, but it\n          // isn't, because the checked value was assigned too early. This can happen when Ivy\n          // assigns the static input value before the `ngOnInit` has run.\n          group._syncButtonToggle(this, this._checked);\n        }\n      }\n    }\n    ngAfterViewInit() {\n      this._focusMonitor.monitor(this._elementRef, true);\n    }\n    ngOnDestroy() {\n      const group = this.buttonToggleGroup;\n      this._focusMonitor.stopMonitoring(this._elementRef);\n      // Remove the toggle from the selection once it's destroyed. Needs to happen\n      // on the next tick in order to avoid \"changed after checked\" errors.\n      if (group && group._isSelected(this)) {\n        group._syncButtonToggle(this, false, false, true);\n      }\n    }\n    /** Focuses the button. */\n    focus(options) {\n      this._buttonElement.nativeElement.focus(options);\n    }\n    /** Checks the button toggle due to an interaction with the underlying native button. */\n    _onButtonClick() {\n      const newChecked = this._isSingleSelector() ? true : !this._checked;\n      if (newChecked !== this._checked) {\n        this._checked = newChecked;\n        if (this.buttonToggleGroup) {\n          this.buttonToggleGroup._syncButtonToggle(this, this._checked, true);\n          this.buttonToggleGroup._onTouched();\n        }\n      }\n      // Emit a change event when it's the single selector\n      this.change.emit(new MatButtonToggleChange(this, this.value));\n    }\n    /**\n     * Marks the button toggle as needing checking for change detection.\n     * This method is exposed because the parent button toggle group will directly\n     * update bound properties of the radio button.\n     */\n    _markForCheck() {\n      // When the group value changes, the button will not be notified.\n      // Use `markForCheck` to explicit update button toggle's status.\n      this._changeDetectorRef.markForCheck();\n    }\n    /** Gets the name that should be assigned to the inner DOM node. */\n    _getButtonName() {\n      if (this._isSingleSelector()) {\n        return this.buttonToggleGroup.name;\n      }\n      return this.name || null;\n    }\n    /** Whether the toggle is in single selection mode. */\n    _isSingleSelector() {\n      return this.buttonToggleGroup && !this.buttonToggleGroup.multiple;\n    }\n    static #_ = this.ɵfac = function MatButtonToggle_Factory(t) {\n      return new (t || MatButtonToggle)(i0.ɵɵdirectiveInject(MAT_BUTTON_TOGGLE_GROUP, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.FocusMonitor), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS, 8));\n    };\n    static #_2 = this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatButtonToggle,\n      selectors: [[\"mat-button-toggle\"]],\n      viewQuery: function MatButtonToggle_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._buttonElement = _t.first);\n        }\n      },\n      hostAttrs: [\"role\", \"presentation\", 1, \"mat-button-toggle\"],\n      hostVars: 12,\n      hostBindings: function MatButtonToggle_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"focus\", function MatButtonToggle_focus_HostBindingHandler() {\n            return ctx.focus();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-label\", null)(\"aria-labelledby\", null)(\"id\", ctx.id)(\"name\", null);\n          i0.ɵɵclassProp(\"mat-button-toggle-standalone\", !ctx.buttonToggleGroup)(\"mat-button-toggle-checked\", ctx.checked)(\"mat-button-toggle-disabled\", ctx.disabled)(\"mat-button-toggle-appearance-standard\", ctx.appearance === \"standard\");\n        }\n      },\n      inputs: {\n        disableRipple: \"disableRipple\",\n        ariaLabel: [\"aria-label\", \"ariaLabel\"],\n        ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"],\n        id: \"id\",\n        name: \"name\",\n        value: \"value\",\n        tabIndex: \"tabIndex\",\n        appearance: \"appearance\",\n        checked: \"checked\",\n        disabled: \"disabled\"\n      },\n      outputs: {\n        change: \"change\"\n      },\n      exportAs: [\"matButtonToggle\"],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c1,\n      decls: 6,\n      vars: 9,\n      consts: [[\"type\", \"button\", 1, \"mat-button-toggle-button\", \"mat-focus-indicator\", 3, \"id\", \"disabled\", \"click\"], [\"button\", \"\"], [1, \"mat-button-toggle-label-content\"], [1, \"mat-button-toggle-focus-overlay\"], [\"matRipple\", \"\", 1, \"mat-button-toggle-ripple\", 3, \"matRippleTrigger\", \"matRippleDisabled\"]],\n      template: function MatButtonToggle_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"button\", 0, 1);\n          i0.ɵɵlistener(\"click\", function MatButtonToggle_Template_button_click_0_listener() {\n            return ctx._onButtonClick();\n          });\n          i0.ɵɵelementStart(2, \"span\", 2);\n          i0.ɵɵprojection(3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(4, \"span\", 3)(5, \"span\", 4);\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(1);\n          i0.ɵɵproperty(\"id\", ctx.buttonId)(\"disabled\", ctx.disabled || null);\n          i0.ɵɵattribute(\"tabindex\", ctx.disabled ? -1 : ctx.tabIndex)(\"aria-pressed\", ctx.checked)(\"name\", ctx._getButtonName())(\"aria-label\", ctx.ariaLabel)(\"aria-labelledby\", ctx.ariaLabelledby);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"matRippleTrigger\", _r0)(\"matRippleDisabled\", ctx.disableRipple || ctx.disabled);\n        }\n      },\n      dependencies: [i2.MatRipple],\n      styles: [\".mat-button-toggle-standalone,.mat-button-toggle-group{--mat-legacy-button-toggle-height:36px;--mat-legacy-button-toggle-shape:2px;--mat-legacy-button-toggle-focus-state-layer-opacity:1;position:relative;display:inline-flex;flex-direction:row;white-space:nowrap;overflow:hidden;-webkit-tap-highlight-color:rgba(0,0,0,0);transform:translateZ(0);border-radius:var(--mat-legacy-button-toggle-shape)}.mat-button-toggle-standalone:not([class*=mat-elevation-z]),.mat-button-toggle-group:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.cdk-high-contrast-active .mat-button-toggle-standalone,.cdk-high-contrast-active .mat-button-toggle-group{outline:solid 1px}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard,.mat-button-toggle-group-appearance-standard{--mat-standard-button-toggle-shape:4px;--mat-standard-button-toggle-hover-state-layer-opacity:0.04;--mat-standard-button-toggle-focus-state-layer-opacity:0.12;border-radius:var(--mat-standard-button-toggle-shape);border:solid 1px var(--mat-standard-button-toggle-divider-color)}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard:not([class*=mat-elevation-z]),.mat-button-toggle-group-appearance-standard:not([class*=mat-elevation-z]){box-shadow:none}.cdk-high-contrast-active .mat-button-toggle-standalone.mat-button-toggle-appearance-standard,.cdk-high-contrast-active .mat-button-toggle-group-appearance-standard{outline:0}.mat-button-toggle-vertical{flex-direction:column}.mat-button-toggle-vertical .mat-button-toggle-label-content{display:block}.mat-button-toggle{white-space:nowrap;position:relative;color:var(--mat-legacy-button-toggle-text-color);font-family:var(--mat-legacy-button-toggle-text-font)}.mat-button-toggle.cdk-keyboard-focused .mat-button-toggle-focus-overlay{opacity:var(--mat-legacy-button-toggle-focus-state-layer-opacity)}.mat-button-toggle .mat-icon svg{vertical-align:top}.mat-button-toggle-checked{color:var(--mat-legacy-button-toggle-selected-state-text-color);background-color:var(--mat-legacy-button-toggle-selected-state-background-color)}.mat-button-toggle-disabled{color:var(--mat-legacy-button-toggle-disabled-state-text-color);background-color:var(--mat-legacy-button-toggle-disabled-state-background-color)}.mat-button-toggle-disabled.mat-button-toggle-checked{background-color:var(--mat-legacy-button-toggle-disabled-selected-state-background-color)}.mat-button-toggle-appearance-standard{--mat-standard-button-toggle-shape:4px;--mat-standard-button-toggle-hover-state-layer-opacity:0.04;--mat-standard-button-toggle-focus-state-layer-opacity:0.12;color:var(--mat-standard-button-toggle-text-color);background-color:var(--mat-standard-button-toggle-background-color);font-family:var(--mat-standard-button-toggle-text-font)}.mat-button-toggle-group-appearance-standard .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:solid 1px var(--mat-standard-button-toggle-divider-color)}[dir=rtl] .mat-button-toggle-group-appearance-standard .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:none;border-right:solid 1px var(--mat-standard-button-toggle-divider-color)}.mat-button-toggle-group-appearance-standard.mat-button-toggle-vertical .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:none;border-right:none;border-top:solid 1px var(--mat-standard-button-toggle-divider-color)}.mat-button-toggle-appearance-standard.mat-button-toggle-checked{color:var(--mat-standard-button-toggle-selected-state-text-color);background-color:var(--mat-standard-button-toggle-selected-state-background-color)}.mat-button-toggle-appearance-standard.mat-button-toggle-disabled{color:var(--mat-standard-button-toggle-disabled-state-text-color);background-color:var(--mat-standard-button-toggle-disabled-state-background-color)}.mat-button-toggle-appearance-standard.mat-button-toggle-disabled.mat-button-toggle-checked{color:var(--mat-standard-button-toggle-disabled-selected-state-text-color);background-color:var(--mat-standard-button-toggle-disabled-selected-state-background-color)}.mat-button-toggle-appearance-standard .mat-button-toggle-focus-overlay{background-color:var(--mat-standard-button-toggle-state-layer-color)}.mat-button-toggle-appearance-standard:not(.mat-button-toggle-disabled):hover .mat-button-toggle-focus-overlay{opacity:var(--mat-standard-button-toggle-hover-state-layer-opacity)}.mat-button-toggle-appearance-standard.cdk-keyboard-focused:not(.mat-button-toggle-disabled) .mat-button-toggle-focus-overlay{opacity:var(--mat-standard-button-toggle-focus-state-layer-opacity)}@media(hover: none){.mat-button-toggle-appearance-standard:not(.mat-button-toggle-disabled):hover .mat-button-toggle-focus-overlay{display:none}}.mat-button-toggle-label-content{-webkit-user-select:none;user-select:none;display:inline-block;padding:0 16px;line-height:var(--mat-legacy-button-toggle-height);position:relative}.mat-button-toggle-appearance-standard .mat-button-toggle-label-content{padding:0 12px;line-height:var(--mat-standard-button-toggle-height)}.mat-button-toggle-label-content>*{vertical-align:middle}.mat-button-toggle-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit;pointer-events:none;opacity:0;background-color:var(--mat-legacy-button-toggle-state-layer-color)}.cdk-high-contrast-active .mat-button-toggle-checked .mat-button-toggle-focus-overlay{border-bottom:solid 500px;opacity:.5;height:0}.cdk-high-contrast-active .mat-button-toggle-checked:hover .mat-button-toggle-focus-overlay{opacity:.6}.cdk-high-contrast-active .mat-button-toggle-checked.mat-button-toggle-appearance-standard .mat-button-toggle-focus-overlay{border-bottom:solid 500px}.mat-button-toggle .mat-button-toggle-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-button-toggle-button{border:0;background:none;color:inherit;padding:0;margin:0;font:inherit;outline:none;width:100%;cursor:pointer}.mat-button-toggle-disabled .mat-button-toggle-button{cursor:default}.mat-button-toggle-button::-moz-focus-inner{border:0}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatButtonToggle;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatButtonToggleModule = /*#__PURE__*/(() => {\n  class MatButtonToggleModule {\n    static #_ = this.ɵfac = function MatButtonToggleModule_Factory(t) {\n      return new (t || MatButtonToggleModule)();\n    };\n    static #_2 = this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatButtonToggleModule\n    });\n    static #_3 = this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, MatRippleModule, MatCommonModule]\n    });\n  }\n  return MatButtonToggleModule;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS, MAT_BUTTON_TOGGLE_GROUP, MAT_BUTTON_TOGGLE_GROUP_VALUE_ACCESSOR, MatButtonToggle, MatButtonToggleChange, MatButtonToggleGroup, MatButtonToggleModule };\n//# sourceMappingURL=button-toggle.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}