.settings-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* Header Styles */
.page-header {
  margin-bottom: 24px;
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content h1 {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1.8rem;
  font-weight: 600;
}

.header-content h1 mat-icon {
  color: #1976d2;
}

.subtitle {
  margin: 0;
  color: #666;
  font-size: 1rem;
}

/* Settings Card */
.settings-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
}

/* Tab Content */
.tab-content {
  padding: 32px;
  max-width: 800px;
}

.section-header {
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.section-header h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1.5rem;
  font-weight: 600;
}

.section-header p {
  margin: 0;
  color: #666;
  font-size: 0.95rem;
}

/* Form Styles */
.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

.password-grid {
  grid-template-columns: 1fr;
  max-width: 400px;
}

.form-grid mat-form-field {
  width: 100%;
}

.form-grid mat-form-field:last-child:nth-child(odd) {
  grid-column: 1 / -1;
}

.form-actions {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  padding-top: 24px;
  border-top: 1px solid #e0e0e0;
}

.form-actions button {
  min-width: 140px;
  height: 44px;
  font-weight: 500;
}

.spinner {
  margin-left: 8px;
}

/* Settings Sections */
.settings-section {
  margin-bottom: 32px;
  padding: 24px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #1976d2;
}

.settings-section h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

.toggle-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.toggle-grid mat-slide-toggle {
  font-size: 0.95rem;
}

/* Data Management */
.data-actions {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
}

.action-card {
  border-radius: 8px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.action-content {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 8px;
}

.action-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  color: #1976d2;
  flex-shrink: 0;
}

.action-info {
  flex: 1;
}

.action-info h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
}

.action-info p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.action-content button {
  min-width: 120px;
  height: 40px;
  flex-shrink: 0;
}

/* Tab Styling */
::ng-deep .mat-tab-group {
  background: white;
}

::ng-deep .mat-tab-header {
  border-bottom: 1px solid #e0e0e0;
}

::ng-deep .mat-tab-label {
  min-width: 120px;
  padding: 0 24px;
  font-weight: 500;
}

::ng-deep .mat-tab-label-active {
  color: #1976d2;
}

::ng-deep .mat-ink-bar {
  background-color: #1976d2;
  height: 3px;
}

/* Form Field Styling */
::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
  color: #e0e0e0;
}

::ng-deep .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
  color: #1976d2;
}

::ng-deep .mat-form-field-appearance-outline .mat-form-field-label {
  color: #666;
}

::ng-deep .mat-form-field-appearance-outline.mat-focused .mat-form-field-label {
  color: #1976d2;
}

/* Slide Toggle Styling */
::ng-deep .mat-slide-toggle.mat-checked .mat-slide-toggle-thumb {
  background-color: #1976d2;
}

::ng-deep .mat-slide-toggle.mat-checked .mat-slide-toggle-bar {
  background-color: rgba(25, 118, 210, 0.5);
}

/* Responsive Design */
@media (max-width: 768px) {
  .settings-container {
    padding: 16px;
  }

  .tab-content {
    padding: 24px 16px;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .toggle-grid {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .form-actions button {
    width: 100%;
  }

  .action-content {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .action-content button {
    width: 100%;
  }

  .settings-section {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: 16px;
  }

  .header-content h1 {
    font-size: 1.5rem;
  }

  .section-header h2 {
    font-size: 1.3rem;
  }

  ::ng-deep .mat-tab-label {
    min-width: 80px;
    padding: 0 12px;
    font-size: 0.9rem;
  }
}
