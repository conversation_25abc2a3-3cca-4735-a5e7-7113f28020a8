{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { StudentsRoutingModule } from './students-routing.module';\nimport { FormsModule } from '@angular/forms';\nimport { MaterialModule } from 'src/app/material';\nimport * as i0 from \"@angular/core\";\nexport let StudentsModule = /*#__PURE__*/(() => {\n  class StudentsModule {\n    static #_ = this.ɵfac = function StudentsModule_Factory(t) {\n      return new (t || StudentsModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: StudentsModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, StudentsRoutingModule, FormsModule, MaterialModule]\n    });\n  }\n  return StudentsModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}