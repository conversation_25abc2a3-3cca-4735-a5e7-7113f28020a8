{"ast": null, "code": "/**\n * User Experience Text Standards\n * This file contains standardized, user-friendly text for labels, messages, and tooltips\n * to ensure consistent and clear communication across the application.\n */\nexport const UX_TEXT_STANDARDS = {\n  // Form Labels - Simple and Clear\n  FORM_LABELS: {\n    FULL_NAME: 'Full Name',\n    EMAIL_ADDRESS: 'Email Address',\n    PHONE_NUMBER: 'Phone Number',\n    HOME_ADDRESS: 'Home Address',\n    FATHER_NAME: 'Father\\'s Name',\n    STUDENT_ID: 'Student ID',\n    REGISTRATION_NUMBER: 'Registration Number',\n    ROLL_NUMBER: 'Roll Number',\n    PASSWORD: 'Password',\n    CONFIRM_PASSWORD: 'Confirm Password',\n    DEPARTMENT: 'Department',\n    PROGRAM: 'Program',\n    CLASS_YEAR: 'Class/Year',\n    SECTION: 'Section',\n    SEMESTER: 'Semester',\n    SUBJECT: 'Subject',\n    DESIGNATION: 'Job Title',\n    POSITION: 'Position',\n    IS_VISITING: 'Visiting Faculty',\n    IS_ACTIVE: 'Active Status'\n  },\n  // Helpful Tooltips\n  TOOLTIPS: {\n    FULL_NAME: 'Enter your complete name as it appears on official documents',\n    EMAIL_ADDRESS: 'Use your college email address or personal email for notifications',\n    PHONE_NUMBER: 'Enter a 10-15 digit phone number (e.g., 03001234567)',\n    STUDENT_ID: 'Your unique student identification number assigned by the college',\n    REGISTRATION_NUMBER: 'Official registration number from college records',\n    ROLL_NUMBER: 'Your class roll number for attendance and exams',\n    PASSWORD: 'Choose a strong password with at least 6 characters',\n    DEPARTMENT: 'Select the academic department you belong to',\n    PROGRAM: 'Choose your study program (BS, Intermediate, etc.)',\n    CLASS_YEAR: 'Select your current academic year or class level',\n    SECTION: 'Choose your class section (A, B, C, etc.)',\n    SEMESTER: 'Select your current semester number',\n    VISITING_FACULTY: 'Check if this teacher is a visiting faculty member',\n    ACTIVE_STATUS: 'Uncheck to temporarily disable this account',\n    REFRESH_DATA: 'Click to reload the latest information',\n    MARK_ATTENDANCE: 'Record student attendance for today\\'s class',\n    VIEW_DETAILS: 'Click to see complete information',\n    EDIT_RECORD: 'Click to modify this information',\n    DELETE_RECORD: 'Permanently remove this record (cannot be undone)'\n  },\n  // User-Friendly Error Messages\n  ERROR_MESSAGES: {\n    REQUIRED_FIELD: 'This field is required',\n    INVALID_EMAIL: 'Please enter a valid email address (e.g., <EMAIL>)',\n    INVALID_PHONE: 'Please enter a valid phone number (10-15 digits)',\n    PASSWORD_TOO_SHORT: 'Password must be at least 6 characters long',\n    PASSWORDS_DONT_MATCH: 'Passwords do not match. Please try again',\n    NAME_TOO_SHORT: 'Name must be at least 2 characters long',\n    INVALID_SELECTION: 'Please select a valid option from the list',\n    NETWORK_ERROR: 'Unable to connect to the server. Please check your internet connection and try again',\n    UNAUTHORIZED: 'Your session has expired. Please log in again',\n    FORBIDDEN: 'You don\\'t have permission to perform this action',\n    NOT_FOUND: 'The requested information could not be found',\n    SERVER_ERROR: 'Something went wrong on our end. Please try again in a few minutes',\n    VALIDATION_ERROR: 'Please check your information and try again'\n  },\n  // Encouraging Success Messages\n  SUCCESS_MESSAGES: {\n    USER_CREATED: 'Great! New user account has been created successfully',\n    USER_UPDATED: 'Perfect! User information has been updated',\n    USER_DELETED: 'User account has been removed successfully',\n    LOGIN_SUCCESS: 'Welcome back! You have logged in successfully',\n    LOGOUT_SUCCESS: 'You have been logged out safely',\n    PASSWORD_CHANGED: 'Your password has been updated successfully',\n    ATTENDANCE_MARKED: 'Attendance has been recorded for all students',\n    NOTICE_PUBLISHED: 'Your notice has been published and students will be notified',\n    COMPLAINT_SUBMITTED: 'Your complaint has been submitted. We will review it soon',\n    DATA_SAVED: 'All changes have been saved successfully',\n    EMAIL_SENT: 'Email has been sent successfully',\n    PROFILE_UPDATED: 'Your profile has been updated successfully'\n  },\n  // Loading Messages\n  LOADING_MESSAGES: {\n    SIGNING_IN: 'Signing you in...',\n    LOADING_DASHBOARD: 'Loading your dashboard...',\n    SAVING_DATA: 'Saving your information...',\n    LOADING_STUDENTS: 'Loading student information...',\n    LOADING_TEACHERS: 'Loading teacher information...',\n    LOADING_CLASSES: 'Loading class information...',\n    LOADING_SUBJECTS: 'Loading subjects...',\n    LOADING_ATTENDANCE: 'Loading attendance records...',\n    LOADING_NOTICES: 'Loading notices...',\n    PROCESSING_REQUEST: 'Processing your request...',\n    UPLOADING_FILE: 'Uploading file...',\n    GENERATING_REPORT: 'Generating your report...'\n  },\n  // Navigation Labels\n  NAVIGATION: {\n    DASHBOARD: 'Dashboard',\n    MY_PROFILE: 'My Profile',\n    STUDENTS: 'Students',\n    TEACHERS: 'Teachers',\n    CLASSES: 'Classes',\n    SUBJECTS: 'Subjects',\n    ATTENDANCE: 'Attendance',\n    NOTICES: 'Notices & Announcements',\n    REPORTS: 'Reports',\n    SETTINGS: 'Settings',\n    LOGOUT: 'Sign Out',\n    MY_SUBJECTS: 'My Subjects',\n    MY_CLASSES: 'My Classes',\n    MY_STUDENTS: 'My Students',\n    MY_ATTENDANCE: 'My Attendance',\n    COMPLAINTS: 'Complaints & Feedback',\n    TIMETABLE: 'Class Schedule',\n    PROGRAMS: 'Academic Programs',\n    DEPARTMENTS: 'Departments'\n  },\n  // Button Labels\n  BUTTONS: {\n    SAVE: 'Save Changes',\n    CANCEL: 'Cancel',\n    DELETE: 'Delete',\n    EDIT: 'Edit',\n    ADD: 'Add New',\n    SUBMIT: 'Submit',\n    RESET: 'Reset Form',\n    SEARCH: 'Search',\n    FILTER: 'Filter Results',\n    EXPORT: 'Export Data',\n    PRINT: 'Print',\n    REFRESH: 'Refresh',\n    BACK: 'Go Back',\n    NEXT: 'Next',\n    PREVIOUS: 'Previous',\n    CONFIRM: 'Confirm',\n    MARK_PRESENT: 'Mark Present',\n    MARK_ABSENT: 'Mark Absent',\n    MARK_ATTENDANCE: 'Record Attendance',\n    VIEW_DETAILS: 'View Details',\n    ADD_STUDENT: 'Add New Student',\n    ADD_TEACHER: 'Add New Teacher',\n    ADD_CLASS: 'Create New Class',\n    ADD_SUBJECT: 'Add New Subject',\n    PUBLISH_NOTICE: 'Publish Notice',\n    SEND_MESSAGE: 'Send Message'\n  },\n  // Status Labels\n  STATUS: {\n    ACTIVE: 'Active',\n    INACTIVE: 'Inactive',\n    PRESENT: 'Present',\n    ABSENT: 'Absent',\n    LATE: 'Late',\n    EXCUSED: 'Excused',\n    PUBLISHED: 'Published',\n    DRAFT: 'Draft',\n    PENDING: 'Pending',\n    APPROVED: 'Approved',\n    REJECTED: 'Rejected',\n    COMPLETED: 'Completed',\n    IN_PROGRESS: 'In Progress'\n  },\n  // Placeholder Text\n  PLACEHOLDERS: {\n    SEARCH_USERS: 'Search by name, email, or ID...',\n    SEARCH_STUDENTS: 'Search students by name or roll number...',\n    SEARCH_TEACHERS: 'Search teachers by name or department...',\n    ENTER_NAME: 'Enter full name',\n    ENTER_EMAIL: 'Enter email address',\n    ENTER_PHONE: 'e.g., 03001234567',\n    ENTER_ADDRESS: 'Enter complete address',\n    SELECT_DEPARTMENT: 'Choose department',\n    SELECT_PROGRAM: 'Choose program',\n    SELECT_CLASS: 'Choose class',\n    SELECT_SECTION: 'Choose section',\n    ENTER_SUBJECT_NAME: 'Enter subject name',\n    ENTER_NOTICE_TITLE: 'Enter notice title',\n    ENTER_NOTICE_CONTENT: 'Write your notice content here...',\n    ENTER_COMPLAINT: 'Describe your complaint or feedback...'\n  }\n};\n// Role-specific text variations\nexport const ROLE_SPECIFIC_TEXT = {\n  PRINCIPAL: {\n    DASHBOARD_WELCOME: 'Welcome to your Administrative Dashboard',\n    QUICK_ACTIONS_TITLE: 'Quick Administrative Actions',\n    OVERVIEW_TITLE: 'College Overview'\n  },\n  TEACHER: {\n    DASHBOARD_WELCOME: 'Welcome to your Teaching Dashboard',\n    QUICK_ACTIONS_TITLE: 'Quick Teaching Actions',\n    OVERVIEW_TITLE: 'My Teaching Overview'\n  },\n  STUDENT: {\n    DASHBOARD_WELCOME: 'Welcome to your Student Portal',\n    QUICK_ACTIONS_TITLE: 'Quick Student Actions',\n    OVERVIEW_TITLE: 'My Academic Overview'\n  }\n};\n// Confirmation Messages\nexport const CONFIRMATION_MESSAGES = {\n  DELETE_USER: 'Are you sure you want to delete this user? This action cannot be undone.',\n  DELETE_STUDENT: 'Are you sure you want to remove this student? This will delete all their records.',\n  DELETE_TEACHER: 'Are you sure you want to remove this teacher? This will affect their assigned classes.',\n  DELETE_CLASS: 'Are you sure you want to delete this class? This will affect all enrolled students.',\n  DELETE_SUBJECT: 'Are you sure you want to delete this subject? This will affect all related records.',\n  DELETE_NOTICE: 'Are you sure you want to delete this notice? Students will no longer see it.',\n  MARK_ATTENDANCE: 'Are you sure you want to submit attendance for all students?',\n  PUBLISH_NOTICE: 'Are you sure you want to publish this notice? All targeted users will be notified.',\n  LOGOUT: 'Are you sure you want to sign out?'\n};", "map": {"version": 3, "names": ["UX_TEXT_STANDARDS", "FORM_LABELS", "FULL_NAME", "EMAIL_ADDRESS", "PHONE_NUMBER", "HOME_ADDRESS", "FATHER_NAME", "STUDENT_ID", "REGISTRATION_NUMBER", "ROLL_NUMBER", "PASSWORD", "CONFIRM_PASSWORD", "DEPARTMENT", "PROGRAM", "CLASS_YEAR", "SECTION", "SEMESTER", "SUBJECT", "DESIGNATION", "POSITION", "IS_VISITING", "IS_ACTIVE", "TOOLTIPS", "VISITING_FACULTY", "ACTIVE_STATUS", "REFRESH_DATA", "MARK_ATTENDANCE", "VIEW_DETAILS", "EDIT_RECORD", "DELETE_RECORD", "ERROR_MESSAGES", "REQUIRED_FIELD", "INVALID_EMAIL", "INVALID_PHONE", "PASSWORD_TOO_SHORT", "PASSWORDS_DONT_MATCH", "NAME_TOO_SHORT", "INVALID_SELECTION", "NETWORK_ERROR", "UNAUTHORIZED", "FORBIDDEN", "NOT_FOUND", "SERVER_ERROR", "VALIDATION_ERROR", "SUCCESS_MESSAGES", "USER_CREATED", "USER_UPDATED", "USER_DELETED", "LOGIN_SUCCESS", "LOGOUT_SUCCESS", "PASSWORD_CHANGED", "ATTENDANCE_MARKED", "NOTICE_PUBLISHED", "COMPLAINT_SUBMITTED", "DATA_SAVED", "EMAIL_SENT", "PROFILE_UPDATED", "LOADING_MESSAGES", "SIGNING_IN", "LOADING_DASHBOARD", "SAVING_DATA", "LOADING_STUDENTS", "LOADING_TEACHERS", "LOADING_CLASSES", "LOADING_SUBJECTS", "LOADING_ATTENDANCE", "LOADING_NOTICES", "PROCESSING_REQUEST", "UPLOADING_FILE", "GENERATING_REPORT", "NAVIGATION", "DASHBOARD", "MY_PROFILE", "STUDENTS", "TEACHERS", "CLASSES", "SUBJECTS", "ATTENDANCE", "NOTICES", "REPORTS", "SETTINGS", "LOGOUT", "MY_SUBJECTS", "MY_CLASSES", "MY_STUDENTS", "MY_ATTENDANCE", "COMPLAINTS", "TIMETABLE", "PROGRAMS", "DEPARTMENTS", "BUTTONS", "SAVE", "CANCEL", "DELETE", "EDIT", "ADD", "SUBMIT", "RESET", "SEARCH", "FILTER", "EXPORT", "PRINT", "REFRESH", "BACK", "NEXT", "PREVIOUS", "CONFIRM", "MARK_PRESENT", "MARK_ABSENT", "ADD_STUDENT", "ADD_TEACHER", "ADD_CLASS", "ADD_SUBJECT", "PUBLISH_NOTICE", "SEND_MESSAGE", "STATUS", "ACTIVE", "INACTIVE", "PRESENT", "ABSENT", "LATE", "EXCUSED", "PUBLISHED", "DRAFT", "PENDING", "APPROVED", "REJECTED", "COMPLETED", "IN_PROGRESS", "PLACEHOLDERS", "SEARCH_USERS", "SEARCH_STUDENTS", "SEARCH_TEACHERS", "ENTER_NAME", "ENTER_EMAIL", "ENTER_PHONE", "ENTER_ADDRESS", "SELECT_DEPARTMENT", "SELECT_PROGRAM", "SELECT_CLASS", "SELECT_SECTION", "ENTER_SUBJECT_NAME", "ENTER_NOTICE_TITLE", "ENTER_NOTICE_CONTENT", "ENTER_COMPLAINT", "ROLE_SPECIFIC_TEXT", "PRINCIPAL", "DASHBOARD_WELCOME", "QUICK_ACTIONS_TITLE", "OVERVIEW_TITLE", "TEACHER", "STUDENT", "CONFIRMATION_MESSAGES", "DELETE_USER", "DELETE_STUDENT", "DELETE_TEACHER", "DELETE_CLASS", "DELETE_SUBJECT", "DELETE_NOTICE"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\shared\\constants\\ux-text-standards.ts"], "sourcesContent": ["/**\r\n * User Experience Text Standards\r\n * This file contains standardized, user-friendly text for labels, messages, and tooltips\r\n * to ensure consistent and clear communication across the application.\r\n */\r\n\r\nexport const UX_TEXT_STANDARDS = {\r\n  // Form Labels - Simple and Clear\r\n  FORM_LABELS: {\r\n    FULL_NAME: 'Full Name',\r\n    EMAIL_ADDRESS: 'Email Address',\r\n    PHONE_NUMBER: 'Phone Number',\r\n    HOME_ADDRESS: 'Home Address',\r\n    FATHER_NAME: 'Father\\'s Name',\r\n    STUDENT_ID: 'Student ID',\r\n    REGISTRATION_NUMBER: 'Registration Number',\r\n    ROLL_NUMBER: 'Roll Number',\r\n    PASSWORD: 'Password',\r\n    CONFIRM_PASSWORD: 'Confirm Password',\r\n    DEPARTMENT: 'Department',\r\n    PROGRAM: 'Program',\r\n    CLASS_YEAR: 'Class/Year',\r\n    SECTION: 'Section',\r\n    SEMESTER: 'Semester',\r\n    SUBJECT: 'Subject',\r\n    DESIGNATION: 'Job Title',\r\n    POSITION: 'Position',\r\n    IS_VISITING: 'Visiting Faculty',\r\n    IS_ACTIVE: 'Active Status'\r\n  },\r\n\r\n  // Helpful Tooltips\r\n  TOOLTIPS: {\r\n    FULL_NAME: 'Enter your complete name as it appears on official documents',\r\n    EMAIL_ADDRESS: 'Use your college email address or personal email for notifications',\r\n    PHONE_NUMBER: 'Enter a 10-15 digit phone number (e.g., 03001234567)',\r\n    STUDENT_ID: 'Your unique student identification number assigned by the college',\r\n    REGISTRATION_NUMBER: 'Official registration number from college records',\r\n    ROLL_NUMBER: 'Your class roll number for attendance and exams',\r\n    PASSWORD: 'Choose a strong password with at least 6 characters',\r\n    DEPARTMENT: 'Select the academic department you belong to',\r\n    PROGRAM: 'Choose your study program (BS, Intermediate, etc.)',\r\n    CLASS_YEAR: 'Select your current academic year or class level',\r\n    SECTION: 'Choose your class section (A, B, C, etc.)',\r\n    SEMESTER: 'Select your current semester number',\r\n    VISITING_FACULTY: 'Check if this teacher is a visiting faculty member',\r\n    ACTIVE_STATUS: 'Uncheck to temporarily disable this account',\r\n    REFRESH_DATA: 'Click to reload the latest information',\r\n    MARK_ATTENDANCE: 'Record student attendance for today\\'s class',\r\n    VIEW_DETAILS: 'Click to see complete information',\r\n    EDIT_RECORD: 'Click to modify this information',\r\n    DELETE_RECORD: 'Permanently remove this record (cannot be undone)'\r\n  },\r\n\r\n  // User-Friendly Error Messages\r\n  ERROR_MESSAGES: {\r\n    REQUIRED_FIELD: 'This field is required',\r\n    INVALID_EMAIL: 'Please enter a valid email address (e.g., <EMAIL>)',\r\n    INVALID_PHONE: 'Please enter a valid phone number (10-15 digits)',\r\n    PASSWORD_TOO_SHORT: 'Password must be at least 6 characters long',\r\n    PASSWORDS_DONT_MATCH: 'Passwords do not match. Please try again',\r\n    NAME_TOO_SHORT: 'Name must be at least 2 characters long',\r\n    INVALID_SELECTION: 'Please select a valid option from the list',\r\n    NETWORK_ERROR: 'Unable to connect to the server. Please check your internet connection and try again',\r\n    UNAUTHORIZED: 'Your session has expired. Please log in again',\r\n    FORBIDDEN: 'You don\\'t have permission to perform this action',\r\n    NOT_FOUND: 'The requested information could not be found',\r\n    SERVER_ERROR: 'Something went wrong on our end. Please try again in a few minutes',\r\n    VALIDATION_ERROR: 'Please check your information and try again'\r\n  },\r\n\r\n  // Encouraging Success Messages\r\n  SUCCESS_MESSAGES: {\r\n    USER_CREATED: 'Great! New user account has been created successfully',\r\n    USER_UPDATED: 'Perfect! User information has been updated',\r\n    USER_DELETED: 'User account has been removed successfully',\r\n    LOGIN_SUCCESS: 'Welcome back! You have logged in successfully',\r\n    LOGOUT_SUCCESS: 'You have been logged out safely',\r\n    PASSWORD_CHANGED: 'Your password has been updated successfully',\r\n    ATTENDANCE_MARKED: 'Attendance has been recorded for all students',\r\n    NOTICE_PUBLISHED: 'Your notice has been published and students will be notified',\r\n    COMPLAINT_SUBMITTED: 'Your complaint has been submitted. We will review it soon',\r\n    DATA_SAVED: 'All changes have been saved successfully',\r\n    EMAIL_SENT: 'Email has been sent successfully',\r\n    PROFILE_UPDATED: 'Your profile has been updated successfully'\r\n  },\r\n\r\n  // Loading Messages\r\n  LOADING_MESSAGES: {\r\n    SIGNING_IN: 'Signing you in...',\r\n    LOADING_DASHBOARD: 'Loading your dashboard...',\r\n    SAVING_DATA: 'Saving your information...',\r\n    LOADING_STUDENTS: 'Loading student information...',\r\n    LOADING_TEACHERS: 'Loading teacher information...',\r\n    LOADING_CLASSES: 'Loading class information...',\r\n    LOADING_SUBJECTS: 'Loading subjects...',\r\n    LOADING_ATTENDANCE: 'Loading attendance records...',\r\n    LOADING_NOTICES: 'Loading notices...',\r\n    PROCESSING_REQUEST: 'Processing your request...',\r\n    UPLOADING_FILE: 'Uploading file...',\r\n    GENERATING_REPORT: 'Generating your report...'\r\n  },\r\n\r\n  // Navigation Labels\r\n  NAVIGATION: {\r\n    DASHBOARD: 'Dashboard',\r\n    MY_PROFILE: 'My Profile',\r\n    STUDENTS: 'Students',\r\n    TEACHERS: 'Teachers',\r\n    CLASSES: 'Classes',\r\n    SUBJECTS: 'Subjects',\r\n    ATTENDANCE: 'Attendance',\r\n    NOTICES: 'Notices & Announcements',\r\n    REPORTS: 'Reports',\r\n    SETTINGS: 'Settings',\r\n    LOGOUT: 'Sign Out',\r\n    MY_SUBJECTS: 'My Subjects',\r\n    MY_CLASSES: 'My Classes',\r\n    MY_STUDENTS: 'My Students',\r\n    MY_ATTENDANCE: 'My Attendance',\r\n    COMPLAINTS: 'Complaints & Feedback',\r\n    TIMETABLE: 'Class Schedule',\r\n    PROGRAMS: 'Academic Programs',\r\n    DEPARTMENTS: 'Departments'\r\n  },\r\n\r\n  // Button Labels\r\n  BUTTONS: {\r\n    SAVE: 'Save Changes',\r\n    CANCEL: 'Cancel',\r\n    DELETE: 'Delete',\r\n    EDIT: 'Edit',\r\n    ADD: 'Add New',\r\n    SUBMIT: 'Submit',\r\n    RESET: 'Reset Form',\r\n    SEARCH: 'Search',\r\n    FILTER: 'Filter Results',\r\n    EXPORT: 'Export Data',\r\n    PRINT: 'Print',\r\n    REFRESH: 'Refresh',\r\n    BACK: 'Go Back',\r\n    NEXT: 'Next',\r\n    PREVIOUS: 'Previous',\r\n    CONFIRM: 'Confirm',\r\n    MARK_PRESENT: 'Mark Present',\r\n    MARK_ABSENT: 'Mark Absent',\r\n    MARK_ATTENDANCE: 'Record Attendance',\r\n    VIEW_DETAILS: 'View Details',\r\n    ADD_STUDENT: 'Add New Student',\r\n    ADD_TEACHER: 'Add New Teacher',\r\n    ADD_CLASS: 'Create New Class',\r\n    ADD_SUBJECT: 'Add New Subject',\r\n    PUBLISH_NOTICE: 'Publish Notice',\r\n    SEND_MESSAGE: 'Send Message'\r\n  },\r\n\r\n  // Status Labels\r\n  STATUS: {\r\n    ACTIVE: 'Active',\r\n    INACTIVE: 'Inactive',\r\n    PRESENT: 'Present',\r\n    ABSENT: 'Absent',\r\n    LATE: 'Late',\r\n    EXCUSED: 'Excused',\r\n    PUBLISHED: 'Published',\r\n    DRAFT: 'Draft',\r\n    PENDING: 'Pending',\r\n    APPROVED: 'Approved',\r\n    REJECTED: 'Rejected',\r\n    COMPLETED: 'Completed',\r\n    IN_PROGRESS: 'In Progress'\r\n  },\r\n\r\n  // Placeholder Text\r\n  PLACEHOLDERS: {\r\n    SEARCH_USERS: 'Search by name, email, or ID...',\r\n    SEARCH_STUDENTS: 'Search students by name or roll number...',\r\n    SEARCH_TEACHERS: 'Search teachers by name or department...',\r\n    ENTER_NAME: 'Enter full name',\r\n    ENTER_EMAIL: 'Enter email address',\r\n    ENTER_PHONE: 'e.g., 03001234567',\r\n    ENTER_ADDRESS: 'Enter complete address',\r\n    SELECT_DEPARTMENT: 'Choose department',\r\n    SELECT_PROGRAM: 'Choose program',\r\n    SELECT_CLASS: 'Choose class',\r\n    SELECT_SECTION: 'Choose section',\r\n    ENTER_SUBJECT_NAME: 'Enter subject name',\r\n    ENTER_NOTICE_TITLE: 'Enter notice title',\r\n    ENTER_NOTICE_CONTENT: 'Write your notice content here...',\r\n    ENTER_COMPLAINT: 'Describe your complaint or feedback...'\r\n  }\r\n};\r\n\r\n// Role-specific text variations\r\nexport const ROLE_SPECIFIC_TEXT = {\r\n  PRINCIPAL: {\r\n    DASHBOARD_WELCOME: 'Welcome to your Administrative Dashboard',\r\n    QUICK_ACTIONS_TITLE: 'Quick Administrative Actions',\r\n    OVERVIEW_TITLE: 'College Overview'\r\n  },\r\n  TEACHER: {\r\n    DASHBOARD_WELCOME: 'Welcome to your Teaching Dashboard',\r\n    QUICK_ACTIONS_TITLE: 'Quick Teaching Actions',\r\n    OVERVIEW_TITLE: 'My Teaching Overview'\r\n  },\r\n  STUDENT: {\r\n    DASHBOARD_WELCOME: 'Welcome to your Student Portal',\r\n    QUICK_ACTIONS_TITLE: 'Quick Student Actions',\r\n    OVERVIEW_TITLE: 'My Academic Overview'\r\n  }\r\n};\r\n\r\n// Confirmation Messages\r\nexport const CONFIRMATION_MESSAGES = {\r\n  DELETE_USER: 'Are you sure you want to delete this user? This action cannot be undone.',\r\n  DELETE_STUDENT: 'Are you sure you want to remove this student? This will delete all their records.',\r\n  DELETE_TEACHER: 'Are you sure you want to remove this teacher? This will affect their assigned classes.',\r\n  DELETE_CLASS: 'Are you sure you want to delete this class? This will affect all enrolled students.',\r\n  DELETE_SUBJECT: 'Are you sure you want to delete this subject? This will affect all related records.',\r\n  DELETE_NOTICE: 'Are you sure you want to delete this notice? Students will no longer see it.',\r\n  MARK_ATTENDANCE: 'Are you sure you want to submit attendance for all students?',\r\n  PUBLISH_NOTICE: 'Are you sure you want to publish this notice? All targeted users will be notified.',\r\n  LOGOUT: 'Are you sure you want to sign out?'\r\n};\r\n"], "mappings": "AAAA;;;;;AAMA,OAAO,MAAMA,iBAAiB,GAAG;EAC/B;EACAC,WAAW,EAAE;IACXC,SAAS,EAAE,WAAW;IACtBC,aAAa,EAAE,eAAe;IAC9BC,YAAY,EAAE,cAAc;IAC5BC,YAAY,EAAE,cAAc;IAC5BC,WAAW,EAAE,gBAAgB;IAC7BC,UAAU,EAAE,YAAY;IACxBC,mBAAmB,EAAE,qBAAqB;IAC1CC,WAAW,EAAE,aAAa;IAC1BC,QAAQ,EAAE,UAAU;IACpBC,gBAAgB,EAAE,kBAAkB;IACpCC,UAAU,EAAE,YAAY;IACxBC,OAAO,EAAE,SAAS;IAClBC,UAAU,EAAE,YAAY;IACxBC,OAAO,EAAE,SAAS;IAClBC,QAAQ,EAAE,UAAU;IACpBC,OAAO,EAAE,SAAS;IAClBC,WAAW,EAAE,WAAW;IACxBC,QAAQ,EAAE,UAAU;IACpBC,WAAW,EAAE,kBAAkB;IAC/BC,SAAS,EAAE;GACZ;EAED;EACAC,QAAQ,EAAE;IACRpB,SAAS,EAAE,8DAA8D;IACzEC,aAAa,EAAE,oEAAoE;IACnFC,YAAY,EAAE,sDAAsD;IACpEG,UAAU,EAAE,mEAAmE;IAC/EC,mBAAmB,EAAE,mDAAmD;IACxEC,WAAW,EAAE,iDAAiD;IAC9DC,QAAQ,EAAE,qDAAqD;IAC/DE,UAAU,EAAE,8CAA8C;IAC1DC,OAAO,EAAE,oDAAoD;IAC7DC,UAAU,EAAE,kDAAkD;IAC9DC,OAAO,EAAE,2CAA2C;IACpDC,QAAQ,EAAE,qCAAqC;IAC/CO,gBAAgB,EAAE,oDAAoD;IACtEC,aAAa,EAAE,6CAA6C;IAC5DC,YAAY,EAAE,wCAAwC;IACtDC,eAAe,EAAE,8CAA8C;IAC/DC,YAAY,EAAE,mCAAmC;IACjDC,WAAW,EAAE,kCAAkC;IAC/CC,aAAa,EAAE;GAChB;EAED;EACAC,cAAc,EAAE;IACdC,cAAc,EAAE,wBAAwB;IACxCC,aAAa,EAAE,6DAA6D;IAC5EC,aAAa,EAAE,kDAAkD;IACjEC,kBAAkB,EAAE,6CAA6C;IACjEC,oBAAoB,EAAE,0CAA0C;IAChEC,cAAc,EAAE,yCAAyC;IACzDC,iBAAiB,EAAE,4CAA4C;IAC/DC,aAAa,EAAE,sFAAsF;IACrGC,YAAY,EAAE,+CAA+C;IAC7DC,SAAS,EAAE,mDAAmD;IAC9DC,SAAS,EAAE,8CAA8C;IACzDC,YAAY,EAAE,oEAAoE;IAClFC,gBAAgB,EAAE;GACnB;EAED;EACAC,gBAAgB,EAAE;IAChBC,YAAY,EAAE,uDAAuD;IACrEC,YAAY,EAAE,4CAA4C;IAC1DC,YAAY,EAAE,4CAA4C;IAC1DC,aAAa,EAAE,+CAA+C;IAC9DC,cAAc,EAAE,iCAAiC;IACjDC,gBAAgB,EAAE,6CAA6C;IAC/DC,iBAAiB,EAAE,+CAA+C;IAClEC,gBAAgB,EAAE,8DAA8D;IAChFC,mBAAmB,EAAE,2DAA2D;IAChFC,UAAU,EAAE,0CAA0C;IACtDC,UAAU,EAAE,kCAAkC;IAC9CC,eAAe,EAAE;GAClB;EAED;EACAC,gBAAgB,EAAE;IAChBC,UAAU,EAAE,mBAAmB;IAC/BC,iBAAiB,EAAE,2BAA2B;IAC9CC,WAAW,EAAE,4BAA4B;IACzCC,gBAAgB,EAAE,gCAAgC;IAClDC,gBAAgB,EAAE,gCAAgC;IAClDC,eAAe,EAAE,8BAA8B;IAC/CC,gBAAgB,EAAE,qBAAqB;IACvCC,kBAAkB,EAAE,+BAA+B;IACnDC,eAAe,EAAE,oBAAoB;IACrCC,kBAAkB,EAAE,4BAA4B;IAChDC,cAAc,EAAE,mBAAmB;IACnCC,iBAAiB,EAAE;GACpB;EAED;EACAC,UAAU,EAAE;IACVC,SAAS,EAAE,WAAW;IACtBC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,UAAU;IACpBC,OAAO,EAAE,SAAS;IAClBC,QAAQ,EAAE,UAAU;IACpBC,UAAU,EAAE,YAAY;IACxBC,OAAO,EAAE,yBAAyB;IAClCC,OAAO,EAAE,SAAS;IAClBC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE,aAAa;IAC1BC,UAAU,EAAE,YAAY;IACxBC,WAAW,EAAE,aAAa;IAC1BC,aAAa,EAAE,eAAe;IAC9BC,UAAU,EAAE,uBAAuB;IACnCC,SAAS,EAAE,gBAAgB;IAC3BC,QAAQ,EAAE,mBAAmB;IAC7BC,WAAW,EAAE;GACd;EAED;EACAC,OAAO,EAAE;IACPC,IAAI,EAAE,cAAc;IACpBC,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,QAAQ;IAChBC,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,SAAS;IACdC,MAAM,EAAE,QAAQ;IAChBC,KAAK,EAAE,YAAY;IACnBC,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,gBAAgB;IACxBC,MAAM,EAAE,aAAa;IACrBC,KAAK,EAAE,OAAO;IACdC,OAAO,EAAE,SAAS;IAClBC,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,UAAU;IACpBC,OAAO,EAAE,SAAS;IAClBC,YAAY,EAAE,cAAc;IAC5BC,WAAW,EAAE,aAAa;IAC1BlF,eAAe,EAAE,mBAAmB;IACpCC,YAAY,EAAE,cAAc;IAC5BkF,WAAW,EAAE,iBAAiB;IAC9BC,WAAW,EAAE,iBAAiB;IAC9BC,SAAS,EAAE,kBAAkB;IAC7BC,WAAW,EAAE,iBAAiB;IAC9BC,cAAc,EAAE,gBAAgB;IAChCC,YAAY,EAAE;GACf;EAED;EACAC,MAAM,EAAE;IACNC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,UAAU;IACpBC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,QAAQ;IAChBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,WAAW;IACtBC,KAAK,EAAE,OAAO;IACdC,OAAO,EAAE,SAAS;IAClBC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,UAAU;IACpBC,SAAS,EAAE,WAAW;IACtBC,WAAW,EAAE;GACd;EAED;EACAC,YAAY,EAAE;IACZC,YAAY,EAAE,iCAAiC;IAC/CC,eAAe,EAAE,2CAA2C;IAC5DC,eAAe,EAAE,0CAA0C;IAC3DC,UAAU,EAAE,iBAAiB;IAC7BC,WAAW,EAAE,qBAAqB;IAClCC,WAAW,EAAE,mBAAmB;IAChCC,aAAa,EAAE,wBAAwB;IACvCC,iBAAiB,EAAE,mBAAmB;IACtCC,cAAc,EAAE,gBAAgB;IAChCC,YAAY,EAAE,cAAc;IAC5BC,cAAc,EAAE,gBAAgB;IAChCC,kBAAkB,EAAE,oBAAoB;IACxCC,kBAAkB,EAAE,oBAAoB;IACxCC,oBAAoB,EAAE,mCAAmC;IACzDC,eAAe,EAAE;;CAEpB;AAED;AACA,OAAO,MAAMC,kBAAkB,GAAG;EAChCC,SAAS,EAAE;IACTC,iBAAiB,EAAE,0CAA0C;IAC7DC,mBAAmB,EAAE,8BAA8B;IACnDC,cAAc,EAAE;GACjB;EACDC,OAAO,EAAE;IACPH,iBAAiB,EAAE,oCAAoC;IACvDC,mBAAmB,EAAE,wBAAwB;IAC7CC,cAAc,EAAE;GACjB;EACDE,OAAO,EAAE;IACPJ,iBAAiB,EAAE,gCAAgC;IACnDC,mBAAmB,EAAE,uBAAuB;IAC5CC,cAAc,EAAE;;CAEnB;AAED;AACA,OAAO,MAAMG,qBAAqB,GAAG;EACnCC,WAAW,EAAE,0EAA0E;EACvFC,cAAc,EAAE,mFAAmF;EACnGC,cAAc,EAAE,wFAAwF;EACxGC,YAAY,EAAE,qFAAqF;EACnGC,cAAc,EAAE,qFAAqF;EACrGC,aAAa,EAAE,8EAA8E;EAC7FpI,eAAe,EAAE,8DAA8D;EAC/EuF,cAAc,EAAE,oFAAoF;EACpGhC,MAAM,EAAE;CACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}