.programs-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.page-header h1 {
  margin: 0;
  color: #333;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.header-actions button {
  display: flex;
  align-items: center;
  gap: 8px;
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  text-align: center;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.error-container mat-icon {
  font-size: 48px;
  height: 48px;
  width: 48px;
  margin-bottom: 16px;
}

.table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.programs-table {
  width: 100%;
}

.program-code {
  font-weight: 600;
  color: #1976d2;
  background-color: #e3f2fd;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.9rem;
}

.program-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.program-name {
  font-weight: 500;
  color: #333;
}

.program-description {
  font-size: 0.85rem;
  color: #666;
  font-style: italic;
}

.action-buttons {
  display: flex;
  gap: 4px;
}

.action-buttons button {
  min-width: 40px;
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  color: #666;
}

.no-data mat-icon {
  font-size: 64px;
  height: 64px;
  width: 64px;
  margin-bottom: 16px;
  color: #ccc;
}

.no-data h3 {
  margin: 0 0 8px 0;
  color: #333;
}

.no-data p {
  margin: 0 0 24px 0;
  color: #666;
}

.no-data button {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Material Table Styling */
.mat-table {
  background: transparent;
}

.mat-header-cell {
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #e0e0e0;
}

.mat-cell {
  border-bottom: 1px solid #f0f0f0;
}

.mat-row:hover {
  background-color: #f9f9f9;
}

/* Chip Styling */
.mat-chip-set {
  margin: 0;
}

.mat-chip {
  font-size: 0.8rem;
  min-height: 24px;
  line-height: 24px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .programs-container {
    padding: 10px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .programs-table {
    font-size: 0.9rem;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 2px;
  }
}
th{
  color: green !important;
}
