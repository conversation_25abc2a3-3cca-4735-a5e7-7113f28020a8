{"ast": null, "code": "import Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/user.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/checkbox\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/forms\";\nfunction TeachertableComponent_tr_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 19)(2, \"mat-checkbox\", 20);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\", 21);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\", 22);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 23);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\", 24)(11, \"div\", 25)(12, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function TeachertableComponent_tr_33_Template_button_click_12_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r3);\n      const teacher_r1 = restoredCtx.$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.deleteTeacher(teacher_r1));\n    });\n    i0.ɵɵelementStart(13, \"mat-icon\");\n    i0.ɵɵtext(14, \"delete\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function TeachertableComponent_tr_33_Template_button_click_15_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r3);\n      const teacher_r1 = restoredCtx.$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.editTeacher(teacher_r1));\n    });\n    i0.ɵɵelementStart(16, \"mat-icon\");\n    i0.ɵɵtext(17, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function TeachertableComponent_tr_33_Template_button_click_18_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r3);\n      const teacher_r1 = restoredCtx.$implicit;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.viewTeacher(teacher_r1));\n    });\n    i0.ɵɵelementStart(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"remove_red_eye\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const teacher_r1 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", teacher_r1.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", teacher_r1.email, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (teacher_r1.department == null ? null : teacher_r1.department.name) || \"N/A\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", teacher_r1.designation || \"N/A\", \" \");\n  }\n}\nexport class TeachertableComponent {\n  constructor(teacherService, router, snackBar) {\n    this.teacherService = teacherService;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.isChecked = false;\n    this.isIndeterminate = true;\n    this.searchQuery = '';\n    this.teachers = []; // Define the teacher type if available\n  }\n\n  ngOnInit() {\n    this.fetchTeachers();\n  }\n  fetchTeachers() {\n    this.teacherService.getUsersByRole('Teacher').subscribe({\n      next: response => {\n        // Assuming response contains an array of teachers\n        this.teachers = response.users; // Adjust according to your API response\n      },\n\n      error: error => {\n        console.error('Error fetching teachers:', error);\n      }\n    });\n  }\n  toggleCheckbox() {\n    if (this.isIndeterminate) {\n      this.isIndeterminate = false;\n      this.isChecked = true;\n    } else if (this.isChecked) {\n      this.isChecked = false;\n    } else {\n      this.isIndeterminate = true;\n    }\n  }\n  get filteredTeachers() {\n    const searchLower = this.searchQuery.toLowerCase();\n    return this.teachers.filter(teacher => {\n      return teacher.name.toLowerCase().includes(searchLower) || teacher.designation && teacher.designation.toLowerCase().includes(searchLower) || teacher.department?.name && teacher.department.name.toLowerCase().includes(searchLower) || teacher.email && teacher.email.toLowerCase().includes(searchLower);\n    });\n  }\n  // View teacher details\n  viewTeacher(teacher) {\n    this.router.navigate(['/dashboard/admin/teacher/view', teacher._id]);\n  }\n  // Edit teacher\n  editTeacher(teacher) {\n    this.router.navigate(['/dashboard/admin/teacher/edit', teacher._id]);\n  }\n  // Delete teacher\n  deleteTeacher(teacher) {\n    Swal.fire({\n      title: 'Are you sure?',\n      text: `This will permanently delete ${teacher.name}`,\n      icon: 'warning',\n      showCancelButton: true,\n      confirmButtonColor: '#d33',\n      cancelButtonColor: '#3085d6',\n      confirmButtonText: 'Yes, delete it!',\n      cancelButtonText: 'Cancel'\n    }).then(result => {\n      if (result.isConfirmed) {\n        this.teacherService.deleteUser(teacher._id).subscribe({\n          next: response => {\n            if (response.success) {\n              Swal.fire({\n                title: 'Deleted!',\n                text: 'Teacher has been deleted successfully.',\n                icon: 'success',\n                confirmButtonColor: '#3085d6',\n                timer: 2000\n              });\n              this.fetchTeachers(); // Refresh the list\n            } else {\n              Swal.fire({\n                title: 'Error',\n                text: 'Failed to delete teacher.',\n                icon: 'error',\n                confirmButtonColor: '#3085d6'\n              });\n            }\n          },\n          error: error => {\n            console.error('Error deleting teacher:', error);\n            Swal.fire({\n              title: 'Error',\n              text: 'Failed to delete teacher. Please try again.',\n              icon: 'error',\n              confirmButtonColor: '#3085d6'\n            });\n          }\n        });\n      }\n    });\n  }\n  // Toggle teacher status\n  toggleTeacherStatus(teacher) {\n    const updatedStatus = !teacher.isActive;\n    const updateData = {\n      isActive: updatedStatus\n    };\n    this.teacherService.updateUser(teacher._id, updateData).subscribe({\n      next: response => {\n        if (response.success) {\n          teacher.isActive = updatedStatus;\n          this.snackBar.open(`Teacher ${updatedStatus ? 'activated' : 'deactivated'} successfully`, 'Close', {\n            duration: 3000\n          });\n        } else {\n          this.snackBar.open('Failed to update teacher status', 'Close', {\n            duration: 3000\n          });\n        }\n      },\n      error: error => {\n        console.error('Error updating teacher status:', error);\n        this.snackBar.open('Error updating teacher status', 'Close', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  static #_ = this.ɵfac = function TeachertableComponent_Factory(t) {\n    return new (t || TeachertableComponent)(i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: TeachertableComponent,\n    selectors: [[\"app-teachertable\"]],\n    decls: 43,\n    vars: 4,\n    consts: [[1, \"maindiv\"], [1, \"secondarydiv\"], [1, \"my-3\", \"d-flex\", \"justify-content-between\", \"searchAndtab\"], [1, \"d-flex\", \"flex-wrap\", \"gap-3\"], [\"routerLink\", \"/dashboard/admin/teacher/add-teacher\", 1, \"btn\", \"btn-top\", \"border\", \"d-flex\", \"align-items-center\"], [1, \"search-container\"], [1, \"search-input-wrapper\"], [\"type\", \"text\", \"placeholder\", \"Search\", 1, \"search-input\", 3, \"ngModel\", \"ngModelChange\"], [1, \"search-icon\"], [1, \"table-container\"], [1, \"table-responsive\"], [1, \"tablehead\"], [1, \"one\"], [\"color\", \"primary\", 3, \"ngModel\", \"indeterminate\", \"ngModelChange\", \"change\"], [1, \"two\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"p-3\", \"align-items-center\", \"justify-content-between\"], [1, \"btn\", \"btn-top\", \"border\"], [1, \"page\"], [\"data-label\", \"Teacher Name\", 1, \"name\"], [\"color\", \"primary\"], [\"data-label\", \"Email\", 1, \"para\"], [\"data-label\", \"Department\", 1, \"para\"], [\"data-label\", \"Designation\", 1, \"para\"], [\"data-label\", \"Action\", 1, \"para\"], [1, \"d-flex\", \"gap-2\"], [\"title\", \"Delete Teacher\", 1, \"btn\", \"btndelete\", \"d-flex\", \"justify-content-center\", \"align-items-center\", 3, \"click\"], [\"title\", \"Edit Teacher\", 1, \"btn\", \"btnedit\", \"d-flex\", \"justify-content-center\", \"align-items-center\", 3, \"click\"], [\"title\", \"View Teacher\", 1, \"btn\", \"btnedit\", \"d-flex\", \"justify-content-center\", \"align-items-center\", 3, \"click\"]],\n    template: function TeachertableComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"button\", 4)(5, \"mat-icon\");\n        i0.ɵɵtext(6, \"add\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(7, \"\\u00A0 Add New Teacher \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(8, \"div\")(9, \"div\", 5)(10, \"div\", 6)(11, \"input\", 7);\n        i0.ɵɵlistener(\"ngModelChange\", function TeachertableComponent_Template_input_ngModelChange_11_listener($event) {\n          return ctx.searchQuery = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"mat-icon\", 8);\n        i0.ɵɵtext(13, \"search\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(14, \"div\", 9)(15, \"div\", 10)(16, \"table\")(17, \"thead\")(18, \"tr\", 11)(19, \"th\", 12)(20, \"mat-checkbox\", 13);\n        i0.ɵɵlistener(\"ngModelChange\", function TeachertableComponent_Template_mat_checkbox_ngModelChange_20_listener($event) {\n          return ctx.isChecked = $event;\n        })(\"change\", function TeachertableComponent_Template_mat_checkbox_change_20_listener() {\n          return ctx.toggleCheckbox();\n        });\n        i0.ɵɵtext(21, \" Teacher Name \");\n        i0.ɵɵelementStart(22, \"mat-icon\");\n        i0.ɵɵtext(23, \"arrow_downward\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(24, \"th\");\n        i0.ɵɵtext(25, \"Email\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(26, \"th\");\n        i0.ɵɵtext(27, \"Department\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(28, \"th\");\n        i0.ɵɵtext(29, \"Designation\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(30, \"th\", 14);\n        i0.ɵɵtext(31, \"Actions\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(32, \"tbody\");\n        i0.ɵɵtemplate(33, TeachertableComponent_tr_33_Template, 21, 4, \"tr\", 15);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(34, \"div\", 16)(35, \"div\")(36, \"button\", 17);\n        i0.ɵɵtext(37, \"Previous\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(38, \"div\", 18);\n        i0.ɵɵtext(39, \"Page 1 of 10\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(40, \"div\")(41, \"button\", 17);\n        i0.ɵɵtext(42, \"Next\");\n        i0.ɵɵelementEnd()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(11);\n        i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"ngModel\", ctx.isChecked)(\"indeterminate\", ctx.isIndeterminate);\n        i0.ɵɵadvance(13);\n        i0.ɵɵproperty(\"ngForOf\", ctx.filteredTeachers);\n      }\n    },\n    dependencies: [i4.NgForOf, i2.RouterLink, i5.MatCheckbox, i6.MatIcon, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgModel],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "TeachertableComponent_tr_33_Template_button_click_12_listener", "restoredCtx", "ɵɵrestoreView", "_r3", "teacher_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "deleteTeacher", "TeachertableComponent_tr_33_Template_button_click_15_listener", "ctx_r4", "edit<PERSON><PERSON><PERSON>", "TeachertableComponent_tr_33_Template_button_click_18_listener", "ctx_r5", "<PERSON><PERSON><PERSON>er", "ɵɵadvance", "ɵɵtextInterpolate1", "name", "email", "department", "designation", "TeachertableComponent", "constructor", "teacherService", "router", "snackBar", "isChecked", "isIndeterminate", "searchQuery", "teachers", "ngOnInit", "fetchTeachers", "getUsersByRole", "subscribe", "next", "response", "users", "error", "console", "toggleCheckbox", "filteredTeachers", "searchLower", "toLowerCase", "filter", "teacher", "includes", "navigate", "_id", "fire", "title", "text", "icon", "showCancelButton", "confirmButtonColor", "cancelButtonColor", "confirmButtonText", "cancelButtonText", "then", "result", "isConfirmed", "deleteUser", "success", "timer", "toggleTeacherStatus", "updatedStatus", "isActive", "updateData", "updateUser", "open", "duration", "_", "ɵɵdirectiveInject", "i1", "UserService", "i2", "Router", "i3", "MatSnackBar", "_2", "selectors", "decls", "vars", "consts", "template", "TeachertableComponent_Template", "rf", "ctx", "TeachertableComponent_Template_input_ngModelChange_11_listener", "$event", "TeachertableComponent_Template_mat_checkbox_ngModelChange_20_listener", "TeachertableComponent_Template_mat_checkbox_change_20_listener", "ɵɵtemplate", "TeachertableComponent_tr_33_Template", "ɵɵproperty"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\teacher\\teachertable\\teachertable.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\teacher\\teachertable\\teachertable.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { UserService } from 'src/app/services/user.service';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-teachertable',\r\n  templateUrl: './teachertable.component.html',\r\n  styleUrls: ['./teachertable.component.css']\r\n})\r\nexport class TeachertableComponent implements OnInit {\r\n  isChecked: boolean = false;\r\n  isIndeterminate: boolean = true;\r\n  searchQuery: string = '';\r\n  teachers: any[] = []; // Define the teacher type if available\r\n\r\n  constructor(\r\n    private teacherService: UserService,\r\n    private router: Router,\r\n    private snackBar: MatSnackBar\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.fetchTeachers();\r\n  }\r\n\r\n  fetchTeachers() {\r\n    this.teacherService.getUsersByRole('Teacher').subscribe({\r\n      next: (response: any) => {\r\n        // Assuming response contains an array of teachers\r\n        this.teachers = response.users\r\n        ; // Adjust according to your API response\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Error fetching teachers:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  toggleCheckbox() {\r\n    if (this.isIndeterminate) {\r\n      this.isIndeterminate = false;\r\n      this.isChecked = true;\r\n    } else if (this.isChecked) {\r\n      this.isChecked = false;\r\n    } else {\r\n      this.isIndeterminate = true;\r\n    }\r\n  }\r\n\r\n  get filteredTeachers() {\r\n    const searchLower = this.searchQuery.toLowerCase();\r\n    return this.teachers.filter(teacher => {\r\n      return teacher.name.toLowerCase().includes(searchLower) ||\r\n             (teacher.designation && teacher.designation.toLowerCase().includes(searchLower)) ||\r\n             (teacher.department?.name && teacher.department.name.toLowerCase().includes(searchLower)) ||\r\n             (teacher.email && teacher.email.toLowerCase().includes(searchLower));\r\n    });\r\n  }\r\n\r\n  // View teacher details\r\n  viewTeacher(teacher: any): void {\r\n    this.router.navigate(['/dashboard/admin/teacher/view', teacher._id]);\r\n  }\r\n\r\n  // Edit teacher\r\n  editTeacher(teacher: any): void {\r\n    this.router.navigate(['/dashboard/admin/teacher/edit', teacher._id]);\r\n  }\r\n\r\n  // Delete teacher\r\n  deleteTeacher(teacher: any): void {\r\n    Swal.fire({\r\n      title: 'Are you sure?',\r\n      text: `This will permanently delete ${teacher.name}`,\r\n      icon: 'warning',\r\n      showCancelButton: true,\r\n      confirmButtonColor: '#d33',\r\n      cancelButtonColor: '#3085d6',\r\n      confirmButtonText: 'Yes, delete it!',\r\n      cancelButtonText: 'Cancel'\r\n    }).then((result) => {\r\n      if (result.isConfirmed) {\r\n        this.teacherService.deleteUser(teacher._id).subscribe({\r\n          next: (response) => {\r\n            if (response.success) {\r\n              Swal.fire({\r\n                title: 'Deleted!',\r\n                text: 'Teacher has been deleted successfully.',\r\n                icon: 'success',\r\n                confirmButtonColor: '#3085d6',\r\n                timer: 2000\r\n              });\r\n              this.fetchTeachers(); // Refresh the list\r\n            } else {\r\n              Swal.fire({\r\n                title: 'Error',\r\n                text: 'Failed to delete teacher.',\r\n                icon: 'error',\r\n                confirmButtonColor: '#3085d6'\r\n              });\r\n            }\r\n          },\r\n          error: (error) => {\r\n            console.error('Error deleting teacher:', error);\r\n            Swal.fire({\r\n              title: 'Error',\r\n              text: 'Failed to delete teacher. Please try again.',\r\n              icon: 'error',\r\n              confirmButtonColor: '#3085d6'\r\n            });\r\n          }\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  // Toggle teacher status\r\n  toggleTeacherStatus(teacher: any): void {\r\n    const updatedStatus = !teacher.isActive;\r\n    const updateData = { isActive: updatedStatus };\r\n\r\n    this.teacherService.updateUser(teacher._id, updateData).subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          teacher.isActive = updatedStatus;\r\n          this.snackBar.open(\r\n            `Teacher ${updatedStatus ? 'activated' : 'deactivated'} successfully`,\r\n            'Close',\r\n            { duration: 3000 }\r\n          );\r\n        } else {\r\n          this.snackBar.open('Failed to update teacher status', 'Close', { duration: 3000 });\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error updating teacher status:', error);\r\n        this.snackBar.open('Error updating teacher status', 'Close', { duration: 3000 });\r\n      }\r\n    });\r\n  }\r\n}\r\n", "<div class=\"maindiv\">\r\n    <div class=\"secondarydiv\">\r\n      <div class=\"my-3 d-flex justify-content-between searchAndtab\">\r\n        <div class=\"d-flex flex-wrap gap-3\">\r\n          <button class=\"btn btn-top border d-flex align-items-center\" routerLink=\"/dashboard/admin/teacher/add-teacher\">\r\n            <mat-icon>add</mat-icon>&nbsp; Add New Teacher\r\n          </button>\r\n        </div>\r\n        <div>\r\n          <div class=\"search-container\">\r\n            <div class=\"search-input-wrapper\">\r\n              <input type=\"text\" class=\"search-input\" placeholder=\"Search\" [(ngModel)]=\"searchQuery\">\r\n              <mat-icon class=\"search-icon\">search</mat-icon>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"table-container\">\r\n        <div class=\"table-responsive\">\r\n          <table>\r\n            <thead>\r\n              <tr class=\"tablehead\">\r\n                <th class=\"one\">\r\n                  <mat-checkbox color=\"primary\" [(ngModel)]=\"isChecked\" [indeterminate]=\"isIndeterminate\" (change)=\"toggleCheckbox()\">\r\n                    Teacher Name <mat-icon>arrow_downward</mat-icon>\r\n                  </mat-checkbox>\r\n                </th>\r\n                <th>Email</th>\r\n                <th>Department</th>\r\n                <th>Designation</th>\r\n                <th class=\"two\">Actions</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              <tr *ngFor=\"let teacher of filteredTeachers\">\r\n                <td class=\"name\" data-label=\"Teacher Name\">\r\n                  <mat-checkbox color=\"primary\">\r\n                    {{ teacher.name }}\r\n                  </mat-checkbox>\r\n                </td>\r\n                <td class=\"para\" data-label=\"Email\">\r\n                  {{ teacher.email }}\r\n                </td>\r\n                <td class=\"para\" data-label=\"Department\">\r\n                  {{ teacher.department?.name || 'N/A' }}\r\n                </td>\r\n                <td class=\"para\" data-label=\"Designation\">\r\n                  {{ teacher.designation || 'N/A' }}\r\n                </td>\r\n                <td class=\"para\" data-label=\"Action\">\r\n                  <div class=\"d-flex gap-2\">\r\n                    <button class=\"btn btndelete d-flex justify-content-center align-items-center\"\r\n                            (click)=\"deleteTeacher(teacher)\"\r\n                            title=\"Delete Teacher\">\r\n                      <mat-icon>delete</mat-icon>\r\n                    </button>\r\n                    <button class=\"btn btnedit d-flex justify-content-center align-items-center\"\r\n                            (click)=\"editTeacher(teacher)\"\r\n                            title=\"Edit Teacher\">\r\n                      <mat-icon>edit</mat-icon>\r\n                    </button>\r\n                    <button class=\"btn btnedit d-flex justify-content-center align-items-center\"\r\n                            (click)=\"viewTeacher(teacher)\"\r\n                            title=\"View Teacher\">\r\n                      <mat-icon>remove_red_eye</mat-icon>\r\n                    </button>\r\n                  </div>\r\n                </td>\r\n              </tr>\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n        <div class=\"d-flex p-3 align-items-center justify-content-between\">\r\n          <div>\r\n            <button class=\"btn btn-top border\">Previous</button>\r\n          </div>\r\n          <div class=\"page\">Page 1 of 10</div>\r\n          <div>\r\n            <button class=\"btn btn-top border\">Next</button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  "], "mappings": "AAIA,OAAOA,IAAI,MAAM,aAAa;;;;;;;;;;;;IC8BhBC,EAAA,CAAAC,cAAA,SAA6C;IAGvCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAe;IAEjBH,EAAA,CAAAC,cAAA,aAAoC;IAClCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAAyC;IACvCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAA0C;IACxCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAqC;IAGzBD,EAAA,CAAAI,UAAA,mBAAAC,8DAAA;MAAA,MAAAC,WAAA,GAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,UAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAASZ,EAAA,CAAAa,WAAA,CAAAF,MAAA,CAAAG,aAAA,CAAAL,UAAA,CAAsB;IAAA,EAAC;IAEtCT,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE7BH,EAAA,CAAAC,cAAA,kBAE6B;IADrBD,EAAA,CAAAI,UAAA,mBAAAW,8DAAA;MAAA,MAAAT,WAAA,GAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,UAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAM,MAAA,GAAAhB,EAAA,CAAAY,aAAA;MAAA,OAASZ,EAAA,CAAAa,WAAA,CAAAG,MAAA,CAAAC,WAAA,CAAAR,UAAA,CAAoB;IAAA,EAAC;IAEpCT,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE3BH,EAAA,CAAAC,cAAA,kBAE6B;IADrBD,EAAA,CAAAI,UAAA,mBAAAc,8DAAA;MAAA,MAAAZ,WAAA,GAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,UAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAS,MAAA,GAAAnB,EAAA,CAAAY,aAAA;MAAA,OAASZ,EAAA,CAAAa,WAAA,CAAAM,MAAA,CAAAC,WAAA,CAAAX,UAAA,CAAoB;IAAA,EAAC;IAEpCT,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IA3BrCH,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAAsB,kBAAA,MAAAb,UAAA,CAAAc,IAAA,MACF;IAGAvB,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAAsB,kBAAA,MAAAb,UAAA,CAAAe,KAAA,MACF;IAEExB,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAAsB,kBAAA,OAAAb,UAAA,CAAAgB,UAAA,kBAAAhB,UAAA,CAAAgB,UAAA,CAAAF,IAAA,gBACF;IAEEvB,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAAsB,kBAAA,MAAAb,UAAA,CAAAiB,WAAA,eACF;;;ADrChB,OAAM,MAAOC,qBAAqB;EAMhCC,YACUC,cAA2B,EAC3BC,MAAc,EACdC,QAAqB;IAFrB,KAAAF,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IARlB,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,eAAe,GAAY,IAAI;IAC/B,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,QAAQ,GAAU,EAAE,CAAC,CAAC;EAMnB;;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,EAAE;EACtB;EAEAA,aAAaA,CAAA;IACX,IAAI,CAACR,cAAc,CAACS,cAAc,CAAC,SAAS,CAAC,CAACC,SAAS,CAAC;MACtDC,IAAI,EAAGC,QAAa,IAAI;QACtB;QACA,IAAI,CAACN,QAAQ,GAAGM,QAAQ,CAACC,KAAK,CAC7B,CAAC;MACJ,CAAC;;MACDC,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;KACD,CAAC;EACJ;EAEAE,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACZ,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACD,SAAS,GAAG,IAAI;KACtB,MAAM,IAAI,IAAI,CAACA,SAAS,EAAE;MACzB,IAAI,CAACA,SAAS,GAAG,KAAK;KACvB,MAAM;MACL,IAAI,CAACC,eAAe,GAAG,IAAI;;EAE/B;EAEA,IAAIa,gBAAgBA,CAAA;IAClB,MAAMC,WAAW,GAAG,IAAI,CAACb,WAAW,CAACc,WAAW,EAAE;IAClD,OAAO,IAAI,CAACb,QAAQ,CAACc,MAAM,CAACC,OAAO,IAAG;MACpC,OAAOA,OAAO,CAAC3B,IAAI,CAACyB,WAAW,EAAE,CAACG,QAAQ,CAACJ,WAAW,CAAC,IAC/CG,OAAO,CAACxB,WAAW,IAAIwB,OAAO,CAACxB,WAAW,CAACsB,WAAW,EAAE,CAACG,QAAQ,CAACJ,WAAW,CAAE,IAC/EG,OAAO,CAACzB,UAAU,EAAEF,IAAI,IAAI2B,OAAO,CAACzB,UAAU,CAACF,IAAI,CAACyB,WAAW,EAAE,CAACG,QAAQ,CAACJ,WAAW,CAAE,IACxFG,OAAO,CAAC1B,KAAK,IAAI0B,OAAO,CAAC1B,KAAK,CAACwB,WAAW,EAAE,CAACG,QAAQ,CAACJ,WAAW,CAAE;IAC7E,CAAC,CAAC;EACJ;EAEA;EACA3B,WAAWA,CAAC8B,OAAY;IACtB,IAAI,CAACpB,MAAM,CAACsB,QAAQ,CAAC,CAAC,+BAA+B,EAAEF,OAAO,CAACG,GAAG,CAAC,CAAC;EACtE;EAEA;EACApC,WAAWA,CAACiC,OAAY;IACtB,IAAI,CAACpB,MAAM,CAACsB,QAAQ,CAAC,CAAC,+BAA+B,EAAEF,OAAO,CAACG,GAAG,CAAC,CAAC;EACtE;EAEA;EACAvC,aAAaA,CAACoC,OAAY;IACxBnD,IAAI,CAACuD,IAAI,CAAC;MACRC,KAAK,EAAE,eAAe;MACtBC,IAAI,EAAE,gCAAgCN,OAAO,CAAC3B,IAAI,EAAE;MACpDkC,IAAI,EAAE,SAAS;MACfC,gBAAgB,EAAE,IAAI;MACtBC,kBAAkB,EAAE,MAAM;MAC1BC,iBAAiB,EAAE,SAAS;MAC5BC,iBAAiB,EAAE,iBAAiB;MACpCC,gBAAgB,EAAE;KACnB,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAI;MACjB,IAAIA,MAAM,CAACC,WAAW,EAAE;QACtB,IAAI,CAACpC,cAAc,CAACqC,UAAU,CAAChB,OAAO,CAACG,GAAG,CAAC,CAACd,SAAS,CAAC;UACpDC,IAAI,EAAGC,QAAQ,IAAI;YACjB,IAAIA,QAAQ,CAAC0B,OAAO,EAAE;cACpBpE,IAAI,CAACuD,IAAI,CAAC;gBACRC,KAAK,EAAE,UAAU;gBACjBC,IAAI,EAAE,wCAAwC;gBAC9CC,IAAI,EAAE,SAAS;gBACfE,kBAAkB,EAAE,SAAS;gBAC7BS,KAAK,EAAE;eACR,CAAC;cACF,IAAI,CAAC/B,aAAa,EAAE,CAAC,CAAC;aACvB,MAAM;cACLtC,IAAI,CAACuD,IAAI,CAAC;gBACRC,KAAK,EAAE,OAAO;gBACdC,IAAI,EAAE,2BAA2B;gBACjCC,IAAI,EAAE,OAAO;gBACbE,kBAAkB,EAAE;eACrB,CAAC;;UAEN,CAAC;UACDhB,KAAK,EAAGA,KAAK,IAAI;YACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;YAC/C5C,IAAI,CAACuD,IAAI,CAAC;cACRC,KAAK,EAAE,OAAO;cACdC,IAAI,EAAE,6CAA6C;cACnDC,IAAI,EAAE,OAAO;cACbE,kBAAkB,EAAE;aACrB,CAAC;UACJ;SACD,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEA;EACAU,mBAAmBA,CAACnB,OAAY;IAC9B,MAAMoB,aAAa,GAAG,CAACpB,OAAO,CAACqB,QAAQ;IACvC,MAAMC,UAAU,GAAG;MAAED,QAAQ,EAAED;IAAa,CAAE;IAE9C,IAAI,CAACzC,cAAc,CAAC4C,UAAU,CAACvB,OAAO,CAACG,GAAG,EAAEmB,UAAU,CAAC,CAACjC,SAAS,CAAC;MAChEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAAC0B,OAAO,EAAE;UACpBjB,OAAO,CAACqB,QAAQ,GAAGD,aAAa;UAChC,IAAI,CAACvC,QAAQ,CAAC2C,IAAI,CAChB,WAAWJ,aAAa,GAAG,WAAW,GAAG,aAAa,eAAe,EACrE,OAAO,EACP;YAAEK,QAAQ,EAAE;UAAI,CAAE,CACnB;SACF,MAAM;UACL,IAAI,CAAC5C,QAAQ,CAAC2C,IAAI,CAAC,iCAAiC,EAAE,OAAO,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;;MAEtF,CAAC;MACDhC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAACZ,QAAQ,CAAC2C,IAAI,CAAC,+BAA+B,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MAClF;KACD,CAAC;EACJ;EAAC,QAAAC,CAAA,G;qBAlIUjD,qBAAqB,EAAA3B,EAAA,CAAA6E,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA/E,EAAA,CAAA6E,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAjF,EAAA,CAAA6E,iBAAA,CAAAK,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAArBzD,qBAAqB;IAAA0D,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCXlC3F,EAAA,CAAAC,cAAA,aAAqB;QAKCD,EAAA,CAAAE,MAAA,UAAG;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAAAH,EAAA,CAAAE,MAAA,8BAC1B;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAEXH,EAAA,CAAAC,cAAA,UAAK;QAG8DD,EAAA,CAAAI,UAAA,2BAAAyF,+DAAAC,MAAA;UAAA,OAAAF,GAAA,CAAA1D,WAAA,GAAA4D,MAAA;QAAA,EAAyB;QAAtF9F,EAAA,CAAAG,YAAA,EAAuF;QACvFH,EAAA,CAAAC,cAAA,mBAA8B;QAAAD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAKvDH,EAAA,CAAAC,cAAA,cAA6B;QAMaD,EAAA,CAAAI,UAAA,2BAAA2F,sEAAAD,MAAA;UAAA,OAAAF,GAAA,CAAA5D,SAAA,GAAA8D,MAAA;QAAA,EAAuB,oBAAAE,+DAAA;UAAA,OAA6CJ,GAAA,CAAA/C,cAAA,EAAgB;QAAA,EAA7D;QACnD7C,EAAA,CAAAE,MAAA,sBAAa;QAAAF,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAGpDH,EAAA,CAAAC,cAAA,UAAI;QAAAD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACdH,EAAA,CAAAC,cAAA,UAAI;QAAAD,EAAA,CAAAE,MAAA,kBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACnBH,EAAA,CAAAC,cAAA,UAAI;QAAAD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACpBH,EAAA,CAAAC,cAAA,cAAgB;QAAAD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAGhCH,EAAA,CAAAC,cAAA,aAAO;QACLD,EAAA,CAAAiG,UAAA,KAAAC,oCAAA,kBAkCK;QACPlG,EAAA,CAAAG,YAAA,EAAQ;QAGZH,EAAA,CAAAC,cAAA,eAAmE;QAE5BD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAEtDH,EAAA,CAAAC,cAAA,eAAkB;QAAAD,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAM;QACpCH,EAAA,CAAAC,cAAA,WAAK;QACgCD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAG,YAAA,EAAS;;;QAnEeH,EAAA,CAAAqB,SAAA,IAAyB;QAAzBrB,EAAA,CAAAmG,UAAA,YAAAP,GAAA,CAAA1D,WAAA,CAAyB;QAYpDlC,EAAA,CAAAqB,SAAA,GAAuB;QAAvBrB,EAAA,CAAAmG,UAAA,YAAAP,GAAA,CAAA5D,SAAA,CAAuB,kBAAA4D,GAAA,CAAA3D,eAAA;QAWjCjC,EAAA,CAAAqB,SAAA,IAAmB;QAAnBrB,EAAA,CAAAmG,UAAA,YAAAP,GAAA,CAAA9C,gBAAA,CAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}