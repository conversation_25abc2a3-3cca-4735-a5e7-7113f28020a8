{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AttendanceComponent } from './attendance/attendance.component';\nimport { StudentTeachersComponent } from './student-teachers/student-teachers.component';\nimport { StudentNoticeComponent } from './student-notice/student-notice.component';\nimport { StudentComplaintComponent } from './student-complaint/student-complaint.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'attendance',\n  component: AttendanceComponent\n}, {\n  path: 'teachers',\n  component: StudentTeachersComponent\n}, {\n  path: 'notice',\n  component: StudentNoticeComponent\n}, {\n  path: 'complaint',\n  component: StudentComplaintComponent\n}];\nexport let StudentDashboardRoutingModule = /*#__PURE__*/(() => {\n  class StudentDashboardRoutingModule {\n    static #_ = this.ɵfac = function StudentDashboardRoutingModule_Factory(t) {\n      return new (t || StudentDashboardRoutingModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: StudentDashboardRoutingModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n  return StudentDashboardRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}