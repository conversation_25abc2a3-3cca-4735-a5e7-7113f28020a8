const Program = require('../models/program.js');

// Create a new program
const createProgram = async (req, res) => {
    try {
        const { name, fullName, description, duration, durationUnit, totalSemesters } = req.body;

        // Check if program already exists
        const existingProgram = await Program.findOne({ name });
        if (existingProgram) {
            return res.status(400).json({ 
                success: false, 
                message: 'Program with this name already exists' 
            });
        }

        const program = new Program({
            name,
            fullName,
            description,
            duration,
            durationUnit,
            totalSemesters
        });

        const savedProgram = await program.save();
        res.status(201).json({ 
            success: true, 
            message: 'Program created successfully', 
            program: savedProgram 
        });
    } catch (error) {
        if (process.env.NODE_ENV !== 'production') {
            console.error('Error creating program:', error);
        }
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: process.env.NODE_ENV !== 'production' ? error.message : 'An error occurred'
        });
    }
};

// Get all programs
const getAllPrograms = async (req, res) => {
    try {
        const { isActive } = req.query;
        const filter = {};
        
        if (isActive !== undefined) {
            filter.isActive = isActive === 'true';
        }

        const programs = await Program.find(filter).sort({ createdAt: -1 });
        res.status(200).json({ 
            success: true, 
            programs,
            count: programs.length 
        });
    } catch (error) {
        if (process.env.NODE_ENV !== 'production') {
            console.error('Error fetching programs:', error);
        }
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: process.env.NODE_ENV !== 'production' ? error.message : 'An error occurred'
        });
    }
};

// Get program by ID
const getProgramById = async (req, res) => {
    try {
        const { id } = req.params;
        const program = await Program.findById(id);

        if (!program) {
            return res.status(404).json({ 
                success: false, 
                message: 'Program not found' 
            });
        }

        res.status(200).json({ 
            success: true, 
            program 
        });
    } catch (error) {
        console.error('Error fetching program:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Internal server error', 
            error: error.message 
        });
    }
};

// Update program
const updateProgram = async (req, res) => {
    try {
        const { id } = req.params;
        const updateData = req.body;

        const program = await Program.findByIdAndUpdate(
            id, 
            updateData, 
            { new: true, runValidators: true }
        );

        if (!program) {
            return res.status(404).json({ 
                success: false, 
                message: 'Program not found' 
            });
        }

        res.status(200).json({ 
            success: true, 
            message: 'Program updated successfully', 
            program 
        });
    } catch (error) {
        console.error('Error updating program:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Internal server error', 
            error: error.message 
        });
    }
};

// Delete program (soft delete)
const deleteProgram = async (req, res) => {
    try {
        const { id } = req.params;

        const program = await Program.findByIdAndUpdate(
            id, 
            { isActive: false }, 
            { new: true }
        );

        if (!program) {
            return res.status(404).json({ 
                success: false, 
                message: 'Program not found' 
            });
        }

        res.status(200).json({ 
            success: true, 
            message: 'Program deleted successfully', 
            program 
        });
    } catch (error) {
        console.error('Error deleting program:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Internal server error', 
            error: error.message 
        });
    }
};

// Permanently delete program
const permanentDeleteProgram = async (req, res) => {
    try {
        const { id } = req.params;

        const program = await Program.findByIdAndDelete(id);

        if (!program) {
            return res.status(404).json({ 
                success: false, 
                message: 'Program not found' 
            });
        }

        res.status(200).json({ 
            success: true, 
            message: 'Program permanently deleted' 
        });
    } catch (error) {
        console.error('Error permanently deleting program:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Internal server error', 
            error: error.message 
        });
    }
};

module.exports = {
    createProgram,
    getAllPrograms,
    getProgramById,
    updateProgram,
    deleteProgram,
    permanentDeleteProgram
};
