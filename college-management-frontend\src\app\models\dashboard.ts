import { User, Program, Department, Class, Subject } from './user';
import { Attendance, AttendanceStats, SubjectAttendance } from './attendance';
import { Timetable } from './timetable';

// In your models/dashboard.ts file
export interface DashboardStats {
  totals: {
    students: number;
    teachers: number;
    programs: number;
    departments: number;
    classes: number;
    subjects: number;
  };
  programStats: Array<{
    programName: string;
    studentCount: number;
  }>;
  departmentStats: Array<{
    departmentName: string;
    studentCount: number;
  }>;
  attendanceStats: Array<{
    _id: string;
    count: number;
  }>;
  semesterStats: Array<{
    _id: string;
    count: number;
  }>;
  // Add these new properties
  departmentAttendance: Array<{
    departmentName: string;
    attendanceRate: number;
  }>;
  attendanceTrend: Array<{
    date: string;
    present: number;
    absent: number;
  }>;
}

export interface PrincipalDashboard {
  stats: DashboardStats;
}

export interface TeacherDashboard {
  timetable: Timetable[];
  classes: Class[];
  subjects: Subject[];
  students: User[];
  recentAttendance: Attendance[];
  stats: {
    totalClasses: number;
    totalSubjects: number;
    totalStudents: number;
  };
}

export interface StudentDashboard {
  student: User;
  timetable: Timetable[];
  attendanceHistory: Attendance[];
  attendanceStats: AttendanceStats[];
  subjectAttendance: SubjectAttendance[];
}

export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalUsers?: number;
  totalRecords?: number;
  hasNext: boolean;
  hasPrev: boolean;
}
