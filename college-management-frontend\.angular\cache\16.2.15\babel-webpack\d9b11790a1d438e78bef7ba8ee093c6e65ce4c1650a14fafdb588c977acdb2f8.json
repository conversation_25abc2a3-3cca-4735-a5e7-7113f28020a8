{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'department',\n  loadChildren: () => import('./department/department.module').then(m => m.DepartmentModule)\n}, {\n  path: 'classes',\n  loadChildren: () => import('./classes/classes.module').then(m => m.ClassesModule)\n}, {\n  path: 'teachers',\n  loadChildren: () => import('./teacher/teacher.module').then(m => m.TeacherModule)\n}, {\n  path: 'students',\n  loadChildren: () => import('./students/students.module').then(m => m.StudentsModule)\n}, {\n  path: 'subjects',\n  loadChildren: () => import('./subject/subject.module').then(m => m.SubjectModule)\n}, {\n  path: 'users',\n  loadChildren: () => import('./users/users.module').then(m => m.UsersModule)\n}];\nexport class AdminDashboardRoutingModule {\n  static #_ = this.ɵfac = function AdminDashboardRoutingModule_Factory(t) {\n    return new (t || AdminDashboardRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AdminDashboardRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild(routes), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AdminDashboardRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "routes", "path", "loadChildren", "then", "m", "DepartmentModule", "ClassesModule", "TeacherModule", "StudentsModule", "SubjectModule", "UsersModule", "AdminDashboardRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\admin-dashboard-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\n\r\nconst routes: Routes = [\r\n  {path:'department', loadChildren:() =>import('./department/department.module').then(m=>m.DepartmentModule) },\r\n  {path:'classes', loadChildren:() =>import('./classes/classes.module').then(m=>m.ClassesModule) },\r\n  {path:'teachers', loadChildren:() =>import('./teacher/teacher.module').then(m=>m.TeacherModule) },\r\n  {path:'students', loadChildren:() =>import('./students/students.module').then(m=>m.StudentsModule) },\r\n  {path:'subjects', loadChildren:() =>import('./subject/subject.module').then(m=>m.SubjectModule) },\r\n  {path:'users', loadChildren:() =>import('./users/users.module').then(m=>m.UsersModule) },\r\n\r\n\r\n\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class AdminDashboardRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;;;AAEtD,MAAMC,MAAM,GAAW,CACrB;EAACC,IAAI,EAAC,YAAY;EAAEC,YAAY,EAACA,CAAA,KAAK,MAAM,CAAC,gCAAgC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAEA,CAAC,CAACC,gBAAgB;AAAC,CAAE,EAC5G;EAACJ,IAAI,EAAC,SAAS;EAAEC,YAAY,EAACA,CAAA,KAAK,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAEA,CAAC,CAACE,aAAa;AAAC,CAAE,EAChG;EAACL,IAAI,EAAC,UAAU;EAAEC,YAAY,EAACA,CAAA,KAAK,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAEA,CAAC,CAACG,aAAa;AAAC,CAAE,EACjG;EAACN,IAAI,EAAC,UAAU;EAAEC,YAAY,EAACA,CAAA,KAAK,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAEA,CAAC,CAACI,cAAc;AAAC,CAAE,EACpG;EAACP,IAAI,EAAC,UAAU;EAAEC,YAAY,EAACA,CAAA,KAAK,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAEA,CAAC,CAACK,aAAa;AAAC,CAAE,EACjG;EAACR,IAAI,EAAC,OAAO;EAAEC,YAAY,EAACA,CAAA,KAAK,MAAM,CAAC,sBAAsB,CAAC,CAACC,IAAI,CAACC,CAAC,IAAEA,CAAC,CAACM,WAAW;AAAC,CAAE,CAIzF;AAMD,OAAM,MAAOC,2BAA2B;EAAA,QAAAC,CAAA,G;qBAA3BD,2BAA2B;EAAA;EAAA,QAAAE,EAAA,G;UAA3BF;EAA2B;EAAA,QAAAG,EAAA,G;cAH5Bf,YAAY,CAACgB,QAAQ,CAACf,MAAM,CAAC,EAC7BD,YAAY;EAAA;;;2EAEXY,2BAA2B;IAAAK,OAAA,GAAAC,EAAA,CAAAlB,YAAA;IAAAmB,OAAA,GAF5BnB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}