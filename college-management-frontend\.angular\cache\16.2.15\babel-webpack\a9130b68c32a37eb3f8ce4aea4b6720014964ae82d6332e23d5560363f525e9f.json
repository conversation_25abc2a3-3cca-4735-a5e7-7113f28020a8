{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport Swal from 'sweetalert2'; // Import the service\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/classes.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"src/app/services/program.service\";\nimport * as i5 from \"src/app/services/department.service\";\nimport * as i6 from \"src/app/services/subject.service\";\nimport * as i7 from \"src/app/services/user.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/material/icon\";\nfunction ClassFormComponent_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"save\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Save \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction ClassFormComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 34);\n    i0.ɵɵtext(1, \" Saving... \");\n  }\n}\nfunction ClassFormComponent_option_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const program_r19 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", program_r19._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", program_r19.name, \" - \", program_r19.fullName, \" \");\n  }\n}\nfunction ClassFormComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtext(1, \" Program selection is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClassFormComponent_option_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dept_r20 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", dept_r20._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", dept_r20.name, \" \");\n  }\n}\nfunction ClassFormComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtext(1, \" Department selection is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClassFormComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtext(1, \" No departments available for the selected program \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClassFormComponent_div_32_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtext(1, \" Section is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClassFormComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"label\", 38);\n    i0.ɵɵtext(2, \"Section *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 39);\n    i0.ɵɵtemplate(4, ClassFormComponent_div_32_div_4_Template, 2, 0, \"div\", 14);\n    i0.ɵɵelementStart(5, \"small\", 40)(6, \"mat-icon\", 41);\n    i0.ɵɵtext(7, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" Sections are used for Intermediate programs to group students \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_0_0 = ctx_r8.classForm.get(\"section\")) == null ? null : tmp_0_0.touched) && ((tmp_0_0 = ctx_r8.classForm.get(\"section\")) == null ? null : tmp_0_0.invalid));\n  }\n}\nfunction ClassFormComponent_ng_container_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"option\", 42);\n    i0.ɵɵtext(2, \"1st Year\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"option\", 43);\n    i0.ɵɵtext(4, \"2nd Year\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction ClassFormComponent_ng_container_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"option\", 42);\n    i0.ɵɵtext(2, \"1st Semester\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"option\", 43);\n    i0.ɵɵtext(4, \"2nd Semester\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"option\", 44);\n    i0.ɵɵtext(6, \"3rd Semester\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"option\", 45);\n    i0.ɵɵtext(8, \"4th Semester\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"option\", 46);\n    i0.ɵɵtext(10, \"5th Semester\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"option\", 47);\n    i0.ɵɵtext(12, \"6th Semester\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"option\", 48);\n    i0.ɵɵtext(14, \"7th Semester\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"option\", 49);\n    i0.ɵɵtext(16, \"8th Semester\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction ClassFormComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.isIntermediateProgram() ? \"Year\" : \"Semester\", \" selection is required \");\n  }\n}\nfunction ClassFormComponent_small_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 40)(1, \"mat-icon\", 41);\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Intermediate programs are year-based (1st Year, 2nd Year) \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClassFormComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"label\");\n    i0.ɵɵtext(2, \"Generated Class Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 50)(4, \"strong\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"small\", 40)(7, \"mat-icon\", 41);\n    i0.ɵɵtext(8, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9, \" Class name is automatically generated based on your selections \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r13.generatedClassName);\n  }\n}\nfunction ClassFormComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtext(1, \" Academic year is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClassFormComponent_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtext(1, \" Maximum students is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClassFormComponent_div_63_option_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const subject_r28 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", subject_r28._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", subject_r28.subjectName, \" (\", subject_r28.code, \") \");\n  }\n}\nfunction ClassFormComponent_div_63_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtext(1, \" Subject selection is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClassFormComponent_div_63_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const teacher_r29 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", teacher_r29._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", teacher_r29.name, \" - \", teacher_r29.designation || \"Teacher\", \" \");\n  }\n}\nfunction ClassFormComponent_div_63_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtext(1, \" Teacher selection is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClassFormComponent_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"div\", 52)(2, \"div\", 53)(3, \"label\");\n    i0.ɵɵtext(4, \"Subject *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"select\", 54)(6, \"option\", 12);\n    i0.ɵɵtext(7, \"Select Subject\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, ClassFormComponent_div_63_option_8_Template, 2, 3, \"option\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, ClassFormComponent_div_63_div_9_Template, 2, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 53)(11, \"label\");\n    i0.ɵɵtext(12, \"Teacher *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"select\", 55)(14, \"option\", 12);\n    i0.ɵɵtext(15, \"Select Teacher\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, ClassFormComponent_div_63_option_16_Template, 2, 3, \"option\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, ClassFormComponent_div_63_div_17_Template, 2, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 56)(19, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function ClassFormComponent_div_63_Template_button_click_19_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r31);\n      const i_r23 = restoredCtx.index;\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.removeSubjectTeacher(i_r23));\n    });\n    i0.ɵɵelementStart(20, \"mat-icon\");\n    i0.ɵɵtext(21, \"delete\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const subjectGroup_r22 = ctx.$implicit;\n    const i_r23 = ctx.index;\n    const ctx_r16 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    let tmp_6_0;\n    i0.ɵɵproperty(\"formGroupName\", i_r23);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_1_0 = subjectGroup_r22.get(\"subject\")) == null ? null : tmp_1_0.touched) && ((tmp_1_0 = subjectGroup_r22.get(\"subject\")) == null ? null : tmp_1_0.invalid));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r16.filteredSubjects);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = subjectGroup_r22.get(\"subject\")) == null ? null : tmp_3_0.touched) && ((tmp_3_0 = subjectGroup_r22.get(\"subject\")) == null ? null : tmp_3_0.invalid));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_4_0 = subjectGroup_r22.get(\"teacher\")) == null ? null : tmp_4_0.touched) && ((tmp_4_0 = subjectGroup_r22.get(\"teacher\")) == null ? null : tmp_4_0.invalid));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r16.teachers);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = subjectGroup_r22.get(\"teacher\")) == null ? null : tmp_6_0.touched) && ((tmp_6_0 = subjectGroup_r22.get(\"teacher\")) == null ? null : tmp_6_0.invalid));\n  }\n}\nfunction ClassFormComponent_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵtext(1, \" No subjects added yet. Click \\\"Add Subject\\\" to assign subjects and teachers to this class. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClassFormComponent_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"small\", 37)(2, \"strong\");\n    i0.ɵɵtext(3, \"Current Assignments:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r18.subjectsFormArray.length, \" subject(s) assigned \");\n  }\n}\nexport class ClassFormComponent {\n  constructor(classesService, ActivatedRoute, fb, router, programService, departmentService, subjectService, userService) {\n    this.classesService = classesService;\n    this.ActivatedRoute = ActivatedRoute;\n    this.fb = fb;\n    this.router = router;\n    this.programService = programService;\n    this.departmentService = departmentService;\n    this.subjectService = subjectService;\n    this.userService = userService;\n    this.subjects = [];\n    this.teachers = [];\n    this.programs = [];\n    this.departments = [];\n    this.filteredDepartments = [];\n    this.filteredSubjects = [];\n    this.isLoading = false;\n    this.generatedClassName = '';\n    this.classForm = this.fb.group({\n      section: [''],\n      program: ['', Validators.required],\n      department: ['', Validators.required],\n      semester: ['', [Validators.required, Validators.min(1), Validators.max(8)]],\n      academicYear: ['', Validators.required],\n      maxStudents: [50, [Validators.required, Validators.min(1)]],\n      subjects: this.fb.array([]) // Array for dynamically selected subjects\n    });\n  }\n\n  ngOnInit() {\n    this.editingClassId = this.ActivatedRoute.snapshot.paramMap.get('id');\n    this.loadInitialData();\n    this.setupFormSubscriptions();\n    if (this.editingClassId) {\n      this.loadClassData(this.editingClassId);\n    }\n  }\n  loadInitialData() {\n    this.loadPrograms();\n    this.loadDepartments();\n    this.loadTeachers();\n    this.loadSubjects();\n  }\n  loadPrograms() {\n    this.programService.getAllPrograms(true).subscribe({\n      next: response => {\n        if (response.success) {\n          this.programs = response.programs;\n        }\n      },\n      error: error => {\n        console.error('Error loading programs:', error);\n        Swal.fire('Error', 'Failed to load programs', 'error');\n      }\n    });\n  }\n  loadDepartments() {\n    // Initialize empty arrays - departments will be loaded based on program selection\n    this.departments = [];\n    this.filteredDepartments = [];\n  }\n  loadTeachers() {\n    this.userService.getUsersByRole('Teacher').subscribe({\n      next: response => {\n        if (response.success) {\n          this.teachers = response.users;\n        }\n      },\n      error: error => {\n        console.error('Error loading teachers:', error);\n        Swal.fire('Error', 'Failed to load teachers', 'error');\n      }\n    });\n  }\n  loadSubjects() {\n    this.subjectService.getAllSubjects().subscribe({\n      next: response => {\n        if (response.success) {\n          this.subjects = response.subjects;\n          this.filteredSubjects = this.subjects;\n        }\n      },\n      error: error => {\n        console.error('Error loading subjects:', error);\n        Swal.fire('Error', 'Failed to load subjects', 'error');\n      }\n    });\n  }\n  setupFormSubscriptions() {\n    // Load departments when program changes\n    this.classForm.get('program')?.valueChanges.subscribe(programId => {\n      if (programId) {\n        // Load departments for the selected program\n        this.loadDepartmentsByProgram(programId);\n        // Reset department selection\n        this.classForm.get('department')?.setValue('');\n        // Handle program type logic\n        const selectedProgram = this.programs.find(p => p._id === programId);\n        this.handleProgramTypeChange(selectedProgram);\n        // Filter teachers by program\n        this.filterTeachersByProgram(programId);\n        // Generate class name\n        this.generateClassName();\n      } else {\n        this.filteredDepartments = [];\n        this.filteredSubjects = [];\n        this.generatedClassName = '';\n        this.classForm.get('department')?.setValue('');\n      }\n    });\n    // Filter subjects when department changes\n    this.classForm.get('department')?.valueChanges.subscribe(departmentId => {\n      if (departmentId) {\n        this.loadSubjectsByDepartment(departmentId);\n        this.generateClassName();\n      } else {\n        this.filteredSubjects = [];\n      }\n    });\n    // Generate class name when semester changes\n    this.classForm.get('semester')?.valueChanges.subscribe(() => {\n      this.generateClassName();\n    });\n    // Generate class name when section changes\n    this.classForm.get('section')?.valueChanges.subscribe(() => {\n      this.generateClassName();\n    });\n  }\n  loadDepartmentsByProgram(programId) {\n    this.departmentService.getDepartmentsByProgram(programId, true).subscribe({\n      next: response => {\n        if (response.success) {\n          this.filteredDepartments = response.departments;\n        }\n      },\n      error: error => {\n        console.error('Error loading departments by program:', error);\n        Swal.fire('Error', 'Failed to load departments for selected program', 'error');\n        this.filteredDepartments = [];\n      }\n    });\n  }\n  loadSubjectsByDepartment(departmentId) {\n    this.subjectService.getSubjectsByDepartment(departmentId, true).subscribe({\n      next: response => {\n        if (response.success) {\n          this.filteredSubjects = response.subjects;\n        }\n      },\n      error: error => {\n        console.error('Error loading subjects by department:', error);\n        this.filteredSubjects = [];\n      }\n    });\n  }\n  generateClassName() {\n    const formData = this.classForm.value;\n    const selectedProgram = this.programs.find(p => p._id === formData.program);\n    const selectedDepartment = this.filteredDepartments.find(d => d._id === formData.department);\n    if (selectedProgram && selectedDepartment && formData.semester) {\n      let semesterText = '';\n      if (selectedProgram.name === 'Intermediate') {\n        // For Intermediate programs - require section\n        if (!formData.section) {\n          this.generatedClassName = '';\n          return;\n        }\n        switch (formData.semester) {\n          case '1':\n            semesterText = '2nd Year';\n            break;\n          case '2':\n            semesterText = '1st Year';\n            break;\n          default:\n            semesterText = `${formData.semester} Year`;\n        }\n        this.generatedClassName = `${semesterText} - Section ${formData.section}`;\n      } else {\n        // For BS/MS programs - no section needed\n        switch (formData.semester) {\n          case '1':\n            semesterText = '1st Semester';\n            break;\n          case '2':\n            semesterText = '2nd Semester';\n            break;\n          case '3':\n            semesterText = '3rd Semester';\n            break;\n          case '4':\n            semesterText = '4th Semester';\n            break;\n          case '5':\n            semesterText = '5th Semester';\n            break;\n          case '6':\n            semesterText = '6th Semester';\n            break;\n          case '7':\n            semesterText = '7th Semester';\n            break;\n          case '8':\n            semesterText = '8th Semester';\n            break;\n          default:\n            semesterText = `${formData.semester} Semester`;\n        }\n        this.generatedClassName = semesterText;\n      }\n    } else {\n      this.generatedClassName = '';\n    }\n  }\n  handleProgramTypeChange(program) {\n    const sectionControl = this.classForm.get('section');\n    if (program?.name === 'Intermediate') {\n      // For Intermediate programs, section is required\n      sectionControl?.setValidators([Validators.required]);\n      sectionControl?.updateValueAndValidity();\n      // Set default values\n      this.classForm.patchValue({\n        semester: '1',\n        academicYear: new Date().getFullYear() + '-' + (new Date().getFullYear() + 1)\n      });\n    } else {\n      // For BS/MS programs, section is not required\n      sectionControl?.clearValidators();\n      sectionControl?.updateValueAndValidity();\n      sectionControl?.setValue(''); // Clear section value\n      // Set default values\n      this.classForm.patchValue({\n        semester: '1',\n        academicYear: new Date().getFullYear() + '-' + (new Date().getFullYear() + 1)\n      });\n    }\n  }\n  filterTeachersByProgram(programId) {\n    // Filter teachers based on program\n    this.userService.getUsersByProgram(programId, 'Teacher').subscribe({\n      next: response => {\n        if (response.success) {\n          console.log(\"res\", response);\n          this.teachers = response.users;\n        }\n      },\n      error: error => {\n        console.error('Error loading teachers by program:', error);\n        // Fallback to all teachers if program-specific filtering fails\n        this.loadTeachers();\n      }\n    });\n  }\n  isIntermediateProgram() {\n    const programId = this.classForm.get('program')?.value;\n    const selectedProgram = this.programs.find(p => p._id === programId);\n    return selectedProgram?.name === 'Intermediate';\n  }\n  get subjectsFormArray() {\n    return this.classForm.get('subjects');\n  }\n  loadClassData(id) {\n    this.classesService.getClassById(id).subscribe({\n      next: response => {\n        if (response.success) {\n          const classData = response.class;\n          console.log('Loading class data:', classData); // Debug log\n          // First load departments for the program\n          this.loadDepartmentsByProgram(classData.program._id);\n          // Load subjects by department\n          this.loadSubjectsByDepartment(classData.department._id);\n          // Load teachers by program\n          this.filterTeachersByProgram(classData.program._id);\n          // Then patch the form values\n          this.classForm.patchValue({\n            section: classData.section,\n            program: classData.program._id,\n            department: classData.department._id,\n            semester: classData.semester.toString(),\n            academicYear: classData.academicYear,\n            maxStudents: classData.maxStudents\n          });\n          // Load subjects and teachers after a delay to ensure data is loaded\n          setTimeout(() => {\n            this.loadSubjectsAndTeachers(classData);\n            this.generateClassName();\n          }, 1500); // Increased delay to ensure all data is loaded\n        }\n      },\n\n      error: error => {\n        console.error('Error loading class data:', error);\n        Swal.fire('Error', 'Failed to load class data', 'error');\n      }\n    });\n  }\n  loadSubjectsAndTeachers(classData) {\n    const subjectsArray = this.classForm.get('subjects');\n    subjectsArray.clear();\n    if (classData.subjects && classData.subjects.length > 0) {\n      console.log('Loading subjects and teachers:', classData.subjects); // Debug log\n      classData.subjects.forEach(subjectTeacher => {\n        const subjectId = subjectTeacher.subject?._id || subjectTeacher.subject;\n        const teacherId = subjectTeacher.teacher?._id || subjectTeacher.teacher;\n        subjectsArray.push(this.fb.group({\n          subject: [subjectId, Validators.required],\n          teacher: [teacherId, Validators.required]\n        }));\n      });\n      console.log('Subjects form array after loading:', subjectsArray.value); // Debug log\n    }\n  }\n\n  onSubjectTeacherSelect(subjectId, event) {\n    const teacherId = event.target.value; // Get the selected teacher ID from the event\n    const subjectsArray = this.classForm.get('subjects');\n    const existingIndex = subjectsArray.controls.findIndex(control => control.value.subject === subjectId);\n    if (existingIndex !== -1) {\n      subjectsArray.at(existingIndex).patchValue({\n        teacher: teacherId\n      });\n    } else {\n      subjectsArray.push(this.fb.group({\n        subject: [subjectId],\n        teacher: [teacherId]\n      }));\n    }\n  }\n  addSubjectTeacher() {\n    const subjectsArray = this.classForm.get('subjects');\n    subjectsArray.push(this.fb.group({\n      subject: ['', Validators.required],\n      teacher: ['', Validators.required]\n    }));\n  }\n  removeSubjectTeacher(index) {\n    const subjectsArray = this.classForm.get('subjects');\n    subjectsArray.removeAt(index);\n  }\n  // Handle checkbox change for subjects (legacy method - keeping for compatibility)\n  onSubjectChange(event) {\n    // This method is kept for backward compatibility but not actively used\n    // The main subject/teacher assignment is handled through addSubjectTeacher/removeSubjectTeacher\n    console.log('Legacy subject change method called:', event.target.value);\n  }\n  // Submit the form to add a new class\n  onSubmit() {\n    // Custom validation based on program type\n    const formData = this.classForm.value;\n    const isIntermediate = this.isIntermediateProgram();\n    // Check required fields\n    if (!formData.program || !formData.department || !formData.semester || !formData.academicYear) {\n      this.markFormGroupTouched();\n      Swal.fire('Validation Error', 'Please fill in all required fields correctly.', 'warning');\n      return;\n    }\n    // For Intermediate programs, section is required\n    if (isIntermediate && !formData.section) {\n      this.classForm.get('section')?.markAsTouched();\n      Swal.fire('Validation Error', 'Section is required for Intermediate programs.', 'warning');\n      return;\n    }\n    if (!this.generatedClassName) {\n      Swal.fire('Error', 'Please complete all selections to generate a class name.', 'warning');\n      return;\n    }\n    // Validate subjects if any are added\n    const subjectsArray = this.classForm.get('subjects');\n    if (subjectsArray.length > 0) {\n      for (let i = 0; i < subjectsArray.length; i++) {\n        const subjectGroup = subjectsArray.at(i);\n        if (!subjectGroup.get('subject')?.value || !subjectGroup.get('teacher')?.value) {\n          Swal.fire('Validation Error', 'Please select both subject and teacher for all added entries.', 'warning');\n          return;\n        }\n      }\n    }\n    this.isLoading = true;\n    const classData = {\n      ...this.classForm.value,\n      className: this.generatedClassName,\n      semester: parseInt(this.classForm.value.semester),\n      section: isIntermediate ? formData.section : '' // Set section to empty string for BS programs\n    };\n\n    console.log('Submitting class data:', classData); // Debug log\n    const request = this.editingClassId ? this.classesService.updateClass(this.editingClassId, classData) : this.classesService.createClass(classData);\n    request.subscribe({\n      next: response => {\n        if (response.success) {\n          this.isLoading = false;\n          Swal.fire({\n            title: this.editingClassId ? 'Class updated successfully' : 'Class created successfully',\n            icon: 'success',\n            confirmButtonColor: '#3085d6',\n            timer: 1500\n          }).then(() => {\n            this.router.navigate(['/dashboard/admin/classes']);\n          });\n        } else {\n          this.isLoading = false;\n          Swal.fire('Error', response.message || 'Failed to save class', 'error');\n        }\n      },\n      error: error => {\n        this.isLoading = false;\n        console.error('Error saving class:', error);\n        const errorMessage = error.error?.message || 'Something went wrong while saving the class';\n        Swal.fire('Error', errorMessage, 'error');\n      }\n    });\n  }\n  markFormGroupTouched() {\n    Object.keys(this.classForm.controls).forEach(key => {\n      const control = this.classForm.get(key);\n      control?.markAsTouched();\n    });\n    // Mark subjects array controls as touched\n    const subjectsArray = this.classForm.get('subjects');\n    subjectsArray.controls.forEach(group => {\n      Object.keys(group.controls).forEach(key => {\n        group.get(key)?.markAsTouched();\n      });\n    });\n  }\n  // Navigate back\n  goBack() {\n    this.router.navigate(['/dashboard/admin/classes']);\n  }\n  static #_ = this.ɵfac = function ClassFormComponent_Factory(t) {\n    return new (t || ClassFormComponent)(i0.ɵɵdirectiveInject(i1.ClassesService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i4.ProgramService), i0.ɵɵdirectiveInject(i5.DepartmentService), i0.ɵɵdirectiveInject(i6.SubjectService), i0.ɵɵdirectiveInject(i7.UserService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ClassFormComponent,\n    selectors: [[\"app-class-form\"]],\n    decls: 66,\n    vars: 25,\n    consts: [[1, \"form-container\"], [1, \"form-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"d-flex\", \"gap-2\"], [1, \"btn-back\", 3, \"click\"], [1, \"btn\", \"btn-primary\", \"btn-save\", 3, \"disabled\", \"click\"], [4, \"ngIf\", \"ngIfElse\"], [\"loading\", \"\"], [3, \"formGroup\"], [1, \"form-grid\"], [1, \"form-group\", \"mb-3\"], [\"for\", \"program\"], [\"formControlName\", \"program\", 1, \"form-control\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [\"for\", \"department\"], [\"formControlName\", \"department\", 1, \"form-control\", 3, \"disabled\"], [\"class\", \"text-info\", 4, \"ngIf\"], [\"class\", \"form-group mb-3\", 4, \"ngIf\"], [\"for\", \"semester\"], [\"formControlName\", \"semester\", 1, \"form-control\"], [4, \"ngIf\"], [\"class\", \"form-text text-muted\", 4, \"ngIf\"], [\"for\", \"academicYear\"], [\"type\", \"text\", \"placeholder\", \"e.g., 2024-2025\", \"formControlName\", \"academicYear\", 1, \"form-control\"], [\"for\", \"maxStudents\"], [\"type\", \"number\", \"placeholder\", \"50\", \"formControlName\", \"maxStudents\", \"min\", \"1\", 1, \"form-control\"], [1, \"subjects-section\", \"mt-4\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-primary\", 3, \"click\"], [\"formArrayName\", \"subjects\"], [\"class\", \"subject-teacher-row mb-3\", 3, \"formGroupName\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-muted text-center py-3\", 4, \"ngIf\"], [\"class\", \"mt-3\", 4, \"ngIf\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"text-light\", \"me-1\"], [3, \"value\"], [1, \"text-danger\"], [1, \"text-info\"], [\"for\", \"section\"], [\"type\", \"text\", \"placeholder\", \"e.g., A, B, C\", \"formControlName\", \"section\", 1, \"form-control\"], [1, \"form-text\", \"text-muted\"], [1, \"small-icon\"], [\"value\", \"1\"], [\"value\", \"2\"], [\"value\", \"3\"], [\"value\", \"4\"], [\"value\", \"5\"], [\"value\", \"6\"], [\"value\", \"7\"], [\"value\", \"8\"], [1, \"form-control-plaintext\", \"bg-light\", \"p-2\", \"rounded\"], [1, \"subject-teacher-row\", \"mb-3\", 3, \"formGroupName\"], [1, \"row\"], [1, \"col-md-5\"], [\"formControlName\", \"subject\", 1, \"form-control\"], [\"formControlName\", \"teacher\", 1, \"form-control\"], [1, \"col-md-2\", \"d-flex\", \"align-items-end\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", 3, \"click\"], [1, \"text-muted\", \"text-center\", \"py-3\"], [1, \"mt-3\"]],\n    template: function ClassFormComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n        i0.ɵɵtext(3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\", 2)(5, \"button\", 3);\n        i0.ɵɵlistener(\"click\", function ClassFormComponent_Template_button_click_5_listener() {\n          return ctx.goBack();\n        });\n        i0.ɵɵelementStart(6, \"mat-icon\");\n        i0.ɵɵtext(7, \"arrow_back\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(8, \" Back \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"button\", 4);\n        i0.ɵɵlistener(\"click\", function ClassFormComponent_Template_button_click_9_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵtemplate(10, ClassFormComponent_ng_container_10_Template, 4, 0, \"ng-container\", 5);\n        i0.ɵɵtemplate(11, ClassFormComponent_ng_template_11_Template, 2, 0, \"ng-template\", null, 6, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(13, \"form\", 7)(14, \"div\", 8)(15, \"div\", 9)(16, \"label\", 10);\n        i0.ɵɵtext(17, \"Program *\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(18, \"select\", 11)(19, \"option\", 12);\n        i0.ɵɵtext(20, \"Select Program\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(21, ClassFormComponent_option_21_Template, 2, 3, \"option\", 13);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(22, ClassFormComponent_div_22_Template, 2, 0, \"div\", 14);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(23, \"div\", 9)(24, \"label\", 15);\n        i0.ɵɵtext(25, \"Department *\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(26, \"select\", 16)(27, \"option\", 12);\n        i0.ɵɵtext(28);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(29, ClassFormComponent_option_29_Template, 2, 2, \"option\", 13);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(30, ClassFormComponent_div_30_Template, 2, 0, \"div\", 14);\n        i0.ɵɵtemplate(31, ClassFormComponent_div_31_Template, 2, 0, \"div\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(32, ClassFormComponent_div_32_Template, 9, 1, \"div\", 18);\n        i0.ɵɵelementStart(33, \"div\", 9)(34, \"label\", 19);\n        i0.ɵɵtext(35);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(36, \"select\", 20)(37, \"option\", 12);\n        i0.ɵɵtext(38);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(39, ClassFormComponent_ng_container_39_Template, 5, 0, \"ng-container\", 21);\n        i0.ɵɵtemplate(40, ClassFormComponent_ng_container_40_Template, 17, 0, \"ng-container\", 21);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(41, ClassFormComponent_div_41_Template, 2, 1, \"div\", 14);\n        i0.ɵɵtemplate(42, ClassFormComponent_small_42_Template, 4, 0, \"small\", 22);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(43, ClassFormComponent_div_43_Template, 10, 1, \"div\", 18);\n        i0.ɵɵelementStart(44, \"div\", 9)(45, \"label\", 23);\n        i0.ɵɵtext(46, \"Academic Year *\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(47, \"input\", 24);\n        i0.ɵɵtemplate(48, ClassFormComponent_div_48_Template, 2, 0, \"div\", 14);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(49, \"div\", 9)(50, \"label\", 25);\n        i0.ɵɵtext(51, \"Maximum Students *\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(52, \"input\", 26);\n        i0.ɵɵtemplate(53, ClassFormComponent_div_53_Template, 2, 0, \"div\", 14);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(54, \"div\", 27)(55, \"div\", 28)(56, \"h4\");\n        i0.ɵɵtext(57, \"Subjects & Teachers\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(58, \"button\", 29);\n        i0.ɵɵlistener(\"click\", function ClassFormComponent_Template_button_click_58_listener() {\n          return ctx.addSubjectTeacher();\n        });\n        i0.ɵɵelementStart(59, \"mat-icon\");\n        i0.ɵɵtext(60, \"add\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(61, \" Add Subject \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(62, \"div\", 30);\n        i0.ɵɵtemplate(63, ClassFormComponent_div_63_Template, 22, 9, \"div\", 31);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(64, ClassFormComponent_div_64_Template, 2, 0, \"div\", 32);\n        i0.ɵɵtemplate(65, ClassFormComponent_div_65_Template, 5, 1, \"div\", 33);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        const _r1 = i0.ɵɵreference(12);\n        let tmp_6_0;\n        let tmp_7_0;\n        let tmp_8_0;\n        let tmp_10_0;\n        let tmp_11_0;\n        let tmp_17_0;\n        let tmp_20_0;\n        let tmp_21_0;\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.editingClassId ? \"Update Class\" : \"Add New Class\");\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"disabled\", ctx.classForm.invalid || ctx.isLoading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLoading)(\"ngIfElse\", _r1);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"formGroup\", ctx.classForm);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"ngForOf\", ctx.programs);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx.classForm.get(\"program\")) == null ? null : tmp_6_0.touched) && ((tmp_6_0 = ctx.classForm.get(\"program\")) == null ? null : tmp_6_0.invalid));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"disabled\", !((tmp_7_0 = ctx.classForm.get(\"program\")) == null ? null : tmp_7_0.value));\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(!((tmp_8_0 = ctx.classForm.get(\"program\")) == null ? null : tmp_8_0.value) ? \"Select Program First\" : \"Select Department\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngForOf\", ctx.filteredDepartments);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_10_0 = ctx.classForm.get(\"department\")) == null ? null : tmp_10_0.touched) && ((tmp_10_0 = ctx.classForm.get(\"department\")) == null ? null : tmp_10_0.invalid));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_11_0 = ctx.classForm.get(\"program\")) == null ? null : tmp_11_0.value) && ctx.filteredDepartments.length === 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isIntermediateProgram());\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.isIntermediateProgram() ? \"Academic Year *\" : \"Semester *\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.isIntermediateProgram() ? \"Select Year\" : \"Select Semester\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isIntermediateProgram());\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isIntermediateProgram());\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_17_0 = ctx.classForm.get(\"semester\")) == null ? null : tmp_17_0.touched) && ((tmp_17_0 = ctx.classForm.get(\"semester\")) == null ? null : tmp_17_0.invalid));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isIntermediateProgram());\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.generatedClassName);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_20_0 = ctx.classForm.get(\"academicYear\")) == null ? null : tmp_20_0.touched) && ((tmp_20_0 = ctx.classForm.get(\"academicYear\")) == null ? null : tmp_20_0.invalid));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_21_0 = ctx.classForm.get(\"maxStudents\")) == null ? null : tmp_21_0.touched) && ((tmp_21_0 = ctx.classForm.get(\"maxStudents\")) == null ? null : tmp_21_0.invalid));\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"ngForOf\", ctx.subjectsFormArray.controls);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.subjectsFormArray.length === 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.editingClassId && ctx.subjectsFormArray.length > 0);\n      }\n    },\n    dependencies: [i8.NgForOf, i8.NgIf, i9.MatIcon, i3.ɵNgNoValidate, i3.NgSelectOption, i3.ɵNgSelectMultipleOption, i3.DefaultValueAccessor, i3.NumberValueAccessor, i3.SelectControlValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.MinValidator, i3.FormGroupDirective, i3.FormControlName, i3.FormGroupName, i3.FormArrayName],\n    styles: [\"\\n\\n.subjects-section[_ngcontent-%COMP%] {\\n  margin-top: 24px;\\n}\\n\\n.subjects-label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 12px;\\n  color: #515154;\\n  font-weight: 500;\\n  font-size: 14px;\\n}\\n\\n.subjects-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\\n  gap: 12px;\\n}\\n\\n\\n\\n.checkbox-container[_ngcontent-%COMP%] {\\n  display: block;\\n  position: relative;\\n  padding-left: 30px;\\n  margin-bottom: 12px;\\n  cursor: pointer;\\n  font-size: 14px;\\n  color: black;\\n  -webkit-user-select: none;\\n  user-select: none;\\n}\\n\\n.checkbox-container[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  position: absolute;\\n  opacity: 0;\\n  cursor: pointer;\\n  height: 0;\\n  width: 0;\\n}\\n\\n.checkmark[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  height: 20px;\\n  width: 20px;\\n  background-color: white;\\n  border: 1px solid #515154;\\n  border-radius: 4px;\\n}\\n\\n.checkbox-container[_ngcontent-%COMP%]:hover   input[_ngcontent-%COMP%]    ~ .checkmark[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n}\\n\\n.checkbox-container[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked    ~ .checkmark[_ngcontent-%COMP%] {\\n  background-color: #11418e;\\n  border-color: #11418e;\\n}\\n\\n.checkmark[_ngcontent-%COMP%]:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  display: none;\\n}\\n\\n.checkbox-container[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked    ~ .checkmark[_ngcontent-%COMP%]:after {\\n  display: block;\\n}\\n\\n.checkbox-container[_ngcontent-%COMP%]   .checkmark[_ngcontent-%COMP%]:after {\\n  left: 7px;\\n  top: 3px;\\n  width: 5px;\\n  height: 10px;\\n  border: solid white;\\n  border-width: 0 2px 2px 0;\\n  transform: rotate(45deg);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "<PERSON><PERSON>", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelementContainerEnd", "ɵɵelement", "ɵɵproperty", "program_r19", "_id", "ɵɵadvance", "ɵɵtextInterpolate2", "name", "fullName", "dept_r20", "ɵɵtextInterpolate1", "ɵɵtemplate", "ClassFormComponent_div_32_div_4_Template", "tmp_0_0", "ctx_r8", "classForm", "get", "touched", "invalid", "ctx_r11", "isIntermediateProgram", "ɵɵtextInterpolate", "ctx_r13", "generatedClassName", "subject_r28", "subjectName", "code", "teacher_r29", "designation", "ClassFormComponent_div_63_option_8_Template", "ClassFormComponent_div_63_div_9_Template", "ClassFormComponent_div_63_option_16_Template", "ClassFormComponent_div_63_div_17_Template", "ɵɵlistener", "ClassFormComponent_div_63_Template_button_click_19_listener", "restoredCtx", "ɵɵrestoreView", "_r31", "i_r23", "index", "ctx_r30", "ɵɵnextContext", "ɵɵresetView", "removeSubjectTeacher", "ɵɵclassProp", "tmp_1_0", "subjectGroup_r22", "ctx_r16", "filteredSubjects", "tmp_3_0", "tmp_4_0", "teachers", "tmp_6_0", "ctx_r18", "subjectsFormArray", "length", "ClassFormComponent", "constructor", "classesService", "ActivatedRoute", "fb", "router", "programService", "departmentService", "subjectService", "userService", "subjects", "programs", "departments", "filteredDepartments", "isLoading", "group", "section", "program", "required", "department", "semester", "min", "max", "academicYear", "maxStudents", "array", "ngOnInit", "editingClassId", "snapshot", "paramMap", "loadInitialData", "setupFormSubscriptions", "loadClassData", "loadPrograms", "loadDepartments", "loadTeachers", "loadSubjects", "getAllPrograms", "subscribe", "next", "response", "success", "error", "console", "fire", "getUsersByRole", "users", "getAllSubjects", "valueChanges", "programId", "loadDepartmentsByProgram", "setValue", "selectedProgram", "find", "p", "handleProgramTypeChange", "filterTeachersByProgram", "generateClassName", "departmentId", "loadSubjectsByDepartment", "getDepartmentsByProgram", "getSubjectsByDepartment", "formData", "value", "selectedDepartment", "d", "semesterText", "sectionControl", "setValidators", "updateValueAndValidity", "patchValue", "Date", "getFullYear", "clearValidators", "getUsersByProgram", "log", "id", "getClassById", "classData", "class", "toString", "setTimeout", "loadSubjectsAndTeachers", "subjectsArray", "clear", "for<PERSON>ach", "<PERSON><PERSON><PERSON>er", "subjectId", "subject", "teacherId", "teacher", "push", "onSubjectTeacherSelect", "event", "target", "existingIndex", "controls", "findIndex", "control", "at", "addSubjectTeacher", "removeAt", "onSubjectChange", "onSubmit", "isIntermediate", "markFormGroupTouched", "<PERSON><PERSON><PERSON><PERSON>ched", "i", "subjectGroup", "className", "parseInt", "request", "updateClass", "createClass", "title", "icon", "confirmButtonColor", "timer", "then", "navigate", "message", "errorMessage", "Object", "keys", "key", "goBack", "_", "ɵɵdirectiveInject", "i1", "ClassesService", "i2", "i3", "FormBuilder", "Router", "i4", "ProgramService", "i5", "DepartmentService", "i6", "SubjectService", "i7", "UserService", "_2", "selectors", "decls", "vars", "consts", "template", "ClassFormComponent_Template", "rf", "ctx", "ClassFormComponent_Template_button_click_5_listener", "ClassFormComponent_Template_button_click_9_listener", "ClassFormComponent_ng_container_10_Template", "ClassFormComponent_ng_template_11_Template", "ɵɵtemplateRefExtractor", "ClassFormComponent_option_21_Template", "ClassFormComponent_div_22_Template", "ClassFormComponent_option_29_Template", "ClassFormComponent_div_30_Template", "ClassFormComponent_div_31_Template", "ClassFormComponent_div_32_Template", "ClassFormComponent_ng_container_39_Template", "ClassFormComponent_ng_container_40_Template", "ClassFormComponent_div_41_Template", "ClassFormComponent_small_42_Template", "ClassFormComponent_div_43_Template", "ClassFormComponent_div_48_Template", "ClassFormComponent_div_53_Template", "ClassFormComponent_Template_button_click_58_listener", "ClassFormComponent_div_63_Template", "ClassFormComponent_div_64_Template", "ClassFormComponent_div_65_Template", "_r1", "tmp_7_0", "tmp_8_0", "tmp_10_0", "tmp_11_0", "tmp_17_0", "tmp_20_0", "tmp_21_0"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\classes\\class-form\\class-form.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\classes\\class-form\\class-form.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { <PERSON><PERSON><PERSON><PERSON>, FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { ClassesService } from 'src/app/services/classes.service';\r\nimport { ProgramService } from 'src/app/services/program.service';\r\nimport { DepartmentService } from 'src/app/services/department.service';\r\nimport { SubjectService } from 'src/app/services/subject.service';\r\nimport { UserService } from 'src/app/services/user.service';\r\nimport { Program, Department, Subject, User } from 'src/app/models/user';\r\nimport Swal from 'sweetalert2'; // Import the service\r\n\r\n@Component({\r\n  selector: 'app-class-form',\r\n  templateUrl: './class-form.component.html',\r\n  styleUrls: ['./class-form.component.css']\r\n})\r\nexport class ClassFormComponent implements OnInit {\r\n  classForm!: FormGroup;\r\n  subjects: Subject[] = [];\r\n  teachers: User[] = [];\r\n  programs: Program[] = [];\r\n  departments: Department[] = [];\r\n  filteredDepartments: Department[] = [];\r\n  filteredSubjects: Subject[] = [];\r\n  editingClassId: any;\r\n  isLoading = false;\r\n  generatedClassName = '';\r\n\r\n  constructor(\r\n    private classesService: ClassesService,\r\n    public ActivatedRoute: ActivatedRoute,\r\n    private fb: FormBuilder,\r\n    private router: Router,\r\n    private programService: ProgramService,\r\n    private departmentService: DepartmentService,\r\n    private subjectService: SubjectService,\r\n    private userService: UserService\r\n  ) {\r\n    this.classForm = this.fb.group({\r\n      section: [''], // Will be conditionally required based on program\r\n      program: ['', Validators.required],\r\n      department: ['', Validators.required],\r\n      semester: ['', [Validators.required, Validators.min(1), Validators.max(8)]],\r\n      academicYear: ['', Validators.required],\r\n      maxStudents: [50, [Validators.required, Validators.min(1)]],\r\n      subjects: this.fb.array([]) // Array for dynamically selected subjects\r\n    });\r\n  }\r\n  ngOnInit(): void {\r\n    this.editingClassId = this.ActivatedRoute.snapshot.paramMap.get('id');\r\n    this.loadInitialData();\r\n    this.setupFormSubscriptions();\r\n    if (this.editingClassId) {\r\n      this.loadClassData(this.editingClassId);\r\n    }\r\n  }\r\n\r\n  loadInitialData(): void {\r\n    this.loadPrograms();\r\n    this.loadDepartments();\r\n    this.loadTeachers();\r\n    this.loadSubjects();\r\n  }\r\n\r\n  loadPrograms(): void {\r\n    this.programService.getAllPrograms(true).subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.programs = response.programs;\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading programs:', error);\r\n        Swal.fire('Error', 'Failed to load programs', 'error');\r\n      }\r\n    });\r\n  }\r\n\r\n  loadDepartments(): void {\r\n    // Initialize empty arrays - departments will be loaded based on program selection\r\n    this.departments = [];\r\n    this.filteredDepartments = [];\r\n  }\r\n\r\n  loadTeachers(): void {\r\n    this.userService.getUsersByRole('Teacher').subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.teachers = response.users;\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading teachers:', error);\r\n        Swal.fire('Error', 'Failed to load teachers', 'error');\r\n      }\r\n    });\r\n  }\r\n\r\n  loadSubjects(): void {\r\n    this.subjectService.getAllSubjects().subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.subjects = response.subjects;\r\n          this.filteredSubjects = this.subjects;\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading subjects:', error);\r\n        Swal.fire('Error', 'Failed to load subjects', 'error');\r\n      }\r\n    });\r\n  }\r\n\r\n  setupFormSubscriptions(): void {\r\n    // Load departments when program changes\r\n    this.classForm.get('program')?.valueChanges.subscribe(programId => {\r\n      if (programId) {\r\n        // Load departments for the selected program\r\n        this.loadDepartmentsByProgram(programId);\r\n\r\n        // Reset department selection\r\n        this.classForm.get('department')?.setValue('');\r\n\r\n        // Handle program type logic\r\n        const selectedProgram = this.programs.find(p => p._id === programId);\r\n        this.handleProgramTypeChange(selectedProgram);\r\n\r\n        // Filter teachers by program\r\n        this.filterTeachersByProgram(programId);\r\n\r\n        // Generate class name\r\n        this.generateClassName();\r\n      } else {\r\n        this.filteredDepartments = [];\r\n        this.filteredSubjects = [];\r\n        this.generatedClassName = '';\r\n        this.classForm.get('department')?.setValue('');\r\n      }\r\n    });\r\n\r\n    // Filter subjects when department changes\r\n    this.classForm.get('department')?.valueChanges.subscribe(departmentId => {\r\n      if (departmentId) {\r\n        this.loadSubjectsByDepartment(departmentId);\r\n        this.generateClassName();\r\n      } else {\r\n        this.filteredSubjects = [];\r\n      }\r\n    });\r\n\r\n    // Generate class name when semester changes\r\n    this.classForm.get('semester')?.valueChanges.subscribe(() => {\r\n      this.generateClassName();\r\n    });\r\n\r\n    // Generate class name when section changes\r\n    this.classForm.get('section')?.valueChanges.subscribe(() => {\r\n      this.generateClassName();\r\n    });\r\n  }\r\n\r\n  loadDepartmentsByProgram(programId: string): void {\r\n    this.departmentService.getDepartmentsByProgram(programId, true).subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.filteredDepartments = response.departments;\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading departments by program:', error);\r\n        Swal.fire('Error', 'Failed to load departments for selected program', 'error');\r\n        this.filteredDepartments = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  loadSubjectsByDepartment(departmentId: string): void {\r\n    this.subjectService.getSubjectsByDepartment(departmentId, true).subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.filteredSubjects = response.subjects;\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading subjects by department:', error);\r\n        this.filteredSubjects = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  generateClassName(): void {\r\n    const formData = this.classForm.value;\r\n    const selectedProgram = this.programs.find(p => p._id === formData.program);\r\n    const selectedDepartment = this.filteredDepartments.find(d => d._id === formData.department);\r\n\r\n    if (selectedProgram && selectedDepartment && formData.semester) {\r\n      let semesterText = '';\r\n\r\n      if (selectedProgram.name === 'Intermediate') {\r\n        // For Intermediate programs - require section\r\n        if (!formData.section) {\r\n          this.generatedClassName = '';\r\n          return;\r\n        }\r\n\r\n        switch (formData.semester) {\r\n          case '1': semesterText = '2nd Year'; break;\r\n          case '2': semesterText = '1st Year'; break;\r\n          default: semesterText = `${formData.semester} Year`;\r\n        }\r\n\r\n        this.generatedClassName = `${semesterText} - Section ${formData.section}`;\r\n      } else {\r\n        // For BS/MS programs - no section needed\r\n        switch (formData.semester) {\r\n          case '1': semesterText = '1st Semester'; break;\r\n          case '2': semesterText = '2nd Semester'; break;\r\n          case '3': semesterText = '3rd Semester'; break;\r\n          case '4': semesterText = '4th Semester'; break;\r\n          case '5': semesterText = '5th Semester'; break;\r\n          case '6': semesterText = '6th Semester'; break;\r\n          case '7': semesterText = '7th Semester'; break;\r\n          case '8': semesterText = '8th Semester'; break;\r\n          default: semesterText = `${formData.semester} Semester`;\r\n        }\r\n\r\n        this.generatedClassName = semesterText;\r\n      }\r\n    } else {\r\n      this.generatedClassName = '';\r\n    }\r\n  }\r\n\r\n  handleProgramTypeChange(program: any): void {\r\n    const sectionControl = this.classForm.get('section');\r\n\r\n    if (program?.name === 'Intermediate') {\r\n      // For Intermediate programs, section is required\r\n      sectionControl?.setValidators([Validators.required]);\r\n      sectionControl?.updateValueAndValidity();\r\n\r\n      // Set default values\r\n      this.classForm.patchValue({\r\n        semester: '1', // Default to 1st year\r\n        academicYear: new Date().getFullYear() + '-' + (new Date().getFullYear() + 1)\r\n      });\r\n    } else {\r\n      // For BS/MS programs, section is not required\r\n      sectionControl?.clearValidators();\r\n      sectionControl?.updateValueAndValidity();\r\n      sectionControl?.setValue(''); // Clear section value\r\n\r\n      // Set default values\r\n      this.classForm.patchValue({\r\n        semester: '1', // Default to 1st semester\r\n        academicYear: new Date().getFullYear() + '-' + (new Date().getFullYear() + 1)\r\n      });\r\n    }\r\n  }\r\n\r\n  filterTeachersByProgram(programId: string): void {\r\n    // Filter teachers based on program\r\n    this.userService.getUsersByProgram(programId, 'Teacher').subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          console.log(\"res\" , response)\r\n          this.teachers = response.users;\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading teachers by program:', error);\r\n        // Fallback to all teachers if program-specific filtering fails\r\n        this.loadTeachers();\r\n      }\r\n    });\r\n  }\r\n\r\n  isIntermediateProgram(): boolean {\r\n    const programId = this.classForm.get('program')?.value;\r\n    const selectedProgram = this.programs.find(p => p._id === programId);\r\n    return selectedProgram?.name === 'Intermediate';\r\n  }\r\n  get subjectsFormArray() {\r\n    return this.classForm.get('subjects') as FormArray;\r\n  }\r\n\r\n  loadClassData(id: string): void {\r\n    this.classesService.getClassById(id).subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          const classData = response.class;\r\n          console.log('Loading class data:', classData); // Debug log\r\n\r\n          // First load departments for the program\r\n          this.loadDepartmentsByProgram(classData.program._id);\r\n\r\n          // Load subjects by department\r\n          this.loadSubjectsByDepartment(classData.department._id);\r\n\r\n          // Load teachers by program\r\n          this.filterTeachersByProgram(classData.program._id);\r\n\r\n          // Then patch the form values\r\n          this.classForm.patchValue({\r\n            section: classData.section,\r\n            program: classData.program._id,\r\n            department: classData.department._id,\r\n            semester: classData.semester.toString(),\r\n            academicYear: classData.academicYear,\r\n            maxStudents: classData.maxStudents\r\n          });\r\n\r\n          // Load subjects and teachers after a delay to ensure data is loaded\r\n          setTimeout(() => {\r\n            this.loadSubjectsAndTeachers(classData);\r\n            this.generateClassName();\r\n          }, 1500); // Increased delay to ensure all data is loaded\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading class data:', error);\r\n        Swal.fire('Error', 'Failed to load class data', 'error');\r\n      }\r\n    });\r\n  }\r\n\r\n  private loadSubjectsAndTeachers(classData: any): void {\r\n    const subjectsArray = this.classForm.get('subjects') as FormArray;\r\n    subjectsArray.clear();\r\n\r\n    if (classData.subjects && classData.subjects.length > 0) {\r\n      console.log('Loading subjects and teachers:', classData.subjects); // Debug log\r\n      classData.subjects.forEach((subjectTeacher: any) => {\r\n        const subjectId = subjectTeacher.subject?._id || subjectTeacher.subject;\r\n        const teacherId = subjectTeacher.teacher?._id || subjectTeacher.teacher;\r\n\r\n        subjectsArray.push(this.fb.group({\r\n          subject: [subjectId, Validators.required],\r\n          teacher: [teacherId, Validators.required]\r\n        }));\r\n      });\r\n      console.log('Subjects form array after loading:', subjectsArray.value); // Debug log\r\n    }\r\n  }\r\n  onSubjectTeacherSelect(subjectId: string, event: any) {\r\n    const teacherId = event.target.value; // Get the selected teacher ID from the event\r\n\r\n    const subjectsArray = this.classForm.get('subjects') as FormArray;\r\n  \r\n    const existingIndex = subjectsArray.controls.findIndex(control =>\r\n      control.value.subject === subjectId\r\n    );\r\n  \r\n    if (existingIndex !== -1) {\r\n      subjectsArray.at(existingIndex).patchValue({ teacher: teacherId });\r\n    } else {\r\n      subjectsArray.push(this.fb.group({\r\n        subject: [subjectId],\r\n        teacher: [teacherId]\r\n      }));\r\n    }\r\n  }\r\n  \r\n  addSubjectTeacher(): void {\r\n    const subjectsArray = this.classForm.get('subjects') as FormArray;\r\n    subjectsArray.push(this.fb.group({\r\n      subject: ['', Validators.required],\r\n      teacher: ['', Validators.required]\r\n    }));\r\n  }\r\n\r\n  removeSubjectTeacher(index: number): void {\r\n    const subjectsArray = this.classForm.get('subjects') as FormArray;\r\n    subjectsArray.removeAt(index);\r\n  }\r\n\r\n  // Handle checkbox change for subjects (legacy method - keeping for compatibility)\r\n  onSubjectChange(event: any) {\r\n    // This method is kept for backward compatibility but not actively used\r\n    // The main subject/teacher assignment is handled through addSubjectTeacher/removeSubjectTeacher\r\n    console.log('Legacy subject change method called:', event.target.value);\r\n  }\r\n\r\n  // Submit the form to add a new class\r\n  onSubmit(): void {\r\n    // Custom validation based on program type\r\n    const formData = this.classForm.value;\r\n    const isIntermediate = this.isIntermediateProgram();\r\n\r\n    // Check required fields\r\n    if (!formData.program || !formData.department || !formData.semester || !formData.academicYear) {\r\n      this.markFormGroupTouched();\r\n      Swal.fire('Validation Error', 'Please fill in all required fields correctly.', 'warning');\r\n      return;\r\n    }\r\n\r\n    // For Intermediate programs, section is required\r\n    if (isIntermediate && !formData.section) {\r\n      this.classForm.get('section')?.markAsTouched();\r\n      Swal.fire('Validation Error', 'Section is required for Intermediate programs.', 'warning');\r\n      return;\r\n    }\r\n\r\n    if (!this.generatedClassName) {\r\n      Swal.fire('Error', 'Please complete all selections to generate a class name.', 'warning');\r\n      return;\r\n    }\r\n\r\n    // Validate subjects if any are added\r\n    const subjectsArray = this.classForm.get('subjects') as FormArray;\r\n    if (subjectsArray.length > 0) {\r\n      for (let i = 0; i < subjectsArray.length; i++) {\r\n        const subjectGroup = subjectsArray.at(i) as FormGroup;\r\n        if (!subjectGroup.get('subject')?.value || !subjectGroup.get('teacher')?.value) {\r\n          Swal.fire('Validation Error', 'Please select both subject and teacher for all added entries.', 'warning');\r\n          return;\r\n        }\r\n      }\r\n    }\r\n\r\n    this.isLoading = true;\r\n    const classData = {\r\n      ...this.classForm.value,\r\n      className: this.generatedClassName,\r\n      semester: parseInt(this.classForm.value.semester),\r\n      section: isIntermediate ? formData.section : '' // Set section to empty string for BS programs\r\n    };\r\n\r\n    console.log('Submitting class data:', classData); // Debug log\r\n\r\n    const request = this.editingClassId\r\n      ? this.classesService.updateClass(this.editingClassId, classData)\r\n      : this.classesService.createClass(classData);\r\n\r\n    request.subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.isLoading = false;\r\n          Swal.fire({\r\n            title: this.editingClassId ? 'Class updated successfully' : 'Class created successfully',\r\n            icon: 'success',\r\n            confirmButtonColor: '#3085d6',\r\n            timer: 1500\r\n          }).then(() => {\r\n            this.router.navigate(['/dashboard/admin/classes']);\r\n          });\r\n        } else {\r\n          this.isLoading = false;\r\n          Swal.fire('Error', response.message || 'Failed to save class', 'error');\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.isLoading = false;\r\n        console.error('Error saving class:', error);\r\n        const errorMessage = error.error?.message || 'Something went wrong while saving the class';\r\n        Swal.fire('Error', errorMessage, 'error');\r\n      }\r\n    });\r\n  }\r\n\r\n  private markFormGroupTouched(): void {\r\n    Object.keys(this.classForm.controls).forEach(key => {\r\n      const control = this.classForm.get(key);\r\n      control?.markAsTouched();\r\n    });\r\n\r\n    // Mark subjects array controls as touched\r\n    const subjectsArray = this.classForm.get('subjects') as FormArray;\r\n    subjectsArray.controls.forEach(group => {\r\n      Object.keys((group as FormGroup).controls).forEach(key => {\r\n        group.get(key)?.markAsTouched();\r\n      });\r\n    });\r\n  }\r\n\r\n  // Navigate back\r\n  goBack() {\r\n    this.router.navigate(['/dashboard/admin/classes']);\r\n  }\r\n}\r\n  \r\n\r\n", "<div class=\"form-container\">\r\n    <div class=\"form-header d-flex justify-content-between align-items-center mb-3\">\r\n        <h2>{{ editingClassId ? 'Update Class' : 'Add New Class' }}</h2>\r\n        <div class=\"d-flex gap-2\">\r\n          <button class=\"btn-back\" (click)=\"goBack()\">\r\n            <mat-icon>arrow_back</mat-icon> Back\r\n          </button>   \r\n          <button class=\"btn btn-primary btn-save\" [disabled]=\"classForm.invalid || isLoading\" (click)=\"onSubmit()\">\r\n            <ng-container *ngIf=\"!isLoading; else loading\">\r\n              <mat-icon>save</mat-icon> Save\r\n            </ng-container>\r\n            <ng-template #loading>\r\n              <span class=\"spinner-border spinner-border-sm text-light me-1\" role=\"status\" aria-hidden=\"true\"></span>\r\n              Saving...\r\n            </ng-template>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    \r\n    <form [formGroup]=\"classForm\">\r\n        <div class=\"form-grid\">\r\n            <div class=\"form-group mb-3\">\r\n                <label for=\"program\">Program *</label>\r\n                <select class=\"form-control\" formControlName=\"program\">\r\n                    <option value=\"\">Select Program</option>\r\n                    <option *ngFor=\"let program of programs\" [value]=\"program._id\">\r\n                        {{ program.name }} - {{ program.fullName }}\r\n                    </option>\r\n                </select>\r\n                <div *ngIf=\"classForm.get('program')?.touched && classForm.get('program')?.invalid\" class=\"text-danger\">\r\n                    Program selection is required\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"form-group mb-3\">\r\n                <label for=\"department\">Department *</label>\r\n                <select class=\"form-control\" formControlName=\"department\" [disabled]=\"!classForm.get('program')?.value\">\r\n                    <option value=\"\">{{ !classForm.get('program')?.value ? 'Select Program First' : 'Select Department' }}</option>\r\n                    <option *ngFor=\"let dept of filteredDepartments\" [value]=\"dept._id\">\r\n                        {{ dept.name }}\r\n                    </option>\r\n                </select>\r\n                <div *ngIf=\"classForm.get('department')?.touched && classForm.get('department')?.invalid\" class=\"text-danger\">\r\n                    Department selection is required\r\n                </div>\r\n                <div *ngIf=\"classForm.get('program')?.value && filteredDepartments.length === 0\" class=\"text-info\">\r\n                    No departments available for the selected program\r\n                </div>\r\n            </div>\r\n\r\n            <!-- Section field - Only for Intermediate Programs -->\r\n            <div class=\"form-group mb-3\" *ngIf=\"isIntermediateProgram()\">\r\n                <label for=\"section\">Section *</label>\r\n                <input type=\"text\" class=\"form-control\" placeholder=\"e.g., A, B, C\" formControlName=\"section\">\r\n                <div *ngIf=\"classForm.get('section')?.touched && classForm.get('section')?.invalid\" class=\"text-danger\">\r\n                    Section is required\r\n                </div>\r\n                <small class=\"form-text text-muted\">\r\n                    <mat-icon class=\"small-icon\">info</mat-icon>\r\n                    Sections are used for Intermediate programs to group students\r\n                </small>\r\n            </div>\r\n\r\n           \r\n\r\n            <div class=\"form-group mb-3\">\r\n                <label for=\"semester\">{{ isIntermediateProgram() ? 'Academic Year *' : 'Semester *' }}</label>\r\n                <select class=\"form-control\" formControlName=\"semester\">\r\n                    <option value=\"\">{{ isIntermediateProgram() ? 'Select Year' : 'Select Semester' }}</option>\r\n\r\n                    <!-- For Intermediate Programs (Year-based) -->\r\n                    <ng-container *ngIf=\"isIntermediateProgram()\">\r\n                        <option value=\"1\">1st Year</option>\r\n                        <option value=\"2\">2nd Year</option>\r\n                    </ng-container>\r\n\r\n                    <!-- For BS/MS Programs (Semester-based) -->\r\n                    <ng-container *ngIf=\"!isIntermediateProgram()\">\r\n                        <option value=\"1\">1st Semester</option>\r\n                        <option value=\"2\">2nd Semester</option>\r\n                        <option value=\"3\">3rd Semester</option>\r\n                        <option value=\"4\">4th Semester</option>\r\n                        <option value=\"5\">5th Semester</option>\r\n                        <option value=\"6\">6th Semester</option>\r\n                        <option value=\"7\">7th Semester</option>\r\n                        <option value=\"8\">8th Semester</option>\r\n                    </ng-container>\r\n                </select>\r\n                <div *ngIf=\"classForm.get('semester')?.touched && classForm.get('semester')?.invalid\" class=\"text-danger\">\r\n                    {{ isIntermediateProgram() ? 'Year' : 'Semester' }} selection is required\r\n                </div>\r\n\r\n                <!-- Info message for program type -->\r\n                <small class=\"form-text text-muted\" *ngIf=\"isIntermediateProgram()\">\r\n                    <mat-icon class=\"small-icon\">info</mat-icon>\r\n                    Intermediate programs are year-based (1st Year, 2nd Year)\r\n                </small>\r\n            </div>\r\n\r\n            <!-- Auto-generated Class Name Display -->\r\n            <div class=\"form-group mb-3\" *ngIf=\"generatedClassName\">\r\n                <label>Generated Class Name</label>\r\n                <div class=\"form-control-plaintext bg-light p-2 rounded\">\r\n                    <strong>{{ generatedClassName }}</strong>\r\n                </div>\r\n                <small class=\"form-text text-muted\">\r\n                    <mat-icon class=\"small-icon\">info</mat-icon>\r\n                    Class name is automatically generated based on your selections\r\n                </small>\r\n            </div>\r\n\r\n            <div class=\"form-group mb-3\">\r\n                <label for=\"academicYear\">Academic Year *</label>\r\n                <input type=\"text\" class=\"form-control\" placeholder=\"e.g., 2024-2025\" formControlName=\"academicYear\">\r\n                <div *ngIf=\"classForm.get('academicYear')?.touched && classForm.get('academicYear')?.invalid\" class=\"text-danger\">\r\n                    Academic year is required\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"form-group mb-3\">\r\n                <label for=\"maxStudents\">Maximum Students *</label>\r\n                <input type=\"number\" class=\"form-control\" placeholder=\"50\" formControlName=\"maxStudents\" min=\"1\">\r\n                <div *ngIf=\"classForm.get('maxStudents')?.touched && classForm.get('maxStudents')?.invalid\" class=\"text-danger\">\r\n                    Maximum students is required\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- Subjects and Teachers Section -->\r\n        <div class=\"subjects-section mt-4\">\r\n            <div class=\"d-flex justify-content-between align-items-center mb-3\">\r\n                <h4>Subjects & Teachers</h4>\r\n                <button type=\"button\" class=\"btn btn-outline-primary\" (click)=\"addSubjectTeacher()\">\r\n                    <mat-icon>add</mat-icon> Add Subject\r\n                </button>\r\n            </div>\r\n\r\n            <div formArrayName=\"subjects\">\r\n                <div *ngFor=\"let subjectGroup of subjectsFormArray.controls; let i = index\"\r\n                     [formGroupName]=\"i\" class=\"subject-teacher-row mb-3\">\r\n                    <div class=\"row\">\r\n                        <div class=\"col-md-5\">\r\n                            <label>Subject *</label>\r\n                            <select class=\"form-control\" formControlName=\"subject\"\r\n                                    [class.is-invalid]=\"subjectGroup.get('subject')?.touched && subjectGroup.get('subject')?.invalid\">\r\n                                <option value=\"\">Select Subject</option>\r\n                                <option *ngFor=\"let subject of filteredSubjects\" [value]=\"subject._id\">\r\n                                    {{ subject.subjectName }} ({{ subject.code }})\r\n                                </option>\r\n                            </select>\r\n                            <div *ngIf=\"subjectGroup.get('subject')?.touched && subjectGroup.get('subject')?.invalid\" class=\"text-danger\">\r\n                                Subject selection is required\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"col-md-5\">\r\n                            <label>Teacher *</label>\r\n                            <select class=\"form-control\" formControlName=\"teacher\"\r\n                                    [class.is-invalid]=\"subjectGroup.get('teacher')?.touched && subjectGroup.get('teacher')?.invalid\">\r\n                                <option value=\"\">Select Teacher</option>\r\n                                <option *ngFor=\"let teacher of teachers\" [value]=\"teacher._id\">\r\n                                    {{ teacher.name }} - {{ teacher.designation || 'Teacher' }}\r\n                                </option>\r\n                            </select>\r\n                            <div *ngIf=\"subjectGroup.get('teacher')?.touched && subjectGroup.get('teacher')?.invalid\" class=\"text-danger\">\r\n                                Teacher selection is required\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"col-md-2 d-flex align-items-end\">\r\n                            <button type=\"button\" class=\"btn btn-outline-danger\" (click)=\"removeSubjectTeacher(i)\">\r\n                                <mat-icon>delete</mat-icon>\r\n                            </button>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div *ngIf=\"subjectsFormArray.length === 0\" class=\"text-muted text-center py-3\">\r\n                No subjects added yet. Click \"Add Subject\" to assign subjects and teachers to this class.\r\n            </div>\r\n\r\n            <!-- Debug Information (only in edit mode) -->\r\n            <div *ngIf=\"editingClassId && subjectsFormArray.length > 0\" class=\"mt-3\">\r\n                <small class=\"text-info\">\r\n                    <strong>Current Assignments:</strong> {{ subjectsFormArray.length }} subject(s) assigned\r\n                </small>\r\n            </div>\r\n        </div>\r\n\r\n    </form>\r\n</div>"], "mappings": "AACA,SAA4CA,UAAU,QAAQ,gBAAgB;AAQ9E,OAAOC,IAAI,MAAM,aAAa,CAAC,CAAC;;;;;;;;;;;;;ICDpBC,EAAA,CAAAC,uBAAA,GAA+C;IAC7CD,EAAA,CAAAE,cAAA,eAAU;IAAAF,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAACJ,EAAA,CAAAG,MAAA,aAC5B;IAAAH,EAAA,CAAAK,qBAAA,EAAe;;;;;IAEbL,EAAA,CAAAM,SAAA,eAAuG;IACvGN,EAAA,CAAAG,MAAA,kBACF;;;;;IAWQH,EAAA,CAAAE,cAAA,iBAA+D;IAC3DF,EAAA,CAAAG,MAAA,GACJ;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAFgCJ,EAAA,CAAAO,UAAA,UAAAC,WAAA,CAAAC,GAAA,CAAqB;IAC1DT,EAAA,CAAAU,SAAA,GACJ;IADIV,EAAA,CAAAW,kBAAA,MAAAH,WAAA,CAAAI,IAAA,SAAAJ,WAAA,CAAAK,QAAA,MACJ;;;;;IAEJb,EAAA,CAAAE,cAAA,cAAwG;IACpGF,EAAA,CAAAG,MAAA,sCACJ;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAOFJ,EAAA,CAAAE,cAAA,iBAAoE;IAChEF,EAAA,CAAAG,MAAA,GACJ;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAFwCJ,EAAA,CAAAO,UAAA,UAAAO,QAAA,CAAAL,GAAA,CAAkB;IAC/DT,EAAA,CAAAU,SAAA,GACJ;IADIV,EAAA,CAAAe,kBAAA,MAAAD,QAAA,CAAAF,IAAA,MACJ;;;;;IAEJZ,EAAA,CAAAE,cAAA,cAA8G;IAC1GF,EAAA,CAAAG,MAAA,yCACJ;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IACNJ,EAAA,CAAAE,cAAA,cAAmG;IAC/FF,EAAA,CAAAG,MAAA,0DACJ;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAONJ,EAAA,CAAAE,cAAA,cAAwG;IACpGF,EAAA,CAAAG,MAAA,4BACJ;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IALVJ,EAAA,CAAAE,cAAA,aAA6D;IACpCF,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACtCJ,EAAA,CAAAM,SAAA,gBAA8F;IAC9FN,EAAA,CAAAgB,UAAA,IAAAC,wCAAA,kBAEM;IACNjB,EAAA,CAAAE,cAAA,gBAAoC;IACHF,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC5CJ,EAAA,CAAAG,MAAA,sEACJ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;;;;;IANFJ,EAAA,CAAAU,SAAA,GAA4E;IAA5EV,EAAA,CAAAO,UAAA,WAAAW,OAAA,GAAAC,MAAA,CAAAC,SAAA,CAAAC,GAAA,8BAAAH,OAAA,CAAAI,OAAA,OAAAJ,OAAA,GAAAC,MAAA,CAAAC,SAAA,CAAAC,GAAA,8BAAAH,OAAA,CAAAK,OAAA,EAA4E;;;;;IAiB9EvB,EAAA,CAAAC,uBAAA,GAA8C;IAC1CD,EAAA,CAAAE,cAAA,iBAAkB;IAAAF,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACnCJ,EAAA,CAAAE,cAAA,iBAAkB;IAAAF,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACvCJ,EAAA,CAAAK,qBAAA,EAAe;;;;;IAGfL,EAAA,CAAAC,uBAAA,GAA+C;IAC3CD,EAAA,CAAAE,cAAA,iBAAkB;IAAAF,EAAA,CAAAG,MAAA,mBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACvCJ,EAAA,CAAAE,cAAA,iBAAkB;IAAAF,EAAA,CAAAG,MAAA,mBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACvCJ,EAAA,CAAAE,cAAA,iBAAkB;IAAAF,EAAA,CAAAG,MAAA,mBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACvCJ,EAAA,CAAAE,cAAA,iBAAkB;IAAAF,EAAA,CAAAG,MAAA,mBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACvCJ,EAAA,CAAAE,cAAA,iBAAkB;IAAAF,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACvCJ,EAAA,CAAAE,cAAA,kBAAkB;IAAAF,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACvCJ,EAAA,CAAAE,cAAA,kBAAkB;IAAAF,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACvCJ,EAAA,CAAAE,cAAA,kBAAkB;IAAAF,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAC3CJ,EAAA,CAAAK,qBAAA,EAAe;;;;;IAEnBL,EAAA,CAAAE,cAAA,cAA0G;IACtGF,EAAA,CAAAG,MAAA,GACJ;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADFJ,EAAA,CAAAU,SAAA,GACJ;IADIV,EAAA,CAAAe,kBAAA,MAAAS,OAAA,CAAAC,qBAAA,oDACJ;;;;;IAGAzB,EAAA,CAAAE,cAAA,gBAAoE;IACnCF,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC5CJ,EAAA,CAAAG,MAAA,kEACJ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;;;;;IAIZJ,EAAA,CAAAE,cAAA,aAAwD;IAC7CF,EAAA,CAAAG,MAAA,2BAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACnCJ,EAAA,CAAAE,cAAA,cAAyD;IAC7CF,EAAA,CAAAG,MAAA,GAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAE7CJ,EAAA,CAAAE,cAAA,gBAAoC;IACHF,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC5CJ,EAAA,CAAAG,MAAA,uEACJ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;;;;IALIJ,EAAA,CAAAU,SAAA,GAAwB;IAAxBV,EAAA,CAAA0B,iBAAA,CAAAC,OAAA,CAAAC,kBAAA,CAAwB;;;;;IAWpC5B,EAAA,CAAAE,cAAA,cAAkH;IAC9GF,EAAA,CAAAG,MAAA,kCACJ;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAMNJ,EAAA,CAAAE,cAAA,cAAgH;IAC5GF,EAAA,CAAAG,MAAA,qCACJ;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAsBUJ,EAAA,CAAAE,cAAA,iBAAuE;IACnEF,EAAA,CAAAG,MAAA,GACJ;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAFwCJ,EAAA,CAAAO,UAAA,UAAAsB,WAAA,CAAApB,GAAA,CAAqB;IAClET,EAAA,CAAAU,SAAA,GACJ;IADIV,EAAA,CAAAW,kBAAA,MAAAkB,WAAA,CAAAC,WAAA,QAAAD,WAAA,CAAAE,IAAA,OACJ;;;;;IAEJ/B,EAAA,CAAAE,cAAA,cAA8G;IAC1GF,EAAA,CAAAG,MAAA,sCACJ;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAOFJ,EAAA,CAAAE,cAAA,iBAA+D;IAC3DF,EAAA,CAAAG,MAAA,GACJ;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAFgCJ,EAAA,CAAAO,UAAA,UAAAyB,WAAA,CAAAvB,GAAA,CAAqB;IAC1DT,EAAA,CAAAU,SAAA,GACJ;IADIV,EAAA,CAAAW,kBAAA,MAAAqB,WAAA,CAAApB,IAAA,SAAAoB,WAAA,CAAAC,WAAA,mBACJ;;;;;IAEJjC,EAAA,CAAAE,cAAA,cAA8G;IAC1GF,EAAA,CAAAG,MAAA,sCACJ;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;;IA3BlBJ,EAAA,CAAAE,cAAA,cAC0D;IAGvCF,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACxBJ,EAAA,CAAAE,cAAA,iBAC0G;IACrFF,EAAA,CAAAG,MAAA,qBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACxCJ,EAAA,CAAAgB,UAAA,IAAAkB,2CAAA,qBAES;IACblC,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAgB,UAAA,IAAAmB,wCAAA,kBAEM;IACVnC,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,eAAsB;IACXF,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACxBJ,EAAA,CAAAE,cAAA,kBAC0G;IACrFF,EAAA,CAAAG,MAAA,sBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACxCJ,EAAA,CAAAgB,UAAA,KAAAoB,4CAAA,qBAES;IACbpC,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAgB,UAAA,KAAAqB,yCAAA,kBAEM;IACVrC,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,eAA6C;IACYF,EAAA,CAAAsC,UAAA,mBAAAC,4DAAA;MAAA,MAAAC,WAAA,GAAAxC,EAAA,CAAAyC,aAAA,CAAAC,IAAA;MAAA,MAAAC,KAAA,GAAAH,WAAA,CAAAI,KAAA;MAAA,MAAAC,OAAA,GAAA7C,EAAA,CAAA8C,aAAA;MAAA,OAAS9C,EAAA,CAAA+C,WAAA,CAAAF,OAAA,CAAAG,oBAAA,CAAAL,KAAA,CAAuB;IAAA,EAAC;IAClF3C,EAAA,CAAAE,cAAA,gBAAU;IAAAF,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAW;;;;;;;;;;IA9BtCJ,EAAA,CAAAO,UAAA,kBAAAoC,KAAA,CAAmB;IAKJ3C,EAAA,CAAAU,SAAA,GAAiG;IAAjGV,EAAA,CAAAiD,WAAA,iBAAAC,OAAA,GAAAC,gBAAA,CAAA9B,GAAA,8BAAA6B,OAAA,CAAA5B,OAAA,OAAA4B,OAAA,GAAAC,gBAAA,CAAA9B,GAAA,8BAAA6B,OAAA,CAAA3B,OAAA,EAAiG;IAEzEvB,EAAA,CAAAU,SAAA,GAAmB;IAAnBV,EAAA,CAAAO,UAAA,YAAA6C,OAAA,CAAAC,gBAAA,CAAmB;IAI7CrD,EAAA,CAAAU,SAAA,GAAkF;IAAlFV,EAAA,CAAAO,UAAA,WAAA+C,OAAA,GAAAH,gBAAA,CAAA9B,GAAA,8BAAAiC,OAAA,CAAAhC,OAAA,OAAAgC,OAAA,GAAAH,gBAAA,CAAA9B,GAAA,8BAAAiC,OAAA,CAAA/B,OAAA,EAAkF;IAOhFvB,EAAA,CAAAU,SAAA,GAAiG;IAAjGV,EAAA,CAAAiD,WAAA,iBAAAM,OAAA,GAAAJ,gBAAA,CAAA9B,GAAA,8BAAAkC,OAAA,CAAAjC,OAAA,OAAAiC,OAAA,GAAAJ,gBAAA,CAAA9B,GAAA,8BAAAkC,OAAA,CAAAhC,OAAA,EAAiG;IAEzEvB,EAAA,CAAAU,SAAA,GAAW;IAAXV,EAAA,CAAAO,UAAA,YAAA6C,OAAA,CAAAI,QAAA,CAAW;IAIrCxD,EAAA,CAAAU,SAAA,GAAkF;IAAlFV,EAAA,CAAAO,UAAA,WAAAkD,OAAA,GAAAN,gBAAA,CAAA9B,GAAA,8BAAAoC,OAAA,CAAAnC,OAAA,OAAAmC,OAAA,GAAAN,gBAAA,CAAA9B,GAAA,8BAAAoC,OAAA,CAAAlC,OAAA,EAAkF;;;;;IAaxGvB,EAAA,CAAAE,cAAA,cAAgF;IAC5EF,EAAA,CAAAG,MAAA,oGACJ;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAGNJ,EAAA,CAAAE,cAAA,cAAyE;IAEzDF,EAAA,CAAAG,MAAA,2BAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAACJ,EAAA,CAAAG,MAAA,GAC1C;IAAAH,EAAA,CAAAI,YAAA,EAAQ;;;;IADkCJ,EAAA,CAAAU,SAAA,GAC1C;IAD0CV,EAAA,CAAAe,kBAAA,MAAA2C,OAAA,CAAAC,iBAAA,CAAAC,MAAA,0BAC1C;;;ADxKhB,OAAM,MAAOC,kBAAkB;EAY7BC,YACUC,cAA8B,EAC/BC,cAA8B,EAC7BC,EAAe,EACfC,MAAc,EACdC,cAA8B,EAC9BC,iBAAoC,EACpCC,cAA8B,EAC9BC,WAAwB;IAPxB,KAAAP,cAAc,GAAdA,cAAc;IACf,KAAAC,cAAc,GAAdA,cAAc;IACb,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IAlBrB,KAAAC,QAAQ,GAAc,EAAE;IACxB,KAAAf,QAAQ,GAAW,EAAE;IACrB,KAAAgB,QAAQ,GAAc,EAAE;IACxB,KAAAC,WAAW,GAAiB,EAAE;IAC9B,KAAAC,mBAAmB,GAAiB,EAAE;IACtC,KAAArB,gBAAgB,GAAc,EAAE;IAEhC,KAAAsB,SAAS,GAAG,KAAK;IACjB,KAAA/C,kBAAkB,GAAG,EAAE;IAYrB,IAAI,CAACR,SAAS,GAAG,IAAI,CAAC6C,EAAE,CAACW,KAAK,CAAC;MAC7BC,OAAO,EAAE,CAAC,EAAE,CAAC;MACbC,OAAO,EAAE,CAAC,EAAE,EAAEhF,UAAU,CAACiF,QAAQ,CAAC;MAClCC,UAAU,EAAE,CAAC,EAAE,EAAElF,UAAU,CAACiF,QAAQ,CAAC;MACrCE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACnF,UAAU,CAACiF,QAAQ,EAAEjF,UAAU,CAACoF,GAAG,CAAC,CAAC,CAAC,EAAEpF,UAAU,CAACqF,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3EC,YAAY,EAAE,CAAC,EAAE,EAAEtF,UAAU,CAACiF,QAAQ,CAAC;MACvCM,WAAW,EAAE,CAAC,EAAE,EAAE,CAACvF,UAAU,CAACiF,QAAQ,EAAEjF,UAAU,CAACoF,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3DX,QAAQ,EAAE,IAAI,CAACN,EAAE,CAACqB,KAAK,CAAC,EAAE,CAAC,CAAC;KAC7B,CAAC;EACJ;;EACAC,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,GAAG,IAAI,CAACxB,cAAc,CAACyB,QAAQ,CAACC,QAAQ,CAACrE,GAAG,CAAC,IAAI,CAAC;IACrE,IAAI,CAACsE,eAAe,EAAE;IACtB,IAAI,CAACC,sBAAsB,EAAE;IAC7B,IAAI,IAAI,CAACJ,cAAc,EAAE;MACvB,IAAI,CAACK,aAAa,CAAC,IAAI,CAACL,cAAc,CAAC;;EAE3C;EAEAG,eAAeA,CAAA;IACb,IAAI,CAACG,YAAY,EAAE;IACnB,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAH,YAAYA,CAAA;IACV,IAAI,CAAC3B,cAAc,CAAC+B,cAAc,CAAC,IAAI,CAAC,CAACC,SAAS,CAAC;MACjDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAAC9B,QAAQ,GAAG6B,QAAQ,CAAC7B,QAAQ;;MAErC,CAAC;MACD+B,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/CxG,IAAI,CAAC0G,IAAI,CAAC,OAAO,EAAE,yBAAyB,EAAE,OAAO,CAAC;MACxD;KACD,CAAC;EACJ;EAEAV,eAAeA,CAAA;IACb;IACA,IAAI,CAACtB,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,mBAAmB,GAAG,EAAE;EAC/B;EAEAsB,YAAYA,CAAA;IACV,IAAI,CAAC1B,WAAW,CAACoC,cAAc,CAAC,SAAS,CAAC,CAACP,SAAS,CAAC;MACnDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAAC9C,QAAQ,GAAG6C,QAAQ,CAACM,KAAK;;MAElC,CAAC;MACDJ,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/CxG,IAAI,CAAC0G,IAAI,CAAC,OAAO,EAAE,yBAAyB,EAAE,OAAO,CAAC;MACxD;KACD,CAAC;EACJ;EAEAR,YAAYA,CAAA;IACV,IAAI,CAAC5B,cAAc,CAACuC,cAAc,EAAE,CAACT,SAAS,CAAC;MAC7CC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAAC/B,QAAQ,GAAG8B,QAAQ,CAAC9B,QAAQ;UACjC,IAAI,CAAClB,gBAAgB,GAAG,IAAI,CAACkB,QAAQ;;MAEzC,CAAC;MACDgC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/CxG,IAAI,CAAC0G,IAAI,CAAC,OAAO,EAAE,yBAAyB,EAAE,OAAO,CAAC;MACxD;KACD,CAAC;EACJ;EAEAb,sBAAsBA,CAAA;IACpB;IACA,IAAI,CAACxE,SAAS,CAACC,GAAG,CAAC,SAAS,CAAC,EAAEwF,YAAY,CAACV,SAAS,CAACW,SAAS,IAAG;MAChE,IAAIA,SAAS,EAAE;QACb;QACA,IAAI,CAACC,wBAAwB,CAACD,SAAS,CAAC;QAExC;QACA,IAAI,CAAC1F,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC,EAAE2F,QAAQ,CAAC,EAAE,CAAC;QAE9C;QACA,MAAMC,eAAe,GAAG,IAAI,CAACzC,QAAQ,CAAC0C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1G,GAAG,KAAKqG,SAAS,CAAC;QACpE,IAAI,CAACM,uBAAuB,CAACH,eAAe,CAAC;QAE7C;QACA,IAAI,CAACI,uBAAuB,CAACP,SAAS,CAAC;QAEvC;QACA,IAAI,CAACQ,iBAAiB,EAAE;OACzB,MAAM;QACL,IAAI,CAAC5C,mBAAmB,GAAG,EAAE;QAC7B,IAAI,CAACrB,gBAAgB,GAAG,EAAE;QAC1B,IAAI,CAACzB,kBAAkB,GAAG,EAAE;QAC5B,IAAI,CAACR,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC,EAAE2F,QAAQ,CAAC,EAAE,CAAC;;IAElD,CAAC,CAAC;IAEF;IACA,IAAI,CAAC5F,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC,EAAEwF,YAAY,CAACV,SAAS,CAACoB,YAAY,IAAG;MACtE,IAAIA,YAAY,EAAE;QAChB,IAAI,CAACC,wBAAwB,CAACD,YAAY,CAAC;QAC3C,IAAI,CAACD,iBAAiB,EAAE;OACzB,MAAM;QACL,IAAI,CAACjE,gBAAgB,GAAG,EAAE;;IAE9B,CAAC,CAAC;IAEF;IACA,IAAI,CAACjC,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEwF,YAAY,CAACV,SAAS,CAAC,MAAK;MAC1D,IAAI,CAACmB,iBAAiB,EAAE;IAC1B,CAAC,CAAC;IAEF;IACA,IAAI,CAAClG,SAAS,CAACC,GAAG,CAAC,SAAS,CAAC,EAAEwF,YAAY,CAACV,SAAS,CAAC,MAAK;MACzD,IAAI,CAACmB,iBAAiB,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAP,wBAAwBA,CAACD,SAAiB;IACxC,IAAI,CAAC1C,iBAAiB,CAACqD,uBAAuB,CAACX,SAAS,EAAE,IAAI,CAAC,CAACX,SAAS,CAAC;MACxEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAAC5B,mBAAmB,GAAG2B,QAAQ,CAAC5B,WAAW;;MAEnD,CAAC;MACD8B,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7DxG,IAAI,CAAC0G,IAAI,CAAC,OAAO,EAAE,iDAAiD,EAAE,OAAO,CAAC;QAC9E,IAAI,CAAC/B,mBAAmB,GAAG,EAAE;MAC/B;KACD,CAAC;EACJ;EAEA8C,wBAAwBA,CAACD,YAAoB;IAC3C,IAAI,CAAClD,cAAc,CAACqD,uBAAuB,CAACH,YAAY,EAAE,IAAI,CAAC,CAACpB,SAAS,CAAC;MACxEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACjD,gBAAgB,GAAGgD,QAAQ,CAAC9B,QAAQ;;MAE7C,CAAC;MACDgC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7D,IAAI,CAAClD,gBAAgB,GAAG,EAAE;MAC5B;KACD,CAAC;EACJ;EAEAiE,iBAAiBA,CAAA;IACf,MAAMK,QAAQ,GAAG,IAAI,CAACvG,SAAS,CAACwG,KAAK;IACrC,MAAMX,eAAe,GAAG,IAAI,CAACzC,QAAQ,CAAC0C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1G,GAAG,KAAKkH,QAAQ,CAAC7C,OAAO,CAAC;IAC3E,MAAM+C,kBAAkB,GAAG,IAAI,CAACnD,mBAAmB,CAACwC,IAAI,CAACY,CAAC,IAAIA,CAAC,CAACrH,GAAG,KAAKkH,QAAQ,CAAC3C,UAAU,CAAC;IAE5F,IAAIiC,eAAe,IAAIY,kBAAkB,IAAIF,QAAQ,CAAC1C,QAAQ,EAAE;MAC9D,IAAI8C,YAAY,GAAG,EAAE;MAErB,IAAId,eAAe,CAACrG,IAAI,KAAK,cAAc,EAAE;QAC3C;QACA,IAAI,CAAC+G,QAAQ,CAAC9C,OAAO,EAAE;UACrB,IAAI,CAACjD,kBAAkB,GAAG,EAAE;UAC5B;;QAGF,QAAQ+F,QAAQ,CAAC1C,QAAQ;UACvB,KAAK,GAAG;YAAE8C,YAAY,GAAG,UAAU;YAAE;UACrC,KAAK,GAAG;YAAEA,YAAY,GAAG,UAAU;YAAE;UACrC;YAASA,YAAY,GAAG,GAAGJ,QAAQ,CAAC1C,QAAQ,OAAO;;QAGrD,IAAI,CAACrD,kBAAkB,GAAG,GAAGmG,YAAY,cAAcJ,QAAQ,CAAC9C,OAAO,EAAE;OAC1E,MAAM;QACL;QACA,QAAQ8C,QAAQ,CAAC1C,QAAQ;UACvB,KAAK,GAAG;YAAE8C,YAAY,GAAG,cAAc;YAAE;UACzC,KAAK,GAAG;YAAEA,YAAY,GAAG,cAAc;YAAE;UACzC,KAAK,GAAG;YAAEA,YAAY,GAAG,cAAc;YAAE;UACzC,KAAK,GAAG;YAAEA,YAAY,GAAG,cAAc;YAAE;UACzC,KAAK,GAAG;YAAEA,YAAY,GAAG,cAAc;YAAE;UACzC,KAAK,GAAG;YAAEA,YAAY,GAAG,cAAc;YAAE;UACzC,KAAK,GAAG;YAAEA,YAAY,GAAG,cAAc;YAAE;UACzC,KAAK,GAAG;YAAEA,YAAY,GAAG,cAAc;YAAE;UACzC;YAASA,YAAY,GAAG,GAAGJ,QAAQ,CAAC1C,QAAQ,WAAW;;QAGzD,IAAI,CAACrD,kBAAkB,GAAGmG,YAAY;;KAEzC,MAAM;MACL,IAAI,CAACnG,kBAAkB,GAAG,EAAE;;EAEhC;EAEAwF,uBAAuBA,CAACtC,OAAY;IAClC,MAAMkD,cAAc,GAAG,IAAI,CAAC5G,SAAS,CAACC,GAAG,CAAC,SAAS,CAAC;IAEpD,IAAIyD,OAAO,EAAElE,IAAI,KAAK,cAAc,EAAE;MACpC;MACAoH,cAAc,EAAEC,aAAa,CAAC,CAACnI,UAAU,CAACiF,QAAQ,CAAC,CAAC;MACpDiD,cAAc,EAAEE,sBAAsB,EAAE;MAExC;MACA,IAAI,CAAC9G,SAAS,CAAC+G,UAAU,CAAC;QACxBlD,QAAQ,EAAE,GAAG;QACbG,YAAY,EAAE,IAAIgD,IAAI,EAAE,CAACC,WAAW,EAAE,GAAG,GAAG,IAAI,IAAID,IAAI,EAAE,CAACC,WAAW,EAAE,GAAG,CAAC;OAC7E,CAAC;KACH,MAAM;MACL;MACAL,cAAc,EAAEM,eAAe,EAAE;MACjCN,cAAc,EAAEE,sBAAsB,EAAE;MACxCF,cAAc,EAAEhB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;MAE9B;MACA,IAAI,CAAC5F,SAAS,CAAC+G,UAAU,CAAC;QACxBlD,QAAQ,EAAE,GAAG;QACbG,YAAY,EAAE,IAAIgD,IAAI,EAAE,CAACC,WAAW,EAAE,GAAG,GAAG,IAAI,IAAID,IAAI,EAAE,CAACC,WAAW,EAAE,GAAG,CAAC;OAC7E,CAAC;;EAEN;EAEAhB,uBAAuBA,CAACP,SAAiB;IACvC;IACA,IAAI,CAACxC,WAAW,CAACiE,iBAAiB,CAACzB,SAAS,EAAE,SAAS,CAAC,CAACX,SAAS,CAAC;MACjEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpBE,OAAO,CAACgC,GAAG,CAAC,KAAK,EAAGnC,QAAQ,CAAC;UAC7B,IAAI,CAAC7C,QAAQ,GAAG6C,QAAQ,CAACM,KAAK;;MAElC,CAAC;MACDJ,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1D;QACA,IAAI,CAACP,YAAY,EAAE;MACrB;KACD,CAAC;EACJ;EAEAvE,qBAAqBA,CAAA;IACnB,MAAMqF,SAAS,GAAG,IAAI,CAAC1F,SAAS,CAACC,GAAG,CAAC,SAAS,CAAC,EAAEuG,KAAK;IACtD,MAAMX,eAAe,GAAG,IAAI,CAACzC,QAAQ,CAAC0C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1G,GAAG,KAAKqG,SAAS,CAAC;IACpE,OAAOG,eAAe,EAAErG,IAAI,KAAK,cAAc;EACjD;EACA,IAAI+C,iBAAiBA,CAAA;IACnB,OAAO,IAAI,CAACvC,SAAS,CAACC,GAAG,CAAC,UAAU,CAAc;EACpD;EAEAwE,aAAaA,CAAC4C,EAAU;IACtB,IAAI,CAAC1E,cAAc,CAAC2E,YAAY,CAACD,EAAE,CAAC,CAACtC,SAAS,CAAC;MAC7CC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,MAAMqC,SAAS,GAAGtC,QAAQ,CAACuC,KAAK;UAChCpC,OAAO,CAACgC,GAAG,CAAC,qBAAqB,EAAEG,SAAS,CAAC,CAAC,CAAC;UAE/C;UACA,IAAI,CAAC5B,wBAAwB,CAAC4B,SAAS,CAAC7D,OAAO,CAACrE,GAAG,CAAC;UAEpD;UACA,IAAI,CAAC+G,wBAAwB,CAACmB,SAAS,CAAC3D,UAAU,CAACvE,GAAG,CAAC;UAEvD;UACA,IAAI,CAAC4G,uBAAuB,CAACsB,SAAS,CAAC7D,OAAO,CAACrE,GAAG,CAAC;UAEnD;UACA,IAAI,CAACW,SAAS,CAAC+G,UAAU,CAAC;YACxBtD,OAAO,EAAE8D,SAAS,CAAC9D,OAAO;YAC1BC,OAAO,EAAE6D,SAAS,CAAC7D,OAAO,CAACrE,GAAG;YAC9BuE,UAAU,EAAE2D,SAAS,CAAC3D,UAAU,CAACvE,GAAG;YACpCwE,QAAQ,EAAE0D,SAAS,CAAC1D,QAAQ,CAAC4D,QAAQ,EAAE;YACvCzD,YAAY,EAAEuD,SAAS,CAACvD,YAAY;YACpCC,WAAW,EAAEsD,SAAS,CAACtD;WACxB,CAAC;UAEF;UACAyD,UAAU,CAAC,MAAK;YACd,IAAI,CAACC,uBAAuB,CAACJ,SAAS,CAAC;YACvC,IAAI,CAACrB,iBAAiB,EAAE;UAC1B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEd,CAAC;;MACDf,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDxG,IAAI,CAAC0G,IAAI,CAAC,OAAO,EAAE,2BAA2B,EAAE,OAAO,CAAC;MAC1D;KACD,CAAC;EACJ;EAEQsC,uBAAuBA,CAACJ,SAAc;IAC5C,MAAMK,aAAa,GAAG,IAAI,CAAC5H,SAAS,CAACC,GAAG,CAAC,UAAU,CAAc;IACjE2H,aAAa,CAACC,KAAK,EAAE;IAErB,IAAIN,SAAS,CAACpE,QAAQ,IAAIoE,SAAS,CAACpE,QAAQ,CAACX,MAAM,GAAG,CAAC,EAAE;MACvD4C,OAAO,CAACgC,GAAG,CAAC,gCAAgC,EAAEG,SAAS,CAACpE,QAAQ,CAAC,CAAC,CAAC;MACnEoE,SAAS,CAACpE,QAAQ,CAAC2E,OAAO,CAAEC,cAAmB,IAAI;QACjD,MAAMC,SAAS,GAAGD,cAAc,CAACE,OAAO,EAAE5I,GAAG,IAAI0I,cAAc,CAACE,OAAO;QACvE,MAAMC,SAAS,GAAGH,cAAc,CAACI,OAAO,EAAE9I,GAAG,IAAI0I,cAAc,CAACI,OAAO;QAEvEP,aAAa,CAACQ,IAAI,CAAC,IAAI,CAACvF,EAAE,CAACW,KAAK,CAAC;UAC/ByE,OAAO,EAAE,CAACD,SAAS,EAAEtJ,UAAU,CAACiF,QAAQ,CAAC;UACzCwE,OAAO,EAAE,CAACD,SAAS,EAAExJ,UAAU,CAACiF,QAAQ;SACzC,CAAC,CAAC;MACL,CAAC,CAAC;MACFyB,OAAO,CAACgC,GAAG,CAAC,oCAAoC,EAAEQ,aAAa,CAACpB,KAAK,CAAC,CAAC,CAAC;;EAE5E;;EACA6B,sBAAsBA,CAACL,SAAiB,EAAEM,KAAU;IAClD,MAAMJ,SAAS,GAAGI,KAAK,CAACC,MAAM,CAAC/B,KAAK,CAAC,CAAC;IAEtC,MAAMoB,aAAa,GAAG,IAAI,CAAC5H,SAAS,CAACC,GAAG,CAAC,UAAU,CAAc;IAEjE,MAAMuI,aAAa,GAAGZ,aAAa,CAACa,QAAQ,CAACC,SAAS,CAACC,OAAO,IAC5DA,OAAO,CAACnC,KAAK,CAACyB,OAAO,KAAKD,SAAS,CACpC;IAED,IAAIQ,aAAa,KAAK,CAAC,CAAC,EAAE;MACxBZ,aAAa,CAACgB,EAAE,CAACJ,aAAa,CAAC,CAACzB,UAAU,CAAC;QAAEoB,OAAO,EAAED;MAAS,CAAE,CAAC;KACnE,MAAM;MACLN,aAAa,CAACQ,IAAI,CAAC,IAAI,CAACvF,EAAE,CAACW,KAAK,CAAC;QAC/ByE,OAAO,EAAE,CAACD,SAAS,CAAC;QACpBG,OAAO,EAAE,CAACD,SAAS;OACpB,CAAC,CAAC;;EAEP;EAEAW,iBAAiBA,CAAA;IACf,MAAMjB,aAAa,GAAG,IAAI,CAAC5H,SAAS,CAACC,GAAG,CAAC,UAAU,CAAc;IACjE2H,aAAa,CAACQ,IAAI,CAAC,IAAI,CAACvF,EAAE,CAACW,KAAK,CAAC;MAC/ByE,OAAO,EAAE,CAAC,EAAE,EAAEvJ,UAAU,CAACiF,QAAQ,CAAC;MAClCwE,OAAO,EAAE,CAAC,EAAE,EAAEzJ,UAAU,CAACiF,QAAQ;KAClC,CAAC,CAAC;EACL;EAEA/B,oBAAoBA,CAACJ,KAAa;IAChC,MAAMoG,aAAa,GAAG,IAAI,CAAC5H,SAAS,CAACC,GAAG,CAAC,UAAU,CAAc;IACjE2H,aAAa,CAACkB,QAAQ,CAACtH,KAAK,CAAC;EAC/B;EAEA;EACAuH,eAAeA,CAACT,KAAU;IACxB;IACA;IACAlD,OAAO,CAACgC,GAAG,CAAC,sCAAsC,EAAEkB,KAAK,CAACC,MAAM,CAAC/B,KAAK,CAAC;EACzE;EAEA;EACAwC,QAAQA,CAAA;IACN;IACA,MAAMzC,QAAQ,GAAG,IAAI,CAACvG,SAAS,CAACwG,KAAK;IACrC,MAAMyC,cAAc,GAAG,IAAI,CAAC5I,qBAAqB,EAAE;IAEnD;IACA,IAAI,CAACkG,QAAQ,CAAC7C,OAAO,IAAI,CAAC6C,QAAQ,CAAC3C,UAAU,IAAI,CAAC2C,QAAQ,CAAC1C,QAAQ,IAAI,CAAC0C,QAAQ,CAACvC,YAAY,EAAE;MAC7F,IAAI,CAACkF,oBAAoB,EAAE;MAC3BvK,IAAI,CAAC0G,IAAI,CAAC,kBAAkB,EAAE,+CAA+C,EAAE,SAAS,CAAC;MACzF;;IAGF;IACA,IAAI4D,cAAc,IAAI,CAAC1C,QAAQ,CAAC9C,OAAO,EAAE;MACvC,IAAI,CAACzD,SAAS,CAACC,GAAG,CAAC,SAAS,CAAC,EAAEkJ,aAAa,EAAE;MAC9CxK,IAAI,CAAC0G,IAAI,CAAC,kBAAkB,EAAE,gDAAgD,EAAE,SAAS,CAAC;MAC1F;;IAGF,IAAI,CAAC,IAAI,CAAC7E,kBAAkB,EAAE;MAC5B7B,IAAI,CAAC0G,IAAI,CAAC,OAAO,EAAE,0DAA0D,EAAE,SAAS,CAAC;MACzF;;IAGF;IACA,MAAMuC,aAAa,GAAG,IAAI,CAAC5H,SAAS,CAACC,GAAG,CAAC,UAAU,CAAc;IACjE,IAAI2H,aAAa,CAACpF,MAAM,GAAG,CAAC,EAAE;MAC5B,KAAK,IAAI4G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxB,aAAa,CAACpF,MAAM,EAAE4G,CAAC,EAAE,EAAE;QAC7C,MAAMC,YAAY,GAAGzB,aAAa,CAACgB,EAAE,CAACQ,CAAC,CAAc;QACrD,IAAI,CAACC,YAAY,CAACpJ,GAAG,CAAC,SAAS,CAAC,EAAEuG,KAAK,IAAI,CAAC6C,YAAY,CAACpJ,GAAG,CAAC,SAAS,CAAC,EAAEuG,KAAK,EAAE;UAC9E7H,IAAI,CAAC0G,IAAI,CAAC,kBAAkB,EAAE,+DAA+D,EAAE,SAAS,CAAC;UACzG;;;;IAKN,IAAI,CAAC9B,SAAS,GAAG,IAAI;IACrB,MAAMgE,SAAS,GAAG;MAChB,GAAG,IAAI,CAACvH,SAAS,CAACwG,KAAK;MACvB8C,SAAS,EAAE,IAAI,CAAC9I,kBAAkB;MAClCqD,QAAQ,EAAE0F,QAAQ,CAAC,IAAI,CAACvJ,SAAS,CAACwG,KAAK,CAAC3C,QAAQ,CAAC;MACjDJ,OAAO,EAAEwF,cAAc,GAAG1C,QAAQ,CAAC9C,OAAO,GAAG,EAAE,CAAC;KACjD;;IAED2B,OAAO,CAACgC,GAAG,CAAC,wBAAwB,EAAEG,SAAS,CAAC,CAAC,CAAC;IAElD,MAAMiC,OAAO,GAAG,IAAI,CAACpF,cAAc,GAC/B,IAAI,CAACzB,cAAc,CAAC8G,WAAW,CAAC,IAAI,CAACrF,cAAc,EAAEmD,SAAS,CAAC,GAC/D,IAAI,CAAC5E,cAAc,CAAC+G,WAAW,CAACnC,SAAS,CAAC;IAE9CiC,OAAO,CAACzE,SAAS,CAAC;MAChBC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAAC3B,SAAS,GAAG,KAAK;UACtB5E,IAAI,CAAC0G,IAAI,CAAC;YACRsE,KAAK,EAAE,IAAI,CAACvF,cAAc,GAAG,4BAA4B,GAAG,4BAA4B;YACxFwF,IAAI,EAAE,SAAS;YACfC,kBAAkB,EAAE,SAAS;YAC7BC,KAAK,EAAE;WACR,CAAC,CAACC,IAAI,CAAC,MAAK;YACX,IAAI,CAACjH,MAAM,CAACkH,QAAQ,CAAC,CAAC,0BAA0B,CAAC,CAAC;UACpD,CAAC,CAAC;SACH,MAAM;UACL,IAAI,CAACzG,SAAS,GAAG,KAAK;UACtB5E,IAAI,CAAC0G,IAAI,CAAC,OAAO,EAAEJ,QAAQ,CAACgF,OAAO,IAAI,sBAAsB,EAAE,OAAO,CAAC;;MAE3E,CAAC;MACD9E,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC5B,SAAS,GAAG,KAAK;QACtB6B,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C,MAAM+E,YAAY,GAAG/E,KAAK,CAACA,KAAK,EAAE8E,OAAO,IAAI,6CAA6C;QAC1FtL,IAAI,CAAC0G,IAAI,CAAC,OAAO,EAAE6E,YAAY,EAAE,OAAO,CAAC;MAC3C;KACD,CAAC;EACJ;EAEQhB,oBAAoBA,CAAA;IAC1BiB,MAAM,CAACC,IAAI,CAAC,IAAI,CAACpK,SAAS,CAACyI,QAAQ,CAAC,CAACX,OAAO,CAACuC,GAAG,IAAG;MACjD,MAAM1B,OAAO,GAAG,IAAI,CAAC3I,SAAS,CAACC,GAAG,CAACoK,GAAG,CAAC;MACvC1B,OAAO,EAAEQ,aAAa,EAAE;IAC1B,CAAC,CAAC;IAEF;IACA,MAAMvB,aAAa,GAAG,IAAI,CAAC5H,SAAS,CAACC,GAAG,CAAC,UAAU,CAAc;IACjE2H,aAAa,CAACa,QAAQ,CAACX,OAAO,CAACtE,KAAK,IAAG;MACrC2G,MAAM,CAACC,IAAI,CAAE5G,KAAmB,CAACiF,QAAQ,CAAC,CAACX,OAAO,CAACuC,GAAG,IAAG;QACvD7G,KAAK,CAACvD,GAAG,CAACoK,GAAG,CAAC,EAAElB,aAAa,EAAE;MACjC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA;EACAmB,MAAMA,CAAA;IACJ,IAAI,CAACxH,MAAM,CAACkH,QAAQ,CAAC,CAAC,0BAA0B,CAAC,CAAC;EACpD;EAAC,QAAAO,CAAA,G;qBA9cU9H,kBAAkB,EAAA7D,EAAA,CAAA4L,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA9L,EAAA,CAAA4L,iBAAA,CAAAG,EAAA,CAAA/H,cAAA,GAAAhE,EAAA,CAAA4L,iBAAA,CAAAI,EAAA,CAAAC,WAAA,GAAAjM,EAAA,CAAA4L,iBAAA,CAAAG,EAAA,CAAAG,MAAA,GAAAlM,EAAA,CAAA4L,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAApM,EAAA,CAAA4L,iBAAA,CAAAS,EAAA,CAAAC,iBAAA,GAAAtM,EAAA,CAAA4L,iBAAA,CAAAW,EAAA,CAAAC,cAAA,GAAAxM,EAAA,CAAA4L,iBAAA,CAAAa,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAlB9I,kBAAkB;IAAA+I,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QChB/BlN,EAAA,CAAAE,cAAA,aAA4B;QAEhBF,EAAA,CAAAG,MAAA,GAAuD;QAAAH,EAAA,CAAAI,YAAA,EAAK;QAChEJ,EAAA,CAAAE,cAAA,aAA0B;QACCF,EAAA,CAAAsC,UAAA,mBAAA8K,oDAAA;UAAA,OAASD,GAAA,CAAAzB,MAAA,EAAQ;QAAA,EAAC;QACzC1L,EAAA,CAAAE,cAAA,eAAU;QAAAF,EAAA,CAAAG,MAAA,iBAAU;QAAAH,EAAA,CAAAI,YAAA,EAAW;QAACJ,EAAA,CAAAG,MAAA,aAClC;QAAAH,EAAA,CAAAI,YAAA,EAAS;QACTJ,EAAA,CAAAE,cAAA,gBAA0G;QAArBF,EAAA,CAAAsC,UAAA,mBAAA+K,oDAAA;UAAA,OAASF,GAAA,CAAA/C,QAAA,EAAU;QAAA,EAAC;QACvGpK,EAAA,CAAAgB,UAAA,KAAAsM,2CAAA,0BAEe;QACftN,EAAA,CAAAgB,UAAA,KAAAuM,0CAAA,gCAAAvN,EAAA,CAAAwN,sBAAA,CAGc;QAChBxN,EAAA,CAAAI,YAAA,EAAS;QAIfJ,EAAA,CAAAE,cAAA,eAA8B;QAGGF,EAAA,CAAAG,MAAA,iBAAS;QAAAH,EAAA,CAAAI,YAAA,EAAQ;QACtCJ,EAAA,CAAAE,cAAA,kBAAuD;QAClCF,EAAA,CAAAG,MAAA,sBAAc;QAAAH,EAAA,CAAAI,YAAA,EAAS;QACxCJ,EAAA,CAAAgB,UAAA,KAAAyM,qCAAA,qBAES;QACbzN,EAAA,CAAAI,YAAA,EAAS;QACTJ,EAAA,CAAAgB,UAAA,KAAA0M,kCAAA,kBAEM;QACV1N,EAAA,CAAAI,YAAA,EAAM;QAENJ,EAAA,CAAAE,cAAA,cAA6B;QACDF,EAAA,CAAAG,MAAA,oBAAY;QAAAH,EAAA,CAAAI,YAAA,EAAQ;QAC5CJ,EAAA,CAAAE,cAAA,kBAAwG;QACnFF,EAAA,CAAAG,MAAA,IAAqF;QAAAH,EAAA,CAAAI,YAAA,EAAS;QAC/GJ,EAAA,CAAAgB,UAAA,KAAA2M,qCAAA,qBAES;QACb3N,EAAA,CAAAI,YAAA,EAAS;QACTJ,EAAA,CAAAgB,UAAA,KAAA4M,kCAAA,kBAEM;QACN5N,EAAA,CAAAgB,UAAA,KAAA6M,kCAAA,kBAEM;QACV7N,EAAA,CAAAI,YAAA,EAAM;QAGNJ,EAAA,CAAAgB,UAAA,KAAA8M,kCAAA,kBAUM;QAIN9N,EAAA,CAAAE,cAAA,cAA6B;QACHF,EAAA,CAAAG,MAAA,IAAgE;QAAAH,EAAA,CAAAI,YAAA,EAAQ;QAC9FJ,EAAA,CAAAE,cAAA,kBAAwD;QACnCF,EAAA,CAAAG,MAAA,IAAiE;QAAAH,EAAA,CAAAI,YAAA,EAAS;QAG3FJ,EAAA,CAAAgB,UAAA,KAAA+M,2CAAA,2BAGe;QAGf/N,EAAA,CAAAgB,UAAA,KAAAgN,2CAAA,4BASe;QACnBhO,EAAA,CAAAI,YAAA,EAAS;QACTJ,EAAA,CAAAgB,UAAA,KAAAiN,kCAAA,kBAEM;QAGNjO,EAAA,CAAAgB,UAAA,KAAAkN,oCAAA,oBAGQ;QACZlO,EAAA,CAAAI,YAAA,EAAM;QAGNJ,EAAA,CAAAgB,UAAA,KAAAmN,kCAAA,mBASM;QAENnO,EAAA,CAAAE,cAAA,cAA6B;QACCF,EAAA,CAAAG,MAAA,uBAAe;QAAAH,EAAA,CAAAI,YAAA,EAAQ;QACjDJ,EAAA,CAAAM,SAAA,iBAAqG;QACrGN,EAAA,CAAAgB,UAAA,KAAAoN,kCAAA,kBAEM;QACVpO,EAAA,CAAAI,YAAA,EAAM;QAENJ,EAAA,CAAAE,cAAA,cAA6B;QACAF,EAAA,CAAAG,MAAA,0BAAkB;QAAAH,EAAA,CAAAI,YAAA,EAAQ;QACnDJ,EAAA,CAAAM,SAAA,iBAAiG;QACjGN,EAAA,CAAAgB,UAAA,KAAAqN,kCAAA,kBAEM;QACVrO,EAAA,CAAAI,YAAA,EAAM;QAIVJ,EAAA,CAAAE,cAAA,eAAmC;QAEvBF,EAAA,CAAAG,MAAA,2BAAmB;QAAAH,EAAA,CAAAI,YAAA,EAAK;QAC5BJ,EAAA,CAAAE,cAAA,kBAAoF;QAA9BF,EAAA,CAAAsC,UAAA,mBAAAgM,qDAAA;UAAA,OAASnB,GAAA,CAAAlD,iBAAA,EAAmB;QAAA,EAAC;QAC/EjK,EAAA,CAAAE,cAAA,gBAAU;QAAAF,EAAA,CAAAG,MAAA,WAAG;QAAAH,EAAA,CAAAI,YAAA,EAAW;QAACJ,EAAA,CAAAG,MAAA,qBAC7B;QAAAH,EAAA,CAAAI,YAAA,EAAS;QAGbJ,EAAA,CAAAE,cAAA,eAA8B;QAC1BF,EAAA,CAAAgB,UAAA,KAAAuN,kCAAA,mBAmCM;QACVvO,EAAA,CAAAI,YAAA,EAAM;QAENJ,EAAA,CAAAgB,UAAA,KAAAwN,kCAAA,kBAEM;QAGNxO,EAAA,CAAAgB,UAAA,KAAAyN,kCAAA,kBAIM;QACVzO,EAAA,CAAAI,YAAA,EAAM;;;;;;;;;;;;QAxLFJ,EAAA,CAAAU,SAAA,GAAuD;QAAvDV,EAAA,CAAA0B,iBAAA,CAAAyL,GAAA,CAAA3H,cAAA,oCAAuD;QAKhBxF,EAAA,CAAAU,SAAA,GAA2C;QAA3CV,EAAA,CAAAO,UAAA,aAAA4M,GAAA,CAAA/L,SAAA,CAAAG,OAAA,IAAA4L,GAAA,CAAAxI,SAAA,CAA2C;QACnE3E,EAAA,CAAAU,SAAA,GAAkB;QAAlBV,EAAA,CAAAO,UAAA,UAAA4M,GAAA,CAAAxI,SAAA,CAAkB,aAAA+J,GAAA;QAWnC1O,EAAA,CAAAU,SAAA,GAAuB;QAAvBV,EAAA,CAAAO,UAAA,cAAA4M,GAAA,CAAA/L,SAAA,CAAuB;QAMepB,EAAA,CAAAU,SAAA,GAAW;QAAXV,EAAA,CAAAO,UAAA,YAAA4M,GAAA,CAAA3I,QAAA,CAAW;QAIrCxE,EAAA,CAAAU,SAAA,GAA4E;QAA5EV,EAAA,CAAAO,UAAA,WAAAkD,OAAA,GAAA0J,GAAA,CAAA/L,SAAA,CAAAC,GAAA,8BAAAoC,OAAA,CAAAnC,OAAA,OAAAmC,OAAA,GAAA0J,GAAA,CAAA/L,SAAA,CAAAC,GAAA,8BAAAoC,OAAA,CAAAlC,OAAA,EAA4E;QAOxBvB,EAAA,CAAAU,SAAA,GAA6C;QAA7CV,EAAA,CAAAO,UAAA,gBAAAoO,OAAA,GAAAxB,GAAA,CAAA/L,SAAA,CAAAC,GAAA,8BAAAsN,OAAA,CAAA/G,KAAA,EAA6C;QAClF5H,EAAA,CAAAU,SAAA,GAAqF;QAArFV,EAAA,CAAA0B,iBAAA,IAAAkN,OAAA,GAAAzB,GAAA,CAAA/L,SAAA,CAAAC,GAAA,8BAAAuN,OAAA,CAAAhH,KAAA,iDAAqF;QAC7E5H,EAAA,CAAAU,SAAA,GAAsB;QAAtBV,EAAA,CAAAO,UAAA,YAAA4M,GAAA,CAAAzI,mBAAA,CAAsB;QAI7C1E,EAAA,CAAAU,SAAA,GAAkF;QAAlFV,EAAA,CAAAO,UAAA,WAAAsO,QAAA,GAAA1B,GAAA,CAAA/L,SAAA,CAAAC,GAAA,iCAAAwN,QAAA,CAAAvN,OAAA,OAAAuN,QAAA,GAAA1B,GAAA,CAAA/L,SAAA,CAAAC,GAAA,iCAAAwN,QAAA,CAAAtN,OAAA,EAAkF;QAGlFvB,EAAA,CAAAU,SAAA,GAAyE;QAAzEV,EAAA,CAAAO,UAAA,WAAAuO,QAAA,GAAA3B,GAAA,CAAA/L,SAAA,CAAAC,GAAA,8BAAAyN,QAAA,CAAAlH,KAAA,KAAAuF,GAAA,CAAAzI,mBAAA,CAAAd,MAAA,OAAyE;QAMrD5D,EAAA,CAAAU,SAAA,GAA6B;QAA7BV,EAAA,CAAAO,UAAA,SAAA4M,GAAA,CAAA1L,qBAAA,GAA6B;QAejCzB,EAAA,CAAAU,SAAA,GAAgE;QAAhEV,EAAA,CAAA0B,iBAAA,CAAAyL,GAAA,CAAA1L,qBAAA,sCAAgE;QAEjEzB,EAAA,CAAAU,SAAA,GAAiE;QAAjEV,EAAA,CAAA0B,iBAAA,CAAAyL,GAAA,CAAA1L,qBAAA,uCAAiE;QAGnEzB,EAAA,CAAAU,SAAA,GAA6B;QAA7BV,EAAA,CAAAO,UAAA,SAAA4M,GAAA,CAAA1L,qBAAA,GAA6B;QAM7BzB,EAAA,CAAAU,SAAA,GAA8B;QAA9BV,EAAA,CAAAO,UAAA,UAAA4M,GAAA,CAAA1L,qBAAA,GAA8B;QAW3CzB,EAAA,CAAAU,SAAA,GAA8E;QAA9EV,EAAA,CAAAO,UAAA,WAAAwO,QAAA,GAAA5B,GAAA,CAAA/L,SAAA,CAAAC,GAAA,+BAAA0N,QAAA,CAAAzN,OAAA,OAAAyN,QAAA,GAAA5B,GAAA,CAAA/L,SAAA,CAAAC,GAAA,+BAAA0N,QAAA,CAAAxN,OAAA,EAA8E;QAK/CvB,EAAA,CAAAU,SAAA,GAA6B;QAA7BV,EAAA,CAAAO,UAAA,SAAA4M,GAAA,CAAA1L,qBAAA,GAA6B;QAOxCzB,EAAA,CAAAU,SAAA,GAAwB;QAAxBV,EAAA,CAAAO,UAAA,SAAA4M,GAAA,CAAAvL,kBAAA,CAAwB;QAc5C5B,EAAA,CAAAU,SAAA,GAAsF;QAAtFV,EAAA,CAAAO,UAAA,WAAAyO,QAAA,GAAA7B,GAAA,CAAA/L,SAAA,CAAAC,GAAA,mCAAA2N,QAAA,CAAA1N,OAAA,OAAA0N,QAAA,GAAA7B,GAAA,CAAA/L,SAAA,CAAAC,GAAA,mCAAA2N,QAAA,CAAAzN,OAAA,EAAsF;QAQtFvB,EAAA,CAAAU,SAAA,GAAoF;QAApFV,EAAA,CAAAO,UAAA,WAAA0O,QAAA,GAAA9B,GAAA,CAAA/L,SAAA,CAAAC,GAAA,kCAAA4N,QAAA,CAAA3N,OAAA,OAAA2N,QAAA,GAAA9B,GAAA,CAAA/L,SAAA,CAAAC,GAAA,kCAAA4N,QAAA,CAAA1N,OAAA,EAAoF;QAgB5DvB,EAAA,CAAAU,SAAA,IAA+B;QAA/BV,EAAA,CAAAO,UAAA,YAAA4M,GAAA,CAAAxJ,iBAAA,CAAAkG,QAAA,CAA+B;QAsC3D7J,EAAA,CAAAU,SAAA,GAAoC;QAApCV,EAAA,CAAAO,UAAA,SAAA4M,GAAA,CAAAxJ,iBAAA,CAAAC,MAAA,OAAoC;QAKpC5D,EAAA,CAAAU,SAAA,GAAoD;QAApDV,EAAA,CAAAO,UAAA,SAAA4M,GAAA,CAAA3H,cAAA,IAAA2H,GAAA,CAAAxJ,iBAAA,CAAAC,MAAA,KAAoD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}