{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { StudentDashboardRoutingModule } from './student-dashboard-routing.module';\nimport { StudentOverviewComponent } from './student-overview/student-overview.component';\nimport { AttendanceComponent } from './attendance/attendance.component';\nimport { MaterialModule } from 'src/app/material';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { StudentTeachersComponent } from './student-teachers/student-teachers.component';\nimport { StudentNoticeComponent } from './student-notice/student-notice.component';\nimport { StudentComplaintComponent } from './student-complaint/student-complaint.component';\nimport { StudentClassesComponent } from './student-classes/student-classes.component';\nimport { StudentSubjectsComponent } from './student-subjects/student-subjects.component';\nimport { StudentTimetableComponent } from './student-timetable/student-timetable.component';\nimport * as i0 from \"@angular/core\";\nexport class StudentDashboardModule {\n  static #_ = this.ɵfac = function StudentDashboardModule_Factory(t) {\n    return new (t || StudentDashboardModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: StudentDashboardModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, StudentDashboardRoutingModule, MaterialModule, FormsModule, ReactiveFormsModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(StudentDashboardModule, {\n    declarations: [StudentOverviewComponent, AttendanceComponent, StudentTeachersComponent, StudentNoticeComponent, StudentComplaintComponent, StudentClassesComponent, StudentSubjectsComponent, StudentTimetableComponent],\n    imports: [CommonModule, StudentDashboardRoutingModule, MaterialModule, FormsModule, ReactiveFormsModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "StudentDashboardRoutingModule", "StudentOverviewComponent", "AttendanceComponent", "MaterialModule", "FormsModule", "ReactiveFormsModule", "StudentTeachersComponent", "StudentNoticeComponent", "StudentComplaintComponent", "StudentClassesComponent", "StudentSubjectsComponent", "StudentTimetableComponent", "StudentDashboardModule", "_", "_2", "_3", "declarations", "imports"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\student-dashboard\\student-dashboard.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { StudentDashboardRoutingModule } from './student-dashboard-routing.module';\r\nimport { StudentOverviewComponent } from './student-overview/student-overview.component';\r\nimport { AttendanceComponent } from './attendance/attendance.component';\r\nimport { MaterialModule } from 'src/app/material';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { StudentTeachersComponent } from './student-teachers/student-teachers.component';\r\nimport { StudentNoticeComponent } from './student-notice/student-notice.component';\r\nimport { StudentComplaintComponent } from './student-complaint/student-complaint.component';\r\nimport { StudentClassesComponent } from './student-classes/student-classes.component';\r\nimport { StudentSubjectsComponent } from './student-subjects/student-subjects.component';\r\nimport { StudentTimetableComponent } from './student-timetable/student-timetable.component';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    StudentOverviewComponent,\r\n    AttendanceComponent,\r\n    StudentTeachersComponent,\r\n    StudentNoticeComponent,\r\n    StudentComplaintComponent,\r\n    StudentClassesComponent,\r\n    StudentSubjectsComponent,\r\n    StudentTimetableComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    StudentDashboardRoutingModule,\r\n    MaterialModule,\r\n    FormsModule,\r\n    ReactiveFormsModule\r\n  ]\r\n})\r\nexport class StudentDashboardModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,6BAA6B,QAAQ,oCAAoC;AAClF,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,yBAAyB,QAAQ,iDAAiD;AAC3F,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,yBAAyB,QAAQ,iDAAiD;;AAqB3F,OAAM,MAAOC,sBAAsB;EAAA,QAAAC,CAAA,G;qBAAtBD,sBAAsB;EAAA;EAAA,QAAAE,EAAA,G;UAAtBF;EAAsB;EAAA,QAAAG,EAAA,G;cAP/BhB,YAAY,EACZC,6BAA6B,EAC7BG,cAAc,EACdC,WAAW,EACXC,mBAAmB;EAAA;;;2EAGVO,sBAAsB;IAAAI,YAAA,GAjB/Bf,wBAAwB,EACxBC,mBAAmB,EACnBI,wBAAwB,EACxBC,sBAAsB,EACtBC,yBAAyB,EACzBC,uBAAuB,EACvBC,wBAAwB,EACxBC,yBAAyB;IAAAM,OAAA,GAGzBlB,YAAY,EACZC,6BAA6B,EAC7BG,cAAc,EACdC,WAAW,EACXC,mBAAmB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}