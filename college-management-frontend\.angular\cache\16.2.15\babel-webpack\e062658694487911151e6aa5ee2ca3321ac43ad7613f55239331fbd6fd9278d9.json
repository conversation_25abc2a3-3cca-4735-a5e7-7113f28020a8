{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class SubjectService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = environment.apiUrl;\n  }\n  // Get all subjects with filters\n  getAllSubjects(filters) {\n    let params = new HttpParams();\n    if (filters) {\n      Object.keys(filters).forEach(key => {\n        const value = filters[key];\n        if (value !== undefined && value !== null) {\n          params = params.set(key, value.toString());\n        }\n      });\n    }\n    return this.http.get(`${this.apiUrl}/subjects`, {\n      params\n    });\n  }\n  // Get subjects by program\n  getSubjectsByProgram(programId, isActive) {\n    let params = new HttpParams();\n    if (isActive !== undefined) params = params.set('isActive', isActive.toString());\n    return this.http.get(`${this.apiUrl}/subjects/program/${programId}`, {\n      params\n    });\n  }\n  // Get subjects by department\n  getSubjectsByDepartment(departmentId, isActive) {\n    let params = new HttpParams();\n    if (isActive !== undefined) params = params.set('isActive', isActive.toString());\n    return this.http.get(`${this.apiUrl}/subjects/department/${departmentId}`, {\n      params\n    });\n  }\n  // Get subject by ID\n  getSubjectById(id) {\n    return this.http.get(`${this.apiUrl}/subjects/${id}`);\n  }\n  // Create a new subject\n  createSubject(subjectData) {\n    return this.http.post(`${this.apiUrl}/subjects`, subjectData);\n  }\n  // Update an existing subject\n  updateSubject(id, subjectData) {\n    return this.http.put(`${this.apiUrl}/subjects/${id}`, subjectData);\n  }\n  // Delete a subject (soft delete)\n  deleteSubject(id) {\n    return this.http.delete(`${this.apiUrl}/subjects/${id}`);\n  }\n  // Legacy methods for backward compatibility\n  getSubjects() {\n    return this.getAllSubjects();\n  }\n  addSubject(subjectData) {\n    return this.createSubject(subjectData);\n  }\n  static #_ = this.ɵfac = function SubjectService_Factory(t) {\n    return new (t || SubjectService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: SubjectService,\n    factory: SubjectService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpParams", "environment", "SubjectService", "constructor", "http", "apiUrl", "getAllSubjects", "filters", "params", "Object", "keys", "for<PERSON>ach", "key", "value", "undefined", "set", "toString", "get", "getSubjectsByProgram", "programId", "isActive", "getSubjectsByDepartment", "departmentId", "getSubjectById", "id", "createSubject", "subjectData", "post", "updateSubject", "put", "deleteSubject", "delete", "getSubjects", "addSubject", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\services\\subject.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { environment } from 'src/environments/environment';\r\nimport { Subject } from '../models/user';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class SubjectService {\r\n  private apiUrl = environment.apiUrl;\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  // Get all subjects with filters\r\n  getAllSubjects(filters?: {\r\n    program?: string;\r\n    department?: string;\r\n    semester?: number;\r\n    isActive?: boolean;\r\n  }): Observable<any> {\r\n    let params = new HttpParams();\r\n\r\n    if (filters) {\r\n      Object.keys(filters).forEach(key => {\r\n        const value = (filters as any)[key];\r\n        if (value !== undefined && value !== null) {\r\n          params = params.set(key, value.toString());\r\n        }\r\n      });\r\n    }\r\n\r\n    return this.http.get<any>(`${this.apiUrl}/subjects`, { params });\r\n  }\r\n\r\n  // Get subjects by program\r\n  getSubjectsByProgram(programId: string, isActive?: boolean): Observable<any> {\r\n    let params = new HttpParams();\r\n    if (isActive !== undefined) params = params.set('isActive', isActive.toString());\r\n\r\n    return this.http.get<any>(`${this.apiUrl}/subjects/program/${programId}`, { params });\r\n  }\r\n\r\n  // Get subjects by department\r\n  getSubjectsByDepartment(departmentId: string, isActive?: boolean): Observable<any> {\r\n    let params = new HttpParams();\r\n    if (isActive !== undefined) params = params.set('isActive', isActive.toString());\r\n\r\n    return this.http.get<any>(`${this.apiUrl}/subjects/department/${departmentId}`, { params });\r\n  }\r\n\r\n  // Get subject by ID\r\n  getSubjectById(id: string): Observable<any> {\r\n    return this.http.get<any>(`${this.apiUrl}/subjects/${id}`);\r\n  }\r\n\r\n  // Create a new subject\r\n  createSubject(subjectData: Partial<Subject>): Observable<any> {\r\n    return this.http.post<any>(`${this.apiUrl}/subjects`, subjectData);\r\n  }\r\n\r\n  // Update an existing subject\r\n  updateSubject(id: string, subjectData: Partial<Subject>): Observable<any> {\r\n    return this.http.put<any>(`${this.apiUrl}/subjects/${id}`, subjectData);\r\n  }\r\n\r\n  // Delete a subject (soft delete)\r\n  deleteSubject(id: string): Observable<any> {\r\n    return this.http.delete<any>(`${this.apiUrl}/subjects/${id}`);\r\n  }\r\n\r\n  // Legacy methods for backward compatibility\r\n  getSubjects(): Observable<any> {\r\n    return this.getAllSubjects();\r\n  }\r\n\r\n  addSubject(subjectData: any): Observable<any> {\r\n    return this.createSubject(subjectData);\r\n  }\r\n}\r\n"], "mappings": "AACA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,WAAW,QAAQ,8BAA8B;;;AAM1D,OAAM,MAAOC,cAAc;EAGzBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAGJ,WAAW,CAACI,MAAM;EAEI;EAEvC;EACAC,cAAcA,CAACC,OAKd;IACC,IAAIC,MAAM,GAAG,IAAIR,UAAU,EAAE;IAE7B,IAAIO,OAAO,EAAE;MACXE,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC,CAACI,OAAO,CAACC,GAAG,IAAG;QACjC,MAAMC,KAAK,GAAIN,OAAe,CAACK,GAAG,CAAC;QACnC,IAAIC,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,EAAE;UACzCL,MAAM,GAAGA,MAAM,CAACO,GAAG,CAACH,GAAG,EAAEC,KAAK,CAACG,QAAQ,EAAE,CAAC;;MAE9C,CAAC,CAAC;;IAGJ,OAAO,IAAI,CAACZ,IAAI,CAACa,GAAG,CAAM,GAAG,IAAI,CAACZ,MAAM,WAAW,EAAE;MAAEG;IAAM,CAAE,CAAC;EAClE;EAEA;EACAU,oBAAoBA,CAACC,SAAiB,EAAEC,QAAkB;IACxD,IAAIZ,MAAM,GAAG,IAAIR,UAAU,EAAE;IAC7B,IAAIoB,QAAQ,KAAKN,SAAS,EAAEN,MAAM,GAAGA,MAAM,CAACO,GAAG,CAAC,UAAU,EAAEK,QAAQ,CAACJ,QAAQ,EAAE,CAAC;IAEhF,OAAO,IAAI,CAACZ,IAAI,CAACa,GAAG,CAAM,GAAG,IAAI,CAACZ,MAAM,qBAAqBc,SAAS,EAAE,EAAE;MAAEX;IAAM,CAAE,CAAC;EACvF;EAEA;EACAa,uBAAuBA,CAACC,YAAoB,EAAEF,QAAkB;IAC9D,IAAIZ,MAAM,GAAG,IAAIR,UAAU,EAAE;IAC7B,IAAIoB,QAAQ,KAAKN,SAAS,EAAEN,MAAM,GAAGA,MAAM,CAACO,GAAG,CAAC,UAAU,EAAEK,QAAQ,CAACJ,QAAQ,EAAE,CAAC;IAEhF,OAAO,IAAI,CAACZ,IAAI,CAACa,GAAG,CAAM,GAAG,IAAI,CAACZ,MAAM,wBAAwBiB,YAAY,EAAE,EAAE;MAAEd;IAAM,CAAE,CAAC;EAC7F;EAEA;EACAe,cAAcA,CAACC,EAAU;IACvB,OAAO,IAAI,CAACpB,IAAI,CAACa,GAAG,CAAM,GAAG,IAAI,CAACZ,MAAM,aAAamB,EAAE,EAAE,CAAC;EAC5D;EAEA;EACAC,aAAaA,CAACC,WAA6B;IACzC,OAAO,IAAI,CAACtB,IAAI,CAACuB,IAAI,CAAM,GAAG,IAAI,CAACtB,MAAM,WAAW,EAAEqB,WAAW,CAAC;EACpE;EAEA;EACAE,aAAaA,CAACJ,EAAU,EAAEE,WAA6B;IACrD,OAAO,IAAI,CAACtB,IAAI,CAACyB,GAAG,CAAM,GAAG,IAAI,CAACxB,MAAM,aAAamB,EAAE,EAAE,EAAEE,WAAW,CAAC;EACzE;EAEA;EACAI,aAAaA,CAACN,EAAU;IACtB,OAAO,IAAI,CAACpB,IAAI,CAAC2B,MAAM,CAAM,GAAG,IAAI,CAAC1B,MAAM,aAAamB,EAAE,EAAE,CAAC;EAC/D;EAEA;EACAQ,WAAWA,CAAA;IACT,OAAO,IAAI,CAAC1B,cAAc,EAAE;EAC9B;EAEA2B,UAAUA,CAACP,WAAgB;IACzB,OAAO,IAAI,CAACD,aAAa,CAACC,WAAW,CAAC;EACxC;EAAC,QAAAQ,CAAA,G;qBArEUhC,cAAc,EAAAiC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAdrC,cAAc;IAAAsC,OAAA,EAAdtC,cAAc,CAAAuC,IAAA;IAAAC,UAAA,EAFb;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}