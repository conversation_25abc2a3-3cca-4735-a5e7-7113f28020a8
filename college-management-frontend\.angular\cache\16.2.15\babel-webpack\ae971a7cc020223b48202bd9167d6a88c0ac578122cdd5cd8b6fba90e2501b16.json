{"ast": null, "code": "/*!\n * Chart.js v4.4.4\n * https://www.chartjs.org\n * (c) 2024 Chart.js Contributors\n * Released under the MIT License\n */\nimport { r as requestAnimFrame, a as resolve, e as effects, c as color, i as isObject, d as defaults, b as isArray, v as valueOrDefault, u as unlistenArrayEvents, l as listenArrayEvents, f as resolveObjectKey, g as isNumberFinite, h as defined, s as sign, j as createContext, k as isNullOrUndef, _ as _arrayUnique, t as toRadians, m as toPercentage, n as toDimension, T as TAU, o as formatNumber, p as _angleBetween, H as HALF_PI, P as PI, q as _getStartAndCountOfVisiblePoints, w as _scaleRangesChanged, x as isNumber, y as _parseObjectDataRadialScale, z as getRelativePosition, A as _rlookupByKey, B as _lookup<PERSON>y<PERSON>ey, C as _isPointInArea, D as getAngleFromPoint, E as toPadding, F as each, G as getMaximumSize, I as _getParentNode, J as readUsedSize, K as supportsEventListenerOptions, L as throttled, M as _isDomSupported, N as _factorize, O as finiteOrDefault, Q as callback, R as _addGrace, S as _limitValue, U as toDegrees, V as _measureText, W as _int16Range, X as _alignPixel, Y as clipArea, Z as renderText, $ as unclipArea, a0 as toFont, a1 as _toLeftRightCenter, a2 as _alignStartEnd, a3 as overrides, a4 as merge, a5 as _capitalize, a6 as descriptors, a7 as isFunction, a8 as _attachContext, a9 as _createResolver, aa as _descriptors, ab as mergeIf, ac as uid, ad as debounce, ae as retinaScale, af as clearCanvas, ag as setsEqual, ah as _elementsEqual, ai as _isClickEvent, aj as _isBetween, ak as _readValueToProps, al as _updateBezierControlPoints, am as _computeSegments, an as _boundSegments, ao as _steppedInterpolation, ap as _bezierInterpolation, aq as _pointInLine, ar as _steppedLineTo, as as _bezierCurveTo, at as drawPoint, au as addRoundedRectPath, av as toTRBL, aw as toTRBLCorners, ax as _boundSegment, ay as _normalizeAngle, az as getRtlAdapter, aA as overrideTextDirection, aB as _textX, aC as restoreTextDirection, aD as drawPointLegend, aE as distanceBetweenPoints, aF as noop, aG as _setMinAndMaxByKey, aH as niceNum, aI as almostWhole, aJ as almostEquals, aK as _decimalPlaces, aL as Ticks, aM as log10, aN as _longestText, aO as _filterBetween, aP as _lookup } from './chunks/helpers.segment.js';\nimport '@kurkle/color';\nclass Animator {\n  constructor() {\n    this._request = null;\n    this._charts = new Map();\n    this._running = false;\n    this._lastDate = undefined;\n  }\n  _notify(chart, anims, date, type) {\n    const callbacks = anims.listeners[type];\n    const numSteps = anims.duration;\n    callbacks.forEach(fn => fn({\n      chart,\n      initial: anims.initial,\n      numSteps,\n      currentStep: Math.min(date - anims.start, numSteps)\n    }));\n  }\n  _refresh() {\n    if (this._request) {\n      return;\n    }\n    this._running = true;\n    this._request = requestAnimFrame.call(window, () => {\n      this._update();\n      this._request = null;\n      if (this._running) {\n        this._refresh();\n      }\n    });\n  }\n  _update(date = Date.now()) {\n    let remaining = 0;\n    this._charts.forEach((anims, chart) => {\n      if (!anims.running || !anims.items.length) {\n        return;\n      }\n      const items = anims.items;\n      let i = items.length - 1;\n      let draw = false;\n      let item;\n      for (; i >= 0; --i) {\n        item = items[i];\n        if (item._active) {\n          if (item._total > anims.duration) {\n            anims.duration = item._total;\n          }\n          item.tick(date);\n          draw = true;\n        } else {\n          items[i] = items[items.length - 1];\n          items.pop();\n        }\n      }\n      if (draw) {\n        chart.draw();\n        this._notify(chart, anims, date, 'progress');\n      }\n      if (!items.length) {\n        anims.running = false;\n        this._notify(chart, anims, date, 'complete');\n        anims.initial = false;\n      }\n      remaining += items.length;\n    });\n    this._lastDate = date;\n    if (remaining === 0) {\n      this._running = false;\n    }\n  }\n  _getAnims(chart) {\n    const charts = this._charts;\n    let anims = charts.get(chart);\n    if (!anims) {\n      anims = {\n        running: false,\n        initial: true,\n        items: [],\n        listeners: {\n          complete: [],\n          progress: []\n        }\n      };\n      charts.set(chart, anims);\n    }\n    return anims;\n  }\n  listen(chart, event, cb) {\n    this._getAnims(chart).listeners[event].push(cb);\n  }\n  add(chart, items) {\n    if (!items || !items.length) {\n      return;\n    }\n    this._getAnims(chart).items.push(...items);\n  }\n  has(chart) {\n    return this._getAnims(chart).items.length > 0;\n  }\n  start(chart) {\n    const anims = this._charts.get(chart);\n    if (!anims) {\n      return;\n    }\n    anims.running = true;\n    anims.start = Date.now();\n    anims.duration = anims.items.reduce((acc, cur) => Math.max(acc, cur._duration), 0);\n    this._refresh();\n  }\n  running(chart) {\n    if (!this._running) {\n      return false;\n    }\n    const anims = this._charts.get(chart);\n    if (!anims || !anims.running || !anims.items.length) {\n      return false;\n    }\n    return true;\n  }\n  stop(chart) {\n    const anims = this._charts.get(chart);\n    if (!anims || !anims.items.length) {\n      return;\n    }\n    const items = anims.items;\n    let i = items.length - 1;\n    for (; i >= 0; --i) {\n      items[i].cancel();\n    }\n    anims.items = [];\n    this._notify(chart, anims, Date.now(), 'complete');\n  }\n  remove(chart) {\n    return this._charts.delete(chart);\n  }\n}\nvar animator = /* #__PURE__ */new Animator();\nconst transparent = 'transparent';\nconst interpolators = {\n  boolean(from, to, factor) {\n    return factor > 0.5 ? to : from;\n  },\n  color(from, to, factor) {\n    const c0 = color(from || transparent);\n    const c1 = c0.valid && color(to || transparent);\n    return c1 && c1.valid ? c1.mix(c0, factor).hexString() : to;\n  },\n  number(from, to, factor) {\n    return from + (to - from) * factor;\n  }\n};\nclass Animation {\n  constructor(cfg, target, prop, to) {\n    const currentValue = target[prop];\n    to = resolve([cfg.to, to, currentValue, cfg.from]);\n    const from = resolve([cfg.from, currentValue, to]);\n    this._active = true;\n    this._fn = cfg.fn || interpolators[cfg.type || typeof from];\n    this._easing = effects[cfg.easing] || effects.linear;\n    this._start = Math.floor(Date.now() + (cfg.delay || 0));\n    this._duration = this._total = Math.floor(cfg.duration);\n    this._loop = !!cfg.loop;\n    this._target = target;\n    this._prop = prop;\n    this._from = from;\n    this._to = to;\n    this._promises = undefined;\n  }\n  active() {\n    return this._active;\n  }\n  update(cfg, to, date) {\n    if (this._active) {\n      this._notify(false);\n      const currentValue = this._target[this._prop];\n      const elapsed = date - this._start;\n      const remain = this._duration - elapsed;\n      this._start = date;\n      this._duration = Math.floor(Math.max(remain, cfg.duration));\n      this._total += elapsed;\n      this._loop = !!cfg.loop;\n      this._to = resolve([cfg.to, to, currentValue, cfg.from]);\n      this._from = resolve([cfg.from, currentValue, to]);\n    }\n  }\n  cancel() {\n    if (this._active) {\n      this.tick(Date.now());\n      this._active = false;\n      this._notify(false);\n    }\n  }\n  tick(date) {\n    const elapsed = date - this._start;\n    const duration = this._duration;\n    const prop = this._prop;\n    const from = this._from;\n    const loop = this._loop;\n    const to = this._to;\n    let factor;\n    this._active = from !== to && (loop || elapsed < duration);\n    if (!this._active) {\n      this._target[prop] = to;\n      this._notify(true);\n      return;\n    }\n    if (elapsed < 0) {\n      this._target[prop] = from;\n      return;\n    }\n    factor = elapsed / duration % 2;\n    factor = loop && factor > 1 ? 2 - factor : factor;\n    factor = this._easing(Math.min(1, Math.max(0, factor)));\n    this._target[prop] = this._fn(from, to, factor);\n  }\n  wait() {\n    const promises = this._promises || (this._promises = []);\n    return new Promise((res, rej) => {\n      promises.push({\n        res,\n        rej\n      });\n    });\n  }\n  _notify(resolved) {\n    const method = resolved ? 'res' : 'rej';\n    const promises = this._promises || [];\n    for (let i = 0; i < promises.length; i++) {\n      promises[i][method]();\n    }\n  }\n}\nclass Animations {\n  constructor(chart, config) {\n    this._chart = chart;\n    this._properties = new Map();\n    this.configure(config);\n  }\n  configure(config) {\n    if (!isObject(config)) {\n      return;\n    }\n    const animationOptions = Object.keys(defaults.animation);\n    const animatedProps = this._properties;\n    Object.getOwnPropertyNames(config).forEach(key => {\n      const cfg = config[key];\n      if (!isObject(cfg)) {\n        return;\n      }\n      const resolved = {};\n      for (const option of animationOptions) {\n        resolved[option] = cfg[option];\n      }\n      (isArray(cfg.properties) && cfg.properties || [key]).forEach(prop => {\n        if (prop === key || !animatedProps.has(prop)) {\n          animatedProps.set(prop, resolved);\n        }\n      });\n    });\n  }\n  _animateOptions(target, values) {\n    const newOptions = values.options;\n    const options = resolveTargetOptions(target, newOptions);\n    if (!options) {\n      return [];\n    }\n    const animations = this._createAnimations(options, newOptions);\n    if (newOptions.$shared) {\n      awaitAll(target.options.$animations, newOptions).then(() => {\n        target.options = newOptions;\n      }, () => {});\n    }\n    return animations;\n  }\n  _createAnimations(target, values) {\n    const animatedProps = this._properties;\n    const animations = [];\n    const running = target.$animations || (target.$animations = {});\n    const props = Object.keys(values);\n    const date = Date.now();\n    let i;\n    for (i = props.length - 1; i >= 0; --i) {\n      const prop = props[i];\n      if (prop.charAt(0) === '$') {\n        continue;\n      }\n      if (prop === 'options') {\n        animations.push(...this._animateOptions(target, values));\n        continue;\n      }\n      const value = values[prop];\n      let animation = running[prop];\n      const cfg = animatedProps.get(prop);\n      if (animation) {\n        if (cfg && animation.active()) {\n          animation.update(cfg, value, date);\n          continue;\n        } else {\n          animation.cancel();\n        }\n      }\n      if (!cfg || !cfg.duration) {\n        target[prop] = value;\n        continue;\n      }\n      running[prop] = animation = new Animation(cfg, target, prop, value);\n      animations.push(animation);\n    }\n    return animations;\n  }\n  update(target, values) {\n    if (this._properties.size === 0) {\n      Object.assign(target, values);\n      return;\n    }\n    const animations = this._createAnimations(target, values);\n    if (animations.length) {\n      animator.add(this._chart, animations);\n      return true;\n    }\n  }\n}\nfunction awaitAll(animations, properties) {\n  const running = [];\n  const keys = Object.keys(properties);\n  for (let i = 0; i < keys.length; i++) {\n    const anim = animations[keys[i]];\n    if (anim && anim.active()) {\n      running.push(anim.wait());\n    }\n  }\n  return Promise.all(running);\n}\nfunction resolveTargetOptions(target, newOptions) {\n  if (!newOptions) {\n    return;\n  }\n  let options = target.options;\n  if (!options) {\n    target.options = newOptions;\n    return;\n  }\n  if (options.$shared) {\n    target.options = options = Object.assign({}, options, {\n      $shared: false,\n      $animations: {}\n    });\n  }\n  return options;\n}\nfunction scaleClip(scale, allowedOverflow) {\n  const opts = scale && scale.options || {};\n  const reverse = opts.reverse;\n  const min = opts.min === undefined ? allowedOverflow : 0;\n  const max = opts.max === undefined ? allowedOverflow : 0;\n  return {\n    start: reverse ? max : min,\n    end: reverse ? min : max\n  };\n}\nfunction defaultClip(xScale, yScale, allowedOverflow) {\n  if (allowedOverflow === false) {\n    return false;\n  }\n  const x = scaleClip(xScale, allowedOverflow);\n  const y = scaleClip(yScale, allowedOverflow);\n  return {\n    top: y.end,\n    right: x.end,\n    bottom: y.start,\n    left: x.start\n  };\n}\nfunction toClip(value) {\n  let t, r, b, l;\n  if (isObject(value)) {\n    t = value.top;\n    r = value.right;\n    b = value.bottom;\n    l = value.left;\n  } else {\n    t = r = b = l = value;\n  }\n  return {\n    top: t,\n    right: r,\n    bottom: b,\n    left: l,\n    disabled: value === false\n  };\n}\nfunction getSortedDatasetIndices(chart, filterVisible) {\n  const keys = [];\n  const metasets = chart._getSortedDatasetMetas(filterVisible);\n  let i, ilen;\n  for (i = 0, ilen = metasets.length; i < ilen; ++i) {\n    keys.push(metasets[i].index);\n  }\n  return keys;\n}\nfunction applyStack(stack, value, dsIndex, options = {}) {\n  const keys = stack.keys;\n  const singleMode = options.mode === 'single';\n  let i, ilen, datasetIndex, otherValue;\n  if (value === null) {\n    return;\n  }\n  for (i = 0, ilen = keys.length; i < ilen; ++i) {\n    datasetIndex = +keys[i];\n    if (datasetIndex === dsIndex) {\n      if (options.all) {\n        continue;\n      }\n      break;\n    }\n    otherValue = stack.values[datasetIndex];\n    if (isNumberFinite(otherValue) && (singleMode || value === 0 || sign(value) === sign(otherValue))) {\n      value += otherValue;\n    }\n  }\n  return value;\n}\nfunction convertObjectDataToArray(data, meta) {\n  const {\n    iScale,\n    vScale\n  } = meta;\n  const iAxisKey = iScale.axis === 'x' ? 'x' : 'y';\n  const vAxisKey = vScale.axis === 'x' ? 'x' : 'y';\n  const keys = Object.keys(data);\n  const adata = new Array(keys.length);\n  let i, ilen, key;\n  for (i = 0, ilen = keys.length; i < ilen; ++i) {\n    key = keys[i];\n    adata[i] = {\n      [iAxisKey]: key,\n      [vAxisKey]: data[key]\n    };\n  }\n  return adata;\n}\nfunction isStacked(scale, meta) {\n  const stacked = scale && scale.options.stacked;\n  return stacked || stacked === undefined && meta.stack !== undefined;\n}\nfunction getStackKey(indexScale, valueScale, meta) {\n  return `${indexScale.id}.${valueScale.id}.${meta.stack || meta.type}`;\n}\nfunction getUserBounds(scale) {\n  const {\n    min,\n    max,\n    minDefined,\n    maxDefined\n  } = scale.getUserBounds();\n  return {\n    min: minDefined ? min : Number.NEGATIVE_INFINITY,\n    max: maxDefined ? max : Number.POSITIVE_INFINITY\n  };\n}\nfunction getOrCreateStack(stacks, stackKey, indexValue) {\n  const subStack = stacks[stackKey] || (stacks[stackKey] = {});\n  return subStack[indexValue] || (subStack[indexValue] = {});\n}\nfunction getLastIndexInStack(stack, vScale, positive, type) {\n  for (const meta of vScale.getMatchingVisibleMetas(type).reverse()) {\n    const value = stack[meta.index];\n    if (positive && value > 0 || !positive && value < 0) {\n      return meta.index;\n    }\n  }\n  return null;\n}\nfunction updateStacks(controller, parsed) {\n  const {\n    chart,\n    _cachedMeta: meta\n  } = controller;\n  const stacks = chart._stacks || (chart._stacks = {});\n  const {\n    iScale,\n    vScale,\n    index: datasetIndex\n  } = meta;\n  const iAxis = iScale.axis;\n  const vAxis = vScale.axis;\n  const key = getStackKey(iScale, vScale, meta);\n  const ilen = parsed.length;\n  let stack;\n  for (let i = 0; i < ilen; ++i) {\n    const item = parsed[i];\n    const {\n      [iAxis]: index,\n      [vAxis]: value\n    } = item;\n    const itemStacks = item._stacks || (item._stacks = {});\n    stack = itemStacks[vAxis] = getOrCreateStack(stacks, key, index);\n    stack[datasetIndex] = value;\n    stack._top = getLastIndexInStack(stack, vScale, true, meta.type);\n    stack._bottom = getLastIndexInStack(stack, vScale, false, meta.type);\n    const visualValues = stack._visualValues || (stack._visualValues = {});\n    visualValues[datasetIndex] = value;\n  }\n}\nfunction getFirstScaleId(chart, axis) {\n  const scales = chart.scales;\n  return Object.keys(scales).filter(key => scales[key].axis === axis).shift();\n}\nfunction createDatasetContext(parent, index) {\n  return createContext(parent, {\n    active: false,\n    dataset: undefined,\n    datasetIndex: index,\n    index,\n    mode: 'default',\n    type: 'dataset'\n  });\n}\nfunction createDataContext(parent, index, element) {\n  return createContext(parent, {\n    active: false,\n    dataIndex: index,\n    parsed: undefined,\n    raw: undefined,\n    element,\n    index,\n    mode: 'default',\n    type: 'data'\n  });\n}\nfunction clearStacks(meta, items) {\n  const datasetIndex = meta.controller.index;\n  const axis = meta.vScale && meta.vScale.axis;\n  if (!axis) {\n    return;\n  }\n  items = items || meta._parsed;\n  for (const parsed of items) {\n    const stacks = parsed._stacks;\n    if (!stacks || stacks[axis] === undefined || stacks[axis][datasetIndex] === undefined) {\n      return;\n    }\n    delete stacks[axis][datasetIndex];\n    if (stacks[axis]._visualValues !== undefined && stacks[axis]._visualValues[datasetIndex] !== undefined) {\n      delete stacks[axis]._visualValues[datasetIndex];\n    }\n  }\n}\nconst isDirectUpdateMode = mode => mode === 'reset' || mode === 'none';\nconst cloneIfNotShared = (cached, shared) => shared ? cached : Object.assign({}, cached);\nconst createStack = (canStack, meta, chart) => canStack && !meta.hidden && meta._stacked && {\n  keys: getSortedDatasetIndices(chart, true),\n  values: null\n};\nlet DatasetController = /*#__PURE__*/(() => {\n  class DatasetController {\n    static defaults = {};\n    static datasetElementType = null;\n    static dataElementType = null;\n    constructor(chart, datasetIndex) {\n      this.chart = chart;\n      this._ctx = chart.ctx;\n      this.index = datasetIndex;\n      this._cachedDataOpts = {};\n      this._cachedMeta = this.getMeta();\n      this._type = this._cachedMeta.type;\n      this.options = undefined;\n      this._parsing = false;\n      this._data = undefined;\n      this._objectData = undefined;\n      this._sharedOptions = undefined;\n      this._drawStart = undefined;\n      this._drawCount = undefined;\n      this.enableOptionSharing = false;\n      this.supportsDecimation = false;\n      this.$context = undefined;\n      this._syncList = [];\n      this.datasetElementType = new.target.datasetElementType;\n      this.dataElementType = new.target.dataElementType;\n      this.initialize();\n    }\n    initialize() {\n      const meta = this._cachedMeta;\n      this.configure();\n      this.linkScales();\n      meta._stacked = isStacked(meta.vScale, meta);\n      this.addElements();\n      if (this.options.fill && !this.chart.isPluginEnabled('filler')) {\n        console.warn(\"Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options\");\n      }\n    }\n    updateIndex(datasetIndex) {\n      if (this.index !== datasetIndex) {\n        clearStacks(this._cachedMeta);\n      }\n      this.index = datasetIndex;\n    }\n    linkScales() {\n      const chart = this.chart;\n      const meta = this._cachedMeta;\n      const dataset = this.getDataset();\n      const chooseId = (axis, x, y, r) => axis === 'x' ? x : axis === 'r' ? r : y;\n      const xid = meta.xAxisID = valueOrDefault(dataset.xAxisID, getFirstScaleId(chart, 'x'));\n      const yid = meta.yAxisID = valueOrDefault(dataset.yAxisID, getFirstScaleId(chart, 'y'));\n      const rid = meta.rAxisID = valueOrDefault(dataset.rAxisID, getFirstScaleId(chart, 'r'));\n      const indexAxis = meta.indexAxis;\n      const iid = meta.iAxisID = chooseId(indexAxis, xid, yid, rid);\n      const vid = meta.vAxisID = chooseId(indexAxis, yid, xid, rid);\n      meta.xScale = this.getScaleForId(xid);\n      meta.yScale = this.getScaleForId(yid);\n      meta.rScale = this.getScaleForId(rid);\n      meta.iScale = this.getScaleForId(iid);\n      meta.vScale = this.getScaleForId(vid);\n    }\n    getDataset() {\n      return this.chart.data.datasets[this.index];\n    }\n    getMeta() {\n      return this.chart.getDatasetMeta(this.index);\n    }\n    getScaleForId(scaleID) {\n      return this.chart.scales[scaleID];\n    }\n    _getOtherScale(scale) {\n      const meta = this._cachedMeta;\n      return scale === meta.iScale ? meta.vScale : meta.iScale;\n    }\n    reset() {\n      this._update('reset');\n    }\n    _destroy() {\n      const meta = this._cachedMeta;\n      if (this._data) {\n        unlistenArrayEvents(this._data, this);\n      }\n      if (meta._stacked) {\n        clearStacks(meta);\n      }\n    }\n    _dataCheck() {\n      const dataset = this.getDataset();\n      const data = dataset.data || (dataset.data = []);\n      const _data = this._data;\n      if (isObject(data)) {\n        const meta = this._cachedMeta;\n        this._data = convertObjectDataToArray(data, meta);\n      } else if (_data !== data) {\n        if (_data) {\n          unlistenArrayEvents(_data, this);\n          const meta = this._cachedMeta;\n          clearStacks(meta);\n          meta._parsed = [];\n        }\n        if (data && Object.isExtensible(data)) {\n          listenArrayEvents(data, this);\n        }\n        this._syncList = [];\n        this._data = data;\n      }\n    }\n    addElements() {\n      const meta = this._cachedMeta;\n      this._dataCheck();\n      if (this.datasetElementType) {\n        meta.dataset = new this.datasetElementType();\n      }\n    }\n    buildOrUpdateElements(resetNewElements) {\n      const meta = this._cachedMeta;\n      const dataset = this.getDataset();\n      let stackChanged = false;\n      this._dataCheck();\n      const oldStacked = meta._stacked;\n      meta._stacked = isStacked(meta.vScale, meta);\n      if (meta.stack !== dataset.stack) {\n        stackChanged = true;\n        clearStacks(meta);\n        meta.stack = dataset.stack;\n      }\n      this._resyncElements(resetNewElements);\n      if (stackChanged || oldStacked !== meta._stacked) {\n        updateStacks(this, meta._parsed);\n      }\n    }\n    configure() {\n      const config = this.chart.config;\n      const scopeKeys = config.datasetScopeKeys(this._type);\n      const scopes = config.getOptionScopes(this.getDataset(), scopeKeys, true);\n      this.options = config.createResolver(scopes, this.getContext());\n      this._parsing = this.options.parsing;\n      this._cachedDataOpts = {};\n    }\n    parse(start, count) {\n      const {\n        _cachedMeta: meta,\n        _data: data\n      } = this;\n      const {\n        iScale,\n        _stacked\n      } = meta;\n      const iAxis = iScale.axis;\n      let sorted = start === 0 && count === data.length ? true : meta._sorted;\n      let prev = start > 0 && meta._parsed[start - 1];\n      let i, cur, parsed;\n      if (this._parsing === false) {\n        meta._parsed = data;\n        meta._sorted = true;\n        parsed = data;\n      } else {\n        if (isArray(data[start])) {\n          parsed = this.parseArrayData(meta, data, start, count);\n        } else if (isObject(data[start])) {\n          parsed = this.parseObjectData(meta, data, start, count);\n        } else {\n          parsed = this.parsePrimitiveData(meta, data, start, count);\n        }\n        const isNotInOrderComparedToPrev = () => cur[iAxis] === null || prev && cur[iAxis] < prev[iAxis];\n        for (i = 0; i < count; ++i) {\n          meta._parsed[i + start] = cur = parsed[i];\n          if (sorted) {\n            if (isNotInOrderComparedToPrev()) {\n              sorted = false;\n            }\n            prev = cur;\n          }\n        }\n        meta._sorted = sorted;\n      }\n      if (_stacked) {\n        updateStacks(this, parsed);\n      }\n    }\n    parsePrimitiveData(meta, data, start, count) {\n      const {\n        iScale,\n        vScale\n      } = meta;\n      const iAxis = iScale.axis;\n      const vAxis = vScale.axis;\n      const labels = iScale.getLabels();\n      const singleScale = iScale === vScale;\n      const parsed = new Array(count);\n      let i, ilen, index;\n      for (i = 0, ilen = count; i < ilen; ++i) {\n        index = i + start;\n        parsed[i] = {\n          [iAxis]: singleScale || iScale.parse(labels[index], index),\n          [vAxis]: vScale.parse(data[index], index)\n        };\n      }\n      return parsed;\n    }\n    parseArrayData(meta, data, start, count) {\n      const {\n        xScale,\n        yScale\n      } = meta;\n      const parsed = new Array(count);\n      let i, ilen, index, item;\n      for (i = 0, ilen = count; i < ilen; ++i) {\n        index = i + start;\n        item = data[index];\n        parsed[i] = {\n          x: xScale.parse(item[0], index),\n          y: yScale.parse(item[1], index)\n        };\n      }\n      return parsed;\n    }\n    parseObjectData(meta, data, start, count) {\n      const {\n        xScale,\n        yScale\n      } = meta;\n      const {\n        xAxisKey = 'x',\n        yAxisKey = 'y'\n      } = this._parsing;\n      const parsed = new Array(count);\n      let i, ilen, index, item;\n      for (i = 0, ilen = count; i < ilen; ++i) {\n        index = i + start;\n        item = data[index];\n        parsed[i] = {\n          x: xScale.parse(resolveObjectKey(item, xAxisKey), index),\n          y: yScale.parse(resolveObjectKey(item, yAxisKey), index)\n        };\n      }\n      return parsed;\n    }\n    getParsed(index) {\n      return this._cachedMeta._parsed[index];\n    }\n    getDataElement(index) {\n      return this._cachedMeta.data[index];\n    }\n    applyStack(scale, parsed, mode) {\n      const chart = this.chart;\n      const meta = this._cachedMeta;\n      const value = parsed[scale.axis];\n      const stack = {\n        keys: getSortedDatasetIndices(chart, true),\n        values: parsed._stacks[scale.axis]._visualValues\n      };\n      return applyStack(stack, value, meta.index, {\n        mode\n      });\n    }\n    updateRangeFromParsed(range, scale, parsed, stack) {\n      const parsedValue = parsed[scale.axis];\n      let value = parsedValue === null ? NaN : parsedValue;\n      const values = stack && parsed._stacks[scale.axis];\n      if (stack && values) {\n        stack.values = values;\n        value = applyStack(stack, parsedValue, this._cachedMeta.index);\n      }\n      range.min = Math.min(range.min, value);\n      range.max = Math.max(range.max, value);\n    }\n    getMinMax(scale, canStack) {\n      const meta = this._cachedMeta;\n      const _parsed = meta._parsed;\n      const sorted = meta._sorted && scale === meta.iScale;\n      const ilen = _parsed.length;\n      const otherScale = this._getOtherScale(scale);\n      const stack = createStack(canStack, meta, this.chart);\n      const range = {\n        min: Number.POSITIVE_INFINITY,\n        max: Number.NEGATIVE_INFINITY\n      };\n      const {\n        min: otherMin,\n        max: otherMax\n      } = getUserBounds(otherScale);\n      let i, parsed;\n      function _skip() {\n        parsed = _parsed[i];\n        const otherValue = parsed[otherScale.axis];\n        return !isNumberFinite(parsed[scale.axis]) || otherMin > otherValue || otherMax < otherValue;\n      }\n      for (i = 0; i < ilen; ++i) {\n        if (_skip()) {\n          continue;\n        }\n        this.updateRangeFromParsed(range, scale, parsed, stack);\n        if (sorted) {\n          break;\n        }\n      }\n      if (sorted) {\n        for (i = ilen - 1; i >= 0; --i) {\n          if (_skip()) {\n            continue;\n          }\n          this.updateRangeFromParsed(range, scale, parsed, stack);\n          break;\n        }\n      }\n      return range;\n    }\n    getAllParsedValues(scale) {\n      const parsed = this._cachedMeta._parsed;\n      const values = [];\n      let i, ilen, value;\n      for (i = 0, ilen = parsed.length; i < ilen; ++i) {\n        value = parsed[i][scale.axis];\n        if (isNumberFinite(value)) {\n          values.push(value);\n        }\n      }\n      return values;\n    }\n    getMaxOverflow() {\n      return false;\n    }\n    getLabelAndValue(index) {\n      const meta = this._cachedMeta;\n      const iScale = meta.iScale;\n      const vScale = meta.vScale;\n      const parsed = this.getParsed(index);\n      return {\n        label: iScale ? '' + iScale.getLabelForValue(parsed[iScale.axis]) : '',\n        value: vScale ? '' + vScale.getLabelForValue(parsed[vScale.axis]) : ''\n      };\n    }\n    _update(mode) {\n      const meta = this._cachedMeta;\n      this.update(mode || 'default');\n      meta._clip = toClip(valueOrDefault(this.options.clip, defaultClip(meta.xScale, meta.yScale, this.getMaxOverflow())));\n    }\n    update(mode) {}\n    draw() {\n      const ctx = this._ctx;\n      const chart = this.chart;\n      const meta = this._cachedMeta;\n      const elements = meta.data || [];\n      const area = chart.chartArea;\n      const active = [];\n      const start = this._drawStart || 0;\n      const count = this._drawCount || elements.length - start;\n      const drawActiveElementsOnTop = this.options.drawActiveElementsOnTop;\n      let i;\n      if (meta.dataset) {\n        meta.dataset.draw(ctx, area, start, count);\n      }\n      for (i = start; i < start + count; ++i) {\n        const element = elements[i];\n        if (element.hidden) {\n          continue;\n        }\n        if (element.active && drawActiveElementsOnTop) {\n          active.push(element);\n        } else {\n          element.draw(ctx, area);\n        }\n      }\n      for (i = 0; i < active.length; ++i) {\n        active[i].draw(ctx, area);\n      }\n    }\n    getStyle(index, active) {\n      const mode = active ? 'active' : 'default';\n      return index === undefined && this._cachedMeta.dataset ? this.resolveDatasetElementOptions(mode) : this.resolveDataElementOptions(index || 0, mode);\n    }\n    getContext(index, active, mode) {\n      const dataset = this.getDataset();\n      let context;\n      if (index >= 0 && index < this._cachedMeta.data.length) {\n        const element = this._cachedMeta.data[index];\n        context = element.$context || (element.$context = createDataContext(this.getContext(), index, element));\n        context.parsed = this.getParsed(index);\n        context.raw = dataset.data[index];\n        context.index = context.dataIndex = index;\n      } else {\n        context = this.$context || (this.$context = createDatasetContext(this.chart.getContext(), this.index));\n        context.dataset = dataset;\n        context.index = context.datasetIndex = this.index;\n      }\n      context.active = !!active;\n      context.mode = mode;\n      return context;\n    }\n    resolveDatasetElementOptions(mode) {\n      return this._resolveElementOptions(this.datasetElementType.id, mode);\n    }\n    resolveDataElementOptions(index, mode) {\n      return this._resolveElementOptions(this.dataElementType.id, mode, index);\n    }\n    _resolveElementOptions(elementType, mode = 'default', index) {\n      const active = mode === 'active';\n      const cache = this._cachedDataOpts;\n      const cacheKey = elementType + '-' + mode;\n      const cached = cache[cacheKey];\n      const sharing = this.enableOptionSharing && defined(index);\n      if (cached) {\n        return cloneIfNotShared(cached, sharing);\n      }\n      const config = this.chart.config;\n      const scopeKeys = config.datasetElementScopeKeys(this._type, elementType);\n      const prefixes = active ? [`${elementType}Hover`, 'hover', elementType, ''] : [elementType, ''];\n      const scopes = config.getOptionScopes(this.getDataset(), scopeKeys);\n      const names = Object.keys(defaults.elements[elementType]);\n      const context = () => this.getContext(index, active, mode);\n      const values = config.resolveNamedOptions(scopes, names, context, prefixes);\n      if (values.$shared) {\n        values.$shared = sharing;\n        cache[cacheKey] = Object.freeze(cloneIfNotShared(values, sharing));\n      }\n      return values;\n    }\n    _resolveAnimations(index, transition, active) {\n      const chart = this.chart;\n      const cache = this._cachedDataOpts;\n      const cacheKey = `animation-${transition}`;\n      const cached = cache[cacheKey];\n      if (cached) {\n        return cached;\n      }\n      let options;\n      if (chart.options.animation !== false) {\n        const config = this.chart.config;\n        const scopeKeys = config.datasetAnimationScopeKeys(this._type, transition);\n        const scopes = config.getOptionScopes(this.getDataset(), scopeKeys);\n        options = config.createResolver(scopes, this.getContext(index, active, transition));\n      }\n      const animations = new Animations(chart, options && options.animations);\n      if (options && options._cacheable) {\n        cache[cacheKey] = Object.freeze(animations);\n      }\n      return animations;\n    }\n    getSharedOptions(options) {\n      if (!options.$shared) {\n        return;\n      }\n      return this._sharedOptions || (this._sharedOptions = Object.assign({}, options));\n    }\n    includeOptions(mode, sharedOptions) {\n      return !sharedOptions || isDirectUpdateMode(mode) || this.chart._animationsDisabled;\n    }\n    _getSharedOptions(start, mode) {\n      const firstOpts = this.resolveDataElementOptions(start, mode);\n      const previouslySharedOptions = this._sharedOptions;\n      const sharedOptions = this.getSharedOptions(firstOpts);\n      const includeOptions = this.includeOptions(mode, sharedOptions) || sharedOptions !== previouslySharedOptions;\n      this.updateSharedOptions(sharedOptions, mode, firstOpts);\n      return {\n        sharedOptions,\n        includeOptions\n      };\n    }\n    updateElement(element, index, properties, mode) {\n      if (isDirectUpdateMode(mode)) {\n        Object.assign(element, properties);\n      } else {\n        this._resolveAnimations(index, mode).update(element, properties);\n      }\n    }\n    updateSharedOptions(sharedOptions, mode, newOptions) {\n      if (sharedOptions && !isDirectUpdateMode(mode)) {\n        this._resolveAnimations(undefined, mode).update(sharedOptions, newOptions);\n      }\n    }\n    _setStyle(element, index, mode, active) {\n      element.active = active;\n      const options = this.getStyle(index, active);\n      this._resolveAnimations(index, mode, active).update(element, {\n        options: !active && this.getSharedOptions(options) || options\n      });\n    }\n    removeHoverStyle(element, datasetIndex, index) {\n      this._setStyle(element, index, 'active', false);\n    }\n    setHoverStyle(element, datasetIndex, index) {\n      this._setStyle(element, index, 'active', true);\n    }\n    _removeDatasetHoverStyle() {\n      const element = this._cachedMeta.dataset;\n      if (element) {\n        this._setStyle(element, undefined, 'active', false);\n      }\n    }\n    _setDatasetHoverStyle() {\n      const element = this._cachedMeta.dataset;\n      if (element) {\n        this._setStyle(element, undefined, 'active', true);\n      }\n    }\n    _resyncElements(resetNewElements) {\n      const data = this._data;\n      const elements = this._cachedMeta.data;\n      for (const [method, arg1, arg2] of this._syncList) {\n        this[method](arg1, arg2);\n      }\n      this._syncList = [];\n      const numMeta = elements.length;\n      const numData = data.length;\n      const count = Math.min(numData, numMeta);\n      if (count) {\n        this.parse(0, count);\n      }\n      if (numData > numMeta) {\n        this._insertElements(numMeta, numData - numMeta, resetNewElements);\n      } else if (numData < numMeta) {\n        this._removeElements(numData, numMeta - numData);\n      }\n    }\n    _insertElements(start, count, resetNewElements = true) {\n      const meta = this._cachedMeta;\n      const data = meta.data;\n      const end = start + count;\n      let i;\n      const move = arr => {\n        arr.length += count;\n        for (i = arr.length - 1; i >= end; i--) {\n          arr[i] = arr[i - count];\n        }\n      };\n      move(data);\n      for (i = start; i < end; ++i) {\n        data[i] = new this.dataElementType();\n      }\n      if (this._parsing) {\n        move(meta._parsed);\n      }\n      this.parse(start, count);\n      if (resetNewElements) {\n        this.updateElements(data, start, count, 'reset');\n      }\n    }\n    updateElements(element, start, count, mode) {}\n    _removeElements(start, count) {\n      const meta = this._cachedMeta;\n      if (this._parsing) {\n        const removed = meta._parsed.splice(start, count);\n        if (meta._stacked) {\n          clearStacks(meta, removed);\n        }\n      }\n      meta.data.splice(start, count);\n    }\n    _sync(args) {\n      if (this._parsing) {\n        this._syncList.push(args);\n      } else {\n        const [method, arg1, arg2] = args;\n        this[method](arg1, arg2);\n      }\n      this.chart._dataChanges.push([this.index, ...args]);\n    }\n    _onDataPush() {\n      const count = arguments.length;\n      this._sync(['_insertElements', this.getDataset().data.length - count, count]);\n    }\n    _onDataPop() {\n      this._sync(['_removeElements', this._cachedMeta.data.length - 1, 1]);\n    }\n    _onDataShift() {\n      this._sync(['_removeElements', 0, 1]);\n    }\n    _onDataSplice(start, count) {\n      if (count) {\n        this._sync(['_removeElements', start, count]);\n      }\n      const newCount = arguments.length - 2;\n      if (newCount) {\n        this._sync(['_insertElements', start, newCount]);\n      }\n    }\n    _onDataUnshift() {\n      this._sync(['_insertElements', 0, arguments.length]);\n    }\n  }\n  return DatasetController;\n})();\nfunction getAllScaleValues(scale, type) {\n  if (!scale._cache.$bar) {\n    const visibleMetas = scale.getMatchingVisibleMetas(type);\n    let values = [];\n    for (let i = 0, ilen = visibleMetas.length; i < ilen; i++) {\n      values = values.concat(visibleMetas[i].controller.getAllParsedValues(scale));\n    }\n    scale._cache.$bar = _arrayUnique(values.sort((a, b) => a - b));\n  }\n  return scale._cache.$bar;\n}\nfunction computeMinSampleSize(meta) {\n  const scale = meta.iScale;\n  const values = getAllScaleValues(scale, meta.type);\n  let min = scale._length;\n  let i, ilen, curr, prev;\n  const updateMinAndPrev = () => {\n    if (curr === 32767 || curr === -32768) {\n      return;\n    }\n    if (defined(prev)) {\n      min = Math.min(min, Math.abs(curr - prev) || min);\n    }\n    prev = curr;\n  };\n  for (i = 0, ilen = values.length; i < ilen; ++i) {\n    curr = scale.getPixelForValue(values[i]);\n    updateMinAndPrev();\n  }\n  prev = undefined;\n  for (i = 0, ilen = scale.ticks.length; i < ilen; ++i) {\n    curr = scale.getPixelForTick(i);\n    updateMinAndPrev();\n  }\n  return min;\n}\nfunction computeFitCategoryTraits(index, ruler, options, stackCount) {\n  const thickness = options.barThickness;\n  let size, ratio;\n  if (isNullOrUndef(thickness)) {\n    size = ruler.min * options.categoryPercentage;\n    ratio = options.barPercentage;\n  } else {\n    size = thickness * stackCount;\n    ratio = 1;\n  }\n  return {\n    chunk: size / stackCount,\n    ratio,\n    start: ruler.pixels[index] - size / 2\n  };\n}\nfunction computeFlexCategoryTraits(index, ruler, options, stackCount) {\n  const pixels = ruler.pixels;\n  const curr = pixels[index];\n  let prev = index > 0 ? pixels[index - 1] : null;\n  let next = index < pixels.length - 1 ? pixels[index + 1] : null;\n  const percent = options.categoryPercentage;\n  if (prev === null) {\n    prev = curr - (next === null ? ruler.end - ruler.start : next - curr);\n  }\n  if (next === null) {\n    next = curr + curr - prev;\n  }\n  const start = curr - (curr - Math.min(prev, next)) / 2 * percent;\n  const size = Math.abs(next - prev) / 2 * percent;\n  return {\n    chunk: size / stackCount,\n    ratio: options.barPercentage,\n    start\n  };\n}\nfunction parseFloatBar(entry, item, vScale, i) {\n  const startValue = vScale.parse(entry[0], i);\n  const endValue = vScale.parse(entry[1], i);\n  const min = Math.min(startValue, endValue);\n  const max = Math.max(startValue, endValue);\n  let barStart = min;\n  let barEnd = max;\n  if (Math.abs(min) > Math.abs(max)) {\n    barStart = max;\n    barEnd = min;\n  }\n  item[vScale.axis] = barEnd;\n  item._custom = {\n    barStart,\n    barEnd,\n    start: startValue,\n    end: endValue,\n    min,\n    max\n  };\n}\nfunction parseValue(entry, item, vScale, i) {\n  if (isArray(entry)) {\n    parseFloatBar(entry, item, vScale, i);\n  } else {\n    item[vScale.axis] = vScale.parse(entry, i);\n  }\n  return item;\n}\nfunction parseArrayOrPrimitive(meta, data, start, count) {\n  const iScale = meta.iScale;\n  const vScale = meta.vScale;\n  const labels = iScale.getLabels();\n  const singleScale = iScale === vScale;\n  const parsed = [];\n  let i, ilen, item, entry;\n  for (i = start, ilen = start + count; i < ilen; ++i) {\n    entry = data[i];\n    item = {};\n    item[iScale.axis] = singleScale || iScale.parse(labels[i], i);\n    parsed.push(parseValue(entry, item, vScale, i));\n  }\n  return parsed;\n}\nfunction isFloatBar(custom) {\n  return custom && custom.barStart !== undefined && custom.barEnd !== undefined;\n}\nfunction barSign(size, vScale, actualBase) {\n  if (size !== 0) {\n    return sign(size);\n  }\n  return (vScale.isHorizontal() ? 1 : -1) * (vScale.min >= actualBase ? 1 : -1);\n}\nfunction borderProps(properties) {\n  let reverse, start, end, top, bottom;\n  if (properties.horizontal) {\n    reverse = properties.base > properties.x;\n    start = 'left';\n    end = 'right';\n  } else {\n    reverse = properties.base < properties.y;\n    start = 'bottom';\n    end = 'top';\n  }\n  if (reverse) {\n    top = 'end';\n    bottom = 'start';\n  } else {\n    top = 'start';\n    bottom = 'end';\n  }\n  return {\n    start,\n    end,\n    reverse,\n    top,\n    bottom\n  };\n}\nfunction setBorderSkipped(properties, options, stack, index) {\n  let edge = options.borderSkipped;\n  const res = {};\n  if (!edge) {\n    properties.borderSkipped = res;\n    return;\n  }\n  if (edge === true) {\n    properties.borderSkipped = {\n      top: true,\n      right: true,\n      bottom: true,\n      left: true\n    };\n    return;\n  }\n  const {\n    start,\n    end,\n    reverse,\n    top,\n    bottom\n  } = borderProps(properties);\n  if (edge === 'middle' && stack) {\n    properties.enableBorderRadius = true;\n    if ((stack._top || 0) === index) {\n      edge = top;\n    } else if ((stack._bottom || 0) === index) {\n      edge = bottom;\n    } else {\n      res[parseEdge(bottom, start, end, reverse)] = true;\n      edge = top;\n    }\n  }\n  res[parseEdge(edge, start, end, reverse)] = true;\n  properties.borderSkipped = res;\n}\nfunction parseEdge(edge, a, b, reverse) {\n  if (reverse) {\n    edge = swap(edge, a, b);\n    edge = startEnd(edge, b, a);\n  } else {\n    edge = startEnd(edge, a, b);\n  }\n  return edge;\n}\nfunction swap(orig, v1, v2) {\n  return orig === v1 ? v2 : orig === v2 ? v1 : orig;\n}\nfunction startEnd(v, start, end) {\n  return v === 'start' ? start : v === 'end' ? end : v;\n}\nfunction setInflateAmount(properties, {\n  inflateAmount\n}, ratio) {\n  properties.inflateAmount = inflateAmount === 'auto' ? ratio === 1 ? 0.33 : 0 : inflateAmount;\n}\nlet BarController = /*#__PURE__*/(() => {\n  class BarController extends DatasetController {\n    static id = 'bar';\n    static defaults = {\n      datasetElementType: false,\n      dataElementType: 'bar',\n      categoryPercentage: 0.8,\n      barPercentage: 0.9,\n      grouped: true,\n      animations: {\n        numbers: {\n          type: 'number',\n          properties: ['x', 'y', 'base', 'width', 'height']\n        }\n      }\n    };\n    static overrides = {\n      scales: {\n        _index_: {\n          type: 'category',\n          offset: true,\n          grid: {\n            offset: true\n          }\n        },\n        _value_: {\n          type: 'linear',\n          beginAtZero: true\n        }\n      }\n    };\n    parsePrimitiveData(meta, data, start, count) {\n      return parseArrayOrPrimitive(meta, data, start, count);\n    }\n    parseArrayData(meta, data, start, count) {\n      return parseArrayOrPrimitive(meta, data, start, count);\n    }\n    parseObjectData(meta, data, start, count) {\n      const {\n        iScale,\n        vScale\n      } = meta;\n      const {\n        xAxisKey = 'x',\n        yAxisKey = 'y'\n      } = this._parsing;\n      const iAxisKey = iScale.axis === 'x' ? xAxisKey : yAxisKey;\n      const vAxisKey = vScale.axis === 'x' ? xAxisKey : yAxisKey;\n      const parsed = [];\n      let i, ilen, item, obj;\n      for (i = start, ilen = start + count; i < ilen; ++i) {\n        obj = data[i];\n        item = {};\n        item[iScale.axis] = iScale.parse(resolveObjectKey(obj, iAxisKey), i);\n        parsed.push(parseValue(resolveObjectKey(obj, vAxisKey), item, vScale, i));\n      }\n      return parsed;\n    }\n    updateRangeFromParsed(range, scale, parsed, stack) {\n      super.updateRangeFromParsed(range, scale, parsed, stack);\n      const custom = parsed._custom;\n      if (custom && scale === this._cachedMeta.vScale) {\n        range.min = Math.min(range.min, custom.min);\n        range.max = Math.max(range.max, custom.max);\n      }\n    }\n    getMaxOverflow() {\n      return 0;\n    }\n    getLabelAndValue(index) {\n      const meta = this._cachedMeta;\n      const {\n        iScale,\n        vScale\n      } = meta;\n      const parsed = this.getParsed(index);\n      const custom = parsed._custom;\n      const value = isFloatBar(custom) ? '[' + custom.start + ', ' + custom.end + ']' : '' + vScale.getLabelForValue(parsed[vScale.axis]);\n      return {\n        label: '' + iScale.getLabelForValue(parsed[iScale.axis]),\n        value\n      };\n    }\n    initialize() {\n      this.enableOptionSharing = true;\n      super.initialize();\n      const meta = this._cachedMeta;\n      meta.stack = this.getDataset().stack;\n    }\n    update(mode) {\n      const meta = this._cachedMeta;\n      this.updateElements(meta.data, 0, meta.data.length, mode);\n    }\n    updateElements(bars, start, count, mode) {\n      const reset = mode === 'reset';\n      const {\n        index,\n        _cachedMeta: {\n          vScale\n        }\n      } = this;\n      const base = vScale.getBasePixel();\n      const horizontal = vScale.isHorizontal();\n      const ruler = this._getRuler();\n      const {\n        sharedOptions,\n        includeOptions\n      } = this._getSharedOptions(start, mode);\n      for (let i = start; i < start + count; i++) {\n        const parsed = this.getParsed(i);\n        const vpixels = reset || isNullOrUndef(parsed[vScale.axis]) ? {\n          base,\n          head: base\n        } : this._calculateBarValuePixels(i);\n        const ipixels = this._calculateBarIndexPixels(i, ruler);\n        const stack = (parsed._stacks || {})[vScale.axis];\n        const properties = {\n          horizontal,\n          base: vpixels.base,\n          enableBorderRadius: !stack || isFloatBar(parsed._custom) || index === stack._top || index === stack._bottom,\n          x: horizontal ? vpixels.head : ipixels.center,\n          y: horizontal ? ipixels.center : vpixels.head,\n          height: horizontal ? ipixels.size : Math.abs(vpixels.size),\n          width: horizontal ? Math.abs(vpixels.size) : ipixels.size\n        };\n        if (includeOptions) {\n          properties.options = sharedOptions || this.resolveDataElementOptions(i, bars[i].active ? 'active' : mode);\n        }\n        const options = properties.options || bars[i].options;\n        setBorderSkipped(properties, options, stack, index);\n        setInflateAmount(properties, options, ruler.ratio);\n        this.updateElement(bars[i], i, properties, mode);\n      }\n    }\n    _getStacks(last, dataIndex) {\n      const {\n        iScale\n      } = this._cachedMeta;\n      const metasets = iScale.getMatchingVisibleMetas(this._type).filter(meta => meta.controller.options.grouped);\n      const stacked = iScale.options.stacked;\n      const stacks = [];\n      const currentParsed = this._cachedMeta.controller.getParsed(dataIndex);\n      const iScaleValue = currentParsed && currentParsed[iScale.axis];\n      const skipNull = meta => {\n        const parsed = meta._parsed.find(item => item[iScale.axis] === iScaleValue);\n        const val = parsed && parsed[meta.vScale.axis];\n        if (isNullOrUndef(val) || isNaN(val)) {\n          return true;\n        }\n      };\n      for (const meta of metasets) {\n        if (dataIndex !== undefined && skipNull(meta)) {\n          continue;\n        }\n        if (stacked === false || stacks.indexOf(meta.stack) === -1 || stacked === undefined && meta.stack === undefined) {\n          stacks.push(meta.stack);\n        }\n        if (meta.index === last) {\n          break;\n        }\n      }\n      if (!stacks.length) {\n        stacks.push(undefined);\n      }\n      return stacks;\n    }\n    _getStackCount(index) {\n      return this._getStacks(undefined, index).length;\n    }\n    _getStackIndex(datasetIndex, name, dataIndex) {\n      const stacks = this._getStacks(datasetIndex, dataIndex);\n      const index = name !== undefined ? stacks.indexOf(name) : -1;\n      return index === -1 ? stacks.length - 1 : index;\n    }\n    _getRuler() {\n      const opts = this.options;\n      const meta = this._cachedMeta;\n      const iScale = meta.iScale;\n      const pixels = [];\n      let i, ilen;\n      for (i = 0, ilen = meta.data.length; i < ilen; ++i) {\n        pixels.push(iScale.getPixelForValue(this.getParsed(i)[iScale.axis], i));\n      }\n      const barThickness = opts.barThickness;\n      const min = barThickness || computeMinSampleSize(meta);\n      return {\n        min,\n        pixels,\n        start: iScale._startPixel,\n        end: iScale._endPixel,\n        stackCount: this._getStackCount(),\n        scale: iScale,\n        grouped: opts.grouped,\n        ratio: barThickness ? 1 : opts.categoryPercentage * opts.barPercentage\n      };\n    }\n    _calculateBarValuePixels(index) {\n      const {\n        _cachedMeta: {\n          vScale,\n          _stacked,\n          index: datasetIndex\n        },\n        options: {\n          base: baseValue,\n          minBarLength\n        }\n      } = this;\n      const actualBase = baseValue || 0;\n      const parsed = this.getParsed(index);\n      const custom = parsed._custom;\n      const floating = isFloatBar(custom);\n      let value = parsed[vScale.axis];\n      let start = 0;\n      let length = _stacked ? this.applyStack(vScale, parsed, _stacked) : value;\n      let head, size;\n      if (length !== value) {\n        start = length - value;\n        length = value;\n      }\n      if (floating) {\n        value = custom.barStart;\n        length = custom.barEnd - custom.barStart;\n        if (value !== 0 && sign(value) !== sign(custom.barEnd)) {\n          start = 0;\n        }\n        start += value;\n      }\n      const startValue = !isNullOrUndef(baseValue) && !floating ? baseValue : start;\n      let base = vScale.getPixelForValue(startValue);\n      if (this.chart.getDataVisibility(index)) {\n        head = vScale.getPixelForValue(start + length);\n      } else {\n        head = base;\n      }\n      size = head - base;\n      if (Math.abs(size) < minBarLength) {\n        size = barSign(size, vScale, actualBase) * minBarLength;\n        if (value === actualBase) {\n          base -= size / 2;\n        }\n        const startPixel = vScale.getPixelForDecimal(0);\n        const endPixel = vScale.getPixelForDecimal(1);\n        const min = Math.min(startPixel, endPixel);\n        const max = Math.max(startPixel, endPixel);\n        base = Math.max(Math.min(base, max), min);\n        head = base + size;\n        if (_stacked && !floating) {\n          parsed._stacks[vScale.axis]._visualValues[datasetIndex] = vScale.getValueForPixel(head) - vScale.getValueForPixel(base);\n        }\n      }\n      if (base === vScale.getPixelForValue(actualBase)) {\n        const halfGrid = sign(size) * vScale.getLineWidthForValue(actualBase) / 2;\n        base += halfGrid;\n        size -= halfGrid;\n      }\n      return {\n        size,\n        base,\n        head,\n        center: head + size / 2\n      };\n    }\n    _calculateBarIndexPixels(index, ruler) {\n      const scale = ruler.scale;\n      const options = this.options;\n      const skipNull = options.skipNull;\n      const maxBarThickness = valueOrDefault(options.maxBarThickness, Infinity);\n      let center, size;\n      if (ruler.grouped) {\n        const stackCount = skipNull ? this._getStackCount(index) : ruler.stackCount;\n        const range = options.barThickness === 'flex' ? computeFlexCategoryTraits(index, ruler, options, stackCount) : computeFitCategoryTraits(index, ruler, options, stackCount);\n        const stackIndex = this._getStackIndex(this.index, this._cachedMeta.stack, skipNull ? index : undefined);\n        center = range.start + range.chunk * stackIndex + range.chunk / 2;\n        size = Math.min(maxBarThickness, range.chunk * range.ratio);\n      } else {\n        center = scale.getPixelForValue(this.getParsed(index)[scale.axis], index);\n        size = Math.min(maxBarThickness, ruler.min * ruler.ratio);\n      }\n      return {\n        base: center - size / 2,\n        head: center + size / 2,\n        center,\n        size\n      };\n    }\n    draw() {\n      const meta = this._cachedMeta;\n      const vScale = meta.vScale;\n      const rects = meta.data;\n      const ilen = rects.length;\n      let i = 0;\n      for (; i < ilen; ++i) {\n        if (this.getParsed(i)[vScale.axis] !== null && !rects[i].hidden) {\n          rects[i].draw(this._ctx);\n        }\n      }\n    }\n  }\n  return BarController;\n})();\nlet BubbleController = /*#__PURE__*/(() => {\n  class BubbleController extends DatasetController {\n    static id = 'bubble';\n    static defaults = {\n      datasetElementType: false,\n      dataElementType: 'point',\n      animations: {\n        numbers: {\n          type: 'number',\n          properties: ['x', 'y', 'borderWidth', 'radius']\n        }\n      }\n    };\n    static overrides = {\n      scales: {\n        x: {\n          type: 'linear'\n        },\n        y: {\n          type: 'linear'\n        }\n      }\n    };\n    initialize() {\n      this.enableOptionSharing = true;\n      super.initialize();\n    }\n    parsePrimitiveData(meta, data, start, count) {\n      const parsed = super.parsePrimitiveData(meta, data, start, count);\n      for (let i = 0; i < parsed.length; i++) {\n        parsed[i]._custom = this.resolveDataElementOptions(i + start).radius;\n      }\n      return parsed;\n    }\n    parseArrayData(meta, data, start, count) {\n      const parsed = super.parseArrayData(meta, data, start, count);\n      for (let i = 0; i < parsed.length; i++) {\n        const item = data[start + i];\n        parsed[i]._custom = valueOrDefault(item[2], this.resolveDataElementOptions(i + start).radius);\n      }\n      return parsed;\n    }\n    parseObjectData(meta, data, start, count) {\n      const parsed = super.parseObjectData(meta, data, start, count);\n      for (let i = 0; i < parsed.length; i++) {\n        const item = data[start + i];\n        parsed[i]._custom = valueOrDefault(item && item.r && +item.r, this.resolveDataElementOptions(i + start).radius);\n      }\n      return parsed;\n    }\n    getMaxOverflow() {\n      const data = this._cachedMeta.data;\n      let max = 0;\n      for (let i = data.length - 1; i >= 0; --i) {\n        max = Math.max(max, data[i].size(this.resolveDataElementOptions(i)) / 2);\n      }\n      return max > 0 && max;\n    }\n    getLabelAndValue(index) {\n      const meta = this._cachedMeta;\n      const labels = this.chart.data.labels || [];\n      const {\n        xScale,\n        yScale\n      } = meta;\n      const parsed = this.getParsed(index);\n      const x = xScale.getLabelForValue(parsed.x);\n      const y = yScale.getLabelForValue(parsed.y);\n      const r = parsed._custom;\n      return {\n        label: labels[index] || '',\n        value: '(' + x + ', ' + y + (r ? ', ' + r : '') + ')'\n      };\n    }\n    update(mode) {\n      const points = this._cachedMeta.data;\n      this.updateElements(points, 0, points.length, mode);\n    }\n    updateElements(points, start, count, mode) {\n      const reset = mode === 'reset';\n      const {\n        iScale,\n        vScale\n      } = this._cachedMeta;\n      const {\n        sharedOptions,\n        includeOptions\n      } = this._getSharedOptions(start, mode);\n      const iAxis = iScale.axis;\n      const vAxis = vScale.axis;\n      for (let i = start; i < start + count; i++) {\n        const point = points[i];\n        const parsed = !reset && this.getParsed(i);\n        const properties = {};\n        const iPixel = properties[iAxis] = reset ? iScale.getPixelForDecimal(0.5) : iScale.getPixelForValue(parsed[iAxis]);\n        const vPixel = properties[vAxis] = reset ? vScale.getBasePixel() : vScale.getPixelForValue(parsed[vAxis]);\n        properties.skip = isNaN(iPixel) || isNaN(vPixel);\n        if (includeOptions) {\n          properties.options = sharedOptions || this.resolveDataElementOptions(i, point.active ? 'active' : mode);\n          if (reset) {\n            properties.options.radius = 0;\n          }\n        }\n        this.updateElement(point, i, properties, mode);\n      }\n    }\n    resolveDataElementOptions(index, mode) {\n      const parsed = this.getParsed(index);\n      let values = super.resolveDataElementOptions(index, mode);\n      if (values.$shared) {\n        values = Object.assign({}, values, {\n          $shared: false\n        });\n      }\n      const radius = values.radius;\n      if (mode !== 'active') {\n        values.radius = 0;\n      }\n      values.radius += valueOrDefault(parsed && parsed._custom, radius);\n      return values;\n    }\n  }\n  return BubbleController;\n})();\nfunction getRatioAndOffset(rotation, circumference, cutout) {\n  let ratioX = 1;\n  let ratioY = 1;\n  let offsetX = 0;\n  let offsetY = 0;\n  if (circumference < TAU) {\n    const startAngle = rotation;\n    const endAngle = startAngle + circumference;\n    const startX = Math.cos(startAngle);\n    const startY = Math.sin(startAngle);\n    const endX = Math.cos(endAngle);\n    const endY = Math.sin(endAngle);\n    const calcMax = (angle, a, b) => _angleBetween(angle, startAngle, endAngle, true) ? 1 : Math.max(a, a * cutout, b, b * cutout);\n    const calcMin = (angle, a, b) => _angleBetween(angle, startAngle, endAngle, true) ? -1 : Math.min(a, a * cutout, b, b * cutout);\n    const maxX = calcMax(0, startX, endX);\n    const maxY = calcMax(HALF_PI, startY, endY);\n    const minX = calcMin(PI, startX, endX);\n    const minY = calcMin(PI + HALF_PI, startY, endY);\n    ratioX = (maxX - minX) / 2;\n    ratioY = (maxY - minY) / 2;\n    offsetX = -(maxX + minX) / 2;\n    offsetY = -(maxY + minY) / 2;\n  }\n  return {\n    ratioX,\n    ratioY,\n    offsetX,\n    offsetY\n  };\n}\nlet DoughnutController = /*#__PURE__*/(() => {\n  class DoughnutController extends DatasetController {\n    static id = 'doughnut';\n    static defaults = {\n      datasetElementType: false,\n      dataElementType: 'arc',\n      animation: {\n        animateRotate: true,\n        animateScale: false\n      },\n      animations: {\n        numbers: {\n          type: 'number',\n          properties: ['circumference', 'endAngle', 'innerRadius', 'outerRadius', 'startAngle', 'x', 'y', 'offset', 'borderWidth', 'spacing']\n        }\n      },\n      cutout: '50%',\n      rotation: 0,\n      circumference: 360,\n      radius: '100%',\n      spacing: 0,\n      indexAxis: 'r'\n    };\n    static descriptors = {\n      _scriptable: name => name !== 'spacing',\n      _indexable: name => name !== 'spacing' && !name.startsWith('borderDash') && !name.startsWith('hoverBorderDash')\n    };\n    static overrides = {\n      aspectRatio: 1,\n      plugins: {\n        legend: {\n          labels: {\n            generateLabels(chart) {\n              const data = chart.data;\n              if (data.labels.length && data.datasets.length) {\n                const {\n                  labels: {\n                    pointStyle,\n                    color\n                  }\n                } = chart.legend.options;\n                return data.labels.map((label, i) => {\n                  const meta = chart.getDatasetMeta(0);\n                  const style = meta.controller.getStyle(i);\n                  return {\n                    text: label,\n                    fillStyle: style.backgroundColor,\n                    strokeStyle: style.borderColor,\n                    fontColor: color,\n                    lineWidth: style.borderWidth,\n                    pointStyle: pointStyle,\n                    hidden: !chart.getDataVisibility(i),\n                    index: i\n                  };\n                });\n              }\n              return [];\n            }\n          },\n          onClick(e, legendItem, legend) {\n            legend.chart.toggleDataVisibility(legendItem.index);\n            legend.chart.update();\n          }\n        }\n      }\n    };\n    constructor(chart, datasetIndex) {\n      super(chart, datasetIndex);\n      this.enableOptionSharing = true;\n      this.innerRadius = undefined;\n      this.outerRadius = undefined;\n      this.offsetX = undefined;\n      this.offsetY = undefined;\n    }\n    linkScales() {}\n    parse(start, count) {\n      const data = this.getDataset().data;\n      const meta = this._cachedMeta;\n      if (this._parsing === false) {\n        meta._parsed = data;\n      } else {\n        let getter = i => +data[i];\n        if (isObject(data[start])) {\n          const {\n            key = 'value'\n          } = this._parsing;\n          getter = i => +resolveObjectKey(data[i], key);\n        }\n        let i, ilen;\n        for (i = start, ilen = start + count; i < ilen; ++i) {\n          meta._parsed[i] = getter(i);\n        }\n      }\n    }\n    _getRotation() {\n      return toRadians(this.options.rotation - 90);\n    }\n    _getCircumference() {\n      return toRadians(this.options.circumference);\n    }\n    _getRotationExtents() {\n      let min = TAU;\n      let max = -TAU;\n      for (let i = 0; i < this.chart.data.datasets.length; ++i) {\n        if (this.chart.isDatasetVisible(i) && this.chart.getDatasetMeta(i).type === this._type) {\n          const controller = this.chart.getDatasetMeta(i).controller;\n          const rotation = controller._getRotation();\n          const circumference = controller._getCircumference();\n          min = Math.min(min, rotation);\n          max = Math.max(max, rotation + circumference);\n        }\n      }\n      return {\n        rotation: min,\n        circumference: max - min\n      };\n    }\n    update(mode) {\n      const chart = this.chart;\n      const {\n        chartArea\n      } = chart;\n      const meta = this._cachedMeta;\n      const arcs = meta.data;\n      const spacing = this.getMaxBorderWidth() + this.getMaxOffset(arcs) + this.options.spacing;\n      const maxSize = Math.max((Math.min(chartArea.width, chartArea.height) - spacing) / 2, 0);\n      const cutout = Math.min(toPercentage(this.options.cutout, maxSize), 1);\n      const chartWeight = this._getRingWeight(this.index);\n      const {\n        circumference,\n        rotation\n      } = this._getRotationExtents();\n      const {\n        ratioX,\n        ratioY,\n        offsetX,\n        offsetY\n      } = getRatioAndOffset(rotation, circumference, cutout);\n      const maxWidth = (chartArea.width - spacing) / ratioX;\n      const maxHeight = (chartArea.height - spacing) / ratioY;\n      const maxRadius = Math.max(Math.min(maxWidth, maxHeight) / 2, 0);\n      const outerRadius = toDimension(this.options.radius, maxRadius);\n      const innerRadius = Math.max(outerRadius * cutout, 0);\n      const radiusLength = (outerRadius - innerRadius) / this._getVisibleDatasetWeightTotal();\n      this.offsetX = offsetX * outerRadius;\n      this.offsetY = offsetY * outerRadius;\n      meta.total = this.calculateTotal();\n      this.outerRadius = outerRadius - radiusLength * this._getRingWeightOffset(this.index);\n      this.innerRadius = Math.max(this.outerRadius - radiusLength * chartWeight, 0);\n      this.updateElements(arcs, 0, arcs.length, mode);\n    }\n    _circumference(i, reset) {\n      const opts = this.options;\n      const meta = this._cachedMeta;\n      const circumference = this._getCircumference();\n      if (reset && opts.animation.animateRotate || !this.chart.getDataVisibility(i) || meta._parsed[i] === null || meta.data[i].hidden) {\n        return 0;\n      }\n      return this.calculateCircumference(meta._parsed[i] * circumference / TAU);\n    }\n    updateElements(arcs, start, count, mode) {\n      const reset = mode === 'reset';\n      const chart = this.chart;\n      const chartArea = chart.chartArea;\n      const opts = chart.options;\n      const animationOpts = opts.animation;\n      const centerX = (chartArea.left + chartArea.right) / 2;\n      const centerY = (chartArea.top + chartArea.bottom) / 2;\n      const animateScale = reset && animationOpts.animateScale;\n      const innerRadius = animateScale ? 0 : this.innerRadius;\n      const outerRadius = animateScale ? 0 : this.outerRadius;\n      const {\n        sharedOptions,\n        includeOptions\n      } = this._getSharedOptions(start, mode);\n      let startAngle = this._getRotation();\n      let i;\n      for (i = 0; i < start; ++i) {\n        startAngle += this._circumference(i, reset);\n      }\n      for (i = start; i < start + count; ++i) {\n        const circumference = this._circumference(i, reset);\n        const arc = arcs[i];\n        const properties = {\n          x: centerX + this.offsetX,\n          y: centerY + this.offsetY,\n          startAngle,\n          endAngle: startAngle + circumference,\n          circumference,\n          outerRadius,\n          innerRadius\n        };\n        if (includeOptions) {\n          properties.options = sharedOptions || this.resolveDataElementOptions(i, arc.active ? 'active' : mode);\n        }\n        startAngle += circumference;\n        this.updateElement(arc, i, properties, mode);\n      }\n    }\n    calculateTotal() {\n      const meta = this._cachedMeta;\n      const metaData = meta.data;\n      let total = 0;\n      let i;\n      for (i = 0; i < metaData.length; i++) {\n        const value = meta._parsed[i];\n        if (value !== null && !isNaN(value) && this.chart.getDataVisibility(i) && !metaData[i].hidden) {\n          total += Math.abs(value);\n        }\n      }\n      return total;\n    }\n    calculateCircumference(value) {\n      const total = this._cachedMeta.total;\n      if (total > 0 && !isNaN(value)) {\n        return TAU * (Math.abs(value) / total);\n      }\n      return 0;\n    }\n    getLabelAndValue(index) {\n      const meta = this._cachedMeta;\n      const chart = this.chart;\n      const labels = chart.data.labels || [];\n      const value = formatNumber(meta._parsed[index], chart.options.locale);\n      return {\n        label: labels[index] || '',\n        value\n      };\n    }\n    getMaxBorderWidth(arcs) {\n      let max = 0;\n      const chart = this.chart;\n      let i, ilen, meta, controller, options;\n      if (!arcs) {\n        for (i = 0, ilen = chart.data.datasets.length; i < ilen; ++i) {\n          if (chart.isDatasetVisible(i)) {\n            meta = chart.getDatasetMeta(i);\n            arcs = meta.data;\n            controller = meta.controller;\n            break;\n          }\n        }\n      }\n      if (!arcs) {\n        return 0;\n      }\n      for (i = 0, ilen = arcs.length; i < ilen; ++i) {\n        options = controller.resolveDataElementOptions(i);\n        if (options.borderAlign !== 'inner') {\n          max = Math.max(max, options.borderWidth || 0, options.hoverBorderWidth || 0);\n        }\n      }\n      return max;\n    }\n    getMaxOffset(arcs) {\n      let max = 0;\n      for (let i = 0, ilen = arcs.length; i < ilen; ++i) {\n        const options = this.resolveDataElementOptions(i);\n        max = Math.max(max, options.offset || 0, options.hoverOffset || 0);\n      }\n      return max;\n    }\n    _getRingWeightOffset(datasetIndex) {\n      let ringWeightOffset = 0;\n      for (let i = 0; i < datasetIndex; ++i) {\n        if (this.chart.isDatasetVisible(i)) {\n          ringWeightOffset += this._getRingWeight(i);\n        }\n      }\n      return ringWeightOffset;\n    }\n    _getRingWeight(datasetIndex) {\n      return Math.max(valueOrDefault(this.chart.data.datasets[datasetIndex].weight, 1), 0);\n    }\n    _getVisibleDatasetWeightTotal() {\n      return this._getRingWeightOffset(this.chart.data.datasets.length) || 1;\n    }\n  }\n  return DoughnutController;\n})();\nlet LineController = /*#__PURE__*/(() => {\n  class LineController extends DatasetController {\n    static id = 'line';\n    static defaults = {\n      datasetElementType: 'line',\n      dataElementType: 'point',\n      showLine: true,\n      spanGaps: false\n    };\n    static overrides = {\n      scales: {\n        _index_: {\n          type: 'category'\n        },\n        _value_: {\n          type: 'linear'\n        }\n      }\n    };\n    initialize() {\n      this.enableOptionSharing = true;\n      this.supportsDecimation = true;\n      super.initialize();\n    }\n    update(mode) {\n      const meta = this._cachedMeta;\n      const {\n        dataset: line,\n        data: points = [],\n        _dataset\n      } = meta;\n      const animationsDisabled = this.chart._animationsDisabled;\n      let {\n        start,\n        count\n      } = _getStartAndCountOfVisiblePoints(meta, points, animationsDisabled);\n      this._drawStart = start;\n      this._drawCount = count;\n      if (_scaleRangesChanged(meta)) {\n        start = 0;\n        count = points.length;\n      }\n      line._chart = this.chart;\n      line._datasetIndex = this.index;\n      line._decimated = !!_dataset._decimated;\n      line.points = points;\n      const options = this.resolveDatasetElementOptions(mode);\n      if (!this.options.showLine) {\n        options.borderWidth = 0;\n      }\n      options.segment = this.options.segment;\n      this.updateElement(line, undefined, {\n        animated: !animationsDisabled,\n        options\n      }, mode);\n      this.updateElements(points, start, count, mode);\n    }\n    updateElements(points, start, count, mode) {\n      const reset = mode === 'reset';\n      const {\n        iScale,\n        vScale,\n        _stacked,\n        _dataset\n      } = this._cachedMeta;\n      const {\n        sharedOptions,\n        includeOptions\n      } = this._getSharedOptions(start, mode);\n      const iAxis = iScale.axis;\n      const vAxis = vScale.axis;\n      const {\n        spanGaps,\n        segment\n      } = this.options;\n      const maxGapLength = isNumber(spanGaps) ? spanGaps : Number.POSITIVE_INFINITY;\n      const directUpdate = this.chart._animationsDisabled || reset || mode === 'none';\n      const end = start + count;\n      const pointsCount = points.length;\n      let prevParsed = start > 0 && this.getParsed(start - 1);\n      for (let i = 0; i < pointsCount; ++i) {\n        const point = points[i];\n        const properties = directUpdate ? point : {};\n        if (i < start || i >= end) {\n          properties.skip = true;\n          continue;\n        }\n        const parsed = this.getParsed(i);\n        const nullData = isNullOrUndef(parsed[vAxis]);\n        const iPixel = properties[iAxis] = iScale.getPixelForValue(parsed[iAxis], i);\n        const vPixel = properties[vAxis] = reset || nullData ? vScale.getBasePixel() : vScale.getPixelForValue(_stacked ? this.applyStack(vScale, parsed, _stacked) : parsed[vAxis], i);\n        properties.skip = isNaN(iPixel) || isNaN(vPixel) || nullData;\n        properties.stop = i > 0 && Math.abs(parsed[iAxis] - prevParsed[iAxis]) > maxGapLength;\n        if (segment) {\n          properties.parsed = parsed;\n          properties.raw = _dataset.data[i];\n        }\n        if (includeOptions) {\n          properties.options = sharedOptions || this.resolveDataElementOptions(i, point.active ? 'active' : mode);\n        }\n        if (!directUpdate) {\n          this.updateElement(point, i, properties, mode);\n        }\n        prevParsed = parsed;\n      }\n    }\n    getMaxOverflow() {\n      const meta = this._cachedMeta;\n      const dataset = meta.dataset;\n      const border = dataset.options && dataset.options.borderWidth || 0;\n      const data = meta.data || [];\n      if (!data.length) {\n        return border;\n      }\n      const firstPoint = data[0].size(this.resolveDataElementOptions(0));\n      const lastPoint = data[data.length - 1].size(this.resolveDataElementOptions(data.length - 1));\n      return Math.max(border, firstPoint, lastPoint) / 2;\n    }\n    draw() {\n      const meta = this._cachedMeta;\n      meta.dataset.updateControlPoints(this.chart.chartArea, meta.iScale.axis);\n      super.draw();\n    }\n  }\n  return LineController;\n})();\nlet PolarAreaController = /*#__PURE__*/(() => {\n  class PolarAreaController extends DatasetController {\n    static id = 'polarArea';\n    static defaults = {\n      dataElementType: 'arc',\n      animation: {\n        animateRotate: true,\n        animateScale: true\n      },\n      animations: {\n        numbers: {\n          type: 'number',\n          properties: ['x', 'y', 'startAngle', 'endAngle', 'innerRadius', 'outerRadius']\n        }\n      },\n      indexAxis: 'r',\n      startAngle: 0\n    };\n    static overrides = {\n      aspectRatio: 1,\n      plugins: {\n        legend: {\n          labels: {\n            generateLabels(chart) {\n              const data = chart.data;\n              if (data.labels.length && data.datasets.length) {\n                const {\n                  labels: {\n                    pointStyle,\n                    color\n                  }\n                } = chart.legend.options;\n                return data.labels.map((label, i) => {\n                  const meta = chart.getDatasetMeta(0);\n                  const style = meta.controller.getStyle(i);\n                  return {\n                    text: label,\n                    fillStyle: style.backgroundColor,\n                    strokeStyle: style.borderColor,\n                    fontColor: color,\n                    lineWidth: style.borderWidth,\n                    pointStyle: pointStyle,\n                    hidden: !chart.getDataVisibility(i),\n                    index: i\n                  };\n                });\n              }\n              return [];\n            }\n          },\n          onClick(e, legendItem, legend) {\n            legend.chart.toggleDataVisibility(legendItem.index);\n            legend.chart.update();\n          }\n        }\n      },\n      scales: {\n        r: {\n          type: 'radialLinear',\n          angleLines: {\n            display: false\n          },\n          beginAtZero: true,\n          grid: {\n            circular: true\n          },\n          pointLabels: {\n            display: false\n          },\n          startAngle: 0\n        }\n      }\n    };\n    constructor(chart, datasetIndex) {\n      super(chart, datasetIndex);\n      this.innerRadius = undefined;\n      this.outerRadius = undefined;\n    }\n    getLabelAndValue(index) {\n      const meta = this._cachedMeta;\n      const chart = this.chart;\n      const labels = chart.data.labels || [];\n      const value = formatNumber(meta._parsed[index].r, chart.options.locale);\n      return {\n        label: labels[index] || '',\n        value\n      };\n    }\n    parseObjectData(meta, data, start, count) {\n      return _parseObjectDataRadialScale.bind(this)(meta, data, start, count);\n    }\n    update(mode) {\n      const arcs = this._cachedMeta.data;\n      this._updateRadius();\n      this.updateElements(arcs, 0, arcs.length, mode);\n    }\n    getMinMax() {\n      const meta = this._cachedMeta;\n      const range = {\n        min: Number.POSITIVE_INFINITY,\n        max: Number.NEGATIVE_INFINITY\n      };\n      meta.data.forEach((element, index) => {\n        const parsed = this.getParsed(index).r;\n        if (!isNaN(parsed) && this.chart.getDataVisibility(index)) {\n          if (parsed < range.min) {\n            range.min = parsed;\n          }\n          if (parsed > range.max) {\n            range.max = parsed;\n          }\n        }\n      });\n      return range;\n    }\n    _updateRadius() {\n      const chart = this.chart;\n      const chartArea = chart.chartArea;\n      const opts = chart.options;\n      const minSize = Math.min(chartArea.right - chartArea.left, chartArea.bottom - chartArea.top);\n      const outerRadius = Math.max(minSize / 2, 0);\n      const innerRadius = Math.max(opts.cutoutPercentage ? outerRadius / 100 * opts.cutoutPercentage : 1, 0);\n      const radiusLength = (outerRadius - innerRadius) / chart.getVisibleDatasetCount();\n      this.outerRadius = outerRadius - radiusLength * this.index;\n      this.innerRadius = this.outerRadius - radiusLength;\n    }\n    updateElements(arcs, start, count, mode) {\n      const reset = mode === 'reset';\n      const chart = this.chart;\n      const opts = chart.options;\n      const animationOpts = opts.animation;\n      const scale = this._cachedMeta.rScale;\n      const centerX = scale.xCenter;\n      const centerY = scale.yCenter;\n      const datasetStartAngle = scale.getIndexAngle(0) - 0.5 * PI;\n      let angle = datasetStartAngle;\n      let i;\n      const defaultAngle = 360 / this.countVisibleElements();\n      for (i = 0; i < start; ++i) {\n        angle += this._computeAngle(i, mode, defaultAngle);\n      }\n      for (i = start; i < start + count; i++) {\n        const arc = arcs[i];\n        let startAngle = angle;\n        let endAngle = angle + this._computeAngle(i, mode, defaultAngle);\n        let outerRadius = chart.getDataVisibility(i) ? scale.getDistanceFromCenterForValue(this.getParsed(i).r) : 0;\n        angle = endAngle;\n        if (reset) {\n          if (animationOpts.animateScale) {\n            outerRadius = 0;\n          }\n          if (animationOpts.animateRotate) {\n            startAngle = endAngle = datasetStartAngle;\n          }\n        }\n        const properties = {\n          x: centerX,\n          y: centerY,\n          innerRadius: 0,\n          outerRadius,\n          startAngle,\n          endAngle,\n          options: this.resolveDataElementOptions(i, arc.active ? 'active' : mode)\n        };\n        this.updateElement(arc, i, properties, mode);\n      }\n    }\n    countVisibleElements() {\n      const meta = this._cachedMeta;\n      let count = 0;\n      meta.data.forEach((element, index) => {\n        if (!isNaN(this.getParsed(index).r) && this.chart.getDataVisibility(index)) {\n          count++;\n        }\n      });\n      return count;\n    }\n    _computeAngle(index, mode, defaultAngle) {\n      return this.chart.getDataVisibility(index) ? toRadians(this.resolveDataElementOptions(index, mode).angle || defaultAngle) : 0;\n    }\n  }\n  return PolarAreaController;\n})();\nlet PieController = /*#__PURE__*/(() => {\n  class PieController extends DoughnutController {\n    static id = 'pie';\n    static defaults = {\n      cutout: 0,\n      rotation: 0,\n      circumference: 360,\n      radius: '100%'\n    };\n  }\n  return PieController;\n})();\nlet RadarController = /*#__PURE__*/(() => {\n  class RadarController extends DatasetController {\n    static id = 'radar';\n    static defaults = {\n      datasetElementType: 'line',\n      dataElementType: 'point',\n      indexAxis: 'r',\n      showLine: true,\n      elements: {\n        line: {\n          fill: 'start'\n        }\n      }\n    };\n    static overrides = {\n      aspectRatio: 1,\n      scales: {\n        r: {\n          type: 'radialLinear'\n        }\n      }\n    };\n    getLabelAndValue(index) {\n      const vScale = this._cachedMeta.vScale;\n      const parsed = this.getParsed(index);\n      return {\n        label: vScale.getLabels()[index],\n        value: '' + vScale.getLabelForValue(parsed[vScale.axis])\n      };\n    }\n    parseObjectData(meta, data, start, count) {\n      return _parseObjectDataRadialScale.bind(this)(meta, data, start, count);\n    }\n    update(mode) {\n      const meta = this._cachedMeta;\n      const line = meta.dataset;\n      const points = meta.data || [];\n      const labels = meta.iScale.getLabels();\n      line.points = points;\n      if (mode !== 'resize') {\n        const options = this.resolveDatasetElementOptions(mode);\n        if (!this.options.showLine) {\n          options.borderWidth = 0;\n        }\n        const properties = {\n          _loop: true,\n          _fullLoop: labels.length === points.length,\n          options\n        };\n        this.updateElement(line, undefined, properties, mode);\n      }\n      this.updateElements(points, 0, points.length, mode);\n    }\n    updateElements(points, start, count, mode) {\n      const scale = this._cachedMeta.rScale;\n      const reset = mode === 'reset';\n      for (let i = start; i < start + count; i++) {\n        const point = points[i];\n        const options = this.resolveDataElementOptions(i, point.active ? 'active' : mode);\n        const pointPosition = scale.getPointPositionForValue(i, this.getParsed(i).r);\n        const x = reset ? scale.xCenter : pointPosition.x;\n        const y = reset ? scale.yCenter : pointPosition.y;\n        const properties = {\n          x,\n          y,\n          angle: pointPosition.angle,\n          skip: isNaN(x) || isNaN(y),\n          options\n        };\n        this.updateElement(point, i, properties, mode);\n      }\n    }\n  }\n  return RadarController;\n})();\nlet ScatterController = /*#__PURE__*/(() => {\n  class ScatterController extends DatasetController {\n    static id = 'scatter';\n    static defaults = {\n      datasetElementType: false,\n      dataElementType: 'point',\n      showLine: false,\n      fill: false\n    };\n    static overrides = {\n      interaction: {\n        mode: 'point'\n      },\n      scales: {\n        x: {\n          type: 'linear'\n        },\n        y: {\n          type: 'linear'\n        }\n      }\n    };\n    getLabelAndValue(index) {\n      const meta = this._cachedMeta;\n      const labels = this.chart.data.labels || [];\n      const {\n        xScale,\n        yScale\n      } = meta;\n      const parsed = this.getParsed(index);\n      const x = xScale.getLabelForValue(parsed.x);\n      const y = yScale.getLabelForValue(parsed.y);\n      return {\n        label: labels[index] || '',\n        value: '(' + x + ', ' + y + ')'\n      };\n    }\n    update(mode) {\n      const meta = this._cachedMeta;\n      const {\n        data: points = []\n      } = meta;\n      const animationsDisabled = this.chart._animationsDisabled;\n      let {\n        start,\n        count\n      } = _getStartAndCountOfVisiblePoints(meta, points, animationsDisabled);\n      this._drawStart = start;\n      this._drawCount = count;\n      if (_scaleRangesChanged(meta)) {\n        start = 0;\n        count = points.length;\n      }\n      if (this.options.showLine) {\n        if (!this.datasetElementType) {\n          this.addElements();\n        }\n        const {\n          dataset: line,\n          _dataset\n        } = meta;\n        line._chart = this.chart;\n        line._datasetIndex = this.index;\n        line._decimated = !!_dataset._decimated;\n        line.points = points;\n        const options = this.resolveDatasetElementOptions(mode);\n        options.segment = this.options.segment;\n        this.updateElement(line, undefined, {\n          animated: !animationsDisabled,\n          options\n        }, mode);\n      } else if (this.datasetElementType) {\n        delete meta.dataset;\n        this.datasetElementType = false;\n      }\n      this.updateElements(points, start, count, mode);\n    }\n    addElements() {\n      const {\n        showLine\n      } = this.options;\n      if (!this.datasetElementType && showLine) {\n        this.datasetElementType = this.chart.registry.getElement('line');\n      }\n      super.addElements();\n    }\n    updateElements(points, start, count, mode) {\n      const reset = mode === 'reset';\n      const {\n        iScale,\n        vScale,\n        _stacked,\n        _dataset\n      } = this._cachedMeta;\n      const firstOpts = this.resolveDataElementOptions(start, mode);\n      const sharedOptions = this.getSharedOptions(firstOpts);\n      const includeOptions = this.includeOptions(mode, sharedOptions);\n      const iAxis = iScale.axis;\n      const vAxis = vScale.axis;\n      const {\n        spanGaps,\n        segment\n      } = this.options;\n      const maxGapLength = isNumber(spanGaps) ? spanGaps : Number.POSITIVE_INFINITY;\n      const directUpdate = this.chart._animationsDisabled || reset || mode === 'none';\n      let prevParsed = start > 0 && this.getParsed(start - 1);\n      for (let i = start; i < start + count; ++i) {\n        const point = points[i];\n        const parsed = this.getParsed(i);\n        const properties = directUpdate ? point : {};\n        const nullData = isNullOrUndef(parsed[vAxis]);\n        const iPixel = properties[iAxis] = iScale.getPixelForValue(parsed[iAxis], i);\n        const vPixel = properties[vAxis] = reset || nullData ? vScale.getBasePixel() : vScale.getPixelForValue(_stacked ? this.applyStack(vScale, parsed, _stacked) : parsed[vAxis], i);\n        properties.skip = isNaN(iPixel) || isNaN(vPixel) || nullData;\n        properties.stop = i > 0 && Math.abs(parsed[iAxis] - prevParsed[iAxis]) > maxGapLength;\n        if (segment) {\n          properties.parsed = parsed;\n          properties.raw = _dataset.data[i];\n        }\n        if (includeOptions) {\n          properties.options = sharedOptions || this.resolveDataElementOptions(i, point.active ? 'active' : mode);\n        }\n        if (!directUpdate) {\n          this.updateElement(point, i, properties, mode);\n        }\n        prevParsed = parsed;\n      }\n      this.updateSharedOptions(sharedOptions, mode, firstOpts);\n    }\n    getMaxOverflow() {\n      const meta = this._cachedMeta;\n      const data = meta.data || [];\n      if (!this.options.showLine) {\n        let max = 0;\n        for (let i = data.length - 1; i >= 0; --i) {\n          max = Math.max(max, data[i].size(this.resolveDataElementOptions(i)) / 2);\n        }\n        return max > 0 && max;\n      }\n      const dataset = meta.dataset;\n      const border = dataset.options && dataset.options.borderWidth || 0;\n      if (!data.length) {\n        return border;\n      }\n      const firstPoint = data[0].size(this.resolveDataElementOptions(0));\n      const lastPoint = data[data.length - 1].size(this.resolveDataElementOptions(data.length - 1));\n      return Math.max(border, firstPoint, lastPoint) / 2;\n    }\n  }\n  return ScatterController;\n})();\nvar controllers = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  BarController: BarController,\n  BubbleController: BubbleController,\n  DoughnutController: DoughnutController,\n  LineController: LineController,\n  PieController: PieController,\n  PolarAreaController: PolarAreaController,\n  RadarController: RadarController,\n  ScatterController: ScatterController\n});\n\n/**\n * @namespace Chart._adapters\n * @since 2.8.0\n * @private\n */\nfunction abstract() {\n  throw new Error('This method is not implemented: Check that a complete date adapter is provided.');\n}\n/**\n * Date adapter (current used by the time scale)\n * @namespace Chart._adapters._date\n * @memberof Chart._adapters\n * @private\n */\nclass DateAdapterBase {\n  /**\n  * Override default date adapter methods.\n  * Accepts type parameter to define options type.\n  * @example\n  * Chart._adapters._date.override<{myAdapterOption: string}>({\n  *   init() {\n  *     console.log(this.options.myAdapterOption);\n  *   }\n  * })\n  */\n  static override(members) {\n    Object.assign(DateAdapterBase.prototype, members);\n  }\n  options;\n  constructor(options) {\n    this.options = options || {};\n  }\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  init() {}\n  formats() {\n    return abstract();\n  }\n  parse() {\n    return abstract();\n  }\n  format() {\n    return abstract();\n  }\n  add() {\n    return abstract();\n  }\n  diff() {\n    return abstract();\n  }\n  startOf() {\n    return abstract();\n  }\n  endOf() {\n    return abstract();\n  }\n}\nvar adapters = {\n  _date: DateAdapterBase\n};\nfunction binarySearch(metaset, axis, value, intersect) {\n  const {\n    controller,\n    data,\n    _sorted\n  } = metaset;\n  const iScale = controller._cachedMeta.iScale;\n  if (iScale && axis === iScale.axis && axis !== 'r' && _sorted && data.length) {\n    const lookupMethod = iScale._reversePixels ? _rlookupByKey : _lookupByKey;\n    if (!intersect) {\n      return lookupMethod(data, axis, value);\n    } else if (controller._sharedOptions) {\n      const el = data[0];\n      const range = typeof el.getRange === 'function' && el.getRange(axis);\n      if (range) {\n        const start = lookupMethod(data, axis, value - range);\n        const end = lookupMethod(data, axis, value + range);\n        return {\n          lo: start.lo,\n          hi: end.hi\n        };\n      }\n    }\n  }\n  return {\n    lo: 0,\n    hi: data.length - 1\n  };\n}\nfunction evaluateInteractionItems(chart, axis, position, handler, intersect) {\n  const metasets = chart.getSortedVisibleDatasetMetas();\n  const value = position[axis];\n  for (let i = 0, ilen = metasets.length; i < ilen; ++i) {\n    const {\n      index,\n      data\n    } = metasets[i];\n    const {\n      lo,\n      hi\n    } = binarySearch(metasets[i], axis, value, intersect);\n    for (let j = lo; j <= hi; ++j) {\n      const element = data[j];\n      if (!element.skip) {\n        handler(element, index, j);\n      }\n    }\n  }\n}\nfunction getDistanceMetricForAxis(axis) {\n  const useX = axis.indexOf('x') !== -1;\n  const useY = axis.indexOf('y') !== -1;\n  return function (pt1, pt2) {\n    const deltaX = useX ? Math.abs(pt1.x - pt2.x) : 0;\n    const deltaY = useY ? Math.abs(pt1.y - pt2.y) : 0;\n    return Math.sqrt(Math.pow(deltaX, 2) + Math.pow(deltaY, 2));\n  };\n}\nfunction getIntersectItems(chart, position, axis, useFinalPosition, includeInvisible) {\n  const items = [];\n  if (!includeInvisible && !chart.isPointInArea(position)) {\n    return items;\n  }\n  const evaluationFunc = function (element, datasetIndex, index) {\n    if (!includeInvisible && !_isPointInArea(element, chart.chartArea, 0)) {\n      return;\n    }\n    if (element.inRange(position.x, position.y, useFinalPosition)) {\n      items.push({\n        element,\n        datasetIndex,\n        index\n      });\n    }\n  };\n  evaluateInteractionItems(chart, axis, position, evaluationFunc, true);\n  return items;\n}\nfunction getNearestRadialItems(chart, position, axis, useFinalPosition) {\n  let items = [];\n  function evaluationFunc(element, datasetIndex, index) {\n    const {\n      startAngle,\n      endAngle\n    } = element.getProps(['startAngle', 'endAngle'], useFinalPosition);\n    const {\n      angle\n    } = getAngleFromPoint(element, {\n      x: position.x,\n      y: position.y\n    });\n    if (_angleBetween(angle, startAngle, endAngle)) {\n      items.push({\n        element,\n        datasetIndex,\n        index\n      });\n    }\n  }\n  evaluateInteractionItems(chart, axis, position, evaluationFunc);\n  return items;\n}\nfunction getNearestCartesianItems(chart, position, axis, intersect, useFinalPosition, includeInvisible) {\n  let items = [];\n  const distanceMetric = getDistanceMetricForAxis(axis);\n  let minDistance = Number.POSITIVE_INFINITY;\n  function evaluationFunc(element, datasetIndex, index) {\n    const inRange = element.inRange(position.x, position.y, useFinalPosition);\n    if (intersect && !inRange) {\n      return;\n    }\n    const center = element.getCenterPoint(useFinalPosition);\n    const pointInArea = !!includeInvisible || chart.isPointInArea(center);\n    if (!pointInArea && !inRange) {\n      return;\n    }\n    const distance = distanceMetric(position, center);\n    if (distance < minDistance) {\n      items = [{\n        element,\n        datasetIndex,\n        index\n      }];\n      minDistance = distance;\n    } else if (distance === minDistance) {\n      items.push({\n        element,\n        datasetIndex,\n        index\n      });\n    }\n  }\n  evaluateInteractionItems(chart, axis, position, evaluationFunc);\n  return items;\n}\nfunction getNearestItems(chart, position, axis, intersect, useFinalPosition, includeInvisible) {\n  if (!includeInvisible && !chart.isPointInArea(position)) {\n    return [];\n  }\n  return axis === 'r' && !intersect ? getNearestRadialItems(chart, position, axis, useFinalPosition) : getNearestCartesianItems(chart, position, axis, intersect, useFinalPosition, includeInvisible);\n}\nfunction getAxisItems(chart, position, axis, intersect, useFinalPosition) {\n  const items = [];\n  const rangeMethod = axis === 'x' ? 'inXRange' : 'inYRange';\n  let intersectsItem = false;\n  evaluateInteractionItems(chart, axis, position, (element, datasetIndex, index) => {\n    if (element[rangeMethod] && element[rangeMethod](position[axis], useFinalPosition)) {\n      items.push({\n        element,\n        datasetIndex,\n        index\n      });\n      intersectsItem = intersectsItem || element.inRange(position.x, position.y, useFinalPosition);\n    }\n  });\n  if (intersect && !intersectsItem) {\n    return [];\n  }\n  return items;\n}\nvar Interaction = {\n  evaluateInteractionItems,\n  modes: {\n    index(chart, e, options, useFinalPosition) {\n      const position = getRelativePosition(e, chart);\n      const axis = options.axis || 'x';\n      const includeInvisible = options.includeInvisible || false;\n      const items = options.intersect ? getIntersectItems(chart, position, axis, useFinalPosition, includeInvisible) : getNearestItems(chart, position, axis, false, useFinalPosition, includeInvisible);\n      const elements = [];\n      if (!items.length) {\n        return [];\n      }\n      chart.getSortedVisibleDatasetMetas().forEach(meta => {\n        const index = items[0].index;\n        const element = meta.data[index];\n        if (element && !element.skip) {\n          elements.push({\n            element,\n            datasetIndex: meta.index,\n            index\n          });\n        }\n      });\n      return elements;\n    },\n    dataset(chart, e, options, useFinalPosition) {\n      const position = getRelativePosition(e, chart);\n      const axis = options.axis || 'xy';\n      const includeInvisible = options.includeInvisible || false;\n      let items = options.intersect ? getIntersectItems(chart, position, axis, useFinalPosition, includeInvisible) : getNearestItems(chart, position, axis, false, useFinalPosition, includeInvisible);\n      if (items.length > 0) {\n        const datasetIndex = items[0].datasetIndex;\n        const data = chart.getDatasetMeta(datasetIndex).data;\n        items = [];\n        for (let i = 0; i < data.length; ++i) {\n          items.push({\n            element: data[i],\n            datasetIndex,\n            index: i\n          });\n        }\n      }\n      return items;\n    },\n    point(chart, e, options, useFinalPosition) {\n      const position = getRelativePosition(e, chart);\n      const axis = options.axis || 'xy';\n      const includeInvisible = options.includeInvisible || false;\n      return getIntersectItems(chart, position, axis, useFinalPosition, includeInvisible);\n    },\n    nearest(chart, e, options, useFinalPosition) {\n      const position = getRelativePosition(e, chart);\n      const axis = options.axis || 'xy';\n      const includeInvisible = options.includeInvisible || false;\n      return getNearestItems(chart, position, axis, options.intersect, useFinalPosition, includeInvisible);\n    },\n    x(chart, e, options, useFinalPosition) {\n      const position = getRelativePosition(e, chart);\n      return getAxisItems(chart, position, 'x', options.intersect, useFinalPosition);\n    },\n    y(chart, e, options, useFinalPosition) {\n      const position = getRelativePosition(e, chart);\n      return getAxisItems(chart, position, 'y', options.intersect, useFinalPosition);\n    }\n  }\n};\nconst STATIC_POSITIONS = ['left', 'top', 'right', 'bottom'];\nfunction filterByPosition(array, position) {\n  return array.filter(v => v.pos === position);\n}\nfunction filterDynamicPositionByAxis(array, axis) {\n  return array.filter(v => STATIC_POSITIONS.indexOf(v.pos) === -1 && v.box.axis === axis);\n}\nfunction sortByWeight(array, reverse) {\n  return array.sort((a, b) => {\n    const v0 = reverse ? b : a;\n    const v1 = reverse ? a : b;\n    return v0.weight === v1.weight ? v0.index - v1.index : v0.weight - v1.weight;\n  });\n}\nfunction wrapBoxes(boxes) {\n  const layoutBoxes = [];\n  let i, ilen, box, pos, stack, stackWeight;\n  for (i = 0, ilen = (boxes || []).length; i < ilen; ++i) {\n    box = boxes[i];\n    ({\n      position: pos,\n      options: {\n        stack,\n        stackWeight = 1\n      }\n    } = box);\n    layoutBoxes.push({\n      index: i,\n      box,\n      pos,\n      horizontal: box.isHorizontal(),\n      weight: box.weight,\n      stack: stack && pos + stack,\n      stackWeight\n    });\n  }\n  return layoutBoxes;\n}\nfunction buildStacks(layouts) {\n  const stacks = {};\n  for (const wrap of layouts) {\n    const {\n      stack,\n      pos,\n      stackWeight\n    } = wrap;\n    if (!stack || !STATIC_POSITIONS.includes(pos)) {\n      continue;\n    }\n    const _stack = stacks[stack] || (stacks[stack] = {\n      count: 0,\n      placed: 0,\n      weight: 0,\n      size: 0\n    });\n    _stack.count++;\n    _stack.weight += stackWeight;\n  }\n  return stacks;\n}\nfunction setLayoutDims(layouts, params) {\n  const stacks = buildStacks(layouts);\n  const {\n    vBoxMaxWidth,\n    hBoxMaxHeight\n  } = params;\n  let i, ilen, layout;\n  for (i = 0, ilen = layouts.length; i < ilen; ++i) {\n    layout = layouts[i];\n    const {\n      fullSize\n    } = layout.box;\n    const stack = stacks[layout.stack];\n    const factor = stack && layout.stackWeight / stack.weight;\n    if (layout.horizontal) {\n      layout.width = factor ? factor * vBoxMaxWidth : fullSize && params.availableWidth;\n      layout.height = hBoxMaxHeight;\n    } else {\n      layout.width = vBoxMaxWidth;\n      layout.height = factor ? factor * hBoxMaxHeight : fullSize && params.availableHeight;\n    }\n  }\n  return stacks;\n}\nfunction buildLayoutBoxes(boxes) {\n  const layoutBoxes = wrapBoxes(boxes);\n  const fullSize = sortByWeight(layoutBoxes.filter(wrap => wrap.box.fullSize), true);\n  const left = sortByWeight(filterByPosition(layoutBoxes, 'left'), true);\n  const right = sortByWeight(filterByPosition(layoutBoxes, 'right'));\n  const top = sortByWeight(filterByPosition(layoutBoxes, 'top'), true);\n  const bottom = sortByWeight(filterByPosition(layoutBoxes, 'bottom'));\n  const centerHorizontal = filterDynamicPositionByAxis(layoutBoxes, 'x');\n  const centerVertical = filterDynamicPositionByAxis(layoutBoxes, 'y');\n  return {\n    fullSize,\n    leftAndTop: left.concat(top),\n    rightAndBottom: right.concat(centerVertical).concat(bottom).concat(centerHorizontal),\n    chartArea: filterByPosition(layoutBoxes, 'chartArea'),\n    vertical: left.concat(right).concat(centerVertical),\n    horizontal: top.concat(bottom).concat(centerHorizontal)\n  };\n}\nfunction getCombinedMax(maxPadding, chartArea, a, b) {\n  return Math.max(maxPadding[a], chartArea[a]) + Math.max(maxPadding[b], chartArea[b]);\n}\nfunction updateMaxPadding(maxPadding, boxPadding) {\n  maxPadding.top = Math.max(maxPadding.top, boxPadding.top);\n  maxPadding.left = Math.max(maxPadding.left, boxPadding.left);\n  maxPadding.bottom = Math.max(maxPadding.bottom, boxPadding.bottom);\n  maxPadding.right = Math.max(maxPadding.right, boxPadding.right);\n}\nfunction updateDims(chartArea, params, layout, stacks) {\n  const {\n    pos,\n    box\n  } = layout;\n  const maxPadding = chartArea.maxPadding;\n  if (!isObject(pos)) {\n    if (layout.size) {\n      chartArea[pos] -= layout.size;\n    }\n    const stack = stacks[layout.stack] || {\n      size: 0,\n      count: 1\n    };\n    stack.size = Math.max(stack.size, layout.horizontal ? box.height : box.width);\n    layout.size = stack.size / stack.count;\n    chartArea[pos] += layout.size;\n  }\n  if (box.getPadding) {\n    updateMaxPadding(maxPadding, box.getPadding());\n  }\n  const newWidth = Math.max(0, params.outerWidth - getCombinedMax(maxPadding, chartArea, 'left', 'right'));\n  const newHeight = Math.max(0, params.outerHeight - getCombinedMax(maxPadding, chartArea, 'top', 'bottom'));\n  const widthChanged = newWidth !== chartArea.w;\n  const heightChanged = newHeight !== chartArea.h;\n  chartArea.w = newWidth;\n  chartArea.h = newHeight;\n  return layout.horizontal ? {\n    same: widthChanged,\n    other: heightChanged\n  } : {\n    same: heightChanged,\n    other: widthChanged\n  };\n}\nfunction handleMaxPadding(chartArea) {\n  const maxPadding = chartArea.maxPadding;\n  function updatePos(pos) {\n    const change = Math.max(maxPadding[pos] - chartArea[pos], 0);\n    chartArea[pos] += change;\n    return change;\n  }\n  chartArea.y += updatePos('top');\n  chartArea.x += updatePos('left');\n  updatePos('right');\n  updatePos('bottom');\n}\nfunction getMargins(horizontal, chartArea) {\n  const maxPadding = chartArea.maxPadding;\n  function marginForPositions(positions) {\n    const margin = {\n      left: 0,\n      top: 0,\n      right: 0,\n      bottom: 0\n    };\n    positions.forEach(pos => {\n      margin[pos] = Math.max(chartArea[pos], maxPadding[pos]);\n    });\n    return margin;\n  }\n  return horizontal ? marginForPositions(['left', 'right']) : marginForPositions(['top', 'bottom']);\n}\nfunction fitBoxes(boxes, chartArea, params, stacks) {\n  const refitBoxes = [];\n  let i, ilen, layout, box, refit, changed;\n  for (i = 0, ilen = boxes.length, refit = 0; i < ilen; ++i) {\n    layout = boxes[i];\n    box = layout.box;\n    box.update(layout.width || chartArea.w, layout.height || chartArea.h, getMargins(layout.horizontal, chartArea));\n    const {\n      same,\n      other\n    } = updateDims(chartArea, params, layout, stacks);\n    refit |= same && refitBoxes.length;\n    changed = changed || other;\n    if (!box.fullSize) {\n      refitBoxes.push(layout);\n    }\n  }\n  return refit && fitBoxes(refitBoxes, chartArea, params, stacks) || changed;\n}\nfunction setBoxDims(box, left, top, width, height) {\n  box.top = top;\n  box.left = left;\n  box.right = left + width;\n  box.bottom = top + height;\n  box.width = width;\n  box.height = height;\n}\nfunction placeBoxes(boxes, chartArea, params, stacks) {\n  const userPadding = params.padding;\n  let {\n    x,\n    y\n  } = chartArea;\n  for (const layout of boxes) {\n    const box = layout.box;\n    const stack = stacks[layout.stack] || {\n      count: 1,\n      placed: 0,\n      weight: 1\n    };\n    const weight = layout.stackWeight / stack.weight || 1;\n    if (layout.horizontal) {\n      const width = chartArea.w * weight;\n      const height = stack.size || box.height;\n      if (defined(stack.start)) {\n        y = stack.start;\n      }\n      if (box.fullSize) {\n        setBoxDims(box, userPadding.left, y, params.outerWidth - userPadding.right - userPadding.left, height);\n      } else {\n        setBoxDims(box, chartArea.left + stack.placed, y, width, height);\n      }\n      stack.start = y;\n      stack.placed += width;\n      y = box.bottom;\n    } else {\n      const height = chartArea.h * weight;\n      const width = stack.size || box.width;\n      if (defined(stack.start)) {\n        x = stack.start;\n      }\n      if (box.fullSize) {\n        setBoxDims(box, x, userPadding.top, width, params.outerHeight - userPadding.bottom - userPadding.top);\n      } else {\n        setBoxDims(box, x, chartArea.top + stack.placed, width, height);\n      }\n      stack.start = x;\n      stack.placed += height;\n      x = box.right;\n    }\n  }\n  chartArea.x = x;\n  chartArea.y = y;\n}\nvar layouts = {\n  addBox(chart, item) {\n    if (!chart.boxes) {\n      chart.boxes = [];\n    }\n    item.fullSize = item.fullSize || false;\n    item.position = item.position || 'top';\n    item.weight = item.weight || 0;\n    item._layers = item._layers || function () {\n      return [{\n        z: 0,\n        draw(chartArea) {\n          item.draw(chartArea);\n        }\n      }];\n    };\n    chart.boxes.push(item);\n  },\n  removeBox(chart, layoutItem) {\n    const index = chart.boxes ? chart.boxes.indexOf(layoutItem) : -1;\n    if (index !== -1) {\n      chart.boxes.splice(index, 1);\n    }\n  },\n  configure(chart, item, options) {\n    item.fullSize = options.fullSize;\n    item.position = options.position;\n    item.weight = options.weight;\n  },\n  update(chart, width, height, minPadding) {\n    if (!chart) {\n      return;\n    }\n    const padding = toPadding(chart.options.layout.padding);\n    const availableWidth = Math.max(width - padding.width, 0);\n    const availableHeight = Math.max(height - padding.height, 0);\n    const boxes = buildLayoutBoxes(chart.boxes);\n    const verticalBoxes = boxes.vertical;\n    const horizontalBoxes = boxes.horizontal;\n    each(chart.boxes, box => {\n      if (typeof box.beforeLayout === 'function') {\n        box.beforeLayout();\n      }\n    });\n    const visibleVerticalBoxCount = verticalBoxes.reduce((total, wrap) => wrap.box.options && wrap.box.options.display === false ? total : total + 1, 0) || 1;\n    const params = Object.freeze({\n      outerWidth: width,\n      outerHeight: height,\n      padding,\n      availableWidth,\n      availableHeight,\n      vBoxMaxWidth: availableWidth / 2 / visibleVerticalBoxCount,\n      hBoxMaxHeight: availableHeight / 2\n    });\n    const maxPadding = Object.assign({}, padding);\n    updateMaxPadding(maxPadding, toPadding(minPadding));\n    const chartArea = Object.assign({\n      maxPadding,\n      w: availableWidth,\n      h: availableHeight,\n      x: padding.left,\n      y: padding.top\n    }, padding);\n    const stacks = setLayoutDims(verticalBoxes.concat(horizontalBoxes), params);\n    fitBoxes(boxes.fullSize, chartArea, params, stacks);\n    fitBoxes(verticalBoxes, chartArea, params, stacks);\n    if (fitBoxes(horizontalBoxes, chartArea, params, stacks)) {\n      fitBoxes(verticalBoxes, chartArea, params, stacks);\n    }\n    handleMaxPadding(chartArea);\n    placeBoxes(boxes.leftAndTop, chartArea, params, stacks);\n    chartArea.x += chartArea.w;\n    chartArea.y += chartArea.h;\n    placeBoxes(boxes.rightAndBottom, chartArea, params, stacks);\n    chart.chartArea = {\n      left: chartArea.left,\n      top: chartArea.top,\n      right: chartArea.left + chartArea.w,\n      bottom: chartArea.top + chartArea.h,\n      height: chartArea.h,\n      width: chartArea.w\n    };\n    each(boxes.chartArea, layout => {\n      const box = layout.box;\n      Object.assign(box, chart.chartArea);\n      box.update(chartArea.w, chartArea.h, {\n        left: 0,\n        top: 0,\n        right: 0,\n        bottom: 0\n      });\n    });\n  }\n};\nclass BasePlatform {\n  acquireContext(canvas, aspectRatio) {}\n  releaseContext(context) {\n    return false;\n  }\n  addEventListener(chart, type, listener) {}\n  removeEventListener(chart, type, listener) {}\n  getDevicePixelRatio() {\n    return 1;\n  }\n  getMaximumSize(element, width, height, aspectRatio) {\n    width = Math.max(0, width || element.width);\n    height = height || element.height;\n    return {\n      width,\n      height: Math.max(0, aspectRatio ? Math.floor(width / aspectRatio) : height)\n    };\n  }\n  isAttached(canvas) {\n    return true;\n  }\n  updateConfig(config) {}\n}\nclass BasicPlatform extends BasePlatform {\n  acquireContext(item) {\n    return item && item.getContext && item.getContext('2d') || null;\n  }\n  updateConfig(config) {\n    config.options.animation = false;\n  }\n}\nconst EXPANDO_KEY = '$chartjs';\nconst EVENT_TYPES = {\n  touchstart: 'mousedown',\n  touchmove: 'mousemove',\n  touchend: 'mouseup',\n  pointerenter: 'mouseenter',\n  pointerdown: 'mousedown',\n  pointermove: 'mousemove',\n  pointerup: 'mouseup',\n  pointerleave: 'mouseout',\n  pointerout: 'mouseout'\n};\nconst isNullOrEmpty = value => value === null || value === '';\nfunction initCanvas(canvas, aspectRatio) {\n  const style = canvas.style;\n  const renderHeight = canvas.getAttribute('height');\n  const renderWidth = canvas.getAttribute('width');\n  canvas[EXPANDO_KEY] = {\n    initial: {\n      height: renderHeight,\n      width: renderWidth,\n      style: {\n        display: style.display,\n        height: style.height,\n        width: style.width\n      }\n    }\n  };\n  style.display = style.display || 'block';\n  style.boxSizing = style.boxSizing || 'border-box';\n  if (isNullOrEmpty(renderWidth)) {\n    const displayWidth = readUsedSize(canvas, 'width');\n    if (displayWidth !== undefined) {\n      canvas.width = displayWidth;\n    }\n  }\n  if (isNullOrEmpty(renderHeight)) {\n    if (canvas.style.height === '') {\n      canvas.height = canvas.width / (aspectRatio || 2);\n    } else {\n      const displayHeight = readUsedSize(canvas, 'height');\n      if (displayHeight !== undefined) {\n        canvas.height = displayHeight;\n      }\n    }\n  }\n  return canvas;\n}\nconst eventListenerOptions = supportsEventListenerOptions ? {\n  passive: true\n} : false;\nfunction addListener(node, type, listener) {\n  if (node) {\n    node.addEventListener(type, listener, eventListenerOptions);\n  }\n}\nfunction removeListener(chart, type, listener) {\n  if (chart && chart.canvas) {\n    chart.canvas.removeEventListener(type, listener, eventListenerOptions);\n  }\n}\nfunction fromNativeEvent(event, chart) {\n  const type = EVENT_TYPES[event.type] || event.type;\n  const {\n    x,\n    y\n  } = getRelativePosition(event, chart);\n  return {\n    type,\n    chart,\n    native: event,\n    x: x !== undefined ? x : null,\n    y: y !== undefined ? y : null\n  };\n}\nfunction nodeListContains(nodeList, canvas) {\n  for (const node of nodeList) {\n    if (node === canvas || node.contains(canvas)) {\n      return true;\n    }\n  }\n}\nfunction createAttachObserver(chart, type, listener) {\n  const canvas = chart.canvas;\n  const observer = new MutationObserver(entries => {\n    let trigger = false;\n    for (const entry of entries) {\n      trigger = trigger || nodeListContains(entry.addedNodes, canvas);\n      trigger = trigger && !nodeListContains(entry.removedNodes, canvas);\n    }\n    if (trigger) {\n      listener();\n    }\n  });\n  observer.observe(document, {\n    childList: true,\n    subtree: true\n  });\n  return observer;\n}\nfunction createDetachObserver(chart, type, listener) {\n  const canvas = chart.canvas;\n  const observer = new MutationObserver(entries => {\n    let trigger = false;\n    for (const entry of entries) {\n      trigger = trigger || nodeListContains(entry.removedNodes, canvas);\n      trigger = trigger && !nodeListContains(entry.addedNodes, canvas);\n    }\n    if (trigger) {\n      listener();\n    }\n  });\n  observer.observe(document, {\n    childList: true,\n    subtree: true\n  });\n  return observer;\n}\nconst drpListeningCharts = new Map();\nlet oldDevicePixelRatio = 0;\nfunction onWindowResize() {\n  const dpr = window.devicePixelRatio;\n  if (dpr === oldDevicePixelRatio) {\n    return;\n  }\n  oldDevicePixelRatio = dpr;\n  drpListeningCharts.forEach((resize, chart) => {\n    if (chart.currentDevicePixelRatio !== dpr) {\n      resize();\n    }\n  });\n}\nfunction listenDevicePixelRatioChanges(chart, resize) {\n  if (!drpListeningCharts.size) {\n    window.addEventListener('resize', onWindowResize);\n  }\n  drpListeningCharts.set(chart, resize);\n}\nfunction unlistenDevicePixelRatioChanges(chart) {\n  drpListeningCharts.delete(chart);\n  if (!drpListeningCharts.size) {\n    window.removeEventListener('resize', onWindowResize);\n  }\n}\nfunction createResizeObserver(chart, type, listener) {\n  const canvas = chart.canvas;\n  const container = canvas && _getParentNode(canvas);\n  if (!container) {\n    return;\n  }\n  const resize = throttled((width, height) => {\n    const w = container.clientWidth;\n    listener(width, height);\n    if (w < container.clientWidth) {\n      listener();\n    }\n  }, window);\n  const observer = new ResizeObserver(entries => {\n    const entry = entries[0];\n    const width = entry.contentRect.width;\n    const height = entry.contentRect.height;\n    if (width === 0 && height === 0) {\n      return;\n    }\n    resize(width, height);\n  });\n  observer.observe(container);\n  listenDevicePixelRatioChanges(chart, resize);\n  return observer;\n}\nfunction releaseObserver(chart, type, observer) {\n  if (observer) {\n    observer.disconnect();\n  }\n  if (type === 'resize') {\n    unlistenDevicePixelRatioChanges(chart);\n  }\n}\nfunction createProxyAndListen(chart, type, listener) {\n  const canvas = chart.canvas;\n  const proxy = throttled(event => {\n    if (chart.ctx !== null) {\n      listener(fromNativeEvent(event, chart));\n    }\n  }, chart);\n  addListener(canvas, type, proxy);\n  return proxy;\n}\nclass DomPlatform extends BasePlatform {\n  acquireContext(canvas, aspectRatio) {\n    const context = canvas && canvas.getContext && canvas.getContext('2d');\n    if (context && context.canvas === canvas) {\n      initCanvas(canvas, aspectRatio);\n      return context;\n    }\n    return null;\n  }\n  releaseContext(context) {\n    const canvas = context.canvas;\n    if (!canvas[EXPANDO_KEY]) {\n      return false;\n    }\n    const initial = canvas[EXPANDO_KEY].initial;\n    ['height', 'width'].forEach(prop => {\n      const value = initial[prop];\n      if (isNullOrUndef(value)) {\n        canvas.removeAttribute(prop);\n      } else {\n        canvas.setAttribute(prop, value);\n      }\n    });\n    const style = initial.style || {};\n    Object.keys(style).forEach(key => {\n      canvas.style[key] = style[key];\n    });\n    canvas.width = canvas.width;\n    delete canvas[EXPANDO_KEY];\n    return true;\n  }\n  addEventListener(chart, type, listener) {\n    this.removeEventListener(chart, type);\n    const proxies = chart.$proxies || (chart.$proxies = {});\n    const handlers = {\n      attach: createAttachObserver,\n      detach: createDetachObserver,\n      resize: createResizeObserver\n    };\n    const handler = handlers[type] || createProxyAndListen;\n    proxies[type] = handler(chart, type, listener);\n  }\n  removeEventListener(chart, type) {\n    const proxies = chart.$proxies || (chart.$proxies = {});\n    const proxy = proxies[type];\n    if (!proxy) {\n      return;\n    }\n    const handlers = {\n      attach: releaseObserver,\n      detach: releaseObserver,\n      resize: releaseObserver\n    };\n    const handler = handlers[type] || removeListener;\n    handler(chart, type, proxy);\n    proxies[type] = undefined;\n  }\n  getDevicePixelRatio() {\n    return window.devicePixelRatio;\n  }\n  getMaximumSize(canvas, width, height, aspectRatio) {\n    return getMaximumSize(canvas, width, height, aspectRatio);\n  }\n  isAttached(canvas) {\n    const container = canvas && _getParentNode(canvas);\n    return !!(container && container.isConnected);\n  }\n}\nfunction _detectPlatform(canvas) {\n  if (!_isDomSupported() || typeof OffscreenCanvas !== 'undefined' && canvas instanceof OffscreenCanvas) {\n    return BasicPlatform;\n  }\n  return DomPlatform;\n}\nclass Element {\n  static defaults = {};\n  static defaultRoutes = undefined;\n  x;\n  y;\n  active = false;\n  options;\n  $animations;\n  tooltipPosition(useFinalPosition) {\n    const {\n      x,\n      y\n    } = this.getProps(['x', 'y'], useFinalPosition);\n    return {\n      x,\n      y\n    };\n  }\n  hasValue() {\n    return isNumber(this.x) && isNumber(this.y);\n  }\n  getProps(props, final) {\n    const anims = this.$animations;\n    if (!final || !anims) {\n      // let's not create an object, if not needed\n      return this;\n    }\n    const ret = {};\n    props.forEach(prop => {\n      ret[prop] = anims[prop] && anims[prop].active() ? anims[prop]._to : this[prop];\n    });\n    return ret;\n  }\n}\nfunction autoSkip(scale, ticks) {\n  const tickOpts = scale.options.ticks;\n  const determinedMaxTicks = determineMaxTicks(scale);\n  const ticksLimit = Math.min(tickOpts.maxTicksLimit || determinedMaxTicks, determinedMaxTicks);\n  const majorIndices = tickOpts.major.enabled ? getMajorIndices(ticks) : [];\n  const numMajorIndices = majorIndices.length;\n  const first = majorIndices[0];\n  const last = majorIndices[numMajorIndices - 1];\n  const newTicks = [];\n  if (numMajorIndices > ticksLimit) {\n    skipMajors(ticks, newTicks, majorIndices, numMajorIndices / ticksLimit);\n    return newTicks;\n  }\n  const spacing = calculateSpacing(majorIndices, ticks, ticksLimit);\n  if (numMajorIndices > 0) {\n    let i, ilen;\n    const avgMajorSpacing = numMajorIndices > 1 ? Math.round((last - first) / (numMajorIndices - 1)) : null;\n    skip(ticks, newTicks, spacing, isNullOrUndef(avgMajorSpacing) ? 0 : first - avgMajorSpacing, first);\n    for (i = 0, ilen = numMajorIndices - 1; i < ilen; i++) {\n      skip(ticks, newTicks, spacing, majorIndices[i], majorIndices[i + 1]);\n    }\n    skip(ticks, newTicks, spacing, last, isNullOrUndef(avgMajorSpacing) ? ticks.length : last + avgMajorSpacing);\n    return newTicks;\n  }\n  skip(ticks, newTicks, spacing);\n  return newTicks;\n}\nfunction determineMaxTicks(scale) {\n  const offset = scale.options.offset;\n  const tickLength = scale._tickSize();\n  const maxScale = scale._length / tickLength + (offset ? 0 : 1);\n  const maxChart = scale._maxLength / tickLength;\n  return Math.floor(Math.min(maxScale, maxChart));\n}\nfunction calculateSpacing(majorIndices, ticks, ticksLimit) {\n  const evenMajorSpacing = getEvenSpacing(majorIndices);\n  const spacing = ticks.length / ticksLimit;\n  if (!evenMajorSpacing) {\n    return Math.max(spacing, 1);\n  }\n  const factors = _factorize(evenMajorSpacing);\n  for (let i = 0, ilen = factors.length - 1; i < ilen; i++) {\n    const factor = factors[i];\n    if (factor > spacing) {\n      return factor;\n    }\n  }\n  return Math.max(spacing, 1);\n}\nfunction getMajorIndices(ticks) {\n  const result = [];\n  let i, ilen;\n  for (i = 0, ilen = ticks.length; i < ilen; i++) {\n    if (ticks[i].major) {\n      result.push(i);\n    }\n  }\n  return result;\n}\nfunction skipMajors(ticks, newTicks, majorIndices, spacing) {\n  let count = 0;\n  let next = majorIndices[0];\n  let i;\n  spacing = Math.ceil(spacing);\n  for (i = 0; i < ticks.length; i++) {\n    if (i === next) {\n      newTicks.push(ticks[i]);\n      count++;\n      next = majorIndices[count * spacing];\n    }\n  }\n}\nfunction skip(ticks, newTicks, spacing, majorStart, majorEnd) {\n  const start = valueOrDefault(majorStart, 0);\n  const end = Math.min(valueOrDefault(majorEnd, ticks.length), ticks.length);\n  let count = 0;\n  let length, i, next;\n  spacing = Math.ceil(spacing);\n  if (majorEnd) {\n    length = majorEnd - majorStart;\n    spacing = length / Math.floor(length / spacing);\n  }\n  next = start;\n  while (next < 0) {\n    count++;\n    next = Math.round(start + count * spacing);\n  }\n  for (i = Math.max(start, 0); i < end; i++) {\n    if (i === next) {\n      newTicks.push(ticks[i]);\n      count++;\n      next = Math.round(start + count * spacing);\n    }\n  }\n}\nfunction getEvenSpacing(arr) {\n  const len = arr.length;\n  let i, diff;\n  if (len < 2) {\n    return false;\n  }\n  for (diff = arr[0], i = 1; i < len; ++i) {\n    if (arr[i] - arr[i - 1] !== diff) {\n      return false;\n    }\n  }\n  return diff;\n}\nconst reverseAlign = align => align === 'left' ? 'right' : align === 'right' ? 'left' : align;\nconst offsetFromEdge = (scale, edge, offset) => edge === 'top' || edge === 'left' ? scale[edge] + offset : scale[edge] - offset;\nconst getTicksLimit = (ticksLength, maxTicksLimit) => Math.min(maxTicksLimit || ticksLength, ticksLength);\nfunction sample(arr, numItems) {\n  const result = [];\n  const increment = arr.length / numItems;\n  const len = arr.length;\n  let i = 0;\n  for (; i < len; i += increment) {\n    result.push(arr[Math.floor(i)]);\n  }\n  return result;\n}\nfunction getPixelForGridLine(scale, index, offsetGridLines) {\n  const length = scale.ticks.length;\n  const validIndex = Math.min(index, length - 1);\n  const start = scale._startPixel;\n  const end = scale._endPixel;\n  const epsilon = 1e-6;\n  let lineValue = scale.getPixelForTick(validIndex);\n  let offset;\n  if (offsetGridLines) {\n    if (length === 1) {\n      offset = Math.max(lineValue - start, end - lineValue);\n    } else if (index === 0) {\n      offset = (scale.getPixelForTick(1) - lineValue) / 2;\n    } else {\n      offset = (lineValue - scale.getPixelForTick(validIndex - 1)) / 2;\n    }\n    lineValue += validIndex < index ? offset : -offset;\n    if (lineValue < start - epsilon || lineValue > end + epsilon) {\n      return;\n    }\n  }\n  return lineValue;\n}\nfunction garbageCollect(caches, length) {\n  each(caches, cache => {\n    const gc = cache.gc;\n    const gcLen = gc.length / 2;\n    let i;\n    if (gcLen > length) {\n      for (i = 0; i < gcLen; ++i) {\n        delete cache.data[gc[i]];\n      }\n      gc.splice(0, gcLen);\n    }\n  });\n}\nfunction getTickMarkLength(options) {\n  return options.drawTicks ? options.tickLength : 0;\n}\nfunction getTitleHeight(options, fallback) {\n  if (!options.display) {\n    return 0;\n  }\n  const font = toFont(options.font, fallback);\n  const padding = toPadding(options.padding);\n  const lines = isArray(options.text) ? options.text.length : 1;\n  return lines * font.lineHeight + padding.height;\n}\nfunction createScaleContext(parent, scale) {\n  return createContext(parent, {\n    scale,\n    type: 'scale'\n  });\n}\nfunction createTickContext(parent, index, tick) {\n  return createContext(parent, {\n    tick,\n    index,\n    type: 'tick'\n  });\n}\nfunction titleAlign(align, position, reverse) {\n  let ret = _toLeftRightCenter(align);\n  if (reverse && position !== 'right' || !reverse && position === 'right') {\n    ret = reverseAlign(ret);\n  }\n  return ret;\n}\nfunction titleArgs(scale, offset, position, align) {\n  const {\n    top,\n    left,\n    bottom,\n    right,\n    chart\n  } = scale;\n  const {\n    chartArea,\n    scales\n  } = chart;\n  let rotation = 0;\n  let maxWidth, titleX, titleY;\n  const height = bottom - top;\n  const width = right - left;\n  if (scale.isHorizontal()) {\n    titleX = _alignStartEnd(align, left, right);\n    if (isObject(position)) {\n      const positionAxisID = Object.keys(position)[0];\n      const value = position[positionAxisID];\n      titleY = scales[positionAxisID].getPixelForValue(value) + height - offset;\n    } else if (position === 'center') {\n      titleY = (chartArea.bottom + chartArea.top) / 2 + height - offset;\n    } else {\n      titleY = offsetFromEdge(scale, position, offset);\n    }\n    maxWidth = right - left;\n  } else {\n    if (isObject(position)) {\n      const positionAxisID = Object.keys(position)[0];\n      const value = position[positionAxisID];\n      titleX = scales[positionAxisID].getPixelForValue(value) - width + offset;\n    } else if (position === 'center') {\n      titleX = (chartArea.left + chartArea.right) / 2 - width + offset;\n    } else {\n      titleX = offsetFromEdge(scale, position, offset);\n    }\n    titleY = _alignStartEnd(align, bottom, top);\n    rotation = position === 'left' ? -HALF_PI : HALF_PI;\n  }\n  return {\n    titleX,\n    titleY,\n    maxWidth,\n    rotation\n  };\n}\nclass Scale extends Element {\n  constructor(cfg) {\n    super();\n    this.id = cfg.id;\n    this.type = cfg.type;\n    this.options = undefined;\n    this.ctx = cfg.ctx;\n    this.chart = cfg.chart;\n    this.top = undefined;\n    this.bottom = undefined;\n    this.left = undefined;\n    this.right = undefined;\n    this.width = undefined;\n    this.height = undefined;\n    this._margins = {\n      left: 0,\n      right: 0,\n      top: 0,\n      bottom: 0\n    };\n    this.maxWidth = undefined;\n    this.maxHeight = undefined;\n    this.paddingTop = undefined;\n    this.paddingBottom = undefined;\n    this.paddingLeft = undefined;\n    this.paddingRight = undefined;\n    this.axis = undefined;\n    this.labelRotation = undefined;\n    this.min = undefined;\n    this.max = undefined;\n    this._range = undefined;\n    this.ticks = [];\n    this._gridLineItems = null;\n    this._labelItems = null;\n    this._labelSizes = null;\n    this._length = 0;\n    this._maxLength = 0;\n    this._longestTextCache = {};\n    this._startPixel = undefined;\n    this._endPixel = undefined;\n    this._reversePixels = false;\n    this._userMax = undefined;\n    this._userMin = undefined;\n    this._suggestedMax = undefined;\n    this._suggestedMin = undefined;\n    this._ticksLength = 0;\n    this._borderValue = 0;\n    this._cache = {};\n    this._dataLimitsCached = false;\n    this.$context = undefined;\n  }\n  init(options) {\n    this.options = options.setContext(this.getContext());\n    this.axis = options.axis;\n    this._userMin = this.parse(options.min);\n    this._userMax = this.parse(options.max);\n    this._suggestedMin = this.parse(options.suggestedMin);\n    this._suggestedMax = this.parse(options.suggestedMax);\n  }\n  parse(raw, index) {\n    return raw;\n  }\n  getUserBounds() {\n    let {\n      _userMin,\n      _userMax,\n      _suggestedMin,\n      _suggestedMax\n    } = this;\n    _userMin = finiteOrDefault(_userMin, Number.POSITIVE_INFINITY);\n    _userMax = finiteOrDefault(_userMax, Number.NEGATIVE_INFINITY);\n    _suggestedMin = finiteOrDefault(_suggestedMin, Number.POSITIVE_INFINITY);\n    _suggestedMax = finiteOrDefault(_suggestedMax, Number.NEGATIVE_INFINITY);\n    return {\n      min: finiteOrDefault(_userMin, _suggestedMin),\n      max: finiteOrDefault(_userMax, _suggestedMax),\n      minDefined: isNumberFinite(_userMin),\n      maxDefined: isNumberFinite(_userMax)\n    };\n  }\n  getMinMax(canStack) {\n    let {\n      min,\n      max,\n      minDefined,\n      maxDefined\n    } = this.getUserBounds();\n    let range;\n    if (minDefined && maxDefined) {\n      return {\n        min,\n        max\n      };\n    }\n    const metas = this.getMatchingVisibleMetas();\n    for (let i = 0, ilen = metas.length; i < ilen; ++i) {\n      range = metas[i].controller.getMinMax(this, canStack);\n      if (!minDefined) {\n        min = Math.min(min, range.min);\n      }\n      if (!maxDefined) {\n        max = Math.max(max, range.max);\n      }\n    }\n    min = maxDefined && min > max ? max : min;\n    max = minDefined && min > max ? min : max;\n    return {\n      min: finiteOrDefault(min, finiteOrDefault(max, min)),\n      max: finiteOrDefault(max, finiteOrDefault(min, max))\n    };\n  }\n  getPadding() {\n    return {\n      left: this.paddingLeft || 0,\n      top: this.paddingTop || 0,\n      right: this.paddingRight || 0,\n      bottom: this.paddingBottom || 0\n    };\n  }\n  getTicks() {\n    return this.ticks;\n  }\n  getLabels() {\n    const data = this.chart.data;\n    return this.options.labels || (this.isHorizontal() ? data.xLabels : data.yLabels) || data.labels || [];\n  }\n  getLabelItems(chartArea = this.chart.chartArea) {\n    const items = this._labelItems || (this._labelItems = this._computeLabelItems(chartArea));\n    return items;\n  }\n  beforeLayout() {\n    this._cache = {};\n    this._dataLimitsCached = false;\n  }\n  beforeUpdate() {\n    callback(this.options.beforeUpdate, [this]);\n  }\n  update(maxWidth, maxHeight, margins) {\n    const {\n      beginAtZero,\n      grace,\n      ticks: tickOpts\n    } = this.options;\n    const sampleSize = tickOpts.sampleSize;\n    this.beforeUpdate();\n    this.maxWidth = maxWidth;\n    this.maxHeight = maxHeight;\n    this._margins = margins = Object.assign({\n      left: 0,\n      right: 0,\n      top: 0,\n      bottom: 0\n    }, margins);\n    this.ticks = null;\n    this._labelSizes = null;\n    this._gridLineItems = null;\n    this._labelItems = null;\n    this.beforeSetDimensions();\n    this.setDimensions();\n    this.afterSetDimensions();\n    this._maxLength = this.isHorizontal() ? this.width + margins.left + margins.right : this.height + margins.top + margins.bottom;\n    if (!this._dataLimitsCached) {\n      this.beforeDataLimits();\n      this.determineDataLimits();\n      this.afterDataLimits();\n      this._range = _addGrace(this, grace, beginAtZero);\n      this._dataLimitsCached = true;\n    }\n    this.beforeBuildTicks();\n    this.ticks = this.buildTicks() || [];\n    this.afterBuildTicks();\n    const samplingEnabled = sampleSize < this.ticks.length;\n    this._convertTicksToLabels(samplingEnabled ? sample(this.ticks, sampleSize) : this.ticks);\n    this.configure();\n    this.beforeCalculateLabelRotation();\n    this.calculateLabelRotation();\n    this.afterCalculateLabelRotation();\n    if (tickOpts.display && (tickOpts.autoSkip || tickOpts.source === 'auto')) {\n      this.ticks = autoSkip(this, this.ticks);\n      this._labelSizes = null;\n      this.afterAutoSkip();\n    }\n    if (samplingEnabled) {\n      this._convertTicksToLabels(this.ticks);\n    }\n    this.beforeFit();\n    this.fit();\n    this.afterFit();\n    this.afterUpdate();\n  }\n  configure() {\n    let reversePixels = this.options.reverse;\n    let startPixel, endPixel;\n    if (this.isHorizontal()) {\n      startPixel = this.left;\n      endPixel = this.right;\n    } else {\n      startPixel = this.top;\n      endPixel = this.bottom;\n      reversePixels = !reversePixels;\n    }\n    this._startPixel = startPixel;\n    this._endPixel = endPixel;\n    this._reversePixels = reversePixels;\n    this._length = endPixel - startPixel;\n    this._alignToPixels = this.options.alignToPixels;\n  }\n  afterUpdate() {\n    callback(this.options.afterUpdate, [this]);\n  }\n  beforeSetDimensions() {\n    callback(this.options.beforeSetDimensions, [this]);\n  }\n  setDimensions() {\n    if (this.isHorizontal()) {\n      this.width = this.maxWidth;\n      this.left = 0;\n      this.right = this.width;\n    } else {\n      this.height = this.maxHeight;\n      this.top = 0;\n      this.bottom = this.height;\n    }\n    this.paddingLeft = 0;\n    this.paddingTop = 0;\n    this.paddingRight = 0;\n    this.paddingBottom = 0;\n  }\n  afterSetDimensions() {\n    callback(this.options.afterSetDimensions, [this]);\n  }\n  _callHooks(name) {\n    this.chart.notifyPlugins(name, this.getContext());\n    callback(this.options[name], [this]);\n  }\n  beforeDataLimits() {\n    this._callHooks('beforeDataLimits');\n  }\n  determineDataLimits() {}\n  afterDataLimits() {\n    this._callHooks('afterDataLimits');\n  }\n  beforeBuildTicks() {\n    this._callHooks('beforeBuildTicks');\n  }\n  buildTicks() {\n    return [];\n  }\n  afterBuildTicks() {\n    this._callHooks('afterBuildTicks');\n  }\n  beforeTickToLabelConversion() {\n    callback(this.options.beforeTickToLabelConversion, [this]);\n  }\n  generateTickLabels(ticks) {\n    const tickOpts = this.options.ticks;\n    let i, ilen, tick;\n    for (i = 0, ilen = ticks.length; i < ilen; i++) {\n      tick = ticks[i];\n      tick.label = callback(tickOpts.callback, [tick.value, i, ticks], this);\n    }\n  }\n  afterTickToLabelConversion() {\n    callback(this.options.afterTickToLabelConversion, [this]);\n  }\n  beforeCalculateLabelRotation() {\n    callback(this.options.beforeCalculateLabelRotation, [this]);\n  }\n  calculateLabelRotation() {\n    const options = this.options;\n    const tickOpts = options.ticks;\n    const numTicks = getTicksLimit(this.ticks.length, options.ticks.maxTicksLimit);\n    const minRotation = tickOpts.minRotation || 0;\n    const maxRotation = tickOpts.maxRotation;\n    let labelRotation = minRotation;\n    let tickWidth, maxHeight, maxLabelDiagonal;\n    if (!this._isVisible() || !tickOpts.display || minRotation >= maxRotation || numTicks <= 1 || !this.isHorizontal()) {\n      this.labelRotation = minRotation;\n      return;\n    }\n    const labelSizes = this._getLabelSizes();\n    const maxLabelWidth = labelSizes.widest.width;\n    const maxLabelHeight = labelSizes.highest.height;\n    const maxWidth = _limitValue(this.chart.width - maxLabelWidth, 0, this.maxWidth);\n    tickWidth = options.offset ? this.maxWidth / numTicks : maxWidth / (numTicks - 1);\n    if (maxLabelWidth + 6 > tickWidth) {\n      tickWidth = maxWidth / (numTicks - (options.offset ? 0.5 : 1));\n      maxHeight = this.maxHeight - getTickMarkLength(options.grid) - tickOpts.padding - getTitleHeight(options.title, this.chart.options.font);\n      maxLabelDiagonal = Math.sqrt(maxLabelWidth * maxLabelWidth + maxLabelHeight * maxLabelHeight);\n      labelRotation = toDegrees(Math.min(Math.asin(_limitValue((labelSizes.highest.height + 6) / tickWidth, -1, 1)), Math.asin(_limitValue(maxHeight / maxLabelDiagonal, -1, 1)) - Math.asin(_limitValue(maxLabelHeight / maxLabelDiagonal, -1, 1))));\n      labelRotation = Math.max(minRotation, Math.min(maxRotation, labelRotation));\n    }\n    this.labelRotation = labelRotation;\n  }\n  afterCalculateLabelRotation() {\n    callback(this.options.afterCalculateLabelRotation, [this]);\n  }\n  afterAutoSkip() {}\n  beforeFit() {\n    callback(this.options.beforeFit, [this]);\n  }\n  fit() {\n    const minSize = {\n      width: 0,\n      height: 0\n    };\n    const {\n      chart,\n      options: {\n        ticks: tickOpts,\n        title: titleOpts,\n        grid: gridOpts\n      }\n    } = this;\n    const display = this._isVisible();\n    const isHorizontal = this.isHorizontal();\n    if (display) {\n      const titleHeight = getTitleHeight(titleOpts, chart.options.font);\n      if (isHorizontal) {\n        minSize.width = this.maxWidth;\n        minSize.height = getTickMarkLength(gridOpts) + titleHeight;\n      } else {\n        minSize.height = this.maxHeight;\n        minSize.width = getTickMarkLength(gridOpts) + titleHeight;\n      }\n      if (tickOpts.display && this.ticks.length) {\n        const {\n          first,\n          last,\n          widest,\n          highest\n        } = this._getLabelSizes();\n        const tickPadding = tickOpts.padding * 2;\n        const angleRadians = toRadians(this.labelRotation);\n        const cos = Math.cos(angleRadians);\n        const sin = Math.sin(angleRadians);\n        if (isHorizontal) {\n          const labelHeight = tickOpts.mirror ? 0 : sin * widest.width + cos * highest.height;\n          minSize.height = Math.min(this.maxHeight, minSize.height + labelHeight + tickPadding);\n        } else {\n          const labelWidth = tickOpts.mirror ? 0 : cos * widest.width + sin * highest.height;\n          minSize.width = Math.min(this.maxWidth, minSize.width + labelWidth + tickPadding);\n        }\n        this._calculatePadding(first, last, sin, cos);\n      }\n    }\n    this._handleMargins();\n    if (isHorizontal) {\n      this.width = this._length = chart.width - this._margins.left - this._margins.right;\n      this.height = minSize.height;\n    } else {\n      this.width = minSize.width;\n      this.height = this._length = chart.height - this._margins.top - this._margins.bottom;\n    }\n  }\n  _calculatePadding(first, last, sin, cos) {\n    const {\n      ticks: {\n        align,\n        padding\n      },\n      position\n    } = this.options;\n    const isRotated = this.labelRotation !== 0;\n    const labelsBelowTicks = position !== 'top' && this.axis === 'x';\n    if (this.isHorizontal()) {\n      const offsetLeft = this.getPixelForTick(0) - this.left;\n      const offsetRight = this.right - this.getPixelForTick(this.ticks.length - 1);\n      let paddingLeft = 0;\n      let paddingRight = 0;\n      if (isRotated) {\n        if (labelsBelowTicks) {\n          paddingLeft = cos * first.width;\n          paddingRight = sin * last.height;\n        } else {\n          paddingLeft = sin * first.height;\n          paddingRight = cos * last.width;\n        }\n      } else if (align === 'start') {\n        paddingRight = last.width;\n      } else if (align === 'end') {\n        paddingLeft = first.width;\n      } else if (align !== 'inner') {\n        paddingLeft = first.width / 2;\n        paddingRight = last.width / 2;\n      }\n      this.paddingLeft = Math.max((paddingLeft - offsetLeft + padding) * this.width / (this.width - offsetLeft), 0);\n      this.paddingRight = Math.max((paddingRight - offsetRight + padding) * this.width / (this.width - offsetRight), 0);\n    } else {\n      let paddingTop = last.height / 2;\n      let paddingBottom = first.height / 2;\n      if (align === 'start') {\n        paddingTop = 0;\n        paddingBottom = first.height;\n      } else if (align === 'end') {\n        paddingTop = last.height;\n        paddingBottom = 0;\n      }\n      this.paddingTop = paddingTop + padding;\n      this.paddingBottom = paddingBottom + padding;\n    }\n  }\n  _handleMargins() {\n    if (this._margins) {\n      this._margins.left = Math.max(this.paddingLeft, this._margins.left);\n      this._margins.top = Math.max(this.paddingTop, this._margins.top);\n      this._margins.right = Math.max(this.paddingRight, this._margins.right);\n      this._margins.bottom = Math.max(this.paddingBottom, this._margins.bottom);\n    }\n  }\n  afterFit() {\n    callback(this.options.afterFit, [this]);\n  }\n  isHorizontal() {\n    const {\n      axis,\n      position\n    } = this.options;\n    return position === 'top' || position === 'bottom' || axis === 'x';\n  }\n  isFullSize() {\n    return this.options.fullSize;\n  }\n  _convertTicksToLabels(ticks) {\n    this.beforeTickToLabelConversion();\n    this.generateTickLabels(ticks);\n    let i, ilen;\n    for (i = 0, ilen = ticks.length; i < ilen; i++) {\n      if (isNullOrUndef(ticks[i].label)) {\n        ticks.splice(i, 1);\n        ilen--;\n        i--;\n      }\n    }\n    this.afterTickToLabelConversion();\n  }\n  _getLabelSizes() {\n    let labelSizes = this._labelSizes;\n    if (!labelSizes) {\n      const sampleSize = this.options.ticks.sampleSize;\n      let ticks = this.ticks;\n      if (sampleSize < ticks.length) {\n        ticks = sample(ticks, sampleSize);\n      }\n      this._labelSizes = labelSizes = this._computeLabelSizes(ticks, ticks.length, this.options.ticks.maxTicksLimit);\n    }\n    return labelSizes;\n  }\n  _computeLabelSizes(ticks, length, maxTicksLimit) {\n    const {\n      ctx,\n      _longestTextCache: caches\n    } = this;\n    const widths = [];\n    const heights = [];\n    const increment = Math.floor(length / getTicksLimit(length, maxTicksLimit));\n    let widestLabelSize = 0;\n    let highestLabelSize = 0;\n    let i, j, jlen, label, tickFont, fontString, cache, lineHeight, width, height, nestedLabel;\n    for (i = 0; i < length; i += increment) {\n      label = ticks[i].label;\n      tickFont = this._resolveTickFontOptions(i);\n      ctx.font = fontString = tickFont.string;\n      cache = caches[fontString] = caches[fontString] || {\n        data: {},\n        gc: []\n      };\n      lineHeight = tickFont.lineHeight;\n      width = height = 0;\n      if (!isNullOrUndef(label) && !isArray(label)) {\n        width = _measureText(ctx, cache.data, cache.gc, width, label);\n        height = lineHeight;\n      } else if (isArray(label)) {\n        for (j = 0, jlen = label.length; j < jlen; ++j) {\n          nestedLabel = label[j];\n          if (!isNullOrUndef(nestedLabel) && !isArray(nestedLabel)) {\n            width = _measureText(ctx, cache.data, cache.gc, width, nestedLabel);\n            height += lineHeight;\n          }\n        }\n      }\n      widths.push(width);\n      heights.push(height);\n      widestLabelSize = Math.max(width, widestLabelSize);\n      highestLabelSize = Math.max(height, highestLabelSize);\n    }\n    garbageCollect(caches, length);\n    const widest = widths.indexOf(widestLabelSize);\n    const highest = heights.indexOf(highestLabelSize);\n    const valueAt = idx => ({\n      width: widths[idx] || 0,\n      height: heights[idx] || 0\n    });\n    return {\n      first: valueAt(0),\n      last: valueAt(length - 1),\n      widest: valueAt(widest),\n      highest: valueAt(highest),\n      widths,\n      heights\n    };\n  }\n  getLabelForValue(value) {\n    return value;\n  }\n  getPixelForValue(value, index) {\n    return NaN;\n  }\n  getValueForPixel(pixel) {}\n  getPixelForTick(index) {\n    const ticks = this.ticks;\n    if (index < 0 || index > ticks.length - 1) {\n      return null;\n    }\n    return this.getPixelForValue(ticks[index].value);\n  }\n  getPixelForDecimal(decimal) {\n    if (this._reversePixels) {\n      decimal = 1 - decimal;\n    }\n    const pixel = this._startPixel + decimal * this._length;\n    return _int16Range(this._alignToPixels ? _alignPixel(this.chart, pixel, 0) : pixel);\n  }\n  getDecimalForPixel(pixel) {\n    const decimal = (pixel - this._startPixel) / this._length;\n    return this._reversePixels ? 1 - decimal : decimal;\n  }\n  getBasePixel() {\n    return this.getPixelForValue(this.getBaseValue());\n  }\n  getBaseValue() {\n    const {\n      min,\n      max\n    } = this;\n    return min < 0 && max < 0 ? max : min > 0 && max > 0 ? min : 0;\n  }\n  getContext(index) {\n    const ticks = this.ticks || [];\n    if (index >= 0 && index < ticks.length) {\n      const tick = ticks[index];\n      return tick.$context || (tick.$context = createTickContext(this.getContext(), index, tick));\n    }\n    return this.$context || (this.$context = createScaleContext(this.chart.getContext(), this));\n  }\n  _tickSize() {\n    const optionTicks = this.options.ticks;\n    const rot = toRadians(this.labelRotation);\n    const cos = Math.abs(Math.cos(rot));\n    const sin = Math.abs(Math.sin(rot));\n    const labelSizes = this._getLabelSizes();\n    const padding = optionTicks.autoSkipPadding || 0;\n    const w = labelSizes ? labelSizes.widest.width + padding : 0;\n    const h = labelSizes ? labelSizes.highest.height + padding : 0;\n    return this.isHorizontal() ? h * cos > w * sin ? w / cos : h / sin : h * sin < w * cos ? h / cos : w / sin;\n  }\n  _isVisible() {\n    const display = this.options.display;\n    if (display !== 'auto') {\n      return !!display;\n    }\n    return this.getMatchingVisibleMetas().length > 0;\n  }\n  _computeGridLineItems(chartArea) {\n    const axis = this.axis;\n    const chart = this.chart;\n    const options = this.options;\n    const {\n      grid,\n      position,\n      border\n    } = options;\n    const offset = grid.offset;\n    const isHorizontal = this.isHorizontal();\n    const ticks = this.ticks;\n    const ticksLength = ticks.length + (offset ? 1 : 0);\n    const tl = getTickMarkLength(grid);\n    const items = [];\n    const borderOpts = border.setContext(this.getContext());\n    const axisWidth = borderOpts.display ? borderOpts.width : 0;\n    const axisHalfWidth = axisWidth / 2;\n    const alignBorderValue = function (pixel) {\n      return _alignPixel(chart, pixel, axisWidth);\n    };\n    let borderValue, i, lineValue, alignedLineValue;\n    let tx1, ty1, tx2, ty2, x1, y1, x2, y2;\n    if (position === 'top') {\n      borderValue = alignBorderValue(this.bottom);\n      ty1 = this.bottom - tl;\n      ty2 = borderValue - axisHalfWidth;\n      y1 = alignBorderValue(chartArea.top) + axisHalfWidth;\n      y2 = chartArea.bottom;\n    } else if (position === 'bottom') {\n      borderValue = alignBorderValue(this.top);\n      y1 = chartArea.top;\n      y2 = alignBorderValue(chartArea.bottom) - axisHalfWidth;\n      ty1 = borderValue + axisHalfWidth;\n      ty2 = this.top + tl;\n    } else if (position === 'left') {\n      borderValue = alignBorderValue(this.right);\n      tx1 = this.right - tl;\n      tx2 = borderValue - axisHalfWidth;\n      x1 = alignBorderValue(chartArea.left) + axisHalfWidth;\n      x2 = chartArea.right;\n    } else if (position === 'right') {\n      borderValue = alignBorderValue(this.left);\n      x1 = chartArea.left;\n      x2 = alignBorderValue(chartArea.right) - axisHalfWidth;\n      tx1 = borderValue + axisHalfWidth;\n      tx2 = this.left + tl;\n    } else if (axis === 'x') {\n      if (position === 'center') {\n        borderValue = alignBorderValue((chartArea.top + chartArea.bottom) / 2 + 0.5);\n      } else if (isObject(position)) {\n        const positionAxisID = Object.keys(position)[0];\n        const value = position[positionAxisID];\n        borderValue = alignBorderValue(this.chart.scales[positionAxisID].getPixelForValue(value));\n      }\n      y1 = chartArea.top;\n      y2 = chartArea.bottom;\n      ty1 = borderValue + axisHalfWidth;\n      ty2 = ty1 + tl;\n    } else if (axis === 'y') {\n      if (position === 'center') {\n        borderValue = alignBorderValue((chartArea.left + chartArea.right) / 2);\n      } else if (isObject(position)) {\n        const positionAxisID = Object.keys(position)[0];\n        const value = position[positionAxisID];\n        borderValue = alignBorderValue(this.chart.scales[positionAxisID].getPixelForValue(value));\n      }\n      tx1 = borderValue - axisHalfWidth;\n      tx2 = tx1 - tl;\n      x1 = chartArea.left;\n      x2 = chartArea.right;\n    }\n    const limit = valueOrDefault(options.ticks.maxTicksLimit, ticksLength);\n    const step = Math.max(1, Math.ceil(ticksLength / limit));\n    for (i = 0; i < ticksLength; i += step) {\n      const context = this.getContext(i);\n      const optsAtIndex = grid.setContext(context);\n      const optsAtIndexBorder = border.setContext(context);\n      const lineWidth = optsAtIndex.lineWidth;\n      const lineColor = optsAtIndex.color;\n      const borderDash = optsAtIndexBorder.dash || [];\n      const borderDashOffset = optsAtIndexBorder.dashOffset;\n      const tickWidth = optsAtIndex.tickWidth;\n      const tickColor = optsAtIndex.tickColor;\n      const tickBorderDash = optsAtIndex.tickBorderDash || [];\n      const tickBorderDashOffset = optsAtIndex.tickBorderDashOffset;\n      lineValue = getPixelForGridLine(this, i, offset);\n      if (lineValue === undefined) {\n        continue;\n      }\n      alignedLineValue = _alignPixel(chart, lineValue, lineWidth);\n      if (isHorizontal) {\n        tx1 = tx2 = x1 = x2 = alignedLineValue;\n      } else {\n        ty1 = ty2 = y1 = y2 = alignedLineValue;\n      }\n      items.push({\n        tx1,\n        ty1,\n        tx2,\n        ty2,\n        x1,\n        y1,\n        x2,\n        y2,\n        width: lineWidth,\n        color: lineColor,\n        borderDash,\n        borderDashOffset,\n        tickWidth,\n        tickColor,\n        tickBorderDash,\n        tickBorderDashOffset\n      });\n    }\n    this._ticksLength = ticksLength;\n    this._borderValue = borderValue;\n    return items;\n  }\n  _computeLabelItems(chartArea) {\n    const axis = this.axis;\n    const options = this.options;\n    const {\n      position,\n      ticks: optionTicks\n    } = options;\n    const isHorizontal = this.isHorizontal();\n    const ticks = this.ticks;\n    const {\n      align,\n      crossAlign,\n      padding,\n      mirror\n    } = optionTicks;\n    const tl = getTickMarkLength(options.grid);\n    const tickAndPadding = tl + padding;\n    const hTickAndPadding = mirror ? -padding : tickAndPadding;\n    const rotation = -toRadians(this.labelRotation);\n    const items = [];\n    let i, ilen, tick, label, x, y, textAlign, pixel, font, lineHeight, lineCount, textOffset;\n    let textBaseline = 'middle';\n    if (position === 'top') {\n      y = this.bottom - hTickAndPadding;\n      textAlign = this._getXAxisLabelAlignment();\n    } else if (position === 'bottom') {\n      y = this.top + hTickAndPadding;\n      textAlign = this._getXAxisLabelAlignment();\n    } else if (position === 'left') {\n      const ret = this._getYAxisLabelAlignment(tl);\n      textAlign = ret.textAlign;\n      x = ret.x;\n    } else if (position === 'right') {\n      const ret = this._getYAxisLabelAlignment(tl);\n      textAlign = ret.textAlign;\n      x = ret.x;\n    } else if (axis === 'x') {\n      if (position === 'center') {\n        y = (chartArea.top + chartArea.bottom) / 2 + tickAndPadding;\n      } else if (isObject(position)) {\n        const positionAxisID = Object.keys(position)[0];\n        const value = position[positionAxisID];\n        y = this.chart.scales[positionAxisID].getPixelForValue(value) + tickAndPadding;\n      }\n      textAlign = this._getXAxisLabelAlignment();\n    } else if (axis === 'y') {\n      if (position === 'center') {\n        x = (chartArea.left + chartArea.right) / 2 - tickAndPadding;\n      } else if (isObject(position)) {\n        const positionAxisID = Object.keys(position)[0];\n        const value = position[positionAxisID];\n        x = this.chart.scales[positionAxisID].getPixelForValue(value);\n      }\n      textAlign = this._getYAxisLabelAlignment(tl).textAlign;\n    }\n    if (axis === 'y') {\n      if (align === 'start') {\n        textBaseline = 'top';\n      } else if (align === 'end') {\n        textBaseline = 'bottom';\n      }\n    }\n    const labelSizes = this._getLabelSizes();\n    for (i = 0, ilen = ticks.length; i < ilen; ++i) {\n      tick = ticks[i];\n      label = tick.label;\n      const optsAtIndex = optionTicks.setContext(this.getContext(i));\n      pixel = this.getPixelForTick(i) + optionTicks.labelOffset;\n      font = this._resolveTickFontOptions(i);\n      lineHeight = font.lineHeight;\n      lineCount = isArray(label) ? label.length : 1;\n      const halfCount = lineCount / 2;\n      const color = optsAtIndex.color;\n      const strokeColor = optsAtIndex.textStrokeColor;\n      const strokeWidth = optsAtIndex.textStrokeWidth;\n      let tickTextAlign = textAlign;\n      if (isHorizontal) {\n        x = pixel;\n        if (textAlign === 'inner') {\n          if (i === ilen - 1) {\n            tickTextAlign = !this.options.reverse ? 'right' : 'left';\n          } else if (i === 0) {\n            tickTextAlign = !this.options.reverse ? 'left' : 'right';\n          } else {\n            tickTextAlign = 'center';\n          }\n        }\n        if (position === 'top') {\n          if (crossAlign === 'near' || rotation !== 0) {\n            textOffset = -lineCount * lineHeight + lineHeight / 2;\n          } else if (crossAlign === 'center') {\n            textOffset = -labelSizes.highest.height / 2 - halfCount * lineHeight + lineHeight;\n          } else {\n            textOffset = -labelSizes.highest.height + lineHeight / 2;\n          }\n        } else {\n          if (crossAlign === 'near' || rotation !== 0) {\n            textOffset = lineHeight / 2;\n          } else if (crossAlign === 'center') {\n            textOffset = labelSizes.highest.height / 2 - halfCount * lineHeight;\n          } else {\n            textOffset = labelSizes.highest.height - lineCount * lineHeight;\n          }\n        }\n        if (mirror) {\n          textOffset *= -1;\n        }\n        if (rotation !== 0 && !optsAtIndex.showLabelBackdrop) {\n          x += lineHeight / 2 * Math.sin(rotation);\n        }\n      } else {\n        y = pixel;\n        textOffset = (1 - lineCount) * lineHeight / 2;\n      }\n      let backdrop;\n      if (optsAtIndex.showLabelBackdrop) {\n        const labelPadding = toPadding(optsAtIndex.backdropPadding);\n        const height = labelSizes.heights[i];\n        const width = labelSizes.widths[i];\n        let top = textOffset - labelPadding.top;\n        let left = 0 - labelPadding.left;\n        switch (textBaseline) {\n          case 'middle':\n            top -= height / 2;\n            break;\n          case 'bottom':\n            top -= height;\n            break;\n        }\n        switch (textAlign) {\n          case 'center':\n            left -= width / 2;\n            break;\n          case 'right':\n            left -= width;\n            break;\n          case 'inner':\n            if (i === ilen - 1) {\n              left -= width;\n            } else if (i > 0) {\n              left -= width / 2;\n            }\n            break;\n        }\n        backdrop = {\n          left,\n          top,\n          width: width + labelPadding.width,\n          height: height + labelPadding.height,\n          color: optsAtIndex.backdropColor\n        };\n      }\n      items.push({\n        label,\n        font,\n        textOffset,\n        options: {\n          rotation,\n          color,\n          strokeColor,\n          strokeWidth,\n          textAlign: tickTextAlign,\n          textBaseline,\n          translation: [x, y],\n          backdrop\n        }\n      });\n    }\n    return items;\n  }\n  _getXAxisLabelAlignment() {\n    const {\n      position,\n      ticks\n    } = this.options;\n    const rotation = -toRadians(this.labelRotation);\n    if (rotation) {\n      return position === 'top' ? 'left' : 'right';\n    }\n    let align = 'center';\n    if (ticks.align === 'start') {\n      align = 'left';\n    } else if (ticks.align === 'end') {\n      align = 'right';\n    } else if (ticks.align === 'inner') {\n      align = 'inner';\n    }\n    return align;\n  }\n  _getYAxisLabelAlignment(tl) {\n    const {\n      position,\n      ticks: {\n        crossAlign,\n        mirror,\n        padding\n      }\n    } = this.options;\n    const labelSizes = this._getLabelSizes();\n    const tickAndPadding = tl + padding;\n    const widest = labelSizes.widest.width;\n    let textAlign;\n    let x;\n    if (position === 'left') {\n      if (mirror) {\n        x = this.right + padding;\n        if (crossAlign === 'near') {\n          textAlign = 'left';\n        } else if (crossAlign === 'center') {\n          textAlign = 'center';\n          x += widest / 2;\n        } else {\n          textAlign = 'right';\n          x += widest;\n        }\n      } else {\n        x = this.right - tickAndPadding;\n        if (crossAlign === 'near') {\n          textAlign = 'right';\n        } else if (crossAlign === 'center') {\n          textAlign = 'center';\n          x -= widest / 2;\n        } else {\n          textAlign = 'left';\n          x = this.left;\n        }\n      }\n    } else if (position === 'right') {\n      if (mirror) {\n        x = this.left + padding;\n        if (crossAlign === 'near') {\n          textAlign = 'right';\n        } else if (crossAlign === 'center') {\n          textAlign = 'center';\n          x -= widest / 2;\n        } else {\n          textAlign = 'left';\n          x -= widest;\n        }\n      } else {\n        x = this.left + tickAndPadding;\n        if (crossAlign === 'near') {\n          textAlign = 'left';\n        } else if (crossAlign === 'center') {\n          textAlign = 'center';\n          x += widest / 2;\n        } else {\n          textAlign = 'right';\n          x = this.right;\n        }\n      }\n    } else {\n      textAlign = 'right';\n    }\n    return {\n      textAlign,\n      x\n    };\n  }\n  _computeLabelArea() {\n    if (this.options.ticks.mirror) {\n      return;\n    }\n    const chart = this.chart;\n    const position = this.options.position;\n    if (position === 'left' || position === 'right') {\n      return {\n        top: 0,\n        left: this.left,\n        bottom: chart.height,\n        right: this.right\n      };\n    }\n    if (position === 'top' || position === 'bottom') {\n      return {\n        top: this.top,\n        left: 0,\n        bottom: this.bottom,\n        right: chart.width\n      };\n    }\n  }\n  drawBackground() {\n    const {\n      ctx,\n      options: {\n        backgroundColor\n      },\n      left,\n      top,\n      width,\n      height\n    } = this;\n    if (backgroundColor) {\n      ctx.save();\n      ctx.fillStyle = backgroundColor;\n      ctx.fillRect(left, top, width, height);\n      ctx.restore();\n    }\n  }\n  getLineWidthForValue(value) {\n    const grid = this.options.grid;\n    if (!this._isVisible() || !grid.display) {\n      return 0;\n    }\n    const ticks = this.ticks;\n    const index = ticks.findIndex(t => t.value === value);\n    if (index >= 0) {\n      const opts = grid.setContext(this.getContext(index));\n      return opts.lineWidth;\n    }\n    return 0;\n  }\n  drawGrid(chartArea) {\n    const grid = this.options.grid;\n    const ctx = this.ctx;\n    const items = this._gridLineItems || (this._gridLineItems = this._computeGridLineItems(chartArea));\n    let i, ilen;\n    const drawLine = (p1, p2, style) => {\n      if (!style.width || !style.color) {\n        return;\n      }\n      ctx.save();\n      ctx.lineWidth = style.width;\n      ctx.strokeStyle = style.color;\n      ctx.setLineDash(style.borderDash || []);\n      ctx.lineDashOffset = style.borderDashOffset;\n      ctx.beginPath();\n      ctx.moveTo(p1.x, p1.y);\n      ctx.lineTo(p2.x, p2.y);\n      ctx.stroke();\n      ctx.restore();\n    };\n    if (grid.display) {\n      for (i = 0, ilen = items.length; i < ilen; ++i) {\n        const item = items[i];\n        if (grid.drawOnChartArea) {\n          drawLine({\n            x: item.x1,\n            y: item.y1\n          }, {\n            x: item.x2,\n            y: item.y2\n          }, item);\n        }\n        if (grid.drawTicks) {\n          drawLine({\n            x: item.tx1,\n            y: item.ty1\n          }, {\n            x: item.tx2,\n            y: item.ty2\n          }, {\n            color: item.tickColor,\n            width: item.tickWidth,\n            borderDash: item.tickBorderDash,\n            borderDashOffset: item.tickBorderDashOffset\n          });\n        }\n      }\n    }\n  }\n  drawBorder() {\n    const {\n      chart,\n      ctx,\n      options: {\n        border,\n        grid\n      }\n    } = this;\n    const borderOpts = border.setContext(this.getContext());\n    const axisWidth = border.display ? borderOpts.width : 0;\n    if (!axisWidth) {\n      return;\n    }\n    const lastLineWidth = grid.setContext(this.getContext(0)).lineWidth;\n    const borderValue = this._borderValue;\n    let x1, x2, y1, y2;\n    if (this.isHorizontal()) {\n      x1 = _alignPixel(chart, this.left, axisWidth) - axisWidth / 2;\n      x2 = _alignPixel(chart, this.right, lastLineWidth) + lastLineWidth / 2;\n      y1 = y2 = borderValue;\n    } else {\n      y1 = _alignPixel(chart, this.top, axisWidth) - axisWidth / 2;\n      y2 = _alignPixel(chart, this.bottom, lastLineWidth) + lastLineWidth / 2;\n      x1 = x2 = borderValue;\n    }\n    ctx.save();\n    ctx.lineWidth = borderOpts.width;\n    ctx.strokeStyle = borderOpts.color;\n    ctx.beginPath();\n    ctx.moveTo(x1, y1);\n    ctx.lineTo(x2, y2);\n    ctx.stroke();\n    ctx.restore();\n  }\n  drawLabels(chartArea) {\n    const optionTicks = this.options.ticks;\n    if (!optionTicks.display) {\n      return;\n    }\n    const ctx = this.ctx;\n    const area = this._computeLabelArea();\n    if (area) {\n      clipArea(ctx, area);\n    }\n    const items = this.getLabelItems(chartArea);\n    for (const item of items) {\n      const renderTextOptions = item.options;\n      const tickFont = item.font;\n      const label = item.label;\n      const y = item.textOffset;\n      renderText(ctx, label, 0, y, tickFont, renderTextOptions);\n    }\n    if (area) {\n      unclipArea(ctx);\n    }\n  }\n  drawTitle() {\n    const {\n      ctx,\n      options: {\n        position,\n        title,\n        reverse\n      }\n    } = this;\n    if (!title.display) {\n      return;\n    }\n    const font = toFont(title.font);\n    const padding = toPadding(title.padding);\n    const align = title.align;\n    let offset = font.lineHeight / 2;\n    if (position === 'bottom' || position === 'center' || isObject(position)) {\n      offset += padding.bottom;\n      if (isArray(title.text)) {\n        offset += font.lineHeight * (title.text.length - 1);\n      }\n    } else {\n      offset += padding.top;\n    }\n    const {\n      titleX,\n      titleY,\n      maxWidth,\n      rotation\n    } = titleArgs(this, offset, position, align);\n    renderText(ctx, title.text, 0, 0, font, {\n      color: title.color,\n      maxWidth,\n      rotation,\n      textAlign: titleAlign(align, position, reverse),\n      textBaseline: 'middle',\n      translation: [titleX, titleY]\n    });\n  }\n  draw(chartArea) {\n    if (!this._isVisible()) {\n      return;\n    }\n    this.drawBackground();\n    this.drawGrid(chartArea);\n    this.drawBorder();\n    this.drawTitle();\n    this.drawLabels(chartArea);\n  }\n  _layers() {\n    const opts = this.options;\n    const tz = opts.ticks && opts.ticks.z || 0;\n    const gz = valueOrDefault(opts.grid && opts.grid.z, -1);\n    const bz = valueOrDefault(opts.border && opts.border.z, 0);\n    if (!this._isVisible() || this.draw !== Scale.prototype.draw) {\n      return [{\n        z: tz,\n        draw: chartArea => {\n          this.draw(chartArea);\n        }\n      }];\n    }\n    return [{\n      z: gz,\n      draw: chartArea => {\n        this.drawBackground();\n        this.drawGrid(chartArea);\n        this.drawTitle();\n      }\n    }, {\n      z: bz,\n      draw: () => {\n        this.drawBorder();\n      }\n    }, {\n      z: tz,\n      draw: chartArea => {\n        this.drawLabels(chartArea);\n      }\n    }];\n  }\n  getMatchingVisibleMetas(type) {\n    const metas = this.chart.getSortedVisibleDatasetMetas();\n    const axisID = this.axis + 'AxisID';\n    const result = [];\n    let i, ilen;\n    for (i = 0, ilen = metas.length; i < ilen; ++i) {\n      const meta = metas[i];\n      if (meta[axisID] === this.id && (!type || meta.type === type)) {\n        result.push(meta);\n      }\n    }\n    return result;\n  }\n  _resolveTickFontOptions(index) {\n    const opts = this.options.ticks.setContext(this.getContext(index));\n    return toFont(opts.font);\n  }\n  _maxDigits() {\n    const fontSize = this._resolveTickFontOptions(0).lineHeight;\n    return (this.isHorizontal() ? this.width : this.height) / fontSize;\n  }\n}\nclass TypedRegistry {\n  constructor(type, scope, override) {\n    this.type = type;\n    this.scope = scope;\n    this.override = override;\n    this.items = Object.create(null);\n  }\n  isForType(type) {\n    return Object.prototype.isPrototypeOf.call(this.type.prototype, type.prototype);\n  }\n  register(item) {\n    const proto = Object.getPrototypeOf(item);\n    let parentScope;\n    if (isIChartComponent(proto)) {\n      parentScope = this.register(proto);\n    }\n    const items = this.items;\n    const id = item.id;\n    const scope = this.scope + '.' + id;\n    if (!id) {\n      throw new Error('class does not have id: ' + item);\n    }\n    if (id in items) {\n      return scope;\n    }\n    items[id] = item;\n    registerDefaults(item, scope, parentScope);\n    if (this.override) {\n      defaults.override(item.id, item.overrides);\n    }\n    return scope;\n  }\n  get(id) {\n    return this.items[id];\n  }\n  unregister(item) {\n    const items = this.items;\n    const id = item.id;\n    const scope = this.scope;\n    if (id in items) {\n      delete items[id];\n    }\n    if (scope && id in defaults[scope]) {\n      delete defaults[scope][id];\n      if (this.override) {\n        delete overrides[id];\n      }\n    }\n  }\n}\nfunction registerDefaults(item, scope, parentScope) {\n  const itemDefaults = merge(Object.create(null), [parentScope ? defaults.get(parentScope) : {}, defaults.get(scope), item.defaults]);\n  defaults.set(scope, itemDefaults);\n  if (item.defaultRoutes) {\n    routeDefaults(scope, item.defaultRoutes);\n  }\n  if (item.descriptors) {\n    defaults.describe(scope, item.descriptors);\n  }\n}\nfunction routeDefaults(scope, routes) {\n  Object.keys(routes).forEach(property => {\n    const propertyParts = property.split('.');\n    const sourceName = propertyParts.pop();\n    const sourceScope = [scope].concat(propertyParts).join('.');\n    const parts = routes[property].split('.');\n    const targetName = parts.pop();\n    const targetScope = parts.join('.');\n    defaults.route(sourceScope, sourceName, targetScope, targetName);\n  });\n}\nfunction isIChartComponent(proto) {\n  return 'id' in proto && 'defaults' in proto;\n}\nclass Registry {\n  constructor() {\n    this.controllers = new TypedRegistry(DatasetController, 'datasets', true);\n    this.elements = new TypedRegistry(Element, 'elements');\n    this.plugins = new TypedRegistry(Object, 'plugins');\n    this.scales = new TypedRegistry(Scale, 'scales');\n    this._typedRegistries = [this.controllers, this.scales, this.elements];\n  }\n  add(...args) {\n    this._each('register', args);\n  }\n  remove(...args) {\n    this._each('unregister', args);\n  }\n  addControllers(...args) {\n    this._each('register', args, this.controllers);\n  }\n  addElements(...args) {\n    this._each('register', args, this.elements);\n  }\n  addPlugins(...args) {\n    this._each('register', args, this.plugins);\n  }\n  addScales(...args) {\n    this._each('register', args, this.scales);\n  }\n  getController(id) {\n    return this._get(id, this.controllers, 'controller');\n  }\n  getElement(id) {\n    return this._get(id, this.elements, 'element');\n  }\n  getPlugin(id) {\n    return this._get(id, this.plugins, 'plugin');\n  }\n  getScale(id) {\n    return this._get(id, this.scales, 'scale');\n  }\n  removeControllers(...args) {\n    this._each('unregister', args, this.controllers);\n  }\n  removeElements(...args) {\n    this._each('unregister', args, this.elements);\n  }\n  removePlugins(...args) {\n    this._each('unregister', args, this.plugins);\n  }\n  removeScales(...args) {\n    this._each('unregister', args, this.scales);\n  }\n  _each(method, args, typedRegistry) {\n    [...args].forEach(arg => {\n      const reg = typedRegistry || this._getRegistryForType(arg);\n      if (typedRegistry || reg.isForType(arg) || reg === this.plugins && arg.id) {\n        this._exec(method, reg, arg);\n      } else {\n        each(arg, item => {\n          const itemReg = typedRegistry || this._getRegistryForType(item);\n          this._exec(method, itemReg, item);\n        });\n      }\n    });\n  }\n  _exec(method, registry, component) {\n    const camelMethod = _capitalize(method);\n    callback(component['before' + camelMethod], [], component);\n    registry[method](component);\n    callback(component['after' + camelMethod], [], component);\n  }\n  _getRegistryForType(type) {\n    for (let i = 0; i < this._typedRegistries.length; i++) {\n      const reg = this._typedRegistries[i];\n      if (reg.isForType(type)) {\n        return reg;\n      }\n    }\n    return this.plugins;\n  }\n  _get(id, typedRegistry, type) {\n    const item = typedRegistry.get(id);\n    if (item === undefined) {\n      throw new Error('\"' + id + '\" is not a registered ' + type + '.');\n    }\n    return item;\n  }\n}\nvar registry = /* #__PURE__ */new Registry();\nclass PluginService {\n  constructor() {\n    this._init = [];\n  }\n  notify(chart, hook, args, filter) {\n    if (hook === 'beforeInit') {\n      this._init = this._createDescriptors(chart, true);\n      this._notify(this._init, chart, 'install');\n    }\n    const descriptors = filter ? this._descriptors(chart).filter(filter) : this._descriptors(chart);\n    const result = this._notify(descriptors, chart, hook, args);\n    if (hook === 'afterDestroy') {\n      this._notify(descriptors, chart, 'stop');\n      this._notify(this._init, chart, 'uninstall');\n    }\n    return result;\n  }\n  _notify(descriptors, chart, hook, args) {\n    args = args || {};\n    for (const descriptor of descriptors) {\n      const plugin = descriptor.plugin;\n      const method = plugin[hook];\n      const params = [chart, args, descriptor.options];\n      if (callback(method, params, plugin) === false && args.cancelable) {\n        return false;\n      }\n    }\n    return true;\n  }\n  invalidate() {\n    if (!isNullOrUndef(this._cache)) {\n      this._oldCache = this._cache;\n      this._cache = undefined;\n    }\n  }\n  _descriptors(chart) {\n    if (this._cache) {\n      return this._cache;\n    }\n    const descriptors = this._cache = this._createDescriptors(chart);\n    this._notifyStateChanges(chart);\n    return descriptors;\n  }\n  _createDescriptors(chart, all) {\n    const config = chart && chart.config;\n    const options = valueOrDefault(config.options && config.options.plugins, {});\n    const plugins = allPlugins(config);\n    return options === false && !all ? [] : createDescriptors(chart, plugins, options, all);\n  }\n  _notifyStateChanges(chart) {\n    const previousDescriptors = this._oldCache || [];\n    const descriptors = this._cache;\n    const diff = (a, b) => a.filter(x => !b.some(y => x.plugin.id === y.plugin.id));\n    this._notify(diff(previousDescriptors, descriptors), chart, 'stop');\n    this._notify(diff(descriptors, previousDescriptors), chart, 'start');\n  }\n}\nfunction allPlugins(config) {\n  const localIds = {};\n  const plugins = [];\n  const keys = Object.keys(registry.plugins.items);\n  for (let i = 0; i < keys.length; i++) {\n    plugins.push(registry.getPlugin(keys[i]));\n  }\n  const local = config.plugins || [];\n  for (let i = 0; i < local.length; i++) {\n    const plugin = local[i];\n    if (plugins.indexOf(plugin) === -1) {\n      plugins.push(plugin);\n      localIds[plugin.id] = true;\n    }\n  }\n  return {\n    plugins,\n    localIds\n  };\n}\nfunction getOpts(options, all) {\n  if (!all && options === false) {\n    return null;\n  }\n  if (options === true) {\n    return {};\n  }\n  return options;\n}\nfunction createDescriptors(chart, {\n  plugins,\n  localIds\n}, options, all) {\n  const result = [];\n  const context = chart.getContext();\n  for (const plugin of plugins) {\n    const id = plugin.id;\n    const opts = getOpts(options[id], all);\n    if (opts === null) {\n      continue;\n    }\n    result.push({\n      plugin,\n      options: pluginOpts(chart.config, {\n        plugin,\n        local: localIds[id]\n      }, opts, context)\n    });\n  }\n  return result;\n}\nfunction pluginOpts(config, {\n  plugin,\n  local\n}, opts, context) {\n  const keys = config.pluginScopeKeys(plugin);\n  const scopes = config.getOptionScopes(opts, keys);\n  if (local && plugin.defaults) {\n    scopes.push(plugin.defaults);\n  }\n  return config.createResolver(scopes, context, [''], {\n    scriptable: false,\n    indexable: false,\n    allKeys: true\n  });\n}\nfunction getIndexAxis(type, options) {\n  const datasetDefaults = defaults.datasets[type] || {};\n  const datasetOptions = (options.datasets || {})[type] || {};\n  return datasetOptions.indexAxis || options.indexAxis || datasetDefaults.indexAxis || 'x';\n}\nfunction getAxisFromDefaultScaleID(id, indexAxis) {\n  let axis = id;\n  if (id === '_index_') {\n    axis = indexAxis;\n  } else if (id === '_value_') {\n    axis = indexAxis === 'x' ? 'y' : 'x';\n  }\n  return axis;\n}\nfunction getDefaultScaleIDFromAxis(axis, indexAxis) {\n  return axis === indexAxis ? '_index_' : '_value_';\n}\nfunction idMatchesAxis(id) {\n  if (id === 'x' || id === 'y' || id === 'r') {\n    return id;\n  }\n}\nfunction axisFromPosition(position) {\n  if (position === 'top' || position === 'bottom') {\n    return 'x';\n  }\n  if (position === 'left' || position === 'right') {\n    return 'y';\n  }\n}\nfunction determineAxis(id, ...scaleOptions) {\n  if (idMatchesAxis(id)) {\n    return id;\n  }\n  for (const opts of scaleOptions) {\n    const axis = opts.axis || axisFromPosition(opts.position) || id.length > 1 && idMatchesAxis(id[0].toLowerCase());\n    if (axis) {\n      return axis;\n    }\n  }\n  throw new Error(`Cannot determine type of '${id}' axis. Please provide 'axis' or 'position' option.`);\n}\nfunction getAxisFromDataset(id, axis, dataset) {\n  if (dataset[axis + 'AxisID'] === id) {\n    return {\n      axis\n    };\n  }\n}\nfunction retrieveAxisFromDatasets(id, config) {\n  if (config.data && config.data.datasets) {\n    const boundDs = config.data.datasets.filter(d => d.xAxisID === id || d.yAxisID === id);\n    if (boundDs.length) {\n      return getAxisFromDataset(id, 'x', boundDs[0]) || getAxisFromDataset(id, 'y', boundDs[0]);\n    }\n  }\n  return {};\n}\nfunction mergeScaleConfig(config, options) {\n  const chartDefaults = overrides[config.type] || {\n    scales: {}\n  };\n  const configScales = options.scales || {};\n  const chartIndexAxis = getIndexAxis(config.type, options);\n  const scales = Object.create(null);\n  Object.keys(configScales).forEach(id => {\n    const scaleConf = configScales[id];\n    if (!isObject(scaleConf)) {\n      return console.error(`Invalid scale configuration for scale: ${id}`);\n    }\n    if (scaleConf._proxy) {\n      return console.warn(`Ignoring resolver passed as options for scale: ${id}`);\n    }\n    const axis = determineAxis(id, scaleConf, retrieveAxisFromDatasets(id, config), defaults.scales[scaleConf.type]);\n    const defaultId = getDefaultScaleIDFromAxis(axis, chartIndexAxis);\n    const defaultScaleOptions = chartDefaults.scales || {};\n    scales[id] = mergeIf(Object.create(null), [{\n      axis\n    }, scaleConf, defaultScaleOptions[axis], defaultScaleOptions[defaultId]]);\n  });\n  config.data.datasets.forEach(dataset => {\n    const type = dataset.type || config.type;\n    const indexAxis = dataset.indexAxis || getIndexAxis(type, options);\n    const datasetDefaults = overrides[type] || {};\n    const defaultScaleOptions = datasetDefaults.scales || {};\n    Object.keys(defaultScaleOptions).forEach(defaultID => {\n      const axis = getAxisFromDefaultScaleID(defaultID, indexAxis);\n      const id = dataset[axis + 'AxisID'] || axis;\n      scales[id] = scales[id] || Object.create(null);\n      mergeIf(scales[id], [{\n        axis\n      }, configScales[id], defaultScaleOptions[defaultID]]);\n    });\n  });\n  Object.keys(scales).forEach(key => {\n    const scale = scales[key];\n    mergeIf(scale, [defaults.scales[scale.type], defaults.scale]);\n  });\n  return scales;\n}\nfunction initOptions(config) {\n  const options = config.options || (config.options = {});\n  options.plugins = valueOrDefault(options.plugins, {});\n  options.scales = mergeScaleConfig(config, options);\n}\nfunction initData(data) {\n  data = data || {};\n  data.datasets = data.datasets || [];\n  data.labels = data.labels || [];\n  return data;\n}\nfunction initConfig(config) {\n  config = config || {};\n  config.data = initData(config.data);\n  initOptions(config);\n  return config;\n}\nconst keyCache = new Map();\nconst keysCached = new Set();\nfunction cachedKeys(cacheKey, generate) {\n  let keys = keyCache.get(cacheKey);\n  if (!keys) {\n    keys = generate();\n    keyCache.set(cacheKey, keys);\n    keysCached.add(keys);\n  }\n  return keys;\n}\nconst addIfFound = (set, obj, key) => {\n  const opts = resolveObjectKey(obj, key);\n  if (opts !== undefined) {\n    set.add(opts);\n  }\n};\nclass Config {\n  constructor(config) {\n    this._config = initConfig(config);\n    this._scopeCache = new Map();\n    this._resolverCache = new Map();\n  }\n  get platform() {\n    return this._config.platform;\n  }\n  get type() {\n    return this._config.type;\n  }\n  set type(type) {\n    this._config.type = type;\n  }\n  get data() {\n    return this._config.data;\n  }\n  set data(data) {\n    this._config.data = initData(data);\n  }\n  get options() {\n    return this._config.options;\n  }\n  set options(options) {\n    this._config.options = options;\n  }\n  get plugins() {\n    return this._config.plugins;\n  }\n  update() {\n    const config = this._config;\n    this.clearCache();\n    initOptions(config);\n  }\n  clearCache() {\n    this._scopeCache.clear();\n    this._resolverCache.clear();\n  }\n  datasetScopeKeys(datasetType) {\n    return cachedKeys(datasetType, () => [[`datasets.${datasetType}`, '']]);\n  }\n  datasetAnimationScopeKeys(datasetType, transition) {\n    return cachedKeys(`${datasetType}.transition.${transition}`, () => [[`datasets.${datasetType}.transitions.${transition}`, `transitions.${transition}`], [`datasets.${datasetType}`, '']]);\n  }\n  datasetElementScopeKeys(datasetType, elementType) {\n    return cachedKeys(`${datasetType}-${elementType}`, () => [[`datasets.${datasetType}.elements.${elementType}`, `datasets.${datasetType}`, `elements.${elementType}`, '']]);\n  }\n  pluginScopeKeys(plugin) {\n    const id = plugin.id;\n    const type = this.type;\n    return cachedKeys(`${type}-plugin-${id}`, () => [[`plugins.${id}`, ...(plugin.additionalOptionScopes || [])]]);\n  }\n  _cachedScopes(mainScope, resetCache) {\n    const _scopeCache = this._scopeCache;\n    let cache = _scopeCache.get(mainScope);\n    if (!cache || resetCache) {\n      cache = new Map();\n      _scopeCache.set(mainScope, cache);\n    }\n    return cache;\n  }\n  getOptionScopes(mainScope, keyLists, resetCache) {\n    const {\n      options,\n      type\n    } = this;\n    const cache = this._cachedScopes(mainScope, resetCache);\n    const cached = cache.get(keyLists);\n    if (cached) {\n      return cached;\n    }\n    const scopes = new Set();\n    keyLists.forEach(keys => {\n      if (mainScope) {\n        scopes.add(mainScope);\n        keys.forEach(key => addIfFound(scopes, mainScope, key));\n      }\n      keys.forEach(key => addIfFound(scopes, options, key));\n      keys.forEach(key => addIfFound(scopes, overrides[type] || {}, key));\n      keys.forEach(key => addIfFound(scopes, defaults, key));\n      keys.forEach(key => addIfFound(scopes, descriptors, key));\n    });\n    const array = Array.from(scopes);\n    if (array.length === 0) {\n      array.push(Object.create(null));\n    }\n    if (keysCached.has(keyLists)) {\n      cache.set(keyLists, array);\n    }\n    return array;\n  }\n  chartOptionScopes() {\n    const {\n      options,\n      type\n    } = this;\n    return [options, overrides[type] || {}, defaults.datasets[type] || {}, {\n      type\n    }, defaults, descriptors];\n  }\n  resolveNamedOptions(scopes, names, context, prefixes = ['']) {\n    const result = {\n      $shared: true\n    };\n    const {\n      resolver,\n      subPrefixes\n    } = getResolver(this._resolverCache, scopes, prefixes);\n    let options = resolver;\n    if (needContext(resolver, names)) {\n      result.$shared = false;\n      context = isFunction(context) ? context() : context;\n      const subResolver = this.createResolver(scopes, context, subPrefixes);\n      options = _attachContext(resolver, context, subResolver);\n    }\n    for (const prop of names) {\n      result[prop] = options[prop];\n    }\n    return result;\n  }\n  createResolver(scopes, context, prefixes = [''], descriptorDefaults) {\n    const {\n      resolver\n    } = getResolver(this._resolverCache, scopes, prefixes);\n    return isObject(context) ? _attachContext(resolver, context, undefined, descriptorDefaults) : resolver;\n  }\n}\nfunction getResolver(resolverCache, scopes, prefixes) {\n  let cache = resolverCache.get(scopes);\n  if (!cache) {\n    cache = new Map();\n    resolverCache.set(scopes, cache);\n  }\n  const cacheKey = prefixes.join();\n  let cached = cache.get(cacheKey);\n  if (!cached) {\n    const resolver = _createResolver(scopes, prefixes);\n    cached = {\n      resolver,\n      subPrefixes: prefixes.filter(p => !p.toLowerCase().includes('hover'))\n    };\n    cache.set(cacheKey, cached);\n  }\n  return cached;\n}\nconst hasFunction = value => isObject(value) && Object.getOwnPropertyNames(value).some(key => isFunction(value[key]));\nfunction needContext(proxy, names) {\n  const {\n    isScriptable,\n    isIndexable\n  } = _descriptors(proxy);\n  for (const prop of names) {\n    const scriptable = isScriptable(prop);\n    const indexable = isIndexable(prop);\n    const value = (indexable || scriptable) && proxy[prop];\n    if (scriptable && (isFunction(value) || hasFunction(value)) || indexable && isArray(value)) {\n      return true;\n    }\n  }\n  return false;\n}\nvar version = \"4.4.4\";\nconst KNOWN_POSITIONS = ['top', 'bottom', 'left', 'right', 'chartArea'];\nfunction positionIsHorizontal(position, axis) {\n  return position === 'top' || position === 'bottom' || KNOWN_POSITIONS.indexOf(position) === -1 && axis === 'x';\n}\nfunction compare2Level(l1, l2) {\n  return function (a, b) {\n    return a[l1] === b[l1] ? a[l2] - b[l2] : a[l1] - b[l1];\n  };\n}\nfunction onAnimationsComplete(context) {\n  const chart = context.chart;\n  const animationOptions = chart.options.animation;\n  chart.notifyPlugins('afterRender');\n  callback(animationOptions && animationOptions.onComplete, [context], chart);\n}\nfunction onAnimationProgress(context) {\n  const chart = context.chart;\n  const animationOptions = chart.options.animation;\n  callback(animationOptions && animationOptions.onProgress, [context], chart);\n}\nfunction getCanvas(item) {\n  if (_isDomSupported() && typeof item === 'string') {\n    item = document.getElementById(item);\n  } else if (item && item.length) {\n    item = item[0];\n  }\n  if (item && item.canvas) {\n    item = item.canvas;\n  }\n  return item;\n}\nconst instances = {};\nconst getChart = key => {\n  const canvas = getCanvas(key);\n  return Object.values(instances).filter(c => c.canvas === canvas).pop();\n};\nfunction moveNumericKeys(obj, start, move) {\n  const keys = Object.keys(obj);\n  for (const key of keys) {\n    const intKey = +key;\n    if (intKey >= start) {\n      const value = obj[key];\n      delete obj[key];\n      if (move > 0 || intKey > start) {\n        obj[intKey + move] = value;\n      }\n    }\n  }\n}\nfunction determineLastEvent(e, lastEvent, inChartArea, isClick) {\n  if (!inChartArea || e.type === 'mouseout') {\n    return null;\n  }\n  if (isClick) {\n    return lastEvent;\n  }\n  return e;\n}\nfunction getSizeForArea(scale, chartArea, field) {\n  return scale.options.clip ? scale[field] : chartArea[field];\n}\nfunction getDatasetArea(meta, chartArea) {\n  const {\n    xScale,\n    yScale\n  } = meta;\n  if (xScale && yScale) {\n    return {\n      left: getSizeForArea(xScale, chartArea, 'left'),\n      right: getSizeForArea(xScale, chartArea, 'right'),\n      top: getSizeForArea(yScale, chartArea, 'top'),\n      bottom: getSizeForArea(yScale, chartArea, 'bottom')\n    };\n  }\n  return chartArea;\n}\nlet Chart = /*#__PURE__*/(() => {\n  class Chart {\n    static defaults = defaults;\n    static instances = instances;\n    static overrides = overrides;\n    static registry = registry;\n    static version = version;\n    static getChart = getChart;\n    static register(...items) {\n      registry.add(...items);\n      invalidatePlugins();\n    }\n    static unregister(...items) {\n      registry.remove(...items);\n      invalidatePlugins();\n    }\n    constructor(item, userConfig) {\n      const config = this.config = new Config(userConfig);\n      const initialCanvas = getCanvas(item);\n      const existingChart = getChart(initialCanvas);\n      if (existingChart) {\n        throw new Error('Canvas is already in use. Chart with ID \\'' + existingChart.id + '\\'' + ' must be destroyed before the canvas with ID \\'' + existingChart.canvas.id + '\\' can be reused.');\n      }\n      const options = config.createResolver(config.chartOptionScopes(), this.getContext());\n      this.platform = new (config.platform || _detectPlatform(initialCanvas))();\n      this.platform.updateConfig(config);\n      const context = this.platform.acquireContext(initialCanvas, options.aspectRatio);\n      const canvas = context && context.canvas;\n      const height = canvas && canvas.height;\n      const width = canvas && canvas.width;\n      this.id = uid();\n      this.ctx = context;\n      this.canvas = canvas;\n      this.width = width;\n      this.height = height;\n      this._options = options;\n      this._aspectRatio = this.aspectRatio;\n      this._layers = [];\n      this._metasets = [];\n      this._stacks = undefined;\n      this.boxes = [];\n      this.currentDevicePixelRatio = undefined;\n      this.chartArea = undefined;\n      this._active = [];\n      this._lastEvent = undefined;\n      this._listeners = {};\n      this._responsiveListeners = undefined;\n      this._sortedMetasets = [];\n      this.scales = {};\n      this._plugins = new PluginService();\n      this.$proxies = {};\n      this._hiddenIndices = {};\n      this.attached = false;\n      this._animationsDisabled = undefined;\n      this.$context = undefined;\n      this._doResize = debounce(mode => this.update(mode), options.resizeDelay || 0);\n      this._dataChanges = [];\n      instances[this.id] = this;\n      if (!context || !canvas) {\n        console.error(\"Failed to create chart: can't acquire context from the given item\");\n        return;\n      }\n      animator.listen(this, 'complete', onAnimationsComplete);\n      animator.listen(this, 'progress', onAnimationProgress);\n      this._initialize();\n      if (this.attached) {\n        this.update();\n      }\n    }\n    get aspectRatio() {\n      const {\n        options: {\n          aspectRatio,\n          maintainAspectRatio\n        },\n        width,\n        height,\n        _aspectRatio\n      } = this;\n      if (!isNullOrUndef(aspectRatio)) {\n        return aspectRatio;\n      }\n      if (maintainAspectRatio && _aspectRatio) {\n        return _aspectRatio;\n      }\n      return height ? width / height : null;\n    }\n    get data() {\n      return this.config.data;\n    }\n    set data(data) {\n      this.config.data = data;\n    }\n    get options() {\n      return this._options;\n    }\n    set options(options) {\n      this.config.options = options;\n    }\n    get registry() {\n      return registry;\n    }\n    _initialize() {\n      this.notifyPlugins('beforeInit');\n      if (this.options.responsive) {\n        this.resize();\n      } else {\n        retinaScale(this, this.options.devicePixelRatio);\n      }\n      this.bindEvents();\n      this.notifyPlugins('afterInit');\n      return this;\n    }\n    clear() {\n      clearCanvas(this.canvas, this.ctx);\n      return this;\n    }\n    stop() {\n      animator.stop(this);\n      return this;\n    }\n    resize(width, height) {\n      if (!animator.running(this)) {\n        this._resize(width, height);\n      } else {\n        this._resizeBeforeDraw = {\n          width,\n          height\n        };\n      }\n    }\n    _resize(width, height) {\n      const options = this.options;\n      const canvas = this.canvas;\n      const aspectRatio = options.maintainAspectRatio && this.aspectRatio;\n      const newSize = this.platform.getMaximumSize(canvas, width, height, aspectRatio);\n      const newRatio = options.devicePixelRatio || this.platform.getDevicePixelRatio();\n      const mode = this.width ? 'resize' : 'attach';\n      this.width = newSize.width;\n      this.height = newSize.height;\n      this._aspectRatio = this.aspectRatio;\n      if (!retinaScale(this, newRatio, true)) {\n        return;\n      }\n      this.notifyPlugins('resize', {\n        size: newSize\n      });\n      callback(options.onResize, [this, newSize], this);\n      if (this.attached) {\n        if (this._doResize(mode)) {\n          this.render();\n        }\n      }\n    }\n    ensureScalesHaveIDs() {\n      const options = this.options;\n      const scalesOptions = options.scales || {};\n      each(scalesOptions, (axisOptions, axisID) => {\n        axisOptions.id = axisID;\n      });\n    }\n    buildOrUpdateScales() {\n      const options = this.options;\n      const scaleOpts = options.scales;\n      const scales = this.scales;\n      const updated = Object.keys(scales).reduce((obj, id) => {\n        obj[id] = false;\n        return obj;\n      }, {});\n      let items = [];\n      if (scaleOpts) {\n        items = items.concat(Object.keys(scaleOpts).map(id => {\n          const scaleOptions = scaleOpts[id];\n          const axis = determineAxis(id, scaleOptions);\n          const isRadial = axis === 'r';\n          const isHorizontal = axis === 'x';\n          return {\n            options: scaleOptions,\n            dposition: isRadial ? 'chartArea' : isHorizontal ? 'bottom' : 'left',\n            dtype: isRadial ? 'radialLinear' : isHorizontal ? 'category' : 'linear'\n          };\n        }));\n      }\n      each(items, item => {\n        const scaleOptions = item.options;\n        const id = scaleOptions.id;\n        const axis = determineAxis(id, scaleOptions);\n        const scaleType = valueOrDefault(scaleOptions.type, item.dtype);\n        if (scaleOptions.position === undefined || positionIsHorizontal(scaleOptions.position, axis) !== positionIsHorizontal(item.dposition)) {\n          scaleOptions.position = item.dposition;\n        }\n        updated[id] = true;\n        let scale = null;\n        if (id in scales && scales[id].type === scaleType) {\n          scale = scales[id];\n        } else {\n          const scaleClass = registry.getScale(scaleType);\n          scale = new scaleClass({\n            id,\n            type: scaleType,\n            ctx: this.ctx,\n            chart: this\n          });\n          scales[scale.id] = scale;\n        }\n        scale.init(scaleOptions, options);\n      });\n      each(updated, (hasUpdated, id) => {\n        if (!hasUpdated) {\n          delete scales[id];\n        }\n      });\n      each(scales, scale => {\n        layouts.configure(this, scale, scale.options);\n        layouts.addBox(this, scale);\n      });\n    }\n    _updateMetasets() {\n      const metasets = this._metasets;\n      const numData = this.data.datasets.length;\n      const numMeta = metasets.length;\n      metasets.sort((a, b) => a.index - b.index);\n      if (numMeta > numData) {\n        for (let i = numData; i < numMeta; ++i) {\n          this._destroyDatasetMeta(i);\n        }\n        metasets.splice(numData, numMeta - numData);\n      }\n      this._sortedMetasets = metasets.slice(0).sort(compare2Level('order', 'index'));\n    }\n    _removeUnreferencedMetasets() {\n      const {\n        _metasets: metasets,\n        data: {\n          datasets\n        }\n      } = this;\n      if (metasets.length > datasets.length) {\n        delete this._stacks;\n      }\n      metasets.forEach((meta, index) => {\n        if (datasets.filter(x => x === meta._dataset).length === 0) {\n          this._destroyDatasetMeta(index);\n        }\n      });\n    }\n    buildOrUpdateControllers() {\n      const newControllers = [];\n      const datasets = this.data.datasets;\n      let i, ilen;\n      this._removeUnreferencedMetasets();\n      for (i = 0, ilen = datasets.length; i < ilen; i++) {\n        const dataset = datasets[i];\n        let meta = this.getDatasetMeta(i);\n        const type = dataset.type || this.config.type;\n        if (meta.type && meta.type !== type) {\n          this._destroyDatasetMeta(i);\n          meta = this.getDatasetMeta(i);\n        }\n        meta.type = type;\n        meta.indexAxis = dataset.indexAxis || getIndexAxis(type, this.options);\n        meta.order = dataset.order || 0;\n        meta.index = i;\n        meta.label = '' + dataset.label;\n        meta.visible = this.isDatasetVisible(i);\n        if (meta.controller) {\n          meta.controller.updateIndex(i);\n          meta.controller.linkScales();\n        } else {\n          const ControllerClass = registry.getController(type);\n          const {\n            datasetElementType,\n            dataElementType\n          } = defaults.datasets[type];\n          Object.assign(ControllerClass, {\n            dataElementType: registry.getElement(dataElementType),\n            datasetElementType: datasetElementType && registry.getElement(datasetElementType)\n          });\n          meta.controller = new ControllerClass(this, i);\n          newControllers.push(meta.controller);\n        }\n      }\n      this._updateMetasets();\n      return newControllers;\n    }\n    _resetElements() {\n      each(this.data.datasets, (dataset, datasetIndex) => {\n        this.getDatasetMeta(datasetIndex).controller.reset();\n      }, this);\n    }\n    reset() {\n      this._resetElements();\n      this.notifyPlugins('reset');\n    }\n    update(mode) {\n      const config = this.config;\n      config.update();\n      const options = this._options = config.createResolver(config.chartOptionScopes(), this.getContext());\n      const animsDisabled = this._animationsDisabled = !options.animation;\n      this._updateScales();\n      this._checkEventBindings();\n      this._updateHiddenIndices();\n      this._plugins.invalidate();\n      if (this.notifyPlugins('beforeUpdate', {\n        mode,\n        cancelable: true\n      }) === false) {\n        return;\n      }\n      const newControllers = this.buildOrUpdateControllers();\n      this.notifyPlugins('beforeElementsUpdate');\n      let minPadding = 0;\n      for (let i = 0, ilen = this.data.datasets.length; i < ilen; i++) {\n        const {\n          controller\n        } = this.getDatasetMeta(i);\n        const reset = !animsDisabled && newControllers.indexOf(controller) === -1;\n        controller.buildOrUpdateElements(reset);\n        minPadding = Math.max(+controller.getMaxOverflow(), minPadding);\n      }\n      minPadding = this._minPadding = options.layout.autoPadding ? minPadding : 0;\n      this._updateLayout(minPadding);\n      if (!animsDisabled) {\n        each(newControllers, controller => {\n          controller.reset();\n        });\n      }\n      this._updateDatasets(mode);\n      this.notifyPlugins('afterUpdate', {\n        mode\n      });\n      this._layers.sort(compare2Level('z', '_idx'));\n      const {\n        _active,\n        _lastEvent\n      } = this;\n      if (_lastEvent) {\n        this._eventHandler(_lastEvent, true);\n      } else if (_active.length) {\n        this._updateHoverStyles(_active, _active, true);\n      }\n      this.render();\n    }\n    _updateScales() {\n      each(this.scales, scale => {\n        layouts.removeBox(this, scale);\n      });\n      this.ensureScalesHaveIDs();\n      this.buildOrUpdateScales();\n    }\n    _checkEventBindings() {\n      const options = this.options;\n      const existingEvents = new Set(Object.keys(this._listeners));\n      const newEvents = new Set(options.events);\n      if (!setsEqual(existingEvents, newEvents) || !!this._responsiveListeners !== options.responsive) {\n        this.unbindEvents();\n        this.bindEvents();\n      }\n    }\n    _updateHiddenIndices() {\n      const {\n        _hiddenIndices\n      } = this;\n      const changes = this._getUniformDataChanges() || [];\n      for (const {\n        method,\n        start,\n        count\n      } of changes) {\n        const move = method === '_removeElements' ? -count : count;\n        moveNumericKeys(_hiddenIndices, start, move);\n      }\n    }\n    _getUniformDataChanges() {\n      const _dataChanges = this._dataChanges;\n      if (!_dataChanges || !_dataChanges.length) {\n        return;\n      }\n      this._dataChanges = [];\n      const datasetCount = this.data.datasets.length;\n      const makeSet = idx => new Set(_dataChanges.filter(c => c[0] === idx).map((c, i) => i + ',' + c.splice(1).join(',')));\n      const changeSet = makeSet(0);\n      for (let i = 1; i < datasetCount; i++) {\n        if (!setsEqual(changeSet, makeSet(i))) {\n          return;\n        }\n      }\n      return Array.from(changeSet).map(c => c.split(',')).map(a => ({\n        method: a[1],\n        start: +a[2],\n        count: +a[3]\n      }));\n    }\n    _updateLayout(minPadding) {\n      if (this.notifyPlugins('beforeLayout', {\n        cancelable: true\n      }) === false) {\n        return;\n      }\n      layouts.update(this, this.width, this.height, minPadding);\n      const area = this.chartArea;\n      const noArea = area.width <= 0 || area.height <= 0;\n      this._layers = [];\n      each(this.boxes, box => {\n        if (noArea && box.position === 'chartArea') {\n          return;\n        }\n        if (box.configure) {\n          box.configure();\n        }\n        this._layers.push(...box._layers());\n      }, this);\n      this._layers.forEach((item, index) => {\n        item._idx = index;\n      });\n      this.notifyPlugins('afterLayout');\n    }\n    _updateDatasets(mode) {\n      if (this.notifyPlugins('beforeDatasetsUpdate', {\n        mode,\n        cancelable: true\n      }) === false) {\n        return;\n      }\n      for (let i = 0, ilen = this.data.datasets.length; i < ilen; ++i) {\n        this.getDatasetMeta(i).controller.configure();\n      }\n      for (let i = 0, ilen = this.data.datasets.length; i < ilen; ++i) {\n        this._updateDataset(i, isFunction(mode) ? mode({\n          datasetIndex: i\n        }) : mode);\n      }\n      this.notifyPlugins('afterDatasetsUpdate', {\n        mode\n      });\n    }\n    _updateDataset(index, mode) {\n      const meta = this.getDatasetMeta(index);\n      const args = {\n        meta,\n        index,\n        mode,\n        cancelable: true\n      };\n      if (this.notifyPlugins('beforeDatasetUpdate', args) === false) {\n        return;\n      }\n      meta.controller._update(mode);\n      args.cancelable = false;\n      this.notifyPlugins('afterDatasetUpdate', args);\n    }\n    render() {\n      if (this.notifyPlugins('beforeRender', {\n        cancelable: true\n      }) === false) {\n        return;\n      }\n      if (animator.has(this)) {\n        if (this.attached && !animator.running(this)) {\n          animator.start(this);\n        }\n      } else {\n        this.draw();\n        onAnimationsComplete({\n          chart: this\n        });\n      }\n    }\n    draw() {\n      let i;\n      if (this._resizeBeforeDraw) {\n        const {\n          width,\n          height\n        } = this._resizeBeforeDraw;\n        this._resizeBeforeDraw = null;\n        this._resize(width, height);\n      }\n      this.clear();\n      if (this.width <= 0 || this.height <= 0) {\n        return;\n      }\n      if (this.notifyPlugins('beforeDraw', {\n        cancelable: true\n      }) === false) {\n        return;\n      }\n      const layers = this._layers;\n      for (i = 0; i < layers.length && layers[i].z <= 0; ++i) {\n        layers[i].draw(this.chartArea);\n      }\n      this._drawDatasets();\n      for (; i < layers.length; ++i) {\n        layers[i].draw(this.chartArea);\n      }\n      this.notifyPlugins('afterDraw');\n    }\n    _getSortedDatasetMetas(filterVisible) {\n      const metasets = this._sortedMetasets;\n      const result = [];\n      let i, ilen;\n      for (i = 0, ilen = metasets.length; i < ilen; ++i) {\n        const meta = metasets[i];\n        if (!filterVisible || meta.visible) {\n          result.push(meta);\n        }\n      }\n      return result;\n    }\n    getSortedVisibleDatasetMetas() {\n      return this._getSortedDatasetMetas(true);\n    }\n    _drawDatasets() {\n      if (this.notifyPlugins('beforeDatasetsDraw', {\n        cancelable: true\n      }) === false) {\n        return;\n      }\n      const metasets = this.getSortedVisibleDatasetMetas();\n      for (let i = metasets.length - 1; i >= 0; --i) {\n        this._drawDataset(metasets[i]);\n      }\n      this.notifyPlugins('afterDatasetsDraw');\n    }\n    _drawDataset(meta) {\n      const ctx = this.ctx;\n      const clip = meta._clip;\n      const useClip = !clip.disabled;\n      const area = getDatasetArea(meta, this.chartArea);\n      const args = {\n        meta,\n        index: meta.index,\n        cancelable: true\n      };\n      if (this.notifyPlugins('beforeDatasetDraw', args) === false) {\n        return;\n      }\n      if (useClip) {\n        clipArea(ctx, {\n          left: clip.left === false ? 0 : area.left - clip.left,\n          right: clip.right === false ? this.width : area.right + clip.right,\n          top: clip.top === false ? 0 : area.top - clip.top,\n          bottom: clip.bottom === false ? this.height : area.bottom + clip.bottom\n        });\n      }\n      meta.controller.draw();\n      if (useClip) {\n        unclipArea(ctx);\n      }\n      args.cancelable = false;\n      this.notifyPlugins('afterDatasetDraw', args);\n    }\n    isPointInArea(point) {\n      return _isPointInArea(point, this.chartArea, this._minPadding);\n    }\n    getElementsAtEventForMode(e, mode, options, useFinalPosition) {\n      const method = Interaction.modes[mode];\n      if (typeof method === 'function') {\n        return method(this, e, options, useFinalPosition);\n      }\n      return [];\n    }\n    getDatasetMeta(datasetIndex) {\n      const dataset = this.data.datasets[datasetIndex];\n      const metasets = this._metasets;\n      let meta = metasets.filter(x => x && x._dataset === dataset).pop();\n      if (!meta) {\n        meta = {\n          type: null,\n          data: [],\n          dataset: null,\n          controller: null,\n          hidden: null,\n          xAxisID: null,\n          yAxisID: null,\n          order: dataset && dataset.order || 0,\n          index: datasetIndex,\n          _dataset: dataset,\n          _parsed: [],\n          _sorted: false\n        };\n        metasets.push(meta);\n      }\n      return meta;\n    }\n    getContext() {\n      return this.$context || (this.$context = createContext(null, {\n        chart: this,\n        type: 'chart'\n      }));\n    }\n    getVisibleDatasetCount() {\n      return this.getSortedVisibleDatasetMetas().length;\n    }\n    isDatasetVisible(datasetIndex) {\n      const dataset = this.data.datasets[datasetIndex];\n      if (!dataset) {\n        return false;\n      }\n      const meta = this.getDatasetMeta(datasetIndex);\n      return typeof meta.hidden === 'boolean' ? !meta.hidden : !dataset.hidden;\n    }\n    setDatasetVisibility(datasetIndex, visible) {\n      const meta = this.getDatasetMeta(datasetIndex);\n      meta.hidden = !visible;\n    }\n    toggleDataVisibility(index) {\n      this._hiddenIndices[index] = !this._hiddenIndices[index];\n    }\n    getDataVisibility(index) {\n      return !this._hiddenIndices[index];\n    }\n    _updateVisibility(datasetIndex, dataIndex, visible) {\n      const mode = visible ? 'show' : 'hide';\n      const meta = this.getDatasetMeta(datasetIndex);\n      const anims = meta.controller._resolveAnimations(undefined, mode);\n      if (defined(dataIndex)) {\n        meta.data[dataIndex].hidden = !visible;\n        this.update();\n      } else {\n        this.setDatasetVisibility(datasetIndex, visible);\n        anims.update(meta, {\n          visible\n        });\n        this.update(ctx => ctx.datasetIndex === datasetIndex ? mode : undefined);\n      }\n    }\n    hide(datasetIndex, dataIndex) {\n      this._updateVisibility(datasetIndex, dataIndex, false);\n    }\n    show(datasetIndex, dataIndex) {\n      this._updateVisibility(datasetIndex, dataIndex, true);\n    }\n    _destroyDatasetMeta(datasetIndex) {\n      const meta = this._metasets[datasetIndex];\n      if (meta && meta.controller) {\n        meta.controller._destroy();\n      }\n      delete this._metasets[datasetIndex];\n    }\n    _stop() {\n      let i, ilen;\n      this.stop();\n      animator.remove(this);\n      for (i = 0, ilen = this.data.datasets.length; i < ilen; ++i) {\n        this._destroyDatasetMeta(i);\n      }\n    }\n    destroy() {\n      this.notifyPlugins('beforeDestroy');\n      const {\n        canvas,\n        ctx\n      } = this;\n      this._stop();\n      this.config.clearCache();\n      if (canvas) {\n        this.unbindEvents();\n        clearCanvas(canvas, ctx);\n        this.platform.releaseContext(ctx);\n        this.canvas = null;\n        this.ctx = null;\n      }\n      delete instances[this.id];\n      this.notifyPlugins('afterDestroy');\n    }\n    toBase64Image(...args) {\n      return this.canvas.toDataURL(...args);\n    }\n    bindEvents() {\n      this.bindUserEvents();\n      if (this.options.responsive) {\n        this.bindResponsiveEvents();\n      } else {\n        this.attached = true;\n      }\n    }\n    bindUserEvents() {\n      const listeners = this._listeners;\n      const platform = this.platform;\n      const _add = (type, listener) => {\n        platform.addEventListener(this, type, listener);\n        listeners[type] = listener;\n      };\n      const listener = (e, x, y) => {\n        e.offsetX = x;\n        e.offsetY = y;\n        this._eventHandler(e);\n      };\n      each(this.options.events, type => _add(type, listener));\n    }\n    bindResponsiveEvents() {\n      if (!this._responsiveListeners) {\n        this._responsiveListeners = {};\n      }\n      const listeners = this._responsiveListeners;\n      const platform = this.platform;\n      const _add = (type, listener) => {\n        platform.addEventListener(this, type, listener);\n        listeners[type] = listener;\n      };\n      const _remove = (type, listener) => {\n        if (listeners[type]) {\n          platform.removeEventListener(this, type, listener);\n          delete listeners[type];\n        }\n      };\n      const listener = (width, height) => {\n        if (this.canvas) {\n          this.resize(width, height);\n        }\n      };\n      let detached;\n      const attached = () => {\n        _remove('attach', attached);\n        this.attached = true;\n        this.resize();\n        _add('resize', listener);\n        _add('detach', detached);\n      };\n      detached = () => {\n        this.attached = false;\n        _remove('resize', listener);\n        this._stop();\n        this._resize(0, 0);\n        _add('attach', attached);\n      };\n      if (platform.isAttached(this.canvas)) {\n        attached();\n      } else {\n        detached();\n      }\n    }\n    unbindEvents() {\n      each(this._listeners, (listener, type) => {\n        this.platform.removeEventListener(this, type, listener);\n      });\n      this._listeners = {};\n      each(this._responsiveListeners, (listener, type) => {\n        this.platform.removeEventListener(this, type, listener);\n      });\n      this._responsiveListeners = undefined;\n    }\n    updateHoverStyle(items, mode, enabled) {\n      const prefix = enabled ? 'set' : 'remove';\n      let meta, item, i, ilen;\n      if (mode === 'dataset') {\n        meta = this.getDatasetMeta(items[0].datasetIndex);\n        meta.controller['_' + prefix + 'DatasetHoverStyle']();\n      }\n      for (i = 0, ilen = items.length; i < ilen; ++i) {\n        item = items[i];\n        const controller = item && this.getDatasetMeta(item.datasetIndex).controller;\n        if (controller) {\n          controller[prefix + 'HoverStyle'](item.element, item.datasetIndex, item.index);\n        }\n      }\n    }\n    getActiveElements() {\n      return this._active || [];\n    }\n    setActiveElements(activeElements) {\n      const lastActive = this._active || [];\n      const active = activeElements.map(({\n        datasetIndex,\n        index\n      }) => {\n        const meta = this.getDatasetMeta(datasetIndex);\n        if (!meta) {\n          throw new Error('No dataset found at index ' + datasetIndex);\n        }\n        return {\n          datasetIndex,\n          element: meta.data[index],\n          index\n        };\n      });\n      const changed = !_elementsEqual(active, lastActive);\n      if (changed) {\n        this._active = active;\n        this._lastEvent = null;\n        this._updateHoverStyles(active, lastActive);\n      }\n    }\n    notifyPlugins(hook, args, filter) {\n      return this._plugins.notify(this, hook, args, filter);\n    }\n    isPluginEnabled(pluginId) {\n      return this._plugins._cache.filter(p => p.plugin.id === pluginId).length === 1;\n    }\n    _updateHoverStyles(active, lastActive, replay) {\n      const hoverOptions = this.options.hover;\n      const diff = (a, b) => a.filter(x => !b.some(y => x.datasetIndex === y.datasetIndex && x.index === y.index));\n      const deactivated = diff(lastActive, active);\n      const activated = replay ? active : diff(active, lastActive);\n      if (deactivated.length) {\n        this.updateHoverStyle(deactivated, hoverOptions.mode, false);\n      }\n      if (activated.length && hoverOptions.mode) {\n        this.updateHoverStyle(activated, hoverOptions.mode, true);\n      }\n    }\n    _eventHandler(e, replay) {\n      const args = {\n        event: e,\n        replay,\n        cancelable: true,\n        inChartArea: this.isPointInArea(e)\n      };\n      const eventFilter = plugin => (plugin.options.events || this.options.events).includes(e.native.type);\n      if (this.notifyPlugins('beforeEvent', args, eventFilter) === false) {\n        return;\n      }\n      const changed = this._handleEvent(e, replay, args.inChartArea);\n      args.cancelable = false;\n      this.notifyPlugins('afterEvent', args, eventFilter);\n      if (changed || args.changed) {\n        this.render();\n      }\n      return this;\n    }\n    _handleEvent(e, replay, inChartArea) {\n      const {\n        _active: lastActive = [],\n        options\n      } = this;\n      const useFinalPosition = replay;\n      const active = this._getActiveElements(e, lastActive, inChartArea, useFinalPosition);\n      const isClick = _isClickEvent(e);\n      const lastEvent = determineLastEvent(e, this._lastEvent, inChartArea, isClick);\n      if (inChartArea) {\n        this._lastEvent = null;\n        callback(options.onHover, [e, active, this], this);\n        if (isClick) {\n          callback(options.onClick, [e, active, this], this);\n        }\n      }\n      const changed = !_elementsEqual(active, lastActive);\n      if (changed || replay) {\n        this._active = active;\n        this._updateHoverStyles(active, lastActive, replay);\n      }\n      this._lastEvent = lastEvent;\n      return changed;\n    }\n    _getActiveElements(e, lastActive, inChartArea, useFinalPosition) {\n      if (e.type === 'mouseout') {\n        return [];\n      }\n      if (!inChartArea) {\n        return lastActive;\n      }\n      const hoverOptions = this.options.hover;\n      return this.getElementsAtEventForMode(e, hoverOptions.mode, hoverOptions, useFinalPosition);\n    }\n  }\n  return Chart;\n})();\nfunction invalidatePlugins() {\n  return each(Chart.instances, chart => chart._plugins.invalidate());\n}\nfunction clipArc(ctx, element, endAngle) {\n  const {\n    startAngle,\n    pixelMargin,\n    x,\n    y,\n    outerRadius,\n    innerRadius\n  } = element;\n  let angleMargin = pixelMargin / outerRadius;\n  // Draw an inner border by clipping the arc and drawing a double-width border\n  // Enlarge the clipping arc by 0.33 pixels to eliminate glitches between borders\n  ctx.beginPath();\n  ctx.arc(x, y, outerRadius, startAngle - angleMargin, endAngle + angleMargin);\n  if (innerRadius > pixelMargin) {\n    angleMargin = pixelMargin / innerRadius;\n    ctx.arc(x, y, innerRadius, endAngle + angleMargin, startAngle - angleMargin, true);\n  } else {\n    ctx.arc(x, y, pixelMargin, endAngle + HALF_PI, startAngle - HALF_PI);\n  }\n  ctx.closePath();\n  ctx.clip();\n}\nfunction toRadiusCorners(value) {\n  return _readValueToProps(value, ['outerStart', 'outerEnd', 'innerStart', 'innerEnd']);\n}\n/**\n * Parse border radius from the provided options\n */\nfunction parseBorderRadius$1(arc, innerRadius, outerRadius, angleDelta) {\n  const o = toRadiusCorners(arc.options.borderRadius);\n  const halfThickness = (outerRadius - innerRadius) / 2;\n  const innerLimit = Math.min(halfThickness, angleDelta * innerRadius / 2);\n  // Outer limits are complicated. We want to compute the available angular distance at\n  // a radius of outerRadius - borderRadius because for small angular distances, this term limits.\n  // We compute at r = outerRadius - borderRadius because this circle defines the center of the border corners.\n  //\n  // If the borderRadius is large, that value can become negative.\n  // This causes the outer borders to lose their radius entirely, which is rather unexpected. To solve that, if borderRadius > outerRadius\n  // we know that the thickness term will dominate and compute the limits at that point\n  const computeOuterLimit = val => {\n    const outerArcLimit = (outerRadius - Math.min(halfThickness, val)) * angleDelta / 2;\n    return _limitValue(val, 0, Math.min(halfThickness, outerArcLimit));\n  };\n  return {\n    outerStart: computeOuterLimit(o.outerStart),\n    outerEnd: computeOuterLimit(o.outerEnd),\n    innerStart: _limitValue(o.innerStart, 0, innerLimit),\n    innerEnd: _limitValue(o.innerEnd, 0, innerLimit)\n  };\n}\n/**\n * Convert (r, 𝜃) to (x, y)\n */\nfunction rThetaToXY(r, theta, x, y) {\n  return {\n    x: x + r * Math.cos(theta),\n    y: y + r * Math.sin(theta)\n  };\n}\n/**\n * Path the arc, respecting border radius by separating into left and right halves.\n *\n *   Start      End\n *\n *    1--->a--->2    Outer\n *   /           \\\n *   8           3\n *   |           |\n *   |           |\n *   7           4\n *   \\           /\n *    6<---b<---5    Inner\n */\nfunction pathArc(ctx, element, offset, spacing, end, circular) {\n  const {\n    x,\n    y,\n    startAngle: start,\n    pixelMargin,\n    innerRadius: innerR\n  } = element;\n  const outerRadius = Math.max(element.outerRadius + spacing + offset - pixelMargin, 0);\n  const innerRadius = innerR > 0 ? innerR + spacing + offset + pixelMargin : 0;\n  let spacingOffset = 0;\n  const alpha = end - start;\n  if (spacing) {\n    // When spacing is present, it is the same for all items\n    // So we adjust the start and end angle of the arc such that\n    // the distance is the same as it would be without the spacing\n    const noSpacingInnerRadius = innerR > 0 ? innerR - spacing : 0;\n    const noSpacingOuterRadius = outerRadius > 0 ? outerRadius - spacing : 0;\n    const avNogSpacingRadius = (noSpacingInnerRadius + noSpacingOuterRadius) / 2;\n    const adjustedAngle = avNogSpacingRadius !== 0 ? alpha * avNogSpacingRadius / (avNogSpacingRadius + spacing) : alpha;\n    spacingOffset = (alpha - adjustedAngle) / 2;\n  }\n  const beta = Math.max(0.001, alpha * outerRadius - offset / PI) / outerRadius;\n  const angleOffset = (alpha - beta) / 2;\n  const startAngle = start + angleOffset + spacingOffset;\n  const endAngle = end - angleOffset - spacingOffset;\n  const {\n    outerStart,\n    outerEnd,\n    innerStart,\n    innerEnd\n  } = parseBorderRadius$1(element, innerRadius, outerRadius, endAngle - startAngle);\n  const outerStartAdjustedRadius = outerRadius - outerStart;\n  const outerEndAdjustedRadius = outerRadius - outerEnd;\n  const outerStartAdjustedAngle = startAngle + outerStart / outerStartAdjustedRadius;\n  const outerEndAdjustedAngle = endAngle - outerEnd / outerEndAdjustedRadius;\n  const innerStartAdjustedRadius = innerRadius + innerStart;\n  const innerEndAdjustedRadius = innerRadius + innerEnd;\n  const innerStartAdjustedAngle = startAngle + innerStart / innerStartAdjustedRadius;\n  const innerEndAdjustedAngle = endAngle - innerEnd / innerEndAdjustedRadius;\n  ctx.beginPath();\n  if (circular) {\n    // The first arc segments from point 1 to point a to point 2\n    const outerMidAdjustedAngle = (outerStartAdjustedAngle + outerEndAdjustedAngle) / 2;\n    ctx.arc(x, y, outerRadius, outerStartAdjustedAngle, outerMidAdjustedAngle);\n    ctx.arc(x, y, outerRadius, outerMidAdjustedAngle, outerEndAdjustedAngle);\n    // The corner segment from point 2 to point 3\n    if (outerEnd > 0) {\n      const pCenter = rThetaToXY(outerEndAdjustedRadius, outerEndAdjustedAngle, x, y);\n      ctx.arc(pCenter.x, pCenter.y, outerEnd, outerEndAdjustedAngle, endAngle + HALF_PI);\n    }\n    // The line from point 3 to point 4\n    const p4 = rThetaToXY(innerEndAdjustedRadius, endAngle, x, y);\n    ctx.lineTo(p4.x, p4.y);\n    // The corner segment from point 4 to point 5\n    if (innerEnd > 0) {\n      const pCenter = rThetaToXY(innerEndAdjustedRadius, innerEndAdjustedAngle, x, y);\n      ctx.arc(pCenter.x, pCenter.y, innerEnd, endAngle + HALF_PI, innerEndAdjustedAngle + Math.PI);\n    }\n    // The inner arc from point 5 to point b to point 6\n    const innerMidAdjustedAngle = (endAngle - innerEnd / innerRadius + (startAngle + innerStart / innerRadius)) / 2;\n    ctx.arc(x, y, innerRadius, endAngle - innerEnd / innerRadius, innerMidAdjustedAngle, true);\n    ctx.arc(x, y, innerRadius, innerMidAdjustedAngle, startAngle + innerStart / innerRadius, true);\n    // The corner segment from point 6 to point 7\n    if (innerStart > 0) {\n      const pCenter = rThetaToXY(innerStartAdjustedRadius, innerStartAdjustedAngle, x, y);\n      ctx.arc(pCenter.x, pCenter.y, innerStart, innerStartAdjustedAngle + Math.PI, startAngle - HALF_PI);\n    }\n    // The line from point 7 to point 8\n    const p8 = rThetaToXY(outerStartAdjustedRadius, startAngle, x, y);\n    ctx.lineTo(p8.x, p8.y);\n    // The corner segment from point 8 to point 1\n    if (outerStart > 0) {\n      const pCenter = rThetaToXY(outerStartAdjustedRadius, outerStartAdjustedAngle, x, y);\n      ctx.arc(pCenter.x, pCenter.y, outerStart, startAngle - HALF_PI, outerStartAdjustedAngle);\n    }\n  } else {\n    ctx.moveTo(x, y);\n    const outerStartX = Math.cos(outerStartAdjustedAngle) * outerRadius + x;\n    const outerStartY = Math.sin(outerStartAdjustedAngle) * outerRadius + y;\n    ctx.lineTo(outerStartX, outerStartY);\n    const outerEndX = Math.cos(outerEndAdjustedAngle) * outerRadius + x;\n    const outerEndY = Math.sin(outerEndAdjustedAngle) * outerRadius + y;\n    ctx.lineTo(outerEndX, outerEndY);\n  }\n  ctx.closePath();\n}\nfunction drawArc(ctx, element, offset, spacing, circular) {\n  const {\n    fullCircles,\n    startAngle,\n    circumference\n  } = element;\n  let endAngle = element.endAngle;\n  if (fullCircles) {\n    pathArc(ctx, element, offset, spacing, endAngle, circular);\n    for (let i = 0; i < fullCircles; ++i) {\n      ctx.fill();\n    }\n    if (!isNaN(circumference)) {\n      endAngle = startAngle + (circumference % TAU || TAU);\n    }\n  }\n  pathArc(ctx, element, offset, spacing, endAngle, circular);\n  ctx.fill();\n  return endAngle;\n}\nfunction drawBorder(ctx, element, offset, spacing, circular) {\n  const {\n    fullCircles,\n    startAngle,\n    circumference,\n    options\n  } = element;\n  const {\n    borderWidth,\n    borderJoinStyle,\n    borderDash,\n    borderDashOffset\n  } = options;\n  const inner = options.borderAlign === 'inner';\n  if (!borderWidth) {\n    return;\n  }\n  ctx.setLineDash(borderDash || []);\n  ctx.lineDashOffset = borderDashOffset;\n  if (inner) {\n    ctx.lineWidth = borderWidth * 2;\n    ctx.lineJoin = borderJoinStyle || 'round';\n  } else {\n    ctx.lineWidth = borderWidth;\n    ctx.lineJoin = borderJoinStyle || 'bevel';\n  }\n  let endAngle = element.endAngle;\n  if (fullCircles) {\n    pathArc(ctx, element, offset, spacing, endAngle, circular);\n    for (let i = 0; i < fullCircles; ++i) {\n      ctx.stroke();\n    }\n    if (!isNaN(circumference)) {\n      endAngle = startAngle + (circumference % TAU || TAU);\n    }\n  }\n  if (inner) {\n    clipArc(ctx, element, endAngle);\n  }\n  if (!fullCircles) {\n    pathArc(ctx, element, offset, spacing, endAngle, circular);\n    ctx.stroke();\n  }\n}\nclass ArcElement extends Element {\n  static id = 'arc';\n  static defaults = {\n    borderAlign: 'center',\n    borderColor: '#fff',\n    borderDash: [],\n    borderDashOffset: 0,\n    borderJoinStyle: undefined,\n    borderRadius: 0,\n    borderWidth: 2,\n    offset: 0,\n    spacing: 0,\n    angle: undefined,\n    circular: true\n  };\n  static defaultRoutes = {\n    backgroundColor: 'backgroundColor'\n  };\n  static descriptors = {\n    _scriptable: true,\n    _indexable: name => name !== 'borderDash'\n  };\n  circumference;\n  endAngle;\n  fullCircles;\n  innerRadius;\n  outerRadius;\n  pixelMargin;\n  startAngle;\n  constructor(cfg) {\n    super();\n    this.options = undefined;\n    this.circumference = undefined;\n    this.startAngle = undefined;\n    this.endAngle = undefined;\n    this.innerRadius = undefined;\n    this.outerRadius = undefined;\n    this.pixelMargin = 0;\n    this.fullCircles = 0;\n    if (cfg) {\n      Object.assign(this, cfg);\n    }\n  }\n  inRange(chartX, chartY, useFinalPosition) {\n    const point = this.getProps(['x', 'y'], useFinalPosition);\n    const {\n      angle,\n      distance\n    } = getAngleFromPoint(point, {\n      x: chartX,\n      y: chartY\n    });\n    const {\n      startAngle,\n      endAngle,\n      innerRadius,\n      outerRadius,\n      circumference\n    } = this.getProps(['startAngle', 'endAngle', 'innerRadius', 'outerRadius', 'circumference'], useFinalPosition);\n    const rAdjust = (this.options.spacing + this.options.borderWidth) / 2;\n    const _circumference = valueOrDefault(circumference, endAngle - startAngle);\n    const nonZeroBetween = _angleBetween(angle, startAngle, endAngle) && startAngle !== endAngle;\n    const betweenAngles = _circumference >= TAU || nonZeroBetween;\n    const withinRadius = _isBetween(distance, innerRadius + rAdjust, outerRadius + rAdjust);\n    return betweenAngles && withinRadius;\n  }\n  getCenterPoint(useFinalPosition) {\n    const {\n      x,\n      y,\n      startAngle,\n      endAngle,\n      innerRadius,\n      outerRadius\n    } = this.getProps(['x', 'y', 'startAngle', 'endAngle', 'innerRadius', 'outerRadius'], useFinalPosition);\n    const {\n      offset,\n      spacing\n    } = this.options;\n    const halfAngle = (startAngle + endAngle) / 2;\n    const halfRadius = (innerRadius + outerRadius + spacing + offset) / 2;\n    return {\n      x: x + Math.cos(halfAngle) * halfRadius,\n      y: y + Math.sin(halfAngle) * halfRadius\n    };\n  }\n  tooltipPosition(useFinalPosition) {\n    return this.getCenterPoint(useFinalPosition);\n  }\n  draw(ctx) {\n    const {\n      options,\n      circumference\n    } = this;\n    const offset = (options.offset || 0) / 4;\n    const spacing = (options.spacing || 0) / 2;\n    const circular = options.circular;\n    this.pixelMargin = options.borderAlign === 'inner' ? 0.33 : 0;\n    this.fullCircles = circumference > TAU ? Math.floor(circumference / TAU) : 0;\n    if (circumference === 0 || this.innerRadius < 0 || this.outerRadius < 0) {\n      return;\n    }\n    ctx.save();\n    const halfAngle = (this.startAngle + this.endAngle) / 2;\n    ctx.translate(Math.cos(halfAngle) * offset, Math.sin(halfAngle) * offset);\n    const fix = 1 - Math.sin(Math.min(PI, circumference || 0));\n    const radiusOffset = offset * fix;\n    ctx.fillStyle = options.backgroundColor;\n    ctx.strokeStyle = options.borderColor;\n    drawArc(ctx, this, radiusOffset, spacing, circular);\n    drawBorder(ctx, this, radiusOffset, spacing, circular);\n    ctx.restore();\n  }\n}\nfunction setStyle(ctx, options, style = options) {\n  ctx.lineCap = valueOrDefault(style.borderCapStyle, options.borderCapStyle);\n  ctx.setLineDash(valueOrDefault(style.borderDash, options.borderDash));\n  ctx.lineDashOffset = valueOrDefault(style.borderDashOffset, options.borderDashOffset);\n  ctx.lineJoin = valueOrDefault(style.borderJoinStyle, options.borderJoinStyle);\n  ctx.lineWidth = valueOrDefault(style.borderWidth, options.borderWidth);\n  ctx.strokeStyle = valueOrDefault(style.borderColor, options.borderColor);\n}\nfunction lineTo(ctx, previous, target) {\n  ctx.lineTo(target.x, target.y);\n}\nfunction getLineMethod(options) {\n  if (options.stepped) {\n    return _steppedLineTo;\n  }\n  if (options.tension || options.cubicInterpolationMode === 'monotone') {\n    return _bezierCurveTo;\n  }\n  return lineTo;\n}\nfunction pathVars(points, segment, params = {}) {\n  const count = points.length;\n  const {\n    start: paramsStart = 0,\n    end: paramsEnd = count - 1\n  } = params;\n  const {\n    start: segmentStart,\n    end: segmentEnd\n  } = segment;\n  const start = Math.max(paramsStart, segmentStart);\n  const end = Math.min(paramsEnd, segmentEnd);\n  const outside = paramsStart < segmentStart && paramsEnd < segmentStart || paramsStart > segmentEnd && paramsEnd > segmentEnd;\n  return {\n    count,\n    start,\n    loop: segment.loop,\n    ilen: end < start && !outside ? count + end - start : end - start\n  };\n}\nfunction pathSegment(ctx, line, segment, params) {\n  const {\n    points,\n    options\n  } = line;\n  const {\n    count,\n    start,\n    loop,\n    ilen\n  } = pathVars(points, segment, params);\n  const lineMethod = getLineMethod(options);\n  let {\n    move = true,\n    reverse\n  } = params || {};\n  let i, point, prev;\n  for (i = 0; i <= ilen; ++i) {\n    point = points[(start + (reverse ? ilen - i : i)) % count];\n    if (point.skip) {\n      continue;\n    } else if (move) {\n      ctx.moveTo(point.x, point.y);\n      move = false;\n    } else {\n      lineMethod(ctx, prev, point, reverse, options.stepped);\n    }\n    prev = point;\n  }\n  if (loop) {\n    point = points[(start + (reverse ? ilen : 0)) % count];\n    lineMethod(ctx, prev, point, reverse, options.stepped);\n  }\n  return !!loop;\n}\nfunction fastPathSegment(ctx, line, segment, params) {\n  const points = line.points;\n  const {\n    count,\n    start,\n    ilen\n  } = pathVars(points, segment, params);\n  const {\n    move = true,\n    reverse\n  } = params || {};\n  let avgX = 0;\n  let countX = 0;\n  let i, point, prevX, minY, maxY, lastY;\n  const pointIndex = index => (start + (reverse ? ilen - index : index)) % count;\n  const drawX = () => {\n    if (minY !== maxY) {\n      ctx.lineTo(avgX, maxY);\n      ctx.lineTo(avgX, minY);\n      ctx.lineTo(avgX, lastY);\n    }\n  };\n  if (move) {\n    point = points[pointIndex(0)];\n    ctx.moveTo(point.x, point.y);\n  }\n  for (i = 0; i <= ilen; ++i) {\n    point = points[pointIndex(i)];\n    if (point.skip) {\n      continue;\n    }\n    const x = point.x;\n    const y = point.y;\n    const truncX = x | 0;\n    if (truncX === prevX) {\n      if (y < minY) {\n        minY = y;\n      } else if (y > maxY) {\n        maxY = y;\n      }\n      avgX = (countX * avgX + x) / ++countX;\n    } else {\n      drawX();\n      ctx.lineTo(x, y);\n      prevX = truncX;\n      countX = 0;\n      minY = maxY = y;\n    }\n    lastY = y;\n  }\n  drawX();\n}\nfunction _getSegmentMethod(line) {\n  const opts = line.options;\n  const borderDash = opts.borderDash && opts.borderDash.length;\n  const useFastPath = !line._decimated && !line._loop && !opts.tension && opts.cubicInterpolationMode !== 'monotone' && !opts.stepped && !borderDash;\n  return useFastPath ? fastPathSegment : pathSegment;\n}\nfunction _getInterpolationMethod(options) {\n  if (options.stepped) {\n    return _steppedInterpolation;\n  }\n  if (options.tension || options.cubicInterpolationMode === 'monotone') {\n    return _bezierInterpolation;\n  }\n  return _pointInLine;\n}\nfunction strokePathWithCache(ctx, line, start, count) {\n  let path = line._path;\n  if (!path) {\n    path = line._path = new Path2D();\n    if (line.path(path, start, count)) {\n      path.closePath();\n    }\n  }\n  setStyle(ctx, line.options);\n  ctx.stroke(path);\n}\nfunction strokePathDirect(ctx, line, start, count) {\n  const {\n    segments,\n    options\n  } = line;\n  const segmentMethod = _getSegmentMethod(line);\n  for (const segment of segments) {\n    setStyle(ctx, options, segment.style);\n    ctx.beginPath();\n    if (segmentMethod(ctx, line, segment, {\n      start,\n      end: start + count - 1\n    })) {\n      ctx.closePath();\n    }\n    ctx.stroke();\n  }\n}\nconst usePath2D = typeof Path2D === 'function';\nfunction draw(ctx, line, start, count) {\n  if (usePath2D && !line.options.segment) {\n    strokePathWithCache(ctx, line, start, count);\n  } else {\n    strokePathDirect(ctx, line, start, count);\n  }\n}\nlet LineElement = /*#__PURE__*/(() => {\n  class LineElement extends Element {\n    static id = 'line';\n    static defaults = {\n      borderCapStyle: 'butt',\n      borderDash: [],\n      borderDashOffset: 0,\n      borderJoinStyle: 'miter',\n      borderWidth: 3,\n      capBezierPoints: true,\n      cubicInterpolationMode: 'default',\n      fill: false,\n      spanGaps: false,\n      stepped: false,\n      tension: 0\n    };\n    static defaultRoutes = {\n      backgroundColor: 'backgroundColor',\n      borderColor: 'borderColor'\n    };\n    static descriptors = {\n      _scriptable: true,\n      _indexable: name => name !== 'borderDash' && name !== 'fill'\n    };\n    constructor(cfg) {\n      super();\n      this.animated = true;\n      this.options = undefined;\n      this._chart = undefined;\n      this._loop = undefined;\n      this._fullLoop = undefined;\n      this._path = undefined;\n      this._points = undefined;\n      this._segments = undefined;\n      this._decimated = false;\n      this._pointsUpdated = false;\n      this._datasetIndex = undefined;\n      if (cfg) {\n        Object.assign(this, cfg);\n      }\n    }\n    updateControlPoints(chartArea, indexAxis) {\n      const options = this.options;\n      if ((options.tension || options.cubicInterpolationMode === 'monotone') && !options.stepped && !this._pointsUpdated) {\n        const loop = options.spanGaps ? this._loop : this._fullLoop;\n        _updateBezierControlPoints(this._points, options, chartArea, loop, indexAxis);\n        this._pointsUpdated = true;\n      }\n    }\n    set points(points) {\n      this._points = points;\n      delete this._segments;\n      delete this._path;\n      this._pointsUpdated = false;\n    }\n    get points() {\n      return this._points;\n    }\n    get segments() {\n      return this._segments || (this._segments = _computeSegments(this, this.options.segment));\n    }\n    first() {\n      const segments = this.segments;\n      const points = this.points;\n      return segments.length && points[segments[0].start];\n    }\n    last() {\n      const segments = this.segments;\n      const points = this.points;\n      const count = segments.length;\n      return count && points[segments[count - 1].end];\n    }\n    interpolate(point, property) {\n      const options = this.options;\n      const value = point[property];\n      const points = this.points;\n      const segments = _boundSegments(this, {\n        property,\n        start: value,\n        end: value\n      });\n      if (!segments.length) {\n        return;\n      }\n      const result = [];\n      const _interpolate = _getInterpolationMethod(options);\n      let i, ilen;\n      for (i = 0, ilen = segments.length; i < ilen; ++i) {\n        const {\n          start,\n          end\n        } = segments[i];\n        const p1 = points[start];\n        const p2 = points[end];\n        if (p1 === p2) {\n          result.push(p1);\n          continue;\n        }\n        const t = Math.abs((value - p1[property]) / (p2[property] - p1[property]));\n        const interpolated = _interpolate(p1, p2, t, options.stepped);\n        interpolated[property] = point[property];\n        result.push(interpolated);\n      }\n      return result.length === 1 ? result[0] : result;\n    }\n    pathSegment(ctx, segment, params) {\n      const segmentMethod = _getSegmentMethod(this);\n      return segmentMethod(ctx, this, segment, params);\n    }\n    path(ctx, start, count) {\n      const segments = this.segments;\n      const segmentMethod = _getSegmentMethod(this);\n      let loop = this._loop;\n      start = start || 0;\n      count = count || this.points.length - start;\n      for (const segment of segments) {\n        loop &= segmentMethod(ctx, this, segment, {\n          start,\n          end: start + count - 1\n        });\n      }\n      return !!loop;\n    }\n    draw(ctx, chartArea, start, count) {\n      const options = this.options || {};\n      const points = this.points || [];\n      if (points.length && options.borderWidth) {\n        ctx.save();\n        draw(ctx, this, start, count);\n        ctx.restore();\n      }\n      if (this.animated) {\n        this._pointsUpdated = false;\n        this._path = undefined;\n      }\n    }\n  }\n  return LineElement;\n})();\nfunction inRange$1(el, pos, axis, useFinalPosition) {\n  const options = el.options;\n  const {\n    [axis]: value\n  } = el.getProps([axis], useFinalPosition);\n  return Math.abs(pos - value) < options.radius + options.hitRadius;\n}\nlet PointElement = /*#__PURE__*/(() => {\n  class PointElement extends Element {\n    static id = 'point';\n    parsed;\n    skip;\n    stop;\n    /**\n    * @type {any}\n    */\n    static defaults = {\n      borderWidth: 1,\n      hitRadius: 1,\n      hoverBorderWidth: 1,\n      hoverRadius: 4,\n      pointStyle: 'circle',\n      radius: 3,\n      rotation: 0\n    };\n    /**\n    * @type {any}\n    */\n    static defaultRoutes = {\n      backgroundColor: 'backgroundColor',\n      borderColor: 'borderColor'\n    };\n    constructor(cfg) {\n      super();\n      this.options = undefined;\n      this.parsed = undefined;\n      this.skip = undefined;\n      this.stop = undefined;\n      if (cfg) {\n        Object.assign(this, cfg);\n      }\n    }\n    inRange(mouseX, mouseY, useFinalPosition) {\n      const options = this.options;\n      const {\n        x,\n        y\n      } = this.getProps(['x', 'y'], useFinalPosition);\n      return Math.pow(mouseX - x, 2) + Math.pow(mouseY - y, 2) < Math.pow(options.hitRadius + options.radius, 2);\n    }\n    inXRange(mouseX, useFinalPosition) {\n      return inRange$1(this, mouseX, 'x', useFinalPosition);\n    }\n    inYRange(mouseY, useFinalPosition) {\n      return inRange$1(this, mouseY, 'y', useFinalPosition);\n    }\n    getCenterPoint(useFinalPosition) {\n      const {\n        x,\n        y\n      } = this.getProps(['x', 'y'], useFinalPosition);\n      return {\n        x,\n        y\n      };\n    }\n    size(options) {\n      options = options || this.options || {};\n      let radius = options.radius || 0;\n      radius = Math.max(radius, radius && options.hoverRadius || 0);\n      const borderWidth = radius && options.borderWidth || 0;\n      return (radius + borderWidth) * 2;\n    }\n    draw(ctx, area) {\n      const options = this.options;\n      if (this.skip || options.radius < 0.1 || !_isPointInArea(this, area, this.size(options) / 2)) {\n        return;\n      }\n      ctx.strokeStyle = options.borderColor;\n      ctx.lineWidth = options.borderWidth;\n      ctx.fillStyle = options.backgroundColor;\n      drawPoint(ctx, options, this.x, this.y);\n    }\n    getRange() {\n      const options = this.options || {};\n      // @ts-expect-error Fallbacks should never be hit in practice\n      return options.radius + options.hitRadius;\n    }\n  }\n  return PointElement;\n})();\nfunction getBarBounds(bar, useFinalPosition) {\n  const {\n    x,\n    y,\n    base,\n    width,\n    height\n  } = bar.getProps(['x', 'y', 'base', 'width', 'height'], useFinalPosition);\n  let left, right, top, bottom, half;\n  if (bar.horizontal) {\n    half = height / 2;\n    left = Math.min(x, base);\n    right = Math.max(x, base);\n    top = y - half;\n    bottom = y + half;\n  } else {\n    half = width / 2;\n    left = x - half;\n    right = x + half;\n    top = Math.min(y, base);\n    bottom = Math.max(y, base);\n  }\n  return {\n    left,\n    top,\n    right,\n    bottom\n  };\n}\nfunction skipOrLimit(skip, value, min, max) {\n  return skip ? 0 : _limitValue(value, min, max);\n}\nfunction parseBorderWidth(bar, maxW, maxH) {\n  const value = bar.options.borderWidth;\n  const skip = bar.borderSkipped;\n  const o = toTRBL(value);\n  return {\n    t: skipOrLimit(skip.top, o.top, 0, maxH),\n    r: skipOrLimit(skip.right, o.right, 0, maxW),\n    b: skipOrLimit(skip.bottom, o.bottom, 0, maxH),\n    l: skipOrLimit(skip.left, o.left, 0, maxW)\n  };\n}\nfunction parseBorderRadius(bar, maxW, maxH) {\n  const {\n    enableBorderRadius\n  } = bar.getProps(['enableBorderRadius']);\n  const value = bar.options.borderRadius;\n  const o = toTRBLCorners(value);\n  const maxR = Math.min(maxW, maxH);\n  const skip = bar.borderSkipped;\n  const enableBorder = enableBorderRadius || isObject(value);\n  return {\n    topLeft: skipOrLimit(!enableBorder || skip.top || skip.left, o.topLeft, 0, maxR),\n    topRight: skipOrLimit(!enableBorder || skip.top || skip.right, o.topRight, 0, maxR),\n    bottomLeft: skipOrLimit(!enableBorder || skip.bottom || skip.left, o.bottomLeft, 0, maxR),\n    bottomRight: skipOrLimit(!enableBorder || skip.bottom || skip.right, o.bottomRight, 0, maxR)\n  };\n}\nfunction boundingRects(bar) {\n  const bounds = getBarBounds(bar);\n  const width = bounds.right - bounds.left;\n  const height = bounds.bottom - bounds.top;\n  const border = parseBorderWidth(bar, width / 2, height / 2);\n  const radius = parseBorderRadius(bar, width / 2, height / 2);\n  return {\n    outer: {\n      x: bounds.left,\n      y: bounds.top,\n      w: width,\n      h: height,\n      radius\n    },\n    inner: {\n      x: bounds.left + border.l,\n      y: bounds.top + border.t,\n      w: width - border.l - border.r,\n      h: height - border.t - border.b,\n      radius: {\n        topLeft: Math.max(0, radius.topLeft - Math.max(border.t, border.l)),\n        topRight: Math.max(0, radius.topRight - Math.max(border.t, border.r)),\n        bottomLeft: Math.max(0, radius.bottomLeft - Math.max(border.b, border.l)),\n        bottomRight: Math.max(0, radius.bottomRight - Math.max(border.b, border.r))\n      }\n    }\n  };\n}\nfunction inRange(bar, x, y, useFinalPosition) {\n  const skipX = x === null;\n  const skipY = y === null;\n  const skipBoth = skipX && skipY;\n  const bounds = bar && !skipBoth && getBarBounds(bar, useFinalPosition);\n  return bounds && (skipX || _isBetween(x, bounds.left, bounds.right)) && (skipY || _isBetween(y, bounds.top, bounds.bottom));\n}\nfunction hasRadius(radius) {\n  return radius.topLeft || radius.topRight || radius.bottomLeft || radius.bottomRight;\n}\nfunction addNormalRectPath(ctx, rect) {\n  ctx.rect(rect.x, rect.y, rect.w, rect.h);\n}\nfunction inflateRect(rect, amount, refRect = {}) {\n  const x = rect.x !== refRect.x ? -amount : 0;\n  const y = rect.y !== refRect.y ? -amount : 0;\n  const w = (rect.x + rect.w !== refRect.x + refRect.w ? amount : 0) - x;\n  const h = (rect.y + rect.h !== refRect.y + refRect.h ? amount : 0) - y;\n  return {\n    x: rect.x + x,\n    y: rect.y + y,\n    w: rect.w + w,\n    h: rect.h + h,\n    radius: rect.radius\n  };\n}\nclass BarElement extends Element {\n  static id = 'bar';\n  static defaults = {\n    borderSkipped: 'start',\n    borderWidth: 0,\n    borderRadius: 0,\n    inflateAmount: 'auto',\n    pointStyle: undefined\n  };\n  static defaultRoutes = {\n    backgroundColor: 'backgroundColor',\n    borderColor: 'borderColor'\n  };\n  constructor(cfg) {\n    super();\n    this.options = undefined;\n    this.horizontal = undefined;\n    this.base = undefined;\n    this.width = undefined;\n    this.height = undefined;\n    this.inflateAmount = undefined;\n    if (cfg) {\n      Object.assign(this, cfg);\n    }\n  }\n  draw(ctx) {\n    const {\n      inflateAmount,\n      options: {\n        borderColor,\n        backgroundColor\n      }\n    } = this;\n    const {\n      inner,\n      outer\n    } = boundingRects(this);\n    const addRectPath = hasRadius(outer.radius) ? addRoundedRectPath : addNormalRectPath;\n    ctx.save();\n    if (outer.w !== inner.w || outer.h !== inner.h) {\n      ctx.beginPath();\n      addRectPath(ctx, inflateRect(outer, inflateAmount, inner));\n      ctx.clip();\n      addRectPath(ctx, inflateRect(inner, -inflateAmount, outer));\n      ctx.fillStyle = borderColor;\n      ctx.fill('evenodd');\n    }\n    ctx.beginPath();\n    addRectPath(ctx, inflateRect(inner, inflateAmount));\n    ctx.fillStyle = backgroundColor;\n    ctx.fill();\n    ctx.restore();\n  }\n  inRange(mouseX, mouseY, useFinalPosition) {\n    return inRange(this, mouseX, mouseY, useFinalPosition);\n  }\n  inXRange(mouseX, useFinalPosition) {\n    return inRange(this, mouseX, null, useFinalPosition);\n  }\n  inYRange(mouseY, useFinalPosition) {\n    return inRange(this, null, mouseY, useFinalPosition);\n  }\n  getCenterPoint(useFinalPosition) {\n    const {\n      x,\n      y,\n      base,\n      horizontal\n    } = this.getProps(['x', 'y', 'base', 'horizontal'], useFinalPosition);\n    return {\n      x: horizontal ? (x + base) / 2 : x,\n      y: horizontal ? y : (y + base) / 2\n    };\n  }\n  getRange(axis) {\n    return axis === 'x' ? this.width / 2 : this.height / 2;\n  }\n}\nvar elements = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  ArcElement: ArcElement,\n  BarElement: BarElement,\n  LineElement: LineElement,\n  PointElement: PointElement\n});\nconst BORDER_COLORS = ['rgb(54, 162, 235)', 'rgb(255, 99, 132)', 'rgb(255, 159, 64)', 'rgb(255, 205, 86)', 'rgb(75, 192, 192)', 'rgb(153, 102, 255)', 'rgb(201, 203, 207)' // grey\n];\n// Border colors with 50% transparency\nconst BACKGROUND_COLORS = /* #__PURE__ */BORDER_COLORS.map(color => color.replace('rgb(', 'rgba(').replace(')', ', 0.5)'));\nfunction getBorderColor(i) {\n  return BORDER_COLORS[i % BORDER_COLORS.length];\n}\nfunction getBackgroundColor(i) {\n  return BACKGROUND_COLORS[i % BACKGROUND_COLORS.length];\n}\nfunction colorizeDefaultDataset(dataset, i) {\n  dataset.borderColor = getBorderColor(i);\n  dataset.backgroundColor = getBackgroundColor(i);\n  return ++i;\n}\nfunction colorizeDoughnutDataset(dataset, i) {\n  dataset.backgroundColor = dataset.data.map(() => getBorderColor(i++));\n  return i;\n}\nfunction colorizePolarAreaDataset(dataset, i) {\n  dataset.backgroundColor = dataset.data.map(() => getBackgroundColor(i++));\n  return i;\n}\nfunction getColorizer(chart) {\n  let i = 0;\n  return (dataset, datasetIndex) => {\n    const controller = chart.getDatasetMeta(datasetIndex).controller;\n    if (controller instanceof DoughnutController) {\n      i = colorizeDoughnutDataset(dataset, i);\n    } else if (controller instanceof PolarAreaController) {\n      i = colorizePolarAreaDataset(dataset, i);\n    } else if (controller) {\n      i = colorizeDefaultDataset(dataset, i);\n    }\n  };\n}\nfunction containsColorsDefinitions(descriptors) {\n  let k;\n  for (k in descriptors) {\n    if (descriptors[k].borderColor || descriptors[k].backgroundColor) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction containsColorsDefinition(descriptor) {\n  return descriptor && (descriptor.borderColor || descriptor.backgroundColor);\n}\nvar plugin_colors = {\n  id: 'colors',\n  defaults: {\n    enabled: true,\n    forceOverride: false\n  },\n  beforeLayout(chart, _args, options) {\n    if (!options.enabled) {\n      return;\n    }\n    const {\n      data: {\n        datasets\n      },\n      options: chartOptions\n    } = chart.config;\n    const {\n      elements\n    } = chartOptions;\n    if (!options.forceOverride && (containsColorsDefinitions(datasets) || containsColorsDefinition(chartOptions) || elements && containsColorsDefinitions(elements))) {\n      return;\n    }\n    const colorizer = getColorizer(chart);\n    datasets.forEach(colorizer);\n  }\n};\nfunction lttbDecimation(data, start, count, availableWidth, options) {\n  const samples = options.samples || availableWidth;\n  if (samples >= count) {\n    return data.slice(start, start + count);\n  }\n  const decimated = [];\n  const bucketWidth = (count - 2) / (samples - 2);\n  let sampledIndex = 0;\n  const endIndex = start + count - 1;\n  let a = start;\n  let i, maxAreaPoint, maxArea, area, nextA;\n  decimated[sampledIndex++] = data[a];\n  for (i = 0; i < samples - 2; i++) {\n    let avgX = 0;\n    let avgY = 0;\n    let j;\n    const avgRangeStart = Math.floor((i + 1) * bucketWidth) + 1 + start;\n    const avgRangeEnd = Math.min(Math.floor((i + 2) * bucketWidth) + 1, count) + start;\n    const avgRangeLength = avgRangeEnd - avgRangeStart;\n    for (j = avgRangeStart; j < avgRangeEnd; j++) {\n      avgX += data[j].x;\n      avgY += data[j].y;\n    }\n    avgX /= avgRangeLength;\n    avgY /= avgRangeLength;\n    const rangeOffs = Math.floor(i * bucketWidth) + 1 + start;\n    const rangeTo = Math.min(Math.floor((i + 1) * bucketWidth) + 1, count) + start;\n    const {\n      x: pointAx,\n      y: pointAy\n    } = data[a];\n    maxArea = area = -1;\n    for (j = rangeOffs; j < rangeTo; j++) {\n      area = 0.5 * Math.abs((pointAx - avgX) * (data[j].y - pointAy) - (pointAx - data[j].x) * (avgY - pointAy));\n      if (area > maxArea) {\n        maxArea = area;\n        maxAreaPoint = data[j];\n        nextA = j;\n      }\n    }\n    decimated[sampledIndex++] = maxAreaPoint;\n    a = nextA;\n  }\n  decimated[sampledIndex++] = data[endIndex];\n  return decimated;\n}\nfunction minMaxDecimation(data, start, count, availableWidth) {\n  let avgX = 0;\n  let countX = 0;\n  let i, point, x, y, prevX, minIndex, maxIndex, startIndex, minY, maxY;\n  const decimated = [];\n  const endIndex = start + count - 1;\n  const xMin = data[start].x;\n  const xMax = data[endIndex].x;\n  const dx = xMax - xMin;\n  for (i = start; i < start + count; ++i) {\n    point = data[i];\n    x = (point.x - xMin) / dx * availableWidth;\n    y = point.y;\n    const truncX = x | 0;\n    if (truncX === prevX) {\n      if (y < minY) {\n        minY = y;\n        minIndex = i;\n      } else if (y > maxY) {\n        maxY = y;\n        maxIndex = i;\n      }\n      avgX = (countX * avgX + point.x) / ++countX;\n    } else {\n      const lastIndex = i - 1;\n      if (!isNullOrUndef(minIndex) && !isNullOrUndef(maxIndex)) {\n        const intermediateIndex1 = Math.min(minIndex, maxIndex);\n        const intermediateIndex2 = Math.max(minIndex, maxIndex);\n        if (intermediateIndex1 !== startIndex && intermediateIndex1 !== lastIndex) {\n          decimated.push({\n            ...data[intermediateIndex1],\n            x: avgX\n          });\n        }\n        if (intermediateIndex2 !== startIndex && intermediateIndex2 !== lastIndex) {\n          decimated.push({\n            ...data[intermediateIndex2],\n            x: avgX\n          });\n        }\n      }\n      if (i > 0 && lastIndex !== startIndex) {\n        decimated.push(data[lastIndex]);\n      }\n      decimated.push(point);\n      prevX = truncX;\n      countX = 0;\n      minY = maxY = y;\n      minIndex = maxIndex = startIndex = i;\n    }\n  }\n  return decimated;\n}\nfunction cleanDecimatedDataset(dataset) {\n  if (dataset._decimated) {\n    const data = dataset._data;\n    delete dataset._decimated;\n    delete dataset._data;\n    Object.defineProperty(dataset, 'data', {\n      configurable: true,\n      enumerable: true,\n      writable: true,\n      value: data\n    });\n  }\n}\nfunction cleanDecimatedData(chart) {\n  chart.data.datasets.forEach(dataset => {\n    cleanDecimatedDataset(dataset);\n  });\n}\nfunction getStartAndCountOfVisiblePointsSimplified(meta, points) {\n  const pointCount = points.length;\n  let start = 0;\n  let count;\n  const {\n    iScale\n  } = meta;\n  const {\n    min,\n    max,\n    minDefined,\n    maxDefined\n  } = iScale.getUserBounds();\n  if (minDefined) {\n    start = _limitValue(_lookupByKey(points, iScale.axis, min).lo, 0, pointCount - 1);\n  }\n  if (maxDefined) {\n    count = _limitValue(_lookupByKey(points, iScale.axis, max).hi + 1, start, pointCount) - start;\n  } else {\n    count = pointCount - start;\n  }\n  return {\n    start,\n    count\n  };\n}\nvar plugin_decimation = {\n  id: 'decimation',\n  defaults: {\n    algorithm: 'min-max',\n    enabled: false\n  },\n  beforeElementsUpdate: (chart, args, options) => {\n    if (!options.enabled) {\n      cleanDecimatedData(chart);\n      return;\n    }\n    const availableWidth = chart.width;\n    chart.data.datasets.forEach((dataset, datasetIndex) => {\n      const {\n        _data,\n        indexAxis\n      } = dataset;\n      const meta = chart.getDatasetMeta(datasetIndex);\n      const data = _data || dataset.data;\n      if (resolve([indexAxis, chart.options.indexAxis]) === 'y') {\n        return;\n      }\n      if (!meta.controller.supportsDecimation) {\n        return;\n      }\n      const xAxis = chart.scales[meta.xAxisID];\n      if (xAxis.type !== 'linear' && xAxis.type !== 'time') {\n        return;\n      }\n      if (chart.options.parsing) {\n        return;\n      }\n      let {\n        start,\n        count\n      } = getStartAndCountOfVisiblePointsSimplified(meta, data);\n      const threshold = options.threshold || 4 * availableWidth;\n      if (count <= threshold) {\n        cleanDecimatedDataset(dataset);\n        return;\n      }\n      if (isNullOrUndef(_data)) {\n        dataset._data = data;\n        delete dataset.data;\n        Object.defineProperty(dataset, 'data', {\n          configurable: true,\n          enumerable: true,\n          get: function () {\n            return this._decimated;\n          },\n          set: function (d) {\n            this._data = d;\n          }\n        });\n      }\n      let decimated;\n      switch (options.algorithm) {\n        case 'lttb':\n          decimated = lttbDecimation(data, start, count, availableWidth, options);\n          break;\n        case 'min-max':\n          decimated = minMaxDecimation(data, start, count, availableWidth);\n          break;\n        default:\n          throw new Error(`Unsupported decimation algorithm '${options.algorithm}'`);\n      }\n      dataset._decimated = decimated;\n    });\n  },\n  destroy(chart) {\n    cleanDecimatedData(chart);\n  }\n};\nfunction _segments(line, target, property) {\n  const segments = line.segments;\n  const points = line.points;\n  const tpoints = target.points;\n  const parts = [];\n  for (const segment of segments) {\n    let {\n      start,\n      end\n    } = segment;\n    end = _findSegmentEnd(start, end, points);\n    const bounds = _getBounds(property, points[start], points[end], segment.loop);\n    if (!target.segments) {\n      parts.push({\n        source: segment,\n        target: bounds,\n        start: points[start],\n        end: points[end]\n      });\n      continue;\n    }\n    const targetSegments = _boundSegments(target, bounds);\n    for (const tgt of targetSegments) {\n      const subBounds = _getBounds(property, tpoints[tgt.start], tpoints[tgt.end], tgt.loop);\n      const fillSources = _boundSegment(segment, points, subBounds);\n      for (const fillSource of fillSources) {\n        parts.push({\n          source: fillSource,\n          target: tgt,\n          start: {\n            [property]: _getEdge(bounds, subBounds, 'start', Math.max)\n          },\n          end: {\n            [property]: _getEdge(bounds, subBounds, 'end', Math.min)\n          }\n        });\n      }\n    }\n  }\n  return parts;\n}\nfunction _getBounds(property, first, last, loop) {\n  if (loop) {\n    return;\n  }\n  let start = first[property];\n  let end = last[property];\n  if (property === 'angle') {\n    start = _normalizeAngle(start);\n    end = _normalizeAngle(end);\n  }\n  return {\n    property,\n    start,\n    end\n  };\n}\nfunction _pointsFromSegments(boundary, line) {\n  const {\n    x = null,\n    y = null\n  } = boundary || {};\n  const linePoints = line.points;\n  const points = [];\n  line.segments.forEach(({\n    start,\n    end\n  }) => {\n    end = _findSegmentEnd(start, end, linePoints);\n    const first = linePoints[start];\n    const last = linePoints[end];\n    if (y !== null) {\n      points.push({\n        x: first.x,\n        y\n      });\n      points.push({\n        x: last.x,\n        y\n      });\n    } else if (x !== null) {\n      points.push({\n        x,\n        y: first.y\n      });\n      points.push({\n        x,\n        y: last.y\n      });\n    }\n  });\n  return points;\n}\nfunction _findSegmentEnd(start, end, points) {\n  for (; end > start; end--) {\n    const point = points[end];\n    if (!isNaN(point.x) && !isNaN(point.y)) {\n      break;\n    }\n  }\n  return end;\n}\nfunction _getEdge(a, b, prop, fn) {\n  if (a && b) {\n    return fn(a[prop], b[prop]);\n  }\n  return a ? a[prop] : b ? b[prop] : 0;\n}\nfunction _createBoundaryLine(boundary, line) {\n  let points = [];\n  let _loop = false;\n  if (isArray(boundary)) {\n    _loop = true;\n    points = boundary;\n  } else {\n    points = _pointsFromSegments(boundary, line);\n  }\n  return points.length ? new LineElement({\n    points,\n    options: {\n      tension: 0\n    },\n    _loop,\n    _fullLoop: _loop\n  }) : null;\n}\nfunction _shouldApplyFill(source) {\n  return source && source.fill !== false;\n}\nfunction _resolveTarget(sources, index, propagate) {\n  const source = sources[index];\n  let fill = source.fill;\n  const visited = [index];\n  let target;\n  if (!propagate) {\n    return fill;\n  }\n  while (fill !== false && visited.indexOf(fill) === -1) {\n    if (!isNumberFinite(fill)) {\n      return fill;\n    }\n    target = sources[fill];\n    if (!target) {\n      return false;\n    }\n    if (target.visible) {\n      return fill;\n    }\n    visited.push(fill);\n    fill = target.fill;\n  }\n  return false;\n}\nfunction _decodeFill(line, index, count) {\n  const fill = parseFillOption(line);\n  if (isObject(fill)) {\n    return isNaN(fill.value) ? false : fill;\n  }\n  let target = parseFloat(fill);\n  if (isNumberFinite(target) && Math.floor(target) === target) {\n    return decodeTargetIndex(fill[0], index, target, count);\n  }\n  return ['origin', 'start', 'end', 'stack', 'shape'].indexOf(fill) >= 0 && fill;\n}\nfunction decodeTargetIndex(firstCh, index, target, count) {\n  if (firstCh === '-' || firstCh === '+') {\n    target = index + target;\n  }\n  if (target === index || target < 0 || target >= count) {\n    return false;\n  }\n  return target;\n}\nfunction _getTargetPixel(fill, scale) {\n  let pixel = null;\n  if (fill === 'start') {\n    pixel = scale.bottom;\n  } else if (fill === 'end') {\n    pixel = scale.top;\n  } else if (isObject(fill)) {\n    pixel = scale.getPixelForValue(fill.value);\n  } else if (scale.getBasePixel) {\n    pixel = scale.getBasePixel();\n  }\n  return pixel;\n}\nfunction _getTargetValue(fill, scale, startValue) {\n  let value;\n  if (fill === 'start') {\n    value = startValue;\n  } else if (fill === 'end') {\n    value = scale.options.reverse ? scale.min : scale.max;\n  } else if (isObject(fill)) {\n    value = fill.value;\n  } else {\n    value = scale.getBaseValue();\n  }\n  return value;\n}\nfunction parseFillOption(line) {\n  const options = line.options;\n  const fillOption = options.fill;\n  let fill = valueOrDefault(fillOption && fillOption.target, fillOption);\n  if (fill === undefined) {\n    fill = !!options.backgroundColor;\n  }\n  if (fill === false || fill === null) {\n    return false;\n  }\n  if (fill === true) {\n    return 'origin';\n  }\n  return fill;\n}\nfunction _buildStackLine(source) {\n  const {\n    scale,\n    index,\n    line\n  } = source;\n  const points = [];\n  const segments = line.segments;\n  const sourcePoints = line.points;\n  const linesBelow = getLinesBelow(scale, index);\n  linesBelow.push(_createBoundaryLine({\n    x: null,\n    y: scale.bottom\n  }, line));\n  for (let i = 0; i < segments.length; i++) {\n    const segment = segments[i];\n    for (let j = segment.start; j <= segment.end; j++) {\n      addPointsBelow(points, sourcePoints[j], linesBelow);\n    }\n  }\n  return new LineElement({\n    points,\n    options: {}\n  });\n}\nfunction getLinesBelow(scale, index) {\n  const below = [];\n  const metas = scale.getMatchingVisibleMetas('line');\n  for (let i = 0; i < metas.length; i++) {\n    const meta = metas[i];\n    if (meta.index === index) {\n      break;\n    }\n    if (!meta.hidden) {\n      below.unshift(meta.dataset);\n    }\n  }\n  return below;\n}\nfunction addPointsBelow(points, sourcePoint, linesBelow) {\n  const postponed = [];\n  for (let j = 0; j < linesBelow.length; j++) {\n    const line = linesBelow[j];\n    const {\n      first,\n      last,\n      point\n    } = findPoint(line, sourcePoint, 'x');\n    if (!point || first && last) {\n      continue;\n    }\n    if (first) {\n      postponed.unshift(point);\n    } else {\n      points.push(point);\n      if (!last) {\n        break;\n      }\n    }\n  }\n  points.push(...postponed);\n}\nfunction findPoint(line, sourcePoint, property) {\n  const point = line.interpolate(sourcePoint, property);\n  if (!point) {\n    return {};\n  }\n  const pointValue = point[property];\n  const segments = line.segments;\n  const linePoints = line.points;\n  let first = false;\n  let last = false;\n  for (let i = 0; i < segments.length; i++) {\n    const segment = segments[i];\n    const firstValue = linePoints[segment.start][property];\n    const lastValue = linePoints[segment.end][property];\n    if (_isBetween(pointValue, firstValue, lastValue)) {\n      first = pointValue === firstValue;\n      last = pointValue === lastValue;\n      break;\n    }\n  }\n  return {\n    first,\n    last,\n    point\n  };\n}\nclass simpleArc {\n  constructor(opts) {\n    this.x = opts.x;\n    this.y = opts.y;\n    this.radius = opts.radius;\n  }\n  pathSegment(ctx, bounds, opts) {\n    const {\n      x,\n      y,\n      radius\n    } = this;\n    bounds = bounds || {\n      start: 0,\n      end: TAU\n    };\n    ctx.arc(x, y, radius, bounds.end, bounds.start, true);\n    return !opts.bounds;\n  }\n  interpolate(point) {\n    const {\n      x,\n      y,\n      radius\n    } = this;\n    const angle = point.angle;\n    return {\n      x: x + Math.cos(angle) * radius,\n      y: y + Math.sin(angle) * radius,\n      angle\n    };\n  }\n}\nfunction _getTarget(source) {\n  const {\n    chart,\n    fill,\n    line\n  } = source;\n  if (isNumberFinite(fill)) {\n    return getLineByIndex(chart, fill);\n  }\n  if (fill === 'stack') {\n    return _buildStackLine(source);\n  }\n  if (fill === 'shape') {\n    return true;\n  }\n  const boundary = computeBoundary(source);\n  if (boundary instanceof simpleArc) {\n    return boundary;\n  }\n  return _createBoundaryLine(boundary, line);\n}\nfunction getLineByIndex(chart, index) {\n  const meta = chart.getDatasetMeta(index);\n  const visible = meta && chart.isDatasetVisible(index);\n  return visible ? meta.dataset : null;\n}\nfunction computeBoundary(source) {\n  const scale = source.scale || {};\n  if (scale.getPointPositionForValue) {\n    return computeCircularBoundary(source);\n  }\n  return computeLinearBoundary(source);\n}\nfunction computeLinearBoundary(source) {\n  const {\n    scale = {},\n    fill\n  } = source;\n  const pixel = _getTargetPixel(fill, scale);\n  if (isNumberFinite(pixel)) {\n    const horizontal = scale.isHorizontal();\n    return {\n      x: horizontal ? pixel : null,\n      y: horizontal ? null : pixel\n    };\n  }\n  return null;\n}\nfunction computeCircularBoundary(source) {\n  const {\n    scale,\n    fill\n  } = source;\n  const options = scale.options;\n  const length = scale.getLabels().length;\n  const start = options.reverse ? scale.max : scale.min;\n  const value = _getTargetValue(fill, scale, start);\n  const target = [];\n  if (options.grid.circular) {\n    const center = scale.getPointPositionForValue(0, start);\n    return new simpleArc({\n      x: center.x,\n      y: center.y,\n      radius: scale.getDistanceFromCenterForValue(value)\n    });\n  }\n  for (let i = 0; i < length; ++i) {\n    target.push(scale.getPointPositionForValue(i, value));\n  }\n  return target;\n}\nfunction _drawfill(ctx, source, area) {\n  const target = _getTarget(source);\n  const {\n    line,\n    scale,\n    axis\n  } = source;\n  const lineOpts = line.options;\n  const fillOption = lineOpts.fill;\n  const color = lineOpts.backgroundColor;\n  const {\n    above = color,\n    below = color\n  } = fillOption || {};\n  if (target && line.points.length) {\n    clipArea(ctx, area);\n    doFill(ctx, {\n      line,\n      target,\n      above,\n      below,\n      area,\n      scale,\n      axis\n    });\n    unclipArea(ctx);\n  }\n}\nfunction doFill(ctx, cfg) {\n  const {\n    line,\n    target,\n    above,\n    below,\n    area,\n    scale\n  } = cfg;\n  const property = line._loop ? 'angle' : cfg.axis;\n  ctx.save();\n  if (property === 'x' && below !== above) {\n    clipVertical(ctx, target, area.top);\n    fill(ctx, {\n      line,\n      target,\n      color: above,\n      scale,\n      property\n    });\n    ctx.restore();\n    ctx.save();\n    clipVertical(ctx, target, area.bottom);\n  }\n  fill(ctx, {\n    line,\n    target,\n    color: below,\n    scale,\n    property\n  });\n  ctx.restore();\n}\nfunction clipVertical(ctx, target, clipY) {\n  const {\n    segments,\n    points\n  } = target;\n  let first = true;\n  let lineLoop = false;\n  ctx.beginPath();\n  for (const segment of segments) {\n    const {\n      start,\n      end\n    } = segment;\n    const firstPoint = points[start];\n    const lastPoint = points[_findSegmentEnd(start, end, points)];\n    if (first) {\n      ctx.moveTo(firstPoint.x, firstPoint.y);\n      first = false;\n    } else {\n      ctx.lineTo(firstPoint.x, clipY);\n      ctx.lineTo(firstPoint.x, firstPoint.y);\n    }\n    lineLoop = !!target.pathSegment(ctx, segment, {\n      move: lineLoop\n    });\n    if (lineLoop) {\n      ctx.closePath();\n    } else {\n      ctx.lineTo(lastPoint.x, clipY);\n    }\n  }\n  ctx.lineTo(target.first().x, clipY);\n  ctx.closePath();\n  ctx.clip();\n}\nfunction fill(ctx, cfg) {\n  const {\n    line,\n    target,\n    property,\n    color,\n    scale\n  } = cfg;\n  const segments = _segments(line, target, property);\n  for (const {\n    source: src,\n    target: tgt,\n    start,\n    end\n  } of segments) {\n    const {\n      style: {\n        backgroundColor = color\n      } = {}\n    } = src;\n    const notShape = target !== true;\n    ctx.save();\n    ctx.fillStyle = backgroundColor;\n    clipBounds(ctx, scale, notShape && _getBounds(property, start, end));\n    ctx.beginPath();\n    const lineLoop = !!line.pathSegment(ctx, src);\n    let loop;\n    if (notShape) {\n      if (lineLoop) {\n        ctx.closePath();\n      } else {\n        interpolatedLineTo(ctx, target, end, property);\n      }\n      const targetLoop = !!target.pathSegment(ctx, tgt, {\n        move: lineLoop,\n        reverse: true\n      });\n      loop = lineLoop && targetLoop;\n      if (!loop) {\n        interpolatedLineTo(ctx, target, start, property);\n      }\n    }\n    ctx.closePath();\n    ctx.fill(loop ? 'evenodd' : 'nonzero');\n    ctx.restore();\n  }\n}\nfunction clipBounds(ctx, scale, bounds) {\n  const {\n    top,\n    bottom\n  } = scale.chart.chartArea;\n  const {\n    property,\n    start,\n    end\n  } = bounds || {};\n  if (property === 'x') {\n    ctx.beginPath();\n    ctx.rect(start, top, end - start, bottom - top);\n    ctx.clip();\n  }\n}\nfunction interpolatedLineTo(ctx, target, point, property) {\n  const interpolatedPoint = target.interpolate(point, property);\n  if (interpolatedPoint) {\n    ctx.lineTo(interpolatedPoint.x, interpolatedPoint.y);\n  }\n}\nvar index = {\n  id: 'filler',\n  afterDatasetsUpdate(chart, _args, options) {\n    const count = (chart.data.datasets || []).length;\n    const sources = [];\n    let meta, i, line, source;\n    for (i = 0; i < count; ++i) {\n      meta = chart.getDatasetMeta(i);\n      line = meta.dataset;\n      source = null;\n      if (line && line.options && line instanceof LineElement) {\n        source = {\n          visible: chart.isDatasetVisible(i),\n          index: i,\n          fill: _decodeFill(line, i, count),\n          chart,\n          axis: meta.controller.options.indexAxis,\n          scale: meta.vScale,\n          line\n        };\n      }\n      meta.$filler = source;\n      sources.push(source);\n    }\n    for (i = 0; i < count; ++i) {\n      source = sources[i];\n      if (!source || source.fill === false) {\n        continue;\n      }\n      source.fill = _resolveTarget(sources, i, options.propagate);\n    }\n  },\n  beforeDraw(chart, _args, options) {\n    const draw = options.drawTime === 'beforeDraw';\n    const metasets = chart.getSortedVisibleDatasetMetas();\n    const area = chart.chartArea;\n    for (let i = metasets.length - 1; i >= 0; --i) {\n      const source = metasets[i].$filler;\n      if (!source) {\n        continue;\n      }\n      source.line.updateControlPoints(area, source.axis);\n      if (draw && source.fill) {\n        _drawfill(chart.ctx, source, area);\n      }\n    }\n  },\n  beforeDatasetsDraw(chart, _args, options) {\n    if (options.drawTime !== 'beforeDatasetsDraw') {\n      return;\n    }\n    const metasets = chart.getSortedVisibleDatasetMetas();\n    for (let i = metasets.length - 1; i >= 0; --i) {\n      const source = metasets[i].$filler;\n      if (_shouldApplyFill(source)) {\n        _drawfill(chart.ctx, source, chart.chartArea);\n      }\n    }\n  },\n  beforeDatasetDraw(chart, args, options) {\n    const source = args.meta.$filler;\n    if (!_shouldApplyFill(source) || options.drawTime !== 'beforeDatasetDraw') {\n      return;\n    }\n    _drawfill(chart.ctx, source, chart.chartArea);\n  },\n  defaults: {\n    propagate: true,\n    drawTime: 'beforeDatasetDraw'\n  }\n};\nconst getBoxSize = (labelOpts, fontSize) => {\n  let {\n    boxHeight = fontSize,\n    boxWidth = fontSize\n  } = labelOpts;\n  if (labelOpts.usePointStyle) {\n    boxHeight = Math.min(boxHeight, fontSize);\n    boxWidth = labelOpts.pointStyleWidth || Math.min(boxWidth, fontSize);\n  }\n  return {\n    boxWidth,\n    boxHeight,\n    itemHeight: Math.max(fontSize, boxHeight)\n  };\n};\nconst itemsEqual = (a, b) => a !== null && b !== null && a.datasetIndex === b.datasetIndex && a.index === b.index;\nclass Legend extends Element {\n  constructor(config) {\n    super();\n    this._added = false;\n    this.legendHitBoxes = [];\n    this._hoveredItem = null;\n    this.doughnutMode = false;\n    this.chart = config.chart;\n    this.options = config.options;\n    this.ctx = config.ctx;\n    this.legendItems = undefined;\n    this.columnSizes = undefined;\n    this.lineWidths = undefined;\n    this.maxHeight = undefined;\n    this.maxWidth = undefined;\n    this.top = undefined;\n    this.bottom = undefined;\n    this.left = undefined;\n    this.right = undefined;\n    this.height = undefined;\n    this.width = undefined;\n    this._margins = undefined;\n    this.position = undefined;\n    this.weight = undefined;\n    this.fullSize = undefined;\n  }\n  update(maxWidth, maxHeight, margins) {\n    this.maxWidth = maxWidth;\n    this.maxHeight = maxHeight;\n    this._margins = margins;\n    this.setDimensions();\n    this.buildLabels();\n    this.fit();\n  }\n  setDimensions() {\n    if (this.isHorizontal()) {\n      this.width = this.maxWidth;\n      this.left = this._margins.left;\n      this.right = this.width;\n    } else {\n      this.height = this.maxHeight;\n      this.top = this._margins.top;\n      this.bottom = this.height;\n    }\n  }\n  buildLabels() {\n    const labelOpts = this.options.labels || {};\n    let legendItems = callback(labelOpts.generateLabels, [this.chart], this) || [];\n    if (labelOpts.filter) {\n      legendItems = legendItems.filter(item => labelOpts.filter(item, this.chart.data));\n    }\n    if (labelOpts.sort) {\n      legendItems = legendItems.sort((a, b) => labelOpts.sort(a, b, this.chart.data));\n    }\n    if (this.options.reverse) {\n      legendItems.reverse();\n    }\n    this.legendItems = legendItems;\n  }\n  fit() {\n    const {\n      options,\n      ctx\n    } = this;\n    if (!options.display) {\n      this.width = this.height = 0;\n      return;\n    }\n    const labelOpts = options.labels;\n    const labelFont = toFont(labelOpts.font);\n    const fontSize = labelFont.size;\n    const titleHeight = this._computeTitleHeight();\n    const {\n      boxWidth,\n      itemHeight\n    } = getBoxSize(labelOpts, fontSize);\n    let width, height;\n    ctx.font = labelFont.string;\n    if (this.isHorizontal()) {\n      width = this.maxWidth;\n      height = this._fitRows(titleHeight, fontSize, boxWidth, itemHeight) + 10;\n    } else {\n      height = this.maxHeight;\n      width = this._fitCols(titleHeight, labelFont, boxWidth, itemHeight) + 10;\n    }\n    this.width = Math.min(width, options.maxWidth || this.maxWidth);\n    this.height = Math.min(height, options.maxHeight || this.maxHeight);\n  }\n  _fitRows(titleHeight, fontSize, boxWidth, itemHeight) {\n    const {\n      ctx,\n      maxWidth,\n      options: {\n        labels: {\n          padding\n        }\n      }\n    } = this;\n    const hitboxes = this.legendHitBoxes = [];\n    const lineWidths = this.lineWidths = [0];\n    const lineHeight = itemHeight + padding;\n    let totalHeight = titleHeight;\n    ctx.textAlign = 'left';\n    ctx.textBaseline = 'middle';\n    let row = -1;\n    let top = -lineHeight;\n    this.legendItems.forEach((legendItem, i) => {\n      const itemWidth = boxWidth + fontSize / 2 + ctx.measureText(legendItem.text).width;\n      if (i === 0 || lineWidths[lineWidths.length - 1] + itemWidth + 2 * padding > maxWidth) {\n        totalHeight += lineHeight;\n        lineWidths[lineWidths.length - (i > 0 ? 0 : 1)] = 0;\n        top += lineHeight;\n        row++;\n      }\n      hitboxes[i] = {\n        left: 0,\n        top,\n        row,\n        width: itemWidth,\n        height: itemHeight\n      };\n      lineWidths[lineWidths.length - 1] += itemWidth + padding;\n    });\n    return totalHeight;\n  }\n  _fitCols(titleHeight, labelFont, boxWidth, _itemHeight) {\n    const {\n      ctx,\n      maxHeight,\n      options: {\n        labels: {\n          padding\n        }\n      }\n    } = this;\n    const hitboxes = this.legendHitBoxes = [];\n    const columnSizes = this.columnSizes = [];\n    const heightLimit = maxHeight - titleHeight;\n    let totalWidth = padding;\n    let currentColWidth = 0;\n    let currentColHeight = 0;\n    let left = 0;\n    let col = 0;\n    this.legendItems.forEach((legendItem, i) => {\n      const {\n        itemWidth,\n        itemHeight\n      } = calculateItemSize(boxWidth, labelFont, ctx, legendItem, _itemHeight);\n      if (i > 0 && currentColHeight + itemHeight + 2 * padding > heightLimit) {\n        totalWidth += currentColWidth + padding;\n        columnSizes.push({\n          width: currentColWidth,\n          height: currentColHeight\n        });\n        left += currentColWidth + padding;\n        col++;\n        currentColWidth = currentColHeight = 0;\n      }\n      hitboxes[i] = {\n        left,\n        top: currentColHeight,\n        col,\n        width: itemWidth,\n        height: itemHeight\n      };\n      currentColWidth = Math.max(currentColWidth, itemWidth);\n      currentColHeight += itemHeight + padding;\n    });\n    totalWidth += currentColWidth;\n    columnSizes.push({\n      width: currentColWidth,\n      height: currentColHeight\n    });\n    return totalWidth;\n  }\n  adjustHitBoxes() {\n    if (!this.options.display) {\n      return;\n    }\n    const titleHeight = this._computeTitleHeight();\n    const {\n      legendHitBoxes: hitboxes,\n      options: {\n        align,\n        labels: {\n          padding\n        },\n        rtl\n      }\n    } = this;\n    const rtlHelper = getRtlAdapter(rtl, this.left, this.width);\n    if (this.isHorizontal()) {\n      let row = 0;\n      let left = _alignStartEnd(align, this.left + padding, this.right - this.lineWidths[row]);\n      for (const hitbox of hitboxes) {\n        if (row !== hitbox.row) {\n          row = hitbox.row;\n          left = _alignStartEnd(align, this.left + padding, this.right - this.lineWidths[row]);\n        }\n        hitbox.top += this.top + titleHeight + padding;\n        hitbox.left = rtlHelper.leftForLtr(rtlHelper.x(left), hitbox.width);\n        left += hitbox.width + padding;\n      }\n    } else {\n      let col = 0;\n      let top = _alignStartEnd(align, this.top + titleHeight + padding, this.bottom - this.columnSizes[col].height);\n      for (const hitbox of hitboxes) {\n        if (hitbox.col !== col) {\n          col = hitbox.col;\n          top = _alignStartEnd(align, this.top + titleHeight + padding, this.bottom - this.columnSizes[col].height);\n        }\n        hitbox.top = top;\n        hitbox.left += this.left + padding;\n        hitbox.left = rtlHelper.leftForLtr(rtlHelper.x(hitbox.left), hitbox.width);\n        top += hitbox.height + padding;\n      }\n    }\n  }\n  isHorizontal() {\n    return this.options.position === 'top' || this.options.position === 'bottom';\n  }\n  draw() {\n    if (this.options.display) {\n      const ctx = this.ctx;\n      clipArea(ctx, this);\n      this._draw();\n      unclipArea(ctx);\n    }\n  }\n  _draw() {\n    const {\n      options: opts,\n      columnSizes,\n      lineWidths,\n      ctx\n    } = this;\n    const {\n      align,\n      labels: labelOpts\n    } = opts;\n    const defaultColor = defaults.color;\n    const rtlHelper = getRtlAdapter(opts.rtl, this.left, this.width);\n    const labelFont = toFont(labelOpts.font);\n    const {\n      padding\n    } = labelOpts;\n    const fontSize = labelFont.size;\n    const halfFontSize = fontSize / 2;\n    let cursor;\n    this.drawTitle();\n    ctx.textAlign = rtlHelper.textAlign('left');\n    ctx.textBaseline = 'middle';\n    ctx.lineWidth = 0.5;\n    ctx.font = labelFont.string;\n    const {\n      boxWidth,\n      boxHeight,\n      itemHeight\n    } = getBoxSize(labelOpts, fontSize);\n    const drawLegendBox = function (x, y, legendItem) {\n      if (isNaN(boxWidth) || boxWidth <= 0 || isNaN(boxHeight) || boxHeight < 0) {\n        return;\n      }\n      ctx.save();\n      const lineWidth = valueOrDefault(legendItem.lineWidth, 1);\n      ctx.fillStyle = valueOrDefault(legendItem.fillStyle, defaultColor);\n      ctx.lineCap = valueOrDefault(legendItem.lineCap, 'butt');\n      ctx.lineDashOffset = valueOrDefault(legendItem.lineDashOffset, 0);\n      ctx.lineJoin = valueOrDefault(legendItem.lineJoin, 'miter');\n      ctx.lineWidth = lineWidth;\n      ctx.strokeStyle = valueOrDefault(legendItem.strokeStyle, defaultColor);\n      ctx.setLineDash(valueOrDefault(legendItem.lineDash, []));\n      if (labelOpts.usePointStyle) {\n        const drawOptions = {\n          radius: boxHeight * Math.SQRT2 / 2,\n          pointStyle: legendItem.pointStyle,\n          rotation: legendItem.rotation,\n          borderWidth: lineWidth\n        };\n        const centerX = rtlHelper.xPlus(x, boxWidth / 2);\n        const centerY = y + halfFontSize;\n        drawPointLegend(ctx, drawOptions, centerX, centerY, labelOpts.pointStyleWidth && boxWidth);\n      } else {\n        const yBoxTop = y + Math.max((fontSize - boxHeight) / 2, 0);\n        const xBoxLeft = rtlHelper.leftForLtr(x, boxWidth);\n        const borderRadius = toTRBLCorners(legendItem.borderRadius);\n        ctx.beginPath();\n        if (Object.values(borderRadius).some(v => v !== 0)) {\n          addRoundedRectPath(ctx, {\n            x: xBoxLeft,\n            y: yBoxTop,\n            w: boxWidth,\n            h: boxHeight,\n            radius: borderRadius\n          });\n        } else {\n          ctx.rect(xBoxLeft, yBoxTop, boxWidth, boxHeight);\n        }\n        ctx.fill();\n        if (lineWidth !== 0) {\n          ctx.stroke();\n        }\n      }\n      ctx.restore();\n    };\n    const fillText = function (x, y, legendItem) {\n      renderText(ctx, legendItem.text, x, y + itemHeight / 2, labelFont, {\n        strikethrough: legendItem.hidden,\n        textAlign: rtlHelper.textAlign(legendItem.textAlign)\n      });\n    };\n    const isHorizontal = this.isHorizontal();\n    const titleHeight = this._computeTitleHeight();\n    if (isHorizontal) {\n      cursor = {\n        x: _alignStartEnd(align, this.left + padding, this.right - lineWidths[0]),\n        y: this.top + padding + titleHeight,\n        line: 0\n      };\n    } else {\n      cursor = {\n        x: this.left + padding,\n        y: _alignStartEnd(align, this.top + titleHeight + padding, this.bottom - columnSizes[0].height),\n        line: 0\n      };\n    }\n    overrideTextDirection(this.ctx, opts.textDirection);\n    const lineHeight = itemHeight + padding;\n    this.legendItems.forEach((legendItem, i) => {\n      ctx.strokeStyle = legendItem.fontColor;\n      ctx.fillStyle = legendItem.fontColor;\n      const textWidth = ctx.measureText(legendItem.text).width;\n      const textAlign = rtlHelper.textAlign(legendItem.textAlign || (legendItem.textAlign = labelOpts.textAlign));\n      const width = boxWidth + halfFontSize + textWidth;\n      let x = cursor.x;\n      let y = cursor.y;\n      rtlHelper.setWidth(this.width);\n      if (isHorizontal) {\n        if (i > 0 && x + width + padding > this.right) {\n          y = cursor.y += lineHeight;\n          cursor.line++;\n          x = cursor.x = _alignStartEnd(align, this.left + padding, this.right - lineWidths[cursor.line]);\n        }\n      } else if (i > 0 && y + lineHeight > this.bottom) {\n        x = cursor.x = x + columnSizes[cursor.line].width + padding;\n        cursor.line++;\n        y = cursor.y = _alignStartEnd(align, this.top + titleHeight + padding, this.bottom - columnSizes[cursor.line].height);\n      }\n      const realX = rtlHelper.x(x);\n      drawLegendBox(realX, y, legendItem);\n      x = _textX(textAlign, x + boxWidth + halfFontSize, isHorizontal ? x + width : this.right, opts.rtl);\n      fillText(rtlHelper.x(x), y, legendItem);\n      if (isHorizontal) {\n        cursor.x += width + padding;\n      } else if (typeof legendItem.text !== 'string') {\n        const fontLineHeight = labelFont.lineHeight;\n        cursor.y += calculateLegendItemHeight(legendItem, fontLineHeight) + padding;\n      } else {\n        cursor.y += lineHeight;\n      }\n    });\n    restoreTextDirection(this.ctx, opts.textDirection);\n  }\n  drawTitle() {\n    const opts = this.options;\n    const titleOpts = opts.title;\n    const titleFont = toFont(titleOpts.font);\n    const titlePadding = toPadding(titleOpts.padding);\n    if (!titleOpts.display) {\n      return;\n    }\n    const rtlHelper = getRtlAdapter(opts.rtl, this.left, this.width);\n    const ctx = this.ctx;\n    const position = titleOpts.position;\n    const halfFontSize = titleFont.size / 2;\n    const topPaddingPlusHalfFontSize = titlePadding.top + halfFontSize;\n    let y;\n    let left = this.left;\n    let maxWidth = this.width;\n    if (this.isHorizontal()) {\n      maxWidth = Math.max(...this.lineWidths);\n      y = this.top + topPaddingPlusHalfFontSize;\n      left = _alignStartEnd(opts.align, left, this.right - maxWidth);\n    } else {\n      const maxHeight = this.columnSizes.reduce((acc, size) => Math.max(acc, size.height), 0);\n      y = topPaddingPlusHalfFontSize + _alignStartEnd(opts.align, this.top, this.bottom - maxHeight - opts.labels.padding - this._computeTitleHeight());\n    }\n    const x = _alignStartEnd(position, left, left + maxWidth);\n    ctx.textAlign = rtlHelper.textAlign(_toLeftRightCenter(position));\n    ctx.textBaseline = 'middle';\n    ctx.strokeStyle = titleOpts.color;\n    ctx.fillStyle = titleOpts.color;\n    ctx.font = titleFont.string;\n    renderText(ctx, titleOpts.text, x, y, titleFont);\n  }\n  _computeTitleHeight() {\n    const titleOpts = this.options.title;\n    const titleFont = toFont(titleOpts.font);\n    const titlePadding = toPadding(titleOpts.padding);\n    return titleOpts.display ? titleFont.lineHeight + titlePadding.height : 0;\n  }\n  _getLegendItemAt(x, y) {\n    let i, hitBox, lh;\n    if (_isBetween(x, this.left, this.right) && _isBetween(y, this.top, this.bottom)) {\n      lh = this.legendHitBoxes;\n      for (i = 0; i < lh.length; ++i) {\n        hitBox = lh[i];\n        if (_isBetween(x, hitBox.left, hitBox.left + hitBox.width) && _isBetween(y, hitBox.top, hitBox.top + hitBox.height)) {\n          return this.legendItems[i];\n        }\n      }\n    }\n    return null;\n  }\n  handleEvent(e) {\n    const opts = this.options;\n    if (!isListened(e.type, opts)) {\n      return;\n    }\n    const hoveredItem = this._getLegendItemAt(e.x, e.y);\n    if (e.type === 'mousemove' || e.type === 'mouseout') {\n      const previous = this._hoveredItem;\n      const sameItem = itemsEqual(previous, hoveredItem);\n      if (previous && !sameItem) {\n        callback(opts.onLeave, [e, previous, this], this);\n      }\n      this._hoveredItem = hoveredItem;\n      if (hoveredItem && !sameItem) {\n        callback(opts.onHover, [e, hoveredItem, this], this);\n      }\n    } else if (hoveredItem) {\n      callback(opts.onClick, [e, hoveredItem, this], this);\n    }\n  }\n}\nfunction calculateItemSize(boxWidth, labelFont, ctx, legendItem, _itemHeight) {\n  const itemWidth = calculateItemWidth(legendItem, boxWidth, labelFont, ctx);\n  const itemHeight = calculateItemHeight(_itemHeight, legendItem, labelFont.lineHeight);\n  return {\n    itemWidth,\n    itemHeight\n  };\n}\nfunction calculateItemWidth(legendItem, boxWidth, labelFont, ctx) {\n  let legendItemText = legendItem.text;\n  if (legendItemText && typeof legendItemText !== 'string') {\n    legendItemText = legendItemText.reduce((a, b) => a.length > b.length ? a : b);\n  }\n  return boxWidth + labelFont.size / 2 + ctx.measureText(legendItemText).width;\n}\nfunction calculateItemHeight(_itemHeight, legendItem, fontLineHeight) {\n  let itemHeight = _itemHeight;\n  if (typeof legendItem.text !== 'string') {\n    itemHeight = calculateLegendItemHeight(legendItem, fontLineHeight);\n  }\n  return itemHeight;\n}\nfunction calculateLegendItemHeight(legendItem, fontLineHeight) {\n  const labelHeight = legendItem.text ? legendItem.text.length : 0;\n  return fontLineHeight * labelHeight;\n}\nfunction isListened(type, opts) {\n  if ((type === 'mousemove' || type === 'mouseout') && (opts.onHover || opts.onLeave)) {\n    return true;\n  }\n  if (opts.onClick && (type === 'click' || type === 'mouseup')) {\n    return true;\n  }\n  return false;\n}\nvar plugin_legend = {\n  id: 'legend',\n  _element: Legend,\n  start(chart, _args, options) {\n    const legend = chart.legend = new Legend({\n      ctx: chart.ctx,\n      options,\n      chart\n    });\n    layouts.configure(chart, legend, options);\n    layouts.addBox(chart, legend);\n  },\n  stop(chart) {\n    layouts.removeBox(chart, chart.legend);\n    delete chart.legend;\n  },\n  beforeUpdate(chart, _args, options) {\n    const legend = chart.legend;\n    layouts.configure(chart, legend, options);\n    legend.options = options;\n  },\n  afterUpdate(chart) {\n    const legend = chart.legend;\n    legend.buildLabels();\n    legend.adjustHitBoxes();\n  },\n  afterEvent(chart, args) {\n    if (!args.replay) {\n      chart.legend.handleEvent(args.event);\n    }\n  },\n  defaults: {\n    display: true,\n    position: 'top',\n    align: 'center',\n    fullSize: true,\n    reverse: false,\n    weight: 1000,\n    onClick(e, legendItem, legend) {\n      const index = legendItem.datasetIndex;\n      const ci = legend.chart;\n      if (ci.isDatasetVisible(index)) {\n        ci.hide(index);\n        legendItem.hidden = true;\n      } else {\n        ci.show(index);\n        legendItem.hidden = false;\n      }\n    },\n    onHover: null,\n    onLeave: null,\n    labels: {\n      color: ctx => ctx.chart.options.color,\n      boxWidth: 40,\n      padding: 10,\n      generateLabels(chart) {\n        const datasets = chart.data.datasets;\n        const {\n          labels: {\n            usePointStyle,\n            pointStyle,\n            textAlign,\n            color,\n            useBorderRadius,\n            borderRadius\n          }\n        } = chart.legend.options;\n        return chart._getSortedDatasetMetas().map(meta => {\n          const style = meta.controller.getStyle(usePointStyle ? 0 : undefined);\n          const borderWidth = toPadding(style.borderWidth);\n          return {\n            text: datasets[meta.index].label,\n            fillStyle: style.backgroundColor,\n            fontColor: color,\n            hidden: !meta.visible,\n            lineCap: style.borderCapStyle,\n            lineDash: style.borderDash,\n            lineDashOffset: style.borderDashOffset,\n            lineJoin: style.borderJoinStyle,\n            lineWidth: (borderWidth.width + borderWidth.height) / 4,\n            strokeStyle: style.borderColor,\n            pointStyle: pointStyle || style.pointStyle,\n            rotation: style.rotation,\n            textAlign: textAlign || style.textAlign,\n            borderRadius: useBorderRadius && (borderRadius || style.borderRadius),\n            datasetIndex: meta.index\n          };\n        }, this);\n      }\n    },\n    title: {\n      color: ctx => ctx.chart.options.color,\n      display: false,\n      position: 'center',\n      text: ''\n    }\n  },\n  descriptors: {\n    _scriptable: name => !name.startsWith('on'),\n    labels: {\n      _scriptable: name => !['generateLabels', 'filter', 'sort'].includes(name)\n    }\n  }\n};\nclass Title extends Element {\n  constructor(config) {\n    super();\n    this.chart = config.chart;\n    this.options = config.options;\n    this.ctx = config.ctx;\n    this._padding = undefined;\n    this.top = undefined;\n    this.bottom = undefined;\n    this.left = undefined;\n    this.right = undefined;\n    this.width = undefined;\n    this.height = undefined;\n    this.position = undefined;\n    this.weight = undefined;\n    this.fullSize = undefined;\n  }\n  update(maxWidth, maxHeight) {\n    const opts = this.options;\n    this.left = 0;\n    this.top = 0;\n    if (!opts.display) {\n      this.width = this.height = this.right = this.bottom = 0;\n      return;\n    }\n    this.width = this.right = maxWidth;\n    this.height = this.bottom = maxHeight;\n    const lineCount = isArray(opts.text) ? opts.text.length : 1;\n    this._padding = toPadding(opts.padding);\n    const textSize = lineCount * toFont(opts.font).lineHeight + this._padding.height;\n    if (this.isHorizontal()) {\n      this.height = textSize;\n    } else {\n      this.width = textSize;\n    }\n  }\n  isHorizontal() {\n    const pos = this.options.position;\n    return pos === 'top' || pos === 'bottom';\n  }\n  _drawArgs(offset) {\n    const {\n      top,\n      left,\n      bottom,\n      right,\n      options\n    } = this;\n    const align = options.align;\n    let rotation = 0;\n    let maxWidth, titleX, titleY;\n    if (this.isHorizontal()) {\n      titleX = _alignStartEnd(align, left, right);\n      titleY = top + offset;\n      maxWidth = right - left;\n    } else {\n      if (options.position === 'left') {\n        titleX = left + offset;\n        titleY = _alignStartEnd(align, bottom, top);\n        rotation = PI * -0.5;\n      } else {\n        titleX = right - offset;\n        titleY = _alignStartEnd(align, top, bottom);\n        rotation = PI * 0.5;\n      }\n      maxWidth = bottom - top;\n    }\n    return {\n      titleX,\n      titleY,\n      maxWidth,\n      rotation\n    };\n  }\n  draw() {\n    const ctx = this.ctx;\n    const opts = this.options;\n    if (!opts.display) {\n      return;\n    }\n    const fontOpts = toFont(opts.font);\n    const lineHeight = fontOpts.lineHeight;\n    const offset = lineHeight / 2 + this._padding.top;\n    const {\n      titleX,\n      titleY,\n      maxWidth,\n      rotation\n    } = this._drawArgs(offset);\n    renderText(ctx, opts.text, 0, 0, fontOpts, {\n      color: opts.color,\n      maxWidth,\n      rotation,\n      textAlign: _toLeftRightCenter(opts.align),\n      textBaseline: 'middle',\n      translation: [titleX, titleY]\n    });\n  }\n}\nfunction createTitle(chart, titleOpts) {\n  const title = new Title({\n    ctx: chart.ctx,\n    options: titleOpts,\n    chart\n  });\n  layouts.configure(chart, title, titleOpts);\n  layouts.addBox(chart, title);\n  chart.titleBlock = title;\n}\nvar plugin_title = {\n  id: 'title',\n  _element: Title,\n  start(chart, _args, options) {\n    createTitle(chart, options);\n  },\n  stop(chart) {\n    const titleBlock = chart.titleBlock;\n    layouts.removeBox(chart, titleBlock);\n    delete chart.titleBlock;\n  },\n  beforeUpdate(chart, _args, options) {\n    const title = chart.titleBlock;\n    layouts.configure(chart, title, options);\n    title.options = options;\n  },\n  defaults: {\n    align: 'center',\n    display: false,\n    font: {\n      weight: 'bold'\n    },\n    fullSize: true,\n    padding: 10,\n    position: 'top',\n    text: '',\n    weight: 2000\n  },\n  defaultRoutes: {\n    color: 'color'\n  },\n  descriptors: {\n    _scriptable: true,\n    _indexable: false\n  }\n};\nconst map = new WeakMap();\nvar plugin_subtitle = {\n  id: 'subtitle',\n  start(chart, _args, options) {\n    const title = new Title({\n      ctx: chart.ctx,\n      options,\n      chart\n    });\n    layouts.configure(chart, title, options);\n    layouts.addBox(chart, title);\n    map.set(chart, title);\n  },\n  stop(chart) {\n    layouts.removeBox(chart, map.get(chart));\n    map.delete(chart);\n  },\n  beforeUpdate(chart, _args, options) {\n    const title = map.get(chart);\n    layouts.configure(chart, title, options);\n    title.options = options;\n  },\n  defaults: {\n    align: 'center',\n    display: false,\n    font: {\n      weight: 'normal'\n    },\n    fullSize: true,\n    padding: 0,\n    position: 'top',\n    text: '',\n    weight: 1500\n  },\n  defaultRoutes: {\n    color: 'color'\n  },\n  descriptors: {\n    _scriptable: true,\n    _indexable: false\n  }\n};\nconst positioners = {\n  average(items) {\n    if (!items.length) {\n      return false;\n    }\n    let i, len;\n    let xSet = new Set();\n    let y = 0;\n    let count = 0;\n    for (i = 0, len = items.length; i < len; ++i) {\n      const el = items[i].element;\n      if (el && el.hasValue()) {\n        const pos = el.tooltipPosition();\n        xSet.add(pos.x);\n        y += pos.y;\n        ++count;\n      }\n    }\n    if (count === 0 || xSet.size === 0) {\n      return false;\n    }\n    const xAverage = [...xSet].reduce((a, b) => a + b) / xSet.size;\n    return {\n      x: xAverage,\n      y: y / count\n    };\n  },\n  nearest(items, eventPosition) {\n    if (!items.length) {\n      return false;\n    }\n    let x = eventPosition.x;\n    let y = eventPosition.y;\n    let minDistance = Number.POSITIVE_INFINITY;\n    let i, len, nearestElement;\n    for (i = 0, len = items.length; i < len; ++i) {\n      const el = items[i].element;\n      if (el && el.hasValue()) {\n        const center = el.getCenterPoint();\n        const d = distanceBetweenPoints(eventPosition, center);\n        if (d < minDistance) {\n          minDistance = d;\n          nearestElement = el;\n        }\n      }\n    }\n    if (nearestElement) {\n      const tp = nearestElement.tooltipPosition();\n      x = tp.x;\n      y = tp.y;\n    }\n    return {\n      x,\n      y\n    };\n  }\n};\nfunction pushOrConcat(base, toPush) {\n  if (toPush) {\n    if (isArray(toPush)) {\n      Array.prototype.push.apply(base, toPush);\n    } else {\n      base.push(toPush);\n    }\n  }\n  return base;\n}\nfunction splitNewlines(str) {\n  if ((typeof str === 'string' || str instanceof String) && str.indexOf('\\n') > -1) {\n    return str.split('\\n');\n  }\n  return str;\n}\nfunction createTooltipItem(chart, item) {\n  const {\n    element,\n    datasetIndex,\n    index\n  } = item;\n  const controller = chart.getDatasetMeta(datasetIndex).controller;\n  const {\n    label,\n    value\n  } = controller.getLabelAndValue(index);\n  return {\n    chart,\n    label,\n    parsed: controller.getParsed(index),\n    raw: chart.data.datasets[datasetIndex].data[index],\n    formattedValue: value,\n    dataset: controller.getDataset(),\n    dataIndex: index,\n    datasetIndex,\n    element\n  };\n}\nfunction getTooltipSize(tooltip, options) {\n  const ctx = tooltip.chart.ctx;\n  const {\n    body,\n    footer,\n    title\n  } = tooltip;\n  const {\n    boxWidth,\n    boxHeight\n  } = options;\n  const bodyFont = toFont(options.bodyFont);\n  const titleFont = toFont(options.titleFont);\n  const footerFont = toFont(options.footerFont);\n  const titleLineCount = title.length;\n  const footerLineCount = footer.length;\n  const bodyLineItemCount = body.length;\n  const padding = toPadding(options.padding);\n  let height = padding.height;\n  let width = 0;\n  let combinedBodyLength = body.reduce((count, bodyItem) => count + bodyItem.before.length + bodyItem.lines.length + bodyItem.after.length, 0);\n  combinedBodyLength += tooltip.beforeBody.length + tooltip.afterBody.length;\n  if (titleLineCount) {\n    height += titleLineCount * titleFont.lineHeight + (titleLineCount - 1) * options.titleSpacing + options.titleMarginBottom;\n  }\n  if (combinedBodyLength) {\n    const bodyLineHeight = options.displayColors ? Math.max(boxHeight, bodyFont.lineHeight) : bodyFont.lineHeight;\n    height += bodyLineItemCount * bodyLineHeight + (combinedBodyLength - bodyLineItemCount) * bodyFont.lineHeight + (combinedBodyLength - 1) * options.bodySpacing;\n  }\n  if (footerLineCount) {\n    height += options.footerMarginTop + footerLineCount * footerFont.lineHeight + (footerLineCount - 1) * options.footerSpacing;\n  }\n  let widthPadding = 0;\n  const maxLineWidth = function (line) {\n    width = Math.max(width, ctx.measureText(line).width + widthPadding);\n  };\n  ctx.save();\n  ctx.font = titleFont.string;\n  each(tooltip.title, maxLineWidth);\n  ctx.font = bodyFont.string;\n  each(tooltip.beforeBody.concat(tooltip.afterBody), maxLineWidth);\n  widthPadding = options.displayColors ? boxWidth + 2 + options.boxPadding : 0;\n  each(body, bodyItem => {\n    each(bodyItem.before, maxLineWidth);\n    each(bodyItem.lines, maxLineWidth);\n    each(bodyItem.after, maxLineWidth);\n  });\n  widthPadding = 0;\n  ctx.font = footerFont.string;\n  each(tooltip.footer, maxLineWidth);\n  ctx.restore();\n  width += padding.width;\n  return {\n    width,\n    height\n  };\n}\nfunction determineYAlign(chart, size) {\n  const {\n    y,\n    height\n  } = size;\n  if (y < height / 2) {\n    return 'top';\n  } else if (y > chart.height - height / 2) {\n    return 'bottom';\n  }\n  return 'center';\n}\nfunction doesNotFitWithAlign(xAlign, chart, options, size) {\n  const {\n    x,\n    width\n  } = size;\n  const caret = options.caretSize + options.caretPadding;\n  if (xAlign === 'left' && x + width + caret > chart.width) {\n    return true;\n  }\n  if (xAlign === 'right' && x - width - caret < 0) {\n    return true;\n  }\n}\nfunction determineXAlign(chart, options, size, yAlign) {\n  const {\n    x,\n    width\n  } = size;\n  const {\n    width: chartWidth,\n    chartArea: {\n      left,\n      right\n    }\n  } = chart;\n  let xAlign = 'center';\n  if (yAlign === 'center') {\n    xAlign = x <= (left + right) / 2 ? 'left' : 'right';\n  } else if (x <= width / 2) {\n    xAlign = 'left';\n  } else if (x >= chartWidth - width / 2) {\n    xAlign = 'right';\n  }\n  if (doesNotFitWithAlign(xAlign, chart, options, size)) {\n    xAlign = 'center';\n  }\n  return xAlign;\n}\nfunction determineAlignment(chart, options, size) {\n  const yAlign = size.yAlign || options.yAlign || determineYAlign(chart, size);\n  return {\n    xAlign: size.xAlign || options.xAlign || determineXAlign(chart, options, size, yAlign),\n    yAlign\n  };\n}\nfunction alignX(size, xAlign) {\n  let {\n    x,\n    width\n  } = size;\n  if (xAlign === 'right') {\n    x -= width;\n  } else if (xAlign === 'center') {\n    x -= width / 2;\n  }\n  return x;\n}\nfunction alignY(size, yAlign, paddingAndSize) {\n  let {\n    y,\n    height\n  } = size;\n  if (yAlign === 'top') {\n    y += paddingAndSize;\n  } else if (yAlign === 'bottom') {\n    y -= height + paddingAndSize;\n  } else {\n    y -= height / 2;\n  }\n  return y;\n}\nfunction getBackgroundPoint(options, size, alignment, chart) {\n  const {\n    caretSize,\n    caretPadding,\n    cornerRadius\n  } = options;\n  const {\n    xAlign,\n    yAlign\n  } = alignment;\n  const paddingAndSize = caretSize + caretPadding;\n  const {\n    topLeft,\n    topRight,\n    bottomLeft,\n    bottomRight\n  } = toTRBLCorners(cornerRadius);\n  let x = alignX(size, xAlign);\n  const y = alignY(size, yAlign, paddingAndSize);\n  if (yAlign === 'center') {\n    if (xAlign === 'left') {\n      x += paddingAndSize;\n    } else if (xAlign === 'right') {\n      x -= paddingAndSize;\n    }\n  } else if (xAlign === 'left') {\n    x -= Math.max(topLeft, bottomLeft) + caretSize;\n  } else if (xAlign === 'right') {\n    x += Math.max(topRight, bottomRight) + caretSize;\n  }\n  return {\n    x: _limitValue(x, 0, chart.width - size.width),\n    y: _limitValue(y, 0, chart.height - size.height)\n  };\n}\nfunction getAlignedX(tooltip, align, options) {\n  const padding = toPadding(options.padding);\n  return align === 'center' ? tooltip.x + tooltip.width / 2 : align === 'right' ? tooltip.x + tooltip.width - padding.right : tooltip.x + padding.left;\n}\nfunction getBeforeAfterBodyLines(callback) {\n  return pushOrConcat([], splitNewlines(callback));\n}\nfunction createTooltipContext(parent, tooltip, tooltipItems) {\n  return createContext(parent, {\n    tooltip,\n    tooltipItems,\n    type: 'tooltip'\n  });\n}\nfunction overrideCallbacks(callbacks, context) {\n  const override = context && context.dataset && context.dataset.tooltip && context.dataset.tooltip.callbacks;\n  return override ? callbacks.override(override) : callbacks;\n}\nconst defaultCallbacks = {\n  beforeTitle: noop,\n  title(tooltipItems) {\n    if (tooltipItems.length > 0) {\n      const item = tooltipItems[0];\n      const labels = item.chart.data.labels;\n      const labelCount = labels ? labels.length : 0;\n      if (this && this.options && this.options.mode === 'dataset') {\n        return item.dataset.label || '';\n      } else if (item.label) {\n        return item.label;\n      } else if (labelCount > 0 && item.dataIndex < labelCount) {\n        return labels[item.dataIndex];\n      }\n    }\n    return '';\n  },\n  afterTitle: noop,\n  beforeBody: noop,\n  beforeLabel: noop,\n  label(tooltipItem) {\n    if (this && this.options && this.options.mode === 'dataset') {\n      return tooltipItem.label + ': ' + tooltipItem.formattedValue || tooltipItem.formattedValue;\n    }\n    let label = tooltipItem.dataset.label || '';\n    if (label) {\n      label += ': ';\n    }\n    const value = tooltipItem.formattedValue;\n    if (!isNullOrUndef(value)) {\n      label += value;\n    }\n    return label;\n  },\n  labelColor(tooltipItem) {\n    const meta = tooltipItem.chart.getDatasetMeta(tooltipItem.datasetIndex);\n    const options = meta.controller.getStyle(tooltipItem.dataIndex);\n    return {\n      borderColor: options.borderColor,\n      backgroundColor: options.backgroundColor,\n      borderWidth: options.borderWidth,\n      borderDash: options.borderDash,\n      borderDashOffset: options.borderDashOffset,\n      borderRadius: 0\n    };\n  },\n  labelTextColor() {\n    return this.options.bodyColor;\n  },\n  labelPointStyle(tooltipItem) {\n    const meta = tooltipItem.chart.getDatasetMeta(tooltipItem.datasetIndex);\n    const options = meta.controller.getStyle(tooltipItem.dataIndex);\n    return {\n      pointStyle: options.pointStyle,\n      rotation: options.rotation\n    };\n  },\n  afterLabel: noop,\n  afterBody: noop,\n  beforeFooter: noop,\n  footer: noop,\n  afterFooter: noop\n};\nfunction invokeCallbackWithFallback(callbacks, name, ctx, arg) {\n  const result = callbacks[name].call(ctx, arg);\n  if (typeof result === 'undefined') {\n    return defaultCallbacks[name].call(ctx, arg);\n  }\n  return result;\n}\nlet Tooltip = /*#__PURE__*/(() => {\n  class Tooltip extends Element {\n    static positioners = positioners;\n    constructor(config) {\n      super();\n      this.opacity = 0;\n      this._active = [];\n      this._eventPosition = undefined;\n      this._size = undefined;\n      this._cachedAnimations = undefined;\n      this._tooltipItems = [];\n      this.$animations = undefined;\n      this.$context = undefined;\n      this.chart = config.chart;\n      this.options = config.options;\n      this.dataPoints = undefined;\n      this.title = undefined;\n      this.beforeBody = undefined;\n      this.body = undefined;\n      this.afterBody = undefined;\n      this.footer = undefined;\n      this.xAlign = undefined;\n      this.yAlign = undefined;\n      this.x = undefined;\n      this.y = undefined;\n      this.height = undefined;\n      this.width = undefined;\n      this.caretX = undefined;\n      this.caretY = undefined;\n      this.labelColors = undefined;\n      this.labelPointStyles = undefined;\n      this.labelTextColors = undefined;\n    }\n    initialize(options) {\n      this.options = options;\n      this._cachedAnimations = undefined;\n      this.$context = undefined;\n    }\n    _resolveAnimations() {\n      const cached = this._cachedAnimations;\n      if (cached) {\n        return cached;\n      }\n      const chart = this.chart;\n      const options = this.options.setContext(this.getContext());\n      const opts = options.enabled && chart.options.animation && options.animations;\n      const animations = new Animations(this.chart, opts);\n      if (opts._cacheable) {\n        this._cachedAnimations = Object.freeze(animations);\n      }\n      return animations;\n    }\n    getContext() {\n      return this.$context || (this.$context = createTooltipContext(this.chart.getContext(), this, this._tooltipItems));\n    }\n    getTitle(context, options) {\n      const {\n        callbacks\n      } = options;\n      const beforeTitle = invokeCallbackWithFallback(callbacks, 'beforeTitle', this, context);\n      const title = invokeCallbackWithFallback(callbacks, 'title', this, context);\n      const afterTitle = invokeCallbackWithFallback(callbacks, 'afterTitle', this, context);\n      let lines = [];\n      lines = pushOrConcat(lines, splitNewlines(beforeTitle));\n      lines = pushOrConcat(lines, splitNewlines(title));\n      lines = pushOrConcat(lines, splitNewlines(afterTitle));\n      return lines;\n    }\n    getBeforeBody(tooltipItems, options) {\n      return getBeforeAfterBodyLines(invokeCallbackWithFallback(options.callbacks, 'beforeBody', this, tooltipItems));\n    }\n    getBody(tooltipItems, options) {\n      const {\n        callbacks\n      } = options;\n      const bodyItems = [];\n      each(tooltipItems, context => {\n        const bodyItem = {\n          before: [],\n          lines: [],\n          after: []\n        };\n        const scoped = overrideCallbacks(callbacks, context);\n        pushOrConcat(bodyItem.before, splitNewlines(invokeCallbackWithFallback(scoped, 'beforeLabel', this, context)));\n        pushOrConcat(bodyItem.lines, invokeCallbackWithFallback(scoped, 'label', this, context));\n        pushOrConcat(bodyItem.after, splitNewlines(invokeCallbackWithFallback(scoped, 'afterLabel', this, context)));\n        bodyItems.push(bodyItem);\n      });\n      return bodyItems;\n    }\n    getAfterBody(tooltipItems, options) {\n      return getBeforeAfterBodyLines(invokeCallbackWithFallback(options.callbacks, 'afterBody', this, tooltipItems));\n    }\n    getFooter(tooltipItems, options) {\n      const {\n        callbacks\n      } = options;\n      const beforeFooter = invokeCallbackWithFallback(callbacks, 'beforeFooter', this, tooltipItems);\n      const footer = invokeCallbackWithFallback(callbacks, 'footer', this, tooltipItems);\n      const afterFooter = invokeCallbackWithFallback(callbacks, 'afterFooter', this, tooltipItems);\n      let lines = [];\n      lines = pushOrConcat(lines, splitNewlines(beforeFooter));\n      lines = pushOrConcat(lines, splitNewlines(footer));\n      lines = pushOrConcat(lines, splitNewlines(afterFooter));\n      return lines;\n    }\n    _createItems(options) {\n      const active = this._active;\n      const data = this.chart.data;\n      const labelColors = [];\n      const labelPointStyles = [];\n      const labelTextColors = [];\n      let tooltipItems = [];\n      let i, len;\n      for (i = 0, len = active.length; i < len; ++i) {\n        tooltipItems.push(createTooltipItem(this.chart, active[i]));\n      }\n      if (options.filter) {\n        tooltipItems = tooltipItems.filter((element, index, array) => options.filter(element, index, array, data));\n      }\n      if (options.itemSort) {\n        tooltipItems = tooltipItems.sort((a, b) => options.itemSort(a, b, data));\n      }\n      each(tooltipItems, context => {\n        const scoped = overrideCallbacks(options.callbacks, context);\n        labelColors.push(invokeCallbackWithFallback(scoped, 'labelColor', this, context));\n        labelPointStyles.push(invokeCallbackWithFallback(scoped, 'labelPointStyle', this, context));\n        labelTextColors.push(invokeCallbackWithFallback(scoped, 'labelTextColor', this, context));\n      });\n      this.labelColors = labelColors;\n      this.labelPointStyles = labelPointStyles;\n      this.labelTextColors = labelTextColors;\n      this.dataPoints = tooltipItems;\n      return tooltipItems;\n    }\n    update(changed, replay) {\n      const options = this.options.setContext(this.getContext());\n      const active = this._active;\n      let properties;\n      let tooltipItems = [];\n      if (!active.length) {\n        if (this.opacity !== 0) {\n          properties = {\n            opacity: 0\n          };\n        }\n      } else {\n        const position = positioners[options.position].call(this, active, this._eventPosition);\n        tooltipItems = this._createItems(options);\n        this.title = this.getTitle(tooltipItems, options);\n        this.beforeBody = this.getBeforeBody(tooltipItems, options);\n        this.body = this.getBody(tooltipItems, options);\n        this.afterBody = this.getAfterBody(tooltipItems, options);\n        this.footer = this.getFooter(tooltipItems, options);\n        const size = this._size = getTooltipSize(this, options);\n        const positionAndSize = Object.assign({}, position, size);\n        const alignment = determineAlignment(this.chart, options, positionAndSize);\n        const backgroundPoint = getBackgroundPoint(options, positionAndSize, alignment, this.chart);\n        this.xAlign = alignment.xAlign;\n        this.yAlign = alignment.yAlign;\n        properties = {\n          opacity: 1,\n          x: backgroundPoint.x,\n          y: backgroundPoint.y,\n          width: size.width,\n          height: size.height,\n          caretX: position.x,\n          caretY: position.y\n        };\n      }\n      this._tooltipItems = tooltipItems;\n      this.$context = undefined;\n      if (properties) {\n        this._resolveAnimations().update(this, properties);\n      }\n      if (changed && options.external) {\n        options.external.call(this, {\n          chart: this.chart,\n          tooltip: this,\n          replay\n        });\n      }\n    }\n    drawCaret(tooltipPoint, ctx, size, options) {\n      const caretPosition = this.getCaretPosition(tooltipPoint, size, options);\n      ctx.lineTo(caretPosition.x1, caretPosition.y1);\n      ctx.lineTo(caretPosition.x2, caretPosition.y2);\n      ctx.lineTo(caretPosition.x3, caretPosition.y3);\n    }\n    getCaretPosition(tooltipPoint, size, options) {\n      const {\n        xAlign,\n        yAlign\n      } = this;\n      const {\n        caretSize,\n        cornerRadius\n      } = options;\n      const {\n        topLeft,\n        topRight,\n        bottomLeft,\n        bottomRight\n      } = toTRBLCorners(cornerRadius);\n      const {\n        x: ptX,\n        y: ptY\n      } = tooltipPoint;\n      const {\n        width,\n        height\n      } = size;\n      let x1, x2, x3, y1, y2, y3;\n      if (yAlign === 'center') {\n        y2 = ptY + height / 2;\n        if (xAlign === 'left') {\n          x1 = ptX;\n          x2 = x1 - caretSize;\n          y1 = y2 + caretSize;\n          y3 = y2 - caretSize;\n        } else {\n          x1 = ptX + width;\n          x2 = x1 + caretSize;\n          y1 = y2 - caretSize;\n          y3 = y2 + caretSize;\n        }\n        x3 = x1;\n      } else {\n        if (xAlign === 'left') {\n          x2 = ptX + Math.max(topLeft, bottomLeft) + caretSize;\n        } else if (xAlign === 'right') {\n          x2 = ptX + width - Math.max(topRight, bottomRight) - caretSize;\n        } else {\n          x2 = this.caretX;\n        }\n        if (yAlign === 'top') {\n          y1 = ptY;\n          y2 = y1 - caretSize;\n          x1 = x2 - caretSize;\n          x3 = x2 + caretSize;\n        } else {\n          y1 = ptY + height;\n          y2 = y1 + caretSize;\n          x1 = x2 + caretSize;\n          x3 = x2 - caretSize;\n        }\n        y3 = y1;\n      }\n      return {\n        x1,\n        x2,\n        x3,\n        y1,\n        y2,\n        y3\n      };\n    }\n    drawTitle(pt, ctx, options) {\n      const title = this.title;\n      const length = title.length;\n      let titleFont, titleSpacing, i;\n      if (length) {\n        const rtlHelper = getRtlAdapter(options.rtl, this.x, this.width);\n        pt.x = getAlignedX(this, options.titleAlign, options);\n        ctx.textAlign = rtlHelper.textAlign(options.titleAlign);\n        ctx.textBaseline = 'middle';\n        titleFont = toFont(options.titleFont);\n        titleSpacing = options.titleSpacing;\n        ctx.fillStyle = options.titleColor;\n        ctx.font = titleFont.string;\n        for (i = 0; i < length; ++i) {\n          ctx.fillText(title[i], rtlHelper.x(pt.x), pt.y + titleFont.lineHeight / 2);\n          pt.y += titleFont.lineHeight + titleSpacing;\n          if (i + 1 === length) {\n            pt.y += options.titleMarginBottom - titleSpacing;\n          }\n        }\n      }\n    }\n    _drawColorBox(ctx, pt, i, rtlHelper, options) {\n      const labelColor = this.labelColors[i];\n      const labelPointStyle = this.labelPointStyles[i];\n      const {\n        boxHeight,\n        boxWidth\n      } = options;\n      const bodyFont = toFont(options.bodyFont);\n      const colorX = getAlignedX(this, 'left', options);\n      const rtlColorX = rtlHelper.x(colorX);\n      const yOffSet = boxHeight < bodyFont.lineHeight ? (bodyFont.lineHeight - boxHeight) / 2 : 0;\n      const colorY = pt.y + yOffSet;\n      if (options.usePointStyle) {\n        const drawOptions = {\n          radius: Math.min(boxWidth, boxHeight) / 2,\n          pointStyle: labelPointStyle.pointStyle,\n          rotation: labelPointStyle.rotation,\n          borderWidth: 1\n        };\n        const centerX = rtlHelper.leftForLtr(rtlColorX, boxWidth) + boxWidth / 2;\n        const centerY = colorY + boxHeight / 2;\n        ctx.strokeStyle = options.multiKeyBackground;\n        ctx.fillStyle = options.multiKeyBackground;\n        drawPoint(ctx, drawOptions, centerX, centerY);\n        ctx.strokeStyle = labelColor.borderColor;\n        ctx.fillStyle = labelColor.backgroundColor;\n        drawPoint(ctx, drawOptions, centerX, centerY);\n      } else {\n        ctx.lineWidth = isObject(labelColor.borderWidth) ? Math.max(...Object.values(labelColor.borderWidth)) : labelColor.borderWidth || 1;\n        ctx.strokeStyle = labelColor.borderColor;\n        ctx.setLineDash(labelColor.borderDash || []);\n        ctx.lineDashOffset = labelColor.borderDashOffset || 0;\n        const outerX = rtlHelper.leftForLtr(rtlColorX, boxWidth);\n        const innerX = rtlHelper.leftForLtr(rtlHelper.xPlus(rtlColorX, 1), boxWidth - 2);\n        const borderRadius = toTRBLCorners(labelColor.borderRadius);\n        if (Object.values(borderRadius).some(v => v !== 0)) {\n          ctx.beginPath();\n          ctx.fillStyle = options.multiKeyBackground;\n          addRoundedRectPath(ctx, {\n            x: outerX,\n            y: colorY,\n            w: boxWidth,\n            h: boxHeight,\n            radius: borderRadius\n          });\n          ctx.fill();\n          ctx.stroke();\n          ctx.fillStyle = labelColor.backgroundColor;\n          ctx.beginPath();\n          addRoundedRectPath(ctx, {\n            x: innerX,\n            y: colorY + 1,\n            w: boxWidth - 2,\n            h: boxHeight - 2,\n            radius: borderRadius\n          });\n          ctx.fill();\n        } else {\n          ctx.fillStyle = options.multiKeyBackground;\n          ctx.fillRect(outerX, colorY, boxWidth, boxHeight);\n          ctx.strokeRect(outerX, colorY, boxWidth, boxHeight);\n          ctx.fillStyle = labelColor.backgroundColor;\n          ctx.fillRect(innerX, colorY + 1, boxWidth - 2, boxHeight - 2);\n        }\n      }\n      ctx.fillStyle = this.labelTextColors[i];\n    }\n    drawBody(pt, ctx, options) {\n      const {\n        body\n      } = this;\n      const {\n        bodySpacing,\n        bodyAlign,\n        displayColors,\n        boxHeight,\n        boxWidth,\n        boxPadding\n      } = options;\n      const bodyFont = toFont(options.bodyFont);\n      let bodyLineHeight = bodyFont.lineHeight;\n      let xLinePadding = 0;\n      const rtlHelper = getRtlAdapter(options.rtl, this.x, this.width);\n      const fillLineOfText = function (line) {\n        ctx.fillText(line, rtlHelper.x(pt.x + xLinePadding), pt.y + bodyLineHeight / 2);\n        pt.y += bodyLineHeight + bodySpacing;\n      };\n      const bodyAlignForCalculation = rtlHelper.textAlign(bodyAlign);\n      let bodyItem, textColor, lines, i, j, ilen, jlen;\n      ctx.textAlign = bodyAlign;\n      ctx.textBaseline = 'middle';\n      ctx.font = bodyFont.string;\n      pt.x = getAlignedX(this, bodyAlignForCalculation, options);\n      ctx.fillStyle = options.bodyColor;\n      each(this.beforeBody, fillLineOfText);\n      xLinePadding = displayColors && bodyAlignForCalculation !== 'right' ? bodyAlign === 'center' ? boxWidth / 2 + boxPadding : boxWidth + 2 + boxPadding : 0;\n      for (i = 0, ilen = body.length; i < ilen; ++i) {\n        bodyItem = body[i];\n        textColor = this.labelTextColors[i];\n        ctx.fillStyle = textColor;\n        each(bodyItem.before, fillLineOfText);\n        lines = bodyItem.lines;\n        if (displayColors && lines.length) {\n          this._drawColorBox(ctx, pt, i, rtlHelper, options);\n          bodyLineHeight = Math.max(bodyFont.lineHeight, boxHeight);\n        }\n        for (j = 0, jlen = lines.length; j < jlen; ++j) {\n          fillLineOfText(lines[j]);\n          bodyLineHeight = bodyFont.lineHeight;\n        }\n        each(bodyItem.after, fillLineOfText);\n      }\n      xLinePadding = 0;\n      bodyLineHeight = bodyFont.lineHeight;\n      each(this.afterBody, fillLineOfText);\n      pt.y -= bodySpacing;\n    }\n    drawFooter(pt, ctx, options) {\n      const footer = this.footer;\n      const length = footer.length;\n      let footerFont, i;\n      if (length) {\n        const rtlHelper = getRtlAdapter(options.rtl, this.x, this.width);\n        pt.x = getAlignedX(this, options.footerAlign, options);\n        pt.y += options.footerMarginTop;\n        ctx.textAlign = rtlHelper.textAlign(options.footerAlign);\n        ctx.textBaseline = 'middle';\n        footerFont = toFont(options.footerFont);\n        ctx.fillStyle = options.footerColor;\n        ctx.font = footerFont.string;\n        for (i = 0; i < length; ++i) {\n          ctx.fillText(footer[i], rtlHelper.x(pt.x), pt.y + footerFont.lineHeight / 2);\n          pt.y += footerFont.lineHeight + options.footerSpacing;\n        }\n      }\n    }\n    drawBackground(pt, ctx, tooltipSize, options) {\n      const {\n        xAlign,\n        yAlign\n      } = this;\n      const {\n        x,\n        y\n      } = pt;\n      const {\n        width,\n        height\n      } = tooltipSize;\n      const {\n        topLeft,\n        topRight,\n        bottomLeft,\n        bottomRight\n      } = toTRBLCorners(options.cornerRadius);\n      ctx.fillStyle = options.backgroundColor;\n      ctx.strokeStyle = options.borderColor;\n      ctx.lineWidth = options.borderWidth;\n      ctx.beginPath();\n      ctx.moveTo(x + topLeft, y);\n      if (yAlign === 'top') {\n        this.drawCaret(pt, ctx, tooltipSize, options);\n      }\n      ctx.lineTo(x + width - topRight, y);\n      ctx.quadraticCurveTo(x + width, y, x + width, y + topRight);\n      if (yAlign === 'center' && xAlign === 'right') {\n        this.drawCaret(pt, ctx, tooltipSize, options);\n      }\n      ctx.lineTo(x + width, y + height - bottomRight);\n      ctx.quadraticCurveTo(x + width, y + height, x + width - bottomRight, y + height);\n      if (yAlign === 'bottom') {\n        this.drawCaret(pt, ctx, tooltipSize, options);\n      }\n      ctx.lineTo(x + bottomLeft, y + height);\n      ctx.quadraticCurveTo(x, y + height, x, y + height - bottomLeft);\n      if (yAlign === 'center' && xAlign === 'left') {\n        this.drawCaret(pt, ctx, tooltipSize, options);\n      }\n      ctx.lineTo(x, y + topLeft);\n      ctx.quadraticCurveTo(x, y, x + topLeft, y);\n      ctx.closePath();\n      ctx.fill();\n      if (options.borderWidth > 0) {\n        ctx.stroke();\n      }\n    }\n    _updateAnimationTarget(options) {\n      const chart = this.chart;\n      const anims = this.$animations;\n      const animX = anims && anims.x;\n      const animY = anims && anims.y;\n      if (animX || animY) {\n        const position = positioners[options.position].call(this, this._active, this._eventPosition);\n        if (!position) {\n          return;\n        }\n        const size = this._size = getTooltipSize(this, options);\n        const positionAndSize = Object.assign({}, position, this._size);\n        const alignment = determineAlignment(chart, options, positionAndSize);\n        const point = getBackgroundPoint(options, positionAndSize, alignment, chart);\n        if (animX._to !== point.x || animY._to !== point.y) {\n          this.xAlign = alignment.xAlign;\n          this.yAlign = alignment.yAlign;\n          this.width = size.width;\n          this.height = size.height;\n          this.caretX = position.x;\n          this.caretY = position.y;\n          this._resolveAnimations().update(this, point);\n        }\n      }\n    }\n    _willRender() {\n      return !!this.opacity;\n    }\n    draw(ctx) {\n      const options = this.options.setContext(this.getContext());\n      let opacity = this.opacity;\n      if (!opacity) {\n        return;\n      }\n      this._updateAnimationTarget(options);\n      const tooltipSize = {\n        width: this.width,\n        height: this.height\n      };\n      const pt = {\n        x: this.x,\n        y: this.y\n      };\n      opacity = Math.abs(opacity) < 1e-3 ? 0 : opacity;\n      const padding = toPadding(options.padding);\n      const hasTooltipContent = this.title.length || this.beforeBody.length || this.body.length || this.afterBody.length || this.footer.length;\n      if (options.enabled && hasTooltipContent) {\n        ctx.save();\n        ctx.globalAlpha = opacity;\n        this.drawBackground(pt, ctx, tooltipSize, options);\n        overrideTextDirection(ctx, options.textDirection);\n        pt.y += padding.top;\n        this.drawTitle(pt, ctx, options);\n        this.drawBody(pt, ctx, options);\n        this.drawFooter(pt, ctx, options);\n        restoreTextDirection(ctx, options.textDirection);\n        ctx.restore();\n      }\n    }\n    getActiveElements() {\n      return this._active || [];\n    }\n    setActiveElements(activeElements, eventPosition) {\n      const lastActive = this._active;\n      const active = activeElements.map(({\n        datasetIndex,\n        index\n      }) => {\n        const meta = this.chart.getDatasetMeta(datasetIndex);\n        if (!meta) {\n          throw new Error('Cannot find a dataset at index ' + datasetIndex);\n        }\n        return {\n          datasetIndex,\n          element: meta.data[index],\n          index\n        };\n      });\n      const changed = !_elementsEqual(lastActive, active);\n      const positionChanged = this._positionChanged(active, eventPosition);\n      if (changed || positionChanged) {\n        this._active = active;\n        this._eventPosition = eventPosition;\n        this._ignoreReplayEvents = true;\n        this.update(true);\n      }\n    }\n    handleEvent(e, replay, inChartArea = true) {\n      if (replay && this._ignoreReplayEvents) {\n        return false;\n      }\n      this._ignoreReplayEvents = false;\n      const options = this.options;\n      const lastActive = this._active || [];\n      const active = this._getActiveElements(e, lastActive, replay, inChartArea);\n      const positionChanged = this._positionChanged(active, e);\n      const changed = replay || !_elementsEqual(active, lastActive) || positionChanged;\n      if (changed) {\n        this._active = active;\n        if (options.enabled || options.external) {\n          this._eventPosition = {\n            x: e.x,\n            y: e.y\n          };\n          this.update(true, replay);\n        }\n      }\n      return changed;\n    }\n    _getActiveElements(e, lastActive, replay, inChartArea) {\n      const options = this.options;\n      if (e.type === 'mouseout') {\n        return [];\n      }\n      if (!inChartArea) {\n        return lastActive.filter(i => this.chart.data.datasets[i.datasetIndex] && this.chart.getDatasetMeta(i.datasetIndex).controller.getParsed(i.index) !== undefined);\n      }\n      const active = this.chart.getElementsAtEventForMode(e, options.mode, options, replay);\n      if (options.reverse) {\n        active.reverse();\n      }\n      return active;\n    }\n    _positionChanged(active, e) {\n      const {\n        caretX,\n        caretY,\n        options\n      } = this;\n      const position = positioners[options.position].call(this, active, e);\n      return position !== false && (caretX !== position.x || caretY !== position.y);\n    }\n  }\n  return Tooltip;\n})();\nvar plugin_tooltip = {\n  id: 'tooltip',\n  _element: Tooltip,\n  positioners,\n  afterInit(chart, _args, options) {\n    if (options) {\n      chart.tooltip = new Tooltip({\n        chart,\n        options\n      });\n    }\n  },\n  beforeUpdate(chart, _args, options) {\n    if (chart.tooltip) {\n      chart.tooltip.initialize(options);\n    }\n  },\n  reset(chart, _args, options) {\n    if (chart.tooltip) {\n      chart.tooltip.initialize(options);\n    }\n  },\n  afterDraw(chart) {\n    const tooltip = chart.tooltip;\n    if (tooltip && tooltip._willRender()) {\n      const args = {\n        tooltip\n      };\n      if (chart.notifyPlugins('beforeTooltipDraw', {\n        ...args,\n        cancelable: true\n      }) === false) {\n        return;\n      }\n      tooltip.draw(chart.ctx);\n      chart.notifyPlugins('afterTooltipDraw', args);\n    }\n  },\n  afterEvent(chart, args) {\n    if (chart.tooltip) {\n      const useFinalPosition = args.replay;\n      if (chart.tooltip.handleEvent(args.event, useFinalPosition, args.inChartArea)) {\n        args.changed = true;\n      }\n    }\n  },\n  defaults: {\n    enabled: true,\n    external: null,\n    position: 'average',\n    backgroundColor: 'rgba(0,0,0,0.8)',\n    titleColor: '#fff',\n    titleFont: {\n      weight: 'bold'\n    },\n    titleSpacing: 2,\n    titleMarginBottom: 6,\n    titleAlign: 'left',\n    bodyColor: '#fff',\n    bodySpacing: 2,\n    bodyFont: {},\n    bodyAlign: 'left',\n    footerColor: '#fff',\n    footerSpacing: 2,\n    footerMarginTop: 6,\n    footerFont: {\n      weight: 'bold'\n    },\n    footerAlign: 'left',\n    padding: 6,\n    caretPadding: 2,\n    caretSize: 5,\n    cornerRadius: 6,\n    boxHeight: (ctx, opts) => opts.bodyFont.size,\n    boxWidth: (ctx, opts) => opts.bodyFont.size,\n    multiKeyBackground: '#fff',\n    displayColors: true,\n    boxPadding: 0,\n    borderColor: 'rgba(0,0,0,0)',\n    borderWidth: 0,\n    animation: {\n      duration: 400,\n      easing: 'easeOutQuart'\n    },\n    animations: {\n      numbers: {\n        type: 'number',\n        properties: ['x', 'y', 'width', 'height', 'caretX', 'caretY']\n      },\n      opacity: {\n        easing: 'linear',\n        duration: 200\n      }\n    },\n    callbacks: defaultCallbacks\n  },\n  defaultRoutes: {\n    bodyFont: 'font',\n    footerFont: 'font',\n    titleFont: 'font'\n  },\n  descriptors: {\n    _scriptable: name => name !== 'filter' && name !== 'itemSort' && name !== 'external',\n    _indexable: false,\n    callbacks: {\n      _scriptable: false,\n      _indexable: false\n    },\n    animation: {\n      _fallback: false\n    },\n    animations: {\n      _fallback: 'animation'\n    }\n  },\n  additionalOptionScopes: ['interaction']\n};\nvar plugins = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  Colors: plugin_colors,\n  Decimation: plugin_decimation,\n  Filler: index,\n  Legend: plugin_legend,\n  SubTitle: plugin_subtitle,\n  Title: plugin_title,\n  Tooltip: plugin_tooltip\n});\nconst addIfString = (labels, raw, index, addedLabels) => {\n  if (typeof raw === 'string') {\n    index = labels.push(raw) - 1;\n    addedLabels.unshift({\n      index,\n      label: raw\n    });\n  } else if (isNaN(raw)) {\n    index = null;\n  }\n  return index;\n};\nfunction findOrAddLabel(labels, raw, index, addedLabels) {\n  const first = labels.indexOf(raw);\n  if (first === -1) {\n    return addIfString(labels, raw, index, addedLabels);\n  }\n  const last = labels.lastIndexOf(raw);\n  return first !== last ? index : first;\n}\nconst validIndex = (index, max) => index === null ? null : _limitValue(Math.round(index), 0, max);\nfunction _getLabelForValue(value) {\n  const labels = this.getLabels();\n  if (value >= 0 && value < labels.length) {\n    return labels[value];\n  }\n  return value;\n}\nlet CategoryScale = /*#__PURE__*/(() => {\n  class CategoryScale extends Scale {\n    static id = 'category';\n    static defaults = {\n      ticks: {\n        callback: _getLabelForValue\n      }\n    };\n    constructor(cfg) {\n      super(cfg);\n      this._startValue = undefined;\n      this._valueRange = 0;\n      this._addedLabels = [];\n    }\n    init(scaleOptions) {\n      const added = this._addedLabels;\n      if (added.length) {\n        const labels = this.getLabels();\n        for (const {\n          index,\n          label\n        } of added) {\n          if (labels[index] === label) {\n            labels.splice(index, 1);\n          }\n        }\n        this._addedLabels = [];\n      }\n      super.init(scaleOptions);\n    }\n    parse(raw, index) {\n      if (isNullOrUndef(raw)) {\n        return null;\n      }\n      const labels = this.getLabels();\n      index = isFinite(index) && labels[index] === raw ? index : findOrAddLabel(labels, raw, valueOrDefault(index, raw), this._addedLabels);\n      return validIndex(index, labels.length - 1);\n    }\n    determineDataLimits() {\n      const {\n        minDefined,\n        maxDefined\n      } = this.getUserBounds();\n      let {\n        min,\n        max\n      } = this.getMinMax(true);\n      if (this.options.bounds === 'ticks') {\n        if (!minDefined) {\n          min = 0;\n        }\n        if (!maxDefined) {\n          max = this.getLabels().length - 1;\n        }\n      }\n      this.min = min;\n      this.max = max;\n    }\n    buildTicks() {\n      const min = this.min;\n      const max = this.max;\n      const offset = this.options.offset;\n      const ticks = [];\n      let labels = this.getLabels();\n      labels = min === 0 && max === labels.length - 1 ? labels : labels.slice(min, max + 1);\n      this._valueRange = Math.max(labels.length - (offset ? 0 : 1), 1);\n      this._startValue = this.min - (offset ? 0.5 : 0);\n      for (let value = min; value <= max; value++) {\n        ticks.push({\n          value\n        });\n      }\n      return ticks;\n    }\n    getLabelForValue(value) {\n      return _getLabelForValue.call(this, value);\n    }\n    configure() {\n      super.configure();\n      if (!this.isHorizontal()) {\n        this._reversePixels = !this._reversePixels;\n      }\n    }\n    getPixelForValue(value) {\n      if (typeof value !== 'number') {\n        value = this.parse(value);\n      }\n      return value === null ? NaN : this.getPixelForDecimal((value - this._startValue) / this._valueRange);\n    }\n    getPixelForTick(index) {\n      const ticks = this.ticks;\n      if (index < 0 || index > ticks.length - 1) {\n        return null;\n      }\n      return this.getPixelForValue(ticks[index].value);\n    }\n    getValueForPixel(pixel) {\n      return Math.round(this._startValue + this.getDecimalForPixel(pixel) * this._valueRange);\n    }\n    getBasePixel() {\n      return this.bottom;\n    }\n  }\n  return CategoryScale;\n})();\nfunction generateTicks$1(generationOptions, dataRange) {\n  const ticks = [];\n  const MIN_SPACING = 1e-14;\n  const {\n    bounds,\n    step,\n    min,\n    max,\n    precision,\n    count,\n    maxTicks,\n    maxDigits,\n    includeBounds\n  } = generationOptions;\n  const unit = step || 1;\n  const maxSpaces = maxTicks - 1;\n  const {\n    min: rmin,\n    max: rmax\n  } = dataRange;\n  const minDefined = !isNullOrUndef(min);\n  const maxDefined = !isNullOrUndef(max);\n  const countDefined = !isNullOrUndef(count);\n  const minSpacing = (rmax - rmin) / (maxDigits + 1);\n  let spacing = niceNum((rmax - rmin) / maxSpaces / unit) * unit;\n  let factor, niceMin, niceMax, numSpaces;\n  if (spacing < MIN_SPACING && !minDefined && !maxDefined) {\n    return [{\n      value: rmin\n    }, {\n      value: rmax\n    }];\n  }\n  numSpaces = Math.ceil(rmax / spacing) - Math.floor(rmin / spacing);\n  if (numSpaces > maxSpaces) {\n    spacing = niceNum(numSpaces * spacing / maxSpaces / unit) * unit;\n  }\n  if (!isNullOrUndef(precision)) {\n    factor = Math.pow(10, precision);\n    spacing = Math.ceil(spacing * factor) / factor;\n  }\n  if (bounds === 'ticks') {\n    niceMin = Math.floor(rmin / spacing) * spacing;\n    niceMax = Math.ceil(rmax / spacing) * spacing;\n  } else {\n    niceMin = rmin;\n    niceMax = rmax;\n  }\n  if (minDefined && maxDefined && step && almostWhole((max - min) / step, spacing / 1000)) {\n    numSpaces = Math.round(Math.min((max - min) / spacing, maxTicks));\n    spacing = (max - min) / numSpaces;\n    niceMin = min;\n    niceMax = max;\n  } else if (countDefined) {\n    niceMin = minDefined ? min : niceMin;\n    niceMax = maxDefined ? max : niceMax;\n    numSpaces = count - 1;\n    spacing = (niceMax - niceMin) / numSpaces;\n  } else {\n    numSpaces = (niceMax - niceMin) / spacing;\n    if (almostEquals(numSpaces, Math.round(numSpaces), spacing / 1000)) {\n      numSpaces = Math.round(numSpaces);\n    } else {\n      numSpaces = Math.ceil(numSpaces);\n    }\n  }\n  const decimalPlaces = Math.max(_decimalPlaces(spacing), _decimalPlaces(niceMin));\n  factor = Math.pow(10, isNullOrUndef(precision) ? decimalPlaces : precision);\n  niceMin = Math.round(niceMin * factor) / factor;\n  niceMax = Math.round(niceMax * factor) / factor;\n  let j = 0;\n  if (minDefined) {\n    if (includeBounds && niceMin !== min) {\n      ticks.push({\n        value: min\n      });\n      if (niceMin < min) {\n        j++;\n      }\n      if (almostEquals(Math.round((niceMin + j * spacing) * factor) / factor, min, relativeLabelSize(min, minSpacing, generationOptions))) {\n        j++;\n      }\n    } else if (niceMin < min) {\n      j++;\n    }\n  }\n  for (; j < numSpaces; ++j) {\n    const tickValue = Math.round((niceMin + j * spacing) * factor) / factor;\n    if (maxDefined && tickValue > max) {\n      break;\n    }\n    ticks.push({\n      value: tickValue\n    });\n  }\n  if (maxDefined && includeBounds && niceMax !== max) {\n    if (ticks.length && almostEquals(ticks[ticks.length - 1].value, max, relativeLabelSize(max, minSpacing, generationOptions))) {\n      ticks[ticks.length - 1].value = max;\n    } else {\n      ticks.push({\n        value: max\n      });\n    }\n  } else if (!maxDefined || niceMax === max) {\n    ticks.push({\n      value: niceMax\n    });\n  }\n  return ticks;\n}\nfunction relativeLabelSize(value, minSpacing, {\n  horizontal,\n  minRotation\n}) {\n  const rad = toRadians(minRotation);\n  const ratio = (horizontal ? Math.sin(rad) : Math.cos(rad)) || 0.001;\n  const length = 0.75 * minSpacing * ('' + value).length;\n  return Math.min(minSpacing / ratio, length);\n}\nclass LinearScaleBase extends Scale {\n  constructor(cfg) {\n    super(cfg);\n    this.start = undefined;\n    this.end = undefined;\n    this._startValue = undefined;\n    this._endValue = undefined;\n    this._valueRange = 0;\n  }\n  parse(raw, index) {\n    if (isNullOrUndef(raw)) {\n      return null;\n    }\n    if ((typeof raw === 'number' || raw instanceof Number) && !isFinite(+raw)) {\n      return null;\n    }\n    return +raw;\n  }\n  handleTickRangeOptions() {\n    const {\n      beginAtZero\n    } = this.options;\n    const {\n      minDefined,\n      maxDefined\n    } = this.getUserBounds();\n    let {\n      min,\n      max\n    } = this;\n    const setMin = v => min = minDefined ? min : v;\n    const setMax = v => max = maxDefined ? max : v;\n    if (beginAtZero) {\n      const minSign = sign(min);\n      const maxSign = sign(max);\n      if (minSign < 0 && maxSign < 0) {\n        setMax(0);\n      } else if (minSign > 0 && maxSign > 0) {\n        setMin(0);\n      }\n    }\n    if (min === max) {\n      let offset = max === 0 ? 1 : Math.abs(max * 0.05);\n      setMax(max + offset);\n      if (!beginAtZero) {\n        setMin(min - offset);\n      }\n    }\n    this.min = min;\n    this.max = max;\n  }\n  getTickLimit() {\n    const tickOpts = this.options.ticks;\n    let {\n      maxTicksLimit,\n      stepSize\n    } = tickOpts;\n    let maxTicks;\n    if (stepSize) {\n      maxTicks = Math.ceil(this.max / stepSize) - Math.floor(this.min / stepSize) + 1;\n      if (maxTicks > 1000) {\n        console.warn(`scales.${this.id}.ticks.stepSize: ${stepSize} would result generating up to ${maxTicks} ticks. Limiting to 1000.`);\n        maxTicks = 1000;\n      }\n    } else {\n      maxTicks = this.computeTickLimit();\n      maxTicksLimit = maxTicksLimit || 11;\n    }\n    if (maxTicksLimit) {\n      maxTicks = Math.min(maxTicksLimit, maxTicks);\n    }\n    return maxTicks;\n  }\n  computeTickLimit() {\n    return Number.POSITIVE_INFINITY;\n  }\n  buildTicks() {\n    const opts = this.options;\n    const tickOpts = opts.ticks;\n    let maxTicks = this.getTickLimit();\n    maxTicks = Math.max(2, maxTicks);\n    const numericGeneratorOptions = {\n      maxTicks,\n      bounds: opts.bounds,\n      min: opts.min,\n      max: opts.max,\n      precision: tickOpts.precision,\n      step: tickOpts.stepSize,\n      count: tickOpts.count,\n      maxDigits: this._maxDigits(),\n      horizontal: this.isHorizontal(),\n      minRotation: tickOpts.minRotation || 0,\n      includeBounds: tickOpts.includeBounds !== false\n    };\n    const dataRange = this._range || this;\n    const ticks = generateTicks$1(numericGeneratorOptions, dataRange);\n    if (opts.bounds === 'ticks') {\n      _setMinAndMaxByKey(ticks, this, 'value');\n    }\n    if (opts.reverse) {\n      ticks.reverse();\n      this.start = this.max;\n      this.end = this.min;\n    } else {\n      this.start = this.min;\n      this.end = this.max;\n    }\n    return ticks;\n  }\n  configure() {\n    const ticks = this.ticks;\n    let start = this.min;\n    let end = this.max;\n    super.configure();\n    if (this.options.offset && ticks.length) {\n      const offset = (end - start) / Math.max(ticks.length - 1, 1) / 2;\n      start -= offset;\n      end += offset;\n    }\n    this._startValue = start;\n    this._endValue = end;\n    this._valueRange = end - start;\n  }\n  getLabelForValue(value) {\n    return formatNumber(value, this.chart.options.locale, this.options.ticks.format);\n  }\n}\nclass LinearScale extends LinearScaleBase {\n  static id = 'linear';\n  static defaults = {\n    ticks: {\n      callback: Ticks.formatters.numeric\n    }\n  };\n  determineDataLimits() {\n    const {\n      min,\n      max\n    } = this.getMinMax(true);\n    this.min = isNumberFinite(min) ? min : 0;\n    this.max = isNumberFinite(max) ? max : 1;\n    this.handleTickRangeOptions();\n  }\n  computeTickLimit() {\n    const horizontal = this.isHorizontal();\n    const length = horizontal ? this.width : this.height;\n    const minRotation = toRadians(this.options.ticks.minRotation);\n    const ratio = (horizontal ? Math.sin(minRotation) : Math.cos(minRotation)) || 0.001;\n    const tickFont = this._resolveTickFontOptions(0);\n    return Math.ceil(length / Math.min(40, tickFont.lineHeight / ratio));\n  }\n  getPixelForValue(value) {\n    return value === null ? NaN : this.getPixelForDecimal((value - this._startValue) / this._valueRange);\n  }\n  getValueForPixel(pixel) {\n    return this._startValue + this.getDecimalForPixel(pixel) * this._valueRange;\n  }\n}\nconst log10Floor = v => Math.floor(log10(v));\nconst changeExponent = (v, m) => Math.pow(10, log10Floor(v) + m);\nfunction isMajor(tickVal) {\n  const remain = tickVal / Math.pow(10, log10Floor(tickVal));\n  return remain === 1;\n}\nfunction steps(min, max, rangeExp) {\n  const rangeStep = Math.pow(10, rangeExp);\n  const start = Math.floor(min / rangeStep);\n  const end = Math.ceil(max / rangeStep);\n  return end - start;\n}\nfunction startExp(min, max) {\n  const range = max - min;\n  let rangeExp = log10Floor(range);\n  while (steps(min, max, rangeExp) > 10) {\n    rangeExp++;\n  }\n  while (steps(min, max, rangeExp) < 10) {\n    rangeExp--;\n  }\n  return Math.min(rangeExp, log10Floor(min));\n}\nfunction generateTicks(generationOptions, {\n  min,\n  max\n}) {\n  min = finiteOrDefault(generationOptions.min, min);\n  const ticks = [];\n  const minExp = log10Floor(min);\n  let exp = startExp(min, max);\n  let precision = exp < 0 ? Math.pow(10, Math.abs(exp)) : 1;\n  const stepSize = Math.pow(10, exp);\n  const base = minExp > exp ? Math.pow(10, minExp) : 0;\n  const start = Math.round((min - base) * precision) / precision;\n  const offset = Math.floor((min - base) / stepSize / 10) * stepSize * 10;\n  let significand = Math.floor((start - offset) / Math.pow(10, exp));\n  let value = finiteOrDefault(generationOptions.min, Math.round((base + offset + significand * Math.pow(10, exp)) * precision) / precision);\n  while (value < max) {\n    ticks.push({\n      value,\n      major: isMajor(value),\n      significand\n    });\n    if (significand >= 10) {\n      significand = significand < 15 ? 15 : 20;\n    } else {\n      significand++;\n    }\n    if (significand >= 20) {\n      exp++;\n      significand = 2;\n      precision = exp >= 0 ? 1 : precision;\n    }\n    value = Math.round((base + offset + significand * Math.pow(10, exp)) * precision) / precision;\n  }\n  const lastTick = finiteOrDefault(generationOptions.max, value);\n  ticks.push({\n    value: lastTick,\n    major: isMajor(lastTick),\n    significand\n  });\n  return ticks;\n}\nclass LogarithmicScale extends Scale {\n  static id = 'logarithmic';\n  static defaults = {\n    ticks: {\n      callback: Ticks.formatters.logarithmic,\n      major: {\n        enabled: true\n      }\n    }\n  };\n  constructor(cfg) {\n    super(cfg);\n    this.start = undefined;\n    this.end = undefined;\n    this._startValue = undefined;\n    this._valueRange = 0;\n  }\n  parse(raw, index) {\n    const value = LinearScaleBase.prototype.parse.apply(this, [raw, index]);\n    if (value === 0) {\n      this._zero = true;\n      return undefined;\n    }\n    return isNumberFinite(value) && value > 0 ? value : null;\n  }\n  determineDataLimits() {\n    const {\n      min,\n      max\n    } = this.getMinMax(true);\n    this.min = isNumberFinite(min) ? Math.max(0, min) : null;\n    this.max = isNumberFinite(max) ? Math.max(0, max) : null;\n    if (this.options.beginAtZero) {\n      this._zero = true;\n    }\n    if (this._zero && this.min !== this._suggestedMin && !isNumberFinite(this._userMin)) {\n      this.min = min === changeExponent(this.min, 0) ? changeExponent(this.min, -1) : changeExponent(this.min, 0);\n    }\n    this.handleTickRangeOptions();\n  }\n  handleTickRangeOptions() {\n    const {\n      minDefined,\n      maxDefined\n    } = this.getUserBounds();\n    let min = this.min;\n    let max = this.max;\n    const setMin = v => min = minDefined ? min : v;\n    const setMax = v => max = maxDefined ? max : v;\n    if (min === max) {\n      if (min <= 0) {\n        setMin(1);\n        setMax(10);\n      } else {\n        setMin(changeExponent(min, -1));\n        setMax(changeExponent(max, +1));\n      }\n    }\n    if (min <= 0) {\n      setMin(changeExponent(max, -1));\n    }\n    if (max <= 0) {\n      setMax(changeExponent(min, +1));\n    }\n    this.min = min;\n    this.max = max;\n  }\n  buildTicks() {\n    const opts = this.options;\n    const generationOptions = {\n      min: this._userMin,\n      max: this._userMax\n    };\n    const ticks = generateTicks(generationOptions, this);\n    if (opts.bounds === 'ticks') {\n      _setMinAndMaxByKey(ticks, this, 'value');\n    }\n    if (opts.reverse) {\n      ticks.reverse();\n      this.start = this.max;\n      this.end = this.min;\n    } else {\n      this.start = this.min;\n      this.end = this.max;\n    }\n    return ticks;\n  }\n  getLabelForValue(value) {\n    return value === undefined ? '0' : formatNumber(value, this.chart.options.locale, this.options.ticks.format);\n  }\n  configure() {\n    const start = this.min;\n    super.configure();\n    this._startValue = log10(start);\n    this._valueRange = log10(this.max) - log10(start);\n  }\n  getPixelForValue(value) {\n    if (value === undefined || value === 0) {\n      value = this.min;\n    }\n    if (value === null || isNaN(value)) {\n      return NaN;\n    }\n    return this.getPixelForDecimal(value === this.min ? 0 : (log10(value) - this._startValue) / this._valueRange);\n  }\n  getValueForPixel(pixel) {\n    const decimal = this.getDecimalForPixel(pixel);\n    return Math.pow(10, this._startValue + decimal * this._valueRange);\n  }\n}\nfunction getTickBackdropHeight(opts) {\n  const tickOpts = opts.ticks;\n  if (tickOpts.display && opts.display) {\n    const padding = toPadding(tickOpts.backdropPadding);\n    return valueOrDefault(tickOpts.font && tickOpts.font.size, defaults.font.size) + padding.height;\n  }\n  return 0;\n}\nfunction measureLabelSize(ctx, font, label) {\n  label = isArray(label) ? label : [label];\n  return {\n    w: _longestText(ctx, font.string, label),\n    h: label.length * font.lineHeight\n  };\n}\nfunction determineLimits(angle, pos, size, min, max) {\n  if (angle === min || angle === max) {\n    return {\n      start: pos - size / 2,\n      end: pos + size / 2\n    };\n  } else if (angle < min || angle > max) {\n    return {\n      start: pos - size,\n      end: pos\n    };\n  }\n  return {\n    start: pos,\n    end: pos + size\n  };\n}\nfunction fitWithPointLabels(scale) {\n  const orig = {\n    l: scale.left + scale._padding.left,\n    r: scale.right - scale._padding.right,\n    t: scale.top + scale._padding.top,\n    b: scale.bottom - scale._padding.bottom\n  };\n  const limits = Object.assign({}, orig);\n  const labelSizes = [];\n  const padding = [];\n  const valueCount = scale._pointLabels.length;\n  const pointLabelOpts = scale.options.pointLabels;\n  const additionalAngle = pointLabelOpts.centerPointLabels ? PI / valueCount : 0;\n  for (let i = 0; i < valueCount; i++) {\n    const opts = pointLabelOpts.setContext(scale.getPointLabelContext(i));\n    padding[i] = opts.padding;\n    const pointPosition = scale.getPointPosition(i, scale.drawingArea + padding[i], additionalAngle);\n    const plFont = toFont(opts.font);\n    const textSize = measureLabelSize(scale.ctx, plFont, scale._pointLabels[i]);\n    labelSizes[i] = textSize;\n    const angleRadians = _normalizeAngle(scale.getIndexAngle(i) + additionalAngle);\n    const angle = Math.round(toDegrees(angleRadians));\n    const hLimits = determineLimits(angle, pointPosition.x, textSize.w, 0, 180);\n    const vLimits = determineLimits(angle, pointPosition.y, textSize.h, 90, 270);\n    updateLimits(limits, orig, angleRadians, hLimits, vLimits);\n  }\n  scale.setCenterPoint(orig.l - limits.l, limits.r - orig.r, orig.t - limits.t, limits.b - orig.b);\n  scale._pointLabelItems = buildPointLabelItems(scale, labelSizes, padding);\n}\nfunction updateLimits(limits, orig, angle, hLimits, vLimits) {\n  const sin = Math.abs(Math.sin(angle));\n  const cos = Math.abs(Math.cos(angle));\n  let x = 0;\n  let y = 0;\n  if (hLimits.start < orig.l) {\n    x = (orig.l - hLimits.start) / sin;\n    limits.l = Math.min(limits.l, orig.l - x);\n  } else if (hLimits.end > orig.r) {\n    x = (hLimits.end - orig.r) / sin;\n    limits.r = Math.max(limits.r, orig.r + x);\n  }\n  if (vLimits.start < orig.t) {\n    y = (orig.t - vLimits.start) / cos;\n    limits.t = Math.min(limits.t, orig.t - y);\n  } else if (vLimits.end > orig.b) {\n    y = (vLimits.end - orig.b) / cos;\n    limits.b = Math.max(limits.b, orig.b + y);\n  }\n}\nfunction createPointLabelItem(scale, index, itemOpts) {\n  const outerDistance = scale.drawingArea;\n  const {\n    extra,\n    additionalAngle,\n    padding,\n    size\n  } = itemOpts;\n  const pointLabelPosition = scale.getPointPosition(index, outerDistance + extra + padding, additionalAngle);\n  const angle = Math.round(toDegrees(_normalizeAngle(pointLabelPosition.angle + HALF_PI)));\n  const y = yForAngle(pointLabelPosition.y, size.h, angle);\n  const textAlign = getTextAlignForAngle(angle);\n  const left = leftForTextAlign(pointLabelPosition.x, size.w, textAlign);\n  return {\n    visible: true,\n    x: pointLabelPosition.x,\n    y,\n    textAlign,\n    left,\n    top: y,\n    right: left + size.w,\n    bottom: y + size.h\n  };\n}\nfunction isNotOverlapped(item, area) {\n  if (!area) {\n    return true;\n  }\n  const {\n    left,\n    top,\n    right,\n    bottom\n  } = item;\n  const apexesInArea = _isPointInArea({\n    x: left,\n    y: top\n  }, area) || _isPointInArea({\n    x: left,\n    y: bottom\n  }, area) || _isPointInArea({\n    x: right,\n    y: top\n  }, area) || _isPointInArea({\n    x: right,\n    y: bottom\n  }, area);\n  return !apexesInArea;\n}\nfunction buildPointLabelItems(scale, labelSizes, padding) {\n  const items = [];\n  const valueCount = scale._pointLabels.length;\n  const opts = scale.options;\n  const {\n    centerPointLabels,\n    display\n  } = opts.pointLabels;\n  const itemOpts = {\n    extra: getTickBackdropHeight(opts) / 2,\n    additionalAngle: centerPointLabels ? PI / valueCount : 0\n  };\n  let area;\n  for (let i = 0; i < valueCount; i++) {\n    itemOpts.padding = padding[i];\n    itemOpts.size = labelSizes[i];\n    const item = createPointLabelItem(scale, i, itemOpts);\n    items.push(item);\n    if (display === 'auto') {\n      item.visible = isNotOverlapped(item, area);\n      if (item.visible) {\n        area = item;\n      }\n    }\n  }\n  return items;\n}\nfunction getTextAlignForAngle(angle) {\n  if (angle === 0 || angle === 180) {\n    return 'center';\n  } else if (angle < 180) {\n    return 'left';\n  }\n  return 'right';\n}\nfunction leftForTextAlign(x, w, align) {\n  if (align === 'right') {\n    x -= w;\n  } else if (align === 'center') {\n    x -= w / 2;\n  }\n  return x;\n}\nfunction yForAngle(y, h, angle) {\n  if (angle === 90 || angle === 270) {\n    y -= h / 2;\n  } else if (angle > 270 || angle < 90) {\n    y -= h;\n  }\n  return y;\n}\nfunction drawPointLabelBox(ctx, opts, item) {\n  const {\n    left,\n    top,\n    right,\n    bottom\n  } = item;\n  const {\n    backdropColor\n  } = opts;\n  if (!isNullOrUndef(backdropColor)) {\n    const borderRadius = toTRBLCorners(opts.borderRadius);\n    const padding = toPadding(opts.backdropPadding);\n    ctx.fillStyle = backdropColor;\n    const backdropLeft = left - padding.left;\n    const backdropTop = top - padding.top;\n    const backdropWidth = right - left + padding.width;\n    const backdropHeight = bottom - top + padding.height;\n    if (Object.values(borderRadius).some(v => v !== 0)) {\n      ctx.beginPath();\n      addRoundedRectPath(ctx, {\n        x: backdropLeft,\n        y: backdropTop,\n        w: backdropWidth,\n        h: backdropHeight,\n        radius: borderRadius\n      });\n      ctx.fill();\n    } else {\n      ctx.fillRect(backdropLeft, backdropTop, backdropWidth, backdropHeight);\n    }\n  }\n}\nfunction drawPointLabels(scale, labelCount) {\n  const {\n    ctx,\n    options: {\n      pointLabels\n    }\n  } = scale;\n  for (let i = labelCount - 1; i >= 0; i--) {\n    const item = scale._pointLabelItems[i];\n    if (!item.visible) {\n      continue;\n    }\n    const optsAtIndex = pointLabels.setContext(scale.getPointLabelContext(i));\n    drawPointLabelBox(ctx, optsAtIndex, item);\n    const plFont = toFont(optsAtIndex.font);\n    const {\n      x,\n      y,\n      textAlign\n    } = item;\n    renderText(ctx, scale._pointLabels[i], x, y + plFont.lineHeight / 2, plFont, {\n      color: optsAtIndex.color,\n      textAlign: textAlign,\n      textBaseline: 'middle'\n    });\n  }\n}\nfunction pathRadiusLine(scale, radius, circular, labelCount) {\n  const {\n    ctx\n  } = scale;\n  if (circular) {\n    ctx.arc(scale.xCenter, scale.yCenter, radius, 0, TAU);\n  } else {\n    let pointPosition = scale.getPointPosition(0, radius);\n    ctx.moveTo(pointPosition.x, pointPosition.y);\n    for (let i = 1; i < labelCount; i++) {\n      pointPosition = scale.getPointPosition(i, radius);\n      ctx.lineTo(pointPosition.x, pointPosition.y);\n    }\n  }\n}\nfunction drawRadiusLine(scale, gridLineOpts, radius, labelCount, borderOpts) {\n  const ctx = scale.ctx;\n  const circular = gridLineOpts.circular;\n  const {\n    color,\n    lineWidth\n  } = gridLineOpts;\n  if (!circular && !labelCount || !color || !lineWidth || radius < 0) {\n    return;\n  }\n  ctx.save();\n  ctx.strokeStyle = color;\n  ctx.lineWidth = lineWidth;\n  ctx.setLineDash(borderOpts.dash);\n  ctx.lineDashOffset = borderOpts.dashOffset;\n  ctx.beginPath();\n  pathRadiusLine(scale, radius, circular, labelCount);\n  ctx.closePath();\n  ctx.stroke();\n  ctx.restore();\n}\nfunction createPointLabelContext(parent, index, label) {\n  return createContext(parent, {\n    label,\n    index,\n    type: 'pointLabel'\n  });\n}\nclass RadialLinearScale extends LinearScaleBase {\n  static id = 'radialLinear';\n  static defaults = {\n    display: true,\n    animate: true,\n    position: 'chartArea',\n    angleLines: {\n      display: true,\n      lineWidth: 1,\n      borderDash: [],\n      borderDashOffset: 0.0\n    },\n    grid: {\n      circular: false\n    },\n    startAngle: 0,\n    ticks: {\n      showLabelBackdrop: true,\n      callback: Ticks.formatters.numeric\n    },\n    pointLabels: {\n      backdropColor: undefined,\n      backdropPadding: 2,\n      display: true,\n      font: {\n        size: 10\n      },\n      callback(label) {\n        return label;\n      },\n      padding: 5,\n      centerPointLabels: false\n    }\n  };\n  static defaultRoutes = {\n    'angleLines.color': 'borderColor',\n    'pointLabels.color': 'color',\n    'ticks.color': 'color'\n  };\n  static descriptors = {\n    angleLines: {\n      _fallback: 'grid'\n    }\n  };\n  constructor(cfg) {\n    super(cfg);\n    this.xCenter = undefined;\n    this.yCenter = undefined;\n    this.drawingArea = undefined;\n    this._pointLabels = [];\n    this._pointLabelItems = [];\n  }\n  setDimensions() {\n    const padding = this._padding = toPadding(getTickBackdropHeight(this.options) / 2);\n    const w = this.width = this.maxWidth - padding.width;\n    const h = this.height = this.maxHeight - padding.height;\n    this.xCenter = Math.floor(this.left + w / 2 + padding.left);\n    this.yCenter = Math.floor(this.top + h / 2 + padding.top);\n    this.drawingArea = Math.floor(Math.min(w, h) / 2);\n  }\n  determineDataLimits() {\n    const {\n      min,\n      max\n    } = this.getMinMax(false);\n    this.min = isNumberFinite(min) && !isNaN(min) ? min : 0;\n    this.max = isNumberFinite(max) && !isNaN(max) ? max : 0;\n    this.handleTickRangeOptions();\n  }\n  computeTickLimit() {\n    return Math.ceil(this.drawingArea / getTickBackdropHeight(this.options));\n  }\n  generateTickLabels(ticks) {\n    LinearScaleBase.prototype.generateTickLabels.call(this, ticks);\n    this._pointLabels = this.getLabels().map((value, index) => {\n      const label = callback(this.options.pointLabels.callback, [value, index], this);\n      return label || label === 0 ? label : '';\n    }).filter((v, i) => this.chart.getDataVisibility(i));\n  }\n  fit() {\n    const opts = this.options;\n    if (opts.display && opts.pointLabels.display) {\n      fitWithPointLabels(this);\n    } else {\n      this.setCenterPoint(0, 0, 0, 0);\n    }\n  }\n  setCenterPoint(leftMovement, rightMovement, topMovement, bottomMovement) {\n    this.xCenter += Math.floor((leftMovement - rightMovement) / 2);\n    this.yCenter += Math.floor((topMovement - bottomMovement) / 2);\n    this.drawingArea -= Math.min(this.drawingArea / 2, Math.max(leftMovement, rightMovement, topMovement, bottomMovement));\n  }\n  getIndexAngle(index) {\n    const angleMultiplier = TAU / (this._pointLabels.length || 1);\n    const startAngle = this.options.startAngle || 0;\n    return _normalizeAngle(index * angleMultiplier + toRadians(startAngle));\n  }\n  getDistanceFromCenterForValue(value) {\n    if (isNullOrUndef(value)) {\n      return NaN;\n    }\n    const scalingFactor = this.drawingArea / (this.max - this.min);\n    if (this.options.reverse) {\n      return (this.max - value) * scalingFactor;\n    }\n    return (value - this.min) * scalingFactor;\n  }\n  getValueForDistanceFromCenter(distance) {\n    if (isNullOrUndef(distance)) {\n      return NaN;\n    }\n    const scaledDistance = distance / (this.drawingArea / (this.max - this.min));\n    return this.options.reverse ? this.max - scaledDistance : this.min + scaledDistance;\n  }\n  getPointLabelContext(index) {\n    const pointLabels = this._pointLabels || [];\n    if (index >= 0 && index < pointLabels.length) {\n      const pointLabel = pointLabels[index];\n      return createPointLabelContext(this.getContext(), index, pointLabel);\n    }\n  }\n  getPointPosition(index, distanceFromCenter, additionalAngle = 0) {\n    const angle = this.getIndexAngle(index) - HALF_PI + additionalAngle;\n    return {\n      x: Math.cos(angle) * distanceFromCenter + this.xCenter,\n      y: Math.sin(angle) * distanceFromCenter + this.yCenter,\n      angle\n    };\n  }\n  getPointPositionForValue(index, value) {\n    return this.getPointPosition(index, this.getDistanceFromCenterForValue(value));\n  }\n  getBasePosition(index) {\n    return this.getPointPositionForValue(index || 0, this.getBaseValue());\n  }\n  getPointLabelPosition(index) {\n    const {\n      left,\n      top,\n      right,\n      bottom\n    } = this._pointLabelItems[index];\n    return {\n      left,\n      top,\n      right,\n      bottom\n    };\n  }\n  drawBackground() {\n    const {\n      backgroundColor,\n      grid: {\n        circular\n      }\n    } = this.options;\n    if (backgroundColor) {\n      const ctx = this.ctx;\n      ctx.save();\n      ctx.beginPath();\n      pathRadiusLine(this, this.getDistanceFromCenterForValue(this._endValue), circular, this._pointLabels.length);\n      ctx.closePath();\n      ctx.fillStyle = backgroundColor;\n      ctx.fill();\n      ctx.restore();\n    }\n  }\n  drawGrid() {\n    const ctx = this.ctx;\n    const opts = this.options;\n    const {\n      angleLines,\n      grid,\n      border\n    } = opts;\n    const labelCount = this._pointLabels.length;\n    let i, offset, position;\n    if (opts.pointLabels.display) {\n      drawPointLabels(this, labelCount);\n    }\n    if (grid.display) {\n      this.ticks.forEach((tick, index) => {\n        if (index !== 0 || index === 0 && this.min < 0) {\n          offset = this.getDistanceFromCenterForValue(tick.value);\n          const context = this.getContext(index);\n          const optsAtIndex = grid.setContext(context);\n          const optsAtIndexBorder = border.setContext(context);\n          drawRadiusLine(this, optsAtIndex, offset, labelCount, optsAtIndexBorder);\n        }\n      });\n    }\n    if (angleLines.display) {\n      ctx.save();\n      for (i = labelCount - 1; i >= 0; i--) {\n        const optsAtIndex = angleLines.setContext(this.getPointLabelContext(i));\n        const {\n          color,\n          lineWidth\n        } = optsAtIndex;\n        if (!lineWidth || !color) {\n          continue;\n        }\n        ctx.lineWidth = lineWidth;\n        ctx.strokeStyle = color;\n        ctx.setLineDash(optsAtIndex.borderDash);\n        ctx.lineDashOffset = optsAtIndex.borderDashOffset;\n        offset = this.getDistanceFromCenterForValue(opts.reverse ? this.min : this.max);\n        position = this.getPointPosition(i, offset);\n        ctx.beginPath();\n        ctx.moveTo(this.xCenter, this.yCenter);\n        ctx.lineTo(position.x, position.y);\n        ctx.stroke();\n      }\n      ctx.restore();\n    }\n  }\n  drawBorder() {}\n  drawLabels() {\n    const ctx = this.ctx;\n    const opts = this.options;\n    const tickOpts = opts.ticks;\n    if (!tickOpts.display) {\n      return;\n    }\n    const startAngle = this.getIndexAngle(0);\n    let offset, width;\n    ctx.save();\n    ctx.translate(this.xCenter, this.yCenter);\n    ctx.rotate(startAngle);\n    ctx.textAlign = 'center';\n    ctx.textBaseline = 'middle';\n    this.ticks.forEach((tick, index) => {\n      if (index === 0 && this.min >= 0 && !opts.reverse) {\n        return;\n      }\n      const optsAtIndex = tickOpts.setContext(this.getContext(index));\n      const tickFont = toFont(optsAtIndex.font);\n      offset = this.getDistanceFromCenterForValue(this.ticks[index].value);\n      if (optsAtIndex.showLabelBackdrop) {\n        ctx.font = tickFont.string;\n        width = ctx.measureText(tick.label).width;\n        ctx.fillStyle = optsAtIndex.backdropColor;\n        const padding = toPadding(optsAtIndex.backdropPadding);\n        ctx.fillRect(-width / 2 - padding.left, -offset - tickFont.size / 2 - padding.top, width + padding.width, tickFont.size + padding.height);\n      }\n      renderText(ctx, tick.label, 0, -offset, tickFont, {\n        color: optsAtIndex.color,\n        strokeColor: optsAtIndex.textStrokeColor,\n        strokeWidth: optsAtIndex.textStrokeWidth\n      });\n    });\n    ctx.restore();\n  }\n  drawTitle() {}\n}\nconst INTERVALS = {\n  millisecond: {\n    common: true,\n    size: 1,\n    steps: 1000\n  },\n  second: {\n    common: true,\n    size: 1000,\n    steps: 60\n  },\n  minute: {\n    common: true,\n    size: 60000,\n    steps: 60\n  },\n  hour: {\n    common: true,\n    size: 3600000,\n    steps: 24\n  },\n  day: {\n    common: true,\n    size: 86400000,\n    steps: 30\n  },\n  week: {\n    common: false,\n    size: 604800000,\n    steps: 4\n  },\n  month: {\n    common: true,\n    size: 2.628e9,\n    steps: 12\n  },\n  quarter: {\n    common: false,\n    size: 7.884e9,\n    steps: 4\n  },\n  year: {\n    common: true,\n    size: 3.154e10\n  }\n};\nconst UNITS = /* #__PURE__ */Object.keys(INTERVALS);\nfunction sorter(a, b) {\n  return a - b;\n}\nfunction parse(scale, input) {\n  if (isNullOrUndef(input)) {\n    return null;\n  }\n  const adapter = scale._adapter;\n  const {\n    parser,\n    round,\n    isoWeekday\n  } = scale._parseOpts;\n  let value = input;\n  if (typeof parser === 'function') {\n    value = parser(value);\n  }\n  if (!isNumberFinite(value)) {\n    value = typeof parser === 'string' ? adapter.parse(value, parser) : adapter.parse(value);\n  }\n  if (value === null) {\n    return null;\n  }\n  if (round) {\n    value = round === 'week' && (isNumber(isoWeekday) || isoWeekday === true) ? adapter.startOf(value, 'isoWeek', isoWeekday) : adapter.startOf(value, round);\n  }\n  return +value;\n}\nfunction determineUnitForAutoTicks(minUnit, min, max, capacity) {\n  const ilen = UNITS.length;\n  for (let i = UNITS.indexOf(minUnit); i < ilen - 1; ++i) {\n    const interval = INTERVALS[UNITS[i]];\n    const factor = interval.steps ? interval.steps : Number.MAX_SAFE_INTEGER;\n    if (interval.common && Math.ceil((max - min) / (factor * interval.size)) <= capacity) {\n      return UNITS[i];\n    }\n  }\n  return UNITS[ilen - 1];\n}\nfunction determineUnitForFormatting(scale, numTicks, minUnit, min, max) {\n  for (let i = UNITS.length - 1; i >= UNITS.indexOf(minUnit); i--) {\n    const unit = UNITS[i];\n    if (INTERVALS[unit].common && scale._adapter.diff(max, min, unit) >= numTicks - 1) {\n      return unit;\n    }\n  }\n  return UNITS[minUnit ? UNITS.indexOf(minUnit) : 0];\n}\nfunction determineMajorUnit(unit) {\n  for (let i = UNITS.indexOf(unit) + 1, ilen = UNITS.length; i < ilen; ++i) {\n    if (INTERVALS[UNITS[i]].common) {\n      return UNITS[i];\n    }\n  }\n}\nfunction addTick(ticks, time, timestamps) {\n  if (!timestamps) {\n    ticks[time] = true;\n  } else if (timestamps.length) {\n    const {\n      lo,\n      hi\n    } = _lookup(timestamps, time);\n    const timestamp = timestamps[lo] >= time ? timestamps[lo] : timestamps[hi];\n    ticks[timestamp] = true;\n  }\n}\nfunction setMajorTicks(scale, ticks, map, majorUnit) {\n  const adapter = scale._adapter;\n  const first = +adapter.startOf(ticks[0].value, majorUnit);\n  const last = ticks[ticks.length - 1].value;\n  let major, index;\n  for (major = first; major <= last; major = +adapter.add(major, 1, majorUnit)) {\n    index = map[major];\n    if (index >= 0) {\n      ticks[index].major = true;\n    }\n  }\n  return ticks;\n}\nfunction ticksFromTimestamps(scale, values, majorUnit) {\n  const ticks = [];\n  const map = {};\n  const ilen = values.length;\n  let i, value;\n  for (i = 0; i < ilen; ++i) {\n    value = values[i];\n    map[value] = i;\n    ticks.push({\n      value,\n      major: false\n    });\n  }\n  return ilen === 0 || !majorUnit ? ticks : setMajorTicks(scale, ticks, map, majorUnit);\n}\nlet TimeScale = /*#__PURE__*/(() => {\n  class TimeScale extends Scale {\n    static id = 'time';\n    static defaults = {\n      bounds: 'data',\n      adapters: {},\n      time: {\n        parser: false,\n        unit: false,\n        round: false,\n        isoWeekday: false,\n        minUnit: 'millisecond',\n        displayFormats: {}\n      },\n      ticks: {\n        source: 'auto',\n        callback: false,\n        major: {\n          enabled: false\n        }\n      }\n    };\n    constructor(props) {\n      super(props);\n      this._cache = {\n        data: [],\n        labels: [],\n        all: []\n      };\n      this._unit = 'day';\n      this._majorUnit = undefined;\n      this._offsets = {};\n      this._normalized = false;\n      this._parseOpts = undefined;\n    }\n    init(scaleOpts, opts = {}) {\n      const time = scaleOpts.time || (scaleOpts.time = {});\n      const adapter = this._adapter = new adapters._date(scaleOpts.adapters.date);\n      adapter.init(opts);\n      mergeIf(time.displayFormats, adapter.formats());\n      this._parseOpts = {\n        parser: time.parser,\n        round: time.round,\n        isoWeekday: time.isoWeekday\n      };\n      super.init(scaleOpts);\n      this._normalized = opts.normalized;\n    }\n    parse(raw, index) {\n      if (raw === undefined) {\n        return null;\n      }\n      return parse(this, raw);\n    }\n    beforeLayout() {\n      super.beforeLayout();\n      this._cache = {\n        data: [],\n        labels: [],\n        all: []\n      };\n    }\n    determineDataLimits() {\n      const options = this.options;\n      const adapter = this._adapter;\n      const unit = options.time.unit || 'day';\n      let {\n        min,\n        max,\n        minDefined,\n        maxDefined\n      } = this.getUserBounds();\n      function _applyBounds(bounds) {\n        if (!minDefined && !isNaN(bounds.min)) {\n          min = Math.min(min, bounds.min);\n        }\n        if (!maxDefined && !isNaN(bounds.max)) {\n          max = Math.max(max, bounds.max);\n        }\n      }\n      if (!minDefined || !maxDefined) {\n        _applyBounds(this._getLabelBounds());\n        if (options.bounds !== 'ticks' || options.ticks.source !== 'labels') {\n          _applyBounds(this.getMinMax(false));\n        }\n      }\n      min = isNumberFinite(min) && !isNaN(min) ? min : +adapter.startOf(Date.now(), unit);\n      max = isNumberFinite(max) && !isNaN(max) ? max : +adapter.endOf(Date.now(), unit) + 1;\n      this.min = Math.min(min, max - 1);\n      this.max = Math.max(min + 1, max);\n    }\n    _getLabelBounds() {\n      const arr = this.getLabelTimestamps();\n      let min = Number.POSITIVE_INFINITY;\n      let max = Number.NEGATIVE_INFINITY;\n      if (arr.length) {\n        min = arr[0];\n        max = arr[arr.length - 1];\n      }\n      return {\n        min,\n        max\n      };\n    }\n    buildTicks() {\n      const options = this.options;\n      const timeOpts = options.time;\n      const tickOpts = options.ticks;\n      const timestamps = tickOpts.source === 'labels' ? this.getLabelTimestamps() : this._generate();\n      if (options.bounds === 'ticks' && timestamps.length) {\n        this.min = this._userMin || timestamps[0];\n        this.max = this._userMax || timestamps[timestamps.length - 1];\n      }\n      const min = this.min;\n      const max = this.max;\n      const ticks = _filterBetween(timestamps, min, max);\n      this._unit = timeOpts.unit || (tickOpts.autoSkip ? determineUnitForAutoTicks(timeOpts.minUnit, this.min, this.max, this._getLabelCapacity(min)) : determineUnitForFormatting(this, ticks.length, timeOpts.minUnit, this.min, this.max));\n      this._majorUnit = !tickOpts.major.enabled || this._unit === 'year' ? undefined : determineMajorUnit(this._unit);\n      this.initOffsets(timestamps);\n      if (options.reverse) {\n        ticks.reverse();\n      }\n      return ticksFromTimestamps(this, ticks, this._majorUnit);\n    }\n    afterAutoSkip() {\n      if (this.options.offsetAfterAutoskip) {\n        this.initOffsets(this.ticks.map(tick => +tick.value));\n      }\n    }\n    initOffsets(timestamps = []) {\n      let start = 0;\n      let end = 0;\n      let first, last;\n      if (this.options.offset && timestamps.length) {\n        first = this.getDecimalForValue(timestamps[0]);\n        if (timestamps.length === 1) {\n          start = 1 - first;\n        } else {\n          start = (this.getDecimalForValue(timestamps[1]) - first) / 2;\n        }\n        last = this.getDecimalForValue(timestamps[timestamps.length - 1]);\n        if (timestamps.length === 1) {\n          end = last;\n        } else {\n          end = (last - this.getDecimalForValue(timestamps[timestamps.length - 2])) / 2;\n        }\n      }\n      const limit = timestamps.length < 3 ? 0.5 : 0.25;\n      start = _limitValue(start, 0, limit);\n      end = _limitValue(end, 0, limit);\n      this._offsets = {\n        start,\n        end,\n        factor: 1 / (start + 1 + end)\n      };\n    }\n    _generate() {\n      const adapter = this._adapter;\n      const min = this.min;\n      const max = this.max;\n      const options = this.options;\n      const timeOpts = options.time;\n      const minor = timeOpts.unit || determineUnitForAutoTicks(timeOpts.minUnit, min, max, this._getLabelCapacity(min));\n      const stepSize = valueOrDefault(options.ticks.stepSize, 1);\n      const weekday = minor === 'week' ? timeOpts.isoWeekday : false;\n      const hasWeekday = isNumber(weekday) || weekday === true;\n      const ticks = {};\n      let first = min;\n      let time, count;\n      if (hasWeekday) {\n        first = +adapter.startOf(first, 'isoWeek', weekday);\n      }\n      first = +adapter.startOf(first, hasWeekday ? 'day' : minor);\n      if (adapter.diff(max, min, minor) > 100000 * stepSize) {\n        throw new Error(min + ' and ' + max + ' are too far apart with stepSize of ' + stepSize + ' ' + minor);\n      }\n      const timestamps = options.ticks.source === 'data' && this.getDataTimestamps();\n      for (time = first, count = 0; time < max; time = +adapter.add(time, stepSize, minor), count++) {\n        addTick(ticks, time, timestamps);\n      }\n      if (time === max || options.bounds === 'ticks' || count === 1) {\n        addTick(ticks, time, timestamps);\n      }\n      return Object.keys(ticks).sort(sorter).map(x => +x);\n    }\n    getLabelForValue(value) {\n      const adapter = this._adapter;\n      const timeOpts = this.options.time;\n      if (timeOpts.tooltipFormat) {\n        return adapter.format(value, timeOpts.tooltipFormat);\n      }\n      return adapter.format(value, timeOpts.displayFormats.datetime);\n    }\n    format(value, format) {\n      const options = this.options;\n      const formats = options.time.displayFormats;\n      const unit = this._unit;\n      const fmt = format || formats[unit];\n      return this._adapter.format(value, fmt);\n    }\n    _tickFormatFunction(time, index, ticks, format) {\n      const options = this.options;\n      const formatter = options.ticks.callback;\n      if (formatter) {\n        return callback(formatter, [time, index, ticks], this);\n      }\n      const formats = options.time.displayFormats;\n      const unit = this._unit;\n      const majorUnit = this._majorUnit;\n      const minorFormat = unit && formats[unit];\n      const majorFormat = majorUnit && formats[majorUnit];\n      const tick = ticks[index];\n      const major = majorUnit && majorFormat && tick && tick.major;\n      return this._adapter.format(time, format || (major ? majorFormat : minorFormat));\n    }\n    generateTickLabels(ticks) {\n      let i, ilen, tick;\n      for (i = 0, ilen = ticks.length; i < ilen; ++i) {\n        tick = ticks[i];\n        tick.label = this._tickFormatFunction(tick.value, i, ticks);\n      }\n    }\n    getDecimalForValue(value) {\n      return value === null ? NaN : (value - this.min) / (this.max - this.min);\n    }\n    getPixelForValue(value) {\n      const offsets = this._offsets;\n      const pos = this.getDecimalForValue(value);\n      return this.getPixelForDecimal((offsets.start + pos) * offsets.factor);\n    }\n    getValueForPixel(pixel) {\n      const offsets = this._offsets;\n      const pos = this.getDecimalForPixel(pixel) / offsets.factor - offsets.end;\n      return this.min + pos * (this.max - this.min);\n    }\n    _getLabelSize(label) {\n      const ticksOpts = this.options.ticks;\n      const tickLabelWidth = this.ctx.measureText(label).width;\n      const angle = toRadians(this.isHorizontal() ? ticksOpts.maxRotation : ticksOpts.minRotation);\n      const cosRotation = Math.cos(angle);\n      const sinRotation = Math.sin(angle);\n      const tickFontSize = this._resolveTickFontOptions(0).size;\n      return {\n        w: tickLabelWidth * cosRotation + tickFontSize * sinRotation,\n        h: tickLabelWidth * sinRotation + tickFontSize * cosRotation\n      };\n    }\n    _getLabelCapacity(exampleTime) {\n      const timeOpts = this.options.time;\n      const displayFormats = timeOpts.displayFormats;\n      const format = displayFormats[timeOpts.unit] || displayFormats.millisecond;\n      const exampleLabel = this._tickFormatFunction(exampleTime, 0, ticksFromTimestamps(this, [exampleTime], this._majorUnit), format);\n      const size = this._getLabelSize(exampleLabel);\n      const capacity = Math.floor(this.isHorizontal() ? this.width / size.w : this.height / size.h) - 1;\n      return capacity > 0 ? capacity : 1;\n    }\n    getDataTimestamps() {\n      let timestamps = this._cache.data || [];\n      let i, ilen;\n      if (timestamps.length) {\n        return timestamps;\n      }\n      const metas = this.getMatchingVisibleMetas();\n      if (this._normalized && metas.length) {\n        return this._cache.data = metas[0].controller.getAllParsedValues(this);\n      }\n      for (i = 0, ilen = metas.length; i < ilen; ++i) {\n        timestamps = timestamps.concat(metas[i].controller.getAllParsedValues(this));\n      }\n      return this._cache.data = this.normalize(timestamps);\n    }\n    getLabelTimestamps() {\n      const timestamps = this._cache.labels || [];\n      let i, ilen;\n      if (timestamps.length) {\n        return timestamps;\n      }\n      const labels = this.getLabels();\n      for (i = 0, ilen = labels.length; i < ilen; ++i) {\n        timestamps.push(parse(this, labels[i]));\n      }\n      return this._cache.labels = this._normalized ? timestamps : this.normalize(timestamps);\n    }\n    normalize(values) {\n      return _arrayUnique(values.sort(sorter));\n    }\n  }\n  return TimeScale;\n})();\nfunction interpolate(table, val, reverse) {\n  let lo = 0;\n  let hi = table.length - 1;\n  let prevSource, nextSource, prevTarget, nextTarget;\n  if (reverse) {\n    if (val >= table[lo].pos && val <= table[hi].pos) {\n      ({\n        lo,\n        hi\n      } = _lookupByKey(table, 'pos', val));\n    }\n    ({\n      pos: prevSource,\n      time: prevTarget\n    } = table[lo]);\n    ({\n      pos: nextSource,\n      time: nextTarget\n    } = table[hi]);\n  } else {\n    if (val >= table[lo].time && val <= table[hi].time) {\n      ({\n        lo,\n        hi\n      } = _lookupByKey(table, 'time', val));\n    }\n    ({\n      time: prevSource,\n      pos: prevTarget\n    } = table[lo]);\n    ({\n      time: nextSource,\n      pos: nextTarget\n    } = table[hi]);\n  }\n  const span = nextSource - prevSource;\n  return span ? prevTarget + (nextTarget - prevTarget) * (val - prevSource) / span : prevTarget;\n}\nclass TimeSeriesScale extends TimeScale {\n  static id = 'timeseries';\n  static defaults = TimeScale.defaults;\n  constructor(props) {\n    super(props);\n    this._table = [];\n    this._minPos = undefined;\n    this._tableRange = undefined;\n  }\n  initOffsets() {\n    const timestamps = this._getTimestampsForTable();\n    const table = this._table = this.buildLookupTable(timestamps);\n    this._minPos = interpolate(table, this.min);\n    this._tableRange = interpolate(table, this.max) - this._minPos;\n    super.initOffsets(timestamps);\n  }\n  buildLookupTable(timestamps) {\n    const {\n      min,\n      max\n    } = this;\n    const items = [];\n    const table = [];\n    let i, ilen, prev, curr, next;\n    for (i = 0, ilen = timestamps.length; i < ilen; ++i) {\n      curr = timestamps[i];\n      if (curr >= min && curr <= max) {\n        items.push(curr);\n      }\n    }\n    if (items.length < 2) {\n      return [{\n        time: min,\n        pos: 0\n      }, {\n        time: max,\n        pos: 1\n      }];\n    }\n    for (i = 0, ilen = items.length; i < ilen; ++i) {\n      next = items[i + 1];\n      prev = items[i - 1];\n      curr = items[i];\n      if (Math.round((next + prev) / 2) !== curr) {\n        table.push({\n          time: curr,\n          pos: i / (ilen - 1)\n        });\n      }\n    }\n    return table;\n  }\n  _generate() {\n    const min = this.min;\n    const max = this.max;\n    let timestamps = super.getDataTimestamps();\n    if (!timestamps.includes(min) || !timestamps.length) {\n      timestamps.splice(0, 0, min);\n    }\n    if (!timestamps.includes(max) || timestamps.length === 1) {\n      timestamps.push(max);\n    }\n    return timestamps.sort((a, b) => a - b);\n  }\n  _getTimestampsForTable() {\n    let timestamps = this._cache.all || [];\n    if (timestamps.length) {\n      return timestamps;\n    }\n    const data = this.getDataTimestamps();\n    const label = this.getLabelTimestamps();\n    if (data.length && label.length) {\n      timestamps = this.normalize(data.concat(label));\n    } else {\n      timestamps = data.length ? data : label;\n    }\n    timestamps = this._cache.all = timestamps;\n    return timestamps;\n  }\n  getDecimalForValue(value) {\n    return (interpolate(this._table, value) - this._minPos) / this._tableRange;\n  }\n  getValueForPixel(pixel) {\n    const offsets = this._offsets;\n    const decimal = this.getDecimalForPixel(pixel) / offsets.factor - offsets.end;\n    return interpolate(this._table, decimal * this._tableRange + this._minPos, true);\n  }\n}\nvar scales = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  CategoryScale: CategoryScale,\n  LinearScale: LinearScale,\n  LogarithmicScale: LogarithmicScale,\n  RadialLinearScale: RadialLinearScale,\n  TimeScale: TimeScale,\n  TimeSeriesScale: TimeSeriesScale\n});\nconst registerables = [controllers, elements, plugins, scales];\nexport { Animation, Animations, ArcElement, BarController, BarElement, BasePlatform, BasicPlatform, BubbleController, CategoryScale, Chart, plugin_colors as Colors, DatasetController, plugin_decimation as Decimation, DomPlatform, DoughnutController, Element, index as Filler, Interaction, plugin_legend as Legend, LineController, LineElement, LinearScale, LogarithmicScale, PieController, PointElement, PolarAreaController, RadarController, RadialLinearScale, Scale, ScatterController, plugin_subtitle as SubTitle, Ticks, TimeScale, TimeSeriesScale, plugin_title as Title, plugin_tooltip as Tooltip, adapters as _adapters, _detectPlatform, animator, controllers, defaults, elements, layouts, plugins, registerables, registry, scales };\n//# sourceMappingURL=chart.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}