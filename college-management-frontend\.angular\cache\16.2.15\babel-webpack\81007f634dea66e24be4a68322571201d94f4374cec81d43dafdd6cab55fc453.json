{"ast": null, "code": "import Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/auth.service\";\nimport * as i2 from \"src/app/services/user.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/material/icon\";\nfunction UserListComponent_option_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const role_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", role_r4 === \"All\" ? \"\" : role_r4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(role_r4);\n  }\n}\nconst _c0 = function (a0, a1, a2, a3) {\n  return {\n    \"badge-student\": a0,\n    \"badge-teacher\": a1,\n    \"badge-principal\": a2,\n    \"badge-admin\": a3\n  };\n};\nconst _c1 = function (a0, a1) {\n  return {\n    \"badge-active\": a0,\n    \"badge-inactive\": a1\n  };\n};\nfunction UserListComponent_div_16_tr_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\")(6, \"span\", 23);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\")(15, \"span\", 23);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"td\")(18, \"div\", 24)(19, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function UserListComponent_div_16_tr_22_Template_button_click_19_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const user_r6 = restoredCtx.$implicit;\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.editUser(user_r6));\n    });\n    i0.ɵɵelementStart(20, \"mat-icon\");\n    i0.ɵɵtext(21, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function UserListComponent_div_16_tr_22_Template_button_click_22_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const user_r6 = restoredCtx.$implicit;\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.deleteUser(user_r6));\n    });\n    i0.ɵɵelementStart(23, \"mat-icon\");\n    i0.ɵɵtext(24, \"delete\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const user_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r6.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(9, _c0, user_r6.role === \"Student\", user_r6.role === \"Teacher\", user_r6.role === \"Principal\", user_r6.role === \"Admin\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", user_r6.role, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r6.regNo || user_r6.rollNo || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r6.department || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r6.contact || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(14, _c1, user_r6.isActive, !user_r6.isActive));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", user_r6.isActive ? \"Active\" : \"Inactive\", \" \");\n  }\n}\nfunction UserListComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"table\")(3, \"thead\")(4, \"tr\", 16)(5, \"th\");\n    i0.ɵɵtext(6, \"Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"Role\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\");\n    i0.ɵɵtext(12, \"Reg/Roll No\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\");\n    i0.ɵɵtext(14, \"Department\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"th\");\n    i0.ɵɵtext(16, \"Contact\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"th\");\n    i0.ɵɵtext(18, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"th\");\n    i0.ɵɵtext(20, \"Actions\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"tbody\");\n    i0.ɵɵtemplate(22, UserListComponent_div_16_tr_22_Template, 25, 17, \"tr\", 17);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"div\", 18)(24, \"div\");\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 19)(27, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function UserListComponent_div_16_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.previousPage());\n    });\n    i0.ɵɵtext(28, \" Previous \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"span\", 21);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function UserListComponent_div_16_Template_button_click_31_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.nextPage());\n    });\n    i0.ɵɵtext(32, \" Next \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(22);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.paginatedUsers);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate3(\" Showing \", (ctx_r1.currentPage - 1) * ctx_r1.itemsPerPage + 1, \" to \", ctx_r1.currentPage * ctx_r1.itemsPerPage > ctx_r1.filteredUsers.length ? ctx_r1.filteredUsers.length : ctx_r1.currentPage * ctx_r1.itemsPerPage, \" of \", ctx_r1.filteredUsers.length, \" users \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentPage === 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"Page \", ctx_r1.currentPage, \" of \", ctx_r1.totalPages, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentPage === ctx_r1.totalPages);\n  }\n}\nfunction UserListComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 28)(2, \"span\", 29);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 30);\n    i0.ɵɵtext(5, \"Loading users...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserListComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"mat-icon\", 31);\n    i0.ɵɵtext(2, \"people\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\", 32);\n    i0.ɵɵtext(4, \"No users found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 33);\n    i0.ɵɵtext(6, \"Try adjusting your search criteria or add a new user.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class UserListComponent {\n  constructor(authService, userService, router) {\n    this.authService = authService;\n    this.userService = userService;\n    this.router = router;\n    this.users = [];\n    this.filteredUsers = [];\n    this.searchQuery = '';\n    this.selectedRole = '';\n    this.currentPage = 1;\n    this.itemsPerPage = 10;\n    this.loading = false;\n    this.roles = ['All', 'Student', 'Teacher', 'Principal', 'Admin'];\n  }\n  ngOnInit() {\n    this.loadUsers();\n  }\n  loadUsers() {\n    this.loading = true;\n    this.authService.getAllUsers().subscribe({\n      next: response => {\n        this.loading = false;\n        if (response.success) {\n          this.users = response.users || [];\n          this.filteredUsers = [...this.users];\n        }\n      },\n      error: error => {\n        this.loading = false;\n        console.error('Error loading users:', error);\n        Swal.fire({\n          title: 'Error',\n          text: 'Failed to load users. Please try again.',\n          icon: 'error',\n          confirmButtonColor: '#29578c'\n        });\n      }\n    });\n  }\n  filterUsers() {\n    this.filteredUsers = this.users.filter(user => {\n      const matchesSearch = !this.searchQuery || user.name.toLowerCase().includes(this.searchQuery.toLowerCase()) || user.email.toLowerCase().includes(this.searchQuery.toLowerCase()) || user.regNo && user.regNo.toLowerCase().includes(this.searchQuery.toLowerCase()) || user.rollNo && user.rollNo.toLowerCase().includes(this.searchQuery.toLowerCase());\n      const matchesRole = !this.selectedRole || this.selectedRole === 'All' || user.role === this.selectedRole;\n      return matchesSearch && matchesRole;\n    });\n    this.currentPage = 1;\n  }\n  onSearchChange() {\n    this.filterUsers();\n  }\n  onRoleChange() {\n    this.filterUsers();\n  }\n  get paginatedUsers() {\n    const startIndex = (this.currentPage - 1) * this.itemsPerPage;\n    const endIndex = startIndex + this.itemsPerPage;\n    return this.filteredUsers.slice(startIndex, endIndex);\n  }\n  get totalPages() {\n    return Math.ceil(this.filteredUsers.length / this.itemsPerPage);\n  }\n  nextPage() {\n    if (this.currentPage < this.totalPages) {\n      this.currentPage++;\n    }\n  }\n  previousPage() {\n    if (this.currentPage > 1) {\n      this.currentPage--;\n    }\n  }\n  editUser(user) {\n    this.router.navigate(['/dashboard/admin/users/edit-user', user._id]);\n  }\n  deleteUser(user) {\n    Swal.fire({\n      title: 'Are you sure?',\n      text: `Do you want to delete ${user.name}? This action cannot be undone.`,\n      icon: 'warning',\n      showCancelButton: true,\n      confirmButtonColor: '#d33',\n      cancelButtonColor: '#29578c',\n      confirmButtonText: 'Yes, delete it!'\n    }).then(result => {\n      if (result.isConfirmed) {\n        this.performDelete(user);\n      }\n    });\n  }\n  performDelete(user) {\n    if (!user._id) return;\n    this.authService.deleteUser(user._id).subscribe({\n      next: response => {\n        if (response.success) {\n          Swal.fire({\n            title: 'Deleted!',\n            text: `${user.name} has been deleted successfully.`,\n            icon: 'success',\n            confirmButtonColor: '#29578c'\n          });\n          this.loadUsers(); // Reload the users list\n        }\n      },\n\n      error: error => {\n        console.error('Error deleting user:', error);\n        Swal.fire({\n          title: 'Error',\n          text: 'Failed to delete user. Please try again.',\n          icon: 'error',\n          confirmButtonColor: '#29578c'\n        });\n      }\n    });\n  }\n  addNewUser() {\n    this.router.navigate(['/dashboard/admin/users/add-user']);\n  }\n  static #_ = this.ɵfac = function UserListComponent_Factory(t) {\n    return new (t || UserListComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.Router));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: UserListComponent,\n    selectors: [[\"app-user-list\"]],\n    decls: 19,\n    vars: 6,\n    consts: [[1, \"maindiv\"], [1, \"secondarydiv\"], [1, \"my-3\", \"d-flex\", \"justify-content-between\", \"searchAndtab\"], [1, \"d-flex\", \"flex-wrap\", \"gap-3\"], [1, \"btn\", \"btn-top\", \"border\", \"d-flex\", \"align-items-center\", 3, \"click\"], [1, \"form-select\", 2, \"width\", \"auto\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"search-container\"], [1, \"search-input-wrapper\"], [\"type\", \"text\", \"placeholder\", \"Search users...\", 1, \"search-input\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [1, \"search-icon\"], [\"class\", \"table-container\", 4, \"ngIf\"], [\"class\", \"text-center py-5\", 4, \"ngIf\"], [3, \"value\"], [1, \"table-container\"], [1, \"table-responsive\"], [1, \"tablehead\"], [4, \"ngFor\", \"ngForOf\"], [1, \"pagination-container\", \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mt-3\"], [1, \"pagination\"], [1, \"btn\", \"btn-outline-secondary\", \"me-2\", 3, \"disabled\", \"click\"], [1, \"page-info\"], [1, \"btn\", \"btn-outline-secondary\", \"ms-2\", 3, \"disabled\", \"click\"], [1, \"badge\", 3, \"ngClass\"], [1, \"action-buttons\"], [\"title\", \"Edit User\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", \"me-2\", 3, \"click\"], [\"title\", \"Delete User\", 1, \"btn\", \"btn-sm\", \"btn-outline-danger\", 3, \"click\"], [1, \"text-center\", \"py-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-2\"], [2, \"font-size\", \"48px\", \"color\", \"#ccc\"], [1, \"mt-3\"], [1, \"text-muted\"]],\n    template: function UserListComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"button\", 4);\n        i0.ɵɵlistener(\"click\", function UserListComponent_Template_button_click_4_listener() {\n          return ctx.addNewUser();\n        });\n        i0.ɵɵelementStart(5, \"mat-icon\");\n        i0.ɵɵtext(6, \"add\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(7, \"\\u00A0 Add New User \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"select\", 5);\n        i0.ɵɵlistener(\"ngModelChange\", function UserListComponent_Template_select_ngModelChange_8_listener($event) {\n          return ctx.selectedRole = $event;\n        })(\"change\", function UserListComponent_Template_select_change_8_listener() {\n          return ctx.onRoleChange();\n        });\n        i0.ɵɵtemplate(9, UserListComponent_option_9_Template, 2, 2, \"option\", 6);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\")(11, \"div\", 7)(12, \"div\", 8)(13, \"input\", 9);\n        i0.ɵɵlistener(\"ngModelChange\", function UserListComponent_Template_input_ngModelChange_13_listener($event) {\n          return ctx.searchQuery = $event;\n        })(\"input\", function UserListComponent_Template_input_input_13_listener() {\n          return ctx.onSearchChange();\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(14, \"mat-icon\", 10);\n        i0.ɵɵtext(15, \"search\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵtemplate(16, UserListComponent_div_16_Template, 33, 8, \"div\", 11);\n        i0.ɵɵtemplate(17, UserListComponent_div_17_Template, 6, 0, \"div\", 12);\n        i0.ɵɵtemplate(18, UserListComponent_div_18_Template, 7, 0, \"div\", 12);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"ngModel\", ctx.selectedRole);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngForOf\", ctx.roles);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.filteredUsers.length === 0);\n      }\n    },\n    dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.DefaultValueAccessor, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgModel, i6.MatIcon],\n    styles: [\".maindiv[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background-color: #f8f9fa;\\n  min-height: 100vh;\\n}\\n\\n.secondarydiv[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 8px;\\n  padding: 20px;\\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\\n}\\n\\n.searchAndtab[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.btn-top[_ngcontent-%COMP%] {\\n  background-color: #29578c;\\n  color: white;\\n  border: none;\\n  padding: 8px 16px;\\n  border-radius: 4px;\\n  font-size: 14px;\\n}\\n\\n.btn-top[_ngcontent-%COMP%]:hover {\\n  background-color: #1e3f63;\\n  color: white;\\n}\\n\\n.search-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.search-input-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.search-input[_ngcontent-%COMP%] {\\n  padding: 8px 40px 8px 12px;\\n  border: 1px solid #ddd;\\n  border-radius: 4px;\\n  width: 250px;\\n  font-size: 14px;\\n}\\n\\n.search-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 12px;\\n  color: #666;\\n  font-size: 18px;\\n}\\n\\n.table-container[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n}\\n\\ntable[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: collapse;\\n  margin-top: 10px;\\n}\\n\\n.tablehead[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border-bottom: 2px solid #dee2e6;\\n}\\n\\n.tablehead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  padding: 12px 8px;\\n  text-align: left;\\n  font-weight: 600;\\n  color: #495057;\\n  font-size: 14px;\\n}\\n\\ntbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #dee2e6;\\n}\\n\\ntbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n\\ntbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 12px 8px;\\n  font-size: 14px;\\n  color: #495057;\\n}\\n\\n.badge[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 12px;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n}\\n\\n.badge-student[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1976d2;\\n}\\n\\n.badge-teacher[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n}\\n\\n.badge-principal[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #f57c00;\\n}\\n\\n.badge-admin[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #d32f2f;\\n}\\n\\n.badge-active[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #2e7d32;\\n}\\n\\n.badge-inactive[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #d32f2f;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 5px;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  border-radius: 4px;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.pagination-container[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  padding-top: 20px;\\n  border-top: 1px solid #dee2e6;\\n}\\n\\n.pagination[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n\\n.page-info[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  background-color: #f8f9fa;\\n  border-radius: 4px;\\n  font-size: 14px;\\n}\\n\\n.form-select[_ngcontent-%COMP%] {\\n  padding: 6px 12px;\\n  border: 1px solid #ddd;\\n  border-radius: 4px;\\n  font-size: 14px;\\n  background-color: white;\\n}\\n\\n.spinner-border[_ngcontent-%COMP%] {\\n  width: 3rem;\\n  height: 3rem;\\n}\\n\\n@media (max-width: 768px) {\\n  .searchAndtab[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 15px;\\n  }\\n  \\n  .search-input[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  \\n  .pagination-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 15px;\\n    text-align: center;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "role_r4", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵlistener", "UserListComponent_div_16_tr_22_Template_button_click_19_listener", "restoredCtx", "ɵɵrestoreView", "_r9", "user_r6", "$implicit", "ctx_r8", "ɵɵnextContext", "ɵɵresetView", "editUser", "UserListComponent_div_16_tr_22_Template_button_click_22_listener", "ctx_r10", "deleteUser", "name", "email", "ɵɵpureFunction4", "_c0", "role", "ɵɵtextInterpolate1", "regNo", "rollNo", "department", "contact", "ɵɵpureFunction2", "_c1", "isActive", "ɵɵtemplate", "UserListComponent_div_16_tr_22_Template", "UserListComponent_div_16_Template_button_click_27_listener", "_r12", "ctx_r11", "previousPage", "UserListComponent_div_16_Template_button_click_31_listener", "ctx_r13", "nextPage", "ctx_r1", "paginatedUsers", "ɵɵtextInterpolate3", "currentPage", "itemsPerPage", "filteredUsers", "length", "ɵɵtextInterpolate2", "totalPages", "UserListComponent", "constructor", "authService", "userService", "router", "users", "searchQuery", "selectedR<PERSON>", "loading", "roles", "ngOnInit", "loadUsers", "getAllUsers", "subscribe", "next", "response", "success", "error", "console", "fire", "title", "text", "icon", "confirmButtonColor", "filterUsers", "filter", "user", "matchesSearch", "toLowerCase", "includes", "matchesRole", "onSearchChange", "onRoleChange", "startIndex", "endIndex", "slice", "Math", "ceil", "navigate", "_id", "showCancelButton", "cancelButtonColor", "confirmButtonText", "then", "result", "isConfirmed", "performDelete", "addNewUser", "_", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "UserService", "i3", "Router", "_2", "selectors", "decls", "vars", "consts", "template", "UserListComponent_Template", "rf", "ctx", "UserListComponent_Template_button_click_4_listener", "UserListComponent_Template_select_ngModelChange_8_listener", "$event", "UserListComponent_Template_select_change_8_listener", "UserListComponent_option_9_Template", "UserListComponent_Template_input_ngModelChange_13_listener", "UserListComponent_Template_input_input_13_listener", "UserListComponent_div_16_Template", "UserListComponent_div_17_Template", "UserListComponent_div_18_Template"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\users\\user-list\\user-list.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\users\\user-list\\user-list.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from 'src/app/services/auth.service';\nimport { UserService } from 'src/app/services/user.service';\nimport { User } from 'src/app/models/user';\nimport Swal from 'sweetalert2';\n\n@Component({\n  selector: 'app-user-list',\n  templateUrl: './user-list.component.html',\n  styleUrls: ['./user-list.component.css']\n})\nexport class UserListComponent implements OnInit {\n  users: User[] = [];\n  filteredUsers: User[] = [];\n  searchQuery: string = '';\n  selectedRole: string = '';\n  currentPage: number = 1;\n  itemsPerPage: number = 10;\n  loading: boolean = false;\n\n  roles = ['All', 'Student', 'Teacher', 'Principal', 'Admin'];\n\n  constructor(\n    private authService: AuthService,\n    private userService: UserService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.loadUsers();\n  }\n\n  loadUsers(): void {\n    this.loading = true;\n    this.authService.getAllUsers().subscribe({\n      next: (response) => {\n        this.loading = false;\n        if (response.success) {\n          this.users = response.users || [];\n          this.filteredUsers = [...this.users];\n        }\n      },\n      error: (error) => {\n        this.loading = false;\n        console.error('Error loading users:', error);\n        Swal.fire({\n          title: 'Error',\n          text: 'Failed to load users. Please try again.',\n          icon: 'error',\n          confirmButtonColor: '#29578c'\n        });\n      }\n    });\n  }\n\n  filterUsers(): void {\n    this.filteredUsers = this.users.filter(user => {\n      const matchesSearch = !this.searchQuery || \n        user.name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||\n        user.email.toLowerCase().includes(this.searchQuery.toLowerCase()) ||\n        (user.regNo && user.regNo.toLowerCase().includes(this.searchQuery.toLowerCase())) ||\n        (user.rollNo && user.rollNo.toLowerCase().includes(this.searchQuery.toLowerCase()));\n\n      const matchesRole = !this.selectedRole || this.selectedRole === 'All' || \n        user.role === this.selectedRole;\n\n      return matchesSearch && matchesRole;\n    });\n    this.currentPage = 1;\n  }\n\n  onSearchChange(): void {\n    this.filterUsers();\n  }\n\n  onRoleChange(): void {\n    this.filterUsers();\n  }\n\n  get paginatedUsers(): User[] {\n    const startIndex = (this.currentPage - 1) * this.itemsPerPage;\n    const endIndex = startIndex + this.itemsPerPage;\n    return this.filteredUsers.slice(startIndex, endIndex);\n  }\n\n  get totalPages(): number {\n    return Math.ceil(this.filteredUsers.length / this.itemsPerPage);\n  }\n\n  nextPage(): void {\n    if (this.currentPage < this.totalPages) {\n      this.currentPage++;\n    }\n  }\n\n  previousPage(): void {\n    if (this.currentPage > 1) {\n      this.currentPage--;\n    }\n  }\n\n  editUser(user: User): void {\n    this.router.navigate(['/dashboard/admin/users/edit-user', user._id]);\n  }\n\n  deleteUser(user: User): void {\n    Swal.fire({\n      title: 'Are you sure?',\n      text: `Do you want to delete ${user.name}? This action cannot be undone.`,\n      icon: 'warning',\n      showCancelButton: true,\n      confirmButtonColor: '#d33',\n      cancelButtonColor: '#29578c',\n      confirmButtonText: 'Yes, delete it!'\n    }).then((result) => {\n      if (result.isConfirmed) {\n        this.performDelete(user);\n      }\n    });\n  }\n\n  private performDelete(user: User): void {\n    if (!user._id) return;\n\n    this.authService.deleteUser(user._id).subscribe({\n      next: (response) => {\n        if (response.success) {\n          Swal.fire({\n            title: 'Deleted!',\n            text: `${user.name} has been deleted successfully.`,\n            icon: 'success',\n            confirmButtonColor: '#29578c'\n          });\n          this.loadUsers(); // Reload the users list\n        }\n      },\n      error: (error) => {\n        console.error('Error deleting user:', error);\n        Swal.fire({\n          title: 'Error',\n          text: 'Failed to delete user. Please try again.',\n          icon: 'error',\n          confirmButtonColor: '#29578c'\n        });\n      }\n    });\n  }\n\n  addNewUser(): void {\n    this.router.navigate(['/dashboard/admin/users/add-user']);\n  }\n}\n", "<div class=\"maindiv\">\n  <div class=\"secondarydiv\">\n    <div class=\"my-3 d-flex justify-content-between searchAndtab\">\n      <div class=\"d-flex flex-wrap gap-3\">\n        <button class=\"btn btn-top border d-flex align-items-center\" (click)=\"addNewUser()\">\n          <mat-icon>add</mat-icon>&nbsp; Add New User\n        </button>\n        <select class=\"form-select\" [(ngModel)]=\"selectedRole\" (change)=\"onRoleChange()\" style=\"width: auto;\">\n          <option *ngFor=\"let role of roles\" [value]=\"role === 'All' ? '' : role\">{{ role }}</option>\n        </select>\n      </div>\n      <div>\n        <div class=\"search-container\">\n          <div class=\"search-input-wrapper\">\n            <input \n              type=\"text\" \n              class=\"search-input\" \n              placeholder=\"Search users...\" \n              [(ngModel)]=\"searchQuery\"\n              (input)=\"onSearchChange()\">\n            <mat-icon class=\"search-icon\">search</mat-icon>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"table-container\" *ngIf=\"!loading\">\n      <div class=\"table-responsive\">\n        <table>\n          <thead>\n            <tr class=\"tablehead\">\n              <th>Name</th>\n              <th>Email</th>\n              <th>Role</th>\n              <th>Reg/Roll No</th>\n              <th>Department</th>\n              <th>Contact</th>\n              <th>Status</th>\n              <th>Actions</th>\n            </tr>\n          </thead>\n          <tbody>\n            <tr *ngFor=\"let user of paginatedUsers; let i = index\">\n              <td>{{ user.name }}</td>\n              <td>{{ user.email }}</td>\n              <td>\n                <span class=\"badge\" [ngClass]=\"{\n                  'badge-student': user.role === 'Student',\n                  'badge-teacher': user.role === 'Teacher',\n                  'badge-principal': user.role === 'Principal',\n                  'badge-admin': user.role === 'Admin'\n                }\">\n                  {{ user.role }}\n                </span>\n              </td>\n              <td>{{ user.regNo || user.rollNo || '-' }}</td>\n              <td>{{ user.department || '-' }}</td>\n              <td>{{ user.contact || '-' }}</td>\n              <td>\n                <span class=\"badge\" [ngClass]=\"{\n                  'badge-active': user.isActive,\n                  'badge-inactive': !user.isActive\n                }\">\n                  {{ user.isActive ? 'Active' : 'Inactive' }}\n                </span>\n              </td>\n              <td>\n                <div class=\"action-buttons\">\n                  <button \n                    class=\"btn btn-sm btn-outline-primary me-2\" \n                    (click)=\"editUser(user)\"\n                    title=\"Edit User\">\n                    <mat-icon>edit</mat-icon>\n                  </button>\n                  <button \n                    class=\"btn btn-sm btn-outline-danger\" \n                    (click)=\"deleteUser(user)\"\n                    title=\"Delete User\">\n                    <mat-icon>delete</mat-icon>\n                  </button>\n                </div>\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n\n      <!-- Pagination -->\n      <div class=\"pagination-container d-flex justify-content-between align-items-center mt-3\">\n        <div>\n          Showing {{ (currentPage - 1) * itemsPerPage + 1 }} to\n          {{ currentPage * itemsPerPage > filteredUsers.length ? filteredUsers.length : currentPage * itemsPerPage }} of\n          {{ filteredUsers.length }} users\n        </div>\n        <div class=\"pagination\">\n          <button \n            class=\"btn btn-outline-secondary me-2\" \n            (click)=\"previousPage()\" \n            [disabled]=\"currentPage === 1\">\n            Previous\n          </button>\n          <span class=\"page-info\">Page {{ currentPage }} of {{ totalPages }}</span>\n          <button \n            class=\"btn btn-outline-secondary ms-2\" \n            (click)=\"nextPage()\" \n            [disabled]=\"currentPage === totalPages\">\n            Next\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Loading State -->\n    <div *ngIf=\"loading\" class=\"text-center py-5\">\n      <div class=\"spinner-border text-primary\" role=\"status\">\n        <span class=\"visually-hidden\">Loading...</span>\n      </div>\n      <p class=\"mt-2\">Loading users...</p>\n    </div>\n\n    <!-- Empty State -->\n    <div *ngIf=\"!loading && filteredUsers.length === 0\" class=\"text-center py-5\">\n      <mat-icon style=\"font-size: 48px; color: #ccc;\">people</mat-icon>\n      <h4 class=\"mt-3\">No users found</h4>\n      <p class=\"text-muted\">Try adjusting your search criteria or add a new user.</p>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAKA,OAAOA,IAAI,MAAM,aAAa;;;;;;;;;;ICGpBC,EAAA,CAAAC,cAAA,iBAAwE;IAAAD,EAAA,CAAAE,MAAA,GAAU;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAxDH,EAAA,CAAAI,UAAA,UAAAC,OAAA,kBAAAA,OAAA,CAAoC;IAACL,EAAA,CAAAM,SAAA,GAAU;IAAVN,EAAA,CAAAO,iBAAA,CAAAF,OAAA,CAAU;;;;;;;;;;;;;;;;;;;;IAkChFL,EAAA,CAAAC,cAAA,SAAuD;IACjDD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,SAAI;IAOAD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/CH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrCH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAC,cAAA,UAAI;IAKAD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,UAAI;IAIED,EAAA,CAAAQ,UAAA,mBAAAC,iEAAA;MAAA,MAAAC,WAAA,GAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAC,OAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAShB,EAAA,CAAAiB,WAAA,CAAAF,MAAA,CAAAG,QAAA,CAAAL,OAAA,CAAc;IAAA,EAAC;IAExBb,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE3BH,EAAA,CAAAC,cAAA,kBAGsB;IADpBD,EAAA,CAAAQ,UAAA,mBAAAW,iEAAA;MAAA,MAAAT,WAAA,GAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAC,OAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAM,OAAA,GAAApB,EAAA,CAAAgB,aAAA;MAAA,OAAShB,EAAA,CAAAiB,WAAA,CAAAG,OAAA,CAAAC,UAAA,CAAAR,OAAA,CAAgB;IAAA,EAAC;IAE1Bb,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IAnC7BH,EAAA,CAAAM,SAAA,GAAe;IAAfN,EAAA,CAAAO,iBAAA,CAAAM,OAAA,CAAAS,IAAA,CAAe;IACftB,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAO,iBAAA,CAAAM,OAAA,CAAAU,KAAA,CAAgB;IAEEvB,EAAA,CAAAM,SAAA,GAKlB;IALkBN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAwB,eAAA,IAAAC,GAAA,EAAAZ,OAAA,CAAAa,IAAA,gBAAAb,OAAA,CAAAa,IAAA,gBAAAb,OAAA,CAAAa,IAAA,kBAAAb,OAAA,CAAAa,IAAA,cAKlB;IACA1B,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAA2B,kBAAA,MAAAd,OAAA,CAAAa,IAAA,MACF;IAEE1B,EAAA,CAAAM,SAAA,GAAsC;IAAtCN,EAAA,CAAAO,iBAAA,CAAAM,OAAA,CAAAe,KAAA,IAAAf,OAAA,CAAAgB,MAAA,QAAsC;IACtC7B,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAO,iBAAA,CAAAM,OAAA,CAAAiB,UAAA,QAA4B;IAC5B9B,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAO,iBAAA,CAAAM,OAAA,CAAAkB,OAAA,QAAyB;IAEP/B,EAAA,CAAAM,SAAA,GAGlB;IAHkBN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAgC,eAAA,KAAAC,GAAA,EAAApB,OAAA,CAAAqB,QAAA,GAAArB,OAAA,CAAAqB,QAAA,EAGlB;IACAlC,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAA2B,kBAAA,MAAAd,OAAA,CAAAqB,QAAA,8BACF;;;;;;IAtCZlC,EAAA,CAAAC,cAAA,cAA8C;IAKhCD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACbH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACdH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACbH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGpBH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAmC,UAAA,KAAAC,uCAAA,mBAwCK;IACPpC,EAAA,CAAAG,YAAA,EAAQ;IAKZH,EAAA,CAAAC,cAAA,eAAyF;IAErFD,EAAA,CAAAE,MAAA,IAGF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAwB;IAGpBD,EAAA,CAAAQ,UAAA,mBAAA6B,2DAAA;MAAArC,EAAA,CAAAW,aAAA,CAAA2B,IAAA;MAAA,MAAAC,OAAA,GAAAvC,EAAA,CAAAgB,aAAA;MAAA,OAAShB,EAAA,CAAAiB,WAAA,CAAAsB,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAExBxC,EAAA,CAAAE,MAAA,kBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAA0C;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzEH,EAAA,CAAAC,cAAA,kBAG0C;IADxCD,EAAA,CAAAQ,UAAA,mBAAAiC,2DAAA;MAAAzC,EAAA,CAAAW,aAAA,CAAA2B,IAAA;MAAA,MAAAI,OAAA,GAAA1C,EAAA,CAAAgB,aAAA;MAAA,OAAShB,EAAA,CAAAiB,WAAA,CAAAyB,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IAEpB3C,EAAA,CAAAE,MAAA,cACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAjEcH,EAAA,CAAAM,SAAA,IAAmB;IAAnBN,EAAA,CAAAI,UAAA,YAAAwC,MAAA,CAAAC,cAAA,CAAmB;IAgD1C7C,EAAA,CAAAM,SAAA,GAGF;IAHEN,EAAA,CAAA8C,kBAAA,eAAAF,MAAA,CAAAG,WAAA,QAAAH,MAAA,CAAAI,YAAA,cAAAJ,MAAA,CAAAG,WAAA,GAAAH,MAAA,CAAAI,YAAA,GAAAJ,MAAA,CAAAK,aAAA,CAAAC,MAAA,GAAAN,MAAA,CAAAK,aAAA,CAAAC,MAAA,GAAAN,MAAA,CAAAG,WAAA,GAAAH,MAAA,CAAAI,YAAA,UAAAJ,MAAA,CAAAK,aAAA,CAAAC,MAAA,YAGF;IAKIlD,EAAA,CAAAM,SAAA,GAA8B;IAA9BN,EAAA,CAAAI,UAAA,aAAAwC,MAAA,CAAAG,WAAA,OAA8B;IAGR/C,EAAA,CAAAM,SAAA,GAA0C;IAA1CN,EAAA,CAAAmD,kBAAA,UAAAP,MAAA,CAAAG,WAAA,UAAAH,MAAA,CAAAQ,UAAA,KAA0C;IAIhEpD,EAAA,CAAAM,SAAA,GAAuC;IAAvCN,EAAA,CAAAI,UAAA,aAAAwC,MAAA,CAAAG,WAAA,KAAAH,MAAA,CAAAQ,UAAA,CAAuC;;;;;IAQ/CpD,EAAA,CAAAC,cAAA,cAA8C;IAEZD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEjDH,EAAA,CAAAC,cAAA,YAAgB;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAItCH,EAAA,CAAAC,cAAA,cAA6E;IAC3BD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACjEH,EAAA,CAAAC,cAAA,aAAiB;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpCH,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAE,MAAA,4DAAqD;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;ADhHrF,OAAM,MAAOkD,iBAAiB;EAW5BC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc;IAFd,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAbhB,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAT,aAAa,GAAW,EAAE;IAC1B,KAAAU,WAAW,GAAW,EAAE;IACxB,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAb,WAAW,GAAW,CAAC;IACvB,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAa,OAAO,GAAY,KAAK;IAExB,KAAAC,KAAK,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC;EAMxD;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,EAAE;EAClB;EAEAA,SAASA,CAAA;IACP,IAAI,CAACH,OAAO,GAAG,IAAI;IACnB,IAAI,CAACN,WAAW,CAACU,WAAW,EAAE,CAACC,SAAS,CAAC;MACvCC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACP,OAAO,GAAG,KAAK;QACpB,IAAIO,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACX,KAAK,GAAGU,QAAQ,CAACV,KAAK,IAAI,EAAE;UACjC,IAAI,CAACT,aAAa,GAAG,CAAC,GAAG,IAAI,CAACS,KAAK,CAAC;;MAExC,CAAC;MACDY,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACT,OAAO,GAAG,KAAK;QACpBU,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5CvE,IAAI,CAACyE,IAAI,CAAC;UACRC,KAAK,EAAE,OAAO;UACdC,IAAI,EAAE,yCAAyC;UAC/CC,IAAI,EAAE,OAAO;UACbC,kBAAkB,EAAE;SACrB,CAAC;MACJ;KACD,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC5B,aAAa,GAAG,IAAI,CAACS,KAAK,CAACoB,MAAM,CAACC,IAAI,IAAG;MAC5C,MAAMC,aAAa,GAAG,CAAC,IAAI,CAACrB,WAAW,IACrCoB,IAAI,CAACzD,IAAI,CAAC2D,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACvB,WAAW,CAACsB,WAAW,EAAE,CAAC,IAChEF,IAAI,CAACxD,KAAK,CAAC0D,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACvB,WAAW,CAACsB,WAAW,EAAE,CAAC,IAChEF,IAAI,CAACnD,KAAK,IAAImD,IAAI,CAACnD,KAAK,CAACqD,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACvB,WAAW,CAACsB,WAAW,EAAE,CAAE,IAChFF,IAAI,CAAClD,MAAM,IAAIkD,IAAI,CAAClD,MAAM,CAACoD,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACvB,WAAW,CAACsB,WAAW,EAAE,CAAE;MAErF,MAAME,WAAW,GAAG,CAAC,IAAI,CAACvB,YAAY,IAAI,IAAI,CAACA,YAAY,KAAK,KAAK,IACnEmB,IAAI,CAACrD,IAAI,KAAK,IAAI,CAACkC,YAAY;MAEjC,OAAOoB,aAAa,IAAIG,WAAW;IACrC,CAAC,CAAC;IACF,IAAI,CAACpC,WAAW,GAAG,CAAC;EACtB;EAEAqC,cAAcA,CAAA;IACZ,IAAI,CAACP,WAAW,EAAE;EACpB;EAEAQ,YAAYA,CAAA;IACV,IAAI,CAACR,WAAW,EAAE;EACpB;EAEA,IAAIhC,cAAcA,CAAA;IAChB,MAAMyC,UAAU,GAAG,CAAC,IAAI,CAACvC,WAAW,GAAG,CAAC,IAAI,IAAI,CAACC,YAAY;IAC7D,MAAMuC,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAACtC,YAAY;IAC/C,OAAO,IAAI,CAACC,aAAa,CAACuC,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EACvD;EAEA,IAAInC,UAAUA,CAAA;IACZ,OAAOqC,IAAI,CAACC,IAAI,CAAC,IAAI,CAACzC,aAAa,CAACC,MAAM,GAAG,IAAI,CAACF,YAAY,CAAC;EACjE;EAEAL,QAAQA,CAAA;IACN,IAAI,IAAI,CAACI,WAAW,GAAG,IAAI,CAACK,UAAU,EAAE;MACtC,IAAI,CAACL,WAAW,EAAE;;EAEtB;EAEAP,YAAYA,CAAA;IACV,IAAI,IAAI,CAACO,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;;EAEtB;EAEA7B,QAAQA,CAAC6D,IAAU;IACjB,IAAI,CAACtB,MAAM,CAACkC,QAAQ,CAAC,CAAC,kCAAkC,EAAEZ,IAAI,CAACa,GAAG,CAAC,CAAC;EACtE;EAEAvE,UAAUA,CAAC0D,IAAU;IACnBhF,IAAI,CAACyE,IAAI,CAAC;MACRC,KAAK,EAAE,eAAe;MACtBC,IAAI,EAAE,yBAAyBK,IAAI,CAACzD,IAAI,iCAAiC;MACzEqD,IAAI,EAAE,SAAS;MACfkB,gBAAgB,EAAE,IAAI;MACtBjB,kBAAkB,EAAE,MAAM;MAC1BkB,iBAAiB,EAAE,SAAS;MAC5BC,iBAAiB,EAAE;KACpB,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAI;MACjB,IAAIA,MAAM,CAACC,WAAW,EAAE;QACtB,IAAI,CAACC,aAAa,CAACpB,IAAI,CAAC;;IAE5B,CAAC,CAAC;EACJ;EAEQoB,aAAaA,CAACpB,IAAU;IAC9B,IAAI,CAACA,IAAI,CAACa,GAAG,EAAE;IAEf,IAAI,CAACrC,WAAW,CAAClC,UAAU,CAAC0D,IAAI,CAACa,GAAG,CAAC,CAAC1B,SAAS,CAAC;MAC9CC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpBtE,IAAI,CAACyE,IAAI,CAAC;YACRC,KAAK,EAAE,UAAU;YACjBC,IAAI,EAAE,GAAGK,IAAI,CAACzD,IAAI,iCAAiC;YACnDqD,IAAI,EAAE,SAAS;YACfC,kBAAkB,EAAE;WACrB,CAAC;UACF,IAAI,CAACZ,SAAS,EAAE,CAAC,CAAC;;MAEtB,CAAC;;MACDM,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5CvE,IAAI,CAACyE,IAAI,CAAC;UACRC,KAAK,EAAE,OAAO;UACdC,IAAI,EAAE,0CAA0C;UAChDC,IAAI,EAAE,OAAO;UACbC,kBAAkB,EAAE;SACrB,CAAC;MACJ;KACD,CAAC;EACJ;EAEAwB,UAAUA,CAAA;IACR,IAAI,CAAC3C,MAAM,CAACkC,QAAQ,CAAC,CAAC,iCAAiC,CAAC,CAAC;EAC3D;EAAC,QAAAU,CAAA,G;qBA3IUhD,iBAAiB,EAAArD,EAAA,CAAAsG,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxG,EAAA,CAAAsG,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA1G,EAAA,CAAAsG,iBAAA,CAAAK,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAjBxD,iBAAiB;IAAAyD,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCZ9BpH,EAAA,CAAAC,cAAA,aAAqB;QAIgDD,EAAA,CAAAQ,UAAA,mBAAA8G,mDAAA;UAAA,OAASD,GAAA,CAAAjB,UAAA,EAAY;QAAA,EAAC;QACjFpG,EAAA,CAAAC,cAAA,eAAU;QAAAD,EAAA,CAAAE,MAAA,UAAG;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAAAH,EAAA,CAAAE,MAAA,2BAC1B;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,gBAAsG;QAA1ED,EAAA,CAAAQ,UAAA,2BAAA+G,2DAAAC,MAAA;UAAA,OAAAH,GAAA,CAAAzD,YAAA,GAAA4D,MAAA;QAAA,EAA0B,oBAAAC,oDAAA;UAAA,OAAWJ,GAAA,CAAAhC,YAAA,EAAc;QAAA,EAAzB;QACpDrF,EAAA,CAAAmC,UAAA,IAAAuF,mCAAA,oBAA2F;QAC7F1H,EAAA,CAAAG,YAAA,EAAS;QAEXH,EAAA,CAAAC,cAAA,WAAK;QAOGD,EAAA,CAAAQ,UAAA,2BAAAmH,2DAAAH,MAAA;UAAA,OAAAH,GAAA,CAAA1D,WAAA,GAAA6D,MAAA;QAAA,EAAyB,mBAAAI,mDAAA;UAAA,OAChBP,GAAA,CAAAjC,cAAA,EAAgB;QAAA,EADA;QAJ3BpF,EAAA,CAAAG,YAAA,EAK6B;QAC7BH,EAAA,CAAAC,cAAA,oBAA8B;QAAAD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAMvDH,EAAA,CAAAmC,UAAA,KAAA0F,iCAAA,mBAoFM;QAGN7H,EAAA,CAAAmC,UAAA,KAAA2F,iCAAA,kBAKM;QAGN9H,EAAA,CAAAmC,UAAA,KAAA4F,iCAAA,kBAIM;QACR/H,EAAA,CAAAG,YAAA,EAAM;;;QAvH4BH,EAAA,CAAAM,SAAA,GAA0B;QAA1BN,EAAA,CAAAI,UAAA,YAAAiH,GAAA,CAAAzD,YAAA,CAA0B;QAC3B5D,EAAA,CAAAM,SAAA,GAAQ;QAARN,EAAA,CAAAI,UAAA,YAAAiH,GAAA,CAAAvD,KAAA,CAAQ;QAU7B9D,EAAA,CAAAM,SAAA,GAAyB;QAAzBN,EAAA,CAAAI,UAAA,YAAAiH,GAAA,CAAA1D,WAAA,CAAyB;QAQL3D,EAAA,CAAAM,SAAA,GAAc;QAAdN,EAAA,CAAAI,UAAA,UAAAiH,GAAA,CAAAxD,OAAA,CAAc;QAuFtC7D,EAAA,CAAAM,SAAA,GAAa;QAAbN,EAAA,CAAAI,UAAA,SAAAiH,GAAA,CAAAxD,OAAA,CAAa;QAQb7D,EAAA,CAAAM,SAAA,GAA4C;QAA5CN,EAAA,CAAAI,UAAA,UAAAiH,GAAA,CAAAxD,OAAA,IAAAwD,GAAA,CAAApE,aAAA,CAAAC,MAAA,OAA4C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}