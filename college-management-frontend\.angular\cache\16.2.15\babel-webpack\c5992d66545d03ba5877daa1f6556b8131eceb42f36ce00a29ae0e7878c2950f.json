{"ast": null, "code": "import Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/services/classes.service\";\nimport * as i3 from \"src/app/services/user.service\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/paginator\";\nimport * as i10 from \"@angular/material/progress-spinner\";\nimport * as i11 from \"@angular/material/table\";\nimport * as i12 from \"@angular/material/tooltip\";\nfunction ViewClassComponent_p_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r0.classData.className, \" - \", ctx_r0.classData.section || \"No Section\", \"\");\n  }\n}\nfunction ViewClassComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function ViewClassComponent_div_12_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.editClass());\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Edit Class \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ViewClassComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading class details...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ViewClassComponent_div_14_div_58_div_2_p_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const subjectTeacher_r15 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", subjectTeacher_r15.teacher.email, \" \");\n  }\n}\nfunction ViewClassComponent_div_14_div_58_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 27);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 28)(7, \"p\", 29)(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, ViewClassComponent_div_14_div_58_div_2_p_11_Template, 2, 1, \"p\", 30);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const subjectTeacher_r15 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((subjectTeacher_r15.subject == null ? null : subjectTeacher_r15.subject.subjectName) || \"Unknown Subject\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Code: \", (subjectTeacher_r15.subject == null ? null : subjectTeacher_r15.subject.code) || \"N/A\", \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (subjectTeacher_r15.teacher == null ? null : subjectTeacher_r15.teacher.name) || \"No Teacher Assigned\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", subjectTeacher_r15.teacher == null ? null : subjectTeacher_r15.teacher.email);\n  }\n}\nfunction ViewClassComponent_div_14_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 23);\n    i0.ɵɵtemplate(2, ViewClassComponent_div_14_div_58_div_2_Template, 12, 4, \"div\", 24);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.classData.subjects);\n  }\n}\nfunction ViewClassComponent_div_14_ng_template_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"subject\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No subjects assigned to this class yet.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ViewClassComponent_div_14_div_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵelement(1, \"mat-spinner\", 33);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading students...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ViewClassComponent_div_14_div_69_th_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 46);\n    i0.ɵɵtext(1, \"Name\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewClassComponent_div_14_div_69_td_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const student_r31 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(student_r31.name);\n  }\n}\nfunction ViewClassComponent_div_14_div_69_th_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 46);\n    i0.ɵɵtext(1, \"Roll Number\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewClassComponent_div_14_div_69_td_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const student_r32 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(student_r32.rollNo || \"N/A\");\n  }\n}\nfunction ViewClassComponent_div_14_div_69_th_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 46);\n    i0.ɵɵtext(1, \"Registration Number\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewClassComponent_div_14_div_69_td_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const student_r33 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(student_r33.regNo || \"N/A\");\n  }\n}\nfunction ViewClassComponent_div_14_div_69_th_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 46);\n    i0.ɵɵtext(1, \"Email\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewClassComponent_div_14_div_69_td_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const student_r34 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(student_r34.email);\n  }\n}\nfunction ViewClassComponent_div_14_div_69_th_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 46);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewClassComponent_div_14_div_69_td_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 47)(1, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function ViewClassComponent_div_14_div_69_td_17_Template_button_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r37);\n      const student_r35 = restoredCtx.$implicit;\n      const ctx_r36 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r36.viewStudent(student_r35._id));\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"visibility\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ViewClassComponent_div_14_div_69_tr_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 49);\n  }\n}\nfunction ViewClassComponent_div_14_div_69_tr_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 50);\n  }\n}\nconst _c0 = function () {\n  return [5, 10, 20];\n};\nfunction ViewClassComponent_div_14_div_69_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r40 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"mat-paginator\", 52);\n    i0.ɵɵlistener(\"page\", function ViewClassComponent_div_14_div_69_div_20_Template_mat_paginator_page_1_listener($event) {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r39 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r39.currentPage = $event.pageIndex + 1);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r30 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"length\", ctx_r30.students.length)(\"pageSize\", ctx_r30.itemsPerPage)(\"pageSizeOptions\", i0.ɵɵpureFunction0(3, _c0));\n  }\n}\nconst _c1 = function () {\n  return [\"name\", \"rollNo\", \"regNo\", \"email\", \"actions\"];\n};\nfunction ViewClassComponent_div_14_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 34)(2, \"table\", 35);\n    i0.ɵɵelementContainerStart(3, 36);\n    i0.ɵɵtemplate(4, ViewClassComponent_div_14_div_69_th_4_Template, 2, 0, \"th\", 37);\n    i0.ɵɵtemplate(5, ViewClassComponent_div_14_div_69_td_5_Template, 2, 1, \"td\", 38);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(6, 39);\n    i0.ɵɵtemplate(7, ViewClassComponent_div_14_div_69_th_7_Template, 2, 0, \"th\", 37);\n    i0.ɵɵtemplate(8, ViewClassComponent_div_14_div_69_td_8_Template, 2, 1, \"td\", 38);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(9, 40);\n    i0.ɵɵtemplate(10, ViewClassComponent_div_14_div_69_th_10_Template, 2, 0, \"th\", 37);\n    i0.ɵɵtemplate(11, ViewClassComponent_div_14_div_69_td_11_Template, 2, 1, \"td\", 38);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(12, 41);\n    i0.ɵɵtemplate(13, ViewClassComponent_div_14_div_69_th_13_Template, 2, 0, \"th\", 37);\n    i0.ɵɵtemplate(14, ViewClassComponent_div_14_div_69_td_14_Template, 2, 1, \"td\", 38);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(15, 42);\n    i0.ɵɵtemplate(16, ViewClassComponent_div_14_div_69_th_16_Template, 2, 0, \"th\", 37);\n    i0.ɵɵtemplate(17, ViewClassComponent_div_14_div_69_td_17_Template, 4, 0, \"td\", 38);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(18, ViewClassComponent_div_14_div_69_tr_18_Template, 1, 0, \"tr\", 43);\n    i0.ɵɵtemplate(19, ViewClassComponent_div_14_div_69_tr_19_Template, 1, 0, \"tr\", 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(20, ViewClassComponent_div_14_div_69_div_20_Template, 2, 4, \"div\", 45);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"dataSource\", ctx_r11.paginatedStudents);\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"matHeaderRowDef\", i0.ɵɵpureFunction0(4, _c1));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matRowDefColumns\", i0.ɵɵpureFunction0(5, _c1));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.totalPages > 1);\n  }\n}\nfunction ViewClassComponent_div_14_ng_template_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"groups\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No students enrolled in this class yet.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ViewClassComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"mat-card\", 17)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" Basic Information \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"div\", 18)(9, \"div\", 19)(10, \"label\");\n    i0.ɵɵtext(11, \"Class Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"h3\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 19)(15, \"label\");\n    i0.ɵɵtext(16, \"Section\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 19)(20, \"label\");\n    i0.ɵɵtext(21, \"Program\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"h3\");\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"small\");\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 19)(27, \"label\");\n    i0.ɵɵtext(28, \"Department\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"h3\");\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 19)(32, \"label\");\n    i0.ɵɵtext(33, \"Semester\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"h3\");\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"div\", 19)(37, \"label\");\n    i0.ɵɵtext(38, \"Academic Year\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"p\");\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"div\", 19)(42, \"label\");\n    i0.ɵɵtext(43, \"Maximum Students\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"p\");\n    i0.ɵɵtext(45);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 19)(47, \"label\");\n    i0.ɵɵtext(48, \"Current Students\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"p\");\n    i0.ɵɵtext(50);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(51, \"mat-card\", 17)(52, \"mat-card-header\")(53, \"mat-card-title\")(54, \"mat-icon\");\n    i0.ɵɵtext(55, \"subject\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(56, \" Subjects & Teachers \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(57, \"mat-card-content\");\n    i0.ɵɵtemplate(58, ViewClassComponent_div_14_div_58_Template, 3, 1, \"div\", 20);\n    i0.ɵɵtemplate(59, ViewClassComponent_div_14_ng_template_59_Template, 5, 0, \"ng-template\", null, 21, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(61, \"mat-card\", 17)(62, \"mat-card-header\")(63, \"mat-card-title\")(64, \"mat-icon\");\n    i0.ɵɵtext(65, \"groups\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(66);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(67, \"mat-card-content\");\n    i0.ɵɵtemplate(68, ViewClassComponent_div_14_div_68_Template, 4, 0, \"div\", 9);\n    i0.ɵɵtemplate(69, ViewClassComponent_div_14_div_69_Template, 21, 6, \"div\", 20);\n    i0.ɵɵtemplate(70, ViewClassComponent_div_14_ng_template_70_Template, 5, 0, \"ng-template\", null, 22, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const _r8 = i0.ɵɵreference(60);\n    const _r12 = i0.ɵɵreference(71);\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate(ctx_r3.classData.className);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r3.classData.section || \"No Section\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r3.classData.program == null ? null : ctx_r3.classData.program.name) || \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((ctx_r3.classData.program == null ? null : ctx_r3.classData.program.fullName) || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r3.classData.department == null ? null : ctx_r3.classData.department.name) || \"N/A\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r3.classData.semester || \"N/A\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r3.classData.academicYear || \"N/A\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r3.classData.maxStudents || \"N/A\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r3.students.length);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.classData.subjects && ctx_r3.classData.subjects.length > 0)(\"ngIfElse\", _r8);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" Enrolled Students (\", ctx_r3.students.length, \") \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.studentsLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.studentsLoading && ctx_r3.students.length > 0)(\"ngIfElse\", _r12);\n  }\n}\nfunction ViewClassComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r42 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Class Not Found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"The requested class could not be found.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function ViewClassComponent_div_15_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r42);\n      const ctx_r41 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r41.goBack());\n    });\n    i0.ɵɵtext(8, \" Go Back to Classes \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ViewClassComponent {\n  constructor(route, router, classesService, userService, snackBar) {\n    this.route = route;\n    this.router = router;\n    this.classesService = classesService;\n    this.userService = userService;\n    this.snackBar = snackBar;\n    this.classId = null;\n    this.classData = null;\n    this.students = [];\n    this.loading = false;\n    this.studentsLoading = false;\n    // Pagination for students\n    this.currentPage = 1;\n    this.itemsPerPage = 10;\n  }\n  ngOnInit() {\n    this.classId = this.route.snapshot.paramMap.get('id');\n    if (this.classId) {\n      this.loadClassDetails();\n      this.loadClassStudents();\n    } else {\n      Swal.fire('Error', 'Class ID not found', 'error');\n      this.router.navigate(['/dashboard/admin/classes']);\n    }\n  }\n  loadClassDetails() {\n    if (!this.classId) return;\n    this.loading = true;\n    this.classesService.getClassById(this.classId).subscribe({\n      next: response => {\n        this.loading = false;\n        if (response.success) {\n          this.classData = response.class;\n        } else {\n          this.snackBar.open('Failed to load class details', 'Close', {\n            duration: 3000\n          });\n        }\n      },\n      error: error => {\n        this.loading = false;\n        console.error('Error loading class details:', error);\n        this.snackBar.open('Error loading class details', 'Close', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  loadClassStudents() {\n    if (!this.classId) return;\n    this.studentsLoading = true;\n    this.userService.getUsersByClass(this.classId).subscribe({\n      next: response => {\n        this.studentsLoading = false;\n        if (response.success) {\n          this.students = response.users || [];\n        } else {\n          this.students = [];\n        }\n      },\n      error: error => {\n        this.studentsLoading = false;\n        console.error('Error loading students:', error);\n        this.students = [];\n      }\n    });\n  }\n  get paginatedStudents() {\n    const startIndex = (this.currentPage - 1) * this.itemsPerPage;\n    return this.students.slice(startIndex, startIndex + this.itemsPerPage);\n  }\n  get totalPages() {\n    return Math.ceil(this.students.length / this.itemsPerPage);\n  }\n  nextPage() {\n    if (this.currentPage < this.totalPages) {\n      this.currentPage++;\n    }\n  }\n  previousPage() {\n    if (this.currentPage > 1) {\n      this.currentPage--;\n    }\n  }\n  editClass() {\n    if (this.classId) {\n      this.router.navigate(['/dashboard/admin/classes/add-class', this.classId]);\n    }\n  }\n  viewStudent(studentId) {\n    this.router.navigate(['/dashboard/admin/students/view', studentId]);\n  }\n  goBack() {\n    this.router.navigate(['/dashboard/admin/classes']);\n  }\n  static #_ = this.ɵfac = function ViewClassComponent_Factory(t) {\n    return new (t || ViewClassComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ClassesService), i0.ɵɵdirectiveInject(i3.UserService), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ViewClassComponent,\n    selectors: [[\"app-view-class\"]],\n    decls: 16,\n    vars: 5,\n    consts: [[1, \"class-view-container\"], [1, \"page-header\"], [1, \"header-content\"], [\"mat-icon-button\", \"\", 1, \"back-btn\", 3, \"click\"], [1, \"title-section\"], [1, \"page-title\"], [1, \"title-icon\"], [\"class\", \"page-subtitle\", 4, \"ngIf\"], [\"class\", \"header-actions\", 4, \"ngIf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"class-details\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [1, \"page-subtitle\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"loading-container\"], [1, \"class-details\"], [1, \"info-card\"], [1, \"info-grid\"], [1, \"info-item\"], [4, \"ngIf\", \"ngIfElse\"], [\"noSubjects\", \"\"], [\"noStudents\", \"\"], [1, \"subjects-list\"], [\"class\", \"subject-teacher-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"subject-teacher-item\"], [1, \"subject-info\"], [1, \"subject-code\"], [1, \"teacher-info\"], [1, \"teacher-name\"], [\"class\", \"teacher-email\", 4, \"ngIf\"], [1, \"teacher-email\"], [1, \"no-data\"], [\"diameter\", \"30\"], [1, \"students-table\"], [\"mat-table\", \"\", 1, \"mat-elevation-z2\", 3, \"dataSource\"], [\"matColumnDef\", \"name\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"rollNo\"], [\"matColumnDef\", \"regNo\"], [\"matColumnDef\", \"email\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"class\", \"pagination-container\", 4, \"ngIf\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [\"mat-icon-button\", \"\", \"color\", \"primary\", \"matTooltip\", \"View Student\", 3, \"click\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"], [1, \"pagination-container\"], [\"showFirstLastButtons\", \"\", 3, \"length\", \"pageSize\", \"pageSizeOptions\", \"page\"], [1, \"error-container\"]],\n    template: function ViewClassComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"button\", 3);\n        i0.ɵɵlistener(\"click\", function ViewClassComponent_Template_button_click_3_listener() {\n          return ctx.goBack();\n        });\n        i0.ɵɵelementStart(4, \"mat-icon\");\n        i0.ɵɵtext(5, \"arrow_back\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(6, \"div\", 4)(7, \"h1\", 5)(8, \"mat-icon\", 6);\n        i0.ɵɵtext(9, \"class\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(10, \" Class Details \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(11, ViewClassComponent_p_11_Template, 2, 2, \"p\", 7);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(12, ViewClassComponent_div_12_Template, 5, 0, \"div\", 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(13, ViewClassComponent_div_13_Template, 4, 0, \"div\", 9);\n        i0.ɵɵtemplate(14, ViewClassComponent_div_14_Template, 72, 15, \"div\", 10);\n        i0.ɵɵtemplate(15, ViewClassComponent_div_15_Template, 9, 0, \"div\", 11);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(11);\n        i0.ɵɵproperty(\"ngIf\", ctx.classData);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.classData);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.classData);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.classData);\n      }\n    },\n    dependencies: [i5.NgForOf, i5.NgIf, i6.MatButton, i6.MatIconButton, i7.MatCard, i7.MatCardContent, i7.MatCardHeader, i7.MatCardTitle, i8.MatIcon, i9.MatPaginator, i10.MatProgressSpinner, i11.MatTable, i11.MatHeaderCellDef, i11.MatHeaderRowDef, i11.MatColumnDef, i11.MatCellDef, i11.MatRowDef, i11.MatHeaderCell, i11.MatCell, i11.MatHeaderRow, i11.MatRow, i12.MatTooltip],\n    styles: [\".class-view-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.page-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 30px;\\n  padding-bottom: 20px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n}\\n\\n.back-btn[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #666;\\n}\\n\\n.title-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  color: #333;\\n}\\n\\n.title-icon[_ngcontent-%COMP%] {\\n  color: #2196f3;\\n}\\n\\n.page-subtitle[_ngcontent-%COMP%] {\\n  margin: 5px 0 0 0;\\n  color: #666;\\n  font-size: 14px;\\n}\\n\\n.header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px;\\n  text-align: center;\\n}\\n\\n.class-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n}\\n\\n.info-card[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.info-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.info-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  color: #333;\\n}\\n\\n.info-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 20px;\\n}\\n\\n.info-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-weight: 500;\\n  color: #666;\\n  margin-bottom: 5px;\\n  font-size: 12px;\\n  text-transform: uppercase;\\n}\\n\\n.info-item[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #333;\\n  font-size: 18px;\\n}\\n\\n.info-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #555;\\n}\\n\\n.info-item[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #888;\\n  font-size: 12px;\\n  margin-top: 2px;\\n}\\n\\n.subjects-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 15px;\\n}\\n\\n.subject-teacher-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 15px;\\n  background-color: #f9f9f9;\\n  border-radius: 8px;\\n  border-left: 4px solid #2196f3;\\n}\\n\\n.subject-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 5px 0;\\n  color: #333;\\n}\\n\\n.subject-code[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 12px;\\n}\\n\\n.teacher-info[_ngcontent-%COMP%] {\\n  text-align: right;\\n}\\n\\n.teacher-name[_ngcontent-%COMP%] {\\n  margin: 0 0 5px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 5px;\\n  color: #333;\\n  font-weight: 500;\\n}\\n\\n.teacher-email[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 12px;\\n}\\n\\n.students-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 20px;\\n}\\n\\n.students-table[_ngcontent-%COMP%]   table[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.pagination-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-top: 20px;\\n}\\n\\n.no-data[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px;\\n  text-align: center;\\n  color: #666;\\n}\\n\\n.no-data[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  width: 48px;\\n  height: 48px;\\n  margin-bottom: 15px;\\n  color: #ccc;\\n}\\n\\n.error-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 60px;\\n  text-align: center;\\n}\\n\\n.error-container[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  width: 64px;\\n  height: 64px;\\n  color: #f44336;\\n  margin-bottom: 20px;\\n}\\n\\n.error-container[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 10px 0;\\n  color: #333;\\n}\\n\\n.error-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 20px 0;\\n  color: #666;\\n}\\n\\n@media (max-width: 768px) {\\n  .page-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 15px;\\n  }\\n\\n  .info-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n\\n  .subject-teacher-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 10px;\\n  }\\n\\n  .teacher-info[_ngcontent-%COMP%] {\\n    text-align: left;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate2", "ctx_r0", "classData", "className", "section", "ɵɵlistener", "ViewClassComponent_div_12_Template_button_click_1_listener", "ɵɵrestoreView", "_r6", "ctx_r5", "ɵɵnextContext", "ɵɵresetView", "editClass", "ɵɵelement", "ɵɵtextInterpolate1", "subjectTeacher_r15", "teacher", "email", "ɵɵtemplate", "ViewClassComponent_div_14_div_58_div_2_p_11_Template", "ɵɵtextInterpolate", "subject", "subjectName", "code", "name", "ɵɵproperty", "ViewClassComponent_div_14_div_58_div_2_Template", "ctx_r7", "subjects", "student_r31", "student_r32", "rollNo", "student_r33", "regNo", "student_r34", "ViewClassComponent_div_14_div_69_td_17_Template_button_click_1_listener", "restoredCtx", "_r37", "student_r35", "$implicit", "ctx_r36", "viewStudent", "_id", "ViewClassComponent_div_14_div_69_div_20_Template_mat_paginator_page_1_listener", "$event", "_r40", "ctx_r39", "currentPage", "pageIndex", "ctx_r30", "students", "length", "itemsPerPage", "ɵɵpureFunction0", "_c0", "ɵɵelementContainerStart", "ViewClassComponent_div_14_div_69_th_4_Template", "ViewClassComponent_div_14_div_69_td_5_Template", "ɵɵelementContainerEnd", "ViewClassComponent_div_14_div_69_th_7_Template", "ViewClassComponent_div_14_div_69_td_8_Template", "ViewClassComponent_div_14_div_69_th_10_Template", "ViewClassComponent_div_14_div_69_td_11_Template", "ViewClassComponent_div_14_div_69_th_13_Template", "ViewClassComponent_div_14_div_69_td_14_Template", "ViewClassComponent_div_14_div_69_th_16_Template", "ViewClassComponent_div_14_div_69_td_17_Template", "ViewClassComponent_div_14_div_69_tr_18_Template", "ViewClassComponent_div_14_div_69_tr_19_Template", "ViewClassComponent_div_14_div_69_div_20_Template", "ctx_r11", "paginatedStudents", "_c1", "totalPages", "ViewClassComponent_div_14_div_58_Template", "ViewClassComponent_div_14_ng_template_59_Template", "ɵɵtemplateRefExtractor", "ViewClassComponent_div_14_div_68_Template", "ViewClassComponent_div_14_div_69_Template", "ViewClassComponent_div_14_ng_template_70_Template", "ctx_r3", "program", "fullName", "department", "semester", "academicYear", "maxStudents", "_r8", "studentsLoading", "_r12", "ViewClassComponent_div_15_Template_button_click_7_listener", "_r42", "ctx_r41", "goBack", "ViewClassComponent", "constructor", "route", "router", "classesService", "userService", "snackBar", "classId", "loading", "ngOnInit", "snapshot", "paramMap", "get", "loadClassDetails", "loadClassStudents", "fire", "navigate", "getClassById", "subscribe", "next", "response", "success", "class", "open", "duration", "error", "console", "getUsersByClass", "users", "startIndex", "slice", "Math", "ceil", "nextPage", "previousPage", "studentId", "_", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "ClassesService", "i3", "UserService", "i4", "MatSnackBar", "_2", "selectors", "decls", "vars", "consts", "template", "ViewClassComponent_Template", "rf", "ctx", "ViewClassComponent_Template_button_click_3_listener", "ViewClassComponent_p_11_Template", "ViewClassComponent_div_12_Template", "ViewClassComponent_div_13_Template", "ViewClassComponent_div_14_Template", "ViewClassComponent_div_15_Template"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\classes\\view-class\\view-class.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\classes\\view-class\\view-class.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { ClassesService } from 'src/app/services/classes.service';\r\nimport { UserService } from 'src/app/services/user.service';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { Class, User } from 'src/app/models/user';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-view-class',\r\n  templateUrl: './view-class.component.html',\r\n  styleUrls: ['./view-class.component.css']\r\n})\r\nexport class ViewClassComponent implements OnInit {\r\n  classId: string | null = null;\r\n  classData: any = null;\r\n  students: User[] = [];\r\n  loading = false;\r\n  studentsLoading = false;\r\n\r\n  // Pagination for students\r\n  currentPage = 1;\r\n  itemsPerPage = 10;\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private classesService: ClassesService,\r\n    private userService: UserService,\r\n    private snackBar: MatSnackBar\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.classId = this.route.snapshot.paramMap.get('id');\r\n    if (this.classId) {\r\n      this.loadClassDetails();\r\n      this.loadClassStudents();\r\n    } else {\r\n      Swal.fire('Error', 'Class ID not found', 'error');\r\n      this.router.navigate(['/dashboard/admin/classes']);\r\n    }\r\n  }\r\n\r\n  loadClassDetails(): void {\r\n    if (!this.classId) return;\r\n\r\n    this.loading = true;\r\n    this.classesService.getClassById(this.classId).subscribe({\r\n      next: (response) => {\r\n        this.loading = false;\r\n        if (response.success) {\r\n          this.classData = response.class;\r\n        } else {\r\n          this.snackBar.open('Failed to load class details', 'Close', { duration: 3000 });\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.loading = false;\r\n        console.error('Error loading class details:', error);\r\n        this.snackBar.open('Error loading class details', 'Close', { duration: 3000 });\r\n      }\r\n    });\r\n  }\r\n\r\n  loadClassStudents(): void {\r\n    if (!this.classId) return;\r\n\r\n    this.studentsLoading = true;\r\n    this.userService.getUsersByClass(this.classId).subscribe({\r\n      next: (response) => {\r\n        this.studentsLoading = false;\r\n        if (response.success) {\r\n          this.students = response.users || [];\r\n        } else {\r\n          this.students = [];\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.studentsLoading = false;\r\n        console.error('Error loading students:', error);\r\n        this.students = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  get paginatedStudents(): User[] {\r\n    const startIndex = (this.currentPage - 1) * this.itemsPerPage;\r\n    return this.students.slice(startIndex, startIndex + this.itemsPerPage);\r\n  }\r\n\r\n  get totalPages(): number {\r\n    return Math.ceil(this.students.length / this.itemsPerPage);\r\n  }\r\n\r\n  nextPage(): void {\r\n    if (this.currentPage < this.totalPages) {\r\n      this.currentPage++;\r\n    }\r\n  }\r\n\r\n  previousPage(): void {\r\n    if (this.currentPage > 1) {\r\n      this.currentPage--;\r\n    }\r\n  }\r\n\r\n  editClass(): void {\r\n    if (this.classId) {\r\n      this.router.navigate(['/dashboard/admin/classes/add-class', this.classId]);\r\n    }\r\n  }\r\n\r\n  viewStudent(studentId: string): void {\r\n    this.router.navigate(['/dashboard/admin/students/view', studentId]);\r\n  }\r\n\r\n  goBack(): void {\r\n    this.router.navigate(['/dashboard/admin/classes']);\r\n  }\r\n}\r\n", "<div class=\"class-view-container\">\r\n  <!-- Header -->\r\n  <div class=\"page-header\">\r\n    <div class=\"header-content\">\r\n      <button mat-icon-button (click)=\"goBack()\" class=\"back-btn\">\r\n        <mat-icon>arrow_back</mat-icon>\r\n      </button>\r\n      <div class=\"title-section\">\r\n        <h1 class=\"page-title\">\r\n          <mat-icon class=\"title-icon\">class</mat-icon>\r\n          Class Details\r\n        </h1>\r\n        <p class=\"page-subtitle\" *ngIf=\"classData\">{{ classData.className }} - {{ classData.section || 'No Section' }}</p>\r\n      </div>\r\n    </div>\r\n    <div class=\"header-actions\" *ngIf=\"classData\">\r\n      <button mat-raised-button color=\"primary\" (click)=\"editClass()\">\r\n        <mat-icon>edit</mat-icon>\r\n        Edit Class\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Loading State -->\r\n  <div *ngIf=\"loading\" class=\"loading-container\">\r\n    <mat-spinner></mat-spinner>\r\n    <p>Loading class details...</p>\r\n  </div>\r\n\r\n  <!-- Class Details -->\r\n  <div *ngIf=\"!loading && classData\" class=\"class-details\">\r\n    <!-- Basic Information Card -->\r\n    <mat-card class=\"info-card\">\r\n      <mat-card-header>\r\n        <mat-card-title>\r\n          <mat-icon>info</mat-icon>\r\n          Basic Information\r\n        </mat-card-title>\r\n      </mat-card-header>\r\n      <mat-card-content>\r\n        <div class=\"info-grid\">\r\n          <div class=\"info-item\">\r\n            <label>Class Name</label>\r\n            <h3>{{ classData.className }}</h3>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <label>Section</label>\r\n            <p>{{ classData.section || 'No Section' }}</p>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <label>Program</label>\r\n            <h3>{{ classData.program?.name || 'N/A' }}</h3>\r\n            <small>{{ classData.program?.fullName || '' }}</small>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <label>Department</label>\r\n            <h3>{{ classData.department?.name || 'N/A' }}</h3>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <label>Semester</label>\r\n            <h3>{{ classData.semester || 'N/A' }}</h3>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <label>Academic Year</label>\r\n            <p>{{ classData.academicYear || 'N/A' }}</p>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <label>Maximum Students</label>\r\n            <p>{{ classData.maxStudents || 'N/A' }}</p>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <label>Current Students</label>\r\n            <p>{{ students.length }}</p>\r\n          </div>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n\r\n    <!-- Subjects and Teachers Card -->\r\n    <mat-card class=\"info-card\">\r\n      <mat-card-header>\r\n        <mat-card-title>\r\n          <mat-icon>subject</mat-icon>\r\n          Subjects & Teachers\r\n        </mat-card-title>\r\n      </mat-card-header>\r\n      <mat-card-content>\r\n        <div *ngIf=\"classData.subjects && classData.subjects.length > 0; else noSubjects\">\r\n          <div class=\"subjects-list\">\r\n            <div *ngFor=\"let subjectTeacher of classData.subjects\" class=\"subject-teacher-item\">\r\n              <div class=\"subject-info\">\r\n                <h4>{{ subjectTeacher.subject?.subjectName || 'Unknown Subject' }}</h4>\r\n                <p class=\"subject-code\">Code: {{ subjectTeacher.subject?.code || 'N/A' }}</p>\r\n              </div>\r\n              <div class=\"teacher-info\">\r\n                <p class=\"teacher-name\">\r\n                  <mat-icon>person</mat-icon>\r\n                  {{ subjectTeacher.teacher?.name || 'No Teacher Assigned' }}\r\n                </p>\r\n                <p class=\"teacher-email\" *ngIf=\"subjectTeacher.teacher?.email\">\r\n                  {{ subjectTeacher.teacher.email }}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <ng-template #noSubjects>\r\n          <div class=\"no-data\">\r\n            <mat-icon>subject</mat-icon>\r\n            <p>No subjects assigned to this class yet.</p>\r\n          </div>\r\n        </ng-template>\r\n      </mat-card-content>\r\n    </mat-card>\r\n\r\n    <!-- Students Card -->\r\n    <mat-card class=\"info-card\">\r\n      <mat-card-header>\r\n        <mat-card-title>\r\n          <mat-icon>groups</mat-icon>\r\n          Enrolled Students ({{ students.length }})\r\n        </mat-card-title>\r\n      </mat-card-header>\r\n      <mat-card-content>\r\n        <!-- Loading State for Students -->\r\n        <div *ngIf=\"studentsLoading\" class=\"loading-container\">\r\n          <mat-spinner diameter=\"30\"></mat-spinner>\r\n          <p>Loading students...</p>\r\n        </div>\r\n\r\n        <!-- Students List -->\r\n        <div *ngIf=\"!studentsLoading && students.length > 0; else noStudents\">\r\n          <div class=\"students-table\">\r\n            <table mat-table [dataSource]=\"paginatedStudents\" class=\"mat-elevation-z2\">\r\n              <!-- Name Column -->\r\n              <ng-container matColumnDef=\"name\">\r\n                <th mat-header-cell *matHeaderCellDef>Name</th>\r\n                <td mat-cell *matCellDef=\"let student\">{{ student.name }}</td>\r\n              </ng-container>\r\n\r\n              <!-- Roll Number Column -->\r\n              <ng-container matColumnDef=\"rollNo\">\r\n                <th mat-header-cell *matHeaderCellDef>Roll Number</th>\r\n                <td mat-cell *matCellDef=\"let student\">{{ student.rollNo || 'N/A' }}</td>\r\n              </ng-container>\r\n\r\n              <!-- Registration Number Column -->\r\n              <ng-container matColumnDef=\"regNo\">\r\n                <th mat-header-cell *matHeaderCellDef>Registration Number</th>\r\n                <td mat-cell *matCellDef=\"let student\">{{ student.regNo || 'N/A' }}</td>\r\n              </ng-container>\r\n\r\n              <!-- Email Column -->\r\n              <ng-container matColumnDef=\"email\">\r\n                <th mat-header-cell *matHeaderCellDef>Email</th>\r\n                <td mat-cell *matCellDef=\"let student\">{{ student.email }}</td>\r\n              </ng-container>\r\n\r\n              <!-- Actions Column -->\r\n              <ng-container matColumnDef=\"actions\">\r\n                <th mat-header-cell *matHeaderCellDef>Actions</th>\r\n                <td mat-cell *matCellDef=\"let student\">\r\n                  <button mat-icon-button color=\"primary\" (click)=\"viewStudent(student._id)\" matTooltip=\"View Student\">\r\n                    <mat-icon>visibility</mat-icon>\r\n                  </button>\r\n                </td>\r\n              </ng-container>\r\n\r\n              <tr mat-header-row *matHeaderRowDef=\"['name', 'rollNo', 'regNo', 'email', 'actions']\"></tr>\r\n              <tr mat-row *matRowDef=\"let row; columns: ['name', 'rollNo', 'regNo', 'email', 'actions'];\"></tr>\r\n            </table>\r\n          </div>\r\n\r\n          <!-- Pagination -->\r\n          <div class=\"pagination-container\" *ngIf=\"totalPages > 1\">\r\n            <mat-paginator\r\n              [length]=\"students.length\"\r\n              [pageSize]=\"itemsPerPage\"\r\n              [pageSizeOptions]=\"[5, 10, 20]\"\r\n              (page)=\"currentPage = $event.pageIndex + 1\"\r\n              showFirstLastButtons>\r\n            </mat-paginator>\r\n          </div>\r\n        </div>\r\n\r\n        <ng-template #noStudents>\r\n          <div class=\"no-data\">\r\n            <mat-icon>groups</mat-icon>\r\n            <p>No students enrolled in this class yet.</p>\r\n          </div>\r\n        </ng-template>\r\n      </mat-card-content>\r\n    </mat-card>\r\n  </div>\r\n\r\n  <!-- Error State -->\r\n  <div *ngIf=\"!loading && !classData\" class=\"error-container\">\r\n    <mat-icon>error</mat-icon>\r\n    <h3>Class Not Found</h3>\r\n    <p>The requested class could not be found.</p>\r\n    <button mat-raised-button color=\"primary\" (click)=\"goBack()\">\r\n      Go Back to Classes\r\n    </button>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAMA,OAAOA,IAAI,MAAM,aAAa;;;;;;;;;;;;;;;;ICMtBC,EAAA,CAAAC,cAAA,YAA2C;IAAAD,EAAA,CAAAE,MAAA,GAAmE;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAAvEH,EAAA,CAAAI,SAAA,GAAmE;IAAnEJ,EAAA,CAAAK,kBAAA,KAAAC,MAAA,CAAAC,SAAA,CAAAC,SAAA,SAAAF,MAAA,CAAAC,SAAA,CAAAE,OAAA,qBAAmE;;;;;;IAGlHT,EAAA,CAAAC,cAAA,cAA8C;IACFD,EAAA,CAAAU,UAAA,mBAAAC,2DAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAF,MAAA,CAAAG,SAAA,EAAW;IAAA,EAAC;IAC7DjB,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAE,MAAA,mBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAKbH,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAkB,SAAA,kBAA2B;IAC3BlB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAyEnBH,EAAA,CAAAC,cAAA,YAA+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAmB,kBAAA,MAAAC,kBAAA,CAAAC,OAAA,CAAAC,KAAA,MACF;;;;;IAZJtB,EAAA,CAAAC,cAAA,cAAoF;IAE5ED,EAAA,CAAAE,MAAA,GAA8D;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvEH,EAAA,CAAAC,cAAA,YAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAiD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAE/EH,EAAA,CAAAC,cAAA,cAA0B;IAEZD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAuB,UAAA,KAAAC,oDAAA,gBAEI;IACNxB,EAAA,CAAAG,YAAA,EAAM;;;;IAXAH,EAAA,CAAAI,SAAA,GAA8D;IAA9DJ,EAAA,CAAAyB,iBAAA,EAAAL,kBAAA,CAAAM,OAAA,kBAAAN,kBAAA,CAAAM,OAAA,CAAAC,WAAA,uBAA8D;IAC1C3B,EAAA,CAAAI,SAAA,GAAiD;IAAjDJ,EAAA,CAAAmB,kBAAA,YAAAC,kBAAA,CAAAM,OAAA,kBAAAN,kBAAA,CAAAM,OAAA,CAAAE,IAAA,eAAiD;IAKvE5B,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAmB,kBAAA,OAAAC,kBAAA,CAAAC,OAAA,kBAAAD,kBAAA,CAAAC,OAAA,CAAAQ,IAAA,gCACF;IAC0B7B,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAA8B,UAAA,SAAAV,kBAAA,CAAAC,OAAA,kBAAAD,kBAAA,CAAAC,OAAA,CAAAC,KAAA,CAAmC;;;;;IAZrEtB,EAAA,CAAAC,cAAA,UAAkF;IAE9ED,EAAA,CAAAuB,UAAA,IAAAQ,+CAAA,mBAcM;IACR/B,EAAA,CAAAG,YAAA,EAAM;;;;IAf4BH,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAA8B,UAAA,YAAAE,MAAA,CAAAzB,SAAA,CAAA0B,QAAA,CAAqB;;;;;IAkBvDjC,EAAA,CAAAC,cAAA,cAAqB;IACTD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,8CAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAgBlDH,EAAA,CAAAC,cAAA,cAAuD;IACrDD,EAAA,CAAAkB,SAAA,sBAAyC;IACzClB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IASpBH,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAC/CH,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAvBH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAyB,iBAAA,CAAAS,WAAA,CAAAL,IAAA,CAAkB;;;;;IAKzD7B,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACtDH,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAlCH,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAyB,iBAAA,CAAAU,WAAA,CAAAC,MAAA,UAA6B;;;;;IAKpEpC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAC9DH,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAjCH,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAyB,iBAAA,CAAAY,WAAA,CAAAC,KAAA,UAA4B;;;;;IAKnEtC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAChDH,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAxBH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAyB,iBAAA,CAAAc,WAAA,CAAAjB,KAAA,CAAmB;;;;;IAK1DtB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;;IAClDH,EAAA,CAAAC,cAAA,aAAuC;IACGD,EAAA,CAAAU,UAAA,mBAAA8B,wEAAA;MAAA,MAAAC,WAAA,GAAAzC,EAAA,CAAAY,aAAA,CAAA8B,IAAA;MAAA,MAAAC,WAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAA7C,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAA6B,OAAA,CAAAC,WAAA,CAAAH,WAAA,CAAAI,GAAA,CAAwB;IAAA,EAAC;IACxE/C,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAKrCH,EAAA,CAAAkB,SAAA,aAA2F;;;;;IAC3FlB,EAAA,CAAAkB,SAAA,aAAiG;;;;;;;;;IAKrGlB,EAAA,CAAAC,cAAA,cAAyD;IAKrDD,EAAA,CAAAU,UAAA,kBAAAsC,+EAAAC,MAAA;MAAAjD,EAAA,CAAAY,aAAA,CAAAsC,IAAA;MAAA,MAAAC,OAAA,GAAAnD,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAAmC,OAAA,CAAAC,WAAA,GAAAH,MAAA,CAAAI,SAAA,GAAyC,CAAC;IAAA,EAAC;IAE7CrD,EAAA,CAAAG,YAAA,EAAgB;;;;IALdH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAA8B,UAAA,WAAAwB,OAAA,CAAAC,QAAA,CAAAC,MAAA,CAA0B,aAAAF,OAAA,CAAAG,YAAA,qBAAAzD,EAAA,CAAA0D,eAAA,IAAAC,GAAA;;;;;;;;IA7ChC3D,EAAA,CAAAC,cAAA,UAAsE;IAIhED,EAAA,CAAA4D,uBAAA,OAAkC;IAChC5D,EAAA,CAAAuB,UAAA,IAAAsC,8CAAA,iBAA+C;IAC/C7D,EAAA,CAAAuB,UAAA,IAAAuC,8CAAA,iBAA8D;IAChE9D,EAAA,CAAA+D,qBAAA,EAAe;IAGf/D,EAAA,CAAA4D,uBAAA,OAAoC;IAClC5D,EAAA,CAAAuB,UAAA,IAAAyC,8CAAA,iBAAsD;IACtDhE,EAAA,CAAAuB,UAAA,IAAA0C,8CAAA,iBAAyE;IAC3EjE,EAAA,CAAA+D,qBAAA,EAAe;IAGf/D,EAAA,CAAA4D,uBAAA,OAAmC;IACjC5D,EAAA,CAAAuB,UAAA,KAAA2C,+CAAA,iBAA8D;IAC9DlE,EAAA,CAAAuB,UAAA,KAAA4C,+CAAA,iBAAwE;IAC1EnE,EAAA,CAAA+D,qBAAA,EAAe;IAGf/D,EAAA,CAAA4D,uBAAA,QAAmC;IACjC5D,EAAA,CAAAuB,UAAA,KAAA6C,+CAAA,iBAAgD;IAChDpE,EAAA,CAAAuB,UAAA,KAAA8C,+CAAA,iBAA+D;IACjErE,EAAA,CAAA+D,qBAAA,EAAe;IAGf/D,EAAA,CAAA4D,uBAAA,QAAqC;IACnC5D,EAAA,CAAAuB,UAAA,KAAA+C,+CAAA,iBAAkD;IAClDtE,EAAA,CAAAuB,UAAA,KAAAgD,+CAAA,iBAIK;IACPvE,EAAA,CAAA+D,qBAAA,EAAe;IAEf/D,EAAA,CAAAuB,UAAA,KAAAiD,+CAAA,iBAA2F;IAC3FxE,EAAA,CAAAuB,UAAA,KAAAkD,+CAAA,iBAAiG;IACnGzE,EAAA,CAAAG,YAAA,EAAQ;IAIVH,EAAA,CAAAuB,UAAA,KAAAmD,gDAAA,kBAQM;IACR1E,EAAA,CAAAG,YAAA,EAAM;;;;IAlDeH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAA8B,UAAA,eAAA6C,OAAA,CAAAC,iBAAA,CAAgC;IAmC3B5E,EAAA,CAAAI,SAAA,IAAgE;IAAhEJ,EAAA,CAAA8B,UAAA,oBAAA9B,EAAA,CAAA0D,eAAA,IAAAmB,GAAA,EAAgE;IACnD7E,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAA8B,UAAA,qBAAA9B,EAAA,CAAA0D,eAAA,IAAAmB,GAAA,EAAyD;IAK3D7E,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAA8B,UAAA,SAAA6C,OAAA,CAAAG,UAAA,KAAoB;;;;;IAYvD9E,EAAA,CAAAC,cAAA,cAAqB;IACTD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,8CAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IA9JxDH,EAAA,CAAAC,cAAA,cAAyD;IAKvCD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAEnBH,EAAA,CAAAC,cAAA,uBAAkB;IAGLD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEpCH,EAAA,CAAAC,cAAA,eAAuB;IACdD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtBH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,IAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEhDH,EAAA,CAAAC,cAAA,eAAuB;IACdD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/CH,EAAA,CAAAC,cAAA,aAAO;IAAAD,EAAA,CAAAE,MAAA,IAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAExDH,EAAA,CAAAC,cAAA,eAAuB;IACdD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEpDH,EAAA,CAAAC,cAAA,eAAuB;IACdD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE5CH,EAAA,CAAAC,cAAA,eAAuB;IACdD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC5BH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,IAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAE9CH,EAAA,CAAAC,cAAA,eAAuB;IACdD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC/BH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,IAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAE7CH,EAAA,CAAAC,cAAA,eAAuB;IACdD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC/BH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,IAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAOpCH,EAAA,CAAAC,cAAA,oBAA4B;IAGZD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAEnBH,EAAA,CAAAC,cAAA,wBAAkB;IAChBD,EAAA,CAAAuB,UAAA,KAAAwD,yCAAA,kBAkBM;IACN/E,EAAA,CAAAuB,UAAA,KAAAyD,iDAAA,iCAAAhF,EAAA,CAAAiF,sBAAA,CAKc;IAChBjF,EAAA,CAAAG,YAAA,EAAmB;IAIrBH,EAAA,CAAAC,cAAA,oBAA4B;IAGZD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAEnBH,EAAA,CAAAC,cAAA,wBAAkB;IAEhBD,EAAA,CAAAuB,UAAA,KAAA2D,yCAAA,iBAGM;IAGNlF,EAAA,CAAAuB,UAAA,KAAA4D,yCAAA,mBAoDM;IAENnF,EAAA,CAAAuB,UAAA,KAAA6D,iDAAA,iCAAApF,EAAA,CAAAiF,sBAAA,CAKc;IAChBjF,EAAA,CAAAG,YAAA,EAAmB;;;;;;IApJTH,EAAA,CAAAI,SAAA,IAAyB;IAAzBJ,EAAA,CAAAyB,iBAAA,CAAA4D,MAAA,CAAA9E,SAAA,CAAAC,SAAA,CAAyB;IAI1BR,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAyB,iBAAA,CAAA4D,MAAA,CAAA9E,SAAA,CAAAE,OAAA,iBAAuC;IAItCT,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAyB,iBAAA,EAAA4D,MAAA,CAAA9E,SAAA,CAAA+E,OAAA,kBAAAD,MAAA,CAAA9E,SAAA,CAAA+E,OAAA,CAAAzD,IAAA,WAAsC;IACnC7B,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAyB,iBAAA,EAAA4D,MAAA,CAAA9E,SAAA,CAAA+E,OAAA,kBAAAD,MAAA,CAAA9E,SAAA,CAAA+E,OAAA,CAAAC,QAAA,QAAuC;IAI1CvF,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAyB,iBAAA,EAAA4D,MAAA,CAAA9E,SAAA,CAAAiF,UAAA,kBAAAH,MAAA,CAAA9E,SAAA,CAAAiF,UAAA,CAAA3D,IAAA,WAAyC;IAIzC7B,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAyB,iBAAA,CAAA4D,MAAA,CAAA9E,SAAA,CAAAkF,QAAA,UAAiC;IAIlCzF,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAyB,iBAAA,CAAA4D,MAAA,CAAA9E,SAAA,CAAAmF,YAAA,UAAqC;IAIrC1F,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAyB,iBAAA,CAAA4D,MAAA,CAAA9E,SAAA,CAAAoF,WAAA,UAAoC;IAIpC3F,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAyB,iBAAA,CAAA4D,MAAA,CAAA9B,QAAA,CAAAC,MAAA,CAAqB;IAetBxD,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAA8B,UAAA,SAAAuD,MAAA,CAAA9E,SAAA,CAAA0B,QAAA,IAAAoD,MAAA,CAAA9E,SAAA,CAAA0B,QAAA,CAAAuB,MAAA,KAA2D,aAAAoC,GAAA;IAiC/D5F,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAmB,kBAAA,yBAAAkE,MAAA,CAAA9B,QAAA,CAAAC,MAAA,OACF;IAIMxD,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAA8B,UAAA,SAAAuD,MAAA,CAAAQ,eAAA,CAAqB;IAMrB7F,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAA8B,UAAA,UAAAuD,MAAA,CAAAQ,eAAA,IAAAR,MAAA,CAAA9B,QAAA,CAAAC,MAAA,KAA+C,aAAAsC,IAAA;;;;;;IAiE3D9F,EAAA,CAAAC,cAAA,cAA4D;IAChDD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,8CAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC9CH,EAAA,CAAAC,cAAA,iBAA6D;IAAnBD,EAAA,CAAAU,UAAA,mBAAAqF,2DAAA;MAAA/F,EAAA,CAAAY,aAAA,CAAAoF,IAAA;MAAA,MAAAC,OAAA,GAAAjG,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAiF,OAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IAC1DlG,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;AD7Lb,OAAM,MAAOgG,kBAAkB;EAW7BC,YACUC,KAAqB,EACrBC,MAAc,EACdC,cAA8B,EAC9BC,WAAwB,EACxBC,QAAqB;IAJrB,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAflB,KAAAC,OAAO,GAAkB,IAAI;IAC7B,KAAAnG,SAAS,GAAQ,IAAI;IACrB,KAAAgD,QAAQ,GAAW,EAAE;IACrB,KAAAoD,OAAO,GAAG,KAAK;IACf,KAAAd,eAAe,GAAG,KAAK;IAEvB;IACA,KAAAzC,WAAW,GAAG,CAAC;IACf,KAAAK,YAAY,GAAG,EAAE;EAQd;EAEHmD,QAAQA,CAAA;IACN,IAAI,CAACF,OAAO,GAAG,IAAI,CAACL,KAAK,CAACQ,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACrD,IAAI,IAAI,CAACL,OAAO,EAAE;MAChB,IAAI,CAACM,gBAAgB,EAAE;MACvB,IAAI,CAACC,iBAAiB,EAAE;KACzB,MAAM;MACLlH,IAAI,CAACmH,IAAI,CAAC,OAAO,EAAE,oBAAoB,EAAE,OAAO,CAAC;MACjD,IAAI,CAACZ,MAAM,CAACa,QAAQ,CAAC,CAAC,0BAA0B,CAAC,CAAC;;EAEtD;EAEAH,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACN,OAAO,EAAE;IAEnB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACJ,cAAc,CAACa,YAAY,CAAC,IAAI,CAACV,OAAO,CAAC,CAACW,SAAS,CAAC;MACvDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACZ,OAAO,GAAG,KAAK;QACpB,IAAIY,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACjH,SAAS,GAAGgH,QAAQ,CAACE,KAAK;SAChC,MAAM;UACL,IAAI,CAAChB,QAAQ,CAACiB,IAAI,CAAC,8BAA8B,EAAE,OAAO,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;;MAEnF,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACjB,OAAO,GAAG,KAAK;QACpBkB,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD,IAAI,CAACnB,QAAQ,CAACiB,IAAI,CAAC,6BAA6B,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MAChF;KACD,CAAC;EACJ;EAEAV,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACP,OAAO,EAAE;IAEnB,IAAI,CAACb,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACW,WAAW,CAACsB,eAAe,CAAC,IAAI,CAACpB,OAAO,CAAC,CAACW,SAAS,CAAC;MACvDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAC1B,eAAe,GAAG,KAAK;QAC5B,IAAI0B,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACjE,QAAQ,GAAGgE,QAAQ,CAACQ,KAAK,IAAI,EAAE;SACrC,MAAM;UACL,IAAI,CAACxE,QAAQ,GAAG,EAAE;;MAEtB,CAAC;MACDqE,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC/B,eAAe,GAAG,KAAK;QAC5BgC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACrE,QAAQ,GAAG,EAAE;MACpB;KACD,CAAC;EACJ;EAEA,IAAIqB,iBAAiBA,CAAA;IACnB,MAAMoD,UAAU,GAAG,CAAC,IAAI,CAAC5E,WAAW,GAAG,CAAC,IAAI,IAAI,CAACK,YAAY;IAC7D,OAAO,IAAI,CAACF,QAAQ,CAAC0E,KAAK,CAACD,UAAU,EAAEA,UAAU,GAAG,IAAI,CAACvE,YAAY,CAAC;EACxE;EAEA,IAAIqB,UAAUA,CAAA;IACZ,OAAOoD,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC5E,QAAQ,CAACC,MAAM,GAAG,IAAI,CAACC,YAAY,CAAC;EAC5D;EAEA2E,QAAQA,CAAA;IACN,IAAI,IAAI,CAAChF,WAAW,GAAG,IAAI,CAAC0B,UAAU,EAAE;MACtC,IAAI,CAAC1B,WAAW,EAAE;;EAEtB;EAEAiF,YAAYA,CAAA;IACV,IAAI,IAAI,CAACjF,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;;EAEtB;EAEAnC,SAASA,CAAA;IACP,IAAI,IAAI,CAACyF,OAAO,EAAE;MAChB,IAAI,CAACJ,MAAM,CAACa,QAAQ,CAAC,CAAC,oCAAoC,EAAE,IAAI,CAACT,OAAO,CAAC,CAAC;;EAE9E;EAEA5D,WAAWA,CAACwF,SAAiB;IAC3B,IAAI,CAAChC,MAAM,CAACa,QAAQ,CAAC,CAAC,gCAAgC,EAAEmB,SAAS,CAAC,CAAC;EACrE;EAEApC,MAAMA,CAAA;IACJ,IAAI,CAACI,MAAM,CAACa,QAAQ,CAAC,CAAC,0BAA0B,CAAC,CAAC;EACpD;EAAC,QAAAoB,CAAA,G;qBAzGUpC,kBAAkB,EAAAnG,EAAA,CAAAwI,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA1I,EAAA,CAAAwI,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA3I,EAAA,CAAAwI,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAA7I,EAAA,CAAAwI,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAA/I,EAAA,CAAAwI,iBAAA,CAAAQ,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAlB/C,kBAAkB;IAAAgD,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCb/BzJ,EAAA,CAAAC,cAAA,aAAkC;QAIJD,EAAA,CAAAU,UAAA,mBAAAiJ,oDAAA;UAAA,OAASD,GAAA,CAAAxD,MAAA,EAAQ;QAAA,EAAC;QACxClG,EAAA,CAAAC,cAAA,eAAU;QAAAD,EAAA,CAAAE,MAAA,iBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAEjCH,EAAA,CAAAC,cAAA,aAA2B;QAEMD,EAAA,CAAAE,MAAA,YAAK;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC7CH,EAAA,CAAAE,MAAA,uBACF;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAuB,UAAA,KAAAqI,gCAAA,eAAkH;QACpH5J,EAAA,CAAAG,YAAA,EAAM;QAERH,EAAA,CAAAuB,UAAA,KAAAsI,kCAAA,iBAKM;QACR7J,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAAuB,UAAA,KAAAuI,kCAAA,iBAGM;QAGN9J,EAAA,CAAAuB,UAAA,KAAAwI,kCAAA,oBAmKM;QAGN/J,EAAA,CAAAuB,UAAA,KAAAyI,kCAAA,kBAOM;QACRhK,EAAA,CAAAG,YAAA,EAAM;;;QAhM4BH,EAAA,CAAAI,SAAA,IAAe;QAAfJ,EAAA,CAAA8B,UAAA,SAAA4H,GAAA,CAAAnJ,SAAA,CAAe;QAGhBP,EAAA,CAAAI,SAAA,GAAe;QAAfJ,EAAA,CAAA8B,UAAA,SAAA4H,GAAA,CAAAnJ,SAAA,CAAe;QASxCP,EAAA,CAAAI,SAAA,GAAa;QAAbJ,EAAA,CAAA8B,UAAA,SAAA4H,GAAA,CAAA/C,OAAA,CAAa;QAMb3G,EAAA,CAAAI,SAAA,GAA2B;QAA3BJ,EAAA,CAAA8B,UAAA,UAAA4H,GAAA,CAAA/C,OAAA,IAAA+C,GAAA,CAAAnJ,SAAA,CAA2B;QAsK3BP,EAAA,CAAAI,SAAA,GAA4B;QAA5BJ,EAAA,CAAA8B,UAAA,UAAA4H,GAAA,CAAA/C,OAAA,KAAA+C,GAAA,CAAAnJ,SAAA,CAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}