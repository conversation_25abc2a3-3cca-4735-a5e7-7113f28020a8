const mongoose = require('mongoose');

// Time slot schema for reusable time periods
const timeSlotSchema = new mongoose.Schema({
  startTime: {
    type: String,
    required: [true, 'Start time is required'],
    match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Please enter valid time format (HH:MM)']
  },
  endTime: {
    type: String,
    required: [true, 'End time is required'],
    match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Please enter valid time format (HH:MM)']
  },
  duration: {
    type: Number, // in minutes
    required: [true, 'Duration is required']
  }
});

// Timetable entry schema
const timetableSchema = new mongoose.Schema({
  program: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Program',
    required: [true, 'Program is required']
  },
  department: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Departments',
    required: [true, 'Department is required']
  },
  class: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Class',
    required: [true, 'Class is required']
  },
  subject: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Subject',
    required: [true, 'Subject is required']
  },
  teacher: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Teacher is required']
  },
  dayOfWeek: {
    type: String,
    enum: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],
    required: [true, 'Day of week is required']
  },
  timeSlot: {
    type: timeSlotSchema,
    required: [true, 'Time slot is required']
  },
  room: {
    type: String,
    required: false
  },
  semester: {
    type: Number,
    required: [true, 'Semester is required'],
    min: 1
  },
  academicYear: {
    type: String,
    required: [true, 'Academic year is required']
  },
  isActive: {
    type: Boolean,
    default: true
  },
  notes: {
    type: String
  }
}, { timestamps: true });

// Compound index to prevent scheduling conflicts
timetableSchema.index({ 
  teacher: 1, 
  dayOfWeek: 1, 
  'timeSlot.startTime': 1, 
  'timeSlot.endTime': 1,
  academicYear: 1,
  isActive: 1
}, { 
  unique: true,
  partialFilterExpression: { isActive: true }
});

// Index for room conflicts
timetableSchema.index({ 
  room: 1, 
  dayOfWeek: 1, 
  'timeSlot.startTime': 1, 
  'timeSlot.endTime': 1,
  academicYear: 1,
  isActive: 1
}, { 
  unique: true,
  partialFilterExpression: { room: { $exists: true, $ne: null }, isActive: true }
});

// Method to check for time conflicts
timetableSchema.methods.hasTimeConflict = function(startTime, endTime) {
  const thisStart = this.timeSlot.startTime;
  const thisEnd = this.timeSlot.endTime;
  
  // Convert time strings to minutes for comparison
  const toMinutes = (time) => {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  };
  
  const thisStartMin = toMinutes(thisStart);
  const thisEndMin = toMinutes(thisEnd);
  const newStartMin = toMinutes(startTime);
  const newEndMin = toMinutes(endTime);
  
  // Check if times overlap
  return (newStartMin < thisEndMin && newEndMin > thisStartMin);
};

const Timetable = mongoose.model('Timetable', timetableSchema);

module.exports = Timetable;
