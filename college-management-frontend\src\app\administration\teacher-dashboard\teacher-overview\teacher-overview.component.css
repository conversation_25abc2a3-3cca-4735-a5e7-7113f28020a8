.teacher-dashboard {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  text-align: center;
}

.error-container mat-icon {
  font-size: 48px;
  height: 48px;
  width: 48px;
  margin-bottom: 16px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.welcome-section h1 {
  margin: 0;
  color: #333;
  font-weight: 500;
}

.welcome-section p {
  margin: 8px 0 0 0;
  color: #666;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  transition: transform 0.2s ease-in-out;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.12);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  color: white;
}

.stat-card.classes .stat-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card.subjects .stat-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card.students .stat-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card.today-classes .stat-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-icon mat-icon {
  font-size: 28px;
  height: 28px;
  width: 28px;
}

.stat-info h3 {
  margin: 0;
  font-size: 2rem;
  font-weight: 600;
  color: #333;
}

.stat-info p {
  margin: 4px 0 0 0;
  color: #666;
  font-size: 0.9rem;
}

.schedule-section {
  margin-bottom: 30px;
}

.schedule-row {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
}

.schedule-card, .upcoming-card {
  min-height: 300px;
}

.classes-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.class-item {
  display: flex;
  gap: 16px;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 8px;
  border-left: 4px solid #1976d2;
}

.time-slot {
  min-width: 120px;
}

.time {
  font-weight: 600;
  color: #1976d2;
  font-size: 0.9rem;
}

.class-details h4 {
  margin: 0 0 4px 0;
  color: #333;
  font-size: 1.1rem;
}

.class-details p {
  margin: 2px 0;
  color: #666;
  font-size: 0.9rem;
}

.department {
  color: #888;
  font-style: italic;
}

.room {
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  display: inline-block;
  margin-top: 4px;
}

.no-classes, .no-upcoming {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
  color: #666;
}

.no-classes mat-icon, .no-upcoming mat-icon {
  font-size: 48px;
  height: 48px;
  width: 48px;
  margin-bottom: 16px;
  color: #ccc;
}

.upcoming-class {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 20px;
}

.upcoming-time h2 {
  margin: 0;
  font-size: 2.5rem;
  color: #1976d2;
  font-weight: 600;
}

.upcoming-time p {
  margin: 4px 0 16px 0;
  color: #666;
}

.upcoming-details h3 {
  margin: 0 0 8px 0;
  color: #333;
}

.upcoming-details p {
  margin: 4px 0;
  color: #666;
}

.upcoming-room {
  background-color: #e8f5e8;
  color: #2e7d32;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.9rem;
  margin-top: 8px;
}

.recent-section {
  margin-bottom: 30px;
}

.attendance-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.attendance-item {
  display: grid;
  grid-template-columns: 2fr 2fr 1fr;
  gap: 16px;
  align-items: center;
  padding: 12px;
  background-color: #f9f9f9;
  border-radius: 6px;
}

.student-info {
  display: flex;
  flex-direction: column;
}

.student-name {
  font-weight: 500;
  color: #333;
}

.student-reg {
  font-size: 0.8rem;
  color: #666;
}

.attendance-details {
  display: flex;
  flex-direction: column;
}

.subject {
  font-weight: 500;
  color: #333;
  font-size: 0.9rem;
}

.date {
  font-size: 0.8rem;
  color: #666;
}

.attendance-status {
  display: flex;
  justify-content: center;
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
  color: #666;
}

.no-data mat-icon {
  font-size: 48px;
  height: 48px;
  width: 48px;
  margin-bottom: 16px;
  color: #ccc;
}

.quick-actions {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.quick-actions h2 {
  margin: 0 0 20px 0;
  color: #333;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.action-buttons button {
  display: flex;
  align-items: center;
  gap: 8px;
}

@media (max-width: 768px) {
  .teacher-dashboard {
    padding: 10px;
  }
  
  .dashboard-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .schedule-row {
    grid-template-columns: 1fr;
  }
  
  .attendance-item {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons button {
    width: 100%;
    justify-content: center;
  }
}

/* Additional Mobile Optimizations */
@media (max-width: 576px) {
  .teacher-dashboard {
    padding: 10px;
  }

  .dashboard-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
    padding: 15px;
  }

  .welcome-section h1 {
    font-size: 1.5rem;
  }

  .welcome-section p {
    font-size: 0.9rem;
  }

  .stats-grid {
    gap: 15px;
    margin-bottom: 20px;
  }

  .stat-card {
    padding: 15px;
  }

  .stat-content {
    gap: 10px;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
  }

  .stat-icon mat-icon {
    font-size: 1.5rem;
  }

  .stat-info h3 {
    font-size: 1.5rem;
  }

  .stat-info p {
    font-size: 0.8rem;
  }

  .section-title {
    font-size: 1.2rem;
  }

  .quick-actions-grid {
    gap: 10px;
  }

  .action-card {
    padding: 15px;
  }

  .action-icon {
    width: 40px;
    height: 40px;
  }

  .action-icon mat-icon {
    font-size: 1.5rem;
  }

  .action-title {
    font-size: 0.9rem;
  }

  .action-description {
    font-size: 0.8rem;
  }

  .schedule-item, .attendance-item {
    padding: 12px;
  }

  .schedule-time, .attendance-class {
    font-size: 0.8rem;
  }

  .schedule-subject, .attendance-subject {
    font-size: 0.9rem;
  }

  .schedule-class, .attendance-date {
    font-size: 0.75rem;
  }
}
