{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/checkbox\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/forms\";\nfunction AttendanceComponent_tr_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 14)(2, \"mat-checkbox\", 15);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\", 16);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\", 16);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 16);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\", 16)(11, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function AttendanceComponent_tr_23_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.seedetail());\n    });\n    i0.ɵɵelementStart(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"info\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const student_r2 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", student_r2.subject, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", student_r2.present, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", student_r2.session, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", student_r2.attendancepercentage, \" \");\n  }\n}\nfunction AttendanceComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"h2\");\n    i0.ɵɵtext(2, \"Attendance Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"table\")(6, \"thead\")(7, \"tr\", 5)(8, \"th\", 6)(9, \"mat-checkbox\", 7);\n    i0.ɵɵlistener(\"ngModelChange\", function AttendanceComponent_div_33_Template_mat_checkbox_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.isChecked = $event);\n    })(\"change\", function AttendanceComponent_div_33_Template_mat_checkbox_change_9_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.toggleCheckbox());\n    });\n    i0.ɵɵtext(10, \" Date \");\n    i0.ɵɵelementStart(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"arrow_downward\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(13, \"th\", 8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"tbody\")(15, \"tr\")(16, \"td\", 14)(17, \"mat-checkbox\", 15);\n    i0.ɵɵtext(18, \" Nov 30, 2024 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(19, \"td\", 16);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(20, \"div\", 10)(21, \"div\")(22, \"button\", 11);\n    i0.ɵɵtext(23, \"Previous\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 12);\n    i0.ɵɵtext(25, \"Page 1 of 1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\")(27, \"button\", 11);\n    i0.ɵɵtext(28, \"Next\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.isChecked)(\"indeterminate\", ctx_r1.isIndeterminate);\n  }\n}\nexport let AttendanceComponent = /*#__PURE__*/(() => {\n  class AttendanceComponent {\n    constructor(router) {\n      this.router = router;\n      this.isChecked = false;\n      this.isIndeterminate = true;\n      this.studentstable = false;\n      this.detailtable = false;\n      this.students = [{\n        subject: 'English',\n        present: '10',\n        session: '2024',\n        attendancepercentage: '90%',\n        students: '20'\n      }, {\n        subject: 'Urdu',\n        present: '16',\n        session: '2024',\n        attendancepercentage: '99%',\n        students: '20'\n      }];\n    }\n    toggleCheckbox() {\n      if (this.isIndeterminate) {\n        this.isIndeterminate = false;\n        this.isChecked = true;\n      } else if (this.isChecked) {\n        this.isChecked = false;\n      } else {\n        this.isIndeterminate = true;\n      }\n    }\n    seedetail() {\n      this.detailtable = !this.detailtable;\n    }\n    static #_ = this.ɵfac = function AttendanceComponent_Factory(t) {\n      return new (t || AttendanceComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AttendanceComponent,\n      selectors: [[\"app-attendance\"]],\n      decls: 34,\n      vars: 4,\n      consts: [[1, \"fw-bold\"], [1, \"maindiv\"], [1, \"secondarydiv\"], [1, \"table-container\", \"mb-4\"], [1, \"table-responsive\"], [1, \"tablehead\"], [1, \"one\"], [\"color\", \"primary\", 3, \"ngModel\", \"indeterminate\", \"ngModelChange\", \"change\"], [1, \"two\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"p-3\", \"align-items-center\", \"justify-content-between\"], [1, \"btn\", \"btn-top\", \"border\"], [1, \"page\"], [\"id\", \"details\", 4, \"ngIf\"], [1, \"name\"], [\"color\", \"primary\"], [1, \"para\"], [1, \"btn\", 3, \"click\"], [\"id\", \"details\"]],\n      template: function AttendanceComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"h1\", 0);\n          i0.ɵɵtext(1, \"Attendance Details\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"div\", 1)(3, \"div\", 2)(4, \"div\", 3)(5, \"div\", 4)(6, \"table\")(7, \"thead\")(8, \"tr\", 5)(9, \"th\", 6)(10, \"mat-checkbox\", 7);\n          i0.ɵɵlistener(\"ngModelChange\", function AttendanceComponent_Template_mat_checkbox_ngModelChange_10_listener($event) {\n            return ctx.isChecked = $event;\n          })(\"change\", function AttendanceComponent_Template_mat_checkbox_change_10_listener() {\n            return ctx.toggleCheckbox();\n          });\n          i0.ɵɵtext(11, \" Subject \");\n          i0.ɵɵelementStart(12, \"mat-icon\");\n          i0.ɵɵtext(13, \"arrow_downward\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"th\");\n          i0.ɵɵtext(15, \"Present \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"th\");\n          i0.ɵɵtext(17, \"Session \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"th\");\n          i0.ɵɵtext(19, \"Attendance Percentage \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"th\", 8);\n          i0.ɵɵtext(21, \"Actions \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(22, \"tbody\");\n          i0.ɵɵtemplate(23, AttendanceComponent_tr_23_Template, 14, 4, \"tr\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(24, \"div\", 10)(25, \"div\")(26, \"button\", 11);\n          i0.ɵɵtext(27, \"Previous\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"div\", 12);\n          i0.ɵɵtext(29, \"Page 1 of 1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"div\")(31, \"button\", 11);\n          i0.ɵɵtext(32, \"Next\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(33, AttendanceComponent_div_33_Template, 29, 2, \"div\", 13);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngModel\", ctx.isChecked)(\"indeterminate\", ctx.isIndeterminate);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngForOf\", ctx.students);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngIf\", ctx.detailtable);\n        }\n      },\n      dependencies: [i2.NgForOf, i2.NgIf, i3.MatCheckbox, i4.MatIcon, i5.NgControlStatus, i5.NgModel]\n    });\n  }\n  return AttendanceComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}