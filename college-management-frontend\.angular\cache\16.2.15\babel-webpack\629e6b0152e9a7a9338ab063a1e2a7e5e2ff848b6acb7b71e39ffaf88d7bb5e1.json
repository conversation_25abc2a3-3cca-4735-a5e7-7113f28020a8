{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport let RoleService = /*#__PURE__*/(() => {\n  class RoleService {\n    constructor() {\n      this.userRoleSubject = new BehaviorSubject('');\n      this.userRole$ = this.userRoleSubject.asObservable();\n      // Initialize user role from local storage\n      const storedRole = localStorage.getItem('userRole');\n      if (storedRole) {\n        this.userRoleSubject.next(storedRole);\n      }\n    }\n    setRole(role) {\n      this.userRoleSubject.next(role);\n      // Store user role in local storage\n      localStorage.setItem('userRole', role);\n    }\n    getRole() {\n      return this.userRoleSubject.getValue();\n    }\n    logout() {\n      // Remove user role from local storage\n      localStorage.removeItem('userRole');\n      this.userRoleSubject.next('');\n    }\n    static #_ = this.ɵfac = function RoleService_Factory(t) {\n      return new (t || RoleService)();\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: RoleService,\n      factory: RoleService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return RoleService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}