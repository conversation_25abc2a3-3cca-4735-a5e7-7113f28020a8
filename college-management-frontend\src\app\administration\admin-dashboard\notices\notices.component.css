.notices-container {
  padding: 15px;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* Header Section */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px 0;
}

.header-content {
  flex: 1;
}

.page-title {
  margin: 0;
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.title-icon {
  font-size: 1.8rem;
  color: #3498db;
}

.page-subtitle {
  margin: 5px 0 0 0;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.refresh-btn, .create-btn {
  transition: all 0.3s ease;
}

.create-btn {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  border: none;
  box-shadow: 0 4px 6px rgba(52, 152, 219, 0.3);
}

.create-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 10px rgba(52, 152, 219, 0.4);
}

/* Compact Filters */
.filters-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filters-card mat-card-content {
  padding: 15px !important;
}

.compact-filters {
  margin: 0;
}

.compact-field {
  width: 100%;
}

.compact-field .mat-form-field-wrapper {
  padding-bottom: 0;
}

.compact-field .mat-form-field-infix {
  padding: 8px 0;
  border-top: none;
}

.compact-field .mat-form-field-label {
  font-size: 0.875rem;
}

.reset-btn {
  color: #6c757d;
  transition: all 0.3s ease;
}

.reset-btn:hover {
  color: #495057;
  background-color: #f8f9fa;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .notices-container {
    padding: 10px;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }

  .compact-filters .row {
    margin: 0;
  }

  .compact-filters .col-6,
  .compact-filters .col-12 {
    padding: 2px;
    margin-bottom: 8px;
  }

  .notice-card {
    margin-bottom: 15px;
  }

  .notice-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .notice-actions {
    width: 100%;
    justify-content: space-between;
  }
}

@media (max-width: 576px) {
  .notices-container {
    padding: 8px;
  }

  .page-title {
    font-size: 1.3rem;
  }

  .title-icon {
    font-size: 1.3rem;
  }

  .page-subtitle {
    font-size: 0.8rem;
  }

  .create-btn span {
    display: none !important;
  }

  .notice-form {
    padding: 15px;
  }

  .form-section {
    padding: 15px;
    margin-bottom: 15px;
  }

  .section-title {
    font-size: 1rem;
  }

  .notice-card {
    padding: 15px;
  }

  .notice-title {
    font-size: 1rem;
  }

  .notice-meta {
    flex-direction: column;
    gap: 5px;
  }

  .notice-actions {
    flex-direction: column;
    gap: 8px;
  }

  .action-btn {
    width: 100%;
    justify-content: center;
  }
}

.filter-actions {
  display: flex;
  gap: 10px;
}

.form-card {
  margin-bottom: 20px;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 20px;
}

.full-width {
  grid-column: 1 / -1;
}

.checkbox-group {
  grid-column: 1 / -1;
  display: flex;
  gap: 20px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

.notices-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.notice-card {
  transition: box-shadow 0.3s ease;
}

.notice-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.notice-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
}

.notice-title-section h3 {
  margin: 0 0 10px 0;
  color: #333;
  font-weight: 500;
}

.notice-badges {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.category-badge {
  background-color: #e3f2fd;
  color: #1976d2;
}

.priority-badge {
  color: white;
}

.priority-low {
  background-color: #4caf50;
}

.priority-medium {
  background-color: #ff9800;
}

.priority-high {
  background-color: #f44336;
}

.priority-urgent {
  background-color: #d32f2f;
  animation: pulse 2s infinite;
}

.status-badge {
  color: white;
}

.status-active {
  background-color: #4caf50;
}

.status-draft {
  background-color: #9e9e9e;
}

.status-expired {
  background-color: #f44336;
}

.status-scheduled {
  background-color: #2196f3;
}

.pinned-badge {
  background-color: #ffc107;
  color: #333;
  display: flex;
  align-items: center;
  gap: 4px;
}

.pinned-badge mat-icon {
  font-size: 14px;
  width: 14px;
  height: 14px;
}

.notice-actions {
  display: flex;
  gap: 5px;
}

.notice-content {
  margin: 15px 0;
  color: #666;
  line-height: 1.6;
}

.notice-meta {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #e0e0e0;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #666;
  font-size: 14px;
}

.meta-item mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  color: #666;
}

.empty-state mat-icon {
  font-size: 64px;
  width: 64px;
  height: 64px;
  margin-bottom: 20px;
  color: #ccc;
}

.empty-state h3 {
  margin: 0 0 10px 0;
  color: #333;
}

.empty-state p {
  margin: 0 0 20px 0;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(211, 47, 47, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(211, 47, 47, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(211, 47, 47, 0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .filters-grid {
    grid-template-columns: 1fr;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .notice-header {
    flex-direction: column;
    gap: 15px;
  }
  
  .notice-actions {
    align-self: flex-end;
  }
  
  .notice-meta {
    flex-direction: column;
    gap: 10px;
  }
}
