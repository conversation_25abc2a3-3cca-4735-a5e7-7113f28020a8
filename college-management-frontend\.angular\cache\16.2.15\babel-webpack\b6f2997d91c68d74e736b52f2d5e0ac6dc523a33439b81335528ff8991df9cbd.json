{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let DepartmentService = /*#__PURE__*/(() => {\n  class DepartmentService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = environment.apiUrl; // Replace with your backend URL\n      this.departmentUrl = this.apiUrl + '/department';\n    }\n    // Get all department\n    getdepartment() {\n      return this.http.get(this.departmentUrl);\n    }\n    getdepartmentById(id) {\n      return this.http.get(`${this.apiUrl}/department/${id}`);\n    }\n    // Add a new department\n    adddepartment(departmentData) {\n      return this.http.post(this.departmentUrl, departmentData);\n    }\n    // Update an existing department\n    updatedepartment(id, departmentData) {\n      return this.http.put(`${this.departmentUrl}/${id}`, departmentData);\n    }\n    // Delete a department\n    deletedepartment(id) {\n      return this.http.delete(`${this.departmentUrl}/${id}`);\n    }\n    static #_ = this.ɵfac = function DepartmentService_Factory(t) {\n      return new (t || DepartmentService)(i0.ɵɵinject(i1.HttpClient));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: DepartmentService,\n      factory: DepartmentService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return DepartmentService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}