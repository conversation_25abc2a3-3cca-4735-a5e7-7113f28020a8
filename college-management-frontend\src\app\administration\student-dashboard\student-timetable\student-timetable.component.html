<div class="student-timetable-container">
  <!-- Header -->
  <div class="page-header">
    <div class="header-content">
      <h1>My Timetable</h1>
      <p>View your weekly class schedule</p>
    </div>
    <button mat-icon-button (click)="refreshData()" matTooltip="Refresh Data">
      <mat-icon>refresh</mat-icon>
    </button>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="loading-container">
    <mat-spinner></mat-spinner>
    <p>Loading your timetable...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !loading" class="error-container">
    <mat-icon>error</mat-icon>
    <h3>Error Loading Timetable</h3>
    <p>{{ error }}</p>
    <button mat-raised-button color="primary" (click)="refreshData()">
      <mat-icon>refresh</mat-icon>
      Try Again
    </button>
  </div>

  <!-- Content -->
  <div *ngIf="studentDashboard && !loading && !error" class="timetable-content">
    <!-- Current Day Highlight -->
    <div class="current-day-info">
      <mat-card class="current-day-card">
        <mat-card-content>
          <div class="current-day-content">
            <mat-icon>today</mat-icon>
            <div class="day-info">
              <h3>Today is {{ getCurrentDay() | titlecase }}</h3>
              <p>{{ getCurrentDate() | date:'fullDate' }}</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Timetable Grid -->
    <div class="timetable-wrapper">
      <mat-card class="timetable-card">
        <mat-card-header>
          <mat-card-title>Weekly Schedule</mat-card-title>
          <mat-card-subtitle>Your class timetable for this week</mat-card-subtitle>
        </mat-card-header>
        
        <mat-card-content>
          <div class="timetable-grid">
            <!-- Header Row -->
            <div class="timetable-header">
              <div class="time-header">Time</div>
              <div *ngFor="let day of weekDays" class="day-header" 
                   [class.current-day]="day.toLowerCase() === getCurrentDay()">
                {{ day }}
              </div>
            </div>

            <!-- Time Slots -->
            <div *ngFor="let slot of timetableData" class="timetable-row"
                 [class.current-time]="isCurrentTimeSlot(slot.time)">
              <div class="time-slot">{{ slot.time }}</div>
              
              <div *ngFor="let day of weekDays" class="schedule-cell"
                   [class.current-day]="day.toLowerCase() === getCurrentDay()"
                   [class.has-class]="slot[day.toLowerCase()]"
                   [class.break-time]="slot[day.toLowerCase()]?.isBreak">
                
                <div *ngIf="slot[day.toLowerCase()]" class="class-info">
                  <div class="subject-name" 
                       [class.break]="slot[day.toLowerCase()].isBreak">
                    {{ slot[day.toLowerCase()].subject }}
                  </div>
                  <div *ngIf="!slot[day.toLowerCase()].isBreak" class="class-details">
                    <div class="teacher">{{ slot[day.toLowerCase()].teacher }}</div>
                    <div class="room">{{ slot[day.toLowerCase()].room }}</div>
                    <div class="type" [class]="slot[day.toLowerCase()].type.toLowerCase()">
                      {{ slot[day.toLowerCase()].type }}
                    </div>
                  </div>
                  <div *ngIf="slot[day.toLowerCase()].isBreak" class="break-details">
                    <div class="room">{{ slot[day.toLowerCase()].room }}</div>
                  </div>
                </div>
                
                <div *ngIf="!slot[day.toLowerCase()]" class="free-period">
                  <span>Free</span>
                </div>
              </div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Legend -->
    <div class="legend-section">
      <mat-card class="legend-card">
        <mat-card-header>
          <mat-card-title>Legend</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="legend-items">
            <div class="legend-item">
              <div class="legend-color lecture"></div>
              <span>Lecture</span>
            </div>
            <div class="legend-item">
              <div class="legend-color lab"></div>
              <span>Lab</span>
            </div>
            <div class="legend-item">
              <div class="legend-color tutorial"></div>
              <span>Tutorial</span>
            </div>
            <div class="legend-item">
              <div class="legend-color break"></div>
              <span>Break</span>
            </div>
            <div class="legend-item">
              <div class="legend-color free"></div>
              <span>Free Period</span>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
      <h3>Quick Actions</h3>
      <div class="action-buttons">
        <button mat-raised-button color="primary" routerLink="/dashboard/student/subjects">
          <mat-icon>subject</mat-icon>
          View Subjects
        </button>
        <button mat-raised-button color="accent" routerLink="/dashboard/student/teachers">
          <mat-icon>person</mat-icon>
          View Teachers
        </button>
        <button mat-raised-button color="primary" routerLink="/dashboard/student/attendance">
          <mat-icon>fact_check</mat-icon>
          View Attendance
        </button>
      </div>
    </div>
  </div>
</div>
