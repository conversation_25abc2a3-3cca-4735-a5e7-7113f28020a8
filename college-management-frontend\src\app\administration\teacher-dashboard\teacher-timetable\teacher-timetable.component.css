.teacher-timetable-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.header-content h1 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
  color: #333;
}

.title-icon {
  color: #2196f3;
}

.page-subtitle {
  margin: 5px 0 0 0;
  color: #666;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filters-card {
  margin-bottom: 20px;
}

.filters-row {
  display: flex;
  gap: 20px;
  align-items: center;
}

.filters-row mat-form-field {
  min-width: 200px;
}

.stats-section {
  margin-bottom: 20px;
}

.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon {
  font-size: 36px;
  width: 36px;
  height: 36px;
  opacity: 0.8;
}

.stat-info h3 {
  margin: 0;
  font-size: 28px;
  font-weight: bold;
}

.stat-info p {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

.current-day-info {
  margin-bottom: 20px;
}

.current-day-card {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.current-day-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.current-day-content mat-icon {
  font-size: 32px;
  width: 32px;
  height: 32px;
}

.day-info h3 {
  margin: 0;
  font-size: 20px;
}

.day-info p {
  margin: 0;
  opacity: 0.9;
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px;
  text-align: center;
}

.error-container mat-icon {
  font-size: 64px;
  width: 64px;
  height: 64px;
  color: #f44336;
  margin-bottom: 20px;
}

.timetable-wrapper {
  margin-bottom: 30px;
}

.timetable-grid {
  display: grid;
  grid-template-columns: 120px repeat(6, 1fr);
  gap: 1px;
  background-color: #e0e0e0;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.timetable-header {
  display: contents;
}

.time-header, .day-header {
  background-color: #3f51b5;
  color: white;
  padding: 15px 10px;
  font-weight: bold;
  text-align: center;
  font-size: 14px;
}

.current-day {
  background-color: #ff9800 !important;
}

.timetable-row {
  display: contents;
}

.current-time .time-slot,
.current-time .schedule-cell {
  background-color: #fff3e0 !important;
  border-left: 4px solid #ff9800;
}

.time-slot {
  background-color: #f5f5f5;
  padding: 15px 10px;
  font-weight: 500;
  text-align: center;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.schedule-cell {
  background-color: white;
  padding: 8px;
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.schedule-cell.has-class {
  background-color: #e8f5e8;
  border-left: 4px solid #4caf50;
}

.class-info {
  width: 100%;
  text-align: center;
}

.subject-name {
  font-weight: bold;
  color: #2e7d32;
  font-size: 13px;
  margin-bottom: 4px;
}

.class-details {
  font-size: 11px;
  color: #666;
  margin-bottom: 4px;
}

.class-name {
  display: block;
  font-weight: 500;
}

.program-info {
  display: block;
  font-style: italic;
}

.additional-info {
  font-size: 10px;
  color: #888;
}

.semester, .room {
  display: block;
}

.empty-slot {
  width: 100%;
  text-align: center;
  color: #ccc;
  font-style: italic;
  font-size: 12px;
}

.today-summary {
  margin-bottom: 20px;
}

.today-classes {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.today-class-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #2196f3;
}

.time-info {
  min-width: 120px;
}

.time {
  font-weight: bold;
  color: #2196f3;
}

.class-details h4 {
  margin: 0 0 5px 0;
  color: #333;
}

.class-details p {
  margin: 0 0 3px 0;
  color: #666;
}

.class-details small {
  color: #888;
}

.room-info {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #666;
  font-size: 12px;
}

.no-classes {
  text-align: center;
  padding: 40px;
  color: #666;
}

.no-classes mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  margin-bottom: 15px;
  color: #4caf50;
}

.empty-state {
  margin-top: 40px;
}

.empty-content {
  text-align: center;
  padding: 60px;
}

.empty-content mat-icon {
  font-size: 64px;
  width: 64px;
  height: 64px;
  color: #ccc;
  margin-bottom: 20px;
}

.empty-content h3 {
  margin: 0 0 15px 0;
  color: #333;
}

.empty-content p {
  margin: 0 0 10px 0;
  color: #666;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .filters-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .stats-row {
    grid-template-columns: 1fr;
  }
  
  .timetable-grid {
    grid-template-columns: 80px repeat(6, 1fr);
    font-size: 10px;
  }
  
  .today-class-item {
    flex-direction: column;
    align-items: flex-start;
  }
}
