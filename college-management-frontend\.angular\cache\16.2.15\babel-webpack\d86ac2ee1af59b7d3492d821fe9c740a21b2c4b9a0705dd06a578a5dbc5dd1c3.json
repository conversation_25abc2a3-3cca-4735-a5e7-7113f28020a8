{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/dashboard.service\";\nimport * as i2 from \"../../../services/user.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/chips\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/progress-spinner\";\nimport * as i10 from \"@angular/material/tooltip\";\nfunction StudentOverviewComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading your dashboard...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StudentOverviewComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"mat-icon\", 6);\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function StudentOverviewComponent_div_2_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.refreshData());\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" Retry \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction StudentOverviewComponent_div_3_div_67_div_1_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 44);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const classItem_r15 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(classItem_r15.room);\n  }\n}\nfunction StudentOverviewComponent_div_3_div_67_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"div\", 40)(2, \"span\", 41);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 41);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 42)(7, \"h4\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, StudentOverviewComponent_div_3_div_67_div_1_span_11_Template, 2, 1, \"span\", 43);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const classItem_r15 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(classItem_r15.timeSlot.startTime);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(classItem_r15.timeSlot.endTime);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(classItem_r15.subject.subjectName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(classItem_r15.teacher.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", classItem_r15.room);\n  }\n}\nfunction StudentOverviewComponent_div_3_div_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtemplate(1, StudentOverviewComponent_div_3_div_67_div_1_Template, 12, 5, \"div\", 38);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.getTodayClasses());\n  }\n}\nfunction StudentOverviewComponent_div_3_ng_template_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"event_available\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No classes scheduled for today\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StudentOverviewComponent_div_3_div_75_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 49)(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 50)(7, \"div\", 51);\n    i0.ɵɵelement(8, \"div\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 53);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"number\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const subject_r19 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(subject_r19.subjectName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", subject_r19.present, \"/\", subject_r19.total, \" classes\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"width\", subject_r19.percentage, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(11, 6, subject_r19.percentage, \"1.0-0\"), \"%\");\n  }\n}\nfunction StudentOverviewComponent_div_3_div_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵtemplate(1, StudentOverviewComponent_div_3_div_75_div_1_Template, 12, 9, \"div\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.studentDashboard.subjectAttendance);\n  }\n}\nfunction StudentOverviewComponent_div_3_ng_template_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"subject\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No attendance data available\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StudentOverviewComponent_div_3_div_86_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"div\", 58)(2, \"span\", 59);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 60);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 49)(9, \"span\", 61);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 62);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 63)(14, \"mat-chip\", 64);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"titlecase\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const attendance_r21 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(4, 6, attendance_r21.date, \"shortDate\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(7, 9, attendance_r21.date, \"EEEE\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(attendance_r21.subject.subjectName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(attendance_r21.teacher.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"color\", attendance_r21.status === \"present\" ? \"primary\" : \"warn\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(16, 12, attendance_r21.status), \" \");\n  }\n}\nfunction StudentOverviewComponent_div_3_div_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtemplate(1, StudentOverviewComponent_div_3_div_86_div_1_Template, 17, 14, \"div\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r11.studentDashboard.attendanceHistory.slice(0, 10));\n  }\n}\nfunction StudentOverviewComponent_div_3_ng_template_87_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"history\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No attendance history available\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StudentOverviewComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"div\", 10)(3, \"h1\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 11);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function StudentOverviewComponent_div_3_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.refreshData());\n    });\n    i0.ɵɵelementStart(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"refresh\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 13)(13, \"mat-card\", 14)(14, \"mat-card-content\")(15, \"div\", 15)(16, \"div\", 16)(17, \"mat-icon\");\n    i0.ɵɵtext(18, \"fact_check\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 17)(20, \"h3\");\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"p\");\n    i0.ɵɵtext(23, \"Overall Attendance\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(24, \"mat-card\", 18)(25, \"mat-card-content\")(26, \"div\", 15)(27, \"div\", 16)(28, \"mat-icon\");\n    i0.ɵɵtext(29, \"check_circle\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 17)(31, \"h3\");\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"p\");\n    i0.ɵɵtext(34, \"Classes Attended\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(35, \"mat-card\", 19)(36, \"mat-card-content\")(37, \"div\", 15)(38, \"div\", 16)(39, \"mat-icon\");\n    i0.ɵɵtext(40, \"cancel\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"div\", 17)(42, \"h3\");\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"p\");\n    i0.ɵɵtext(45, \"Classes Missed\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(46, \"mat-card\", 20)(47, \"mat-card-content\")(48, \"div\", 15)(49, \"div\", 16)(50, \"mat-icon\");\n    i0.ɵɵtext(51, \"school\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(52, \"div\", 17)(53, \"h3\");\n    i0.ɵɵtext(54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"p\");\n    i0.ɵɵtext(56, \"Total Classes\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(57, \"div\", 21)(58, \"div\", 22)(59, \"mat-card\", 23)(60, \"mat-card-header\")(61, \"mat-card-title\");\n    i0.ɵɵtext(62, \"Today's Schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(63, \"mat-card-subtitle\");\n    i0.ɵɵtext(64);\n    i0.ɵɵpipe(65, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(66, \"mat-card-content\");\n    i0.ɵɵtemplate(67, StudentOverviewComponent_div_3_div_67_Template, 2, 1, \"div\", 24);\n    i0.ɵɵtemplate(68, StudentOverviewComponent_div_3_ng_template_68_Template, 5, 0, \"ng-template\", null, 25, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(70, \"mat-card\", 26)(71, \"mat-card-header\")(72, \"mat-card-title\");\n    i0.ɵɵtext(73, \"Subject-wise Attendance\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(74, \"mat-card-content\");\n    i0.ɵɵtemplate(75, StudentOverviewComponent_div_3_div_75_Template, 2, 1, \"div\", 27);\n    i0.ɵɵtemplate(76, StudentOverviewComponent_div_3_ng_template_76_Template, 5, 0, \"ng-template\", null, 28, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(78, \"div\", 29)(79, \"mat-card\")(80, \"mat-card-header\")(81, \"mat-card-title\");\n    i0.ɵɵtext(82, \"Recent Attendance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(83, \"mat-card-subtitle\");\n    i0.ɵɵtext(84, \"Last 10 attendance records\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(85, \"mat-card-content\");\n    i0.ɵɵtemplate(86, StudentOverviewComponent_div_3_div_86_Template, 2, 1, \"div\", 30);\n    i0.ɵɵtemplate(87, StudentOverviewComponent_div_3_ng_template_87_Template, 5, 0, \"ng-template\", null, 31, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(89, \"div\", 32)(90, \"h2\");\n    i0.ɵɵtext(91, \"Quick Actions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(92, \"div\", 33)(93, \"button\", 34)(94, \"mat-icon\");\n    i0.ɵɵtext(95, \"fact_check\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(96, \" View Full Attendance \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(97, \"button\", 35)(98, \"mat-icon\");\n    i0.ɵɵtext(99, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(100, \" View Timetable \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(101, \"button\", 36)(102, \"mat-icon\");\n    i0.ɵɵtext(103, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(104, \" View Teachers \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const _r6 = i0.ɵɵreference(69);\n    const _r9 = i0.ɵɵreference(77);\n    const _r12 = i0.ɵɵreference(88);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Welcome, \", ctx_r2.currentUser == null ? null : ctx_r2.currentUser.name, \"!\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.studentDashboard.student.program == null ? null : ctx_r2.studentDashboard.student.program.name, \" - \", ctx_r2.studentDashboard.student.department == null ? null : ctx_r2.studentDashboard.student.department.name, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.studentDashboard.student.regNo, \" | Semester \", ctx_r2.studentDashboard.student.semester, \"\");\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.getOverallAttendancePercentage(), \"%\");\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r2.getPresentClasses());\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r2.getAbsentClasses());\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r2.getTotalClasses());\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(65, 16, ctx_r2.currentDate, \"fullDate\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getTodayClasses().length > 0)(\"ngIfElse\", _r6);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.studentDashboard.subjectAttendance.length > 0)(\"ngIfElse\", _r9);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.studentDashboard.attendanceHistory.length > 0)(\"ngIfElse\", _r12);\n  }\n}\nexport class StudentOverviewComponent {\n  constructor(dashboardService, userService) {\n    this.dashboardService = dashboardService;\n    this.userService = userService;\n    this.studentDashboard = null;\n    this.loading = true;\n    this.error = null;\n    this.currentDate = new Date();\n  }\n  ngOnInit() {\n    this.currentUser = this.userService.getUserFromLocalStorage()?.user;\n    if (this.currentUser) {\n      this.loadStudentDashboard();\n    } else {\n      this.error = 'User not found';\n      this.loading = false;\n    }\n  }\n  loadStudentDashboard() {\n    this.loading = true;\n    this.error = null;\n    this.dashboardService.getStudentDashboard(this.currentUser._id).subscribe({\n      next: response => {\n        if (response.success) {\n          this.studentDashboard = response.dashboard;\n        } else {\n          this.error = 'Failed to load student dashboard';\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading student dashboard:', error);\n        this.error = 'Error loading dashboard data';\n        this.loading = false;\n      }\n    });\n  }\n  refreshData() {\n    this.loadStudentDashboard();\n  }\n  getTodayClasses() {\n    if (!this.studentDashboard?.timetable) return [];\n    const today = new Date().toLocaleDateString('en-US', {\n      weekday: 'long'\n    });\n    return this.studentDashboard.timetable.filter(entry => entry.dayOfWeek === today);\n  }\n  getUpcomingClass() {\n    const todayClasses = this.getTodayClasses();\n    const now = new Date();\n    const currentTime = now.getHours() * 60 + now.getMinutes();\n    return todayClasses.find(classItem => {\n      const [hours, minutes] = classItem.timeSlot.startTime.split(':').map(Number);\n      const classTime = hours * 60 + minutes;\n      return classTime > currentTime;\n    });\n  }\n  getOverallAttendancePercentage() {\n    if (!this.studentDashboard?.attendanceStats.length) return 0;\n    const totalAttendance = this.studentDashboard.attendanceStats.reduce((sum, stat) => sum + stat.count, 0);\n    const presentCount = this.studentDashboard.attendanceStats.find(stat => stat._id === 'present')?.count || 0;\n    return totalAttendance > 0 ? Math.round(presentCount / totalAttendance * 100) : 0;\n  }\n  getTotalClasses() {\n    if (!this.studentDashboard?.attendanceStats.length) return 0;\n    return this.studentDashboard.attendanceStats.reduce((sum, stat) => sum + stat.count, 0);\n  }\n  getPresentClasses() {\n    if (!this.studentDashboard?.attendanceStats.length) return 0;\n    return this.studentDashboard.attendanceStats.find(stat => stat._id === 'present')?.count || 0;\n  }\n  getAbsentClasses() {\n    if (!this.studentDashboard?.attendanceStats.length) return 0;\n    return this.studentDashboard.attendanceStats.find(stat => stat._id === 'absent')?.count || 0;\n  }\n  static #_ = this.ɵfac = function StudentOverviewComponent_Factory(t) {\n    return new (t || StudentOverviewComponent)(i0.ɵɵdirectiveInject(i1.DashboardService), i0.ɵɵdirectiveInject(i2.UserService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: StudentOverviewComponent,\n    selectors: [[\"app-student-overview\"]],\n    decls: 4,\n    vars: 3,\n    consts: [[1, \"student-dashboard\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"dashboard-content\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"error-container\"], [\"color\", \"warn\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"dashboard-content\"], [1, \"dashboard-header\"], [1, \"welcome-section\"], [1, \"student-info\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Refresh Data\", 3, \"click\"], [1, \"stats-grid\"], [1, \"stat-card\", \"attendance\"], [1, \"stat-content\"], [1, \"stat-icon\"], [1, \"stat-info\"], [1, \"stat-card\", \"present\"], [1, \"stat-card\", \"absent\"], [1, \"stat-card\", \"total\"], [1, \"content-section\"], [1, \"content-row\"], [1, \"schedule-card\"], [\"class\", \"classes-list\", 4, \"ngIf\", \"ngIfElse\"], [\"noClassesToday\", \"\"], [1, \"attendance-card\"], [\"class\", \"subject-attendance\", 4, \"ngIf\", \"ngIfElse\"], [\"noSubjectData\", \"\"], [1, \"recent-section\"], [\"class\", \"attendance-history\", 4, \"ngIf\", \"ngIfElse\"], [\"noRecentAttendance\", \"\"], [1, \"quick-actions\"], [1, \"action-buttons\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/dashboard/student/attendance\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"routerLink\", \"/dashboard/student/timetable\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/dashboard/student/teachers\"], [1, \"classes-list\"], [\"class\", \"class-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"class-item\"], [1, \"time-slot\"], [1, \"time\"], [1, \"class-details\"], [\"class\", \"room\", 4, \"ngIf\"], [1, \"room\"], [1, \"no-classes\"], [1, \"subject-attendance\"], [\"class\", \"subject-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"subject-item\"], [1, \"subject-info\"], [1, \"attendance-bar\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"percentage\"], [1, \"no-data\"], [1, \"attendance-history\"], [\"class\", \"history-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"history-item\"], [1, \"date-info\"], [1, \"date\"], [1, \"day\"], [1, \"subject\"], [1, \"teacher\"], [1, \"status-info\"], [\"selected\", \"\", 3, \"color\"]],\n    template: function StudentOverviewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵtemplate(1, StudentOverviewComponent_div_1_Template, 4, 0, \"div\", 1);\n        i0.ɵɵtemplate(2, StudentOverviewComponent_div_2_Template, 9, 1, \"div\", 2);\n        i0.ɵɵtemplate(3, StudentOverviewComponent_div_3_Template, 105, 19, \"div\", 3);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.studentDashboard && !ctx.loading && !ctx.error);\n      }\n    },\n    dependencies: [i3.NgForOf, i3.NgIf, i4.RouterLink, i5.MatButton, i5.MatIconButton, i6.MatCard, i6.MatCardContent, i6.MatCardHeader, i6.MatCardSubtitle, i6.MatCardTitle, i7.MatChip, i8.MatIcon, i9.MatProgressSpinner, i10.MatTooltip, i3.DecimalPipe, i3.TitleCasePipe, i3.DatePipe],\n    styles: [\".student-dashboard[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background-color: #f5f5f5;\\n  min-height: 100vh;\\n}\\n\\n.loading-container[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 400px;\\n  text-align: center;\\n}\\n\\n.error-container[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  margin-bottom: 16px;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 30px;\\n  background: white;\\n  padding: 20px;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\\n}\\n\\n.welcome-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #333;\\n  font-weight: 500;\\n}\\n\\n.welcome-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 8px 0 0 0;\\n  color: #666;\\n}\\n\\n.student-info[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #1976d2;\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 30px;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease-in-out;\\n}\\n\\n.stat-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 8px rgba(0,0,0,0.12);\\n}\\n\\n.stat-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n\\n.stat-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  color: white;\\n}\\n\\n.stat-card.attendance[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n}\\n\\n.stat-card.present[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4caf50 0%, #8bc34a 100%);\\n}\\n\\n.stat-card.absent[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f44336 0%, #ff9800 100%);\\n}\\n\\n.stat-card.total[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #2196f3 0%, #03a9f4 100%);\\n}\\n\\n.stat-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  height: 28px;\\n  width: 28px;\\n}\\n\\n.stat-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 2rem;\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.stat-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 4px 0 0 0;\\n  color: #666;\\n  font-size: 0.9rem;\\n}\\n\\n.content-section[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.content-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 20px;\\n}\\n\\n.schedule-card[_ngcontent-%COMP%], .attendance-card[_ngcontent-%COMP%] {\\n  min-height: 300px;\\n}\\n\\n.classes-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n}\\n\\n.class-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  padding: 16px;\\n  background-color: #f9f9f9;\\n  border-radius: 8px;\\n  border-left: 4px solid #1976d2;\\n}\\n\\n.time-slot[_ngcontent-%COMP%] {\\n  min-width: 80px;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n}\\n\\n.time[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #1976d2;\\n  font-size: 0.9rem;\\n}\\n\\n.class-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  color: #333;\\n  font-size: 1.1rem;\\n}\\n\\n.class-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 2px 0;\\n  color: #666;\\n  font-size: 0.9rem;\\n}\\n\\n.room[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1976d2;\\n  padding: 2px 8px;\\n  border-radius: 12px;\\n  font-size: 0.8rem;\\n  display: inline-block;\\n  margin-top: 4px;\\n}\\n\\n.no-classes[_ngcontent-%COMP%], .no-data[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px;\\n  text-align: center;\\n  color: #666;\\n}\\n\\n.no-classes[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%], .no-data[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  margin-bottom: 16px;\\n  color: #ccc;\\n}\\n\\n.subject-attendance[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n\\n.subject-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 12px;\\n  background-color: #f9f9f9;\\n  border-radius: 8px;\\n}\\n\\n.subject-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  color: #333;\\n  font-size: 1rem;\\n}\\n\\n.subject-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 0.8rem;\\n}\\n\\n.attendance-bar[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  min-width: 120px;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 8px;\\n  background-color: #e0e0e0;\\n  border-radius: 4px;\\n  overflow: hidden;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(90deg, #4caf50 0%, #8bc34a 100%);\\n  border-radius: 4px;\\n  transition: width 0.3s ease;\\n}\\n\\n.percentage[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #333;\\n  font-size: 0.9rem;\\n  min-width: 35px;\\n}\\n\\n.recent-section[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.attendance-history[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n\\n.history-item[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 2fr 1fr;\\n  gap: 16px;\\n  align-items: center;\\n  padding: 12px;\\n  background-color: #f9f9f9;\\n  border-radius: 6px;\\n}\\n\\n.date-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.date[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n  font-size: 0.9rem;\\n}\\n\\n.day[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #666;\\n}\\n\\n.subject-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.subject[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n  font-size: 0.9rem;\\n}\\n\\n.teacher[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #666;\\n}\\n\\n.status-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n}\\n\\n.quick-actions[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 24px;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\\n}\\n\\n.quick-actions[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0 0 20px 0;\\n  color: #333;\\n  font-weight: 500;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 12px;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n@media (max-width: 768px) {\\n  .student-dashboard[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  \\n  .dashboard-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 16px;\\n    align-items: stretch;\\n  }\\n  \\n  .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  \\n  .content-row[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  \\n  .history-item[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 8px;\\n  }\\n  \\n  .action-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  \\n  .action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYWRtaW5pc3RyYXRpb24vc3R1ZGVudC1kYXNoYm9hcmQvc3R1ZGVudC1vdmVydmlldy9zdHVkZW50LW92ZXJ2aWV3LmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxhQUFhO0VBQ2IseUJBQXlCO0VBQ3pCLGlCQUFpQjtBQUNuQjs7QUFFQTtFQUNFLGFBQWE7RUFDYixzQkFBc0I7RUFDdEIsbUJBQW1CO0VBQ25CLHVCQUF1QjtFQUN2QixhQUFhO0VBQ2Isa0JBQWtCO0FBQ3BCOztBQUVBO0VBQ0UsZUFBZTtFQUNmLFlBQVk7RUFDWixXQUFXO0VBQ1gsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0UsYUFBYTtFQUNiLDhCQUE4QjtFQUM5QixtQkFBbUI7RUFDbkIsbUJBQW1CO0VBQ25CLGlCQUFpQjtFQUNqQixhQUFhO0VBQ2Isa0JBQWtCO0VBQ2xCLHFDQUFxQztBQUN2Qzs7QUFFQTtFQUNFLFNBQVM7RUFDVCxXQUFXO0VBQ1gsZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0UsaUJBQWlCO0VBQ2pCLFdBQVc7QUFDYjs7QUFFQTtFQUNFLGdCQUFnQjtFQUNoQixjQUFjO0FBQ2hCOztBQUVBO0VBQ0UsYUFBYTtFQUNiLDJEQUEyRDtFQUMzRCxTQUFTO0VBQ1QsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0Usc0NBQXNDO0FBQ3hDOztBQUVBO0VBQ0UsMkJBQTJCO0VBQzNCLHNDQUFzQztBQUN4Qzs7QUFFQTtFQUNFLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsU0FBUztBQUNYOztBQUVBO0VBQ0UsYUFBYTtFQUNiLG1CQUFtQjtFQUNuQix1QkFBdUI7RUFDdkIsV0FBVztFQUNYLFlBQVk7RUFDWixrQkFBa0I7RUFDbEIsWUFBWTtBQUNkOztBQUVBO0VBQ0UsNkRBQTZEO0FBQy9EOztBQUVBO0VBQ0UsNkRBQTZEO0FBQy9EOztBQUVBO0VBQ0UsNkRBQTZEO0FBQy9EOztBQUVBO0VBQ0UsNkRBQTZEO0FBQy9EOztBQUVBO0VBQ0UsZUFBZTtFQUNmLFlBQVk7RUFDWixXQUFXO0FBQ2I7O0FBRUE7RUFDRSxTQUFTO0VBQ1QsZUFBZTtFQUNmLGdCQUFnQjtFQUNoQixXQUFXO0FBQ2I7O0FBRUE7RUFDRSxpQkFBaUI7RUFDakIsV0FBVztFQUNYLGlCQUFpQjtBQUNuQjs7QUFFQTtFQUNFLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLGFBQWE7RUFDYiw4QkFBOEI7RUFDOUIsU0FBUztBQUNYOztBQUVBO0VBQ0UsaUJBQWlCO0FBQ25COztBQUVBO0VBQ0UsYUFBYTtFQUNiLHNCQUFzQjtFQUN0QixTQUFTO0FBQ1g7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsU0FBUztFQUNULGFBQWE7RUFDYix5QkFBeUI7RUFDekIsa0JBQWtCO0VBQ2xCLDhCQUE4QjtBQUNoQzs7QUFFQTtFQUNFLGVBQWU7RUFDZixhQUFhO0VBQ2Isc0JBQXNCO0VBQ3RCLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLGdCQUFnQjtFQUNoQixjQUFjO0VBQ2QsaUJBQWlCO0FBQ25COztBQUVBO0VBQ0UsaUJBQWlCO0VBQ2pCLFdBQVc7RUFDWCxpQkFBaUI7QUFDbkI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsV0FBVztFQUNYLGlCQUFpQjtBQUNuQjs7QUFFQTtFQUNFLHlCQUF5QjtFQUN6QixjQUFjO0VBQ2QsZ0JBQWdCO0VBQ2hCLG1CQUFtQjtFQUNuQixpQkFBaUI7RUFDakIscUJBQXFCO0VBQ3JCLGVBQWU7QUFDakI7O0FBRUE7RUFDRSxhQUFhO0VBQ2Isc0JBQXNCO0VBQ3RCLG1CQUFtQjtFQUNuQix1QkFBdUI7RUFDdkIsYUFBYTtFQUNiLGtCQUFrQjtFQUNsQixXQUFXO0FBQ2I7O0FBRUE7RUFDRSxlQUFlO0VBQ2YsWUFBWTtFQUNaLFdBQVc7RUFDWCxtQkFBbUI7RUFDbkIsV0FBVztBQUNiOztBQUVBO0VBQ0UsYUFBYTtFQUNiLHNCQUFzQjtFQUN0QixTQUFTO0FBQ1g7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsOEJBQThCO0VBQzlCLG1CQUFtQjtFQUNuQixhQUFhO0VBQ2IseUJBQXlCO0VBQ3pCLGtCQUFrQjtBQUNwQjs7QUFFQTtFQUNFLGlCQUFpQjtFQUNqQixXQUFXO0VBQ1gsZUFBZTtBQUNqQjs7QUFFQTtFQUNFLFNBQVM7RUFDVCxXQUFXO0VBQ1gsaUJBQWlCO0FBQ25COztBQUVBO0VBQ0UsYUFBYTtFQUNiLG1CQUFtQjtFQUNuQixTQUFTO0VBQ1QsZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0UsV0FBVztFQUNYLFdBQVc7RUFDWCx5QkFBeUI7RUFDekIsa0JBQWtCO0VBQ2xCLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLFlBQVk7RUFDWiw0REFBNEQ7RUFDNUQsa0JBQWtCO0VBQ2xCLDJCQUEyQjtBQUM3Qjs7QUFFQTtFQUNFLGdCQUFnQjtFQUNoQixXQUFXO0VBQ1gsaUJBQWlCO0VBQ2pCLGVBQWU7QUFDakI7O0FBRUE7RUFDRSxtQkFBbUI7QUFDckI7O0FBRUE7RUFDRSxhQUFhO0VBQ2Isc0JBQXNCO0VBQ3RCLFFBQVE7QUFDVjs7QUFFQTtFQUNFLGFBQWE7RUFDYixrQ0FBa0M7RUFDbEMsU0FBUztFQUNULG1CQUFtQjtFQUNuQixhQUFhO0VBQ2IseUJBQXlCO0VBQ3pCLGtCQUFrQjtBQUNwQjs7QUFFQTtFQUNFLGFBQWE7RUFDYixzQkFBc0I7QUFDeEI7O0FBRUE7RUFDRSxnQkFBZ0I7RUFDaEIsV0FBVztFQUNYLGlCQUFpQjtBQUNuQjs7QUFFQTtFQUNFLGlCQUFpQjtFQUNqQixXQUFXO0FBQ2I7O0FBRUE7RUFDRSxhQUFhO0VBQ2Isc0JBQXNCO0FBQ3hCOztBQUVBO0VBQ0UsZ0JBQWdCO0VBQ2hCLFdBQVc7RUFDWCxpQkFBaUI7QUFDbkI7O0FBRUE7RUFDRSxpQkFBaUI7RUFDakIsV0FBVztBQUNiOztBQUVBO0VBQ0UsYUFBYTtFQUNiLHVCQUF1QjtBQUN6Qjs7QUFFQTtFQUNFLGlCQUFpQjtFQUNqQixhQUFhO0VBQ2Isa0JBQWtCO0VBQ2xCLHFDQUFxQztBQUN2Qzs7QUFFQTtFQUNFLGtCQUFrQjtFQUNsQixXQUFXO0VBQ1gsZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0UsYUFBYTtFQUNiLGVBQWU7RUFDZixTQUFTO0FBQ1g7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLFFBQVE7QUFDVjs7QUFFQTtFQUNFO0lBQ0UsYUFBYTtFQUNmOztFQUVBO0lBQ0Usc0JBQXNCO0lBQ3RCLFNBQVM7SUFDVCxvQkFBb0I7RUFDdEI7O0VBRUE7SUFDRSwwQkFBMEI7RUFDNUI7O0VBRUE7SUFDRSwwQkFBMEI7RUFDNUI7O0VBRUE7SUFDRSwwQkFBMEI7SUFDMUIsUUFBUTtFQUNWOztFQUVBO0lBQ0Usc0JBQXNCO0VBQ3hCOztFQUVBO0lBQ0UsV0FBVztJQUNYLHVCQUF1QjtFQUN6QjtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiLnN0dWRlbnQtZGFzaGJvYXJkIHtcclxuICBwYWRkaW5nOiAyMHB4O1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNmNWY1ZjU7XHJcbiAgbWluLWhlaWdodDogMTAwdmg7XHJcbn1cclxuXHJcbi5sb2FkaW5nLWNvbnRhaW5lciwgLmVycm9yLWNvbnRhaW5lciB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgaGVpZ2h0OiA0MDBweDtcclxuICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbn1cclxuXHJcbi5lcnJvci1jb250YWluZXIgbWF0LWljb24ge1xyXG4gIGZvbnQtc2l6ZTogNDhweDtcclxuICBoZWlnaHQ6IDQ4cHg7XHJcbiAgd2lkdGg6IDQ4cHg7XHJcbiAgbWFyZ2luLWJvdHRvbTogMTZweDtcclxufVxyXG5cclxuLmRhc2hib2FyZC1oZWFkZXIge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgbWFyZ2luLWJvdHRvbTogMzBweDtcclxuICBiYWNrZ3JvdW5kOiB3aGl0ZTtcclxuICBwYWRkaW5nOiAyMHB4O1xyXG4gIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICBib3gtc2hhZG93OiAwIDJweCA0cHggcmdiYSgwLDAsMCwwLjEpO1xyXG59XHJcblxyXG4ud2VsY29tZS1zZWN0aW9uIGgxIHtcclxuICBtYXJnaW46IDA7XHJcbiAgY29sb3I6ICMzMzM7XHJcbiAgZm9udC13ZWlnaHQ6IDUwMDtcclxufVxyXG5cclxuLndlbGNvbWUtc2VjdGlvbiBwIHtcclxuICBtYXJnaW46IDhweCAwIDAgMDtcclxuICBjb2xvcjogIzY2NjtcclxufVxyXG5cclxuLnN0dWRlbnQtaW5mbyB7XHJcbiAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICBjb2xvcjogIzE5NzZkMjtcclxufVxyXG5cclxuLnN0YXRzLWdyaWQge1xyXG4gIGRpc3BsYXk6IGdyaWQ7XHJcbiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maXQsIG1pbm1heCgyNTBweCwgMWZyKSk7XHJcbiAgZ2FwOiAyMHB4O1xyXG4gIG1hcmdpbi1ib3R0b206IDMwcHg7XHJcbn1cclxuXHJcbi5zdGF0LWNhcmQge1xyXG4gIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwLjJzIGVhc2UtaW4tb3V0O1xyXG59XHJcblxyXG4uc3RhdC1jYXJkOmhvdmVyIHtcclxuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XHJcbiAgYm94LXNoYWRvdzogMCA0cHggOHB4IHJnYmEoMCwwLDAsMC4xMik7XHJcbn1cclxuXHJcbi5zdGF0LWNvbnRlbnQge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBnYXA6IDE2cHg7XHJcbn1cclxuXHJcbi5zdGF0LWljb24ge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICB3aWR0aDogNjBweDtcclxuICBoZWlnaHQ6IDYwcHg7XHJcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xyXG4gIGNvbG9yOiB3aGl0ZTtcclxufVxyXG5cclxuLnN0YXQtY2FyZC5hdHRlbmRhbmNlIC5zdGF0LWljb24ge1xyXG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7XHJcbn1cclxuXHJcbi5zdGF0LWNhcmQucHJlc2VudCAuc3RhdC1pY29uIHtcclxuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNGNhZjUwIDAlLCAjOGJjMzRhIDEwMCUpO1xyXG59XHJcblxyXG4uc3RhdC1jYXJkLmFic2VudCAuc3RhdC1pY29uIHtcclxuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjQ0MzM2IDAlLCAjZmY5ODAwIDEwMCUpO1xyXG59XHJcblxyXG4uc3RhdC1jYXJkLnRvdGFsIC5zdGF0LWljb24ge1xyXG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMyMTk2ZjMgMCUsICMwM2E5ZjQgMTAwJSk7XHJcbn1cclxuXHJcbi5zdGF0LWljb24gbWF0LWljb24ge1xyXG4gIGZvbnQtc2l6ZTogMjhweDtcclxuICBoZWlnaHQ6IDI4cHg7XHJcbiAgd2lkdGg6IDI4cHg7XHJcbn1cclxuXHJcbi5zdGF0LWluZm8gaDMge1xyXG4gIG1hcmdpbjogMDtcclxuICBmb250LXNpemU6IDJyZW07XHJcbiAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICBjb2xvcjogIzMzMztcclxufVxyXG5cclxuLnN0YXQtaW5mbyBwIHtcclxuICBtYXJnaW46IDRweCAwIDAgMDtcclxuICBjb2xvcjogIzY2NjtcclxuICBmb250LXNpemU6IDAuOXJlbTtcclxufVxyXG5cclxuLmNvbnRlbnQtc2VjdGlvbiB7XHJcbiAgbWFyZ2luLWJvdHRvbTogMzBweDtcclxufVxyXG5cclxuLmNvbnRlbnQtcm93IHtcclxuICBkaXNwbGF5OiBncmlkO1xyXG4gIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyIDFmcjtcclxuICBnYXA6IDIwcHg7XHJcbn1cclxuXHJcbi5zY2hlZHVsZS1jYXJkLCAuYXR0ZW5kYW5jZS1jYXJkIHtcclxuICBtaW4taGVpZ2h0OiAzMDBweDtcclxufVxyXG5cclxuLmNsYXNzZXMtbGlzdCB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gIGdhcDogMTJweDtcclxufVxyXG5cclxuLmNsYXNzLWl0ZW0ge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZ2FwOiAxNnB4O1xyXG4gIHBhZGRpbmc6IDE2cHg7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogI2Y5ZjlmOTtcclxuICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgYm9yZGVyLWxlZnQ6IDRweCBzb2xpZCAjMTk3NmQyO1xyXG59XHJcblxyXG4udGltZS1zbG90IHtcclxuICBtaW4td2lkdGg6IDgwcHg7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbn1cclxuXHJcbi50aW1lIHtcclxuICBmb250LXdlaWdodDogNjAwO1xyXG4gIGNvbG9yOiAjMTk3NmQyO1xyXG4gIGZvbnQtc2l6ZTogMC45cmVtO1xyXG59XHJcblxyXG4uY2xhc3MtZGV0YWlscyBoNCB7XHJcbiAgbWFyZ2luOiAwIDAgNHB4IDA7XHJcbiAgY29sb3I6ICMzMzM7XHJcbiAgZm9udC1zaXplOiAxLjFyZW07XHJcbn1cclxuXHJcbi5jbGFzcy1kZXRhaWxzIHAge1xyXG4gIG1hcmdpbjogMnB4IDA7XHJcbiAgY29sb3I6ICM2NjY7XHJcbiAgZm9udC1zaXplOiAwLjlyZW07XHJcbn1cclxuXHJcbi5yb29tIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZTNmMmZkO1xyXG4gIGNvbG9yOiAjMTk3NmQyO1xyXG4gIHBhZGRpbmc6IDJweCA4cHg7XHJcbiAgYm9yZGVyLXJhZGl1czogMTJweDtcclxuICBmb250LXNpemU6IDAuOHJlbTtcclxuICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7XHJcbiAgbWFyZ2luLXRvcDogNHB4O1xyXG59XHJcblxyXG4ubm8tY2xhc3NlcywgLm5vLWRhdGEge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gIHBhZGRpbmc6IDQwcHg7XHJcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gIGNvbG9yOiAjNjY2O1xyXG59XHJcblxyXG4ubm8tY2xhc3NlcyBtYXQtaWNvbiwgLm5vLWRhdGEgbWF0LWljb24ge1xyXG4gIGZvbnQtc2l6ZTogNDhweDtcclxuICBoZWlnaHQ6IDQ4cHg7XHJcbiAgd2lkdGg6IDQ4cHg7XHJcbiAgbWFyZ2luLWJvdHRvbTogMTZweDtcclxuICBjb2xvcjogI2NjYztcclxufVxyXG5cclxuLnN1YmplY3QtYXR0ZW5kYW5jZSB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gIGdhcDogMTZweDtcclxufVxyXG5cclxuLnN1YmplY3QtaXRlbSB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBwYWRkaW5nOiAxMnB4O1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNmOWY5Zjk7XHJcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG59XHJcblxyXG4uc3ViamVjdC1pbmZvIGg0IHtcclxuICBtYXJnaW46IDAgMCA0cHggMDtcclxuICBjb2xvcjogIzMzMztcclxuICBmb250LXNpemU6IDFyZW07XHJcbn1cclxuXHJcbi5zdWJqZWN0LWluZm8gcCB7XHJcbiAgbWFyZ2luOiAwO1xyXG4gIGNvbG9yOiAjNjY2O1xyXG4gIGZvbnQtc2l6ZTogMC44cmVtO1xyXG59XHJcblxyXG4uYXR0ZW5kYW5jZS1iYXIge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBnYXA6IDEycHg7XHJcbiAgbWluLXdpZHRoOiAxMjBweDtcclxufVxyXG5cclxuLnByb2dyZXNzLWJhciB7XHJcbiAgd2lkdGg6IDgwcHg7XHJcbiAgaGVpZ2h0OiA4cHg7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogI2UwZTBlMDtcclxuICBib3JkZXItcmFkaXVzOiA0cHg7XHJcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcclxufVxyXG5cclxuLnByb2dyZXNzLWZpbGwge1xyXG4gIGhlaWdodDogMTAwJTtcclxuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICM0Y2FmNTAgMCUsICM4YmMzNGEgMTAwJSk7XHJcbiAgYm9yZGVyLXJhZGl1czogNHB4O1xyXG4gIHRyYW5zaXRpb246IHdpZHRoIDAuM3MgZWFzZTtcclxufVxyXG5cclxuLnBlcmNlbnRhZ2Uge1xyXG4gIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgY29sb3I6ICMzMzM7XHJcbiAgZm9udC1zaXplOiAwLjlyZW07XHJcbiAgbWluLXdpZHRoOiAzNXB4O1xyXG59XHJcblxyXG4ucmVjZW50LXNlY3Rpb24ge1xyXG4gIG1hcmdpbi1ib3R0b206IDMwcHg7XHJcbn1cclxuXHJcbi5hdHRlbmRhbmNlLWhpc3Rvcnkge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICBnYXA6IDhweDtcclxufVxyXG5cclxuLmhpc3RvcnktaXRlbSB7XHJcbiAgZGlzcGxheTogZ3JpZDtcclxuICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmciAyZnIgMWZyO1xyXG4gIGdhcDogMTZweDtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIHBhZGRpbmc6IDEycHg7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogI2Y5ZjlmOTtcclxuICBib3JkZXItcmFkaXVzOiA2cHg7XHJcbn1cclxuXHJcbi5kYXRlLWluZm8ge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxufVxyXG5cclxuLmRhdGUge1xyXG4gIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgY29sb3I6ICMzMzM7XHJcbiAgZm9udC1zaXplOiAwLjlyZW07XHJcbn1cclxuXHJcbi5kYXkge1xyXG4gIGZvbnQtc2l6ZTogMC44cmVtO1xyXG4gIGNvbG9yOiAjNjY2O1xyXG59XHJcblxyXG4uc3ViamVjdC1pbmZvIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbn1cclxuXHJcbi5zdWJqZWN0IHtcclxuICBmb250LXdlaWdodDogNTAwO1xyXG4gIGNvbG9yOiAjMzMzO1xyXG4gIGZvbnQtc2l6ZTogMC45cmVtO1xyXG59XHJcblxyXG4udGVhY2hlciB7XHJcbiAgZm9udC1zaXplOiAwLjhyZW07XHJcbiAgY29sb3I6ICM2NjY7XHJcbn1cclxuXHJcbi5zdGF0dXMtaW5mbyB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxufVxyXG5cclxuLnF1aWNrLWFjdGlvbnMge1xyXG4gIGJhY2tncm91bmQ6IHdoaXRlO1xyXG4gIHBhZGRpbmc6IDI0cHg7XHJcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gIGJveC1zaGFkb3c6IDAgMnB4IDRweCByZ2JhKDAsMCwwLDAuMSk7XHJcbn1cclxuXHJcbi5xdWljay1hY3Rpb25zIGgyIHtcclxuICBtYXJnaW46IDAgMCAyMHB4IDA7XHJcbiAgY29sb3I6ICMzMzM7XHJcbiAgZm9udC13ZWlnaHQ6IDUwMDtcclxufVxyXG5cclxuLmFjdGlvbi1idXR0b25zIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGZsZXgtd3JhcDogd3JhcDtcclxuICBnYXA6IDEycHg7XHJcbn1cclxuXHJcbi5hY3Rpb24tYnV0dG9ucyBidXR0b24ge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBnYXA6IDhweDtcclxufVxyXG5cclxuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XHJcbiAgLnN0dWRlbnQtZGFzaGJvYXJkIHtcclxuICAgIHBhZGRpbmc6IDEwcHg7XHJcbiAgfVxyXG4gIFxyXG4gIC5kYXNoYm9hcmQtaGVhZGVyIHtcclxuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgICBnYXA6IDE2cHg7XHJcbiAgICBhbGlnbi1pdGVtczogc3RyZXRjaDtcclxuICB9XHJcbiAgXHJcbiAgLnN0YXRzLWdyaWQge1xyXG4gICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnI7XHJcbiAgfVxyXG4gIFxyXG4gIC5jb250ZW50LXJvdyB7XHJcbiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmcjtcclxuICB9XHJcbiAgXHJcbiAgLmhpc3RvcnktaXRlbSB7XHJcbiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmcjtcclxuICAgIGdhcDogOHB4O1xyXG4gIH1cclxuICBcclxuICAuYWN0aW9uLWJ1dHRvbnMge1xyXG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICB9XHJcbiAgXHJcbiAgLmFjdGlvbi1idXR0b25zIGJ1dHRvbiB7XHJcbiAgICB3aWR0aDogMTAwJTtcclxuICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gIH1cclxufVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "StudentOverviewComponent_div_2_Template_button_click_5_listener", "ɵɵrestoreView", "_r4", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "refreshData", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "error", "classItem_r15", "room", "ɵɵtemplate", "StudentOverviewComponent_div_3_div_67_div_1_span_11_Template", "timeSlot", "startTime", "endTime", "subject", "subjectName", "teacher", "name", "ɵɵproperty", "StudentOverviewComponent_div_3_div_67_div_1_Template", "ctx_r5", "getTodayClasses", "subject_r19", "ɵɵtextInterpolate2", "present", "total", "ɵɵstyleProp", "percentage", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "StudentOverviewComponent_div_3_div_75_div_1_Template", "ctx_r8", "studentDashboard", "subjectAttendance", "attendance_r21", "date", "status", "ɵɵpipeBind1", "StudentOverviewComponent_div_3_div_86_div_1_Template", "ctx_r11", "attendanceHistory", "slice", "StudentOverviewComponent_div_3_Template_button_click_9_listener", "_r23", "ctx_r22", "StudentOverviewComponent_div_3_div_67_Template", "StudentOverviewComponent_div_3_ng_template_68_Template", "ɵɵtemplateRefExtractor", "StudentOverviewComponent_div_3_div_75_Template", "StudentOverviewComponent_div_3_ng_template_76_Template", "StudentOverviewComponent_div_3_div_86_Template", "StudentOverviewComponent_div_3_ng_template_87_Template", "ctx_r2", "currentUser", "student", "program", "department", "regNo", "semester", "getOverallAttendancePercentage", "getPresentClasses", "getAbsentClasses", "getTotalClasses", "currentDate", "length", "_r6", "_r9", "_r12", "StudentOverviewComponent", "constructor", "dashboardService", "userService", "loading", "Date", "ngOnInit", "getUserFromLocalStorage", "user", "loadStudentDashboard", "getStudentDashboard", "_id", "subscribe", "next", "response", "success", "dashboard", "console", "timetable", "today", "toLocaleDateString", "weekday", "filter", "entry", "dayOfWeek", "getUpcomingClass", "todayClasses", "now", "currentTime", "getHours", "getMinutes", "find", "classItem", "hours", "minutes", "split", "map", "Number", "classTime", "attendanceStats", "totalAttendance", "reduce", "sum", "stat", "count", "presentCount", "Math", "round", "_", "ɵɵdirectiveInject", "i1", "DashboardService", "i2", "UserService", "_2", "selectors", "decls", "vars", "consts", "template", "StudentOverviewComponent_Template", "rf", "ctx", "StudentOverviewComponent_div_1_Template", "StudentOverviewComponent_div_2_Template", "StudentOverviewComponent_div_3_Template"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\student-dashboard\\student-overview\\student-overview.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\student-dashboard\\student-overview\\student-overview.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { DashboardService } from '../../../services/dashboard.service';\r\nimport { UserService } from '../../../services/user.service';\r\nimport { StudentDashboard } from '../../../models/dashboard';\r\n\r\n@Component({\r\n  selector: 'app-student-overview',\r\n  templateUrl: './student-overview.component.html',\r\n  styleUrls: ['./student-overview.component.css']\r\n})\r\nexport class StudentOverviewComponent implements OnInit {\r\n  studentDashboard: StudentDashboard | null = null;\r\n  loading = true;\r\n  error: string | null = null;\r\n  currentUser: any;\r\n  currentDate = new Date();\r\n\r\n  constructor(\r\n    private dashboardService: DashboardService,\r\n    private userService: UserService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.currentUser = this.userService.getUserFromLocalStorage()?.user;\r\n    if (this.currentUser) {\r\n      this.loadStudentDashboard();\r\n    } else {\r\n      this.error = 'User not found';\r\n      this.loading = false;\r\n    }\r\n  }\r\n\r\n  loadStudentDashboard(): void {\r\n    this.loading = true;\r\n    this.error = null;\r\n\r\n    this.dashboardService.getStudentDashboard(this.currentUser._id).subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.studentDashboard = response.dashboard;\r\n        } else {\r\n          this.error = 'Failed to load student dashboard';\r\n        }\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading student dashboard:', error);\r\n        this.error = 'Error loading dashboard data';\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  refreshData(): void {\r\n    this.loadStudentDashboard();\r\n  }\r\n\r\n  getTodayClasses(): any[] {\r\n    if (!this.studentDashboard?.timetable) return [];\r\n    \r\n    const today = new Date().toLocaleDateString('en-US', { weekday: 'long' });\r\n    return this.studentDashboard.timetable.filter(entry => entry.dayOfWeek === today);\r\n  }\r\n\r\n  getUpcomingClass(): any {\r\n    const todayClasses = this.getTodayClasses();\r\n    const now = new Date();\r\n    const currentTime = now.getHours() * 60 + now.getMinutes();\r\n\r\n    return todayClasses.find(classItem => {\r\n      const [hours, minutes] = classItem.timeSlot.startTime.split(':').map(Number);\r\n      const classTime = hours * 60 + minutes;\r\n      return classTime > currentTime;\r\n    });\r\n  }\r\n\r\n  getOverallAttendancePercentage(): number {\r\n    if (!this.studentDashboard?.attendanceStats.length) return 0;\r\n    \r\n    const totalAttendance = this.studentDashboard.attendanceStats.reduce((sum, stat) => sum + stat.count, 0);\r\n    const presentCount = this.studentDashboard.attendanceStats.find(stat => stat._id === 'present')?.count || 0;\r\n    \r\n    return totalAttendance > 0 ? Math.round((presentCount / totalAttendance) * 100) : 0;\r\n  }\r\n\r\n  getTotalClasses(): number {\r\n    if (!this.studentDashboard?.attendanceStats.length) return 0;\r\n    return this.studentDashboard.attendanceStats.reduce((sum, stat) => sum + stat.count, 0);\r\n  }\r\n\r\n  getPresentClasses(): number {\r\n    if (!this.studentDashboard?.attendanceStats.length) return 0;\r\n    return this.studentDashboard.attendanceStats.find(stat => stat._id === 'present')?.count || 0;\r\n  }\r\n\r\n  getAbsentClasses(): number {\r\n    if (!this.studentDashboard?.attendanceStats.length) return 0;\r\n    return this.studentDashboard.attendanceStats.find(stat => stat._id === 'absent')?.count || 0;\r\n  }\r\n}\r\n", "<div class=\"student-dashboard\">\r\n  <!-- Loading State -->\r\n  <div *ngIf=\"loading\" class=\"loading-container\">\r\n    <mat-spinner></mat-spinner>\r\n    <p>Loading your dashboard...</p>\r\n  </div>\r\n\r\n  <!-- Error State -->\r\n  <div *ngIf=\"error && !loading\" class=\"error-container\">\r\n    <mat-icon color=\"warn\">error</mat-icon>\r\n    <p>{{ error }}</p>\r\n    <button mat-raised-button color=\"primary\" (click)=\"refreshData()\">\r\n      <mat-icon>refresh</mat-icon>\r\n      Retry\r\n    </button>\r\n  </div>\r\n\r\n  <!-- Dashboard Content -->\r\n  <div *ngIf=\"studentDashboard && !loading && !error\" class=\"dashboard-content\">\r\n    <!-- Header -->\r\n    <div class=\"dashboard-header\">\r\n      <div class=\"welcome-section\">\r\n        <h1>Welcome, {{ currentUser?.name }}!</h1>\r\n        <p>{{ studentDashboard.student.program?.name }} - {{ studentDashboard.student.department?.name }}</p>\r\n        <p class=\"student-info\">{{ studentDashboard.student.regNo }} | Semester {{ studentDashboard.student.semester }}</p>\r\n      </div>\r\n      <button mat-icon-button (click)=\"refreshData()\" matTooltip=\"Refresh Data\">\r\n        <mat-icon>refresh</mat-icon>\r\n      </button>\r\n    </div>\r\n\r\n    <!-- Statistics Cards -->\r\n    <div class=\"stats-grid\">\r\n      <mat-card class=\"stat-card attendance\">\r\n        <mat-card-content>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon\">\r\n              <mat-icon>fact_check</mat-icon>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ getOverallAttendancePercentage() }}%</h3>\r\n              <p>Overall Attendance</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n\r\n      <mat-card class=\"stat-card present\">\r\n        <mat-card-content>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon\">\r\n              <mat-icon>check_circle</mat-icon>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ getPresentClasses() }}</h3>\r\n              <p>Classes Attended</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n\r\n      <mat-card class=\"stat-card absent\">\r\n        <mat-card-content>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon\">\r\n              <mat-icon>cancel</mat-icon>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ getAbsentClasses() }}</h3>\r\n              <p>Classes Missed</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n\r\n      <mat-card class=\"stat-card total\">\r\n        <mat-card-content>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon\">\r\n              <mat-icon>school</mat-icon>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ getTotalClasses() }}</h3>\r\n              <p>Total Classes</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n    </div>\r\n\r\n    <!-- Today's Schedule and Subject Attendance -->\r\n    <div class=\"content-section\">\r\n      <div class=\"content-row\">\r\n        <!-- Today's Classes -->\r\n        <mat-card class=\"schedule-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Today's Schedule</mat-card-title>\r\n            <mat-card-subtitle>{{ currentDate | date:'fullDate' }}</mat-card-subtitle>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div *ngIf=\"getTodayClasses().length > 0; else noClassesToday\" class=\"classes-list\">\r\n              <div *ngFor=\"let classItem of getTodayClasses()\" class=\"class-item\">\r\n                <div class=\"time-slot\">\r\n                  <span class=\"time\">{{ classItem.timeSlot.startTime }}</span>\r\n                  <span class=\"time\">{{ classItem.timeSlot.endTime }}</span>\r\n                </div>\r\n                <div class=\"class-details\">\r\n                  <h4>{{ classItem.subject.subjectName }}</h4>\r\n                  <p>{{ classItem.teacher.name }}</p>\r\n                  <span class=\"room\" *ngIf=\"classItem.room\">{{ classItem.room }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <ng-template #noClassesToday>\r\n              <div class=\"no-classes\">\r\n                <mat-icon>event_available</mat-icon>\r\n                <p>No classes scheduled for today</p>\r\n              </div>\r\n            </ng-template>\r\n          </mat-card-content>\r\n        </mat-card>\r\n\r\n        <!-- Subject-wise Attendance -->\r\n        <mat-card class=\"attendance-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Subject-wise Attendance</mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div *ngIf=\"studentDashboard.subjectAttendance.length > 0; else noSubjectData\" class=\"subject-attendance\">\r\n              <div *ngFor=\"let subject of studentDashboard.subjectAttendance\" class=\"subject-item\">\r\n                <div class=\"subject-info\">\r\n                  <h4>{{ subject.subjectName }}</h4>\r\n                  <p>{{ subject.present }}/{{ subject.total }} classes</p>\r\n                </div>\r\n                <div class=\"attendance-bar\">\r\n                  <div class=\"progress-bar\">\r\n                    <div class=\"progress-fill\" [style.width.%]=\"subject.percentage\"></div>\r\n                  </div>\r\n                  <span class=\"percentage\">{{ subject.percentage | number:'1.0-0' }}%</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <ng-template #noSubjectData>\r\n              <div class=\"no-data\">\r\n                <mat-icon>subject</mat-icon>\r\n                <p>No attendance data available</p>\r\n              </div>\r\n            </ng-template>\r\n          </mat-card-content>\r\n        </mat-card>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Recent Attendance History -->\r\n    <div class=\"recent-section\">\r\n      <mat-card>\r\n        <mat-card-header>\r\n          <mat-card-title>Recent Attendance</mat-card-title>\r\n          <mat-card-subtitle>Last 10 attendance records</mat-card-subtitle>\r\n        </mat-card-header>\r\n        <mat-card-content>\r\n          <div *ngIf=\"studentDashboard.attendanceHistory.length > 0; else noRecentAttendance\" class=\"attendance-history\">\r\n            <div *ngFor=\"let attendance of studentDashboard.attendanceHistory.slice(0, 10)\" class=\"history-item\">\r\n              <div class=\"date-info\">\r\n                <span class=\"date\">{{ attendance.date | date:'shortDate' }}</span>\r\n                <span class=\"day\">{{ attendance.date | date:'EEEE' }}</span>\r\n              </div>\r\n              <div class=\"subject-info\">\r\n                <span class=\"subject\">{{ attendance.subject.subjectName }}</span>\r\n                <span class=\"teacher\">{{ attendance.teacher.name }}</span>\r\n              </div>\r\n              <div class=\"status-info\">\r\n                <mat-chip [color]=\"attendance.status === 'present' ? 'primary' : 'warn'\" selected>\r\n                  {{ attendance.status | titlecase }}\r\n                </mat-chip>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <ng-template #noRecentAttendance>\r\n            <div class=\"no-data\">\r\n              <mat-icon>history</mat-icon>\r\n              <p>No attendance history available</p>\r\n            </div>\r\n          </ng-template>\r\n        </mat-card-content>\r\n      </mat-card>\r\n    </div>\r\n\r\n    <!-- Quick Actions -->\r\n    <div class=\"quick-actions\">\r\n      <h2>Quick Actions</h2>\r\n      <div class=\"action-buttons\">\r\n        <button mat-raised-button color=\"primary\" routerLink=\"/dashboard/student/attendance\">\r\n          <mat-icon>fact_check</mat-icon>\r\n          View Full Attendance\r\n        </button>\r\n        <button mat-raised-button color=\"accent\" routerLink=\"/dashboard/student/timetable\">\r\n          <mat-icon>schedule</mat-icon>\r\n          View Timetable\r\n        </button>\r\n        <button mat-raised-button color=\"primary\" routerLink=\"/dashboard/student/teachers\">\r\n          <mat-icon>person</mat-icon>\r\n          View Teachers\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": ";;;;;;;;;;;;;ICEEA,EAAA,CAAAC,cAAA,aAA+C;IAC7CD,EAAA,CAAAE,SAAA,kBAA2B;IAC3BF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,gCAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;;IAIlCJ,EAAA,CAAAC,cAAA,aAAuD;IAC9BD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACvCJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAW;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAClBJ,EAAA,CAAAC,cAAA,gBAAkE;IAAxBD,EAAA,CAAAK,UAAA,mBAAAC,gEAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAC/DZ,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC5BJ,EAAA,CAAAG,MAAA,cACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAJNJ,EAAA,CAAAa,SAAA,GAAW;IAAXb,EAAA,CAAAc,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IAmGAhB,EAAA,CAAAC,cAAA,eAA0C;IAAAD,EAAA,CAAAG,MAAA,GAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAA3BJ,EAAA,CAAAa,SAAA,GAAoB;IAApBb,EAAA,CAAAc,iBAAA,CAAAG,aAAA,CAAAC,IAAA,CAAoB;;;;;IARlElB,EAAA,CAAAC,cAAA,cAAoE;IAE7CD,EAAA,CAAAG,MAAA,GAAkC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC5DJ,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAAG,MAAA,GAAgC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAE5DJ,EAAA,CAAAC,cAAA,cAA2B;IACrBD,EAAA,CAAAG,MAAA,GAAmC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC5CJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,IAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACnCJ,EAAA,CAAAmB,UAAA,KAAAC,4DAAA,mBAAqE;IACvEpB,EAAA,CAAAI,YAAA,EAAM;;;;IAPeJ,EAAA,CAAAa,SAAA,GAAkC;IAAlCb,EAAA,CAAAc,iBAAA,CAAAG,aAAA,CAAAI,QAAA,CAAAC,SAAA,CAAkC;IAClCtB,EAAA,CAAAa,SAAA,GAAgC;IAAhCb,EAAA,CAAAc,iBAAA,CAAAG,aAAA,CAAAI,QAAA,CAAAE,OAAA,CAAgC;IAG/CvB,EAAA,CAAAa,SAAA,GAAmC;IAAnCb,EAAA,CAAAc,iBAAA,CAAAG,aAAA,CAAAO,OAAA,CAAAC,WAAA,CAAmC;IACpCzB,EAAA,CAAAa,SAAA,GAA4B;IAA5Bb,EAAA,CAAAc,iBAAA,CAAAG,aAAA,CAAAS,OAAA,CAAAC,IAAA,CAA4B;IACX3B,EAAA,CAAAa,SAAA,GAAoB;IAApBb,EAAA,CAAA4B,UAAA,SAAAX,aAAA,CAAAC,IAAA,CAAoB;;;;;IAT9ClB,EAAA,CAAAC,cAAA,cAAoF;IAClFD,EAAA,CAAAmB,UAAA,IAAAU,oDAAA,mBAUM;IACR7B,EAAA,CAAAI,YAAA,EAAM;;;;IAXuBJ,EAAA,CAAAa,SAAA,GAAoB;IAApBb,EAAA,CAAA4B,UAAA,YAAAE,MAAA,CAAAC,eAAA,GAAoB;;;;;IAa/C/B,EAAA,CAAAC,cAAA,cAAwB;IACZD,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACpCJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,qCAA8B;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAavCJ,EAAA,CAAAC,cAAA,cAAqF;IAE7ED,EAAA,CAAAG,MAAA,GAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAClCJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAiD;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAE1DJ,EAAA,CAAAC,cAAA,cAA4B;IAExBD,EAAA,CAAAE,SAAA,cAAsE;IACxEF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAG,MAAA,IAA0C;;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAPtEJ,EAAA,CAAAa,SAAA,GAAyB;IAAzBb,EAAA,CAAAc,iBAAA,CAAAkB,WAAA,CAAAP,WAAA,CAAyB;IAC1BzB,EAAA,CAAAa,SAAA,GAAiD;IAAjDb,EAAA,CAAAiC,kBAAA,KAAAD,WAAA,CAAAE,OAAA,OAAAF,WAAA,CAAAG,KAAA,aAAiD;IAIvBnC,EAAA,CAAAa,SAAA,GAAoC;IAApCb,EAAA,CAAAoC,WAAA,UAAAJ,WAAA,CAAAK,UAAA,MAAoC;IAExCrC,EAAA,CAAAa,SAAA,GAA0C;IAA1Cb,EAAA,CAAAsC,kBAAA,KAAAtC,EAAA,CAAAuC,WAAA,QAAAP,WAAA,CAAAK,UAAA,gBAA0C;;;;;IAVzErC,EAAA,CAAAC,cAAA,cAA0G;IACxGD,EAAA,CAAAmB,UAAA,IAAAqB,oDAAA,mBAWM;IACRxC,EAAA,CAAAI,YAAA,EAAM;;;;IAZqBJ,EAAA,CAAAa,SAAA,GAAqC;IAArCb,EAAA,CAAA4B,UAAA,YAAAa,MAAA,CAAAC,gBAAA,CAAAC,iBAAA,CAAqC;;;;;IAc9D3C,EAAA,CAAAC,cAAA,cAAqB;IACTD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC5BJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,mCAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAiBvCJ,EAAA,CAAAC,cAAA,cAAqG;IAE9ED,EAAA,CAAAG,MAAA,GAAwC;;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAClEJ,EAAA,CAAAC,cAAA,eAAkB;IAAAD,EAAA,CAAAG,MAAA,GAAmC;;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAE9DJ,EAAA,CAAAC,cAAA,cAA0B;IACFD,EAAA,CAAAG,MAAA,IAAoC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACjEJ,EAAA,CAAAC,cAAA,gBAAsB;IAAAD,EAAA,CAAAG,MAAA,IAA6B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAE5DJ,EAAA,CAAAC,cAAA,eAAyB;IAErBD,EAAA,CAAAG,MAAA,IACF;;IAAAH,EAAA,CAAAI,YAAA,EAAW;;;;IAVQJ,EAAA,CAAAa,SAAA,GAAwC;IAAxCb,EAAA,CAAAc,iBAAA,CAAAd,EAAA,CAAAuC,WAAA,OAAAK,cAAA,CAAAC,IAAA,eAAwC;IACzC7C,EAAA,CAAAa,SAAA,GAAmC;IAAnCb,EAAA,CAAAc,iBAAA,CAAAd,EAAA,CAAAuC,WAAA,OAAAK,cAAA,CAAAC,IAAA,UAAmC;IAG/B7C,EAAA,CAAAa,SAAA,GAAoC;IAApCb,EAAA,CAAAc,iBAAA,CAAA8B,cAAA,CAAApB,OAAA,CAAAC,WAAA,CAAoC;IACpCzB,EAAA,CAAAa,SAAA,GAA6B;IAA7Bb,EAAA,CAAAc,iBAAA,CAAA8B,cAAA,CAAAlB,OAAA,CAAAC,IAAA,CAA6B;IAGzC3B,EAAA,CAAAa,SAAA,GAA8D;IAA9Db,EAAA,CAAA4B,UAAA,UAAAgB,cAAA,CAAAE,MAAA,oCAA8D;IACtE9C,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAsC,kBAAA,MAAAtC,EAAA,CAAA+C,WAAA,SAAAH,cAAA,CAAAE,MAAA,OACF;;;;;IAbN9C,EAAA,CAAAC,cAAA,cAA+G;IAC7GD,EAAA,CAAAmB,UAAA,IAAA6B,oDAAA,oBAcM;IACRhD,EAAA,CAAAI,YAAA,EAAM;;;;IAfwBJ,EAAA,CAAAa,SAAA,GAAkD;IAAlDb,EAAA,CAAA4B,UAAA,YAAAqB,OAAA,CAAAP,gBAAA,CAAAQ,iBAAA,CAAAC,KAAA,QAAkD;;;;;IAiB9EnD,EAAA,CAAAC,cAAA,cAAqB;IACTD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC5BJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,sCAA+B;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;;IAnKlDJ,EAAA,CAAAC,cAAA,aAA8E;IAIpED,EAAA,CAAAG,MAAA,GAAiC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC1CJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAA8F;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACrGJ,EAAA,CAAAC,cAAA,YAAwB;IAAAD,EAAA,CAAAG,MAAA,GAAuF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAErHJ,EAAA,CAAAC,cAAA,iBAA0E;IAAlDD,EAAA,CAAAK,UAAA,mBAAA+C,gEAAA;MAAApD,EAAA,CAAAO,aAAA,CAAA8C,IAAA;MAAA,MAAAC,OAAA,GAAAtD,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAA2C,OAAA,CAAA1C,WAAA,EAAa;IAAA,EAAC;IAC7CZ,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAKhCJ,EAAA,CAAAC,cAAA,eAAwB;IAKJD,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAEjCJ,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAG,MAAA,IAAuC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAChDJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,0BAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAMjCJ,EAAA,CAAAC,cAAA,oBAAoC;IAIlBD,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAEnCJ,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAG,MAAA,IAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAClCJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,wBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAM/BJ,EAAA,CAAAC,cAAA,oBAAmC;IAIjBD,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAE7BJ,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAG,MAAA,IAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACjCJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,sBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAM7BJ,EAAA,CAAAC,cAAA,oBAAkC;IAIhBD,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAE7BJ,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAG,MAAA,IAAuB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAChCJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAQ9BJ,EAAA,CAAAC,cAAA,eAA6B;IAKLD,EAAA,CAAAG,MAAA,wBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IACjDJ,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAG,MAAA,IAAmC;;IAAAH,EAAA,CAAAI,YAAA,EAAoB;IAE5EJ,EAAA,CAAAC,cAAA,wBAAkB;IAChBD,EAAA,CAAAmB,UAAA,KAAAoC,8CAAA,kBAYM;IACNvD,EAAA,CAAAmB,UAAA,KAAAqC,sDAAA,iCAAAxD,EAAA,CAAAyD,sBAAA,CAKc;IAChBzD,EAAA,CAAAI,YAAA,EAAmB;IAIrBJ,EAAA,CAAAC,cAAA,oBAAkC;IAEdD,EAAA,CAAAG,MAAA,+BAAuB;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IAE1DJ,EAAA,CAAAC,cAAA,wBAAkB;IAChBD,EAAA,CAAAmB,UAAA,KAAAuC,8CAAA,kBAaM;IACN1D,EAAA,CAAAmB,UAAA,KAAAwC,sDAAA,iCAAA3D,EAAA,CAAAyD,sBAAA,CAKc;IAChBzD,EAAA,CAAAI,YAAA,EAAmB;IAMzBJ,EAAA,CAAAC,cAAA,eAA4B;IAGND,EAAA,CAAAG,MAAA,yBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IAClDJ,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAG,MAAA,kCAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAoB;IAEnEJ,EAAA,CAAAC,cAAA,wBAAkB;IAChBD,EAAA,CAAAmB,UAAA,KAAAyC,8CAAA,kBAgBM;IACN5D,EAAA,CAAAmB,UAAA,KAAA0C,sDAAA,iCAAA7D,EAAA,CAAAyD,sBAAA,CAKc;IAChBzD,EAAA,CAAAI,YAAA,EAAmB;IAKvBJ,EAAA,CAAAC,cAAA,eAA2B;IACrBD,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACtBJ,EAAA,CAAAC,cAAA,eAA4B;IAEdD,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC/BJ,EAAA,CAAAG,MAAA,8BACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAAmF;IACvED,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC7BJ,EAAA,CAAAG,MAAA,yBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,mBAAmF;IACvED,EAAA,CAAAG,MAAA,eAAM;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC3BJ,EAAA,CAAAG,MAAA,wBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;;;IArLLJ,EAAA,CAAAa,SAAA,GAAiC;IAAjCb,EAAA,CAAAsC,kBAAA,cAAAwB,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAApC,IAAA,MAAiC;IAClC3B,EAAA,CAAAa,SAAA,GAA8F;IAA9Fb,EAAA,CAAAiC,kBAAA,KAAA6B,MAAA,CAAApB,gBAAA,CAAAsB,OAAA,CAAAC,OAAA,kBAAAH,MAAA,CAAApB,gBAAA,CAAAsB,OAAA,CAAAC,OAAA,CAAAtC,IAAA,SAAAmC,MAAA,CAAApB,gBAAA,CAAAsB,OAAA,CAAAE,UAAA,kBAAAJ,MAAA,CAAApB,gBAAA,CAAAsB,OAAA,CAAAE,UAAA,CAAAvC,IAAA,KAA8F;IACzE3B,EAAA,CAAAa,SAAA,GAAuF;IAAvFb,EAAA,CAAAiC,kBAAA,KAAA6B,MAAA,CAAApB,gBAAA,CAAAsB,OAAA,CAAAG,KAAA,kBAAAL,MAAA,CAAApB,gBAAA,CAAAsB,OAAA,CAAAI,QAAA,KAAuF;IAgBrGpE,EAAA,CAAAa,SAAA,IAAuC;IAAvCb,EAAA,CAAAsC,kBAAA,KAAAwB,MAAA,CAAAO,8BAAA,QAAuC;IAcvCrE,EAAA,CAAAa,SAAA,IAAyB;IAAzBb,EAAA,CAAAc,iBAAA,CAAAgD,MAAA,CAAAQ,iBAAA,GAAyB;IAczBtE,EAAA,CAAAa,SAAA,IAAwB;IAAxBb,EAAA,CAAAc,iBAAA,CAAAgD,MAAA,CAAAS,gBAAA,GAAwB;IAcxBvE,EAAA,CAAAa,SAAA,IAAuB;IAAvBb,EAAA,CAAAc,iBAAA,CAAAgD,MAAA,CAAAU,eAAA,GAAuB;IAeVxE,EAAA,CAAAa,SAAA,IAAmC;IAAnCb,EAAA,CAAAc,iBAAA,CAAAd,EAAA,CAAAuC,WAAA,SAAAuB,MAAA,CAAAW,WAAA,cAAmC;IAGhDzE,EAAA,CAAAa,SAAA,GAAoC;IAApCb,EAAA,CAAA4B,UAAA,SAAAkC,MAAA,CAAA/B,eAAA,GAAA2C,MAAA,KAAoC,aAAAC,GAAA;IA4BpC3E,EAAA,CAAAa,SAAA,GAAqD;IAArDb,EAAA,CAAA4B,UAAA,SAAAkC,MAAA,CAAApB,gBAAA,CAAAC,iBAAA,CAAA+B,MAAA,KAAqD,aAAAE,GAAA;IAiCvD5E,EAAA,CAAAa,SAAA,IAAqD;IAArDb,EAAA,CAAA4B,UAAA,SAAAkC,MAAA,CAAApB,gBAAA,CAAAQ,iBAAA,CAAAwB,MAAA,KAAqD,aAAAG,IAAA;;;ADvJrE,OAAM,MAAOC,wBAAwB;EAOnCC,YACUC,gBAAkC,EAClCC,WAAwB;IADxB,KAAAD,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,WAAW,GAAXA,WAAW;IARrB,KAAAvC,gBAAgB,GAA4B,IAAI;IAChD,KAAAwC,OAAO,GAAG,IAAI;IACd,KAAAlE,KAAK,GAAkB,IAAI;IAE3B,KAAAyD,WAAW,GAAG,IAAIU,IAAI,EAAE;EAKrB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACrB,WAAW,GAAG,IAAI,CAACkB,WAAW,CAACI,uBAAuB,EAAE,EAAEC,IAAI;IACnE,IAAI,IAAI,CAACvB,WAAW,EAAE;MACpB,IAAI,CAACwB,oBAAoB,EAAE;KAC5B,MAAM;MACL,IAAI,CAACvE,KAAK,GAAG,gBAAgB;MAC7B,IAAI,CAACkE,OAAO,GAAG,KAAK;;EAExB;EAEAK,oBAAoBA,CAAA;IAClB,IAAI,CAACL,OAAO,GAAG,IAAI;IACnB,IAAI,CAAClE,KAAK,GAAG,IAAI;IAEjB,IAAI,CAACgE,gBAAgB,CAACQ,mBAAmB,CAAC,IAAI,CAACzB,WAAW,CAAC0B,GAAG,CAAC,CAACC,SAAS,CAAC;MACxEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACnD,gBAAgB,GAAGkD,QAAQ,CAACE,SAAS;SAC3C,MAAM;UACL,IAAI,CAAC9E,KAAK,GAAG,kCAAkC;;QAEjD,IAAI,CAACkE,OAAO,GAAG,KAAK;MACtB,CAAC;MACDlE,KAAK,EAAGA,KAAK,IAAI;QACf+E,OAAO,CAAC/E,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,IAAI,CAACA,KAAK,GAAG,8BAA8B;QAC3C,IAAI,CAACkE,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAtE,WAAWA,CAAA;IACT,IAAI,CAAC2E,oBAAoB,EAAE;EAC7B;EAEAxD,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACW,gBAAgB,EAAEsD,SAAS,EAAE,OAAO,EAAE;IAEhD,MAAMC,KAAK,GAAG,IAAId,IAAI,EAAE,CAACe,kBAAkB,CAAC,OAAO,EAAE;MAAEC,OAAO,EAAE;IAAM,CAAE,CAAC;IACzE,OAAO,IAAI,CAACzD,gBAAgB,CAACsD,SAAS,CAACI,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,SAAS,KAAKL,KAAK,CAAC;EACnF;EAEAM,gBAAgBA,CAAA;IACd,MAAMC,YAAY,GAAG,IAAI,CAACzE,eAAe,EAAE;IAC3C,MAAM0E,GAAG,GAAG,IAAItB,IAAI,EAAE;IACtB,MAAMuB,WAAW,GAAGD,GAAG,CAACE,QAAQ,EAAE,GAAG,EAAE,GAAGF,GAAG,CAACG,UAAU,EAAE;IAE1D,OAAOJ,YAAY,CAACK,IAAI,CAACC,SAAS,IAAG;MACnC,MAAM,CAACC,KAAK,EAAEC,OAAO,CAAC,GAAGF,SAAS,CAACzF,QAAQ,CAACC,SAAS,CAAC2F,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC;MAC5E,MAAMC,SAAS,GAAGL,KAAK,GAAG,EAAE,GAAGC,OAAO;MACtC,OAAOI,SAAS,GAAGV,WAAW;IAChC,CAAC,CAAC;EACJ;EAEArC,8BAA8BA,CAAA;IAC5B,IAAI,CAAC,IAAI,CAAC3B,gBAAgB,EAAE2E,eAAe,CAAC3C,MAAM,EAAE,OAAO,CAAC;IAE5D,MAAM4C,eAAe,GAAG,IAAI,CAAC5E,gBAAgB,CAAC2E,eAAe,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAACC,KAAK,EAAE,CAAC,CAAC;IACxG,MAAMC,YAAY,GAAG,IAAI,CAACjF,gBAAgB,CAAC2E,eAAe,CAACR,IAAI,CAACY,IAAI,IAAIA,IAAI,CAAChC,GAAG,KAAK,SAAS,CAAC,EAAEiC,KAAK,IAAI,CAAC;IAE3G,OAAOJ,eAAe,GAAG,CAAC,GAAGM,IAAI,CAACC,KAAK,CAAEF,YAAY,GAAGL,eAAe,GAAI,GAAG,CAAC,GAAG,CAAC;EACrF;EAEA9C,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAAC9B,gBAAgB,EAAE2E,eAAe,CAAC3C,MAAM,EAAE,OAAO,CAAC;IAC5D,OAAO,IAAI,CAAChC,gBAAgB,CAAC2E,eAAe,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAACC,KAAK,EAAE,CAAC,CAAC;EACzF;EAEApD,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAAC5B,gBAAgB,EAAE2E,eAAe,CAAC3C,MAAM,EAAE,OAAO,CAAC;IAC5D,OAAO,IAAI,CAAChC,gBAAgB,CAAC2E,eAAe,CAACR,IAAI,CAACY,IAAI,IAAIA,IAAI,CAAChC,GAAG,KAAK,SAAS,CAAC,EAAEiC,KAAK,IAAI,CAAC;EAC/F;EAEAnD,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAAC7B,gBAAgB,EAAE2E,eAAe,CAAC3C,MAAM,EAAE,OAAO,CAAC;IAC5D,OAAO,IAAI,CAAChC,gBAAgB,CAAC2E,eAAe,CAACR,IAAI,CAACY,IAAI,IAAIA,IAAI,CAAChC,GAAG,KAAK,QAAQ,CAAC,EAAEiC,KAAK,IAAI,CAAC;EAC9F;EAAC,QAAAI,CAAA,G;qBAxFUhD,wBAAwB,EAAA9E,EAAA,CAAA+H,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAjI,EAAA,CAAA+H,iBAAA,CAAAG,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAxBtD,wBAAwB;IAAAuD,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCVrC3I,EAAA,CAAAC,cAAA,aAA+B;QAE7BD,EAAA,CAAAmB,UAAA,IAAA0H,uCAAA,iBAGM;QAGN7I,EAAA,CAAAmB,UAAA,IAAA2H,uCAAA,iBAOM;QAGN9I,EAAA,CAAAmB,UAAA,IAAA4H,uCAAA,oBA4LM;QACR/I,EAAA,CAAAI,YAAA,EAAM;;;QA7MEJ,EAAA,CAAAa,SAAA,GAAa;QAAbb,EAAA,CAAA4B,UAAA,SAAAgH,GAAA,CAAA1D,OAAA,CAAa;QAMblF,EAAA,CAAAa,SAAA,GAAuB;QAAvBb,EAAA,CAAA4B,UAAA,SAAAgH,GAAA,CAAA5H,KAAA,KAAA4H,GAAA,CAAA1D,OAAA,CAAuB;QAUvBlF,EAAA,CAAAa,SAAA,GAA4C;QAA5Cb,EAAA,CAAA4B,UAAA,SAAAgH,GAAA,CAAAlG,gBAAA,KAAAkG,GAAA,CAAA1D,OAAA,KAAA0D,GAAA,CAAA5H,KAAA,CAA4C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}