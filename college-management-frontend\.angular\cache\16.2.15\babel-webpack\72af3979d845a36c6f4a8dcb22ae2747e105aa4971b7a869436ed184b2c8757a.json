{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../../../services/attendance.service\";\nimport * as i4 from \"../../../services/user.service\";\nimport * as i5 from \"../../../services/dashboard.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/checkbox\";\nimport * as i8 from \"@angular/material/icon\";\nfunction TeacherAttendnaceComponent_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const class_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", class_r7._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", class_r7.className, \" - \", class_r7.section, \" \");\n  }\n}\nfunction TeacherAttendnaceComponent_option_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const subject_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", subject_r8._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", subject_r8.subjectName, \" \");\n  }\n}\nfunction TeacherAttendnaceComponent_div_34_tr_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"mat-checkbox\", 42);\n    i0.ɵɵlistener(\"change\", function TeacherAttendnaceComponent_div_34_tr_29_Template_mat_checkbox_change_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r13);\n      const student_r11 = restoredCtx.$implicit;\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r12.updateAttendanceStatus(student_r11._id, $event.checked ? \"present\" : \"absent\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\")(10, \"div\", 43)(11, \"input\", 44);\n    i0.ɵɵlistener(\"change\", function TeacherAttendnaceComponent_div_34_tr_29_Template_input_change_11_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r13);\n      const student_r11 = restoredCtx.$implicit;\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.updateAttendanceStatus(student_r11._id, \"present\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"label\", 45);\n    i0.ɵɵtext(13, \" Present \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"input\", 44);\n    i0.ɵɵlistener(\"change\", function TeacherAttendnaceComponent_div_34_tr_29_Template_input_change_14_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r13);\n      const student_r11 = restoredCtx.$implicit;\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.updateAttendanceStatus(student_r11._id, \"absent\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"label\", 46);\n    i0.ɵɵtext(16, \" Absent \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"input\", 44);\n    i0.ɵɵlistener(\"change\", function TeacherAttendnaceComponent_div_34_tr_29_Template_input_change_17_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r13);\n      const student_r11 = restoredCtx.$implicit;\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.updateAttendanceStatus(student_r11._id, \"late\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"label\", 47);\n    i0.ɵɵtext(19, \" Late \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const student_r11 = ctx.$implicit;\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r9.attendanceRecords[student_r11._id] === \"present\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(student_r11.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(student_r11.regNo);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(student_r11.rollNo);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"id\", \"present-\" + student_r11._id)(\"name\", \"attendance-\" + student_r11._id)(\"value\", \"present\")(\"checked\", ctx_r9.attendanceRecords[student_r11._id] === \"present\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"for\", \"present-\" + student_r11._id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", \"absent-\" + student_r11._id)(\"name\", \"attendance-\" + student_r11._id)(\"value\", \"absent\")(\"checked\", ctx_r9.attendanceRecords[student_r11._id] === \"absent\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"for\", \"absent-\" + student_r11._id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", \"late-\" + student_r11._id)(\"name\", \"attendance-\" + student_r11._id)(\"value\", \"late\")(\"checked\", ctx_r9.attendanceRecords[student_r11._id] === \"late\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"for\", \"late-\" + student_r11._id);\n  }\n}\nfunction TeacherAttendnaceComponent_div_34_span_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 48);\n  }\n}\nfunction TeacherAttendnaceComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29)(2, \"h5\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 30)(5, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function TeacherAttendnaceComponent_div_34_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.viewAttendanceHistory());\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"history\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" History \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function TeacherAttendnaceComponent_div_34_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.exportAttendance());\n    });\n    i0.ɵɵelementStart(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"download\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" Export \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 33)(14, \"table\", 34)(15, \"thead\", 35)(16, \"tr\")(17, \"th\")(18, \"mat-checkbox\", 36);\n    i0.ɵɵlistener(\"ngModelChange\", function TeacherAttendnaceComponent_div_34_Template_mat_checkbox_ngModelChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.isChecked = $event);\n    })(\"change\", function TeacherAttendnaceComponent_div_34_Template_mat_checkbox_change_18_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.toggleCheckbox());\n    });\n    i0.ɵɵtext(19, \" Select All \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"th\");\n    i0.ɵɵtext(21, \"Student Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"th\");\n    i0.ɵɵtext(23, \"Reg No\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"th\");\n    i0.ɵɵtext(25, \"Roll No\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"th\");\n    i0.ɵɵtext(27, \"Attendance\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"tbody\");\n    i0.ɵɵtemplate(29, TeacherAttendnaceComponent_div_34_tr_29_Template, 20, 19, \"tr\", 37);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"div\", 38)(31, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function TeacherAttendnaceComponent_div_34_Template_button_click_31_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.resetForm());\n    });\n    i0.ɵɵtext(32, \" Reset \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"button\", 40);\n    i0.ɵɵtemplate(34, TeacherAttendnaceComponent_div_34_span_34_Template, 1, 0, \"span\", 41);\n    i0.ɵɵelementStart(35, \"mat-icon\");\n    i0.ɵɵtext(36, \"save\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(37, \" Submit Attendance \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Students (\", ctx_r2.students.length, \")\");\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.isChecked)(\"indeterminate\", ctx_r2.isIndeterminate);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.students);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.loading || ctx_r2.attendanceForm.invalid || ctx_r2.students.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.loading);\n  }\n}\nfunction TeacherAttendnaceComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"div\", 50)(2, \"span\", 51);\n    i0.ɵɵtext(3, \"Loading students...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction TeacherAttendnaceComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"mat-icon\", 52);\n    i0.ɵɵtext(2, \"school\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 53);\n    i0.ɵɵtext(4, \"No students found for this class\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TeacherAttendnaceComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 54)(2, \"div\", 55)(3, \"div\", 56)(4, \"div\", 6)(5, \"h4\", 57);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"small\", 58);\n    i0.ɵɵtext(8, \"Present\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(9, \"div\", 55)(10, \"div\", 56)(11, \"div\", 6)(12, \"h4\", 11);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"small\", 58);\n    i0.ɵɵtext(15, \"Absent\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(16, \"div\", 55)(17, \"div\", 56)(18, \"div\", 6)(19, \"h4\", 59);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"small\", 58);\n    i0.ɵɵtext(22, \"Late\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(23, \"div\", 55)(24, \"div\", 56)(25, \"div\", 6)(26, \"h4\", 60);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"small\", 58);\n    i0.ɵɵtext(29, \"Total\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(30, \"div\", 61)(31, \"div\", 62);\n    i0.ɵɵelement(32, \"div\", 63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"small\", 58);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r5.getAttendanceStats().present);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r5.getAttendanceStats().absent);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r5.getAttendanceStats().late);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r5.getAttendanceStats().total);\n    i0.ɵɵadvance(5);\n    i0.ɵɵstyleProp(\"width\", ctx_r5.getAttendanceStats().present / ctx_r5.getAttendanceStats().total * 100, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Attendance Rate: \", (ctx_r5.getAttendanceStats().present / ctx_r5.getAttendanceStats().total * 100).toFixed(1), \"% \");\n  }\n}\nfunction TeacherAttendnaceComponent_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"mat-icon\", 52);\n    i0.ɵɵtext(2, \"analytics\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 53);\n    i0.ɵɵtext(4, \"Select a class to view summary\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class TeacherAttendnaceComponent {\n  constructor(router, fb, attendanceService, userService, dashboardService) {\n    this.router = router;\n    this.fb = fb;\n    this.attendanceService = attendanceService;\n    this.userService = userService;\n    this.dashboardService = dashboardService;\n    this.isChecked = false;\n    this.isIndeterminate = true;\n    this.studentstable = false;\n    this.teacherClasses = [];\n    this.selectedClass = null;\n    this.students = [];\n    this.attendanceRecords = {};\n    this.loading = false;\n    this.loadingStudents = false;\n    this.selectedDate = new Date().toISOString().split('T')[0];\n  }\n  ngOnInit() {\n    this.currentUser = this.userService.getUserFromLocalStorage()?.user;\n    this.initializeForm();\n    this.loadTeacherClasses();\n  }\n  initializeForm() {\n    this.attendanceForm = this.fb.group({\n      classId: ['', Validators.required],\n      subjectId: ['', Validators.required],\n      date: [this.selectedDate, Validators.required]\n    });\n  }\n  loadTeacherClasses() {\n    if (!this.currentUser) return;\n    this.loading = true;\n    this.dashboardService.getTeacherDashboard(this.currentUser._id).subscribe({\n      next: response => {\n        if (response.success && response.dashboard) {\n          this.teacherClasses = response.dashboard.classes || [];\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading teacher classes:', error);\n        Swal.fire({\n          icon: 'error',\n          title: 'Error',\n          text: 'Failed to load classes. Please try again.'\n        });\n        this.loading = false;\n      }\n    });\n  }\n  onClassChange() {\n    const classId = this.attendanceForm.get('classId')?.value;\n    this.selectedClass = this.teacherClasses.find(c => c._id === classId);\n    if (this.selectedClass) {\n      this.loadStudentsForClass(classId);\n    }\n  }\n  loadStudentsForClass(classId) {\n    this.loadingStudents = true;\n    this.students = [];\n    this.attendanceRecords = {};\n    // Get students from the selected class\n    // This would typically come from a students service\n    // For now, we'll simulate with the class data\n    if (this.selectedClass && this.selectedClass.students) {\n      this.students = this.selectedClass.students;\n      this.initializeAttendanceRecords();\n    }\n    this.loadingStudents = false;\n  }\n  initializeAttendanceRecords() {\n    this.students.forEach(student => {\n      this.attendanceRecords[student._id] = 'present'; // Default to present\n    });\n  }\n\n  toggleCheckbox() {\n    if (this.isIndeterminate) {\n      this.isIndeterminate = false;\n      this.isChecked = true;\n      // Mark all as present\n      this.students.forEach(student => {\n        this.attendanceRecords[student._id] = 'present';\n      });\n    } else if (this.isChecked) {\n      this.isChecked = false;\n      // Mark all as absent\n      this.students.forEach(student => {\n        this.attendanceRecords[student._id] = 'absent';\n      });\n    } else {\n      this.isIndeterminate = true;\n      // Reset to default (present)\n      this.students.forEach(student => {\n        this.attendanceRecords[student._id] = 'present';\n      });\n    }\n  }\n  updateAttendanceStatus(studentId, status) {\n    this.attendanceRecords[studentId] = status;\n    this.updateCheckboxState();\n  }\n  updateCheckboxState() {\n    const presentCount = Object.values(this.attendanceRecords).filter(status => status === 'present').length;\n    const totalStudents = this.students.length;\n    if (presentCount === 0) {\n      this.isChecked = false;\n      this.isIndeterminate = false;\n    } else if (presentCount === totalStudents) {\n      this.isChecked = true;\n      this.isIndeterminate = false;\n    } else {\n      this.isChecked = false;\n      this.isIndeterminate = true;\n    }\n  }\n  submitAttendance() {\n    if (this.attendanceForm.invalid || !this.currentUser) {\n      Swal.fire({\n        icon: 'warning',\n        title: 'Invalid Form',\n        text: 'Please fill all required fields.'\n      });\n      return;\n    }\n    const formData = this.attendanceForm.value;\n    const attendanceRecords = this.students.map(student => ({\n      studentId: student._id,\n      teacherId: this.currentUser._id,\n      subjectId: formData.subjectId,\n      status: this.attendanceRecords[student._id] || 'absent'\n    }));\n    const attendanceData = {\n      classId: formData.classId,\n      date: formData.date,\n      attendanceRecords,\n      teacherId: this.currentUser._id,\n      subjectId: formData.subjectId\n    };\n    this.loading = true;\n    this.attendanceService.markBatchAttendance(attendanceData).subscribe({\n      next: response => {\n        if (response.success) {\n          Swal.fire({\n            icon: 'success',\n            title: 'Attendance Marked',\n            text: 'Attendance has been successfully recorded!',\n            timer: 3000,\n            showConfirmButton: false\n          });\n          this.resetForm();\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error marking attendance:', error);\n        Swal.fire({\n          icon: 'error',\n          title: 'Error',\n          text: error.error?.message || 'Failed to mark attendance. Please try again.'\n        });\n        this.loading = false;\n      }\n    });\n  }\n  resetForm() {\n    this.attendanceForm.reset();\n    this.initializeForm();\n    this.selectedClass = null;\n    this.students = [];\n    this.attendanceRecords = {};\n    this.isChecked = false;\n    this.isIndeterminate = true;\n  }\n  getAttendanceStats() {\n    const total = this.students.length;\n    const present = Object.values(this.attendanceRecords).filter(status => status === 'present').length;\n    const absent = Object.values(this.attendanceRecords).filter(status => status === 'absent').length;\n    const late = Object.values(this.attendanceRecords).filter(status => status === 'late').length;\n    return {\n      total,\n      present,\n      absent,\n      late\n    };\n  }\n  // Navigate to students view\n  gotostudents(selectedClass, selectedDepartment, selectedSection) {\n    this.router.navigate(['/dashboard/teacher/class-students'], {\n      queryParams: {\n        class: selectedClass,\n        department: selectedDepartment,\n        section: selectedSection\n      }\n    });\n  }\n  seestudents() {\n    this.studentstable = !this.studentstable;\n  }\n  viewAttendanceHistory() {\n    if (!this.selectedClass) {\n      Swal.fire({\n        icon: 'warning',\n        title: 'No Class Selected',\n        text: 'Please select a class first.'\n      });\n      return;\n    }\n    this.router.navigate(['/dashboard/teacher/attendance-history'], {\n      queryParams: {\n        classId: this.selectedClass._id,\n        className: this.selectedClass.className\n      }\n    });\n  }\n  exportAttendance() {\n    if (!this.selectedClass) {\n      Swal.fire({\n        icon: 'warning',\n        title: 'No Class Selected',\n        text: 'Please select a class first.'\n      });\n      return;\n    }\n    Swal.fire({\n      title: 'Export Attendance',\n      text: 'This feature will be available soon.',\n      icon: 'info'\n    });\n  }\n  static #_ = this.ɵfac = function TeacherAttendnaceComponent_Factory(t) {\n    return new (t || TeacherAttendnaceComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.AttendanceService), i0.ɵɵdirectiveInject(i4.UserService), i0.ɵɵdirectiveInject(i5.DashboardService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: TeacherAttendnaceComponent,\n    selectors: [[\"app-teacher-attendnace\"]],\n    decls: 63,\n    vars: 12,\n    consts: [[1, \"container-fluid\"], [1, \"row\"], [1, \"col-md-8\"], [1, \"card\"], [1, \"card-header\"], [1, \"fw-bold\", \"mb-0\"], [1, \"card-body\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"row\", \"mb-3\"], [1, \"col-md-4\"], [\"for\", \"classId\", 1, \"form-label\"], [1, \"text-danger\"], [\"id\", \"classId\", \"formControlName\", \"classId\", 1, \"form-select\", 3, \"change\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"subjectId\", 1, \"form-label\"], [\"id\", \"subjectId\", \"formControlName\", \"subjectId\", 1, \"form-select\", 3, \"disabled\"], [\"for\", \"date\", 1, \"form-label\"], [\"type\", \"date\", \"id\", \"date\", \"formControlName\", \"date\", 1, \"form-control\"], [\"class\", \"mt-4\", 4, \"ngIf\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"card\", \"mt-3\"], [1, \"d-grid\", \"gap-2\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"disabled\", \"click\"], [1, \"btn\", \"btn-outline-info\", \"btn-sm\", 3, \"disabled\", \"click\"], [1, \"btn\", \"btn-outline-success\", \"btn-sm\", 3, \"disabled\", \"click\"], [3, \"value\"], [1, \"mt-4\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"d-flex\", \"gap-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-outline-info\", 3, \"click\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\"], [1, \"table-light\"], [\"color\", \"primary\", 3, \"ngModel\", \"indeterminate\", \"ngModelChange\", \"change\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-end\", \"mt-3\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"me-2\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", 4, \"ngIf\"], [\"color\", \"primary\", 3, \"checked\", \"change\"], [\"role\", \"group\", 1, \"btn-group\", \"btn-group-sm\"], [\"type\", \"radio\", 1, \"btn-check\", 3, \"id\", \"name\", \"value\", \"checked\", \"change\"], [1, \"btn\", \"btn-outline-success\", 3, \"for\"], [1, \"btn\", \"btn-outline-danger\", 3, \"for\"], [1, \"btn\", \"btn-outline-warning\", 3, \"for\"], [1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"], [1, \"text-center\", \"py-4\"], [\"role\", \"status\", 1, \"spinner-border\"], [1, \"visually-hidden\"], [1, \"text-muted\", 2, \"font-size\", \"48px\"], [1, \"text-muted\", \"mt-2\"], [1, \"row\", \"text-center\"], [1, \"col-6\", \"mb-3\"], [1, \"card\", \"bg-light\"], [1, \"text-success\"], [1, \"text-muted\"], [1, \"text-warning\"], [1, \"text-primary\"], [1, \"mt-3\"], [1, \"progress\", \"mb-2\"], [\"role\", \"progressbar\", 1, \"progress-bar\", \"bg-success\"]],\n    template: function TeacherAttendnaceComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"h3\", 5);\n        i0.ɵɵtext(6, \"Mark Attendance\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(7, \"div\", 6)(8, \"form\", 7);\n        i0.ɵɵlistener(\"ngSubmit\", function TeacherAttendnaceComponent_Template_form_ngSubmit_8_listener() {\n          return ctx.submitAttendance();\n        });\n        i0.ɵɵelementStart(9, \"div\", 8)(10, \"div\", 9)(11, \"label\", 10);\n        i0.ɵɵtext(12, \"Class \");\n        i0.ɵɵelementStart(13, \"span\", 11);\n        i0.ɵɵtext(14, \"*\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(15, \"select\", 12);\n        i0.ɵɵlistener(\"change\", function TeacherAttendnaceComponent_Template_select_change_15_listener() {\n          return ctx.onClassChange();\n        });\n        i0.ɵɵelementStart(16, \"option\", 13);\n        i0.ɵɵtext(17, \"Select Class\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(18, TeacherAttendnaceComponent_option_18_Template, 2, 3, \"option\", 14);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(19, \"div\", 9)(20, \"label\", 15);\n        i0.ɵɵtext(21, \"Subject \");\n        i0.ɵɵelementStart(22, \"span\", 11);\n        i0.ɵɵtext(23, \"*\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(24, \"select\", 16)(25, \"option\", 13);\n        i0.ɵɵtext(26, \"Select Subject\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(27, TeacherAttendnaceComponent_option_27_Template, 2, 2, \"option\", 14);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(28, \"div\", 9)(29, \"label\", 17);\n        i0.ɵɵtext(30, \"Date \");\n        i0.ɵɵelementStart(31, \"span\", 11);\n        i0.ɵɵtext(32, \"*\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(33, \"input\", 18);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(34, TeacherAttendnaceComponent_div_34_Template, 38, 6, \"div\", 19);\n        i0.ɵɵtemplate(35, TeacherAttendnaceComponent_div_35_Template, 4, 0, \"div\", 20);\n        i0.ɵɵtemplate(36, TeacherAttendnaceComponent_div_36_Template, 5, 0, \"div\", 20);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(37, \"div\", 9)(38, \"div\", 3)(39, \"div\", 4)(40, \"h5\", 5);\n        i0.ɵɵtext(41, \"Attendance Summary\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(42, \"div\", 6);\n        i0.ɵɵtemplate(43, TeacherAttendnaceComponent_div_43_Template, 35, 7, \"div\", 21);\n        i0.ɵɵtemplate(44, TeacherAttendnaceComponent_div_44_Template, 5, 0, \"div\", 20);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(45, \"div\", 22)(46, \"div\", 4)(47, \"h6\", 5);\n        i0.ɵɵtext(48, \"Quick Actions\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(49, \"div\", 6)(50, \"div\", 23)(51, \"button\", 24);\n        i0.ɵɵlistener(\"click\", function TeacherAttendnaceComponent_Template_button_click_51_listener() {\n          return ctx.gotostudents(\"\", \"\", \"\");\n        });\n        i0.ɵɵelementStart(52, \"mat-icon\");\n        i0.ɵɵtext(53, \"people\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(54, \" View All Students \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(55, \"button\", 25);\n        i0.ɵɵlistener(\"click\", function TeacherAttendnaceComponent_Template_button_click_55_listener() {\n          return ctx.viewAttendanceHistory();\n        });\n        i0.ɵɵelementStart(56, \"mat-icon\");\n        i0.ɵɵtext(57, \"history\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(58, \" Attendance History \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(59, \"button\", 26);\n        i0.ɵɵlistener(\"click\", function TeacherAttendnaceComponent_Template_button_click_59_listener() {\n          return ctx.exportAttendance();\n        });\n        i0.ɵɵelementStart(60, \"mat-icon\");\n        i0.ɵɵtext(61, \"download\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(62, \" Export Report \");\n        i0.ɵɵelementEnd()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"formGroup\", ctx.attendanceForm);\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"ngForOf\", ctx.teacherClasses);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"disabled\", !ctx.selectedClass);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.selectedClass == null ? null : ctx.selectedClass.subjects);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedClass && ctx.students.length > 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.loadingStudents);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedClass && ctx.students.length === 0 && !ctx.loadingStudents);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngIf\", ctx.students.length > 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.students.length === 0);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"disabled\", !ctx.selectedClass);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"disabled\", !ctx.selectedClass);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"disabled\", !ctx.selectedClass);\n      }\n    },\n    dependencies: [i6.NgForOf, i6.NgIf, i7.MatCheckbox, i8.MatIcon, i2.ɵNgNoValidate, i2.NgSelectOption, i2.ɵNgSelectMultipleOption, i2.DefaultValueAccessor, i2.SelectControlValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName, i2.NgModel],\n    styles: [\"\\n\\n.teacher-attendance-container[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  min-height: 100vh;\\n  padding: 15px;\\n}\\n\\n\\n\\n.page-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n  padding: 15px 0;\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.page-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #2c3e50;\\n  font-size: 1.8rem;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n\\n.title-icon[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  color: #3498db;\\n}\\n\\n.page-subtitle[_ngcontent-%COMP%] {\\n  margin: 5px 0 0 0;\\n  color: #7f8c8d;\\n  font-size: 0.9rem;\\n}\\n\\n.header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n\\n\\n.attendance-form[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  padding: 20px;\\n  margin-bottom: 20px;\\n}\\n\\n.form-section[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  margin-bottom: 15px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.form-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 15px;\\n}\\n\\n.form-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.form-field[_ngcontent-%COMP%]   .mat-form-field-wrapper[_ngcontent-%COMP%] {\\n  padding-bottom: 0;\\n}\\n\\n.form-field[_ngcontent-%COMP%]   .mat-form-field-infix[_ngcontent-%COMP%] {\\n  padding: 8px 0;\\n  border-top: none;\\n}\\n\\n.form-field[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n\\n\\n\\n.students-section[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  overflow: hidden;\\n}\\n\\n.students-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);\\n  color: white;\\n  padding: 15px 20px;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.students-count[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 0.8rem;\\n}\\n\\n.students-list[_ngcontent-%COMP%] {\\n  max-height: 500px;\\n  overflow-y: auto;\\n}\\n\\n.student-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 12px 20px;\\n  border-bottom: 1px solid #ecf0f1;\\n  transition: background-color 0.2s ease;\\n}\\n\\n.student-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n\\n.student-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n\\n.student-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.student-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  font-weight: 600;\\n  font-size: 0.9rem;\\n}\\n\\n.student-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.student-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2c3e50;\\n  font-size: 0.9rem;\\n}\\n\\n.student-roll[_ngcontent-%COMP%] {\\n  color: #7f8c8d;\\n  font-size: 0.8rem;\\n}\\n\\n.attendance-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  align-items: center;\\n}\\n\\n.status-chip[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n  min-width: 60px;\\n  text-align: center;\\n}\\n\\n.status-present[_ngcontent-%COMP%] {\\n  background: #d4edda;\\n  color: #155724;\\n}\\n\\n.status-absent[_ngcontent-%COMP%] {\\n  background: #f8d7da;\\n  color: #721c24;\\n}\\n\\n.status-late[_ngcontent-%COMP%] {\\n  background: #fff3cd;\\n  color: #856404;\\n}\\n\\n\\n\\n.form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 20px;\\n  background: #f8f9fa;\\n  border-top: 1px solid #ecf0f1;\\n}\\n\\n.bulk-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.save-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.save-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);\\n  border: none;\\n  box-shadow: 0 4px 6px rgba(39, 174, 96, 0.3);\\n}\\n\\n.save-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 6px 10px rgba(39, 174, 96, 0.4);\\n}\\n\\n\\n\\n.students[_ngcontent-%COMP%] {\\n  background-color: #f5fbff;\\n  color: #00a8d1;\\n  font-size: 14px;\\n  font-weight: 600;\\n  line-height: 20px;\\n}\\n\\n\\n\\n@media (max-width: 1200px) {\\n  .teacher-attendance-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n\\n  .form-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));\\n    gap: 12px;\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .page-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 15px;\\n  }\\n\\n  .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  .header-actions[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: space-between;\\n  }\\n\\n  .attendance-form[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n\\n  .form-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 10px;\\n  }\\n\\n  .students-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 10px;\\n    text-align: center;\\n  }\\n\\n  .student-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 10px;\\n    padding: 15px;\\n  }\\n\\n  .student-info[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n\\n  .attendance-controls[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n\\n  .form-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 15px;\\n  }\\n\\n  .bulk-actions[_ngcontent-%COMP%], .save-actions[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n}\\n\\n@media (max-width: 576px) {\\n  .teacher-attendance-container[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n\\n  .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.3rem;\\n  }\\n\\n  .title-icon[_ngcontent-%COMP%] {\\n    font-size: 1.3rem;\\n  }\\n\\n  .attendance-form[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n\\n  .student-avatar[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n    font-size: 0.8rem;\\n  }\\n\\n  .student-name[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n\\n  .student-roll[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n  }\\n\\n  .status-chip[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n    padding: 3px 6px;\\n    min-width: 50px;\\n  }\\n\\n  .bulk-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%], .save-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n    padding: 8px 12px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "class_r7", "_id", "ɵɵadvance", "ɵɵtextInterpolate2", "className", "section", "subject_r8", "ɵɵtextInterpolate1", "subjectName", "ɵɵlistener", "TeacherAttendnaceComponent_div_34_tr_29_Template_mat_checkbox_change_2_listener", "$event", "restoredCtx", "ɵɵrestoreView", "_r13", "student_r11", "$implicit", "ctx_r12", "ɵɵnextContext", "ɵɵresetView", "updateAttendanceStatus", "checked", "TeacherAttendnaceComponent_div_34_tr_29_Template_input_change_11_listener", "ctx_r14", "TeacherAttendnaceComponent_div_34_tr_29_Template_input_change_14_listener", "ctx_r15", "TeacherAttendnaceComponent_div_34_tr_29_Template_input_change_17_listener", "ctx_r16", "ctx_r9", "attendanceRecords", "ɵɵtextInterpolate", "name", "regNo", "rollNo", "ɵɵelement", "TeacherAttendnaceComponent_div_34_Template_button_click_5_listener", "_r18", "ctx_r17", "viewAttendanceHistory", "TeacherAttendnaceComponent_div_34_Template_button_click_9_listener", "ctx_r19", "exportAttendance", "TeacherAttendnaceComponent_div_34_Template_mat_checkbox_ngModelChange_18_listener", "ctx_r20", "isChecked", "TeacherAttendnaceComponent_div_34_Template_mat_checkbox_change_18_listener", "ctx_r21", "toggleCheckbox", "ɵɵtemplate", "TeacherAttendnaceComponent_div_34_tr_29_Template", "TeacherAttendnaceComponent_div_34_Template_button_click_31_listener", "ctx_r22", "resetForm", "TeacherAttendnaceComponent_div_34_span_34_Template", "ctx_r2", "students", "length", "isIndeterminate", "loading", "attendanceForm", "invalid", "ctx_r5", "getAttendanceStats", "present", "absent", "late", "total", "ɵɵstyleProp", "toFixed", "TeacherAttendnaceComponent", "constructor", "router", "fb", "attendanceService", "userService", "dashboardService", "studentstable", "teacherClasses", "selectedClass", "loadingStudents", "selectedDate", "Date", "toISOString", "split", "ngOnInit", "currentUser", "getUserFromLocalStorage", "user", "initializeForm", "loadTeacherClasses", "group", "classId", "required", "subjectId", "date", "getTeacherDashboard", "subscribe", "next", "response", "success", "dashboard", "classes", "error", "console", "fire", "icon", "title", "text", "onClassChange", "get", "value", "find", "c", "loadStudentsForClass", "initializeAttendanceRecords", "for<PERSON>ach", "student", "studentId", "status", "updateCheckboxState", "presentCount", "Object", "values", "filter", "totalStudents", "submitAttendance", "formData", "map", "teacherId", "attendanceData", "markBatchAttendance", "timer", "showConfirmButton", "message", "reset", "gotostudents", "selectedDepartment", "selectedSection", "navigate", "queryParams", "class", "department", "seestudents", "_", "ɵɵdirectiveInject", "i1", "Router", "i2", "FormBuilder", "i3", "AttendanceService", "i4", "UserService", "i5", "DashboardService", "_2", "selectors", "decls", "vars", "consts", "template", "TeacherAttendnaceComponent_Template", "rf", "ctx", "TeacherAttendnaceComponent_Template_form_ngSubmit_8_listener", "TeacherAttendnaceComponent_Template_select_change_15_listener", "TeacherAttendnaceComponent_option_18_Template", "TeacherAttendnaceComponent_option_27_Template", "TeacherAttendnaceComponent_div_34_Template", "TeacherAttendnaceComponent_div_35_Template", "TeacherAttendnaceComponent_div_36_Template", "TeacherAttendnaceComponent_div_43_Template", "TeacherAttendnaceComponent_div_44_Template", "TeacherAttendnaceComponent_Template_button_click_51_listener", "TeacherAttendnaceComponent_Template_button_click_55_listener", "TeacherAttendnaceComponent_Template_button_click_59_listener", "subjects"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\teacher-dashboard\\teacher-attendnace\\teacher-attendnace.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\teacher-dashboard\\teacher-attendnace\\teacher-attendnace.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { AttendanceService } from '../../../services/attendance.service';\r\nimport { UserService } from '../../../services/user.service';\r\nimport { DashboardService } from '../../../services/dashboard.service';\r\nimport { AttendanceRecord } from '../../../models/attendance';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-teacher-attendnace',\r\n  templateUrl: './teacher-attendnace.component.html',\r\n  styleUrls: ['./teacher-attendnace.component.css']\r\n})\r\nexport class TeacherAttendnaceComponent implements OnInit {\r\n  isChecked: boolean = false;\r\n  isIndeterminate: boolean = true;\r\n  studentstable: boolean = false;\r\n\r\n  attendanceForm!: FormGroup;\r\n  currentUser: any;\r\n  teacherClasses: any[] = [];\r\n  selectedClass: any = null;\r\n  students: any[] = [];\r\n  attendanceRecords: { [key: string]: string } = {};\r\n  loading = false;\r\n  loadingStudents = false;\r\n  selectedDate = new Date().toISOString().split('T')[0];\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private fb: FormBuilder,\r\n    private attendanceService: AttendanceService,\r\n    private userService: UserService,\r\n    private dashboardService: DashboardService\r\n  ) {}\r\n  ngOnInit(): void {\r\n    this.currentUser = this.userService.getUserFromLocalStorage()?.user;\r\n    this.initializeForm();\r\n    this.loadTeacherClasses();\r\n  }\r\n\r\n  initializeForm(): void {\r\n    this.attendanceForm = this.fb.group({\r\n      classId: ['', Validators.required],\r\n      subjectId: ['', Validators.required],\r\n      date: [this.selectedDate, Validators.required]\r\n    });\r\n  }\r\n\r\n  loadTeacherClasses(): void {\r\n    if (!this.currentUser) return;\r\n\r\n    this.loading = true;\r\n    this.dashboardService.getTeacherDashboard(this.currentUser._id).subscribe({\r\n      next: (response) => {\r\n        if (response.success && response.dashboard) {\r\n          this.teacherClasses = response.dashboard.classes || [];\r\n        }\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading teacher classes:', error);\r\n        Swal.fire({\r\n          icon: 'error',\r\n          title: 'Error',\r\n          text: 'Failed to load classes. Please try again.',\r\n        });\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  onClassChange(): void {\r\n    const classId = this.attendanceForm.get('classId')?.value;\r\n    this.selectedClass = this.teacherClasses.find(c => c._id === classId);\r\n\r\n    if (this.selectedClass) {\r\n      this.loadStudentsForClass(classId);\r\n    }\r\n  }\r\n\r\n  loadStudentsForClass(classId: string): void {\r\n    this.loadingStudents = true;\r\n    this.students = [];\r\n    this.attendanceRecords = {};\r\n\r\n    // Get students from the selected class\r\n    // This would typically come from a students service\r\n    // For now, we'll simulate with the class data\r\n    if (this.selectedClass && this.selectedClass.students) {\r\n      this.students = this.selectedClass.students;\r\n      this.initializeAttendanceRecords();\r\n    }\r\n\r\n    this.loadingStudents = false;\r\n  }\r\n\r\n  initializeAttendanceRecords(): void {\r\n    this.students.forEach(student => {\r\n      this.attendanceRecords[student._id] = 'present'; // Default to present\r\n    });\r\n  }\r\n\r\n  toggleCheckbox(): void {\r\n    if (this.isIndeterminate) {\r\n      this.isIndeterminate = false;\r\n      this.isChecked = true;\r\n      // Mark all as present\r\n      this.students.forEach(student => {\r\n        this.attendanceRecords[student._id] = 'present';\r\n      });\r\n    } else if (this.isChecked) {\r\n      this.isChecked = false;\r\n      // Mark all as absent\r\n      this.students.forEach(student => {\r\n        this.attendanceRecords[student._id] = 'absent';\r\n      });\r\n    } else {\r\n      this.isIndeterminate = true;\r\n      // Reset to default (present)\r\n      this.students.forEach(student => {\r\n        this.attendanceRecords[student._id] = 'present';\r\n      });\r\n    }\r\n  }\r\n\r\n  updateAttendanceStatus(studentId: string, status: string): void {\r\n    this.attendanceRecords[studentId] = status;\r\n    this.updateCheckboxState();\r\n  }\r\n\r\n  updateCheckboxState(): void {\r\n    const presentCount = Object.values(this.attendanceRecords).filter(status => status === 'present').length;\r\n    const totalStudents = this.students.length;\r\n\r\n    if (presentCount === 0) {\r\n      this.isChecked = false;\r\n      this.isIndeterminate = false;\r\n    } else if (presentCount === totalStudents) {\r\n      this.isChecked = true;\r\n      this.isIndeterminate = false;\r\n    } else {\r\n      this.isChecked = false;\r\n      this.isIndeterminate = true;\r\n    }\r\n  }\r\n\r\n  submitAttendance(): void {\r\n    if (this.attendanceForm.invalid || !this.currentUser) {\r\n      Swal.fire({\r\n        icon: 'warning',\r\n        title: 'Invalid Form',\r\n        text: 'Please fill all required fields.',\r\n      });\r\n      return;\r\n    }\r\n\r\n    const formData = this.attendanceForm.value;\r\n    const attendanceRecords: AttendanceRecord[] = this.students.map(student => ({\r\n      studentId: student._id,\r\n      teacherId: this.currentUser._id,\r\n      subjectId: formData.subjectId,\r\n      status: (this.attendanceRecords[student._id] || 'absent') as 'present' | 'absent' | 'late'\r\n    }));\r\n\r\n    const attendanceData = {\r\n      classId: formData.classId,\r\n      date: formData.date,\r\n      attendanceRecords,\r\n      teacherId: this.currentUser._id,\r\n      subjectId: formData.subjectId\r\n    };\r\n\r\n    this.loading = true;\r\n\r\n    this.attendanceService.markBatchAttendance(attendanceData).subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          Swal.fire({\r\n            icon: 'success',\r\n            title: 'Attendance Marked',\r\n            text: 'Attendance has been successfully recorded!',\r\n            timer: 3000,\r\n            showConfirmButton: false\r\n          });\r\n          this.resetForm();\r\n        }\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error marking attendance:', error);\r\n        Swal.fire({\r\n          icon: 'error',\r\n          title: 'Error',\r\n          text: error.error?.message || 'Failed to mark attendance. Please try again.',\r\n        });\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  resetForm(): void {\r\n    this.attendanceForm.reset();\r\n    this.initializeForm();\r\n    this.selectedClass = null;\r\n    this.students = [];\r\n    this.attendanceRecords = {};\r\n    this.isChecked = false;\r\n    this.isIndeterminate = true;\r\n  }\r\n\r\n  getAttendanceStats(): any {\r\n    const total = this.students.length;\r\n    const present = Object.values(this.attendanceRecords).filter(status => status === 'present').length;\r\n    const absent = Object.values(this.attendanceRecords).filter(status => status === 'absent').length;\r\n    const late = Object.values(this.attendanceRecords).filter(status => status === 'late').length;\r\n\r\n    return { total, present, absent, late };\r\n  }\r\n  // Navigate to students view\r\n  gotostudents(selectedClass: string, selectedDepartment: string, selectedSection: string): void {\r\n    this.router.navigate(['/dashboard/teacher/class-students'], {\r\n      queryParams: {\r\n        class: selectedClass,\r\n        department: selectedDepartment,\r\n        section: selectedSection\r\n      }\r\n    });\r\n  }\r\n\r\n  seestudents(): void {\r\n    this.studentstable = !this.studentstable;\r\n  }\r\n\r\n  viewAttendanceHistory(): void {\r\n    if (!this.selectedClass) {\r\n      Swal.fire({\r\n        icon: 'warning',\r\n        title: 'No Class Selected',\r\n        text: 'Please select a class first.',\r\n      });\r\n      return;\r\n    }\r\n\r\n    this.router.navigate(['/dashboard/teacher/attendance-history'], {\r\n      queryParams: {\r\n        classId: this.selectedClass._id,\r\n        className: this.selectedClass.className\r\n      }\r\n    });\r\n  }\r\n\r\n  exportAttendance(): void {\r\n    if (!this.selectedClass) {\r\n      Swal.fire({\r\n        icon: 'warning',\r\n        title: 'No Class Selected',\r\n        text: 'Please select a class first.',\r\n      });\r\n      return;\r\n    }\r\n\r\n    Swal.fire({\r\n      title: 'Export Attendance',\r\n      text: 'This feature will be available soon.',\r\n      icon: 'info'\r\n    });\r\n  }\r\n}\r\n", "\r\n<div class=\"container-fluid\">\r\n  <div class=\"row\">\r\n    <!-- Attendance Form -->\r\n    <div class=\"col-md-8\">\r\n      <div class=\"card\">\r\n        <div class=\"card-header\">\r\n          <h3 class=\"fw-bold mb-0\">Mark Attendance</h3>\r\n        </div>\r\n        <div class=\"card-body\">\r\n          <form [formGroup]=\"attendanceForm\" (ngSubmit)=\"submitAttendance()\">\r\n            <div class=\"row mb-3\">\r\n              <div class=\"col-md-4\">\r\n                <label for=\"classId\" class=\"form-label\">Class <span class=\"text-danger\">*</span></label>\r\n                <select\r\n                  class=\"form-select\"\r\n                  id=\"classId\"\r\n                  formControlName=\"classId\"\r\n                  (change)=\"onClassChange()\">\r\n                  <option value=\"\">Select Class</option>\r\n                  <option *ngFor=\"let class of teacherClasses\" [value]=\"class._id\">\r\n                    {{ class.className }} - {{ class.section }}\r\n                  </option>\r\n                </select>\r\n              </div>\r\n              <div class=\"col-md-4\">\r\n                <label for=\"subjectId\" class=\"form-label\">Subject <span class=\"text-danger\">*</span></label>\r\n                <select\r\n                  class=\"form-select\"\r\n                  id=\"subjectId\"\r\n                  formControlName=\"subjectId\"\r\n                  [disabled]=\"!selectedClass\">\r\n                  <option value=\"\">Select Subject</option>\r\n                  <option *ngFor=\"let subject of selectedClass?.subjects\" [value]=\"subject._id\">\r\n                    {{ subject.subjectName }}\r\n                  </option>\r\n                </select>\r\n              </div>\r\n              <div class=\"col-md-4\">\r\n                <label for=\"date\" class=\"form-label\">Date <span class=\"text-danger\">*</span></label>\r\n                <input\r\n                  type=\"date\"\r\n                  class=\"form-control\"\r\n                  id=\"date\"\r\n                  formControlName=\"date\">\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Students List -->\r\n            <div *ngIf=\"selectedClass && students.length > 0\" class=\"mt-4\">\r\n              <div class=\"d-flex justify-content-between align-items-center mb-3\">\r\n                <h5>Students ({{ students.length }})</h5>\r\n                <div class=\"d-flex gap-2\">\r\n                  <button type=\"button\" class=\"btn btn-sm btn-outline-secondary\" (click)=\"viewAttendanceHistory()\">\r\n                    <mat-icon>history</mat-icon> History\r\n                  </button>\r\n                  <button type=\"button\" class=\"btn btn-sm btn-outline-info\" (click)=\"exportAttendance()\">\r\n                    <mat-icon>download</mat-icon> Export\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"table-responsive\">\r\n                <table class=\"table table-hover\">\r\n                  <thead class=\"table-light\">\r\n                    <tr>\r\n                      <th>\r\n                        <mat-checkbox\r\n                          color=\"primary\"\r\n                          [(ngModel)]=\"isChecked\"\r\n                          [indeterminate]=\"isIndeterminate\"\r\n                          (change)=\"toggleCheckbox()\">\r\n                          Select All\r\n                        </mat-checkbox>\r\n                      </th>\r\n                      <th>Student Name</th>\r\n                      <th>Reg No</th>\r\n                      <th>Roll No</th>\r\n                      <th>Attendance</th>\r\n                    </tr>\r\n                  </thead>\r\n                  <tbody>\r\n                    <tr *ngFor=\"let student of students\">\r\n                      <td>\r\n                        <mat-checkbox\r\n                          color=\"primary\"\r\n                          [checked]=\"attendanceRecords[student._id] === 'present'\"\r\n                          (change)=\"updateAttendanceStatus(student._id, $event.checked ? 'present' : 'absent')\">\r\n                        </mat-checkbox>\r\n                      </td>\r\n                      <td>{{ student.name }}</td>\r\n                      <td>{{ student.regNo }}</td>\r\n                      <td>{{ student.rollNo }}</td>\r\n                      <td>\r\n                        <div class=\"btn-group btn-group-sm\" role=\"group\">\r\n                          <input\r\n                            type=\"radio\"\r\n                            class=\"btn-check\"\r\n                            [id]=\"'present-' + student._id\"\r\n                            [name]=\"'attendance-' + student._id\"\r\n                            [value]=\"'present'\"\r\n                            [checked]=\"attendanceRecords[student._id] === 'present'\"\r\n                            (change)=\"updateAttendanceStatus(student._id, 'present')\">\r\n                          <label\r\n                            class=\"btn btn-outline-success\"\r\n                            [for]=\"'present-' + student._id\">\r\n                            Present\r\n                          </label>\r\n\r\n                          <input\r\n                            type=\"radio\"\r\n                            class=\"btn-check\"\r\n                            [id]=\"'absent-' + student._id\"\r\n                            [name]=\"'attendance-' + student._id\"\r\n                            [value]=\"'absent'\"\r\n                            [checked]=\"attendanceRecords[student._id] === 'absent'\"\r\n                            (change)=\"updateAttendanceStatus(student._id, 'absent')\">\r\n                          <label\r\n                            class=\"btn btn-outline-danger\"\r\n                            [for]=\"'absent-' + student._id\">\r\n                            Absent\r\n                          </label>\r\n\r\n                          <input\r\n                            type=\"radio\"\r\n                            class=\"btn-check\"\r\n                            [id]=\"'late-' + student._id\"\r\n                            [name]=\"'attendance-' + student._id\"\r\n                            [value]=\"'late'\"\r\n                            [checked]=\"attendanceRecords[student._id] === 'late'\"\r\n                            (change)=\"updateAttendanceStatus(student._id, 'late')\">\r\n                          <label\r\n                            class=\"btn btn-outline-warning\"\r\n                            [for]=\"'late-' + student._id\">\r\n                            Late\r\n                          </label>\r\n                        </div>\r\n                      </td>\r\n                    </tr>\r\n                  </tbody>\r\n                </table>\r\n              </div>\r\n\r\n              <div class=\"d-flex justify-content-end mt-3\">\r\n                <button\r\n                  type=\"button\"\r\n                  class=\"btn btn-secondary me-2\"\r\n                  (click)=\"resetForm()\">\r\n                  Reset\r\n                </button>\r\n                <button\r\n                  type=\"submit\"\r\n                  class=\"btn btn-primary\"\r\n                  [disabled]=\"loading || attendanceForm.invalid || students.length === 0\">\r\n                  <span *ngIf=\"loading\" class=\"spinner-border spinner-border-sm me-2\"></span>\r\n                  <mat-icon>save</mat-icon> Submit Attendance\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <div *ngIf=\"loadingStudents\" class=\"text-center py-4\">\r\n              <div class=\"spinner-border\" role=\"status\">\r\n                <span class=\"visually-hidden\">Loading students...</span>\r\n              </div>\r\n            </div>\r\n\r\n            <div *ngIf=\"selectedClass && students.length === 0 && !loadingStudents\" class=\"text-center py-4\">\r\n              <mat-icon class=\"text-muted\" style=\"font-size: 48px;\">school</mat-icon>\r\n              <p class=\"text-muted mt-2\">No students found for this class</p>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Attendance Summary -->\r\n    <div class=\"col-md-4\">\r\n      <div class=\"card\">\r\n        <div class=\"card-header\">\r\n          <h5 class=\"fw-bold mb-0\">Attendance Summary</h5>\r\n        </div>\r\n        <div class=\"card-body\">\r\n          <div *ngIf=\"students.length > 0\">\r\n            <div class=\"row text-center\">\r\n              <div class=\"col-6 mb-3\">\r\n                <div class=\"card bg-light\">\r\n                  <div class=\"card-body\">\r\n                    <h4 class=\"text-success\">{{ getAttendanceStats().present }}</h4>\r\n                    <small class=\"text-muted\">Present</small>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"col-6 mb-3\">\r\n                <div class=\"card bg-light\">\r\n                  <div class=\"card-body\">\r\n                    <h4 class=\"text-danger\">{{ getAttendanceStats().absent }}</h4>\r\n                    <small class=\"text-muted\">Absent</small>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"col-6 mb-3\">\r\n                <div class=\"card bg-light\">\r\n                  <div class=\"card-body\">\r\n                    <h4 class=\"text-warning\">{{ getAttendanceStats().late }}</h4>\r\n                    <small class=\"text-muted\">Late</small>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"col-6 mb-3\">\r\n                <div class=\"card bg-light\">\r\n                  <div class=\"card-body\">\r\n                    <h4 class=\"text-primary\">{{ getAttendanceStats().total }}</h4>\r\n                    <small class=\"text-muted\">Total</small>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"mt-3\">\r\n              <div class=\"progress mb-2\">\r\n                <div\r\n                  class=\"progress-bar bg-success\"\r\n                  role=\"progressbar\"\r\n                  [style.width.%]=\"(getAttendanceStats().present / getAttendanceStats().total) * 100\">\r\n                </div>\r\n              </div>\r\n              <small class=\"text-muted\">\r\n                Attendance Rate: {{ ((getAttendanceStats().present / getAttendanceStats().total) * 100).toFixed(1) }}%\r\n              </small>\r\n            </div>\r\n          </div>\r\n\r\n          <div *ngIf=\"students.length === 0\" class=\"text-center py-4\">\r\n            <mat-icon class=\"text-muted\" style=\"font-size: 48px;\">analytics</mat-icon>\r\n            <p class=\"text-muted mt-2\">Select a class to view summary</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Quick Actions -->\r\n      <div class=\"card mt-3\">\r\n        <div class=\"card-header\">\r\n          <h6 class=\"fw-bold mb-0\">Quick Actions</h6>\r\n        </div>\r\n        <div class=\"card-body\">\r\n          <div class=\"d-grid gap-2\">\r\n            <button\r\n              class=\"btn btn-outline-primary btn-sm\"\r\n              (click)=\"gotostudents('', '', '')\"\r\n              [disabled]=\"!selectedClass\">\r\n              <mat-icon>people</mat-icon> View All Students\r\n            </button>\r\n            <button\r\n              class=\"btn btn-outline-info btn-sm\"\r\n              (click)=\"viewAttendanceHistory()\"\r\n              [disabled]=\"!selectedClass\">\r\n              <mat-icon>history</mat-icon> Attendance History\r\n            </button>\r\n            <button\r\n              class=\"btn btn-outline-success btn-sm\"\r\n              (click)=\"exportAttendance()\"\r\n              [disabled]=\"!selectedClass\">\r\n              <mat-icon>download</mat-icon> Export Report\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>"], "mappings": "AAEA,SAAiCA,UAAU,QAAQ,gBAAgB;AAKnE,OAAOC,IAAI,MAAM,aAAa;;;;;;;;;;;;ICaZC,EAAA,CAAAC,cAAA,iBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFoCH,EAAA,CAAAI,UAAA,UAAAC,QAAA,CAAAC,GAAA,CAAmB;IAC9DN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,QAAA,CAAAI,SAAA,SAAAJ,QAAA,CAAAK,OAAA,MACF;;;;;IAWAV,EAAA,CAAAC,cAAA,iBAA8E;IAC5ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAF+CH,EAAA,CAAAI,UAAA,UAAAO,UAAA,CAAAL,GAAA,CAAqB;IAC3EN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAY,kBAAA,MAAAD,UAAA,CAAAE,WAAA,MACF;;;;;;IA+CEb,EAAA,CAAAC,cAAA,SAAqC;IAK/BD,EAAA,CAAAc,UAAA,oBAAAC,gFAAAC,MAAA;MAAA,MAAAC,WAAA,GAAAjB,EAAA,CAAAkB,aAAA,CAAAC,IAAA;MAAA,MAAAC,WAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,OAAA,GAAAtB,EAAA,CAAAuB,aAAA;MAAA,OAAUvB,EAAA,CAAAwB,WAAA,CAAAF,OAAA,CAAAG,sBAAA,CAAAL,WAAA,CAAAd,GAAA,EAAAU,MAAA,CAAAU,OAAA,GAAqD,SAAS,GAAG,QAAQ,CAAC;IAAA,EAAC;IACvF1B,EAAA,CAAAG,YAAA,EAAe;IAEjBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7BH,EAAA,CAAAC,cAAA,SAAI;IASED,EAAA,CAAAc,UAAA,oBAAAa,0EAAA;MAAA,MAAAV,WAAA,GAAAjB,EAAA,CAAAkB,aAAA,CAAAC,IAAA;MAAA,MAAAC,WAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAO,OAAA,GAAA5B,EAAA,CAAAuB,aAAA;MAAA,OAAUvB,EAAA,CAAAwB,WAAA,CAAAI,OAAA,CAAAH,sBAAA,CAAAL,WAAA,CAAAd,GAAA,EAAoC,SAAS,CAAC;IAAA,EAAC;IAP3DN,EAAA,CAAAG,YAAA,EAO4D;IAC5DH,EAAA,CAAAC,cAAA,iBAEmC;IACjCD,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAERH,EAAA,CAAAC,cAAA,iBAO2D;IAAzDD,EAAA,CAAAc,UAAA,oBAAAe,0EAAA;MAAA,MAAAZ,WAAA,GAAAjB,EAAA,CAAAkB,aAAA,CAAAC,IAAA;MAAA,MAAAC,WAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAS,OAAA,GAAA9B,EAAA,CAAAuB,aAAA;MAAA,OAAUvB,EAAA,CAAAwB,WAAA,CAAAM,OAAA,CAAAL,sBAAA,CAAAL,WAAA,CAAAd,GAAA,EAAoC,QAAQ,CAAC;IAAA,EAAC;IAP1DN,EAAA,CAAAG,YAAA,EAO2D;IAC3DH,EAAA,CAAAC,cAAA,iBAEkC;IAChCD,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAERH,EAAA,CAAAC,cAAA,iBAOyD;IAAvDD,EAAA,CAAAc,UAAA,oBAAAiB,0EAAA;MAAA,MAAAd,WAAA,GAAAjB,EAAA,CAAAkB,aAAA,CAAAC,IAAA;MAAA,MAAAC,WAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAW,OAAA,GAAAhC,EAAA,CAAAuB,aAAA;MAAA,OAAUvB,EAAA,CAAAwB,WAAA,CAAAQ,OAAA,CAAAP,sBAAA,CAAAL,WAAA,CAAAd,GAAA,EAAoC,MAAM,CAAC;IAAA,EAAC;IAPxDN,EAAA,CAAAG,YAAA,EAOyD;IACzDH,EAAA,CAAAC,cAAA,iBAEgC;IAC9BD,EAAA,CAAAE,MAAA,cACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAjDRH,EAAA,CAAAO,SAAA,GAAwD;IAAxDP,EAAA,CAAAI,UAAA,YAAA6B,MAAA,CAAAC,iBAAA,CAAAd,WAAA,CAAAd,GAAA,gBAAwD;IAIxDN,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAmC,iBAAA,CAAAf,WAAA,CAAAgB,IAAA,CAAkB;IAClBpC,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAmC,iBAAA,CAAAf,WAAA,CAAAiB,KAAA,CAAmB;IACnBrC,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAmC,iBAAA,CAAAf,WAAA,CAAAkB,MAAA,CAAoB;IAMlBtC,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAI,UAAA,oBAAAgB,WAAA,CAAAd,GAAA,CAA+B,yBAAAc,WAAA,CAAAd,GAAA,iCAAA2B,MAAA,CAAAC,iBAAA,CAAAd,WAAA,CAAAd,GAAA;IAO/BN,EAAA,CAAAO,SAAA,GAAgC;IAAhCP,EAAA,CAAAI,UAAA,qBAAAgB,WAAA,CAAAd,GAAA,CAAgC;IAOhCN,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAAI,UAAA,mBAAAgB,WAAA,CAAAd,GAAA,CAA8B,yBAAAc,WAAA,CAAAd,GAAA,gCAAA2B,MAAA,CAAAC,iBAAA,CAAAd,WAAA,CAAAd,GAAA;IAO9BN,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAI,UAAA,oBAAAgB,WAAA,CAAAd,GAAA,CAA+B;IAO/BN,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAAI,UAAA,iBAAAgB,WAAA,CAAAd,GAAA,CAA4B,yBAAAc,WAAA,CAAAd,GAAA,8BAAA2B,MAAA,CAAAC,iBAAA,CAAAd,WAAA,CAAAd,GAAA;IAO5BN,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAI,UAAA,kBAAAgB,WAAA,CAAAd,GAAA,CAA6B;;;;;IAqBvCN,EAAA,CAAAuC,SAAA,eAA2E;;;;;;IAzGjFvC,EAAA,CAAAC,cAAA,cAA+D;IAEvDD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzCH,EAAA,CAAAC,cAAA,cAA0B;IACuCD,EAAA,CAAAc,UAAA,mBAAA0B,mEAAA;MAAAxC,EAAA,CAAAkB,aAAA,CAAAuB,IAAA;MAAA,MAAAC,OAAA,GAAA1C,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAkB,OAAA,CAAAC,qBAAA,EAAuB;IAAA,EAAC;IAC9F3C,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,gBAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAAuF;IAA7BD,EAAA,CAAAc,UAAA,mBAAA8B,mEAAA;MAAA5C,EAAA,CAAAkB,aAAA,CAAAuB,IAAA;MAAA,MAAAI,OAAA,GAAA7C,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAqB,OAAA,CAAAC,gBAAA,EAAkB;IAAA,EAAC;IACpF9C,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,gBAChC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAIbH,EAAA,CAAAC,cAAA,eAA8B;IAOlBD,EAAA,CAAAc,UAAA,2BAAAiC,kFAAA/B,MAAA;MAAAhB,EAAA,CAAAkB,aAAA,CAAAuB,IAAA;MAAA,MAAAO,OAAA,GAAAhD,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAAAwB,OAAA,CAAAC,SAAA,GAAAjC,MAAA;IAAA,EAAuB,oBAAAkC,2EAAA;MAAAlD,EAAA,CAAAkB,aAAA,CAAAuB,IAAA;MAAA,MAAAU,OAAA,GAAAnD,EAAA,CAAAuB,aAAA;MAAA,OAEbvB,EAAA,CAAAwB,WAAA,CAAA2B,OAAA,CAAAC,cAAA,EAAgB;IAAA,EAFH;IAGvBpD,EAAA,CAAAE,MAAA,oBACF;IAAAF,EAAA,CAAAG,YAAA,EAAe;IAEjBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGvBH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAqD,UAAA,KAAAC,gDAAA,mBAwDK;IACPtD,EAAA,CAAAG,YAAA,EAAQ;IAIZH,EAAA,CAAAC,cAAA,eAA6C;IAIzCD,EAAA,CAAAc,UAAA,mBAAAyC,oEAAA;MAAAvD,EAAA,CAAAkB,aAAA,CAAAuB,IAAA;MAAA,MAAAe,OAAA,GAAAxD,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAgC,OAAA,CAAAC,SAAA,EAAW;IAAA,EAAC;IACrBzD,EAAA,CAAAE,MAAA,eACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAG0E;IACxED,EAAA,CAAAqD,UAAA,KAAAK,kDAAA,mBAA2E;IAC3E1D,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,2BAC5B;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAzGLH,EAAA,CAAAO,SAAA,GAAgC;IAAhCP,EAAA,CAAAY,kBAAA,eAAA+C,MAAA,CAAAC,QAAA,CAAAC,MAAA,MAAgC;IAkB1B7D,EAAA,CAAAO,SAAA,IAAuB;IAAvBP,EAAA,CAAAI,UAAA,YAAAuD,MAAA,CAAAV,SAAA,CAAuB,kBAAAU,MAAA,CAAAG,eAAA;IAaL9D,EAAA,CAAAO,SAAA,IAAW;IAAXP,EAAA,CAAAI,UAAA,YAAAuD,MAAA,CAAAC,QAAA,CAAW;IAuErC5D,EAAA,CAAAO,SAAA,GAAuE;IAAvEP,EAAA,CAAAI,UAAA,aAAAuD,MAAA,CAAAI,OAAA,IAAAJ,MAAA,CAAAK,cAAA,CAAAC,OAAA,IAAAN,MAAA,CAAAC,QAAA,CAAAC,MAAA,OAAuE;IAChE7D,EAAA,CAAAO,SAAA,GAAa;IAAbP,EAAA,CAAAI,UAAA,SAAAuD,MAAA,CAAAI,OAAA,CAAa;;;;;IAM1B/D,EAAA,CAAAC,cAAA,cAAsD;IAEpBD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAI5DH,EAAA,CAAAC,cAAA,cAAiG;IACzCD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACvEH,EAAA,CAAAC,cAAA,YAA2B;IAAAD,EAAA,CAAAE,MAAA,uCAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAcnEH,EAAA,CAAAC,cAAA,UAAiC;IAKED,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChEH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAI/CH,EAAA,CAAAC,cAAA,cAAwB;IAGMD,EAAA,CAAAE,MAAA,IAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9DH,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAI9CH,EAAA,CAAAC,cAAA,eAAwB;IAGOD,EAAA,CAAAE,MAAA,IAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7DH,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAI5CH,EAAA,CAAAC,cAAA,eAAwB;IAGOD,EAAA,CAAAE,MAAA,IAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9DH,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAM/CH,EAAA,CAAAC,cAAA,eAAkB;IAEdD,EAAA,CAAAuC,SAAA,eAIM;IACRvC,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,iBAA0B;IACxBD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAzCuBH,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAmC,iBAAA,CAAA+B,MAAA,CAAAC,kBAAA,GAAAC,OAAA,CAAkC;IAQnCpE,EAAA,CAAAO,SAAA,GAAiC;IAAjCP,EAAA,CAAAmC,iBAAA,CAAA+B,MAAA,CAAAC,kBAAA,GAAAE,MAAA,CAAiC;IAQhCrE,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAmC,iBAAA,CAAA+B,MAAA,CAAAC,kBAAA,GAAAG,IAAA,CAA+B;IAQ/BtE,EAAA,CAAAO,SAAA,GAAgC;IAAhCP,EAAA,CAAAmC,iBAAA,CAAA+B,MAAA,CAAAC,kBAAA,GAAAI,KAAA,CAAgC;IAY3DvE,EAAA,CAAAO,SAAA,GAAmF;IAAnFP,EAAA,CAAAwE,WAAA,UAAAN,MAAA,CAAAC,kBAAA,GAAAC,OAAA,GAAAF,MAAA,CAAAC,kBAAA,GAAAI,KAAA,YAAmF;IAIrFvE,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAY,kBAAA,wBAAAsD,MAAA,CAAAC,kBAAA,GAAAC,OAAA,GAAAF,MAAA,CAAAC,kBAAA,GAAAI,KAAA,QAAAE,OAAA,UACF;;;;;IAIJzE,EAAA,CAAAC,cAAA,cAA4D;IACJD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1EH,EAAA,CAAAC,cAAA,YAA2B;IAAAD,EAAA,CAAAE,MAAA,qCAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;AD5NzE,OAAM,MAAOuE,0BAA0B;EAerCC,YACUC,MAAc,EACdC,EAAe,EACfC,iBAAoC,EACpCC,WAAwB,EACxBC,gBAAkC;IAJlC,KAAAJ,MAAM,GAANA,MAAM;IACN,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAnB1B,KAAA/B,SAAS,GAAY,KAAK;IAC1B,KAAAa,eAAe,GAAY,IAAI;IAC/B,KAAAmB,aAAa,GAAY,KAAK;IAI9B,KAAAC,cAAc,GAAU,EAAE;IAC1B,KAAAC,aAAa,GAAQ,IAAI;IACzB,KAAAvB,QAAQ,GAAU,EAAE;IACpB,KAAA1B,iBAAiB,GAA8B,EAAE;IACjD,KAAA6B,OAAO,GAAG,KAAK;IACf,KAAAqB,eAAe,GAAG,KAAK;IACvB,KAAAC,YAAY,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAQlD;EACHC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,GAAG,IAAI,CAACX,WAAW,CAACY,uBAAuB,EAAE,EAAEC,IAAI;IACnE,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEAD,cAAcA,CAAA;IACZ,IAAI,CAAC7B,cAAc,GAAG,IAAI,CAACa,EAAE,CAACkB,KAAK,CAAC;MAClCC,OAAO,EAAE,CAAC,EAAE,EAAElG,UAAU,CAACmG,QAAQ,CAAC;MAClCC,SAAS,EAAE,CAAC,EAAE,EAAEpG,UAAU,CAACmG,QAAQ,CAAC;MACpCE,IAAI,EAAE,CAAC,IAAI,CAACd,YAAY,EAAEvF,UAAU,CAACmG,QAAQ;KAC9C,CAAC;EACJ;EAEAH,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACJ,WAAW,EAAE;IAEvB,IAAI,CAAC3B,OAAO,GAAG,IAAI;IACnB,IAAI,CAACiB,gBAAgB,CAACoB,mBAAmB,CAAC,IAAI,CAACV,WAAW,CAACpF,GAAG,CAAC,CAAC+F,SAAS,CAAC;MACxEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,SAAS,EAAE;UAC1C,IAAI,CAACvB,cAAc,GAAGqB,QAAQ,CAACE,SAAS,CAACC,OAAO,IAAI,EAAE;;QAExD,IAAI,CAAC3C,OAAO,GAAG,KAAK;MACtB,CAAC;MACD4C,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD5G,IAAI,CAAC8G,IAAI,CAAC;UACRC,IAAI,EAAE,OAAO;UACbC,KAAK,EAAE,OAAO;UACdC,IAAI,EAAE;SACP,CAAC;QACF,IAAI,CAACjD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAkD,aAAaA,CAAA;IACX,MAAMjB,OAAO,GAAG,IAAI,CAAChC,cAAc,CAACkD,GAAG,CAAC,SAAS,CAAC,EAAEC,KAAK;IACzD,IAAI,CAAChC,aAAa,GAAG,IAAI,CAACD,cAAc,CAACkC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC/G,GAAG,KAAK0F,OAAO,CAAC;IAErE,IAAI,IAAI,CAACb,aAAa,EAAE;MACtB,IAAI,CAACmC,oBAAoB,CAACtB,OAAO,CAAC;;EAEtC;EAEAsB,oBAAoBA,CAACtB,OAAe;IAClC,IAAI,CAACZ,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACxB,QAAQ,GAAG,EAAE;IAClB,IAAI,CAAC1B,iBAAiB,GAAG,EAAE;IAE3B;IACA;IACA;IACA,IAAI,IAAI,CAACiD,aAAa,IAAI,IAAI,CAACA,aAAa,CAACvB,QAAQ,EAAE;MACrD,IAAI,CAACA,QAAQ,GAAG,IAAI,CAACuB,aAAa,CAACvB,QAAQ;MAC3C,IAAI,CAAC2D,2BAA2B,EAAE;;IAGpC,IAAI,CAACnC,eAAe,GAAG,KAAK;EAC9B;EAEAmC,2BAA2BA,CAAA;IACzB,IAAI,CAAC3D,QAAQ,CAAC4D,OAAO,CAACC,OAAO,IAAG;MAC9B,IAAI,CAACvF,iBAAiB,CAACuF,OAAO,CAACnH,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC;IACnD,CAAC,CAAC;EACJ;;EAEA8C,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACU,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACb,SAAS,GAAG,IAAI;MACrB;MACA,IAAI,CAACW,QAAQ,CAAC4D,OAAO,CAACC,OAAO,IAAG;QAC9B,IAAI,CAACvF,iBAAiB,CAACuF,OAAO,CAACnH,GAAG,CAAC,GAAG,SAAS;MACjD,CAAC,CAAC;KACH,MAAM,IAAI,IAAI,CAAC2C,SAAS,EAAE;MACzB,IAAI,CAACA,SAAS,GAAG,KAAK;MACtB;MACA,IAAI,CAACW,QAAQ,CAAC4D,OAAO,CAACC,OAAO,IAAG;QAC9B,IAAI,CAACvF,iBAAiB,CAACuF,OAAO,CAACnH,GAAG,CAAC,GAAG,QAAQ;MAChD,CAAC,CAAC;KACH,MAAM;MACL,IAAI,CAACwD,eAAe,GAAG,IAAI;MAC3B;MACA,IAAI,CAACF,QAAQ,CAAC4D,OAAO,CAACC,OAAO,IAAG;QAC9B,IAAI,CAACvF,iBAAiB,CAACuF,OAAO,CAACnH,GAAG,CAAC,GAAG,SAAS;MACjD,CAAC,CAAC;;EAEN;EAEAmB,sBAAsBA,CAACiG,SAAiB,EAAEC,MAAc;IACtD,IAAI,CAACzF,iBAAiB,CAACwF,SAAS,CAAC,GAAGC,MAAM;IAC1C,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAA,mBAAmBA,CAAA;IACjB,MAAMC,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC7F,iBAAiB,CAAC,CAAC8F,MAAM,CAACL,MAAM,IAAIA,MAAM,KAAK,SAAS,CAAC,CAAC9D,MAAM;IACxG,MAAMoE,aAAa,GAAG,IAAI,CAACrE,QAAQ,CAACC,MAAM;IAE1C,IAAIgE,YAAY,KAAK,CAAC,EAAE;MACtB,IAAI,CAAC5E,SAAS,GAAG,KAAK;MACtB,IAAI,CAACa,eAAe,GAAG,KAAK;KAC7B,MAAM,IAAI+D,YAAY,KAAKI,aAAa,EAAE;MACzC,IAAI,CAAChF,SAAS,GAAG,IAAI;MACrB,IAAI,CAACa,eAAe,GAAG,KAAK;KAC7B,MAAM;MACL,IAAI,CAACb,SAAS,GAAG,KAAK;MACtB,IAAI,CAACa,eAAe,GAAG,IAAI;;EAE/B;EAEAoE,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAClE,cAAc,CAACC,OAAO,IAAI,CAAC,IAAI,CAACyB,WAAW,EAAE;MACpD3F,IAAI,CAAC8G,IAAI,CAAC;QACRC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,cAAc;QACrBC,IAAI,EAAE;OACP,CAAC;MACF;;IAGF,MAAMmB,QAAQ,GAAG,IAAI,CAACnE,cAAc,CAACmD,KAAK;IAC1C,MAAMjF,iBAAiB,GAAuB,IAAI,CAAC0B,QAAQ,CAACwE,GAAG,CAACX,OAAO,KAAK;MAC1EC,SAAS,EAAED,OAAO,CAACnH,GAAG;MACtB+H,SAAS,EAAE,IAAI,CAAC3C,WAAW,CAACpF,GAAG;MAC/B4F,SAAS,EAAEiC,QAAQ,CAACjC,SAAS;MAC7ByB,MAAM,EAAG,IAAI,CAACzF,iBAAiB,CAACuF,OAAO,CAACnH,GAAG,CAAC,IAAI;KACjD,CAAC,CAAC;IAEH,MAAMgI,cAAc,GAAG;MACrBtC,OAAO,EAAEmC,QAAQ,CAACnC,OAAO;MACzBG,IAAI,EAAEgC,QAAQ,CAAChC,IAAI;MACnBjE,iBAAiB;MACjBmG,SAAS,EAAE,IAAI,CAAC3C,WAAW,CAACpF,GAAG;MAC/B4F,SAAS,EAAEiC,QAAQ,CAACjC;KACrB;IAED,IAAI,CAACnC,OAAO,GAAG,IAAI;IAEnB,IAAI,CAACe,iBAAiB,CAACyD,mBAAmB,CAACD,cAAc,CAAC,CAACjC,SAAS,CAAC;MACnEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpBzG,IAAI,CAAC8G,IAAI,CAAC;YACRC,IAAI,EAAE,SAAS;YACfC,KAAK,EAAE,mBAAmB;YAC1BC,IAAI,EAAE,4CAA4C;YAClDwB,KAAK,EAAE,IAAI;YACXC,iBAAiB,EAAE;WACpB,CAAC;UACF,IAAI,CAAChF,SAAS,EAAE;;QAElB,IAAI,CAACM,OAAO,GAAG,KAAK;MACtB,CAAC;MACD4C,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD5G,IAAI,CAAC8G,IAAI,CAAC;UACRC,IAAI,EAAE,OAAO;UACbC,KAAK,EAAE,OAAO;UACdC,IAAI,EAAEL,KAAK,CAACA,KAAK,EAAE+B,OAAO,IAAI;SAC/B,CAAC;QACF,IAAI,CAAC3E,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAN,SAASA,CAAA;IACP,IAAI,CAACO,cAAc,CAAC2E,KAAK,EAAE;IAC3B,IAAI,CAAC9C,cAAc,EAAE;IACrB,IAAI,CAACV,aAAa,GAAG,IAAI;IACzB,IAAI,CAACvB,QAAQ,GAAG,EAAE;IAClB,IAAI,CAAC1B,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACe,SAAS,GAAG,KAAK;IACtB,IAAI,CAACa,eAAe,GAAG,IAAI;EAC7B;EAEAK,kBAAkBA,CAAA;IAChB,MAAMI,KAAK,GAAG,IAAI,CAACX,QAAQ,CAACC,MAAM;IAClC,MAAMO,OAAO,GAAG0D,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC7F,iBAAiB,CAAC,CAAC8F,MAAM,CAACL,MAAM,IAAIA,MAAM,KAAK,SAAS,CAAC,CAAC9D,MAAM;IACnG,MAAMQ,MAAM,GAAGyD,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC7F,iBAAiB,CAAC,CAAC8F,MAAM,CAACL,MAAM,IAAIA,MAAM,KAAK,QAAQ,CAAC,CAAC9D,MAAM;IACjG,MAAMS,IAAI,GAAGwD,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC7F,iBAAiB,CAAC,CAAC8F,MAAM,CAACL,MAAM,IAAIA,MAAM,KAAK,MAAM,CAAC,CAAC9D,MAAM;IAE7F,OAAO;MAAEU,KAAK;MAAEH,OAAO;MAAEC,MAAM;MAAEC;IAAI,CAAE;EACzC;EACA;EACAsE,YAAYA,CAACzD,aAAqB,EAAE0D,kBAA0B,EAAEC,eAAuB;IACrF,IAAI,CAAClE,MAAM,CAACmE,QAAQ,CAAC,CAAC,mCAAmC,CAAC,EAAE;MAC1DC,WAAW,EAAE;QACXC,KAAK,EAAE9D,aAAa;QACpB+D,UAAU,EAAEL,kBAAkB;QAC9BnI,OAAO,EAAEoI;;KAEZ,CAAC;EACJ;EAEAK,WAAWA,CAAA;IACT,IAAI,CAAClE,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;EAC1C;EAEAtC,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACwC,aAAa,EAAE;MACvBpF,IAAI,CAAC8G,IAAI,CAAC;QACRC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,mBAAmB;QAC1BC,IAAI,EAAE;OACP,CAAC;MACF;;IAGF,IAAI,CAACpC,MAAM,CAACmE,QAAQ,CAAC,CAAC,uCAAuC,CAAC,EAAE;MAC9DC,WAAW,EAAE;QACXhD,OAAO,EAAE,IAAI,CAACb,aAAa,CAAC7E,GAAG;QAC/BG,SAAS,EAAE,IAAI,CAAC0E,aAAa,CAAC1E;;KAEjC,CAAC;EACJ;EAEAqC,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACqC,aAAa,EAAE;MACvBpF,IAAI,CAAC8G,IAAI,CAAC;QACRC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,mBAAmB;QAC1BC,IAAI,EAAE;OACP,CAAC;MACF;;IAGFjH,IAAI,CAAC8G,IAAI,CAAC;MACRE,KAAK,EAAE,mBAAmB;MAC1BC,IAAI,EAAE,sCAAsC;MAC5CF,IAAI,EAAE;KACP,CAAC;EACJ;EAAC,QAAAsC,CAAA,G;qBA9PU1E,0BAA0B,EAAA1E,EAAA,CAAAqJ,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAvJ,EAAA,CAAAqJ,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAzJ,EAAA,CAAAqJ,iBAAA,CAAAK,EAAA,CAAAC,iBAAA,GAAA3J,EAAA,CAAAqJ,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAA7J,EAAA,CAAAqJ,iBAAA,CAAAS,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA1BtF,0BAA0B;IAAAuF,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCbvCvK,EAAA,CAAAC,cAAA,aAA6B;QAMMD,EAAA,CAAAE,MAAA,sBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAE/CH,EAAA,CAAAC,cAAA,aAAuB;QACcD,EAAA,CAAAc,UAAA,sBAAA2J,6DAAA;UAAA,OAAYD,GAAA,CAAAtC,gBAAA,EAAkB;QAAA,EAAC;QAChElI,EAAA,CAAAC,cAAA,aAAsB;QAEsBD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAC,cAAA,gBAA0B;QAAAD,EAAA,CAAAE,MAAA,SAAC;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAChFH,EAAA,CAAAC,cAAA,kBAI6B;QAA3BD,EAAA,CAAAc,UAAA,oBAAA4J,8DAAA;UAAA,OAAUF,GAAA,CAAAvD,aAAA,EAAe;QAAA,EAAC;QAC1BjH,EAAA,CAAAC,cAAA,kBAAiB;QAAAD,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACtCH,EAAA,CAAAqD,UAAA,KAAAsH,6CAAA,qBAES;QACX3K,EAAA,CAAAG,YAAA,EAAS;QAEXH,EAAA,CAAAC,cAAA,cAAsB;QACsBD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAC,cAAA,gBAA0B;QAAAD,EAAA,CAAAE,MAAA,SAAC;QAAAF,EAAA,CAAAG,YAAA,EAAO;QACpFH,EAAA,CAAAC,cAAA,kBAI8B;QACXD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACxCH,EAAA,CAAAqD,UAAA,KAAAuH,6CAAA,qBAES;QACX5K,EAAA,CAAAG,YAAA,EAAS;QAEXH,EAAA,CAAAC,cAAA,cAAsB;QACiBD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAC,cAAA,gBAA0B;QAAAD,EAAA,CAAAE,MAAA,SAAC;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAC5EH,EAAA,CAAAuC,SAAA,iBAIyB;QAC3BvC,EAAA,CAAAG,YAAA,EAAM;QAIRH,EAAA,CAAAqD,UAAA,KAAAwH,0CAAA,mBA6GM;QAEN7K,EAAA,CAAAqD,UAAA,KAAAyH,0CAAA,kBAIM;QAEN9K,EAAA,CAAAqD,UAAA,KAAA0H,0CAAA,kBAGM;QACR/K,EAAA,CAAAG,YAAA,EAAO;QAMbH,EAAA,CAAAC,cAAA,cAAsB;QAGSD,EAAA,CAAAE,MAAA,0BAAkB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAElDH,EAAA,CAAAC,cAAA,cAAuB;QACrBD,EAAA,CAAAqD,UAAA,KAAA2H,0CAAA,mBAgDM;QAENhL,EAAA,CAAAqD,UAAA,KAAA4H,0CAAA,kBAGM;QACRjL,EAAA,CAAAG,YAAA,EAAM;QAIRH,EAAA,CAAAC,cAAA,eAAuB;QAEMD,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAE7CH,EAAA,CAAAC,cAAA,cAAuB;QAIjBD,EAAA,CAAAc,UAAA,mBAAAoK,6DAAA;UAAA,OAASV,GAAA,CAAA5B,YAAA,CAAa,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;QAAA,EAAC;QAElC5I,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAACH,EAAA,CAAAE,MAAA,2BAC9B;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,kBAG8B;QAD5BD,EAAA,CAAAc,UAAA,mBAAAqK,6DAAA;UAAA,OAASX,GAAA,CAAA7H,qBAAA,EAAuB;QAAA,EAAC;QAEjC3C,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAACH,EAAA,CAAAE,MAAA,4BAC/B;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,kBAG8B;QAD5BD,EAAA,CAAAc,UAAA,mBAAAsK,6DAAA;UAAA,OAASZ,GAAA,CAAA1H,gBAAA,EAAkB;QAAA,EAAC;QAE5B9C,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAACH,EAAA,CAAAE,MAAA,uBAChC;QAAAF,EAAA,CAAAG,YAAA,EAAS;;;QA7PLH,EAAA,CAAAO,SAAA,GAA4B;QAA5BP,EAAA,CAAAI,UAAA,cAAAoK,GAAA,CAAAxG,cAAA,CAA4B;QAUAhE,EAAA,CAAAO,SAAA,IAAiB;QAAjBP,EAAA,CAAAI,UAAA,YAAAoK,GAAA,CAAAtF,cAAA,CAAiB;QAW3ClF,EAAA,CAAAO,SAAA,GAA2B;QAA3BP,EAAA,CAAAI,UAAA,cAAAoK,GAAA,CAAArF,aAAA,CAA2B;QAECnF,EAAA,CAAAO,SAAA,GAA0B;QAA1BP,EAAA,CAAAI,UAAA,YAAAoK,GAAA,CAAArF,aAAA,kBAAAqF,GAAA,CAAArF,aAAA,CAAAkG,QAAA,CAA0B;QAgBtDrL,EAAA,CAAAO,SAAA,GAA0C;QAA1CP,EAAA,CAAAI,UAAA,SAAAoK,GAAA,CAAArF,aAAA,IAAAqF,GAAA,CAAA5G,QAAA,CAAAC,MAAA,KAA0C;QA+G1C7D,EAAA,CAAAO,SAAA,GAAqB;QAArBP,EAAA,CAAAI,UAAA,SAAAoK,GAAA,CAAApF,eAAA,CAAqB;QAMrBpF,EAAA,CAAAO,SAAA,GAAgE;QAAhEP,EAAA,CAAAI,UAAA,SAAAoK,GAAA,CAAArF,aAAA,IAAAqF,GAAA,CAAA5G,QAAA,CAAAC,MAAA,WAAA2G,GAAA,CAAApF,eAAA,CAAgE;QAgBlEpF,EAAA,CAAAO,SAAA,GAAyB;QAAzBP,EAAA,CAAAI,UAAA,SAAAoK,GAAA,CAAA5G,QAAA,CAAAC,MAAA,KAAyB;QAkDzB7D,EAAA,CAAAO,SAAA,GAA2B;QAA3BP,EAAA,CAAAI,UAAA,SAAAoK,GAAA,CAAA5G,QAAA,CAAAC,MAAA,OAA2B;QAiB7B7D,EAAA,CAAAO,SAAA,GAA2B;QAA3BP,EAAA,CAAAI,UAAA,cAAAoK,GAAA,CAAArF,aAAA,CAA2B;QAM3BnF,EAAA,CAAAO,SAAA,GAA2B;QAA3BP,EAAA,CAAAI,UAAA,cAAAoK,GAAA,CAAArF,aAAA,CAA2B;QAM3BnF,EAAA,CAAAO,SAAA,GAA2B;QAA3BP,EAAA,CAAAI,UAAA,cAAAoK,GAAA,CAAArF,aAAA,CAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}