{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/checkbox\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/forms\";\nfunction StudentTeachersComponent_tr_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 14)(2, \"mat-checkbox\", 15);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\", 16);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\", 16);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const teacher_r2 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", teacher_r2.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", teacher_r2.subject, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", teacher_r2.session, \" \");\n  }\n}\nfunction StudentTeachersComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"h2\");\n    i0.ɵɵtext(2, \"Attendance Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"table\")(6, \"thead\")(7, \"tr\", 5)(8, \"th\", 6)(9, \"mat-checkbox\", 7);\n    i0.ɵɵlistener(\"ngModelChange\", function StudentTeachersComponent_div_29_Template_mat_checkbox_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.isChecked = $event);\n    })(\"change\", function StudentTeachersComponent_div_29_Template_mat_checkbox_change_9_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.toggleCheckbox());\n    });\n    i0.ɵɵtext(10, \" Date \");\n    i0.ɵɵelementStart(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"arrow_downward\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(13, \"th\", 8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"tbody\")(15, \"tr\")(16, \"td\", 14)(17, \"mat-checkbox\", 15);\n    i0.ɵɵtext(18, \" Nov 30, 2024 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(19, \"td\", 16);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(20, \"div\", 10)(21, \"div\")(22, \"button\", 11);\n    i0.ɵɵtext(23, \"Previous\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 12);\n    i0.ɵɵtext(25, \"Page 1 of 1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\")(27, \"button\", 11);\n    i0.ɵɵtext(28, \"Next\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.isChecked)(\"indeterminate\", ctx_r1.isIndeterminate);\n  }\n}\nexport let StudentTeachersComponent = /*#__PURE__*/(() => {\n  class StudentTeachersComponent {\n    constructor(router) {\n      this.router = router;\n      this.isChecked = false;\n      this.isIndeterminate = true;\n      this.studentstable = false;\n      this.detailtable = false;\n      this.teachers = [{\n        subject: 'English',\n        name: 'Saad',\n        session: '2024'\n      }, {\n        subject: 'Urdu',\n        name: 'Luqman',\n        session: '2024'\n      }];\n    }\n    toggleCheckbox() {\n      if (this.isIndeterminate) {\n        this.isIndeterminate = false;\n        this.isChecked = true;\n      } else if (this.isChecked) {\n        this.isChecked = false;\n      } else {\n        this.isIndeterminate = true;\n      }\n    }\n    seedetail() {\n      this.detailtable = !this.detailtable;\n    }\n    static #_ = this.ɵfac = function StudentTeachersComponent_Factory(t) {\n      return new (t || StudentTeachersComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StudentTeachersComponent,\n      selectors: [[\"app-student-teachers\"]],\n      decls: 30,\n      vars: 4,\n      consts: [[1, \"fw-bold\"], [1, \"maindiv\"], [1, \"secondarydiv\"], [1, \"table-container\", \"mb-4\"], [1, \"table-responsive\"], [1, \"tablehead\"], [1, \"one\"], [\"color\", \"primary\", 3, \"ngModel\", \"indeterminate\", \"ngModelChange\", \"change\"], [1, \"two\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"p-3\", \"align-items-center\", \"justify-content-between\"], [1, \"btn\", \"btn-top\", \"border\"], [1, \"page\"], [\"id\", \"details\", 4, \"ngIf\"], [1, \"name\"], [\"color\", \"primary\"], [1, \"para\"], [\"id\", \"details\"]],\n      template: function StudentTeachersComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"h1\", 0);\n          i0.ɵɵtext(1, \"Teachers Details\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"div\", 1)(3, \"div\", 2)(4, \"div\", 3)(5, \"div\", 4)(6, \"table\")(7, \"thead\")(8, \"tr\", 5)(9, \"th\", 6)(10, \"mat-checkbox\", 7);\n          i0.ɵɵlistener(\"ngModelChange\", function StudentTeachersComponent_Template_mat_checkbox_ngModelChange_10_listener($event) {\n            return ctx.isChecked = $event;\n          })(\"change\", function StudentTeachersComponent_Template_mat_checkbox_change_10_listener() {\n            return ctx.toggleCheckbox();\n          });\n          i0.ɵɵtext(11, \" Teacher Name \");\n          i0.ɵɵelementStart(12, \"mat-icon\");\n          i0.ɵɵtext(13, \"arrow_downward\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"th\");\n          i0.ɵɵtext(15, \"Subject \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"th\", 8);\n          i0.ɵɵtext(17, \"Session \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"tbody\");\n          i0.ɵɵtemplate(19, StudentTeachersComponent_tr_19_Template, 8, 3, \"tr\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(20, \"div\", 10)(21, \"div\")(22, \"button\", 11);\n          i0.ɵɵtext(23, \"Previous\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"div\", 12);\n          i0.ɵɵtext(25, \"Page 1 of 1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\")(27, \"button\", 11);\n          i0.ɵɵtext(28, \"Next\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(29, StudentTeachersComponent_div_29_Template, 29, 2, \"div\", 13);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngModel\", ctx.isChecked)(\"indeterminate\", ctx.isIndeterminate);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngForOf\", ctx.teachers);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngIf\", ctx.detailtable);\n        }\n      },\n      dependencies: [i2.NgForOf, i2.NgIf, i3.MatCheckbox, i4.MatIcon, i5.NgControlStatus, i5.NgModel]\n    });\n  }\n  return StudentTeachersComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}