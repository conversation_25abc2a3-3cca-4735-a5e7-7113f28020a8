{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { AdminDashboardRoutingModule } from './admin-dashboard-routing.module';\nimport { MaterialModule } from 'src/app/material';\nimport { DashboardOverviewComponent } from './dashboard-overview/dashboard-overview.component';\nimport { ProgramsComponent } from './programs/programs.component';\nimport { TimetableComponent } from './timetable/timetable.component';\nimport { NoticesComponent } from './notices/notices.component';\nimport { ProgramDialogComponent } from './programs/program-dialog/program-dialog.component';\nimport * as i0 from \"@angular/core\";\nexport class AdminDashboardModule {\n  static #_ = this.ɵfac = function AdminDashboardModule_Factory(t) {\n    return new (t || AdminDashboardModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AdminDashboardModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, ReactiveFormsModule, FormsModule, AdminDashboardRoutingModule, MaterialModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AdminDashboardModule, {\n    declarations: [DashboardOverviewComponent, ProgramsComponent, TimetableComponent, NoticesComponent, ProgramDialogComponent],\n    imports: [CommonModule, ReactiveFormsModule, FormsModule, AdminDashboardRoutingModule, MaterialModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "FormsModule", "AdminDashboardRoutingModule", "MaterialModule", "DashboardOverviewComponent", "ProgramsComponent", "TimetableComponent", "NoticesComponent", "ProgramDialogComponent", "AdminDashboardModule", "_", "_2", "_3", "declarations", "imports"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\admin-dashboard.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\r\n\r\n\r\nimport { AdminDashboardRoutingModule } from './admin-dashboard-routing.module';\r\nimport { MaterialModule } from 'src/app/material';\r\nimport { DashboardOverviewComponent } from './dashboard-overview/dashboard-overview.component';\r\nimport { ProgramsComponent } from './programs/programs.component';\r\nimport { TimetableComponent } from './timetable/timetable.component';\r\nimport { NoticesComponent } from './notices/notices.component';\r\nimport { ProgramDialogComponent } from './programs/program-dialog/program-dialog.component';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    DashboardOverviewComponent,\r\n    ProgramsComponent,\r\n    TimetableComponent,\r\n    NoticesComponent,\r\n    ProgramDialogComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    FormsModule,\r\n    AdminDashboardRoutingModule,\r\n    MaterialModule,\r\n\r\n  ]\r\n})\r\nexport class AdminDashboardModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AAGjE,SAASC,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,0BAA0B,QAAQ,mDAAmD;AAC9F,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,sBAAsB,QAAQ,oDAAoD;;AAmB3F,OAAM,MAAOC,oBAAoB;EAAA,QAAAC,CAAA,G;qBAApBD,oBAAoB;EAAA;EAAA,QAAAE,EAAA,G;UAApBF;EAAoB;EAAA,QAAAG,EAAA,G;cAR7Bb,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,2BAA2B,EAC3BC,cAAc;EAAA;;;2EAILM,oBAAoB;IAAAI,YAAA,GAf7BT,0BAA0B,EAC1BC,iBAAiB,EACjBC,kBAAkB,EAClBC,gBAAgB,EAChBC,sBAAsB;IAAAM,OAAA,GAGtBf,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,2BAA2B,EAC3BC,cAAc;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}