{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/user.service\";\nimport * as i2 from \"../../../services/dashboard.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/progress-spinner\";\nimport * as i9 from \"@angular/material/tooltip\";\nfunction StudentClassesComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading your classes...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StudentClassesComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Error Loading Classes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function StudentClassesComponent_div_11_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.refreshData());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction StudentClassesComponent_div_12_mat_card_73_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 30)(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 31)(7, \"div\", 32);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"number\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const subject_r7 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(subject_r7.subjectName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", subject_r7.present, \"/\", subject_r7.total, \" classes attended\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"good\", subject_r7.percentage >= 75)(\"warning\", subject_r7.percentage >= 60 && subject_r7.percentage < 75)(\"danger\", subject_r7.percentage < 60);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(9, 10, subject_r7.percentage, \"1.0-0\"), \"% \");\n  }\n}\nfunction StudentClassesComponent_div_12_mat_card_73_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 26)(1, \"mat-card-header\")(2, \"mat-card-title\");\n    i0.ɵɵtext(3, \"My Subjects\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"mat-card-content\")(5, \"div\", 27);\n    i0.ɵɵtemplate(6, StudentClassesComponent_div_12_mat_card_73_div_6_Template, 10, 13, \"div\", 28);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.studentDashboard.subjectAttendance);\n  }\n}\nfunction StudentClassesComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"mat-card\", 11)(2, \"mat-card-header\")(3, \"mat-card-title\");\n    i0.ɵɵtext(4, \"Current Class Information\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"mat-card-content\")(6, \"div\", 12)(7, \"div\", 13)(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"class\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 14)(11, \"h4\");\n    i0.ɵɵtext(12, \"Class\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 13)(16, \"mat-icon\");\n    i0.ɵɵtext(17, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 14)(19, \"h4\");\n    i0.ɵɵtext(20, \"Department\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"p\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"div\", 13)(24, \"mat-icon\");\n    i0.ɵɵtext(25, \"library_books\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 14)(27, \"h4\");\n    i0.ɵɵtext(28, \"Program\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"p\");\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"div\", 13)(32, \"mat-icon\");\n    i0.ɵɵtext(33, \"school\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 14)(35, \"h4\");\n    i0.ɵɵtext(36, \"Semester\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"p\");\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(39, \"div\", 15)(40, \"mat-card\", 16)(41, \"mat-card-content\")(42, \"div\", 17)(43, \"div\", 18)(44, \"mat-icon\");\n    i0.ɵɵtext(45, \"subject\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 19)(47, \"h3\");\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"p\");\n    i0.ɵɵtext(50, \"Total Subjects\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(51, \"mat-card\", 16)(52, \"mat-card-content\")(53, \"div\", 17)(54, \"div\", 18)(55, \"mat-icon\");\n    i0.ɵɵtext(56, \"person\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(57, \"div\", 19)(58, \"h3\");\n    i0.ɵɵtext(59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(60, \"p\");\n    i0.ɵɵtext(61, \"Teachers\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(62, \"mat-card\", 16)(63, \"mat-card-content\")(64, \"div\", 17)(65, \"div\", 18)(66, \"mat-icon\");\n    i0.ɵɵtext(67, \"group\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(68, \"div\", 19)(69, \"h3\");\n    i0.ɵɵtext(70);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(71, \"p\");\n    i0.ɵɵtext(72, \"Classmates\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵtemplate(73, StudentClassesComponent_div_12_mat_card_73_Template, 7, 1, \"mat-card\", 20);\n    i0.ɵɵelementStart(74, \"div\", 21)(75, \"h3\");\n    i0.ɵɵtext(76, \"Quick Actions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(77, \"div\", 22)(78, \"button\", 23)(79, \"mat-icon\");\n    i0.ɵɵtext(80, \"fact_check\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(81, \" View Attendance \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(82, \"button\", 24)(83, \"mat-icon\");\n    i0.ɵɵtext(84, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(85, \" View Teachers \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(86, \"button\", 25)(87, \"mat-icon\");\n    i0.ɵɵtext(88, \"subject\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(89, \" View Subjects \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(14);\n    i0.ɵɵtextInterpolate((ctx_r2.studentDashboard.student.class == null ? null : ctx_r2.studentDashboard.student.class.name) || \"Not Assigned\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r2.studentDashboard.student.department == null ? null : ctx_r2.studentDashboard.student.department.name) || \"Not Assigned\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r2.studentDashboard.student.program == null ? null : ctx_r2.studentDashboard.student.program.name) || \"Not Assigned\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r2.studentDashboard.student.semester || \"Not Assigned\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate((ctx_r2.studentDashboard.subjectAttendance == null ? null : ctx_r2.studentDashboard.subjectAttendance.length) || 0);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate((ctx_r2.studentDashboard.teachers == null ? null : ctx_r2.studentDashboard.teachers.length) || 0);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate((ctx_r2.studentDashboard.classmates == null ? null : ctx_r2.studentDashboard.classmates.length) || 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r2.studentDashboard.subjectAttendance == null ? null : ctx_r2.studentDashboard.subjectAttendance.length) > 0);\n  }\n}\nexport class StudentClassesComponent {\n  constructor(userService, dashboardService) {\n    this.userService = userService;\n    this.dashboardService = dashboardService;\n    this.loading = false;\n    this.error = null;\n  }\n  ngOnInit() {\n    this.currentUser = this.userService.getUserFromLocalStorage()?.user;\n    this.loadStudentClasses();\n  }\n  loadStudentClasses() {\n    if (!this.currentUser) return;\n    this.loading = true;\n    this.error = null;\n    this.dashboardService.getStudentDashboard(this.currentUser._id).subscribe({\n      next: response => {\n        if (response.success) {\n          this.studentDashboard = response.dashboard;\n        } else {\n          this.error = 'Failed to load student classes';\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading student classes:', error);\n        this.error = 'Error loading classes data';\n        this.loading = false;\n      }\n    });\n  }\n  refreshData() {\n    this.loadStudentClasses();\n  }\n  static #_ = this.ɵfac = function StudentClassesComponent_Factory(t) {\n    return new (t || StudentClassesComponent)(i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i2.DashboardService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: StudentClassesComponent,\n    selectors: [[\"app-student-classes\"]],\n    decls: 13,\n    vars: 3,\n    consts: [[1, \"student-classes-container\"], [1, \"page-header\"], [1, \"header-content\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Refresh Data\", 3, \"click\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"classes-content\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"error-container\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"classes-content\"], [1, \"class-info-card\"], [1, \"class-details\"], [1, \"detail-item\"], [1, \"detail-content\"], [1, \"stats-grid\"], [1, \"stat-card\"], [1, \"stat-content\"], [1, \"stat-icon\"], [1, \"stat-info\"], [\"class\", \"subjects-card\", 4, \"ngIf\"], [1, \"quick-actions\"], [1, \"action-buttons\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/dashboard/student/attendance\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"routerLink\", \"/dashboard/student/teachers\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/dashboard/student/subjects\"], [1, \"subjects-card\"], [1, \"subjects-list\"], [\"class\", \"subject-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"subject-item\"], [1, \"subject-info\"], [1, \"subject-stats\"], [1, \"attendance-percentage\"]],\n    template: function StudentClassesComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\");\n        i0.ɵɵtext(4, \"My Classes\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"p\");\n        i0.ɵɵtext(6, \"View your enrolled classes and class information\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(7, \"button\", 3);\n        i0.ɵɵlistener(\"click\", function StudentClassesComponent_Template_button_click_7_listener() {\n          return ctx.refreshData();\n        });\n        i0.ɵɵelementStart(8, \"mat-icon\");\n        i0.ɵɵtext(9, \"refresh\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(10, StudentClassesComponent_div_10_Template, 4, 0, \"div\", 4);\n        i0.ɵɵtemplate(11, StudentClassesComponent_div_11_Template, 11, 1, \"div\", 5);\n        i0.ɵɵtemplate(12, StudentClassesComponent_div_12_Template, 90, 8, \"div\", 6);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.studentDashboard && !ctx.loading && !ctx.error);\n      }\n    },\n    dependencies: [i3.NgForOf, i3.NgIf, i4.RouterLink, i5.MatButton, i5.MatIconButton, i6.MatCard, i6.MatCardContent, i6.MatCardHeader, i6.MatCardTitle, i7.MatIcon, i8.MatProgressSpinner, i9.MatTooltip, i3.DecimalPipe],\n    styles: [\".student-classes-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.page-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 30px;\\n  padding-bottom: 20px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.header-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #333;\\n  font-size: 2rem;\\n  font-weight: 500;\\n}\\n\\n.header-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 5px 0 0 0;\\n  color: #666;\\n  font-size: 1rem;\\n}\\n\\n.loading-container[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 60px 20px;\\n  text-align: center;\\n}\\n\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  color: #666;\\n}\\n\\n.error-container[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  width: 48px;\\n  height: 48px;\\n  color: #f44336;\\n  margin-bottom: 20px;\\n}\\n\\n.classes-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 30px;\\n}\\n\\n.class-info-card[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.class-details[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 20px;\\n  margin-top: 20px;\\n}\\n\\n.detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n  padding: 15px;\\n  background: #f8f9fa;\\n  border-radius: 8px;\\n}\\n\\n.detail-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #2196f3;\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n}\\n\\n.detail-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 5px 0;\\n  color: #333;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n}\\n\\n.detail-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 1rem;\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 20px;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n}\\n\\n.stat-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n}\\n\\n.stat-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  width: 32px;\\n  height: 32px;\\n  opacity: 0.8;\\n}\\n\\n.stat-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 5px 0;\\n  font-size: 1.8rem;\\n  font-weight: 600;\\n}\\n\\n.stat-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  opacity: 0.9;\\n  font-size: 0.9rem;\\n}\\n\\n.subjects-card[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.subjects-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 15px;\\n  margin-top: 20px;\\n}\\n\\n.subject-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 20px;\\n  background: #f8f9fa;\\n  border-radius: 8px;\\n  border-left: 4px solid #2196f3;\\n}\\n\\n.subject-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 5px 0;\\n  color: #333;\\n  font-size: 1.1rem;\\n  font-weight: 500;\\n}\\n\\n.subject-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 0.9rem;\\n}\\n\\n.subject-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n}\\n\\n.attendance-percentage[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  border-radius: 20px;\\n  font-weight: 600;\\n  font-size: 0.9rem;\\n  min-width: 60px;\\n  text-align: center;\\n}\\n\\n.attendance-percentage.good[_ngcontent-%COMP%] {\\n  background: #e8f5e8;\\n  color: #2e7d32;\\n}\\n\\n.attendance-percentage.warning[_ngcontent-%COMP%] {\\n  background: #fff3e0;\\n  color: #f57c00;\\n}\\n\\n.attendance-percentage.danger[_ngcontent-%COMP%] {\\n  background: #ffebee;\\n  color: #d32f2f;\\n}\\n\\n.quick-actions[_ngcontent-%COMP%] {\\n  margin-top: 30px;\\n}\\n\\n.quick-actions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 20px 0;\\n  color: #333;\\n  font-size: 1.3rem;\\n  font-weight: 500;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n  flex-wrap: wrap;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 12px 24px;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  width: 20px;\\n  height: 20px;\\n}\\n\\n@media (max-width: 768px) {\\n  .student-classes-container[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n  \\n  .page-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 15px;\\n  }\\n  \\n  .class-details[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  \\n  .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  \\n  .subject-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 15px;\\n  }\\n  \\n  .action-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  \\n  .action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYWRtaW5pc3RyYXRpb24vc3R1ZGVudC1kYXNoYm9hcmQvc3R1ZGVudC1jbGFzc2VzL3N0dWRlbnQtY2xhc3Nlcy5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsYUFBYTtFQUNiLGlCQUFpQjtFQUNqQixjQUFjO0FBQ2hCOztBQUVBO0VBQ0UsYUFBYTtFQUNiLDhCQUE4QjtFQUM5QixtQkFBbUI7RUFDbkIsbUJBQW1CO0VBQ25CLG9CQUFvQjtFQUNwQixnQ0FBZ0M7QUFDbEM7O0FBRUE7RUFDRSxTQUFTO0VBQ1QsV0FBVztFQUNYLGVBQWU7RUFDZixnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRSxpQkFBaUI7RUFDakIsV0FBVztFQUNYLGVBQWU7QUFDakI7O0FBRUE7RUFDRSxhQUFhO0VBQ2Isc0JBQXNCO0VBQ3RCLG1CQUFtQjtFQUNuQix1QkFBdUI7RUFDdkIsa0JBQWtCO0VBQ2xCLGtCQUFrQjtBQUNwQjs7QUFFQTtFQUNFLGdCQUFnQjtFQUNoQixXQUFXO0FBQ2I7O0FBRUE7RUFDRSxlQUFlO0VBQ2YsV0FBVztFQUNYLFlBQVk7RUFDWixjQUFjO0VBQ2QsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0UsYUFBYTtFQUNiLHNCQUFzQjtFQUN0QixTQUFTO0FBQ1g7O0FBRUE7RUFDRSxtQkFBbUI7QUFDckI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsMkRBQTJEO0VBQzNELFNBQVM7RUFDVCxnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLFNBQVM7RUFDVCxhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLGtCQUFrQjtBQUNwQjs7QUFFQTtFQUNFLGNBQWM7RUFDZCxlQUFlO0VBQ2YsV0FBVztFQUNYLFlBQVk7QUFDZDs7QUFFQTtFQUNFLGlCQUFpQjtFQUNqQixXQUFXO0VBQ1gsaUJBQWlCO0VBQ2pCLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLFNBQVM7RUFDVCxXQUFXO0VBQ1gsZUFBZTtBQUNqQjs7QUFFQTtFQUNFLGFBQWE7RUFDYiwyREFBMkQ7RUFDM0QsU0FBUztFQUNULG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLDZEQUE2RDtFQUM3RCxZQUFZO0FBQ2Q7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLFNBQVM7QUFDWDs7QUFFQTtFQUNFLGVBQWU7RUFDZixXQUFXO0VBQ1gsWUFBWTtFQUNaLFlBQVk7QUFDZDs7QUFFQTtFQUNFLGlCQUFpQjtFQUNqQixpQkFBaUI7RUFDakIsZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0UsU0FBUztFQUNULFlBQVk7RUFDWixpQkFBaUI7QUFDbkI7O0FBRUE7RUFDRSxtQkFBbUI7QUFDckI7O0FBRUE7RUFDRSxhQUFhO0VBQ2Isc0JBQXNCO0VBQ3RCLFNBQVM7RUFDVCxnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsOEJBQThCO0VBQzlCLG1CQUFtQjtFQUNuQixhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLGtCQUFrQjtFQUNsQiw4QkFBOEI7QUFDaEM7O0FBRUE7RUFDRSxpQkFBaUI7RUFDakIsV0FBVztFQUNYLGlCQUFpQjtFQUNqQixnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRSxTQUFTO0VBQ1QsV0FBVztFQUNYLGlCQUFpQjtBQUNuQjs7QUFFQTtFQUNFLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsU0FBUztBQUNYOztBQUVBO0VBQ0UsaUJBQWlCO0VBQ2pCLG1CQUFtQjtFQUNuQixnQkFBZ0I7RUFDaEIsaUJBQWlCO0VBQ2pCLGVBQWU7RUFDZixrQkFBa0I7QUFDcEI7O0FBRUE7RUFDRSxtQkFBbUI7RUFDbkIsY0FBYztBQUNoQjs7QUFFQTtFQUNFLG1CQUFtQjtFQUNuQixjQUFjO0FBQ2hCOztBQUVBO0VBQ0UsbUJBQW1CO0VBQ25CLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSxnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRSxrQkFBa0I7RUFDbEIsV0FBVztFQUNYLGlCQUFpQjtFQUNqQixnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsU0FBUztFQUNULGVBQWU7QUFDakI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLFFBQVE7RUFDUixrQkFBa0I7QUFDcEI7O0FBRUE7RUFDRSxlQUFlO0VBQ2YsV0FBVztFQUNYLFlBQVk7QUFDZDs7QUFFQTtFQUNFO0lBQ0UsYUFBYTtFQUNmOztFQUVBO0lBQ0Usc0JBQXNCO0lBQ3RCLHVCQUF1QjtJQUN2QixTQUFTO0VBQ1g7O0VBRUE7SUFDRSwwQkFBMEI7RUFDNUI7O0VBRUE7SUFDRSwwQkFBMEI7RUFDNUI7O0VBRUE7SUFDRSxzQkFBc0I7SUFDdEIsdUJBQXVCO0lBQ3ZCLFNBQVM7RUFDWDs7RUFFQTtJQUNFLHNCQUFzQjtFQUN4Qjs7RUFFQTtJQUNFLFdBQVc7SUFDWCx1QkFBdUI7RUFDekI7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIi5zdHVkZW50LWNsYXNzZXMtY29udGFpbmVyIHtcclxuICBwYWRkaW5nOiAyMHB4O1xyXG4gIG1heC13aWR0aDogMTIwMHB4O1xyXG4gIG1hcmdpbjogMCBhdXRvO1xyXG59XHJcblxyXG4ucGFnZS1oZWFkZXIge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgbWFyZ2luLWJvdHRvbTogMzBweDtcclxuICBwYWRkaW5nLWJvdHRvbTogMjBweDtcclxuICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2UwZTBlMDtcclxufVxyXG5cclxuLmhlYWRlci1jb250ZW50IGgxIHtcclxuICBtYXJnaW46IDA7XHJcbiAgY29sb3I6ICMzMzM7XHJcbiAgZm9udC1zaXplOiAycmVtO1xyXG4gIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbn1cclxuXHJcbi5oZWFkZXItY29udGVudCBwIHtcclxuICBtYXJnaW46IDVweCAwIDAgMDtcclxuICBjb2xvcjogIzY2NjtcclxuICBmb250LXNpemU6IDFyZW07XHJcbn1cclxuXHJcbi5sb2FkaW5nLWNvbnRhaW5lciwgLmVycm9yLWNvbnRhaW5lciB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgcGFkZGluZzogNjBweCAyMHB4O1xyXG4gIHRleHQtYWxpZ246IGNlbnRlcjtcclxufVxyXG5cclxuLmxvYWRpbmctY29udGFpbmVyIHAsIC5lcnJvci1jb250YWluZXIgcCB7XHJcbiAgbWFyZ2luLXRvcDogMjBweDtcclxuICBjb2xvcjogIzY2NjtcclxufVxyXG5cclxuLmVycm9yLWNvbnRhaW5lciBtYXQtaWNvbiB7XHJcbiAgZm9udC1zaXplOiA0OHB4O1xyXG4gIHdpZHRoOiA0OHB4O1xyXG4gIGhlaWdodDogNDhweDtcclxuICBjb2xvcjogI2Y0NDMzNjtcclxuICBtYXJnaW4tYm90dG9tOiAyMHB4O1xyXG59XHJcblxyXG4uY2xhc3Nlcy1jb250ZW50IHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgZ2FwOiAzMHB4O1xyXG59XHJcblxyXG4uY2xhc3MtaW5mby1jYXJkIHtcclxuICBtYXJnaW4tYm90dG9tOiAyMHB4O1xyXG59XHJcblxyXG4uY2xhc3MtZGV0YWlscyB7XHJcbiAgZGlzcGxheTogZ3JpZDtcclxuICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpdCwgbWlubWF4KDI1MHB4LCAxZnIpKTtcclxuICBnYXA6IDIwcHg7XHJcbiAgbWFyZ2luLXRvcDogMjBweDtcclxufVxyXG5cclxuLmRldGFpbC1pdGVtIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgZ2FwOiAxNXB4O1xyXG4gIHBhZGRpbmc6IDE1cHg7XHJcbiAgYmFja2dyb3VuZDogI2Y4ZjlmYTtcclxuICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbn1cclxuXHJcbi5kZXRhaWwtaXRlbSBtYXQtaWNvbiB7XHJcbiAgY29sb3I6ICMyMTk2ZjM7XHJcbiAgZm9udC1zaXplOiAyNHB4O1xyXG4gIHdpZHRoOiAyNHB4O1xyXG4gIGhlaWdodDogMjRweDtcclxufVxyXG5cclxuLmRldGFpbC1jb250ZW50IGg0IHtcclxuICBtYXJnaW46IDAgMCA1cHggMDtcclxuICBjb2xvcjogIzMzMztcclxuICBmb250LXNpemU6IDAuOXJlbTtcclxuICBmb250LXdlaWdodDogNTAwO1xyXG59XHJcblxyXG4uZGV0YWlsLWNvbnRlbnQgcCB7XHJcbiAgbWFyZ2luOiAwO1xyXG4gIGNvbG9yOiAjNjY2O1xyXG4gIGZvbnQtc2l6ZTogMXJlbTtcclxufVxyXG5cclxuLnN0YXRzLWdyaWQge1xyXG4gIGRpc3BsYXk6IGdyaWQ7XHJcbiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maXQsIG1pbm1heCgyMDBweCwgMWZyKSk7XHJcbiAgZ2FwOiAyMHB4O1xyXG4gIG1hcmdpbi1ib3R0b206IDIwcHg7XHJcbn1cclxuXHJcbi5zdGF0LWNhcmQge1xyXG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7XHJcbiAgY29sb3I6IHdoaXRlO1xyXG59XHJcblxyXG4uc3RhdC1jb250ZW50IHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgZ2FwOiAxNXB4O1xyXG59XHJcblxyXG4uc3RhdC1pY29uIG1hdC1pY29uIHtcclxuICBmb250LXNpemU6IDMycHg7XHJcbiAgd2lkdGg6IDMycHg7XHJcbiAgaGVpZ2h0OiAzMnB4O1xyXG4gIG9wYWNpdHk6IDAuODtcclxufVxyXG5cclxuLnN0YXQtaW5mbyBoMyB7XHJcbiAgbWFyZ2luOiAwIDAgNXB4IDA7XHJcbiAgZm9udC1zaXplOiAxLjhyZW07XHJcbiAgZm9udC13ZWlnaHQ6IDYwMDtcclxufVxyXG5cclxuLnN0YXQtaW5mbyBwIHtcclxuICBtYXJnaW46IDA7XHJcbiAgb3BhY2l0eTogMC45O1xyXG4gIGZvbnQtc2l6ZTogMC45cmVtO1xyXG59XHJcblxyXG4uc3ViamVjdHMtY2FyZCB7XHJcbiAgbWFyZ2luLWJvdHRvbTogMjBweDtcclxufVxyXG5cclxuLnN1YmplY3RzLWxpc3Qge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICBnYXA6IDE1cHg7XHJcbiAgbWFyZ2luLXRvcDogMjBweDtcclxufVxyXG5cclxuLnN1YmplY3QtaXRlbSB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBwYWRkaW5nOiAyMHB4O1xyXG4gIGJhY2tncm91bmQ6ICNmOGY5ZmE7XHJcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gIGJvcmRlci1sZWZ0OiA0cHggc29saWQgIzIxOTZmMztcclxufVxyXG5cclxuLnN1YmplY3QtaW5mbyBoNCB7XHJcbiAgbWFyZ2luOiAwIDAgNXB4IDA7XHJcbiAgY29sb3I6ICMzMzM7XHJcbiAgZm9udC1zaXplOiAxLjFyZW07XHJcbiAgZm9udC13ZWlnaHQ6IDUwMDtcclxufVxyXG5cclxuLnN1YmplY3QtaW5mbyBwIHtcclxuICBtYXJnaW46IDA7XHJcbiAgY29sb3I6ICM2NjY7XHJcbiAgZm9udC1zaXplOiAwLjlyZW07XHJcbn1cclxuXHJcbi5zdWJqZWN0LXN0YXRzIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgZ2FwOiAxNXB4O1xyXG59XHJcblxyXG4uYXR0ZW5kYW5jZS1wZXJjZW50YWdlIHtcclxuICBwYWRkaW5nOiA4cHggMTZweDtcclxuICBib3JkZXItcmFkaXVzOiAyMHB4O1xyXG4gIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgZm9udC1zaXplOiAwLjlyZW07XHJcbiAgbWluLXdpZHRoOiA2MHB4O1xyXG4gIHRleHQtYWxpZ246IGNlbnRlcjtcclxufVxyXG5cclxuLmF0dGVuZGFuY2UtcGVyY2VudGFnZS5nb29kIHtcclxuICBiYWNrZ3JvdW5kOiAjZThmNWU4O1xyXG4gIGNvbG9yOiAjMmU3ZDMyO1xyXG59XHJcblxyXG4uYXR0ZW5kYW5jZS1wZXJjZW50YWdlLndhcm5pbmcge1xyXG4gIGJhY2tncm91bmQ6ICNmZmYzZTA7XHJcbiAgY29sb3I6ICNmNTdjMDA7XHJcbn1cclxuXHJcbi5hdHRlbmRhbmNlLXBlcmNlbnRhZ2UuZGFuZ2VyIHtcclxuICBiYWNrZ3JvdW5kOiAjZmZlYmVlO1xyXG4gIGNvbG9yOiAjZDMyZjJmO1xyXG59XHJcblxyXG4ucXVpY2stYWN0aW9ucyB7XHJcbiAgbWFyZ2luLXRvcDogMzBweDtcclxufVxyXG5cclxuLnF1aWNrLWFjdGlvbnMgaDMge1xyXG4gIG1hcmdpbjogMCAwIDIwcHggMDtcclxuICBjb2xvcjogIzMzMztcclxuICBmb250LXNpemU6IDEuM3JlbTtcclxuICBmb250LXdlaWdodDogNTAwO1xyXG59XHJcblxyXG4uYWN0aW9uLWJ1dHRvbnMge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZ2FwOiAxNXB4O1xyXG4gIGZsZXgtd3JhcDogd3JhcDtcclxufVxyXG5cclxuLmFjdGlvbi1idXR0b25zIGJ1dHRvbiB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIGdhcDogOHB4O1xyXG4gIHBhZGRpbmc6IDEycHggMjRweDtcclxufVxyXG5cclxuLmFjdGlvbi1idXR0b25zIGJ1dHRvbiBtYXQtaWNvbiB7XHJcbiAgZm9udC1zaXplOiAyMHB4O1xyXG4gIHdpZHRoOiAyMHB4O1xyXG4gIGhlaWdodDogMjBweDtcclxufVxyXG5cclxuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XHJcbiAgLnN0dWRlbnQtY2xhc3Nlcy1jb250YWluZXIge1xyXG4gICAgcGFkZGluZzogMTVweDtcclxuICB9XHJcbiAgXHJcbiAgLnBhZ2UtaGVhZGVyIHtcclxuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDtcclxuICAgIGdhcDogMTVweDtcclxuICB9XHJcbiAgXHJcbiAgLmNsYXNzLWRldGFpbHMge1xyXG4gICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnI7XHJcbiAgfVxyXG4gIFxyXG4gIC5zdGF0cy1ncmlkIHtcclxuICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyO1xyXG4gIH1cclxuICBcclxuICAuc3ViamVjdC1pdGVtIHtcclxuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDtcclxuICAgIGdhcDogMTVweDtcclxuICB9XHJcbiAgXHJcbiAgLmFjdGlvbi1idXR0b25zIHtcclxuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgfVxyXG4gIFxyXG4gIC5hY3Rpb24tYnV0dG9ucyBidXR0b24ge1xyXG4gICAgd2lkdGg6IDEwMCU7XHJcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICB9XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "StudentClassesComponent_div_11_Template_button_click_7_listener", "ɵɵrestoreView", "_r4", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "refreshData", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "error", "subject_r7", "subjectName", "ɵɵtextInterpolate2", "present", "total", "ɵɵclassProp", "percentage", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "ɵɵtemplate", "StudentClassesComponent_div_12_mat_card_73_div_6_Template", "ɵɵproperty", "ctx_r5", "studentDashboard", "subjectAttendance", "StudentClassesComponent_div_12_mat_card_73_Template", "ctx_r2", "student", "class", "name", "department", "program", "semester", "length", "teachers", "classmates", "StudentClassesComponent", "constructor", "userService", "dashboardService", "loading", "ngOnInit", "currentUser", "getUserFromLocalStorage", "user", "loadStudentClasses", "getStudentDashboard", "_id", "subscribe", "next", "response", "success", "dashboard", "console", "_", "ɵɵdirectiveInject", "i1", "UserService", "i2", "DashboardService", "_2", "selectors", "decls", "vars", "consts", "template", "StudentClassesComponent_Template", "rf", "ctx", "StudentClassesComponent_Template_button_click_7_listener", "StudentClassesComponent_div_10_Template", "StudentClassesComponent_div_11_Template", "StudentClassesComponent_div_12_Template"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\student-dashboard\\student-classes\\student-classes.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\student-dashboard\\student-classes\\student-classes.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { UserService } from '../../../services/user.service';\r\nimport { DashboardService } from '../../../services/dashboard.service';\r\n\r\n@Component({\r\n  selector: 'app-student-classes',\r\n  templateUrl: './student-classes.component.html',\r\n  styleUrls: ['./student-classes.component.css']\r\n})\r\nexport class StudentClassesComponent implements OnInit {\r\n  currentUser: any;\r\n  studentDashboard: any;\r\n  loading = false;\r\n  error: string | null = null;\r\n\r\n  constructor(\r\n    private userService: UserService,\r\n    private dashboardService: DashboardService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.currentUser = this.userService.getUserFromLocalStorage()?.user;\r\n    this.loadStudentClasses();\r\n  }\r\n\r\n  loadStudentClasses(): void {\r\n    if (!this.currentUser) return;\r\n\r\n    this.loading = true;\r\n    this.error = null;\r\n\r\n    this.dashboardService.getStudentDashboard(this.currentUser._id).subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.studentDashboard = response.dashboard;\r\n        } else {\r\n          this.error = 'Failed to load student classes';\r\n        }\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading student classes:', error);\r\n        this.error = 'Error loading classes data';\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  refreshData(): void {\r\n    this.loadStudentClasses();\r\n  }\r\n}\r\n", "<div class=\"student-classes-container\">\r\n  <!-- Header -->\r\n  <div class=\"page-header\">\r\n    <div class=\"header-content\">\r\n      <h1>My Classes</h1>\r\n      <p>View your enrolled classes and class information</p>\r\n    </div>\r\n    <button mat-icon-button (click)=\"refreshData()\" matTooltip=\"Refresh Data\">\r\n      <mat-icon>refresh</mat-icon>\r\n    </button>\r\n  </div>\r\n\r\n  <!-- Loading State -->\r\n  <div *ngIf=\"loading\" class=\"loading-container\">\r\n    <mat-spinner></mat-spinner>\r\n    <p>Loading your classes...</p>\r\n  </div>\r\n\r\n  <!-- Error State -->\r\n  <div *ngIf=\"error && !loading\" class=\"error-container\">\r\n    <mat-icon>error</mat-icon>\r\n    <h3>Error Loading Classes</h3>\r\n    <p>{{ error }}</p>\r\n    <button mat-raised-button color=\"primary\" (click)=\"refreshData()\">\r\n      <mat-icon>refresh</mat-icon>\r\n      Try Again\r\n    </button>\r\n  </div>\r\n\r\n  <!-- Content -->\r\n  <div *ngIf=\"studentDashboard && !loading && !error\" class=\"classes-content\">\r\n    <!-- Current Class Information -->\r\n    <mat-card class=\"class-info-card\">\r\n      <mat-card-header>\r\n        <mat-card-title>Current Class Information</mat-card-title>\r\n      </mat-card-header>\r\n      <mat-card-content>\r\n        <div class=\"class-details\">\r\n          <div class=\"detail-item\">\r\n            <mat-icon>class</mat-icon>\r\n            <div class=\"detail-content\">\r\n              <h4>Class</h4>\r\n              <p>{{ studentDashboard.student.class?.name || 'Not Assigned' }}</p>\r\n            </div>\r\n          </div>\r\n          <div class=\"detail-item\">\r\n            <mat-icon>business</mat-icon>\r\n            <div class=\"detail-content\">\r\n              <h4>Department</h4>\r\n              <p>{{ studentDashboard.student.department?.name || 'Not Assigned' }}</p>\r\n            </div>\r\n          </div>\r\n          <div class=\"detail-item\">\r\n            <mat-icon>library_books</mat-icon>\r\n            <div class=\"detail-content\">\r\n              <h4>Program</h4>\r\n              <p>{{ studentDashboard.student.program?.name || 'Not Assigned' }}</p>\r\n            </div>\r\n          </div>\r\n          <div class=\"detail-item\">\r\n            <mat-icon>school</mat-icon>\r\n            <div class=\"detail-content\">\r\n              <h4>Semester</h4>\r\n              <p>{{ studentDashboard.student.semester || 'Not Assigned' }}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n\r\n    <!-- Class Statistics -->\r\n    <div class=\"stats-grid\">\r\n      <mat-card class=\"stat-card\">\r\n        <mat-card-content>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon\">\r\n              <mat-icon>subject</mat-icon>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ studentDashboard.subjectAttendance?.length || 0 }}</h3>\r\n              <p>Total Subjects</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n\r\n      <mat-card class=\"stat-card\">\r\n        <mat-card-content>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon\">\r\n              <mat-icon>person</mat-icon>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ studentDashboard.teachers?.length || 0 }}</h3>\r\n              <p>Teachers</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n\r\n      <mat-card class=\"stat-card\">\r\n        <mat-card-content>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon\">\r\n              <mat-icon>group</mat-icon>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ studentDashboard.classmates?.length || 0 }}</h3>\r\n              <p>Classmates</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n    </div>\r\n\r\n    <!-- Subjects List -->\r\n    <mat-card class=\"subjects-card\" *ngIf=\"studentDashboard.subjectAttendance?.length > 0\">\r\n      <mat-card-header>\r\n        <mat-card-title>My Subjects</mat-card-title>\r\n      </mat-card-header>\r\n      <mat-card-content>\r\n        <div class=\"subjects-list\">\r\n          <div *ngFor=\"let subject of studentDashboard.subjectAttendance\" class=\"subject-item\">\r\n            <div class=\"subject-info\">\r\n              <h4>{{ subject.subjectName }}</h4>\r\n              <p>{{ subject.present }}/{{ subject.total }} classes attended</p>\r\n            </div>\r\n            <div class=\"subject-stats\">\r\n              <div class=\"attendance-percentage\" \r\n                   [class.good]=\"subject.percentage >= 75\"\r\n                   [class.warning]=\"subject.percentage >= 60 && subject.percentage < 75\"\r\n                   [class.danger]=\"subject.percentage < 60\">\r\n                {{ subject.percentage | number:'1.0-0' }}%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n\r\n    <!-- Quick Actions -->\r\n    <div class=\"quick-actions\">\r\n      <h3>Quick Actions</h3>\r\n      <div class=\"action-buttons\">\r\n        <button mat-raised-button color=\"primary\" routerLink=\"/dashboard/student/attendance\">\r\n          <mat-icon>fact_check</mat-icon>\r\n          View Attendance\r\n        </button>\r\n        <button mat-raised-button color=\"accent\" routerLink=\"/dashboard/student/teachers\">\r\n          <mat-icon>person</mat-icon>\r\n          View Teachers\r\n        </button>\r\n        <button mat-raised-button color=\"primary\" routerLink=\"/dashboard/student/subjects\">\r\n          <mat-icon>subject</mat-icon>\r\n          View Subjects\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": ";;;;;;;;;;;;ICaEA,EAAA,CAAAC,cAAA,aAA+C;IAC7CD,EAAA,CAAAE,SAAA,kBAA2B;IAC3BF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,8BAAuB;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;;IAIhCJ,EAAA,CAAAC,cAAA,aAAuD;IAC3CD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC1BJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,4BAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC9BJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAW;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAClBJ,EAAA,CAAAC,cAAA,gBAAkE;IAAxBD,EAAA,CAAAK,UAAA,mBAAAC,gEAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAC/DZ,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC5BJ,EAAA,CAAAG,MAAA,mBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAJNJ,EAAA,CAAAa,SAAA,GAAW;IAAXb,EAAA,CAAAc,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IAoGRhB,EAAA,CAAAC,cAAA,cAAqF;IAE7ED,EAAA,CAAAG,MAAA,GAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAClCJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAA0D;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAEnEJ,EAAA,CAAAC,cAAA,cAA2B;IAKvBD,EAAA,CAAAG,MAAA,GACF;;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IATFJ,EAAA,CAAAa,SAAA,GAAyB;IAAzBb,EAAA,CAAAc,iBAAA,CAAAG,UAAA,CAAAC,WAAA,CAAyB;IAC1BlB,EAAA,CAAAa,SAAA,GAA0D;IAA1Db,EAAA,CAAAmB,kBAAA,KAAAF,UAAA,CAAAG,OAAA,OAAAH,UAAA,CAAAI,KAAA,sBAA0D;IAIxDrB,EAAA,CAAAa,SAAA,GAAuC;IAAvCb,EAAA,CAAAsB,WAAA,SAAAL,UAAA,CAAAM,UAAA,OAAuC,YAAAN,UAAA,CAAAM,UAAA,UAAAN,UAAA,CAAAM,UAAA,iBAAAN,UAAA,CAAAM,UAAA;IAG1CvB,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAwB,kBAAA,MAAAxB,EAAA,CAAAyB,WAAA,QAAAR,UAAA,CAAAM,UAAA,iBACF;;;;;IAjBVvB,EAAA,CAAAC,cAAA,mBAAuF;IAEnED,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IAE9CJ,EAAA,CAAAC,cAAA,uBAAkB;IAEdD,EAAA,CAAA0B,UAAA,IAAAC,yDAAA,oBAaM;IACR3B,EAAA,CAAAI,YAAA,EAAM;;;;IAdqBJ,EAAA,CAAAa,SAAA,GAAqC;IAArCb,EAAA,CAAA4B,UAAA,YAAAC,MAAA,CAAAC,gBAAA,CAAAC,iBAAA,CAAqC;;;;;IA5FtE/B,EAAA,CAAAC,cAAA,cAA4E;IAItDD,EAAA,CAAAG,MAAA,gCAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IAE5DJ,EAAA,CAAAC,cAAA,uBAAkB;IAGFD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC1BJ,EAAA,CAAAC,cAAA,eAA4B;IACtBD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACdJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,IAA4D;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAGvEJ,EAAA,CAAAC,cAAA,eAAyB;IACbD,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC7BJ,EAAA,CAAAC,cAAA,eAA4B;IACtBD,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACnBJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,IAAiE;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAG5EJ,EAAA,CAAAC,cAAA,eAAyB;IACbD,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAClCJ,EAAA,CAAAC,cAAA,eAA4B;IACtBD,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAChBJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,IAA8D;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAGzEJ,EAAA,CAAAC,cAAA,eAAyB;IACbD,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC3BJ,EAAA,CAAAC,cAAA,eAA4B;IACtBD,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACjBJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,IAAyD;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAQ1EJ,EAAA,CAAAC,cAAA,eAAwB;IAKJD,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAE9BJ,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAG,MAAA,IAAqD;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC9DJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,sBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAM7BJ,EAAA,CAAAC,cAAA,oBAA4B;IAIVD,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAE7BJ,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAG,MAAA,IAA4C;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACrDJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAMvBJ,EAAA,CAAAC,cAAA,oBAA4B;IAIVD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAE5BJ,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAG,MAAA,IAA8C;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACvDJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAQ3BJ,EAAA,CAAA0B,UAAA,KAAAM,mDAAA,uBAsBW;IAGXhC,EAAA,CAAAC,cAAA,eAA2B;IACrBD,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACtBJ,EAAA,CAAAC,cAAA,eAA4B;IAEdD,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC/BJ,EAAA,CAAAG,MAAA,yBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAAkF;IACtED,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC3BJ,EAAA,CAAAG,MAAA,uBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAAmF;IACvED,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC5BJ,EAAA,CAAAG,MAAA,uBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAjHAJ,EAAA,CAAAa,SAAA,IAA4D;IAA5Db,EAAA,CAAAc,iBAAA,EAAAmB,MAAA,CAAAH,gBAAA,CAAAI,OAAA,CAAAC,KAAA,kBAAAF,MAAA,CAAAH,gBAAA,CAAAI,OAAA,CAAAC,KAAA,CAAAC,IAAA,oBAA4D;IAO5DpC,EAAA,CAAAa,SAAA,GAAiE;IAAjEb,EAAA,CAAAc,iBAAA,EAAAmB,MAAA,CAAAH,gBAAA,CAAAI,OAAA,CAAAG,UAAA,kBAAAJ,MAAA,CAAAH,gBAAA,CAAAI,OAAA,CAAAG,UAAA,CAAAD,IAAA,oBAAiE;IAOjEpC,EAAA,CAAAa,SAAA,GAA8D;IAA9Db,EAAA,CAAAc,iBAAA,EAAAmB,MAAA,CAAAH,gBAAA,CAAAI,OAAA,CAAAI,OAAA,kBAAAL,MAAA,CAAAH,gBAAA,CAAAI,OAAA,CAAAI,OAAA,CAAAF,IAAA,oBAA8D;IAO9DpC,EAAA,CAAAa,SAAA,GAAyD;IAAzDb,EAAA,CAAAc,iBAAA,CAAAmB,MAAA,CAAAH,gBAAA,CAAAI,OAAA,CAAAK,QAAA,mBAAyD;IAgBxDvC,EAAA,CAAAa,SAAA,IAAqD;IAArDb,EAAA,CAAAc,iBAAA,EAAAmB,MAAA,CAAAH,gBAAA,CAAAC,iBAAA,kBAAAE,MAAA,CAAAH,gBAAA,CAAAC,iBAAA,CAAAS,MAAA,OAAqD;IAcrDxC,EAAA,CAAAa,SAAA,IAA4C;IAA5Cb,EAAA,CAAAc,iBAAA,EAAAmB,MAAA,CAAAH,gBAAA,CAAAW,QAAA,kBAAAR,MAAA,CAAAH,gBAAA,CAAAW,QAAA,CAAAD,MAAA,OAA4C;IAc5CxC,EAAA,CAAAa,SAAA,IAA8C;IAA9Cb,EAAA,CAAAc,iBAAA,EAAAmB,MAAA,CAAAH,gBAAA,CAAAY,UAAA,kBAAAT,MAAA,CAAAH,gBAAA,CAAAY,UAAA,CAAAF,MAAA,OAA8C;IAS3BxC,EAAA,CAAAa,SAAA,GAAoD;IAApDb,EAAA,CAAA4B,UAAA,UAAAK,MAAA,CAAAH,gBAAA,CAAAC,iBAAA,kBAAAE,MAAA,CAAAH,gBAAA,CAAAC,iBAAA,CAAAS,MAAA,MAAoD;;;AD3GzF,OAAM,MAAOG,uBAAuB;EAMlCC,YACUC,WAAwB,EACxBC,gBAAkC;IADlC,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAL1B,KAAAC,OAAO,GAAG,KAAK;IACf,KAAA/B,KAAK,GAAkB,IAAI;EAKxB;EAEHgC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,GAAG,IAAI,CAACJ,WAAW,CAACK,uBAAuB,EAAE,EAAEC,IAAI;IACnE,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEAA,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACH,WAAW,EAAE;IAEvB,IAAI,CAACF,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC/B,KAAK,GAAG,IAAI;IAEjB,IAAI,CAAC8B,gBAAgB,CAACO,mBAAmB,CAAC,IAAI,CAACJ,WAAW,CAACK,GAAG,CAAC,CAACC,SAAS,CAAC;MACxEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAAC5B,gBAAgB,GAAG2B,QAAQ,CAACE,SAAS;SAC3C,MAAM;UACL,IAAI,CAAC3C,KAAK,GAAG,gCAAgC;;QAE/C,IAAI,CAAC+B,OAAO,GAAG,KAAK;MACtB,CAAC;MACD/B,KAAK,EAAGA,KAAK,IAAI;QACf4C,OAAO,CAAC5C,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAACA,KAAK,GAAG,4BAA4B;QACzC,IAAI,CAAC+B,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAnC,WAAWA,CAAA;IACT,IAAI,CAACwC,kBAAkB,EAAE;EAC3B;EAAC,QAAAS,CAAA,G;qBAzCUlB,uBAAuB,EAAA3C,EAAA,CAAA8D,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAhE,EAAA,CAAA8D,iBAAA,CAAAG,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAvBxB,uBAAuB;IAAAyB,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCTpC1E,EAAA,CAAAC,cAAA,aAAuC;QAI7BD,EAAA,CAAAG,MAAA,iBAAU;QAAAH,EAAA,CAAAI,YAAA,EAAK;QACnBJ,EAAA,CAAAC,cAAA,QAAG;QAAAD,EAAA,CAAAG,MAAA,uDAAgD;QAAAH,EAAA,CAAAI,YAAA,EAAI;QAEzDJ,EAAA,CAAAC,cAAA,gBAA0E;QAAlDD,EAAA,CAAAK,UAAA,mBAAAuE,yDAAA;UAAA,OAASD,GAAA,CAAA/D,WAAA,EAAa;QAAA,EAAC;QAC7CZ,EAAA,CAAAC,cAAA,eAAU;QAAAD,EAAA,CAAAG,MAAA,cAAO;QAAAH,EAAA,CAAAI,YAAA,EAAW;QAKhCJ,EAAA,CAAA0B,UAAA,KAAAmD,uCAAA,iBAGM;QAGN7E,EAAA,CAAA0B,UAAA,KAAAoD,uCAAA,kBAQM;QAGN9E,EAAA,CAAA0B,UAAA,KAAAqD,uCAAA,kBAgIM;QACR/E,EAAA,CAAAI,YAAA,EAAM;;;QAlJEJ,EAAA,CAAAa,SAAA,IAAa;QAAbb,EAAA,CAAA4B,UAAA,SAAA+C,GAAA,CAAA5B,OAAA,CAAa;QAMb/C,EAAA,CAAAa,SAAA,GAAuB;QAAvBb,EAAA,CAAA4B,UAAA,SAAA+C,GAAA,CAAA3D,KAAA,KAAA2D,GAAA,CAAA5B,OAAA,CAAuB;QAWvB/C,EAAA,CAAAa,SAAA,GAA4C;QAA5Cb,EAAA,CAAA4B,UAAA,SAAA+C,GAAA,CAAA7C,gBAAA,KAAA6C,GAAA,CAAA5B,OAAA,KAAA4B,GAAA,CAAA3D,KAAA,CAA4C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}