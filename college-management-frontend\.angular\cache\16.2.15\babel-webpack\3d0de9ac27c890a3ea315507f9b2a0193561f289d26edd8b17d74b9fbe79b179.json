{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, catchError, tap } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nconst USER_KEY = 'user'; // Key for storing the user in localStorage\nconst TOKEN_KEY = 'token'; // Key for storing the auth token\nexport class UserService {\n  constructor(http) {\n    this.http = http;\n    this.userSubject = new BehaviorSubject(this.getUserFromLocalStorage());\n    this.user$ = this.userSubject.asObservable(); // Public observable for the current user\n    this.apiUrl = environment.apiUrl;\n    this.userRegUrl = `${this.apiUrl}/signup`;\n    this.userLogUrl = `${this.apiUrl}/login`;\n    this.forgotPassUrl = `${this.apiUrl}/forgot-password`;\n    this.getAllUsersByRoleUrl = `${this.apiUrl}/api/users`;\n    this.allUsersUrl = `${this.apiUrl}/allUser`;\n    this.bulkfileuploadUrl = `${this.apiUrl}/bulk-upload`;\n  }\n  // Register a new user\n  register(userRegister) {\n    return this.http.post(this.userRegUrl, userRegister).pipe(tap({\n      next: user => {\n        this.setUserInLocalStorage(user); // Optionally store the user on successful registration\n      },\n\n      error: errorResponse => {\n        throw errorResponse;\n      }\n    }));\n  }\n  uploaduser(userRegister) {\n    return this.http.post(this.bulkfileuploadUrl, userRegister).pipe(tap({\n      next: user => {\n        this.setUserInLocalStorage(user);\n      },\n      error: errorResponse => {\n        throw errorResponse;\n      }\n    }));\n  }\n  // Login a user\n  login(userLogin) {\n    return this.http.post(this.userLogUrl, userLogin).pipe(tap({\n      next: user => {\n        this.setUserInLocalStorage(user);\n        this.userSubject.next(user);\n      }\n    }), catchError(error => {\n      throw error;\n    }));\n  }\n  // Forgot password\n  forgotPassword(email) {\n    return this.http.post(this.forgotPassUrl, email).pipe(tap({\n      next: response => {\n        console.log('Password reset email sent successfully');\n      }\n    }), catchError(error => {\n      throw error;\n    }));\n  }\n  // Fetch users by role\n  getUsersByRole(role) {\n    return this.http.get(`${this.getAllUsersByRoleUrl}/${role}`).pipe(tap({\n      next: response => {\n        console.log('Fetched users by role:', role);\n      }\n    }), catchError(error => {\n      throw error;\n    }));\n  }\n  // Fetch all users\n  getAllUsers() {\n    return this.http.get(this.allUsersUrl).pipe(tap({\n      next: users => {\n        console.log('Fetched all users:', users);\n      }\n    }), catchError(error => {\n      throw error;\n    }));\n  }\n  // Logout the user\n  logout() {\n    localStorage.removeItem(USER_KEY);\n    localStorage.removeItem(TOKEN_KEY);\n    this.userSubject.next(null);\n  }\n  // Store user in local storage\n  setUserInLocalStorage(user) {\n    localStorage.setItem(USER_KEY, JSON.stringify(user));\n    if (user.token) {\n      localStorage.setItem(TOKEN_KEY, user.token); // Store JWT token if available\n    }\n  }\n  // Get the user from local storage\n  getUserFromLocalStorage() {\n    const userJson = localStorage.getItem(USER_KEY);\n    if (userJson) return JSON.parse(userJson);\n    return null;\n  }\n  // Get the JWT token from local storage\n  getToken() {\n    return localStorage.getItem(TOKEN_KEY);\n  }\n  // Check if the user is authenticated\n  isAuthenticated() {\n    return !!this.getToken();\n  }\n  // Helper function to add token in headers\n  getAuthHeaders() {\n    const token = this.getToken();\n    return new HttpHeaders().set('Authorization', `Bearer ${token}`);\n  }\n  static #_ = this.ɵfac = function UserService_Factory(t) {\n    return new (t || UserService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: UserService,\n    factory: UserService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "catchError", "tap", "environment", "USER_KEY", "TOKEN_KEY", "UserService", "constructor", "http", "userSubject", "getUserFromLocalStorage", "user$", "asObservable", "apiUrl", "userRegUrl", "userLogUrl", "forgotPassUrl", "getAllUsersByRoleUrl", "allUsersUrl", "bulkfileuploadUrl", "register", "userRegister", "post", "pipe", "next", "user", "setUserInLocalStorage", "error", "errorResponse", "uploaduser", "login", "userLogin", "forgotPassword", "email", "response", "console", "log", "getUsersByRole", "role", "get", "getAllUsers", "users", "logout", "localStorage", "removeItem", "setItem", "JSON", "stringify", "token", "userJson", "getItem", "parse", "getToken", "isAuthenticated", "getAuthHeaders", "set", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\services\\user.service.ts"], "sourcesContent": ["import { HttpClient, HttpHeaders } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, catchError, Observable, tap } from 'rxjs';\r\nimport { environment } from 'src/environments/environment';\r\nimport { User } from '../models/user';\r\n\r\nconst USER_KEY = 'user'; // Key for storing the user in localStorage\r\nconst TOKEN_KEY = 'token'; // Key for storing the auth token\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class UserService {\r\n  private userSubject = new BehaviorSubject<any>(this.getUserFromLocalStorage());\r\n  public user$ = this.userSubject.asObservable(); // Public observable for the current user\r\n\r\n  apiUrl = environment.apiUrl;\r\n  userRegUrl = `${this.apiUrl}/signup`;\r\n  userLogUrl = `${this.apiUrl}/login`;\r\n  forgotPassUrl = `${this.apiUrl}/forgot-password`;\r\n  getAllUsersByRoleUrl = `${this.apiUrl}/api/users`;\r\n  allUsersUrl = `${this.apiUrl}/allUser`;\r\n  bulkfileuploadUrl = `${this.apiUrl}/bulk-upload`\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  // Register a new user\r\n  register(userRegister: any): Observable<any> {\r\n    return this.http.post<any>(this.userRegUrl, userRegister).pipe(\r\n      tap({\r\n        next: (user) => {\r\n          this.setUserInLocalStorage(user); // Optionally store the user on successful registration\r\n        },\r\n        error: (errorResponse) => {\r\n          throw errorResponse;\r\n        }\r\n      })\r\n    );\r\n  }\r\n  uploaduser(userRegister: any): Observable<any> {\r\n    return this.http.post<any>(this.bulkfileuploadUrl, userRegister).pipe(\r\n      tap({\r\n        next: (user) => {\r\n          this.setUserInLocalStorage(user); \r\n        },\r\n        error: (errorResponse) => {\r\n          throw errorResponse;\r\n        }\r\n      })\r\n    );\r\n  }\r\n  \r\n  // Login a user\r\n  login(userLogin: any): Observable<any> {\r\n    return this.http.post<any>(this.userLogUrl, userLogin).pipe(\r\n      tap({\r\n        next: (user) => {\r\n          this.setUserInLocalStorage(user);\r\n          this.userSubject.next(user);\r\n        },\r\n      }),\r\n      catchError((error) => {\r\n        throw error;\r\n      })\r\n    );\r\n  }\r\n\r\n  // Forgot password\r\n  forgotPassword(email: any): Observable<any> {\r\n    return this.http.post<any>(this.forgotPassUrl, email).pipe(\r\n      tap({\r\n        next: (response) => {\r\n          console.log('Password reset email sent successfully');\r\n        },\r\n      }),\r\n      catchError((error) => {\r\n        throw error;\r\n      })\r\n    );\r\n  }\r\n\r\n  // Fetch users by role\r\n  getUsersByRole(role: string): Observable<any> {\r\n    return this.http.get<any>(`${this.getAllUsersByRoleUrl}/${role}`).pipe(\r\n      tap({\r\n        next: (response) => {\r\n          console.log('Fetched users by role:', role);\r\n        },\r\n      }),\r\n      catchError((error) => {\r\n        throw error;\r\n      })\r\n    );\r\n  }\r\n\r\n  // Fetch all users\r\n  getAllUsers(): Observable<any> {\r\n    return this.http.get<any>(this.allUsersUrl).pipe(\r\n      tap({\r\n        next: (users) => {\r\n          console.log('Fetched all users:', users);\r\n        },\r\n      }),\r\n      catchError((error) => {\r\n        throw error;\r\n      })\r\n    );\r\n  }\r\n\r\n  // Logout the user\r\n  logout() {\r\n    localStorage.removeItem(USER_KEY);\r\n    localStorage.removeItem(TOKEN_KEY);\r\n    this.userSubject.next(null);\r\n  }\r\n\r\n  // Store user in local storage\r\n  private setUserInLocalStorage(user: any) {\r\n    localStorage.setItem(USER_KEY, JSON.stringify(user));\r\n    if (user.token) {\r\n      localStorage.setItem(TOKEN_KEY, user.token); // Store JWT token if available\r\n    }\r\n  }\r\n\r\n  // Get the user from local storage\r\n  public getUserFromLocalStorage(): any {\r\n    const userJson = localStorage.getItem(USER_KEY);\r\n    if (userJson) return JSON.parse(userJson) as User;\r\n    return null;\r\n  }\r\n\r\n  // Get the JWT token from local storage\r\n  getToken(): string | null {\r\n    return localStorage.getItem(TOKEN_KEY);\r\n  }\r\n\r\n  // Check if the user is authenticated\r\n  isAuthenticated(): boolean {\r\n    return !!this.getToken();\r\n  }\r\n\r\n  // Helper function to add token in headers\r\n  private getAuthHeaders() {\r\n    const token = this.getToken();\r\n    return new HttpHeaders().set('Authorization', `Bearer ${token}`);\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,WAAW,QAAQ,sBAAsB;AAE9D,SAASC,eAAe,EAAEC,UAAU,EAAcC,GAAG,QAAQ,MAAM;AACnE,SAASC,WAAW,QAAQ,8BAA8B;;;AAG1D,MAAMC,QAAQ,GAAG,MAAM,CAAC,CAAC;AACzB,MAAMC,SAAS,GAAG,OAAO,CAAC,CAAC;AAK3B,OAAM,MAAOC,WAAW;EAYtBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAXhB,KAAAC,WAAW,GAAG,IAAIT,eAAe,CAAM,IAAI,CAACU,uBAAuB,EAAE,CAAC;IACvE,KAAAC,KAAK,GAAG,IAAI,CAACF,WAAW,CAACG,YAAY,EAAE,CAAC,CAAC;IAEhD,KAAAC,MAAM,GAAGV,WAAW,CAACU,MAAM;IAC3B,KAAAC,UAAU,GAAG,GAAG,IAAI,CAACD,MAAM,SAAS;IACpC,KAAAE,UAAU,GAAG,GAAG,IAAI,CAACF,MAAM,QAAQ;IACnC,KAAAG,aAAa,GAAG,GAAG,IAAI,CAACH,MAAM,kBAAkB;IAChD,KAAAI,oBAAoB,GAAG,GAAG,IAAI,CAACJ,MAAM,YAAY;IACjD,KAAAK,WAAW,GAAG,GAAG,IAAI,CAACL,MAAM,UAAU;IACtC,KAAAM,iBAAiB,GAAG,GAAG,IAAI,CAACN,MAAM,cAAc;EAET;EAEvC;EACAO,QAAQA,CAACC,YAAiB;IACxB,OAAO,IAAI,CAACb,IAAI,CAACc,IAAI,CAAM,IAAI,CAACR,UAAU,EAAEO,YAAY,CAAC,CAACE,IAAI,CAC5DrB,GAAG,CAAC;MACFsB,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAACC,qBAAqB,CAACD,IAAI,CAAC,CAAC,CAAC;MACpC,CAAC;;MACDE,KAAK,EAAGC,aAAa,IAAI;QACvB,MAAMA,aAAa;MACrB;KACD,CAAC,CACH;EACH;EACAC,UAAUA,CAACR,YAAiB;IAC1B,OAAO,IAAI,CAACb,IAAI,CAACc,IAAI,CAAM,IAAI,CAACH,iBAAiB,EAAEE,YAAY,CAAC,CAACE,IAAI,CACnErB,GAAG,CAAC;MACFsB,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAACC,qBAAqB,CAACD,IAAI,CAAC;MAClC,CAAC;MACDE,KAAK,EAAGC,aAAa,IAAI;QACvB,MAAMA,aAAa;MACrB;KACD,CAAC,CACH;EACH;EAEA;EACAE,KAAKA,CAACC,SAAc;IAClB,OAAO,IAAI,CAACvB,IAAI,CAACc,IAAI,CAAM,IAAI,CAACP,UAAU,EAAEgB,SAAS,CAAC,CAACR,IAAI,CACzDrB,GAAG,CAAC;MACFsB,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAACC,qBAAqB,CAACD,IAAI,CAAC;QAChC,IAAI,CAAChB,WAAW,CAACe,IAAI,CAACC,IAAI,CAAC;MAC7B;KACD,CAAC,EACFxB,UAAU,CAAE0B,KAAK,IAAI;MACnB,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACH;EAEA;EACAK,cAAcA,CAACC,KAAU;IACvB,OAAO,IAAI,CAACzB,IAAI,CAACc,IAAI,CAAM,IAAI,CAACN,aAAa,EAAEiB,KAAK,CAAC,CAACV,IAAI,CACxDrB,GAAG,CAAC;MACFsB,IAAI,EAAGU,QAAQ,IAAI;QACjBC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACvD;KACD,CAAC,EACFnC,UAAU,CAAE0B,KAAK,IAAI;MACnB,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACH;EAEA;EACAU,cAAcA,CAACC,IAAY;IACzB,OAAO,IAAI,CAAC9B,IAAI,CAAC+B,GAAG,CAAM,GAAG,IAAI,CAACtB,oBAAoB,IAAIqB,IAAI,EAAE,CAAC,CAACf,IAAI,CACpErB,GAAG,CAAC;MACFsB,IAAI,EAAGU,QAAQ,IAAI;QACjBC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEE,IAAI,CAAC;MAC7C;KACD,CAAC,EACFrC,UAAU,CAAE0B,KAAK,IAAI;MACnB,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACH;EAEA;EACAa,WAAWA,CAAA;IACT,OAAO,IAAI,CAAChC,IAAI,CAAC+B,GAAG,CAAM,IAAI,CAACrB,WAAW,CAAC,CAACK,IAAI,CAC9CrB,GAAG,CAAC;MACFsB,IAAI,EAAGiB,KAAK,IAAI;QACdN,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEK,KAAK,CAAC;MAC1C;KACD,CAAC,EACFxC,UAAU,CAAE0B,KAAK,IAAI;MACnB,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACH;EAEA;EACAe,MAAMA,CAAA;IACJC,YAAY,CAACC,UAAU,CAACxC,QAAQ,CAAC;IACjCuC,YAAY,CAACC,UAAU,CAACvC,SAAS,CAAC;IAClC,IAAI,CAACI,WAAW,CAACe,IAAI,CAAC,IAAI,CAAC;EAC7B;EAEA;EACQE,qBAAqBA,CAACD,IAAS;IACrCkB,YAAY,CAACE,OAAO,CAACzC,QAAQ,EAAE0C,IAAI,CAACC,SAAS,CAACtB,IAAI,CAAC,CAAC;IACpD,IAAIA,IAAI,CAACuB,KAAK,EAAE;MACdL,YAAY,CAACE,OAAO,CAACxC,SAAS,EAAEoB,IAAI,CAACuB,KAAK,CAAC,CAAC,CAAC;;EAEjD;EAEA;EACOtC,uBAAuBA,CAAA;IAC5B,MAAMuC,QAAQ,GAAGN,YAAY,CAACO,OAAO,CAAC9C,QAAQ,CAAC;IAC/C,IAAI6C,QAAQ,EAAE,OAAOH,IAAI,CAACK,KAAK,CAACF,QAAQ,CAAS;IACjD,OAAO,IAAI;EACb;EAEA;EACAG,QAAQA,CAAA;IACN,OAAOT,YAAY,CAACO,OAAO,CAAC7C,SAAS,CAAC;EACxC;EAEA;EACAgD,eAAeA,CAAA;IACb,OAAO,CAAC,CAAC,IAAI,CAACD,QAAQ,EAAE;EAC1B;EAEA;EACQE,cAAcA,CAAA;IACpB,MAAMN,KAAK,GAAG,IAAI,CAACI,QAAQ,EAAE;IAC7B,OAAO,IAAIrD,WAAW,EAAE,CAACwD,GAAG,CAAC,eAAe,EAAE,UAAUP,KAAK,EAAE,CAAC;EAClE;EAAC,QAAAQ,CAAA,G;qBArIUlD,WAAW,EAAAmD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAXvD,WAAW;IAAAwD,OAAA,EAAXxD,WAAW,CAAAyD,IAAA;IAAAC,UAAA,EAFV;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}