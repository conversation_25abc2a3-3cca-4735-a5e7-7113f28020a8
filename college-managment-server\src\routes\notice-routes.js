const express = require('express');
const router = express.Router();

const {
    createNotice,
    getAllNotices,
    getNoticeById,
    updateNotice,
    markAsRead,
    getNoticesForUser,
    deleteNotice
} = require('../controllers/notice-controller');

// Routes

// Create a new notice
router.post('/notices', createNotice);

// Get all notices with filtering and pagination
router.get('/notices', getAllNotices);

// Get notice by ID
router.get('/notices/:id', getNoticeById);

// Update notice
router.put('/notices/:id', updateNotice);

// Mark notice as read
router.post('/notices/:id/read', markAsRead);

// Get notices for specific user
router.get('/notices/user/:userId', getNoticesForUser);

// Delete notice (soft delete)
router.delete('/notices/:id', deleteNotice);

module.exports = router;
