{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { TeachertableComponent } from './teachertable/teachertable.component';\nimport { TeacherViewComponent } from './teacher-view/teacher-view.component';\nimport { TeacherFormComponent } from './teacher-form/teacher-form.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: TeachertableComponent\n}, {\n  path: 'view/:id',\n  component: TeacherViewComponent\n}, {\n  path: 'add-teacher',\n  component: TeacherFormComponent\n}, {\n  path: 'edit/:id',\n  component: TeacherFormComponent\n}];\nexport class TeacherRoutingModule {\n  static #_ = this.ɵfac = function TeacherRoutingModule_Factory(t) {\n    return new (t || TeacherRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: TeacherRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild(routes), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(TeacherRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "TeachertableComponent", "TeacherViewComponent", "TeacherFormComponent", "routes", "path", "component", "TeacherRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\teacher\\teacher-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { TeachertableComponent } from './teachertable/teachertable.component';\r\nimport { TeacherViewComponent } from './teacher-view/teacher-view.component';\r\nimport { TeacherFormComponent } from './teacher-form/teacher-form.component';\r\n\r\nconst routes: Routes = [\r\n  {path:'', component:TeachertableComponent},\r\n  {path:'view/:id', component:TeacherViewComponent},\r\n  {path:'add-teacher', component:TeacherFormComponent},\r\n  {path:'edit/:id', component:TeacherFormComponent}\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class TeacherRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,oBAAoB,QAAQ,uCAAuC;;;AAE5E,MAAMC,MAAM,GAAW,CACrB;EAACC,IAAI,EAAC,EAAE;EAAEC,SAAS,EAACL;AAAqB,CAAC,EAC1C;EAACI,IAAI,EAAC,UAAU;EAAEC,SAAS,EAACJ;AAAoB,CAAC,EACjD;EAACG,IAAI,EAAC,aAAa;EAAEC,SAAS,EAACH;AAAoB,CAAC,EACpD;EAACE,IAAI,EAAC,UAAU;EAAEC,SAAS,EAACH;AAAoB,CAAC,CAClD;AAMD,OAAM,MAAOI,oBAAoB;EAAA,QAAAC,CAAA,G;qBAApBD,oBAAoB;EAAA;EAAA,QAAAE,EAAA,G;UAApBF;EAAoB;EAAA,QAAAG,EAAA,G;cAHrBV,YAAY,CAACW,QAAQ,CAACP,MAAM,CAAC,EAC7BJ,YAAY;EAAA;;;2EAEXO,oBAAoB;IAAAK,OAAA,GAAAC,EAAA,CAAAb,YAAA;IAAAc,OAAA,GAFrBd,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}