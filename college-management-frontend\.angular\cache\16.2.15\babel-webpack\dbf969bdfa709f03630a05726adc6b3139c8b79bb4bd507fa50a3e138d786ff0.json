{"ast": null, "code": "import * as i1 from '@angular/cdk/bidi';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { Platform } from '@angular/cdk/platform';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, ViewChild, inject, Optional, ViewChildren, ContentChild, ContentChildren, forwardRef, EventEmitter, Directive, Output, NgModule } from '@angular/core';\nimport * as i2$1 from '@angular/material/core';\nimport { MatRipple, mixinColor, mixinDisableRipple, MAT_RIPPLE_GLOBAL_OPTIONS, MatCommonModule, MatRippleModule } from '@angular/material/core';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { Subject } from 'rxjs';\n\n/**\n * Injection token that can be used for a `MatSlider` to provide itself as a\n * parent to the `MatSliderThumb` and `MatSliderRangeThumb`.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nconst _c0 = [\"knob\"];\nconst _c1 = [\"valueIndicatorContainer\"];\nfunction MatSliderVisualThumb_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4, 5)(2, \"div\", 6)(3, \"span\", 7);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.valueIndicatorText);\n  }\n}\nconst _c2 = [\"trackActive\"];\nfunction MatSlider_div_6_ng_container_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\");\n  }\n  if (rf & 2) {\n    const tickMark_r6 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(tickMark_r6 === 0 ? \"mdc-slider__tick-mark--active\" : \"mdc-slider__tick-mark--inactive\");\n    i0.ɵɵstyleProp(\"transform\", ctx_r5._calcTickMarkTransform(i_r7));\n  }\n}\nfunction MatSlider_div_6_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MatSlider_div_6_ng_container_2_div_1_Template, 1, 4, \"div\", 11);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4._tickMarks);\n  }\n}\nfunction MatSlider_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8, 9);\n    i0.ɵɵtemplate(2, MatSlider_div_6_ng_container_2_Template, 2, 1, \"ng-container\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1._cachedWidth);\n  }\n}\nfunction MatSlider_mat_slider_visual_thumb_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-slider-visual-thumb\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"discrete\", ctx_r2.discrete)(\"thumbPosition\", 1)(\"valueIndicatorText\", ctx_r2.startValueIndicatorText);\n  }\n}\nconst _c3 = [\"*\"];\nconst MAT_SLIDER = /*#__PURE__*/new InjectionToken('_MatSlider');\n/**\n * Injection token that can be used to query for a `MatSliderThumb`.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nconst MAT_SLIDER_THUMB = /*#__PURE__*/new InjectionToken('_MatSliderThumb');\n/**\n * Injection token that can be used to query for a `MatSliderRangeThumb`.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nconst MAT_SLIDER_RANGE_THUMB = /*#__PURE__*/new InjectionToken('_MatSliderRangeThumb');\n/**\n * Injection token that can be used to query for a `MatSliderVisualThumb`.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nconst MAT_SLIDER_VISUAL_THUMB = /*#__PURE__*/new InjectionToken('_MatSliderVisualThumb');\n/**\n * A simple change event emitted by the MatSlider component.\n * @deprecated Use event bindings directly on the MatSliderThumbs for `change` and `input` events. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\nclass MatSliderChange {}\n\n/**\n * The visual slider thumb.\n *\n * Handles the slider thumb ripple states (hover, focus, and active),\n * and displaying the value tooltip on discrete sliders.\n * @docs-private\n */\nlet MatSliderVisualThumb = /*#__PURE__*/(() => {\n  class MatSliderVisualThumb {\n    constructor(_cdr, _ngZone, _elementRef, _slider) {\n      this._cdr = _cdr;\n      this._ngZone = _ngZone;\n      this._slider = _slider;\n      /** Whether the slider thumb is currently being hovered. */\n      this._isHovered = false;\n      /** Whether the slider thumb is currently being pressed. */\n      this._isActive = false;\n      /** Whether the value indicator tooltip is visible. */\n      this._isValueIndicatorVisible = false;\n      this._onPointerMove = event => {\n        if (this._sliderInput._isFocused) {\n          return;\n        }\n        const rect = this._hostElement.getBoundingClientRect();\n        const isHovered = this._slider._isCursorOnSliderThumb(event, rect);\n        this._isHovered = isHovered;\n        if (isHovered) {\n          this._showHoverRipple();\n        } else {\n          this._hideRipple(this._hoverRippleRef);\n        }\n      };\n      this._onMouseLeave = () => {\n        this._isHovered = false;\n        this._hideRipple(this._hoverRippleRef);\n      };\n      this._onFocus = () => {\n        // We don't want to show the hover ripple on top of the focus ripple.\n        // Happen when the users cursor is over a thumb and then the user tabs to it.\n        this._hideRipple(this._hoverRippleRef);\n        this._showFocusRipple();\n        this._hostElement.classList.add('mdc-slider__thumb--focused');\n      };\n      this._onBlur = () => {\n        // Happens when the user tabs away while still dragging a thumb.\n        if (!this._isActive) {\n          this._hideRipple(this._focusRippleRef);\n        }\n        // Happens when the user tabs away from a thumb but their cursor is still over it.\n        if (this._isHovered) {\n          this._showHoverRipple();\n        }\n        this._hostElement.classList.remove('mdc-slider__thumb--focused');\n      };\n      this._onDragStart = event => {\n        if (event.button !== 0) {\n          return;\n        }\n        this._isActive = true;\n        this._showActiveRipple();\n      };\n      this._onDragEnd = () => {\n        this._isActive = false;\n        this._hideRipple(this._activeRippleRef);\n        // Happens when the user starts dragging a thumb, tabs away, and then stops dragging.\n        if (!this._sliderInput._isFocused) {\n          this._hideRipple(this._focusRippleRef);\n        }\n      };\n      this._hostElement = _elementRef.nativeElement;\n    }\n    ngAfterViewInit() {\n      this._ripple.radius = 24;\n      this._sliderInput = this._slider._getInput(this.thumbPosition);\n      this._sliderInputEl = this._sliderInput._hostElement;\n      const input = this._sliderInputEl;\n      // These listeners don't update any data bindings so we bind them outside\n      // of the NgZone to prevent Angular from needlessly running change detection.\n      this._ngZone.runOutsideAngular(() => {\n        input.addEventListener('pointermove', this._onPointerMove);\n        input.addEventListener('pointerdown', this._onDragStart);\n        input.addEventListener('pointerup', this._onDragEnd);\n        input.addEventListener('pointerleave', this._onMouseLeave);\n        input.addEventListener('focus', this._onFocus);\n        input.addEventListener('blur', this._onBlur);\n      });\n    }\n    ngOnDestroy() {\n      const input = this._sliderInputEl;\n      input.removeEventListener('pointermove', this._onPointerMove);\n      input.removeEventListener('pointerdown', this._onDragStart);\n      input.removeEventListener('pointerup', this._onDragEnd);\n      input.removeEventListener('pointerleave', this._onMouseLeave);\n      input.removeEventListener('focus', this._onFocus);\n      input.removeEventListener('blur', this._onBlur);\n    }\n    /** Handles displaying the hover ripple. */\n    _showHoverRipple() {\n      if (!this._isShowingRipple(this._hoverRippleRef)) {\n        this._hoverRippleRef = this._showRipple({\n          enterDuration: 0,\n          exitDuration: 0\n        });\n        this._hoverRippleRef?.element.classList.add('mat-mdc-slider-hover-ripple');\n      }\n    }\n    /** Handles displaying the focus ripple. */\n    _showFocusRipple() {\n      // Show the focus ripple event if noop animations are enabled.\n      if (!this._isShowingRipple(this._focusRippleRef)) {\n        this._focusRippleRef = this._showRipple({\n          enterDuration: 0,\n          exitDuration: 0\n        }, true);\n        this._focusRippleRef?.element.classList.add('mat-mdc-slider-focus-ripple');\n      }\n    }\n    /** Handles displaying the active ripple. */\n    _showActiveRipple() {\n      if (!this._isShowingRipple(this._activeRippleRef)) {\n        this._activeRippleRef = this._showRipple({\n          enterDuration: 225,\n          exitDuration: 400\n        });\n        this._activeRippleRef?.element.classList.add('mat-mdc-slider-active-ripple');\n      }\n    }\n    /** Whether the given rippleRef is currently fading in or visible. */\n    _isShowingRipple(rippleRef) {\n      return rippleRef?.state === 0 /* RippleState.FADING_IN */ || rippleRef?.state === 1 /* RippleState.VISIBLE */;\n    }\n    /** Manually launches the slider thumb ripple using the specified ripple animation config. */\n    _showRipple(animation, ignoreGlobalRippleConfig) {\n      if (this._slider.disabled) {\n        return;\n      }\n      this._showValueIndicator();\n      if (this._slider._isRange) {\n        const sibling = this._slider._getThumb(this.thumbPosition === 1 /* _MatThumb.START */ ? 2 /* _MatThumb.END */ : 1 /* _MatThumb.START */);\n        sibling._showValueIndicator();\n      }\n      if (this._slider._globalRippleOptions?.disabled && !ignoreGlobalRippleConfig) {\n        return;\n      }\n      return this._ripple.launch({\n        animation: this._slider._noopAnimations ? {\n          enterDuration: 0,\n          exitDuration: 0\n        } : animation,\n        centered: true,\n        persistent: true\n      });\n    }\n    /**\n     * Fades out the given ripple.\n     * Also hides the value indicator if no ripple is showing.\n     */\n    _hideRipple(rippleRef) {\n      rippleRef?.fadeOut();\n      if (this._isShowingAnyRipple()) {\n        return;\n      }\n      if (!this._slider._isRange) {\n        this._hideValueIndicator();\n      }\n      const sibling = this._getSibling();\n      if (!sibling._isShowingAnyRipple()) {\n        this._hideValueIndicator();\n        sibling._hideValueIndicator();\n      }\n    }\n    /** Shows the value indicator ui. */\n    _showValueIndicator() {\n      this._hostElement.classList.add('mdc-slider__thumb--with-indicator');\n    }\n    /** Hides the value indicator ui. */\n    _hideValueIndicator() {\n      this._hostElement.classList.remove('mdc-slider__thumb--with-indicator');\n    }\n    _getSibling() {\n      return this._slider._getThumb(this.thumbPosition === 1 /* _MatThumb.START */ ? 2 /* _MatThumb.END */ : 1 /* _MatThumb.START */);\n    }\n    /** Gets the value indicator container's native HTML element. */\n    _getValueIndicatorContainer() {\n      return this._valueIndicatorContainer?.nativeElement;\n    }\n    /** Gets the native HTML element of the slider thumb knob. */\n    _getKnob() {\n      return this._knob.nativeElement;\n    }\n    _isShowingAnyRipple() {\n      return this._isShowingRipple(this._hoverRippleRef) || this._isShowingRipple(this._focusRippleRef) || this._isShowingRipple(this._activeRippleRef);\n    }\n    static #_ = this.ɵfac = function MatSliderVisualThumb_Factory(t) {\n      return new (t || MatSliderVisualThumb)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MAT_SLIDER));\n    };\n    static #_2 = this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatSliderVisualThumb,\n      selectors: [[\"mat-slider-visual-thumb\"]],\n      viewQuery: function MatSliderVisualThumb_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(MatRipple, 5);\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._ripple = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._knob = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._valueIndicatorContainer = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mdc-slider__thumb\", \"mat-mdc-slider-visual-thumb\"],\n      inputs: {\n        discrete: \"discrete\",\n        thumbPosition: \"thumbPosition\",\n        valueIndicatorText: \"valueIndicatorText\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_SLIDER_VISUAL_THUMB,\n        useExisting: MatSliderVisualThumb\n      }])],\n      decls: 4,\n      vars: 2,\n      consts: [[\"class\", \"mdc-slider__value-indicator-container\", 4, \"ngIf\"], [1, \"mdc-slider__thumb-knob\"], [\"knob\", \"\"], [\"matRipple\", \"\", 1, \"mat-mdc-focus-indicator\", 3, \"matRippleDisabled\"], [1, \"mdc-slider__value-indicator-container\"], [\"valueIndicatorContainer\", \"\"], [1, \"mdc-slider__value-indicator\"], [1, \"mdc-slider__value-indicator-text\"]],\n      template: function MatSliderVisualThumb_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, MatSliderVisualThumb_div_0_Template, 5, 1, \"div\", 0);\n          i0.ɵɵelement(1, \"div\", 1, 2)(3, \"div\", 3);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.discrete);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"matRippleDisabled\", true);\n        }\n      },\n      dependencies: [i2.NgIf, i2$1.MatRipple],\n      styles: [\".mat-mdc-slider-visual-thumb .mat-ripple{height:100%;width:100%}.mat-mdc-slider .mdc-slider__tick-marks{justify-content:start}.mat-mdc-slider .mdc-slider__tick-marks .mdc-slider__tick-mark--active,.mat-mdc-slider .mdc-slider__tick-marks .mdc-slider__tick-mark--inactive{position:absolute;left:2px}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatSliderVisualThumb;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n// TODO(wagnermaciel): maybe handle the following edge case:\n// 1. start dragging discrete slider\n// 2. tab to disable checkbox\n// 3. without ending drag, disable the slider\n// Boilerplate for applying mixins to MatSlider.\nconst _MatSliderMixinBase = /*#__PURE__*/mixinColor( /*#__PURE__*/mixinDisableRipple(class {\n  constructor(_elementRef) {\n    this._elementRef = _elementRef;\n  }\n}), 'primary');\n/**\n * Allows users to select from a range of values by moving the slider thumb. It is similar in\n * behavior to the native `<input type=\"range\">` element.\n */\nlet MatSlider = /*#__PURE__*/(() => {\n  class MatSlider extends _MatSliderMixinBase {\n    /** Whether the slider is disabled. */\n    get disabled() {\n      return this._disabled;\n    }\n    set disabled(v) {\n      this._disabled = coerceBooleanProperty(v);\n      const endInput = this._getInput(2 /* _MatThumb.END */);\n      const startInput = this._getInput(1 /* _MatThumb.START */);\n      if (endInput) {\n        endInput.disabled = this._disabled;\n      }\n      if (startInput) {\n        startInput.disabled = this._disabled;\n      }\n    }\n    /** Whether the slider displays a numeric value label upon pressing the thumb. */\n    get discrete() {\n      return this._discrete;\n    }\n    set discrete(v) {\n      this._discrete = coerceBooleanProperty(v);\n      this._updateValueIndicatorUIs();\n    }\n    /** Whether the slider displays tick marks along the slider track. */\n    get showTickMarks() {\n      return this._showTickMarks;\n    }\n    set showTickMarks(v) {\n      this._showTickMarks = coerceBooleanProperty(v);\n    }\n    /** The minimum value that the slider can have. */\n    get min() {\n      return this._min;\n    }\n    set min(v) {\n      const min = coerceNumberProperty(v, this._min);\n      if (this._min !== min) {\n        this._updateMin(min);\n      }\n    }\n    _updateMin(min) {\n      const prevMin = this._min;\n      this._min = min;\n      this._isRange ? this._updateMinRange({\n        old: prevMin,\n        new: min\n      }) : this._updateMinNonRange(min);\n      this._onMinMaxOrStepChange();\n    }\n    _updateMinRange(min) {\n      const endInput = this._getInput(2 /* _MatThumb.END */);\n      const startInput = this._getInput(1 /* _MatThumb.START */);\n      const oldEndValue = endInput.value;\n      const oldStartValue = startInput.value;\n      startInput.min = min.new;\n      endInput.min = Math.max(min.new, startInput.value);\n      startInput.max = Math.min(endInput.max, endInput.value);\n      startInput._updateWidthInactive();\n      endInput._updateWidthInactive();\n      min.new < min.old ? this._onTranslateXChangeBySideEffect(endInput, startInput) : this._onTranslateXChangeBySideEffect(startInput, endInput);\n      if (oldEndValue !== endInput.value) {\n        this._onValueChange(endInput);\n      }\n      if (oldStartValue !== startInput.value) {\n        this._onValueChange(startInput);\n      }\n    }\n    _updateMinNonRange(min) {\n      const input = this._getInput(2 /* _MatThumb.END */);\n      if (input) {\n        const oldValue = input.value;\n        input.min = min;\n        input._updateThumbUIByValue();\n        this._updateTrackUI(input);\n        if (oldValue !== input.value) {\n          this._onValueChange(input);\n        }\n      }\n    }\n    /** The maximum value that the slider can have. */\n    get max() {\n      return this._max;\n    }\n    set max(v) {\n      const max = coerceNumberProperty(v, this._max);\n      if (this._max !== max) {\n        this._updateMax(max);\n      }\n    }\n    _updateMax(max) {\n      const prevMax = this._max;\n      this._max = max;\n      this._isRange ? this._updateMaxRange({\n        old: prevMax,\n        new: max\n      }) : this._updateMaxNonRange(max);\n      this._onMinMaxOrStepChange();\n    }\n    _updateMaxRange(max) {\n      const endInput = this._getInput(2 /* _MatThumb.END */);\n      const startInput = this._getInput(1 /* _MatThumb.START */);\n      const oldEndValue = endInput.value;\n      const oldStartValue = startInput.value;\n      endInput.max = max.new;\n      startInput.max = Math.min(max.new, endInput.value);\n      endInput.min = startInput.value;\n      endInput._updateWidthInactive();\n      startInput._updateWidthInactive();\n      max.new > max.old ? this._onTranslateXChangeBySideEffect(startInput, endInput) : this._onTranslateXChangeBySideEffect(endInput, startInput);\n      if (oldEndValue !== endInput.value) {\n        this._onValueChange(endInput);\n      }\n      if (oldStartValue !== startInput.value) {\n        this._onValueChange(startInput);\n      }\n    }\n    _updateMaxNonRange(max) {\n      const input = this._getInput(2 /* _MatThumb.END */);\n      if (input) {\n        const oldValue = input.value;\n        input.max = max;\n        input._updateThumbUIByValue();\n        this._updateTrackUI(input);\n        if (oldValue !== input.value) {\n          this._onValueChange(input);\n        }\n      }\n    }\n    /** The values at which the thumb will snap. */\n    get step() {\n      return this._step;\n    }\n    set step(v) {\n      const step = coerceNumberProperty(v, this._step);\n      if (this._step !== step) {\n        this._updateStep(step);\n      }\n    }\n    _updateStep(step) {\n      this._step = step;\n      this._isRange ? this._updateStepRange() : this._updateStepNonRange();\n      this._onMinMaxOrStepChange();\n    }\n    _updateStepRange() {\n      const endInput = this._getInput(2 /* _MatThumb.END */);\n      const startInput = this._getInput(1 /* _MatThumb.START */);\n      const oldEndValue = endInput.value;\n      const oldStartValue = startInput.value;\n      const prevStartValue = startInput.value;\n      endInput.min = this._min;\n      startInput.max = this._max;\n      endInput.step = this._step;\n      startInput.step = this._step;\n      if (this._platform.SAFARI) {\n        endInput.value = endInput.value;\n        startInput.value = startInput.value;\n      }\n      endInput.min = Math.max(this._min, startInput.value);\n      startInput.max = Math.min(this._max, endInput.value);\n      startInput._updateWidthInactive();\n      endInput._updateWidthInactive();\n      endInput.value < prevStartValue ? this._onTranslateXChangeBySideEffect(startInput, endInput) : this._onTranslateXChangeBySideEffect(endInput, startInput);\n      if (oldEndValue !== endInput.value) {\n        this._onValueChange(endInput);\n      }\n      if (oldStartValue !== startInput.value) {\n        this._onValueChange(startInput);\n      }\n    }\n    _updateStepNonRange() {\n      const input = this._getInput(2 /* _MatThumb.END */);\n      if (input) {\n        const oldValue = input.value;\n        input.step = this._step;\n        if (this._platform.SAFARI) {\n          input.value = input.value;\n        }\n        input._updateThumbUIByValue();\n        if (oldValue !== input.value) {\n          this._onValueChange(input);\n        }\n      }\n    }\n    constructor(_ngZone, _cdr, elementRef, _dir, _globalRippleOptions, animationMode) {\n      super(elementRef);\n      this._ngZone = _ngZone;\n      this._cdr = _cdr;\n      this._dir = _dir;\n      this._globalRippleOptions = _globalRippleOptions;\n      this._disabled = false;\n      this._discrete = false;\n      this._showTickMarks = false;\n      this._min = 0;\n      this._max = 100;\n      this._step = 1;\n      /**\n       * Function that will be used to format the value before it is displayed\n       * in the thumb label. Can be used to format very large number in order\n       * for them to fit into the slider thumb.\n       */\n      this.displayWith = value => `${value}`;\n      this._rippleRadius = 24;\n      // The value indicator tooltip text for the visual slider thumb(s).\n      /** @docs-private */\n      this.startValueIndicatorText = '';\n      /** @docs-private */\n      this.endValueIndicatorText = '';\n      this._isRange = false;\n      /** Whether the slider is rtl. */\n      this._isRtl = false;\n      this._hasViewInitialized = false;\n      /**\n       * The width of the tick mark track.\n       * The tick mark track width is different from full track width\n       */\n      this._tickMarkTrackWidth = 0;\n      this._hasAnimation = false;\n      this._resizeTimer = null;\n      this._platform = inject(Platform);\n      /** The radius of the native slider's knob. AFAIK there is no way to avoid hardcoding this. */\n      this._knobRadius = 8;\n      /** Whether or not the slider thumbs overlap. */\n      this._thumbsOverlap = false;\n      this._noopAnimations = animationMode === 'NoopAnimations';\n      this._dirChangeSubscription = this._dir.change.subscribe(() => this._onDirChange());\n      this._isRtl = this._dir.value === 'rtl';\n    }\n    ngAfterViewInit() {\n      if (this._platform.isBrowser) {\n        this._updateDimensions();\n      }\n      const eInput = this._getInput(2 /* _MatThumb.END */);\n      const sInput = this._getInput(1 /* _MatThumb.START */);\n      this._isRange = !!eInput && !!sInput;\n      this._cdr.detectChanges();\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        _validateInputs(this._isRange, this._getInput(2 /* _MatThumb.END */), this._getInput(1 /* _MatThumb.START */));\n      }\n\n      const thumb = this._getThumb(2 /* _MatThumb.END */);\n      this._rippleRadius = thumb._ripple.radius;\n      this._inputPadding = this._rippleRadius - this._knobRadius;\n      this._inputOffset = this._knobRadius;\n      this._isRange ? this._initUIRange(eInput, sInput) : this._initUINonRange(eInput);\n      this._updateTrackUI(eInput);\n      this._updateTickMarkUI();\n      this._updateTickMarkTrackUI();\n      this._observeHostResize();\n      this._cdr.detectChanges();\n    }\n    _initUINonRange(eInput) {\n      eInput.initProps();\n      eInput.initUI();\n      this._updateValueIndicatorUI(eInput);\n      this._hasViewInitialized = true;\n      eInput._updateThumbUIByValue();\n    }\n    _initUIRange(eInput, sInput) {\n      eInput.initProps();\n      eInput.initUI();\n      sInput.initProps();\n      sInput.initUI();\n      eInput._updateMinMax();\n      sInput._updateMinMax();\n      eInput._updateStaticStyles();\n      sInput._updateStaticStyles();\n      this._updateValueIndicatorUIs();\n      this._hasViewInitialized = true;\n      eInput._updateThumbUIByValue();\n      sInput._updateThumbUIByValue();\n    }\n    ngOnDestroy() {\n      this._dirChangeSubscription.unsubscribe();\n      this._resizeObserver?.disconnect();\n      this._resizeObserver = null;\n    }\n    /** Handles updating the slider ui after a dir change. */\n    _onDirChange() {\n      this._isRtl = this._dir.value === 'rtl';\n      this._isRange ? this._onDirChangeRange() : this._onDirChangeNonRange();\n      this._updateTickMarkUI();\n    }\n    _onDirChangeRange() {\n      const endInput = this._getInput(2 /* _MatThumb.END */);\n      const startInput = this._getInput(1 /* _MatThumb.START */);\n      endInput._setIsLeftThumb();\n      startInput._setIsLeftThumb();\n      endInput.translateX = endInput._calcTranslateXByValue();\n      startInput.translateX = startInput._calcTranslateXByValue();\n      endInput._updateStaticStyles();\n      startInput._updateStaticStyles();\n      endInput._updateWidthInactive();\n      startInput._updateWidthInactive();\n      endInput._updateThumbUIByValue();\n      startInput._updateThumbUIByValue();\n    }\n    _onDirChangeNonRange() {\n      const input = this._getInput(2 /* _MatThumb.END */);\n      input._updateThumbUIByValue();\n    }\n    /** Starts observing and updating the slider if the host changes its size. */\n    _observeHostResize() {\n      if (typeof ResizeObserver === 'undefined' || !ResizeObserver) {\n        return;\n      }\n      this._ngZone.runOutsideAngular(() => {\n        this._resizeObserver = new ResizeObserver(() => {\n          if (this._isActive()) {\n            return;\n          }\n          if (this._resizeTimer) {\n            clearTimeout(this._resizeTimer);\n          }\n          this._onResize();\n        });\n        this._resizeObserver.observe(this._elementRef.nativeElement);\n      });\n    }\n    /** Whether any of the thumbs are currently active. */\n    _isActive() {\n      return this._getThumb(1 /* _MatThumb.START */)._isActive || this._getThumb(2 /* _MatThumb.END */)._isActive;\n    }\n    _getValue(thumbPosition = 2 /* _MatThumb.END */) {\n      const input = this._getInput(thumbPosition);\n      if (!input) {\n        return this.min;\n      }\n      return input.value;\n    }\n    _skipUpdate() {\n      return !!(this._getInput(1 /* _MatThumb.START */)?._skipUIUpdate || this._getInput(2 /* _MatThumb.END */)?._skipUIUpdate);\n    }\n    /** Stores the slider dimensions. */\n    _updateDimensions() {\n      this._cachedWidth = this._elementRef.nativeElement.offsetWidth;\n      this._cachedLeft = this._elementRef.nativeElement.getBoundingClientRect().left;\n    }\n    /** Sets the styles for the active portion of the track. */\n    _setTrackActiveStyles(styles) {\n      const trackStyle = this._trackActive.nativeElement.style;\n      trackStyle.left = styles.left;\n      trackStyle.right = styles.right;\n      trackStyle.transformOrigin = styles.transformOrigin;\n      trackStyle.transform = styles.transform;\n    }\n    /** Returns the translateX positioning for a tick mark based on it's index. */\n    _calcTickMarkTransform(index) {\n      // TODO(wagnermaciel): See if we can avoid doing this and just using flex to position these.\n      const translateX = index * (this._tickMarkTrackWidth / (this._tickMarks.length - 1));\n      return `translateX(${translateX}px`;\n    }\n    // Handlers for updating the slider ui.\n    _onTranslateXChange(source) {\n      if (!this._hasViewInitialized) {\n        return;\n      }\n      this._updateThumbUI(source);\n      this._updateTrackUI(source);\n      this._updateOverlappingThumbUI(source);\n    }\n    _onTranslateXChangeBySideEffect(input1, input2) {\n      if (!this._hasViewInitialized) {\n        return;\n      }\n      input1._updateThumbUIByValue();\n      input2._updateThumbUIByValue();\n    }\n    _onValueChange(source) {\n      if (!this._hasViewInitialized) {\n        return;\n      }\n      this._updateValueIndicatorUI(source);\n      this._updateTickMarkUI();\n      this._cdr.detectChanges();\n    }\n    _onMinMaxOrStepChange() {\n      if (!this._hasViewInitialized) {\n        return;\n      }\n      this._updateTickMarkUI();\n      this._updateTickMarkTrackUI();\n      this._cdr.markForCheck();\n    }\n    _onResize() {\n      if (!this._hasViewInitialized) {\n        return;\n      }\n      this._updateDimensions();\n      if (this._isRange) {\n        const eInput = this._getInput(2 /* _MatThumb.END */);\n        const sInput = this._getInput(1 /* _MatThumb.START */);\n        eInput._updateThumbUIByValue();\n        sInput._updateThumbUIByValue();\n        eInput._updateStaticStyles();\n        sInput._updateStaticStyles();\n        eInput._updateMinMax();\n        sInput._updateMinMax();\n        eInput._updateWidthInactive();\n        sInput._updateWidthInactive();\n      } else {\n        const eInput = this._getInput(2 /* _MatThumb.END */);\n        if (eInput) {\n          eInput._updateThumbUIByValue();\n        }\n      }\n      this._updateTickMarkUI();\n      this._updateTickMarkTrackUI();\n      this._cdr.detectChanges();\n    }\n    /** Returns true if the slider knobs are overlapping one another. */\n    _areThumbsOverlapping() {\n      const startInput = this._getInput(1 /* _MatThumb.START */);\n      const endInput = this._getInput(2 /* _MatThumb.END */);\n      if (!startInput || !endInput) {\n        return false;\n      }\n      return endInput.translateX - startInput.translateX < 20;\n    }\n    /**\n     * Updates the class names of overlapping slider thumbs so\n     * that the current active thumb is styled to be on \"top\".\n     */\n    _updateOverlappingThumbClassNames(source) {\n      const sibling = source.getSibling();\n      const sourceThumb = this._getThumb(source.thumbPosition);\n      const siblingThumb = this._getThumb(sibling.thumbPosition);\n      siblingThumb._hostElement.classList.remove('mdc-slider__thumb--top');\n      sourceThumb._hostElement.classList.toggle('mdc-slider__thumb--top', this._thumbsOverlap);\n    }\n    /** Updates the UI of slider thumbs when they begin or stop overlapping. */\n    _updateOverlappingThumbUI(source) {\n      if (!this._isRange || this._skipUpdate()) {\n        return;\n      }\n      if (this._thumbsOverlap !== this._areThumbsOverlapping()) {\n        this._thumbsOverlap = !this._thumbsOverlap;\n        this._updateOverlappingThumbClassNames(source);\n      }\n    }\n    // _MatThumb styles update conditions\n    //\n    // 1. TranslateX, resize, or dir change\n    //    - Reason: The thumb styles need to be updated according to the new translateX.\n    // 2. Min, max, or step\n    //    - Reason: The value may have silently changed.\n    /** Updates the translateX of the given thumb. */\n    _updateThumbUI(source) {\n      if (this._skipUpdate()) {\n        return;\n      }\n      const thumb = this._getThumb(source.thumbPosition === 2 /* _MatThumb.END */ ? 2 /* _MatThumb.END */ : 1 /* _MatThumb.START */);\n      thumb._hostElement.style.transform = `translateX(${source.translateX}px)`;\n    }\n    // Value indicator text update conditions\n    //\n    // 1. Value\n    //    - Reason: The value displayed needs to be updated.\n    // 2. Min, max, or step\n    //    - Reason: The value may have silently changed.\n    /** Updates the value indicator tooltip ui for the given thumb. */\n    _updateValueIndicatorUI(source) {\n      if (this._skipUpdate()) {\n        return;\n      }\n      const valuetext = this.displayWith(source.value);\n      this._hasViewInitialized ? source._valuetext = valuetext : source._hostElement.setAttribute('aria-valuetext', valuetext);\n      if (this.discrete) {\n        source.thumbPosition === 1 /* _MatThumb.START */ ? this.startValueIndicatorText = valuetext : this.endValueIndicatorText = valuetext;\n        const visualThumb = this._getThumb(source.thumbPosition);\n        valuetext.length < 3 ? visualThumb._hostElement.classList.add('mdc-slider__thumb--short-value') : visualThumb._hostElement.classList.remove('mdc-slider__thumb--short-value');\n      }\n    }\n    /** Updates all value indicator UIs in the slider. */\n    _updateValueIndicatorUIs() {\n      const eInput = this._getInput(2 /* _MatThumb.END */);\n      const sInput = this._getInput(1 /* _MatThumb.START */);\n      if (eInput) {\n        this._updateValueIndicatorUI(eInput);\n      }\n      if (sInput) {\n        this._updateValueIndicatorUI(sInput);\n      }\n    }\n    // Update Tick Mark Track Width\n    //\n    // 1. Min, max, or step\n    //    - Reason: The maximum reachable value may have changed.\n    //    - Side note: The maximum reachable value is different from the maximum value set by the\n    //      user. For example, a slider with [min: 5, max: 100, step: 10] would have a maximum\n    //      reachable value of 95.\n    // 2. Resize\n    //    - Reason: The position for the maximum reachable value needs to be recalculated.\n    /** Updates the width of the tick mark track. */\n    _updateTickMarkTrackUI() {\n      if (!this.showTickMarks || this._skipUpdate()) {\n        return;\n      }\n      const step = this._step && this._step > 0 ? this._step : 1;\n      const maxValue = Math.floor(this.max / step) * step;\n      const percentage = (maxValue - this.min) / (this.max - this.min);\n      this._tickMarkTrackWidth = this._cachedWidth * percentage - 6;\n    }\n    // Track active update conditions\n    //\n    // 1. TranslateX\n    //    - Reason: The track active should line up with the new thumb position.\n    // 2. Min or max\n    //    - Reason #1: The 'active' percentage needs to be recalculated.\n    //    - Reason #2: The value may have silently changed.\n    // 3. Step\n    //    - Reason: The value may have silently changed causing the thumb(s) to shift.\n    // 4. Dir change\n    //    - Reason: The track active will need to be updated according to the new thumb position(s).\n    // 5. Resize\n    //    - Reason: The total width the 'active' tracks translateX is based on has changed.\n    /** Updates the scale on the active portion of the track. */\n    _updateTrackUI(source) {\n      if (this._skipUpdate()) {\n        return;\n      }\n      this._isRange ? this._updateTrackUIRange(source) : this._updateTrackUINonRange(source);\n    }\n    _updateTrackUIRange(source) {\n      const sibling = source.getSibling();\n      if (!sibling || !this._cachedWidth) {\n        return;\n      }\n      const activePercentage = Math.abs(sibling.translateX - source.translateX) / this._cachedWidth;\n      if (source._isLeftThumb && this._cachedWidth) {\n        this._setTrackActiveStyles({\n          left: 'auto',\n          right: `${this._cachedWidth - sibling.translateX}px`,\n          transformOrigin: 'right',\n          transform: `scaleX(${activePercentage})`\n        });\n      } else {\n        this._setTrackActiveStyles({\n          left: `${sibling.translateX}px`,\n          right: 'auto',\n          transformOrigin: 'left',\n          transform: `scaleX(${activePercentage})`\n        });\n      }\n    }\n    _updateTrackUINonRange(source) {\n      this._isRtl ? this._setTrackActiveStyles({\n        left: 'auto',\n        right: '0px',\n        transformOrigin: 'right',\n        transform: `scaleX(${1 - source.fillPercentage})`\n      }) : this._setTrackActiveStyles({\n        left: '0px',\n        right: 'auto',\n        transformOrigin: 'left',\n        transform: `scaleX(${source.fillPercentage})`\n      });\n    }\n    // Tick mark update conditions\n    //\n    // 1. Value\n    //    - Reason: a tick mark which was once active might now be inactive or vice versa.\n    // 2. Min, max, or step\n    //    - Reason #1: the number of tick marks may have changed.\n    //    - Reason #2: The value may have silently changed.\n    /** Updates the dots along the slider track. */\n    _updateTickMarkUI() {\n      if (!this.showTickMarks || this.step === undefined || this.min === undefined || this.max === undefined) {\n        return;\n      }\n      const step = this.step > 0 ? this.step : 1;\n      this._isRange ? this._updateTickMarkUIRange(step) : this._updateTickMarkUINonRange(step);\n      if (this._isRtl) {\n        this._tickMarks.reverse();\n      }\n    }\n    _updateTickMarkUINonRange(step) {\n      const value = this._getValue();\n      let numActive = Math.max(Math.round((value - this.min) / step), 0);\n      let numInactive = Math.max(Math.round((this.max - value) / step), 0);\n      this._isRtl ? numActive++ : numInactive++;\n      this._tickMarks = Array(numActive).fill(0 /* _MatTickMark.ACTIVE */).concat(Array(numInactive).fill(1 /* _MatTickMark.INACTIVE */));\n    }\n\n    _updateTickMarkUIRange(step) {\n      const endValue = this._getValue();\n      const startValue = this._getValue(1 /* _MatThumb.START */);\n      const numInactiveBeforeStartThumb = Math.max(Math.floor((startValue - this.min) / step), 0);\n      const numActive = Math.max(Math.floor((endValue - startValue) / step) + 1, 0);\n      const numInactiveAfterEndThumb = Math.max(Math.floor((this.max - endValue) / step), 0);\n      this._tickMarks = Array(numInactiveBeforeStartThumb).fill(1 /* _MatTickMark.INACTIVE */).concat(Array(numActive).fill(0 /* _MatTickMark.ACTIVE */), Array(numInactiveAfterEndThumb).fill(1 /* _MatTickMark.INACTIVE */));\n    }\n    /** Gets the slider thumb input of the given thumb position. */\n    _getInput(thumbPosition) {\n      if (thumbPosition === 2 /* _MatThumb.END */ && this._input) {\n        return this._input;\n      }\n      if (this._inputs?.length) {\n        return thumbPosition === 1 /* _MatThumb.START */ ? this._inputs.first : this._inputs.last;\n      }\n      return;\n    }\n    /** Gets the slider thumb HTML input element of the given thumb position. */\n    _getThumb(thumbPosition) {\n      return thumbPosition === 2 /* _MatThumb.END */ ? this._thumbs?.last : this._thumbs?.first;\n    }\n    _setTransition(withAnimation) {\n      this._hasAnimation = !this._platform.IOS && withAnimation && !this._noopAnimations;\n      this._elementRef.nativeElement.classList.toggle('mat-mdc-slider-with-animation', this._hasAnimation);\n    }\n    /** Whether the given pointer event occurred within the bounds of the slider pointer's DOM Rect. */\n    _isCursorOnSliderThumb(event, rect) {\n      const radius = rect.width / 2;\n      const centerX = rect.x + radius;\n      const centerY = rect.y + radius;\n      const dx = event.clientX - centerX;\n      const dy = event.clientY - centerY;\n      return Math.pow(dx, 2) + Math.pow(dy, 2) < Math.pow(radius, 2);\n    }\n    static #_ = this.ɵfac = function MatSlider_Factory(t) {\n      return new (t || MatSlider)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(MAT_RIPPLE_GLOBAL_OPTIONS, 8), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n    };\n    static #_2 = this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatSlider,\n      selectors: [[\"mat-slider\"]],\n      contentQueries: function MatSlider_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MAT_SLIDER_THUMB, 5);\n          i0.ɵɵcontentQuery(dirIndex, MAT_SLIDER_RANGE_THUMB, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._input = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._inputs = _t);\n        }\n      },\n      viewQuery: function MatSlider_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c2, 5);\n          i0.ɵɵviewQuery(MAT_SLIDER_VISUAL_THUMB, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._trackActive = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._thumbs = _t);\n        }\n      },\n      hostAttrs: [1, \"mat-mdc-slider\", \"mdc-slider\"],\n      hostVars: 10,\n      hostBindings: function MatSlider_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mdc-slider--range\", ctx._isRange)(\"mdc-slider--disabled\", ctx.disabled)(\"mdc-slider--discrete\", ctx.discrete)(\"mdc-slider--tick-marks\", ctx.showTickMarks)(\"_mat-animation-noopable\", ctx._noopAnimations);\n        }\n      },\n      inputs: {\n        color: \"color\",\n        disableRipple: \"disableRipple\",\n        disabled: \"disabled\",\n        discrete: \"discrete\",\n        showTickMarks: \"showTickMarks\",\n        min: \"min\",\n        max: \"max\",\n        step: \"step\",\n        displayWith: \"displayWith\"\n      },\n      exportAs: [\"matSlider\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_SLIDER,\n        useExisting: MatSlider\n      }]), i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c3,\n      decls: 9,\n      vars: 5,\n      consts: [[1, \"mdc-slider__track\"], [1, \"mdc-slider__track--inactive\"], [1, \"mdc-slider__track--active\"], [1, \"mdc-slider__track--active_fill\"], [\"trackActive\", \"\"], [\"class\", \"mdc-slider__tick-marks\", 4, \"ngIf\"], [3, \"discrete\", \"thumbPosition\", \"valueIndicatorText\", 4, \"ngIf\"], [3, \"discrete\", \"thumbPosition\", \"valueIndicatorText\"], [1, \"mdc-slider__tick-marks\"], [\"tickMarkContainer\", \"\"], [4, \"ngIf\"], [3, \"class\", \"transform\", 4, \"ngFor\", \"ngForOf\"]],\n      template: function MatSlider_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n          i0.ɵɵelementStart(1, \"div\", 0);\n          i0.ɵɵelement(2, \"div\", 1);\n          i0.ɵɵelementStart(3, \"div\", 2);\n          i0.ɵɵelement(4, \"div\", 3, 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(6, MatSlider_div_6_Template, 3, 1, \"div\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, MatSlider_mat_slider_visual_thumb_7_Template, 1, 3, \"mat-slider-visual-thumb\", 6);\n          i0.ɵɵelement(8, \"mat-slider-visual-thumb\", 7);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.showTickMarks);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx._isRange);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"discrete\", ctx.discrete)(\"thumbPosition\", 2)(\"valueIndicatorText\", ctx.endValueIndicatorText);\n        }\n      },\n      dependencies: [i2.NgForOf, i2.NgIf, MatSliderVisualThumb],\n      styles: [\".mdc-slider{cursor:pointer;height:48px;margin:0 24px;position:relative;touch-action:pan-y}.mdc-slider .mdc-slider__track{position:absolute;top:50%;transform:translateY(-50%);width:100%}.mdc-slider .mdc-slider__track--active,.mdc-slider .mdc-slider__track--inactive{display:flex;height:100%;position:absolute;width:100%}.mdc-slider .mdc-slider__track--active{overflow:hidden}.mdc-slider .mdc-slider__track--active_fill{border-top-style:solid;box-sizing:border-box;height:100%;width:100%;position:relative;-webkit-transform-origin:left;transform-origin:left}[dir=rtl] .mdc-slider .mdc-slider__track--active_fill,.mdc-slider .mdc-slider__track--active_fill[dir=rtl]{-webkit-transform-origin:right;transform-origin:right}.mdc-slider .mdc-slider__track--inactive{left:0;top:0}.mdc-slider .mdc-slider__track--inactive::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-slider .mdc-slider__track--inactive::before{border-color:CanvasText}}.mdc-slider .mdc-slider__value-indicator-container{bottom:44px;left:var(--slider-value-indicator-container-left, 50%);pointer-events:none;position:absolute;right:var(--slider-value-indicator-container-right);transform:var(--slider-value-indicator-container-transform, translateX(-50%))}.mdc-slider .mdc-slider__value-indicator{transition:transform 100ms 0ms cubic-bezier(0.4, 0, 1, 1);align-items:center;border-radius:4px;display:flex;height:32px;padding:0 12px;transform:scale(0);transform-origin:bottom}.mdc-slider .mdc-slider__value-indicator::before{border-left:6px solid rgba(0,0,0,0);border-right:6px solid rgba(0,0,0,0);border-top:6px solid;bottom:-5px;content:\\\"\\\";height:0;left:var(--slider-value-indicator-caret-left, 50%);position:absolute;right:var(--slider-value-indicator-caret-right);transform:var(--slider-value-indicator-caret-transform, translateX(-50%));width:0}.mdc-slider .mdc-slider__value-indicator::after{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-slider .mdc-slider__value-indicator::after{border-color:CanvasText}}.mdc-slider .mdc-slider__thumb--with-indicator .mdc-slider__value-indicator-container{pointer-events:auto}.mdc-slider .mdc-slider__thumb--with-indicator .mdc-slider__value-indicator{transition:transform 100ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale(1)}@media(prefers-reduced-motion){.mdc-slider .mdc-slider__value-indicator,.mdc-slider .mdc-slider__thumb--with-indicator .mdc-slider__value-indicator{transition:none}}.mdc-slider .mdc-slider__thumb{display:flex;left:-24px;outline:none;position:absolute;user-select:none;height:48px;width:48px}.mdc-slider .mdc-slider__thumb--top{z-index:1}.mdc-slider .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-style:solid;border-width:1px;box-sizing:content-box}.mdc-slider .mdc-slider__thumb-knob{box-sizing:border-box;left:50%;position:absolute;top:50%;transform:translate(-50%, -50%)}.mdc-slider .mdc-slider__tick-marks{align-items:center;box-sizing:border-box;display:flex;height:100%;justify-content:space-between;padding:0 1px;position:absolute;width:100%}.mdc-slider--discrete .mdc-slider__thumb,.mdc-slider--discrete .mdc-slider__track--active_fill{transition:transform 80ms ease}@media(prefers-reduced-motion){.mdc-slider--discrete .mdc-slider__thumb,.mdc-slider--discrete .mdc-slider__track--active_fill{transition:none}}.mdc-slider--disabled{cursor:auto}.mdc-slider--disabled .mdc-slider__thumb{pointer-events:none}.mdc-slider__input{cursor:pointer;left:2px;margin:0;height:44px;opacity:0;pointer-events:none;position:absolute;top:2px;width:44px}.mat-mdc-slider{display:inline-block;box-sizing:border-box;outline:none;vertical-align:middle;margin-left:8px;margin-right:8px;width:auto;min-width:112px;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-slider .mdc-slider__thumb-knob{background-color:var(--mdc-slider-handle-color, var(--mdc-theme-primary, #6200ee));border-color:var(--mdc-slider-handle-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slider .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb-knob{background-color:var(--mdc-slider-disabled-handle-color, var(--mdc-theme-on-surface, #000));border-color:var(--mdc-slider-disabled-handle-color, var(--mdc-theme-on-surface, #000))}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb::before,.mat-mdc-slider .mdc-slider__thumb::after{background-color:var(--mdc-slider-handle-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slider .mdc-slider__thumb:hover::before,.mat-mdc-slider .mdc-slider__thumb.mdc-ripple-surface--hover::before{opacity:var(--mdc-ripple-hover-opacity, 0.04)}.mat-mdc-slider .mdc-slider__thumb.mdc-ripple-upgraded--background-focused::before,.mat-mdc-slider .mdc-slider__thumb:not(.mdc-ripple-upgraded):focus::before{transition-duration:75ms;opacity:var(--mdc-ripple-focus-opacity, 0.12)}.mat-mdc-slider .mdc-slider__thumb:not(.mdc-ripple-upgraded)::after{transition:opacity 150ms linear}.mat-mdc-slider .mdc-slider__thumb:not(.mdc-ripple-upgraded):active::after{transition-duration:75ms;opacity:var(--mdc-ripple-press-opacity, 0.12)}.mat-mdc-slider .mdc-slider__thumb.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-ripple-press-opacity, 0.12)}.mat-mdc-slider .mdc-slider__track--active_fill{border-color:var(--mdc-slider-active-track-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__track--active_fill{border-color:var(--mdc-slider-disabled-active-track-color, var(--mdc-theme-on-surface, #000))}.mat-mdc-slider .mdc-slider__track--inactive{background-color:var(--mdc-slider-inactive-track-color, var(--mdc-theme-primary, #6200ee));opacity:.24}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__track--inactive{background-color:var(--mdc-slider-disabled-inactive-track-color, var(--mdc-theme-on-surface, #000));opacity:.24}.mat-mdc-slider .mdc-slider__tick-mark--active{background-color:var(--mdc-slider-with-tick-marks-active-container-color, var(--mdc-theme-on-primary, #fff));opacity:var(--mdc-slider-with-tick-marks-active-container-opacity, 0.6)}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__tick-mark--active{background-color:var(--mdc-slider-with-tick-marks-active-container-color, var(--mdc-theme-on-primary, #fff));opacity:var(--mdc-slider-with-tick-marks-active-container-opacity, 0.6)}.mat-mdc-slider .mdc-slider__tick-mark--inactive{background-color:var(--mdc-slider-with-tick-marks-inactive-container-color, var(--mdc-theme-primary, #6200ee));opacity:var(--mdc-slider-with-tick-marks-inactive-container-opacity, 0.6)}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__tick-mark--inactive{background-color:var(--mdc-slider-with-tick-marks-disabled-container-color, var(--mdc-theme-on-surface, #000));opacity:var(--mdc-slider-with-tick-marks-inactive-container-opacity, 0.6)}.mat-mdc-slider .mdc-slider__value-indicator{background-color:var(--mdc-slider-label-container-color, #666666);opacity:1}.mat-mdc-slider .mdc-slider__value-indicator::before{border-top-color:var(--mdc-slider-label-container-color, #666666)}.mat-mdc-slider .mdc-slider__value-indicator{color:var(--mdc-slider-label-label-text-color, var(--mdc-theme-on-primary, #fff))}.mat-mdc-slider .mdc-slider__track{height:var(--mdc-slider-inactive-track-height, 4px)}.mat-mdc-slider .mdc-slider__track--active{height:var(--mdc-slider-active-track-height, 6px);top:calc((var(--mdc-slider-inactive-track-height, 4px) - var(--mdc-slider-active-track-height, 6px)) / 2)}.mat-mdc-slider .mdc-slider__track--active_fill{border-top-width:var(--mdc-slider-active-track-height, 6px)}.mat-mdc-slider .mdc-slider__track--inactive{height:var(--mdc-slider-inactive-track-height, 4px)}.mat-mdc-slider .mdc-slider__tick-mark--active,.mat-mdc-slider .mdc-slider__tick-mark--inactive{height:var(--mdc-slider-with-tick-marks-container-size, 2px);width:var(--mdc-slider-with-tick-marks-container-size, 2px)}.mat-mdc-slider.mdc-slider--disabled{opacity:0.38}.mat-mdc-slider .mdc-slider__value-indicator-text{letter-spacing:var(--mdc-slider-label-label-text-tracking, 0.0071428571em);font-size:var(--mdc-slider-label-label-text-size, 0.875rem);font-family:var(--mdc-slider-label-label-text-font, Roboto, sans-serif);font-weight:var(--mdc-slider-label-label-text-weight, 500);line-height:var(--mdc-slider-label-label-text-line-height, 1.375rem)}.mat-mdc-slider .mdc-slider__track--active{border-radius:var(--mdc-slider-active-track-shape, 9999px)}.mat-mdc-slider .mdc-slider__track--inactive{border-radius:var(--mdc-slider-inactive-track-shape, 9999px)}.mat-mdc-slider .mdc-slider__thumb-knob{border-radius:var(--mdc-slider-handle-shape, 50%);width:var(--mdc-slider-handle-width, 20px);height:var(--mdc-slider-handle-height, 20px);border-style:solid;border-width:calc(var(--mdc-slider-handle-height, 20px) / 2) calc(var(--mdc-slider-handle-width, 20px) / 2)}.mat-mdc-slider .mdc-slider__tick-mark--active,.mat-mdc-slider .mdc-slider__tick-mark--inactive{border-radius:var(--mdc-slider-with-tick-marks-container-shape, 50%)}.mat-mdc-slider .mdc-slider__thumb-knob{box-shadow:var(--mdc-slider-handle-elevation, 0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb-knob{background-color:var(--mdc-slider-hover-handle-color, var(--mdc-theme-primary, #6200ee));border-color:var(--mdc-slider-hover-handle-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb-knob{background-color:var(--mdc-slider-focus-handle-color, var(--mdc-theme-primary, #6200ee));border-color:var(--mdc-slider-focus-handle-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb:not(:disabled):active .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:not(:disabled):active .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:not(:disabled):active .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:var(--mdc-slider-with-overlap-handle-outline-color, #fff);border-width:var(--mdc-slider-with-overlap-handle-outline-width, 1px)}.mat-mdc-slider .mdc-slider__input{box-sizing:content-box;pointer-events:auto}.mat-mdc-slider .mdc-slider__input.mat-mdc-slider-input-no-pointer-events{pointer-events:none}.mat-mdc-slider .mdc-slider__input.mat-slider__right-input{left:auto;right:0}.mat-mdc-slider .mdc-slider__thumb,.mat-mdc-slider .mdc-slider__track--active_fill{transition-duration:0ms}.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__thumb,.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__track--active_fill{transition-duration:80ms}.mat-mdc-slider.mdc-slider--discrete .mdc-slider__thumb,.mat-mdc-slider.mdc-slider--discrete .mdc-slider__track--active_fill{transition-duration:0ms}.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__thumb,.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__track--active_fill{transition-duration:80ms}.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__value-indicator{word-break:normal}.mat-mdc-slider .mdc-slider__track,.mat-mdc-slider .mdc-slider__thumb{pointer-events:none}.mat-mdc-slider .mdc-slider__value-indicator{opacity:var(--mat-mdc-slider-value-indicator-opacity, 1)}.mat-mdc-slider .mat-ripple .mat-ripple-element{background-color:var(--mat-mdc-slider-ripple-color, transparent)}.mat-mdc-slider .mat-ripple .mat-mdc-slider-hover-ripple{background-color:var(--mat-mdc-slider-hover-ripple-color, transparent)}.mat-mdc-slider .mat-ripple .mat-mdc-slider-focus-ripple,.mat-mdc-slider .mat-ripple .mat-mdc-slider-active-ripple{background-color:var(--mat-mdc-slider-focus-ripple-color, transparent)}.mat-mdc-slider._mat-animation-noopable.mdc-slider--discrete .mdc-slider__thumb,.mat-mdc-slider._mat-animation-noopable.mdc-slider--discrete .mdc-slider__track--active_fill,.mat-mdc-slider._mat-animation-noopable .mdc-slider__value-indicator{transition:none}.mat-mdc-slider .mat-mdc-focus-indicator::before{border-radius:50%}.mdc-slider__thumb--focused .mat-mdc-focus-indicator::before{content:\\\"\\\"}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatSlider;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Ensures that there is not an invalid configuration for the slider thumb inputs. */\nfunction _validateInputs(isRange, endInputElement, startInputElement) {\n  const startValid = !isRange || startInputElement?._hostElement.hasAttribute('matSliderStartThumb');\n  const endValid = endInputElement._hostElement.hasAttribute(isRange ? 'matSliderEndThumb' : 'matSliderThumb');\n  if (!startValid || !endValid) {\n    _throwInvalidInputConfigurationError();\n  }\n}\nfunction _throwInvalidInputConfigurationError() {\n  throw Error(`Invalid slider thumb input configuration!\n\n   Valid configurations are as follows:\n\n     <mat-slider>\n       <input matSliderThumb>\n     </mat-slider>\n\n     or\n\n     <mat-slider>\n       <input matSliderStartThumb>\n       <input matSliderEndThumb>\n     </mat-slider>\n   `);\n}\n\n/**\n * Provider that allows the slider thumb to register as a ControlValueAccessor.\n * @docs-private\n */\nconst MAT_SLIDER_THUMB_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: /*#__PURE__*/forwardRef(() => MatSliderThumb),\n  multi: true\n};\n/**\n * Provider that allows the range slider thumb to register as a ControlValueAccessor.\n * @docs-private\n */\nconst MAT_SLIDER_RANGE_THUMB_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: /*#__PURE__*/forwardRef(() => MatSliderRangeThumb),\n  multi: true\n};\n/**\n * Directive that adds slider-specific behaviors to an input element inside `<mat-slider>`.\n * Up to two may be placed inside of a `<mat-slider>`.\n *\n * If one is used, the selector `matSliderThumb` must be used, and the outcome will be a normal\n * slider. If two are used, the selectors `matSliderStartThumb` and `matSliderEndThumb` must be\n * used, and the outcome will be a range slider with two slider thumbs.\n */\nlet MatSliderThumb = /*#__PURE__*/(() => {\n  class MatSliderThumb {\n    get value() {\n      return coerceNumberProperty(this._hostElement.value);\n    }\n    set value(v) {\n      const val = coerceNumberProperty(v).toString();\n      if (!this._hasSetInitialValue) {\n        this._initialValue = val;\n        return;\n      }\n      if (this._isActive) {\n        return;\n      }\n      this._hostElement.value = val;\n      this._updateThumbUIByValue();\n      this._slider._onValueChange(this);\n      this._cdr.detectChanges();\n      this._slider._cdr.markForCheck();\n    }\n    /**\n     * The current translateX in px of the slider visual thumb.\n     * @docs-private\n     */\n    get translateX() {\n      if (this._slider.min >= this._slider.max) {\n        this._translateX = 0;\n        return this._translateX;\n      }\n      if (this._translateX === undefined) {\n        this._translateX = this._calcTranslateXByValue();\n      }\n      return this._translateX;\n    }\n    set translateX(v) {\n      this._translateX = v;\n    }\n    /** @docs-private */\n    get min() {\n      return coerceNumberProperty(this._hostElement.min);\n    }\n    set min(v) {\n      this._hostElement.min = coerceNumberProperty(v).toString();\n      this._cdr.detectChanges();\n    }\n    /** @docs-private */\n    get max() {\n      return coerceNumberProperty(this._hostElement.max);\n    }\n    set max(v) {\n      this._hostElement.max = coerceNumberProperty(v).toString();\n      this._cdr.detectChanges();\n    }\n    get step() {\n      return coerceNumberProperty(this._hostElement.step);\n    }\n    set step(v) {\n      this._hostElement.step = coerceNumberProperty(v).toString();\n      this._cdr.detectChanges();\n    }\n    /** @docs-private */\n    get disabled() {\n      return coerceBooleanProperty(this._hostElement.disabled);\n    }\n    set disabled(v) {\n      this._hostElement.disabled = coerceBooleanProperty(v);\n      this._cdr.detectChanges();\n      if (this._slider.disabled !== this.disabled) {\n        this._slider.disabled = this.disabled;\n      }\n    }\n    /** The percentage of the slider that coincides with the value. */\n    get percentage() {\n      if (this._slider.min >= this._slider.max) {\n        return this._slider._isRtl ? 1 : 0;\n      }\n      return (this.value - this._slider.min) / (this._slider.max - this._slider.min);\n    }\n    /** @docs-private */\n    get fillPercentage() {\n      if (!this._slider._cachedWidth) {\n        return this._slider._isRtl ? 1 : 0;\n      }\n      if (this._translateX === 0) {\n        return 0;\n      }\n      return this.translateX / this._slider._cachedWidth;\n    }\n    /** Used to relay updates to _isFocused to the slider visual thumbs. */\n    _setIsFocused(v) {\n      this._isFocused = v;\n    }\n    constructor(_ngZone, _elementRef, _cdr, _slider) {\n      this._ngZone = _ngZone;\n      this._elementRef = _elementRef;\n      this._cdr = _cdr;\n      this._slider = _slider;\n      /** Event emitted when the `value` is changed. */\n      this.valueChange = new EventEmitter();\n      /** Event emitted when the slider thumb starts being dragged. */\n      this.dragStart = new EventEmitter();\n      /** Event emitted when the slider thumb stops being dragged. */\n      this.dragEnd = new EventEmitter();\n      /**\n       * Indicates whether this thumb is the start or end thumb.\n       * @docs-private\n       */\n      this.thumbPosition = 2 /* _MatThumb.END */;\n      /** The radius of a native html slider's knob. */\n      this._knobRadius = 8;\n      /** Whether user's cursor is currently in a mouse down state on the input. */\n      this._isActive = false;\n      /** Whether the input is currently focused (either by tab or after clicking). */\n      this._isFocused = false;\n      /**\n       * Whether the initial value has been set.\n       * This exists because the initial value cannot be immediately set because the min and max\n       * must first be relayed from the parent MatSlider component, which can only happen later\n       * in the component lifecycle.\n       */\n      this._hasSetInitialValue = false;\n      /** Emits when the component is destroyed. */\n      this._destroyed = new Subject();\n      /**\n       * Indicates whether UI updates should be skipped.\n       *\n       * This flag is used to avoid flickering\n       * when correcting values on pointer up/down.\n       */\n      this._skipUIUpdate = false;\n      /** Callback called when the slider input has been touched. */\n      this._onTouchedFn = () => {};\n      /**\n       * Whether the NgModel has been initialized.\n       *\n       * This flag is used to ignore ghost null calls to\n       * writeValue which can break slider initialization.\n       *\n       * See https://github.com/angular/angular/issues/14988.\n       */\n      this._isControlInitialized = false;\n      this._platform = inject(Platform);\n      this._hostElement = _elementRef.nativeElement;\n      this._ngZone.runOutsideAngular(() => {\n        this._hostElement.addEventListener('pointerdown', this._onPointerDown.bind(this));\n        this._hostElement.addEventListener('pointermove', this._onPointerMove.bind(this));\n        this._hostElement.addEventListener('pointerup', this._onPointerUp.bind(this));\n      });\n    }\n    ngOnDestroy() {\n      this._hostElement.removeEventListener('pointerdown', this._onPointerDown);\n      this._hostElement.removeEventListener('pointermove', this._onPointerMove);\n      this._hostElement.removeEventListener('pointerup', this._onPointerUp);\n      this._destroyed.next();\n      this._destroyed.complete();\n      this.dragStart.complete();\n      this.dragEnd.complete();\n    }\n    /** @docs-private */\n    initProps() {\n      this._updateWidthInactive();\n      // If this or the parent slider is disabled, just make everything disabled.\n      if (this.disabled !== this._slider.disabled) {\n        // The MatSlider setter for disabled will relay this and disable both inputs.\n        this._slider.disabled = true;\n      }\n      this.step = this._slider.step;\n      this.min = this._slider.min;\n      this.max = this._slider.max;\n      this._initValue();\n    }\n    /** @docs-private */\n    initUI() {\n      this._updateThumbUIByValue();\n    }\n    _initValue() {\n      this._hasSetInitialValue = true;\n      if (this._initialValue === undefined) {\n        this.value = this._getDefaultValue();\n      } else {\n        this._hostElement.value = this._initialValue;\n        this._updateThumbUIByValue();\n        this._slider._onValueChange(this);\n        this._cdr.detectChanges();\n      }\n    }\n    _getDefaultValue() {\n      return this.min;\n    }\n    _onBlur() {\n      this._setIsFocused(false);\n      this._onTouchedFn();\n    }\n    _onFocus() {\n      this._setIsFocused(true);\n    }\n    _onChange() {\n      this.valueChange.emit(this.value);\n      // only used to handle the edge case where user\n      // mousedown on the slider then uses arrow keys.\n      if (this._isActive) {\n        this._updateThumbUIByValue({\n          withAnimation: true\n        });\n      }\n    }\n    _onInput() {\n      this._onChangeFn?.(this.value);\n      // handles arrowing and updating the value when\n      // a step is defined.\n      if (this._slider.step || !this._isActive) {\n        this._updateThumbUIByValue({\n          withAnimation: true\n        });\n      }\n      this._slider._onValueChange(this);\n    }\n    _onNgControlValueChange() {\n      // only used to handle when the value change\n      // originates outside of the slider.\n      if (!this._isActive || !this._isFocused) {\n        this._slider._onValueChange(this);\n        this._updateThumbUIByValue();\n      }\n      this._slider.disabled = this._formControl.disabled;\n    }\n    _onPointerDown(event) {\n      if (this.disabled || event.button !== 0) {\n        return;\n      }\n      // On IOS, dragging only works if the pointer down happens on the\n      // slider thumb and the slider does not receive focus from pointer events.\n      if (this._platform.IOS) {\n        const isCursorOnSliderThumb = this._slider._isCursorOnSliderThumb(event, this._slider._getThumb(this.thumbPosition)._hostElement.getBoundingClientRect());\n        this._isActive = isCursorOnSliderThumb;\n        this._updateWidthActive();\n        this._slider._updateDimensions();\n        return;\n      }\n      this._isActive = true;\n      this._setIsFocused(true);\n      this._updateWidthActive();\n      this._slider._updateDimensions();\n      // Does nothing if a step is defined because we\n      // want the value to snap to the values on input.\n      if (!this._slider.step) {\n        this._updateThumbUIByPointerEvent(event, {\n          withAnimation: true\n        });\n      }\n      if (!this.disabled) {\n        this._handleValueCorrection(event);\n        this.dragStart.emit({\n          source: this,\n          parent: this._slider,\n          value: this.value\n        });\n      }\n    }\n    /**\n     * Corrects the value of the slider on pointer up/down.\n     *\n     * Called on pointer down and up because the value is set based\n     * on the inactive width instead of the active width.\n     */\n    _handleValueCorrection(event) {\n      // Don't update the UI with the current value! The value on pointerdown\n      // and pointerup is calculated in the split second before the input(s)\n      // resize. See _updateWidthInactive() and _updateWidthActive() for more\n      // details.\n      this._skipUIUpdate = true;\n      // Note that this function gets triggered before the actual value of the\n      // slider is updated. This means if we were to set the value here, it\n      // would immediately be overwritten. Using setTimeout ensures the setting\n      // of the value happens after the value has been updated by the\n      // pointerdown event.\n      setTimeout(() => {\n        this._skipUIUpdate = false;\n        this._fixValue(event);\n      }, 0);\n    }\n    /** Corrects the value of the slider based on the pointer event's position. */\n    _fixValue(event) {\n      const xPos = event.clientX - this._slider._cachedLeft;\n      const width = this._slider._cachedWidth;\n      const step = this._slider.step === 0 ? 1 : this._slider.step;\n      const numSteps = Math.floor((this._slider.max - this._slider.min) / step);\n      const percentage = this._slider._isRtl ? 1 - xPos / width : xPos / width;\n      // To ensure the percentage is rounded to the necessary number of decimals.\n      const fixedPercentage = Math.round(percentage * numSteps) / numSteps;\n      const impreciseValue = fixedPercentage * (this._slider.max - this._slider.min) + this._slider.min;\n      const value = Math.round(impreciseValue / step) * step;\n      const prevValue = this.value;\n      if (value === prevValue) {\n        // Because we prevented UI updates, if it turns out that the race\n        // condition didn't happen and the value is already correct, we\n        // have to apply the ui updates now.\n        this._slider._onValueChange(this);\n        this._slider.step > 0 ? this._updateThumbUIByValue() : this._updateThumbUIByPointerEvent(event, {\n          withAnimation: this._slider._hasAnimation\n        });\n        return;\n      }\n      this.value = value;\n      this.valueChange.emit(this.value);\n      this._onChangeFn?.(this.value);\n      this._slider._onValueChange(this);\n      this._slider.step > 0 ? this._updateThumbUIByValue() : this._updateThumbUIByPointerEvent(event, {\n        withAnimation: this._slider._hasAnimation\n      });\n    }\n    _onPointerMove(event) {\n      // Again, does nothing if a step is defined because\n      // we want the value to snap to the values on input.\n      if (!this._slider.step && this._isActive) {\n        this._updateThumbUIByPointerEvent(event);\n      }\n    }\n    _onPointerUp() {\n      if (this._isActive) {\n        this._isActive = false;\n        this.dragEnd.emit({\n          source: this,\n          parent: this._slider,\n          value: this.value\n        });\n        // This setTimeout is to prevent the pointerup from triggering a value\n        // change on the input based on the inactive width. It's not clear why\n        // but for some reason on IOS this race condition is even more common so\n        // the timeout needs to be increased.\n        setTimeout(() => this._updateWidthInactive(), this._platform.IOS ? 10 : 0);\n      }\n    }\n    _clamp(v) {\n      return Math.max(Math.min(v, this._slider._cachedWidth), 0);\n    }\n    _calcTranslateXByValue() {\n      if (this._slider._isRtl) {\n        return (1 - this.percentage) * this._slider._cachedWidth;\n      }\n      return this.percentage * this._slider._cachedWidth;\n    }\n    _calcTranslateXByPointerEvent(event) {\n      return event.clientX - this._slider._cachedLeft;\n    }\n    /**\n     * Used to set the slider width to the correct\n     * dimensions while the user is dragging.\n     */\n    _updateWidthActive() {\n      this._hostElement.style.padding = `0 ${this._slider._inputPadding}px`;\n      this._hostElement.style.width = `calc(100% + ${this._slider._inputPadding}px)`;\n    }\n    /**\n     * Sets the slider input to disproportionate dimensions to allow for touch\n     * events to be captured on touch devices.\n     */\n    _updateWidthInactive() {\n      this._hostElement.style.padding = '0px';\n      this._hostElement.style.width = 'calc(100% + 48px)';\n      this._hostElement.style.left = '-24px';\n    }\n    _updateThumbUIByValue(options) {\n      this.translateX = this._clamp(this._calcTranslateXByValue());\n      this._updateThumbUI(options);\n    }\n    _updateThumbUIByPointerEvent(event, options) {\n      this.translateX = this._clamp(this._calcTranslateXByPointerEvent(event));\n      this._updateThumbUI(options);\n    }\n    _updateThumbUI(options) {\n      this._slider._setTransition(!!options?.withAnimation);\n      this._slider._onTranslateXChange(this);\n    }\n    /**\n     * Sets the input's value.\n     * @param value The new value of the input\n     * @docs-private\n     */\n    writeValue(value) {\n      if (this._isControlInitialized || value !== null) {\n        this.value = value;\n      }\n    }\n    /**\n     * Registers a callback to be invoked when the input's value changes from user input.\n     * @param fn The callback to register\n     * @docs-private\n     */\n    registerOnChange(fn) {\n      this._onChangeFn = fn;\n      this._isControlInitialized = true;\n    }\n    /**\n     * Registers a callback to be invoked when the input is blurred by the user.\n     * @param fn The callback to register\n     * @docs-private\n     */\n    registerOnTouched(fn) {\n      this._onTouchedFn = fn;\n    }\n    /**\n     * Sets the disabled state of the slider.\n     * @param isDisabled The new disabled state\n     * @docs-private\n     */\n    setDisabledState(isDisabled) {\n      this.disabled = isDisabled;\n    }\n    focus() {\n      this._hostElement.focus();\n    }\n    blur() {\n      this._hostElement.blur();\n    }\n    static #_ = this.ɵfac = function MatSliderThumb_Factory(t) {\n      return new (t || MatSliderThumb)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_SLIDER));\n    };\n    static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatSliderThumb,\n      selectors: [[\"input\", \"matSliderThumb\", \"\"]],\n      hostAttrs: [\"type\", \"range\", 1, \"mdc-slider__input\"],\n      hostVars: 1,\n      hostBindings: function MatSliderThumb_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"change\", function MatSliderThumb_change_HostBindingHandler() {\n            return ctx._onChange();\n          })(\"input\", function MatSliderThumb_input_HostBindingHandler() {\n            return ctx._onInput();\n          })(\"blur\", function MatSliderThumb_blur_HostBindingHandler() {\n            return ctx._onBlur();\n          })(\"focus\", function MatSliderThumb_focus_HostBindingHandler() {\n            return ctx._onFocus();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-valuetext\", ctx._valuetext);\n        }\n      },\n      inputs: {\n        value: \"value\"\n      },\n      outputs: {\n        valueChange: \"valueChange\",\n        dragStart: \"dragStart\",\n        dragEnd: \"dragEnd\"\n      },\n      exportAs: [\"matSliderThumb\"],\n      features: [i0.ɵɵProvidersFeature([MAT_SLIDER_THUMB_VALUE_ACCESSOR, {\n        provide: MAT_SLIDER_THUMB,\n        useExisting: MatSliderThumb\n      }])]\n    });\n  }\n  return MatSliderThumb;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatSliderRangeThumb = /*#__PURE__*/(() => {\n  class MatSliderRangeThumb extends MatSliderThumb {\n    /** @docs-private */\n    getSibling() {\n      if (!this._sibling) {\n        this._sibling = this._slider._getInput(this._isEndThumb ? 1 /* _MatThumb.START */ : 2 /* _MatThumb.END */);\n      }\n\n      return this._sibling;\n    }\n    /**\n     * Returns the minimum translateX position allowed for this slider input's visual thumb.\n     * @docs-private\n     */\n    getMinPos() {\n      const sibling = this.getSibling();\n      if (!this._isLeftThumb && sibling) {\n        return sibling.translateX;\n      }\n      return 0;\n    }\n    /**\n     * Returns the maximum translateX position allowed for this slider input's visual thumb.\n     * @docs-private\n     */\n    getMaxPos() {\n      const sibling = this.getSibling();\n      if (this._isLeftThumb && sibling) {\n        return sibling.translateX;\n      }\n      return this._slider._cachedWidth;\n    }\n    _setIsLeftThumb() {\n      this._isLeftThumb = this._isEndThumb && this._slider._isRtl || !this._isEndThumb && !this._slider._isRtl;\n    }\n    constructor(_ngZone, _slider, _elementRef, _cdr) {\n      super(_ngZone, _elementRef, _cdr, _slider);\n      this._cdr = _cdr;\n      this._isEndThumb = this._hostElement.hasAttribute('matSliderEndThumb');\n      this._setIsLeftThumb();\n      this.thumbPosition = this._isEndThumb ? 2 /* _MatThumb.END */ : 1 /* _MatThumb.START */;\n    }\n\n    _getDefaultValue() {\n      return this._isEndThumb && this._slider._isRange ? this.max : this.min;\n    }\n    _onInput() {\n      super._onInput();\n      this._updateSibling();\n      if (!this._isActive) {\n        this._updateWidthInactive();\n      }\n    }\n    _onNgControlValueChange() {\n      super._onNgControlValueChange();\n      this.getSibling()?._updateMinMax();\n    }\n    _onPointerDown(event) {\n      if (this.disabled || event.button !== 0) {\n        return;\n      }\n      if (this._sibling) {\n        this._sibling._updateWidthActive();\n        this._sibling._hostElement.classList.add('mat-mdc-slider-input-no-pointer-events');\n      }\n      super._onPointerDown(event);\n    }\n    _onPointerUp() {\n      super._onPointerUp();\n      if (this._sibling) {\n        setTimeout(() => {\n          this._sibling._updateWidthInactive();\n          this._sibling._hostElement.classList.remove('mat-mdc-slider-input-no-pointer-events');\n        });\n      }\n    }\n    _onPointerMove(event) {\n      super._onPointerMove(event);\n      if (!this._slider.step && this._isActive) {\n        this._updateSibling();\n      }\n    }\n    _fixValue(event) {\n      super._fixValue(event);\n      this._sibling?._updateMinMax();\n    }\n    _clamp(v) {\n      return Math.max(Math.min(v, this.getMaxPos()), this.getMinPos());\n    }\n    _updateMinMax() {\n      const sibling = this.getSibling();\n      if (!sibling) {\n        return;\n      }\n      if (this._isEndThumb) {\n        this.min = Math.max(this._slider.min, sibling.value);\n        this.max = this._slider.max;\n      } else {\n        this.min = this._slider.min;\n        this.max = Math.min(this._slider.max, sibling.value);\n      }\n    }\n    _updateWidthActive() {\n      const minWidth = this._slider._rippleRadius * 2 - this._slider._inputPadding * 2;\n      const maxWidth = this._slider._cachedWidth + this._slider._inputPadding - minWidth;\n      const percentage = this._slider.min < this._slider.max ? (this.max - this.min) / (this._slider.max - this._slider.min) : 1;\n      const width = maxWidth * percentage + minWidth;\n      this._hostElement.style.width = `${width}px`;\n      this._hostElement.style.padding = `0 ${this._slider._inputPadding}px`;\n    }\n    _updateWidthInactive() {\n      const sibling = this.getSibling();\n      if (!sibling) {\n        return;\n      }\n      const maxWidth = this._slider._cachedWidth;\n      const midValue = this._isEndThumb ? this.value - (this.value - sibling.value) / 2 : this.value + (sibling.value - this.value) / 2;\n      const _percentage = this._isEndThumb ? (this.max - midValue) / (this._slider.max - this._slider.min) : (midValue - this.min) / (this._slider.max - this._slider.min);\n      const percentage = this._slider.min < this._slider.max ? _percentage : 1;\n      // Extend the native input width by the radius of the ripple\n      let ripplePadding = this._slider._rippleRadius;\n      // If one of the inputs is maximally sized (the value of both thumbs is\n      // equal to the min or max), make that input take up all of the width and\n      // make the other unselectable.\n      if (percentage === 1) {\n        ripplePadding = 48;\n      } else if (percentage === 0) {\n        ripplePadding = 0;\n      }\n      const width = maxWidth * percentage + ripplePadding;\n      this._hostElement.style.width = `${width}px`;\n      this._hostElement.style.padding = '0px';\n      if (this._isLeftThumb) {\n        this._hostElement.style.left = '-24px';\n        this._hostElement.style.right = 'auto';\n      } else {\n        this._hostElement.style.left = 'auto';\n        this._hostElement.style.right = '-24px';\n      }\n    }\n    _updateStaticStyles() {\n      this._hostElement.classList.toggle('mat-slider__right-input', !this._isLeftThumb);\n    }\n    _updateSibling() {\n      const sibling = this.getSibling();\n      if (!sibling) {\n        return;\n      }\n      sibling._updateMinMax();\n      if (this._isActive) {\n        sibling._updateWidthActive();\n      } else {\n        sibling._updateWidthInactive();\n      }\n    }\n    /**\n     * Sets the input's value.\n     * @param value The new value of the input\n     * @docs-private\n     */\n    writeValue(value) {\n      if (this._isControlInitialized || value !== null) {\n        this.value = value;\n        this._updateWidthInactive();\n        this._updateSibling();\n      }\n    }\n    static #_ = this.ɵfac = function MatSliderRangeThumb_Factory(t) {\n      return new (t || MatSliderRangeThumb)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(MAT_SLIDER), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatSliderRangeThumb,\n      selectors: [[\"input\", \"matSliderStartThumb\", \"\"], [\"input\", \"matSliderEndThumb\", \"\"]],\n      exportAs: [\"matSliderRangeThumb\"],\n      features: [i0.ɵɵProvidersFeature([MAT_SLIDER_RANGE_THUMB_VALUE_ACCESSOR, {\n        provide: MAT_SLIDER_RANGE_THUMB,\n        useExisting: MatSliderRangeThumb\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return MatSliderRangeThumb;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatSliderModule = /*#__PURE__*/(() => {\n  class MatSliderModule {\n    static #_ = this.ɵfac = function MatSliderModule_Factory(t) {\n      return new (t || MatSliderModule)();\n    };\n    static #_2 = this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatSliderModule\n    });\n    static #_3 = this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, CommonModule, MatRippleModule]\n    });\n  }\n  return MatSliderModule;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MatSlider, MatSliderChange, MatSliderModule, MatSliderRangeThumb, MatSliderThumb, MatSliderVisualThumb };\n//# sourceMappingURL=slider.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}