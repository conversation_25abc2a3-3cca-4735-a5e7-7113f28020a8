<div class="teacher-timetable-container">
  <!-- Header -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">
        <mat-icon class="title-icon">schedule</mat-icon>
        My Timetable
      </h1>
      <p class="page-subtitle">Your weekly teaching schedule</p>
    </div>
    <div class="header-actions">
      <button mat-raised-button color="primary" (click)="refreshTimetable()">
        <mat-icon>refresh</mat-icon>
        Refresh
      </button>
      <button mat-raised-button color="accent" (click)="exportTimetable()">
        <mat-icon>download</mat-icon>
        Export
      </button>
    </div>
  </div>

  <!-- Filters -->
  <mat-card class="filters-card">
    <mat-card-content>
      <div class="filters-row">
        <mat-form-field appearance="outline">
          <mat-label>Academic Year</mat-label>
          <mat-select [(value)]="selectedAcademicYear" (selectionChange)="onFilterChange()">
            <mat-option *ngFor="let year of availableAcademicYears" [value]="year">
              {{ year }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Filter by Day</mat-label>
          <mat-select [(value)]="selectedDay" (selectionChange)="onFilterChange()">
            <mat-option value="">All Days</mat-option>
            <mat-option *ngFor="let day of weekDays" [value]="day">
              {{ day }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Statistics Cards -->
  <div class="stats-section">
    <div class="stats-row">
      <mat-card class="stat-card">
        <mat-card-content>
          <div class="stat-content">
            <mat-icon class="stat-icon">class</mat-icon>
            <div class="stat-info">
              <h3>{{ totalClasses }}</h3>
              <p>Total Classes</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card">
        <mat-card-content>
          <div class="stat-content">
            <mat-icon class="stat-icon">subject</mat-icon>
            <div class="stat-info">
              <h3>{{ totalSubjects }}</h3>
              <p>Subjects</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card">
        <mat-card-content>
          <div class="stat-content">
            <mat-icon class="stat-icon">access_time</mat-icon>
            <div class="stat-info">
              <h3>{{ totalHours }}</h3>
              <p>Hours/Week</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>

  <!-- Current Day Highlight -->
  <div class="current-day-info">
    <mat-card class="current-day-card">
      <mat-card-content>
        <div class="current-day-content">
          <mat-icon>today</mat-icon>
          <div class="day-info">
            <h3>Today is {{ getCurrentDay() | titlecase }}</h3>
            <p>{{ getCurrentDate() | date:'fullDate' }}</p>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="loading-container">
    <mat-spinner></mat-spinner>
    <p>Loading your timetable...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !loading" class="error-container">
    <mat-icon>error</mat-icon>
    <h3>Error Loading Timetable</h3>
    <p>{{ error }}</p>
    <button mat-raised-button color="primary" (click)="refreshTimetable()">
      Try Again
    </button>
  </div>

  <!-- Timetable Grid -->
  <div *ngIf="!loading && !error" class="timetable-wrapper">
    <mat-card class="timetable-card">
      <mat-card-header>
        <mat-card-title>Weekly Schedule</mat-card-title>
        <mat-card-subtitle>Your teaching schedule for {{ selectedAcademicYear }}</mat-card-subtitle>
      </mat-card-header>
      
      <mat-card-content>
        <div class="timetable-grid">
          <!-- Header Row -->
          <div class="timetable-header">
            <div class="time-header">Time</div>
            <div *ngFor="let day of weekDays" class="day-header" 
                 [class.current-day]="day.toLowerCase() === getCurrentDay()">
              {{ day }}
            </div>
          </div>

          <!-- Time Slots -->
          <div *ngFor="let slot of timetableGrid" class="timetable-row"
               [class.current-time]="isCurrentTimeSlot(slot)">
            <div class="time-slot">{{ slot.time }}</div>
            
            <div *ngFor="let day of weekDays" class="schedule-cell"
                 [class.current-day]="day.toLowerCase() === getCurrentDay()"
                 [class.has-class]="slot[day.toLowerCase()]">
              
              <div *ngIf="slot[day.toLowerCase()]; else emptySlot" class="class-info">
                <div class="subject-name">{{ slot[day.toLowerCase()]?.subject.subjectName }}</div>
                <div class="class-details">
                  <span class="class-name">{{ getClassDisplayName(slot[day.toLowerCase()]?.class) }}</span>
                  <span class="program-info">{{ slot[day.toLowerCase()]?.program.name }}</span>
                </div>
                <div class="additional-info">
                  <span class="semester">{{ getSemesterDisplayText(slot[day.toLowerCase()]?.semester, slot[day.toLowerCase()]?.program.name) }}</span>
                  <span class="room" *ngIf="slot[day.toLowerCase()]?.room">Room: {{ slot[day.toLowerCase()]?.room }}</span>
                </div>
              </div>
              
              <ng-template #emptySlot>
                <div class="empty-slot">
                  <span class="free-time">Free</span>
                </div>
              </ng-template>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Today's Classes Summary -->
  <div *ngIf="!loading && !error && timetableEntries.length > 0" class="today-summary">
    <mat-card class="summary-card">
      <mat-card-header>
        <mat-card-title>Today's Classes</mat-card-title>
        <mat-card-subtitle>{{ getCurrentDate() | date:'fullDate' }}</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="today-classes">
          <div *ngFor="let entry of getTodayClasses()" class="today-class-item">
            <div class="time-info">
              <span class="time">{{ entry.timeSlot.startTime }} - {{ entry.timeSlot.endTime }}</span>
            </div>
            <div class="class-details">
              <h4>{{ entry.subject.subjectName }}</h4>
              <p>{{ getClassDisplayName(entry.class) }} - {{ entry.program.name }}</p>
              <small>{{ getSemesterDisplayText(entry.semester, entry.program.name) }}</small>
            </div>
            <div class="room-info" *ngIf="entry.room">
              <mat-icon>location_on</mat-icon>
              <span>{{ entry.room }}</span>
            </div>
          </div>
          <div *ngIf="getTodayClasses().length === 0" class="no-classes">
            <mat-icon>free_breakfast</mat-icon>
            <p>No classes scheduled for today!</p>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Empty State -->
  <div *ngIf="!loading && !error && timetableEntries.length === 0" class="empty-state">
    <mat-card class="empty-card">
      <mat-card-content>
        <div class="empty-content">
          <mat-icon>schedule</mat-icon>
          <h3>No Timetable Found</h3>
          <p>You don't have any classes scheduled for the selected academic year.</p>
          <p>Please contact the administration if you believe this is an error.</p>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
