{"ast": null, "code": "import { HttpHeaders, HttpParams } from '@angular/common/http';\nimport { throwError } from 'rxjs';\nimport { catchError, tap } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class NoticeService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = environment.apiUrl;\n  }\n  getAuthHeaders() {\n    const token = localStorage.getItem('token');\n    return new HttpHeaders({\n      'Authorization': `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    });\n  }\n  // Create a new notice\n  createNotice(noticeData) {\n    return this.http.post(`${this.apiUrl}/notices`, noticeData, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap({\n      next: response => {\n        console.log('Notice created successfully:', response);\n      }\n    }), catchError(error => {\n      console.error('Error creating notice:', error);\n      return throwError(() => error);\n    }));\n  }\n  // Get all notices with filtering\n  getAllNotices(params) {\n    let httpParams = new HttpParams();\n    if (params) {\n      Object.keys(params).forEach(key => {\n        const value = params[key];\n        if (value !== undefined && value !== null) {\n          httpParams = httpParams.set(key, value.toString());\n        }\n      });\n    }\n    return this.http.get(`${this.apiUrl}/notices`, {\n      headers: this.getAuthHeaders(),\n      params: httpParams\n    }).pipe(catchError(error => {\n      console.error('Error fetching notices:', error);\n      return throwError(() => error);\n    }));\n  }\n  // Get notice by ID\n  getNoticeById(id) {\n    return this.http.get(`${this.apiUrl}/notices/${id}`, {\n      headers: this.getAuthHeaders()\n    }).pipe(catchError(error => {\n      console.error('Error fetching notice:', error);\n      return throwError(() => error);\n    }));\n  }\n  // Update notice\n  updateNotice(id, updateData) {\n    return this.http.put(`${this.apiUrl}/notices/${id}`, updateData, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap({\n      next: response => {\n        console.log('Notice updated successfully:', response);\n      }\n    }), catchError(error => {\n      console.error('Error updating notice:', error);\n      return throwError(() => error);\n    }));\n  }\n  // Mark notice as read\n  markAsRead(id, userId) {\n    return this.http.post(`${this.apiUrl}/notices/${id}/read`, {\n      userId\n    }, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap({\n      next: response => {\n        console.log('Notice marked as read:', response);\n      }\n    }), catchError(error => {\n      console.error('Error marking notice as read:', error);\n      return throwError(() => error);\n    }));\n  }\n  // Get notices for specific user\n  getNoticesForUser(userId, page = 1, limit = 10) {\n    const params = new HttpParams().set('page', page.toString()).set('limit', limit.toString());\n    return this.http.get(`${this.apiUrl}/notices/user/${userId}`, {\n      headers: this.getAuthHeaders(),\n      params\n    }).pipe(catchError(error => {\n      console.error('Error fetching user notices:', error);\n      return throwError(() => error);\n    }));\n  }\n  // Delete notice\n  deleteNotice(id) {\n    return this.http.delete(`${this.apiUrl}/notices/${id}`, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap({\n      next: response => {\n        console.log('Notice deleted successfully:', response);\n      }\n    }), catchError(error => {\n      console.error('Error deleting notice:', error);\n      return throwError(() => error);\n    }));\n  }\n  // Publish notice\n  publishNotice(id) {\n    return this.updateNotice(id, {\n      isPublished: true\n    });\n  }\n  // Unpublish notice\n  unpublishNotice(id) {\n    return this.updateNotice(id, {\n      isPublished: false\n    });\n  }\n  // Pin notice\n  pinNotice(id) {\n    return this.updateNotice(id, {\n      isPinned: true\n    });\n  }\n  // Unpin notice\n  unpinNotice(id) {\n    return this.updateNotice(id, {\n      isPinned: false\n    });\n  }\n  // Get notice statistics\n  getNoticeStats() {\n    return this.http.get(`${this.apiUrl}/notices/stats`, {\n      headers: this.getAuthHeaders()\n    }).pipe(catchError(error => {\n      console.error('Error fetching notice stats:', error);\n      return throwError(() => error);\n    }));\n  }\n  static #_ = this.ɵfac = function NoticeService_Factory(t) {\n    return new (t || NoticeService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: NoticeService,\n    factory: NoticeService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "HttpParams", "throwError", "catchError", "tap", "environment", "NoticeService", "constructor", "http", "apiUrl", "getAuthHeaders", "token", "localStorage", "getItem", "createNotice", "noticeData", "post", "headers", "pipe", "next", "response", "console", "log", "error", "getAllNotices", "params", "httpParams", "Object", "keys", "for<PERSON>ach", "key", "value", "undefined", "set", "toString", "get", "getNoticeById", "id", "updateNotice", "updateData", "put", "mark<PERSON><PERSON><PERSON>", "userId", "getNoticesForUser", "page", "limit", "deleteNotice", "delete", "publishNotice", "isPublished", "unpublishNotice", "pinNotice", "isPinned", "unpinNotice", "getNoticeStats", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\services\\notice.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';\r\nimport { Observable, throwError } from 'rxjs';\r\nimport { catchError, tap } from 'rxjs/operators';\r\nimport { environment } from '../../environments/environment';\r\n\r\nexport interface Notice {\r\n  _id?: string;\r\n  title: string;\r\n  content: string;\r\n  author: string;\r\n  authorRole: string;\r\n  category: 'General' | 'Academic' | 'Administrative' | 'Event' | 'Holiday' | 'Examination' | 'Emergency';\r\n  priority: 'Low' | 'Medium' | 'High' | 'Urgent';\r\n  targetAudience: string[];\r\n  targetPrograms?: string[];\r\n  targetDepartments?: string[];\r\n  targetClasses?: string[];\r\n  targetSemesters?: number[];\r\n  publishDate: Date;\r\n  expiryDate?: Date;\r\n  isPublished: boolean;\r\n  isPinned: boolean;\r\n  readBy?: Array<{\r\n    user: string;\r\n    readAt: Date;\r\n  }>;\r\n  views: number;\r\n  createdAt?: Date;\r\n  updatedAt?: Date;\r\n  status?: string;\r\n  daysRemaining?: number;\r\n}\r\n\r\nexport interface NoticeResponse {\r\n  success: boolean;\r\n  message?: string;\r\n  notice?: Notice;\r\n  notices?: Notice[];\r\n  pagination?: {\r\n    currentPage: number;\r\n    totalPages: number;\r\n    totalNotices: number;\r\n    hasNext: boolean;\r\n    hasPrev: boolean;\r\n  };\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class NoticeService {\r\n  private apiUrl = environment.apiUrl;\r\n\r\n  constructor(private http: HttpClient) { }\r\n\r\n  private getAuthHeaders(): HttpHeaders {\r\n    const token = localStorage.getItem('token');\r\n    return new HttpHeaders({\r\n      'Authorization': `Bearer ${token}`,\r\n      'Content-Type': 'application/json'\r\n    });\r\n  }\r\n\r\n  // Create a new notice\r\n  createNotice(noticeData: Partial<Notice>): Observable<NoticeResponse> {\r\n    return this.http.post<NoticeResponse>(`${this.apiUrl}/notices`, noticeData, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      tap({\r\n        next: (response) => {\r\n          console.log('Notice created successfully:', response);\r\n        },\r\n      }),\r\n      catchError((error) => {\r\n        console.error('Error creating notice:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Get all notices with filtering\r\n  getAllNotices(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    category?: string;\r\n    priority?: string;\r\n    author?: string;\r\n    targetAudience?: string;\r\n    isPublished?: boolean;\r\n    isPinned?: boolean;\r\n    includeUnpublished?: boolean;\r\n  }): Observable<NoticeResponse> {\r\n    let httpParams = new HttpParams();\r\n    \r\n    if (params) {\r\n      Object.keys(params).forEach(key => {\r\n        const value = params[key as keyof typeof params];\r\n        if (value !== undefined && value !== null) {\r\n          httpParams = httpParams.set(key, value.toString());\r\n        }\r\n      });\r\n    }\r\n\r\n    return this.http.get<NoticeResponse>(`${this.apiUrl}/notices`, {\r\n      headers: this.getAuthHeaders(),\r\n      params: httpParams\r\n    }).pipe(\r\n      catchError((error) => {\r\n        console.error('Error fetching notices:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Get notice by ID\r\n  getNoticeById(id: string): Observable<NoticeResponse> {\r\n    return this.http.get<NoticeResponse>(`${this.apiUrl}/notices/${id}`, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      catchError((error) => {\r\n        console.error('Error fetching notice:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Update notice\r\n  updateNotice(id: string, updateData: Partial<Notice>): Observable<NoticeResponse> {\r\n    return this.http.put<NoticeResponse>(`${this.apiUrl}/notices/${id}`, updateData, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      tap({\r\n        next: (response) => {\r\n          console.log('Notice updated successfully:', response);\r\n        },\r\n      }),\r\n      catchError((error) => {\r\n        console.error('Error updating notice:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Mark notice as read\r\n  markAsRead(id: string, userId: string): Observable<NoticeResponse> {\r\n    return this.http.post<NoticeResponse>(`${this.apiUrl}/notices/${id}/read`, {\r\n      userId\r\n    }, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      tap({\r\n        next: (response) => {\r\n          console.log('Notice marked as read:', response);\r\n        },\r\n      }),\r\n      catchError((error) => {\r\n        console.error('Error marking notice as read:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Get notices for specific user\r\n  getNoticesForUser(userId: string, page: number = 1, limit: number = 10): Observable<NoticeResponse> {\r\n    const params = new HttpParams()\r\n      .set('page', page.toString())\r\n      .set('limit', limit.toString());\r\n\r\n    return this.http.get<NoticeResponse>(`${this.apiUrl}/notices/user/${userId}`, {\r\n      headers: this.getAuthHeaders(),\r\n      params\r\n    }).pipe(\r\n      catchError((error) => {\r\n        console.error('Error fetching user notices:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Delete notice\r\n  deleteNotice(id: string): Observable<NoticeResponse> {\r\n    return this.http.delete<NoticeResponse>(`${this.apiUrl}/notices/${id}`, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      tap({\r\n        next: (response) => {\r\n          console.log('Notice deleted successfully:', response);\r\n        },\r\n      }),\r\n      catchError((error) => {\r\n        console.error('Error deleting notice:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Publish notice\r\n  publishNotice(id: string): Observable<NoticeResponse> {\r\n    return this.updateNotice(id, { isPublished: true });\r\n  }\r\n\r\n  // Unpublish notice\r\n  unpublishNotice(id: string): Observable<NoticeResponse> {\r\n    return this.updateNotice(id, { isPublished: false });\r\n  }\r\n\r\n  // Pin notice\r\n  pinNotice(id: string): Observable<NoticeResponse> {\r\n    return this.updateNotice(id, { isPinned: true });\r\n  }\r\n\r\n  // Unpin notice\r\n  unpinNotice(id: string): Observable<NoticeResponse> {\r\n    return this.updateNotice(id, { isPinned: false });\r\n  }\r\n\r\n  // Get notice statistics\r\n  getNoticeStats(): Observable<any> {\r\n    return this.http.get<any>(`${this.apiUrl}/notices/stats`, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      catchError((error) => {\r\n        console.error('Error fetching notice stats:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AACA,SAAqBA,WAAW,EAAEC,UAAU,QAAQ,sBAAsB;AAC1E,SAAqBC,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;AAChD,SAASC,WAAW,QAAQ,gCAAgC;;;AA+C5D,OAAM,MAAOC,aAAa;EAGxBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAGJ,WAAW,CAACI,MAAM;EAEK;EAEhCC,cAAcA,CAAA;IACpB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,OAAO,IAAIb,WAAW,CAAC;MACrB,eAAe,EAAE,UAAUW,KAAK,EAAE;MAClC,cAAc,EAAE;KACjB,CAAC;EACJ;EAEA;EACAG,YAAYA,CAACC,UAA2B;IACtC,OAAO,IAAI,CAACP,IAAI,CAACQ,IAAI,CAAiB,GAAG,IAAI,CAACP,MAAM,UAAU,EAAEM,UAAU,EAAE;MAC1EE,OAAO,EAAE,IAAI,CAACP,cAAc;KAC7B,CAAC,CAACQ,IAAI,CACLd,GAAG,CAAC;MACFe,IAAI,EAAGC,QAAQ,IAAI;QACjBC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEF,QAAQ,CAAC;MACvD;KACD,CAAC,EACFjB,UAAU,CAAEoB,KAAK,IAAI;MACnBF,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAOrB,UAAU,CAAC,MAAMqB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAC,aAAaA,CAACC,MAUb;IACC,IAAIC,UAAU,GAAG,IAAIzB,UAAU,EAAE;IAEjC,IAAIwB,MAAM,EAAE;MACVE,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC,CAACI,OAAO,CAACC,GAAG,IAAG;QAChC,MAAMC,KAAK,GAAGN,MAAM,CAACK,GAA0B,CAAC;QAChD,IAAIC,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,EAAE;UACzCL,UAAU,GAAGA,UAAU,CAACO,GAAG,CAACH,GAAG,EAAEC,KAAK,CAACG,QAAQ,EAAE,CAAC;;MAEtD,CAAC,CAAC;;IAGJ,OAAO,IAAI,CAAC1B,IAAI,CAAC2B,GAAG,CAAiB,GAAG,IAAI,CAAC1B,MAAM,UAAU,EAAE;MAC7DQ,OAAO,EAAE,IAAI,CAACP,cAAc,EAAE;MAC9Be,MAAM,EAAEC;KACT,CAAC,CAACR,IAAI,CACLf,UAAU,CAAEoB,KAAK,IAAI;MACnBF,OAAO,CAACE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,OAAOrB,UAAU,CAAC,MAAMqB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAa,aAAaA,CAACC,EAAU;IACtB,OAAO,IAAI,CAAC7B,IAAI,CAAC2B,GAAG,CAAiB,GAAG,IAAI,CAAC1B,MAAM,YAAY4B,EAAE,EAAE,EAAE;MACnEpB,OAAO,EAAE,IAAI,CAACP,cAAc;KAC7B,CAAC,CAACQ,IAAI,CACLf,UAAU,CAAEoB,KAAK,IAAI;MACnBF,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAOrB,UAAU,CAAC,MAAMqB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAe,YAAYA,CAACD,EAAU,EAAEE,UAA2B;IAClD,OAAO,IAAI,CAAC/B,IAAI,CAACgC,GAAG,CAAiB,GAAG,IAAI,CAAC/B,MAAM,YAAY4B,EAAE,EAAE,EAAEE,UAAU,EAAE;MAC/EtB,OAAO,EAAE,IAAI,CAACP,cAAc;KAC7B,CAAC,CAACQ,IAAI,CACLd,GAAG,CAAC;MACFe,IAAI,EAAGC,QAAQ,IAAI;QACjBC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEF,QAAQ,CAAC;MACvD;KACD,CAAC,EACFjB,UAAU,CAAEoB,KAAK,IAAI;MACnBF,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAOrB,UAAU,CAAC,MAAMqB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAkB,UAAUA,CAACJ,EAAU,EAAEK,MAAc;IACnC,OAAO,IAAI,CAAClC,IAAI,CAACQ,IAAI,CAAiB,GAAG,IAAI,CAACP,MAAM,YAAY4B,EAAE,OAAO,EAAE;MACzEK;KACD,EAAE;MACDzB,OAAO,EAAE,IAAI,CAACP,cAAc;KAC7B,CAAC,CAACQ,IAAI,CACLd,GAAG,CAAC;MACFe,IAAI,EAAGC,QAAQ,IAAI;QACjBC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEF,QAAQ,CAAC;MACjD;KACD,CAAC,EACFjB,UAAU,CAAEoB,KAAK,IAAI;MACnBF,OAAO,CAACE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAOrB,UAAU,CAAC,MAAMqB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAoB,iBAAiBA,CAACD,MAAc,EAAEE,IAAA,GAAe,CAAC,EAAEC,KAAA,GAAgB,EAAE;IACpE,MAAMpB,MAAM,GAAG,IAAIxB,UAAU,EAAE,CAC5BgC,GAAG,CAAC,MAAM,EAAEW,IAAI,CAACV,QAAQ,EAAE,CAAC,CAC5BD,GAAG,CAAC,OAAO,EAAEY,KAAK,CAACX,QAAQ,EAAE,CAAC;IAEjC,OAAO,IAAI,CAAC1B,IAAI,CAAC2B,GAAG,CAAiB,GAAG,IAAI,CAAC1B,MAAM,iBAAiBiC,MAAM,EAAE,EAAE;MAC5EzB,OAAO,EAAE,IAAI,CAACP,cAAc,EAAE;MAC9Be;KACD,CAAC,CAACP,IAAI,CACLf,UAAU,CAAEoB,KAAK,IAAI;MACnBF,OAAO,CAACE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAOrB,UAAU,CAAC,MAAMqB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAuB,YAAYA,CAACT,EAAU;IACrB,OAAO,IAAI,CAAC7B,IAAI,CAACuC,MAAM,CAAiB,GAAG,IAAI,CAACtC,MAAM,YAAY4B,EAAE,EAAE,EAAE;MACtEpB,OAAO,EAAE,IAAI,CAACP,cAAc;KAC7B,CAAC,CAACQ,IAAI,CACLd,GAAG,CAAC;MACFe,IAAI,EAAGC,QAAQ,IAAI;QACjBC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEF,QAAQ,CAAC;MACvD;KACD,CAAC,EACFjB,UAAU,CAAEoB,KAAK,IAAI;MACnBF,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAOrB,UAAU,CAAC,MAAMqB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAyB,aAAaA,CAACX,EAAU;IACtB,OAAO,IAAI,CAACC,YAAY,CAACD,EAAE,EAAE;MAAEY,WAAW,EAAE;IAAI,CAAE,CAAC;EACrD;EAEA;EACAC,eAAeA,CAACb,EAAU;IACxB,OAAO,IAAI,CAACC,YAAY,CAACD,EAAE,EAAE;MAAEY,WAAW,EAAE;IAAK,CAAE,CAAC;EACtD;EAEA;EACAE,SAASA,CAACd,EAAU;IAClB,OAAO,IAAI,CAACC,YAAY,CAACD,EAAE,EAAE;MAAEe,QAAQ,EAAE;IAAI,CAAE,CAAC;EAClD;EAEA;EACAC,WAAWA,CAAChB,EAAU;IACpB,OAAO,IAAI,CAACC,YAAY,CAACD,EAAE,EAAE;MAAEe,QAAQ,EAAE;IAAK,CAAE,CAAC;EACnD;EAEA;EACAE,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAC9C,IAAI,CAAC2B,GAAG,CAAM,GAAG,IAAI,CAAC1B,MAAM,gBAAgB,EAAE;MACxDQ,OAAO,EAAE,IAAI,CAACP,cAAc;KAC7B,CAAC,CAACQ,IAAI,CACLf,UAAU,CAAEoB,KAAK,IAAI;MACnBF,OAAO,CAACE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAOrB,UAAU,CAAC,MAAMqB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAAC,QAAAgC,CAAA,G;qBAhLUjD,aAAa,EAAAkD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAbtD,aAAa;IAAAuD,OAAA,EAAbvD,aAAa,CAAAwD,IAAA;IAAAC,UAAA,EAFZ;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}