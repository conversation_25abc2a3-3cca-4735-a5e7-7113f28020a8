const mongoose = require('mongoose');

const noticeSchema = new mongoose.Schema({
    title: {
        type: String,
        required: [true, 'Notice title is required'],
        trim: true
    },
    content: {
        type: String,
        required: [true, 'Notice content is required'],
        trim: true
    },
    author: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: [true, 'Notice author is required']
    },
    authorRole: {
        type: String,
        enum: ['Principal', 'Teacher', 'Admin'],
        required: [true, 'Author role is required']
    },
    category: {
        type: String,
        enum: ['General', 'Academic', 'Administrative', 'Event', 'Holiday', 'Examination', 'Emergency'],
        default: 'General'
    },
    priority: {
        type: String,
        enum: ['Low', 'Medium', 'High', 'Urgent'],
        default: 'Medium'
    },
    targetAudience: {
        type: [String],
        enum: ['All', 'Students', 'Teachers', 'Principal', 'Staff'],
        default: ['All']
    },
    targetPrograms: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Program'
    }],
    targetDepartments: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Departments'
    }],
    targetClasses: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Class'
    }],
    targetSemesters: [{
        type: Number,
        min: 1,
        max: 8
    }],
    publishDate: {
        type: Date,
        default: Date.now
    },
    expiryDate: {
        type: Date
    },
    isPublished: {
        type: Boolean,
        default: false
    },
    isPinned: {
        type: Boolean,
        default: false
    },
    attachments: [{
        filename: String,
        originalName: String,
        path: String,
        size: Number,
        mimetype: String
    }],
    readBy: [{
        user: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        },
        readAt: {
            type: Date,
            default: Date.now
        }
    }],
    views: {
        type: Number,
        default: 0
    },
    isActive: {
        type: Boolean,
        default: true
    }
}, { 
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Virtual for notice status
noticeSchema.virtual('status').get(function() {
    const now = new Date();
    if (!this.isPublished) return 'Draft';
    if (this.expiryDate && now > this.expiryDate) return 'Expired';
    if (now < this.publishDate) return 'Scheduled';
    return 'Active';
});

// Virtual for days remaining
noticeSchema.virtual('daysRemaining').get(function() {
    if (!this.expiryDate) return null;
    const now = new Date();
    const diffTime = this.expiryDate - now;
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

// Index for better query performance
noticeSchema.index({ author: 1, isPublished: 1 });
noticeSchema.index({ publishDate: -1, isPublished: 1 });
noticeSchema.index({ targetAudience: 1, isPublished: 1 });
noticeSchema.index({ isPinned: -1, publishDate: -1 });

module.exports = mongoose.model('Notice', noticeSchema);
