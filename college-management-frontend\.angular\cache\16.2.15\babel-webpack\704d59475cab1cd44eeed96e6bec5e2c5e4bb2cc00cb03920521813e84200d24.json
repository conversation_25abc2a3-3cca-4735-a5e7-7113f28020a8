{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { TeachertableComponent } from './teachertable/teachertable.component';\nimport { TeacherViewComponent } from './teacher-view/teacher-view.component';\nimport { TeacherFormComponent } from './teacher-form/teacher-form.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: TeachertableComponent\n}, {\n  path: 'view-teacher',\n  component: TeacherViewComponent\n}, {\n  path: 'add-teacher',\n  component: TeacherFormComponent\n}];\nexport let TeacherRoutingModule = /*#__PURE__*/(() => {\n  class TeacherRoutingModule {\n    static #_ = this.ɵfac = function TeacherRoutingModule_Factory(t) {\n      return new (t || TeacherRoutingModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: TeacherRoutingModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n  return TeacherRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}