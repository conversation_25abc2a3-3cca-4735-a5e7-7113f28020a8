{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../services/notice.service\";\nimport * as i3 from \"../../../services/user.service\";\nimport * as i4 from \"../../../services/timetable.service\";\nimport * as i5 from \"@angular/material/snack-bar\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/core\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/card\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/input\";\nimport * as i12 from \"@angular/material/form-field\";\nimport * as i13 from \"@angular/forms\";\nimport * as i14 from \"@angular/material/paginator\";\nimport * as i15 from \"@angular/material/progress-spinner\";\nimport * as i16 from \"@angular/material/select\";\nimport * as i17 from \"@angular/material/tooltip\";\nfunction TeacherNoticeComponent_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r0.filteredNotices.length, \")\");\n  }\n}\nfunction TeacherNoticeComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵelement(1, \"mat-spinner\", 14);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading notices...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TeacherNoticeComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"mat-icon\", 16);\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"Error Loading Notices\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function TeacherNoticeComponent_div_15_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.refreshData());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.error);\n  }\n}\nfunction TeacherNoticeComponent_div_16_mat_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", category_r11);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", category_r11, \" \");\n  }\n}\nfunction TeacherNoticeComponent_div_16_mat_option_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const priority_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", priority_r12);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", priority_r12, \" \");\n  }\n}\nfunction TeacherNoticeComponent_div_16_div_31_mat_card_1_mat_icon_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 54);\n    i0.ɵɵtext(1, \"push_pin\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TeacherNoticeComponent_div_16_div_31_mat_card_1_mat_icon_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 55);\n    i0.ɵɵtext(1, \"fiber_manual_record\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TeacherNoticeComponent_div_16_div_31_mat_card_1_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const notice_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", notice_r14.totalViews, \" views\");\n  }\n}\nfunction TeacherNoticeComponent_div_16_div_31_mat_card_1_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const notice_r14 = i0.ɵɵnextContext().$implicit;\n    const ctx_r18 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Expires on \", ctx_r18.formatDate(notice_r14.expiryDate), \"\");\n  }\n}\nfunction TeacherNoticeComponent_div_16_div_31_mat_card_1_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"This notice has expired\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TeacherNoticeComponent_div_16_div_31_mat_card_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 36);\n    i0.ɵɵlistener(\"click\", function TeacherNoticeComponent_div_16_div_31_mat_card_1_Template_mat_card_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r23);\n      const notice_r14 = restoredCtx.$implicit;\n      const ctx_r22 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r22.markAsRead(notice_r14));\n    });\n    i0.ɵɵelementStart(1, \"mat-card-header\")(2, \"div\", 37)(3, \"div\", 38)(4, \"mat-icon\", 39);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 40)(7, \"h3\", 41);\n    i0.ɵɵtemplate(8, TeacherNoticeComponent_div_16_div_31_mat_card_1_mat_icon_8_Template, 2, 0, \"mat-icon\", 42);\n    i0.ɵɵtext(9);\n    i0.ɵɵtemplate(10, TeacherNoticeComponent_div_16_div_31_mat_card_1_mat_icon_10_Template, 2, 0, \"mat-icon\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 44)(12, \"span\", 45);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 46);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 47);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(18, \"mat-card-content\")(19, \"div\", 48)(20, \"p\");\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 49)(23, \"div\", 50)(24, \"mat-icon\");\n    i0.ɵɵtext(25, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\");\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(28, TeacherNoticeComponent_div_16_div_31_mat_card_1_div_28_Template, 5, 1, \"div\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(29, TeacherNoticeComponent_div_16_div_31_mat_card_1_div_29_Template, 5, 1, \"div\", 52);\n    i0.ɵɵtemplate(30, TeacherNoticeComponent_div_16_div_31_mat_card_1_div_30_Template, 5, 0, \"div\", 53);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const notice_r14 = ctx.$implicit;\n    const ctx_r13 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"unread\", !ctx_r13.isNoticeRead(notice_r14))(\"pinned\", notice_r14.isPinned)(\"expired\", ctx_r13.isNoticeExpired(notice_r14));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r13.getCategoryIcon(notice_r14.category));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", notice_r14.isPinned);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", notice_r14.title, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r13.isNoticeRead(notice_r14));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r13.getPriorityClass(notice_r14.priority));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", notice_r14.priority, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(notice_r14.category);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r13.formatDate(notice_r14.publishDate));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(notice_r14.content);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\"\", notice_r14.createdBy.name, \" (\", notice_r14.createdBy.role, \")\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", notice_r14.totalViews);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", notice_r14.expiryDate && !ctx_r13.isNoticeExpired(notice_r14));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r13.isNoticeExpired(notice_r14));\n  }\n}\nfunction TeacherNoticeComponent_div_16_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtemplate(1, TeacherNoticeComponent_div_16_div_31_mat_card_1_Template, 31, 20, \"mat-card\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.paginatedNotices);\n  }\n}\nfunction TeacherNoticeComponent_div_16_div_32_p_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"No notices match your current filters.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TeacherNoticeComponent_div_16_div_32_p_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"No notices available at the moment.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TeacherNoticeComponent_div_16_div_32_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function TeacherNoticeComponent_div_16_div_32_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r27.clearFilters());\n    });\n    i0.ɵɵtext(1, \" Clear Filters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TeacherNoticeComponent_div_16_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"mat-icon\", 60);\n    i0.ɵɵtext(2, \"campaign\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"No Notices Found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, TeacherNoticeComponent_div_16_div_32_p_5_Template, 2, 0, \"p\", 11);\n    i0.ɵɵtemplate(6, TeacherNoticeComponent_div_16_div_32_p_6_Template, 2, 0, \"p\", 11);\n    i0.ɵɵtemplate(7, TeacherNoticeComponent_div_16_div_32_button_7_Template, 2, 0, \"button\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.searchQuery || ctx_r9.selectedCategory || ctx_r9.selectedPriority);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.searchQuery && !ctx_r9.selectedCategory && !ctx_r9.selectedPriority);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.searchQuery || ctx_r9.selectedCategory || ctx_r9.selectedPriority);\n  }\n}\nconst _c0 = function () {\n  return [5, 10, 20];\n};\nfunction TeacherNoticeComponent_div_16_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"mat-paginator\", 64);\n    i0.ɵɵlistener(\"page\", function TeacherNoticeComponent_div_16_div_33_Template_mat_paginator_page_1_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r29 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r29.goToPage($event.pageIndex + 1));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"length\", ctx_r10.filteredNotices.length)(\"pageSize\", ctx_r10.itemsPerPage)(\"pageSizeOptions\", i0.ɵɵpureFunction0(4, _c0))(\"pageIndex\", ctx_r10.currentPage - 1);\n  }\n}\nfunction TeacherNoticeComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"mat-card\", 18)(2, \"mat-card-content\")(3, \"div\", 19)(4, \"div\", 20)(5, \"mat-form-field\", 21)(6, \"mat-label\");\n    i0.ɵɵtext(7, \"Search Notices\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"input\", 22);\n    i0.ɵɵlistener(\"ngModelChange\", function TeacherNoticeComponent_div_16_Template_input_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.searchQuery = $event);\n    })(\"input\", function TeacherNoticeComponent_div_16_Template_input_input_8_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r33 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r33.onSearch());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-icon\", 23);\n    i0.ɵɵtext(10, \"search\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 24)(12, \"mat-form-field\", 21)(13, \"mat-label\");\n    i0.ɵɵtext(14, \"Category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"mat-select\", 25);\n    i0.ɵɵlistener(\"ngModelChange\", function TeacherNoticeComponent_div_16_Template_mat_select_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r34.selectedCategory = $event);\n    })(\"selectionChange\", function TeacherNoticeComponent_div_16_Template_mat_select_selectionChange_15_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.onFilterChange());\n    });\n    i0.ɵɵelementStart(16, \"mat-option\", 26);\n    i0.ɵɵtext(17, \"All Categories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, TeacherNoticeComponent_div_16_mat_option_18_Template, 2, 2, \"mat-option\", 27);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"div\", 24)(20, \"mat-form-field\", 21)(21, \"mat-label\");\n    i0.ɵɵtext(22, \"Priority\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"mat-select\", 25);\n    i0.ɵɵlistener(\"ngModelChange\", function TeacherNoticeComponent_div_16_Template_mat_select_ngModelChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.selectedPriority = $event);\n    })(\"selectionChange\", function TeacherNoticeComponent_div_16_Template_mat_select_selectionChange_23_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r37 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r37.onFilterChange());\n    });\n    i0.ɵɵelementStart(24, \"mat-option\", 26);\n    i0.ɵɵtext(25, \"All Priorities\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, TeacherNoticeComponent_div_16_mat_option_26_Template, 2, 2, \"mat-option\", 27);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(27, \"div\", 28)(28, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function TeacherNoticeComponent_div_16_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.clearFilters());\n    });\n    i0.ɵɵelementStart(29, \"mat-icon\");\n    i0.ɵɵtext(30, \"clear\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵtemplate(31, TeacherNoticeComponent_div_16_div_31_Template, 2, 1, \"div\", 30);\n    i0.ɵɵtemplate(32, TeacherNoticeComponent_div_16_div_32_Template, 8, 3, \"div\", 31);\n    i0.ɵɵtemplate(33, TeacherNoticeComponent_div_16_div_33_Template, 2, 5, \"div\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.searchQuery);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.selectedCategory);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.categories);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.selectedPriority);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.priorities);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.paginatedNotices.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.loading && ctx_r3.paginatedNotices.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.totalPages > 1);\n  }\n}\nexport class TeacherNoticeComponent {\n  constructor(router, noticeService, userService, timetableService, snackBar) {\n    this.router = router;\n    this.noticeService = noticeService;\n    this.userService = userService;\n    this.timetableService = timetableService;\n    this.snackBar = snackBar;\n    // UI State\n    this.loading = true;\n    this.error = null;\n    this.notices = [];\n    this.filteredNotices = [];\n    this.teacherPrograms = [];\n    this.teacherDepartments = [];\n    this.teacherClasses = [];\n    this.teacherSubjects = [];\n    // Filters\n    this.searchQuery = '';\n    this.selectedCategory = '';\n    this.selectedPriority = '';\n    // Pagination\n    this.currentPage = 1;\n    this.itemsPerPage = 10;\n    this.totalItems = 0;\n    // Filter options\n    this.categories = ['General', 'Academic', 'Administrative', 'Event', 'Holiday', 'Examination', 'Emergency'];\n    this.priorities = ['Low', 'Medium', 'High', 'Urgent'];\n  }\n  ngOnInit() {\n    this.currentUser = this.userService.getUserFromLocalStorage()?.user;\n    if (this.currentUser) {\n      this.loadTeacherAssignments();\n    } else {\n      this.error = 'User not found';\n      this.loading = false;\n    }\n  }\n  loadTeacherAssignments() {\n    this.loading = true;\n    this.error = null;\n    // First, get teacher's timetable to understand their assignments\n    this.timetableService.getTimetableByTeacher(this.currentUser._id).subscribe({\n      next: response => {\n        if (response.success) {\n          this.processTeacherAssignments(response.timetable);\n          this.loadNotices();\n        } else {\n          this.loadNotices(); // Load general notices if no specific assignments\n        }\n      },\n\n      error: error => {\n        console.error('Error loading teacher assignments:', error);\n        this.loadNotices(); // Load general notices on error\n      }\n    });\n  }\n\n  processTeacherAssignments(timetableData) {\n    const programs = new Set();\n    const departments = new Set();\n    const classes = new Set();\n    const subjects = new Set();\n    timetableData.forEach(entry => {\n      if (entry.program?._id) programs.add(entry.program._id);\n      if (entry.department?._id) departments.add(entry.department._id);\n      if (entry.class?._id) classes.add(entry.class._id);\n      if (entry.subject?._id) subjects.add(entry.subject._id);\n    });\n    this.teacherPrograms = Array.from(programs);\n    this.teacherDepartments = Array.from(departments);\n    this.teacherClasses = Array.from(classes);\n    this.teacherSubjects = Array.from(subjects);\n  }\n  loadNotices() {\n    this.loading = true;\n    this.error = null;\n    // Get notices for teachers with enhanced filtering\n    const filters = {\n      targetAudience: 'teachers',\n      isPublished: true,\n      page: this.currentPage,\n      limit: this.itemsPerPage\n    };\n    this.noticeService.getAllNotices(filters).subscribe({\n      next: response => {\n        if (response.success) {\n          this.notices = response?.notices ?? [];\n          this.totalItems = response?.pagination?.totalNotices || response?.notices?.length || 0;\n          this.filterNoticesForTeacher();\n          this.applyFilters();\n        } else {\n          this.error = 'Failed to load notices';\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading notices:', error);\n        this.error = 'Error loading notices data';\n        this.loading = false;\n        this.snackBar.open('Failed to load notices', 'Close', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  filterNoticesForTeacher() {\n    // Filter notices based on teacher's assignments\n    this.notices = this.notices.filter(notice => {\n      // Always show general notices for all teachers\n      if (!notice.targetPrograms && !notice.targetDepartments && !notice.targetClasses && !notice.targetSubjects) {\n        return true;\n      }\n      // Check if notice targets teacher's programs\n      if (notice.targetPrograms && notice.targetPrograms.length > 0) {\n        const hasMatchingProgram = notice.targetPrograms.some(programId => this.teacherPrograms.includes(programId));\n        if (hasMatchingProgram) return true;\n      }\n      // Check if notice targets teacher's departments\n      if (notice.targetDepartments && notice.targetDepartments.length > 0) {\n        const hasMatchingDepartment = notice.targetDepartments.some(deptId => this.teacherDepartments.includes(deptId));\n        if (hasMatchingDepartment) return true;\n      }\n      // Check if notice targets teacher's classes\n      if (notice.targetClasses && notice.targetClasses.length > 0) {\n        const hasMatchingClass = notice.targetClasses.some(classId => this.teacherClasses.includes(classId));\n        if (hasMatchingClass) return true;\n      }\n      // Check if notice targets teacher's subjects\n      if (notice.targetSubjects && notice.targetSubjects.length > 0) {\n        const hasMatchingSubject = notice.targetSubjects.some(subjectId => this.teacherSubjects.includes(subjectId));\n        if (hasMatchingSubject) return true;\n      }\n      return false;\n    });\n  }\n  applyFilters() {\n    let filtered = [...this.notices];\n    if (this.searchQuery) {\n      const query = this.searchQuery.toLowerCase();\n      filtered = filtered.filter(notice => notice.title.toLowerCase().includes(query) || notice.content.toLowerCase().includes(query));\n    }\n    if (this.selectedCategory) {\n      filtered = filtered.filter(notice => notice.category === this.selectedCategory);\n    }\n    if (this.selectedPriority) {\n      filtered = filtered.filter(notice => notice.priority === this.selectedPriority);\n    }\n    this.filteredNotices = filtered;\n  }\n  onSearch() {\n    this.applyFilters();\n  }\n  onFilterChange() {\n    this.applyFilters();\n  }\n  clearFilters() {\n    this.searchQuery = '';\n    this.selectedCategory = '';\n    this.selectedPriority = '';\n    this.applyFilters();\n  }\n  markAsRead(notice) {\n    if (this.currentUser && !this.isNoticeRead(notice)) {\n      this.noticeService.markAsRead(notice._id, this.currentUser._id).subscribe({\n        next: response => {\n          if (response.success) {\n            // Update the notice in the local array\n            const index = this.notices.findIndex(n => n._id === notice._id);\n            if (index !== -1) {\n              this.notices[index].readBy = this.notices[index].readBy || [];\n              this.notices[index].readBy.push({\n                user: this.currentUser._id,\n                readAt: new Date()\n              });\n              this.notices[index].totalViews = (this.notices[index].totalViews || 0) + 1;\n            }\n          }\n        },\n        error: error => {\n          console.error('Error marking notice as read:', error);\n        }\n      });\n    }\n  }\n  isNoticeRead(notice) {\n    if (!this.currentUser || !notice.readBy) return false;\n    return notice.readBy.some(read => read.user === this.currentUser._id);\n  }\n  isNoticeExpired(notice) {\n    if (!notice.expiryDate) return false;\n    return new Date() > new Date(notice.expiryDate);\n  }\n  refreshData() {\n    this.loadNotices();\n  }\n  // Pagination\n  get paginatedNotices() {\n    const startIndex = (this.currentPage - 1) * this.itemsPerPage;\n    return this.filteredNotices.slice(startIndex, startIndex + this.itemsPerPage);\n  }\n  get totalPages() {\n    return Math.ceil(this.filteredNotices.length / this.itemsPerPage);\n  }\n  nextPage() {\n    if (this.currentPage < this.totalPages) {\n      this.currentPage++;\n    }\n  }\n  previousPage() {\n    if (this.currentPage > 1) {\n      this.currentPage--;\n    }\n  }\n  goToPage(page) {\n    if (page >= 1 && page <= this.totalPages) {\n      this.currentPage = page;\n    }\n  }\n  // Utility methods\n  getPriorityClass(priority) {\n    switch (priority.toLowerCase()) {\n      case 'urgent':\n        return 'priority-urgent';\n      case 'high':\n        return 'priority-high';\n      case 'medium':\n        return 'priority-medium';\n      case 'low':\n        return 'priority-low';\n      default:\n        return 'priority-medium';\n    }\n  }\n  getCategoryIcon(category) {\n    switch (category.toLowerCase()) {\n      case 'academic':\n        return 'school';\n      case 'administrative':\n        return 'business';\n      case 'event':\n        return 'event';\n      case 'holiday':\n        return 'beach_access';\n      case 'examination':\n        return 'quiz';\n      case 'emergency':\n        return 'warning';\n      default:\n        return 'announcement';\n    }\n  }\n  formatDate(date) {\n    return new Date(date).toLocaleDateString();\n  }\n  formatDateTime(date) {\n    return new Date(date).toLocaleString();\n  }\n  static #_ = this.ɵfac = function TeacherNoticeComponent_Factory(t) {\n    return new (t || TeacherNoticeComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.NoticeService), i0.ɵɵdirectiveInject(i3.UserService), i0.ɵɵdirectiveInject(i4.TimetableService), i0.ɵɵdirectiveInject(i5.MatSnackBar));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: TeacherNoticeComponent,\n    selectors: [[\"app-teacher-notice\"]],\n    decls: 17,\n    vars: 4,\n    consts: [[1, \"teacher-notices-container\"], [1, \"page-header\"], [1, \"header-content\"], [1, \"page-title\"], [1, \"title-icon\"], [\"class\", \"notice-count\", 4, \"ngIf\"], [1, \"page-subtitle\"], [1, \"header-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Refresh\", 1, \"refresh-btn\", 3, \"click\"], [\"class\", \"loading-state\", 4, \"ngIf\"], [\"class\", \"error-state\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"notice-count\"], [1, \"loading-state\"], [\"diameter\", \"40\"], [1, \"error-state\"], [1, \"error-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"filters-card\"], [1, \"filters-row\"], [1, \"search-field\"], [\"appearance\", \"outline\", 1, \"compact-field\"], [\"matInput\", \"\", \"placeholder\", \"Search by title or content...\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [\"matSuffix\", \"\"], [1, \"filter-field\"], [3, \"ngModel\", \"ngModelChange\", \"selectionChange\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"filter-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Clear Filters\", 1, \"clear-btn\", 3, \"click\"], [\"class\", \"notices-list\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"pagination-container\", 4, \"ngIf\"], [3, \"value\"], [1, \"notices-list\"], [\"class\", \"notice-card\", 3, \"unread\", \"pinned\", \"expired\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"notice-card\", 3, \"click\"], [1, \"notice-header\"], [1, \"notice-meta\"], [1, \"category-icon\"], [1, \"notice-info\"], [1, \"notice-title\"], [\"class\", \"pin-icon\", 4, \"ngIf\"], [\"class\", \"unread-indicator\", 4, \"ngIf\"], [1, \"notice-badges\"], [1, \"priority-badge\", 3, \"ngClass\"], [1, \"category-badge\"], [1, \"date-badge\"], [1, \"notice-content\"], [1, \"notice-footer\"], [1, \"author-info\"], [\"class\", \"notice-stats\", 4, \"ngIf\"], [\"class\", \"expiry-warning\", 4, \"ngIf\"], [\"class\", \"expired-notice\", 4, \"ngIf\"], [1, \"pin-icon\"], [1, \"unread-indicator\"], [1, \"notice-stats\"], [1, \"expiry-warning\"], [1, \"expired-notice\"], [1, \"empty-state\"], [1, \"empty-icon\"], [\"mat-button\", \"\", 3, \"click\", 4, \"ngIf\"], [\"mat-button\", \"\", 3, \"click\"], [1, \"pagination-container\"], [\"showFirstLastButtons\", \"\", 3, \"length\", \"pageSize\", \"pageSizeOptions\", \"pageIndex\", \"page\"]],\n    template: function TeacherNoticeComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3)(4, \"mat-icon\", 4);\n        i0.ɵɵtext(5, \"campaign\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(6, \" Notices \");\n        i0.ɵɵtemplate(7, TeacherNoticeComponent_span_7_Template, 2, 1, \"span\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"p\", 6);\n        i0.ɵɵtext(9, \"Important announcements and updates\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 7)(11, \"button\", 8);\n        i0.ɵɵlistener(\"click\", function TeacherNoticeComponent_Template_button_click_11_listener() {\n          return ctx.refreshData();\n        });\n        i0.ɵɵelementStart(12, \"mat-icon\");\n        i0.ɵɵtext(13, \"refresh\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵtemplate(14, TeacherNoticeComponent_div_14_Template, 4, 0, \"div\", 9);\n        i0.ɵɵtemplate(15, TeacherNoticeComponent_div_15_Template, 11, 1, \"div\", 10);\n        i0.ɵɵtemplate(16, TeacherNoticeComponent_div_16_Template, 34, 8, \"div\", 11);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error);\n      }\n    },\n    dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i7.MatOption, i8.MatButton, i8.MatIconButton, i9.MatCard, i9.MatCardContent, i9.MatCardHeader, i10.MatIcon, i11.MatInput, i12.MatFormField, i12.MatLabel, i12.MatSuffix, i13.DefaultValueAccessor, i13.NgControlStatus, i13.NgModel, i14.MatPaginator, i15.MatProgressSpinner, i16.MatSelect, i17.MatTooltip],\n    styles: [\".teacher-notices-container[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  min-height: 100vh;\\n  padding: 15px;\\n}\\n\\n\\n\\n.page-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n  padding: 15px 0;\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.page-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #2c3e50;\\n  font-size: 1.8rem;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n\\n.title-icon[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  color: #3498db;\\n}\\n\\n.notice-count[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);\\n  color: white;\\n  padding: 2px 8px;\\n  border-radius: 10px;\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n  margin-left: 10px;\\n}\\n\\n.page-subtitle[_ngcontent-%COMP%] {\\n  margin: 5px 0 0 0;\\n  color: #7f8c8d;\\n  font-size: 0.9rem;\\n}\\n\\n.header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.refresh-btn[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n\\n.refresh-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n\\n\\n\\n.filters-card[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.filters-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  padding: 15px !important;\\n}\\n\\n.filters-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n}\\n\\n.search-field[_ngcontent-%COMP%] {\\n  flex: 2;\\n}\\n\\n.filter-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.compact-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.compact-field[_ngcontent-%COMP%]   .mat-form-field-wrapper[_ngcontent-%COMP%] {\\n  padding-bottom: 0;\\n}\\n\\n.compact-field[_ngcontent-%COMP%]   .mat-form-field-infix[_ngcontent-%COMP%] {\\n  padding: 8px 0;\\n  border-top: none;\\n}\\n\\n.compact-field[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n\\n.clear-btn[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  transition: all 0.3s ease;\\n}\\n\\n.clear-btn[_ngcontent-%COMP%]:hover {\\n  color: #495057;\\n  background-color: #f8f9fa;\\n}\\n\\n\\n\\n.notices-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 15px;\\n  margin-bottom: 20px;\\n}\\n\\n.notice-card[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease;\\n  border-left: 4px solid #ecf0f1;\\n  cursor: pointer;\\n}\\n\\n.notice-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\\n}\\n\\n.notice-card.unread[_ngcontent-%COMP%] {\\n  border-left-color: #3498db;\\n  background-color: #f8f9fa;\\n}\\n\\n.notice-card.pinned[_ngcontent-%COMP%] {\\n  border-left-color: #f39c12;\\n}\\n\\n.notice-card.expired[_ngcontent-%COMP%] {\\n  border-left-color: #e74c3c;\\n  opacity: 0.7;\\n}\\n\\n.notice-header[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.notice-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 12px;\\n  width: 100%;\\n}\\n\\n.category-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  color: #3498db;\\n  margin-top: 2px;\\n}\\n\\n.notice-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.notice-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #2c3e50;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.pin-icon[_ngcontent-%COMP%] {\\n  color: #f39c12;\\n  font-size: 1rem;\\n}\\n\\n.unread-indicator[_ngcontent-%COMP%] {\\n  color: #3498db;\\n  font-size: 0.8rem;\\n}\\n\\n.notice-badges[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  margin-top: 8px;\\n  flex-wrap: wrap;\\n}\\n\\n.priority-badge[_ngcontent-%COMP%], .category-badge[_ngcontent-%COMP%], .date-badge[_ngcontent-%COMP%] {\\n  padding: 2px 8px;\\n  border-radius: 10px;\\n  font-size: 0.7rem;\\n  font-weight: 600;\\n}\\n\\n.priority-urgent[_ngcontent-%COMP%] {\\n  background: #e74c3c;\\n  color: white;\\n}\\n\\n.priority-high[_ngcontent-%COMP%] {\\n  background: #f39c12;\\n  color: white;\\n}\\n\\n.priority-medium[_ngcontent-%COMP%] {\\n  background: #3498db;\\n  color: white;\\n}\\n\\n.priority-low[_ngcontent-%COMP%] {\\n  background: #95a5a6;\\n  color: white;\\n}\\n\\n.category-badge[_ngcontent-%COMP%] {\\n  background: #8e44ad;\\n  color: white;\\n}\\n\\n.date-badge[_ngcontent-%COMP%] {\\n  background: #34495e;\\n  color: white;\\n}\\n\\n.notice-content[_ngcontent-%COMP%] {\\n  margin: 15px 0;\\n}\\n\\n.notice-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #34495e;\\n  line-height: 1.6;\\n}\\n\\n.notice-footer[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-top: 15px;\\n  padding-top: 15px;\\n  border-top: 1px solid #ecf0f1;\\n}\\n\\n.author-info[_ngcontent-%COMP%], .notice-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  color: #7f8c8d;\\n  font-size: 0.8rem;\\n}\\n\\n.author-info[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%], .notice-stats[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.expiry-warning[_ngcontent-%COMP%], .expired-notice[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  margin-top: 10px;\\n  padding: 8px 12px;\\n  border-radius: 6px;\\n  font-size: 0.8rem;\\n}\\n\\n.expiry-warning[_ngcontent-%COMP%] {\\n  background: #fff3cd;\\n  color: #856404;\\n  border: 1px solid #ffeaa7;\\n}\\n\\n.expired-notice[_ngcontent-%COMP%] {\\n  background: #f8d7da;\\n  color: #721c24;\\n  border: 1px solid #f5c6cb;\\n}\\n\\n.expiry-warning[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%], .expired-notice[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n\\n\\n.loading-state[_ngcontent-%COMP%], .error-state[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n  text-align: center;\\n}\\n\\n.loading-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .error-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n  color: #7f8c8d;\\n}\\n\\n.error-state[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  margin-bottom: 10px;\\n}\\n\\n.error-icon[_ngcontent-%COMP%], .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  color: #bdc3c7;\\n  margin-bottom: 15px;\\n}\\n\\n.error-icon[_ngcontent-%COMP%] {\\n  color: #e74c3c;\\n}\\n\\n\\n\\n.pagination-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-top: 20px;\\n}\\n\\n\\n\\n@media (max-width: 1200px) {\\n  .teacher-notices-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .page-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 15px;\\n  }\\n\\n  .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  .header-actions[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: space-between;\\n  }\\n\\n  .filters-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 10px;\\n  }\\n\\n  .search-field[_ngcontent-%COMP%], .filter-field[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n\\n  .filter-actions[_ngcontent-%COMP%] {\\n    width: 100%;\\n    display: flex;\\n    justify-content: flex-end;\\n  }\\n\\n  .notice-meta[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 8px;\\n  }\\n\\n  .category-icon[_ngcontent-%COMP%] {\\n    align-self: flex-start;\\n  }\\n\\n  .notice-footer[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 8px;\\n    align-items: flex-start;\\n  }\\n\\n  .notice-badges[_ngcontent-%COMP%] {\\n    gap: 5px;\\n  }\\n}\\n\\n@media (max-width: 576px) {\\n  .teacher-notices-container[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n\\n  .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.3rem;\\n  }\\n\\n  .title-icon[_ngcontent-%COMP%] {\\n    font-size: 1.3rem;\\n  }\\n\\n  .notice-count[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n    padding: 1px 6px;\\n  }\\n\\n  .notice-card[_ngcontent-%COMP%] {\\n    margin-bottom: 15px;\\n  }\\n\\n  .notice-title[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n\\n  .notice-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n\\n  .priority-badge[_ngcontent-%COMP%], .category-badge[_ngcontent-%COMP%], .date-badge[_ngcontent-%COMP%] {\\n    font-size: 0.65rem;\\n    padding: 1px 6px;\\n  }\\n\\n  .author-info[_ngcontent-%COMP%], .notice-stats[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n  }\\n\\n  .expiry-warning[_ngcontent-%COMP%], .expired-notice[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n    padding: 6px 10px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "filteredNotices", "length", "ɵɵelement", "ɵɵlistener", "TeacherNoticeComponent_div_15_Template_button_click_7_listener", "ɵɵrestoreView", "_r5", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "refreshData", "ɵɵtextInterpolate", "ctx_r2", "error", "ɵɵproperty", "category_r11", "priority_r12", "notice_r14", "totalViews", "ctx_r18", "formatDate", "expiryDate", "TeacherNoticeComponent_div_16_div_31_mat_card_1_Template_mat_card_click_0_listener", "restoredCtx", "_r23", "$implicit", "ctx_r22", "mark<PERSON><PERSON><PERSON>", "ɵɵtemplate", "TeacherNoticeComponent_div_16_div_31_mat_card_1_mat_icon_8_Template", "TeacherNoticeComponent_div_16_div_31_mat_card_1_mat_icon_10_Template", "TeacherNoticeComponent_div_16_div_31_mat_card_1_div_28_Template", "TeacherNoticeComponent_div_16_div_31_mat_card_1_div_29_Template", "TeacherNoticeComponent_div_16_div_31_mat_card_1_div_30_Template", "ɵɵclassProp", "ctx_r13", "isNoticeRead", "isPinned", "isNoticeExpired", "getCategoryIcon", "category", "title", "getPriorityClass", "priority", "publishDate", "content", "ɵɵtextInterpolate2", "created<PERSON>y", "name", "role", "TeacherNoticeComponent_div_16_div_31_mat_card_1_Template", "ctx_r8", "paginatedNotices", "TeacherNoticeComponent_div_16_div_32_button_7_Template_button_click_0_listener", "_r28", "ctx_r27", "clearFilters", "TeacherNoticeComponent_div_16_div_32_p_5_Template", "TeacherNoticeComponent_div_16_div_32_p_6_Template", "TeacherNoticeComponent_div_16_div_32_button_7_Template", "ctx_r9", "searchQuery", "selectedCate<PERSON><PERSON>", "selectedPriority", "TeacherNoticeComponent_div_16_div_33_Template_mat_paginator_page_1_listener", "$event", "_r30", "ctx_r29", "goToPage", "pageIndex", "ctx_r10", "itemsPerPage", "ɵɵpureFunction0", "_c0", "currentPage", "TeacherNoticeComponent_div_16_Template_input_ngModelChange_8_listener", "_r32", "ctx_r31", "TeacherNoticeComponent_div_16_Template_input_input_8_listener", "ctx_r33", "onSearch", "TeacherNoticeComponent_div_16_Template_mat_select_ngModelChange_15_listener", "ctx_r34", "TeacherNoticeComponent_div_16_Template_mat_select_selectionChange_15_listener", "ctx_r35", "onFilterChange", "TeacherNoticeComponent_div_16_mat_option_18_Template", "TeacherNoticeComponent_div_16_Template_mat_select_ngModelChange_23_listener", "ctx_r36", "TeacherNoticeComponent_div_16_Template_mat_select_selectionChange_23_listener", "ctx_r37", "TeacherNoticeComponent_div_16_mat_option_26_Template", "TeacherNoticeComponent_div_16_Template_button_click_28_listener", "ctx_r38", "TeacherNoticeComponent_div_16_div_31_Template", "TeacherNoticeComponent_div_16_div_32_Template", "TeacherNoticeComponent_div_16_div_33_Template", "ctx_r3", "categories", "priorities", "loading", "totalPages", "TeacherNoticeComponent", "constructor", "router", "noticeService", "userService", "timetableService", "snackBar", "notices", "teacherPrograms", "teacherDepartments", "teacherClasses", "teacherSubjects", "totalItems", "ngOnInit", "currentUser", "getUserFromLocalStorage", "user", "loadTeacherAssignments", "getTimetableByTeacher", "_id", "subscribe", "next", "response", "success", "processTeacherAssignments", "timetable", "loadNotices", "console", "timetableData", "programs", "Set", "departments", "classes", "subjects", "for<PERSON>ach", "entry", "program", "add", "department", "class", "subject", "Array", "from", "filters", "targetAudience", "isPublished", "page", "limit", "getAllNotices", "pagination", "totalNotices", "filterNotices<PERSON>or<PERSON><PERSON><PERSON>", "applyFilters", "open", "duration", "filter", "notice", "targetPrograms", "targetDepartments", "targetClasses", "targetSubjects", "hasMatchingProgram", "some", "programId", "includes", "hasMatchingDepartment", "deptId", "hasMatchingClass", "classId", "hasMatchingSubject", "subjectId", "filtered", "query", "toLowerCase", "index", "findIndex", "n", "readBy", "push", "readAt", "Date", "read", "startIndex", "slice", "Math", "ceil", "nextPage", "previousPage", "date", "toLocaleDateString", "formatDateTime", "toLocaleString", "_", "ɵɵdirectiveInject", "i1", "Router", "i2", "NoticeService", "i3", "UserService", "i4", "TimetableService", "i5", "MatSnackBar", "_2", "selectors", "decls", "vars", "consts", "template", "TeacherNoticeComponent_Template", "rf", "ctx", "TeacherNoticeComponent_span_7_Template", "TeacherNoticeComponent_Template_button_click_11_listener", "TeacherNoticeComponent_div_14_Template", "TeacherNoticeComponent_div_15_Template", "TeacherNoticeComponent_div_16_Template"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\teacher-dashboard\\teacher-notice\\teacher-notice.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\teacher-dashboard\\teacher-notice\\teacher-notice.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { NoticeService } from '../../../services/notice.service';\r\nimport { UserService } from '../../../services/user.service';\r\nimport { TimetableService } from '../../../services/timetable.service';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\n\r\ninterface Notice {\r\n  _id: string;\r\n  title: string;\r\n  content: string;\r\n  category: string;\r\n  priority: string;\r\n  targetAudience: string[];\r\n  publishDate: Date;\r\n  expiryDate?: Date;\r\n  isPublished: boolean;\r\n  isPinned: boolean;\r\n  createdBy: {\r\n    _id: string;\r\n    name: string;\r\n    role: string;\r\n  };\r\n  readBy?: any[];\r\n  totalViews?: number;\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-teacher-notice',\r\n  templateUrl: './teacher-notice.component.html',\r\n  styleUrls: ['./teacher-notice.component.css']\r\n})\r\nexport class TeacherNoticeComponent implements OnInit {\r\n  // UI State\r\n  loading = true;\r\n  error: string | null = null;\r\n\r\n  // Data\r\n  currentUser: any;\r\n  notices: any[] = [];\r\n  filteredNotices: Notice[] = [];\r\n  teacherPrograms: string[] = [];\r\n  teacherDepartments: string[] = [];\r\n  teacherClasses: string[] = [];\r\n  teacherSubjects: string[] = [];\r\n\r\n  // Filters\r\n  searchQuery = '';\r\n  selectedCategory = '';\r\n  selectedPriority = '';\r\n\r\n  // Pagination\r\n  currentPage = 1;\r\n  itemsPerPage = 10;\r\n  totalItems = 0;\r\n\r\n  // Filter options\r\n  categories = ['General', 'Academic', 'Administrative', 'Event', 'Holiday', 'Examination', 'Emergency'];\r\n  priorities = ['Low', 'Medium', 'High', 'Urgent'];\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private noticeService: NoticeService,\r\n    private userService: UserService,\r\n    private timetableService: TimetableService,\r\n    private snackBar: MatSnackBar\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.currentUser = this.userService.getUserFromLocalStorage()?.user;\r\n    if (this.currentUser) {\r\n      this.loadTeacherAssignments();\r\n    } else {\r\n      this.error = 'User not found';\r\n      this.loading = false;\r\n    }\r\n  }\r\n\r\n  loadTeacherAssignments(): void {\r\n    this.loading = true;\r\n    this.error = null;\r\n\r\n    // First, get teacher's timetable to understand their assignments\r\n    this.timetableService.getTimetableByTeacher(this.currentUser._id).subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.processTeacherAssignments(response.timetable);\r\n          this.loadNotices();\r\n        } else {\r\n          this.loadNotices(); // Load general notices if no specific assignments\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading teacher assignments:', error);\r\n        this.loadNotices(); // Load general notices on error\r\n      }\r\n    });\r\n  }\r\n\r\n  processTeacherAssignments(timetableData: any[]): void {\r\n    const programs = new Set<string>();\r\n    const departments = new Set<string>();\r\n    const classes = new Set<string>();\r\n    const subjects = new Set<string>();\r\n\r\n    timetableData.forEach(entry => {\r\n      if (entry.program?._id) programs.add(entry.program._id);\r\n      if (entry.department?._id) departments.add(entry.department._id);\r\n      if (entry.class?._id) classes.add(entry.class._id);\r\n      if (entry.subject?._id) subjects.add(entry.subject._id);\r\n    });\r\n\r\n    this.teacherPrograms = Array.from(programs);\r\n    this.teacherDepartments = Array.from(departments);\r\n    this.teacherClasses = Array.from(classes);\r\n    this.teacherSubjects = Array.from(subjects);\r\n  }\r\n\r\n  loadNotices(): void {\r\n    this.loading = true;\r\n    this.error = null;\r\n\r\n    // Get notices for teachers with enhanced filtering\r\n    const filters = {\r\n      targetAudience: 'teachers',\r\n      isPublished: true,\r\n      page: this.currentPage,\r\n      limit: this.itemsPerPage\r\n    };\r\n\r\n    this.noticeService.getAllNotices(filters).subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.notices = response?.notices ?? [];\r\n          this.totalItems = response?.pagination?.totalNotices || response?.notices?.length || 0;\r\n          this.filterNoticesForTeacher();\r\n          this.applyFilters();\r\n        } else {\r\n          this.error = 'Failed to load notices';\r\n        }\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading notices:', error);\r\n        this.error = 'Error loading notices data';\r\n        this.loading = false;\r\n        this.snackBar.open('Failed to load notices', 'Close', { duration: 3000 });\r\n      }\r\n    });\r\n  }\r\n\r\n  filterNoticesForTeacher(): void {\r\n    // Filter notices based on teacher's assignments\r\n    this.notices = this.notices.filter(notice => {\r\n      // Always show general notices for all teachers\r\n      if (!notice.targetPrograms && !notice.targetDepartments &&\r\n          !notice.targetClasses && !notice.targetSubjects) {\r\n        return true;\r\n      }\r\n\r\n      // Check if notice targets teacher's programs\r\n      if (notice.targetPrograms && notice.targetPrograms.length > 0) {\r\n        const hasMatchingProgram = notice.targetPrograms.some((programId: string) =>\r\n          this.teacherPrograms.includes(programId)\r\n        );\r\n        if (hasMatchingProgram) return true;\r\n      }\r\n\r\n      // Check if notice targets teacher's departments\r\n      if (notice.targetDepartments && notice.targetDepartments.length > 0) {\r\n        const hasMatchingDepartment = notice.targetDepartments.some((deptId: string) =>\r\n          this.teacherDepartments.includes(deptId)\r\n        );\r\n        if (hasMatchingDepartment) return true;\r\n      }\r\n\r\n      // Check if notice targets teacher's classes\r\n      if (notice.targetClasses && notice.targetClasses.length > 0) {\r\n        const hasMatchingClass = notice.targetClasses.some((classId: string) =>\r\n          this.teacherClasses.includes(classId)\r\n        );\r\n        if (hasMatchingClass) return true;\r\n      }\r\n\r\n      // Check if notice targets teacher's subjects\r\n      if (notice.targetSubjects && notice.targetSubjects.length > 0) {\r\n        const hasMatchingSubject = notice.targetSubjects.some((subjectId: string) =>\r\n          this.teacherSubjects.includes(subjectId)\r\n        );\r\n        if (hasMatchingSubject) return true;\r\n      }\r\n\r\n      return false;\r\n    });\r\n  }\r\n\r\n  applyFilters(): void {\r\n    let filtered = [...this.notices];\r\n\r\n    if (this.searchQuery) {\r\n      const query = this.searchQuery.toLowerCase();\r\n      filtered = filtered.filter(notice =>\r\n        notice.title.toLowerCase().includes(query) ||\r\n        notice.content.toLowerCase().includes(query)\r\n      );\r\n    }\r\n\r\n    if (this.selectedCategory) {\r\n      filtered = filtered.filter(notice => notice.category === this.selectedCategory);\r\n    }\r\n\r\n    if (this.selectedPriority) {\r\n      filtered = filtered.filter(notice => notice.priority === this.selectedPriority);\r\n    }\r\n\r\n    this.filteredNotices = filtered;\r\n  }\r\n\r\n  onSearch(): void {\r\n    this.applyFilters();\r\n  }\r\n\r\n  onFilterChange(): void {\r\n    this.applyFilters();\r\n  }\r\n\r\n  clearFilters(): void {\r\n    this.searchQuery = '';\r\n    this.selectedCategory = '';\r\n    this.selectedPriority = '';\r\n    this.applyFilters();\r\n  }\r\n\r\n  markAsRead(notice: Notice): void {\r\n    if (this.currentUser && !this.isNoticeRead(notice)) {\r\n      this.noticeService.markAsRead(notice._id, this.currentUser._id).subscribe({\r\n        next: (response) => {\r\n          if (response.success) {\r\n            // Update the notice in the local array\r\n            const index = this.notices.findIndex(n => n._id === notice._id);\r\n            if (index !== -1) {\r\n              this.notices[index].readBy = this.notices[index].readBy || [];\r\n              this.notices[index].readBy.push({\r\n                user: this.currentUser._id,\r\n                readAt: new Date()\r\n              });\r\n              this.notices[index].totalViews = (this.notices[index].totalViews || 0) + 1;\r\n            }\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('Error marking notice as read:', error);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  isNoticeRead(notice: Notice): boolean {\r\n    if (!this.currentUser || !notice.readBy) return false;\r\n    return notice.readBy.some((read: any) => read.user === this.currentUser._id);\r\n  }\r\n\r\n  isNoticeExpired(notice: Notice): boolean {\r\n    if (!notice.expiryDate) return false;\r\n    return new Date() > new Date(notice.expiryDate);\r\n  }\r\n\r\n  refreshData(): void {\r\n    this.loadNotices();\r\n  }\r\n\r\n  // Pagination\r\n  get paginatedNotices(): Notice[] {\r\n    const startIndex = (this.currentPage - 1) * this.itemsPerPage;\r\n    return this.filteredNotices.slice(startIndex, startIndex + this.itemsPerPage);\r\n  }\r\n\r\n  get totalPages(): number {\r\n    return Math.ceil(this.filteredNotices.length / this.itemsPerPage);\r\n  }\r\n\r\n  nextPage(): void {\r\n    if (this.currentPage < this.totalPages) {\r\n      this.currentPage++;\r\n    }\r\n  }\r\n\r\n  previousPage(): void {\r\n    if (this.currentPage > 1) {\r\n      this.currentPage--;\r\n    }\r\n  }\r\n\r\n  goToPage(page: number): void {\r\n    if (page >= 1 && page <= this.totalPages) {\r\n      this.currentPage = page;\r\n    }\r\n  }\r\n\r\n  // Utility methods\r\n  getPriorityClass(priority: string): string {\r\n    switch (priority.toLowerCase()) {\r\n      case 'urgent': return 'priority-urgent';\r\n      case 'high': return 'priority-high';\r\n      case 'medium': return 'priority-medium';\r\n      case 'low': return 'priority-low';\r\n      default: return 'priority-medium';\r\n    }\r\n  }\r\n\r\n  getCategoryIcon(category: string): string {\r\n    switch (category.toLowerCase()) {\r\n      case 'academic': return 'school';\r\n      case 'administrative': return 'business';\r\n      case 'event': return 'event';\r\n      case 'holiday': return 'beach_access';\r\n      case 'examination': return 'quiz';\r\n      case 'emergency': return 'warning';\r\n      default: return 'announcement';\r\n    }\r\n  }\r\n\r\n  formatDate(date: Date | string): string {\r\n    return new Date(date).toLocaleDateString();\r\n  }\r\n\r\n  formatDateTime(date: Date | string): string {\r\n    return new Date(date).toLocaleString();\r\n  }\r\n}\r\n", "<div class=\"teacher-notices-container\">\r\n  <!-- Header -->\r\n  <div class=\"page-header\">\r\n    <div class=\"header-content\">\r\n      <h1 class=\"page-title\">\r\n        <mat-icon class=\"title-icon\">campaign</mat-icon>\r\n        Notices\r\n        <span class=\"notice-count\" *ngIf=\"!loading\">({{ filteredNotices.length }})</span>\r\n      </h1>\r\n      <p class=\"page-subtitle\">Important announcements and updates</p>\r\n    </div>\r\n    <div class=\"header-actions\">\r\n      <button mat-icon-button (click)=\"refreshData()\" matTooltip=\"Refresh\" class=\"refresh-btn\">\r\n        <mat-icon>refresh</mat-icon>\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Loading State -->\r\n  <div *ngIf=\"loading\" class=\"loading-state\">\r\n    <mat-spinner diameter=\"40\"></mat-spinner>\r\n    <p>Loading notices...</p>\r\n  </div>\r\n\r\n  <!-- Error State -->\r\n  <div *ngIf=\"error && !loading\" class=\"error-state\">\r\n    <mat-icon class=\"error-icon\">error</mat-icon>\r\n    <h4>Error Loading Notices</h4>\r\n    <p>{{ error }}</p>\r\n    <button mat-raised-button color=\"primary\" (click)=\"refreshData()\">\r\n      <mat-icon>refresh</mat-icon>\r\n      Try Again\r\n    </button>\r\n  </div>\r\n\r\n  <!-- Main Content -->\r\n  <div *ngIf=\"!loading && !error\">\r\n    <!-- Filters -->\r\n    <mat-card class=\"filters-card\">\r\n      <mat-card-content>\r\n        <div class=\"filters-row\">\r\n          <div class=\"search-field\">\r\n            <mat-form-field appearance=\"outline\" class=\"compact-field\">\r\n              <mat-label>Search Notices</mat-label>\r\n              <input matInput [(ngModel)]=\"searchQuery\" (input)=\"onSearch()\"\r\n                     placeholder=\"Search by title or content...\">\r\n              <mat-icon matSuffix>search</mat-icon>\r\n            </mat-form-field>\r\n          </div>\r\n\r\n          <div class=\"filter-field\">\r\n            <mat-form-field appearance=\"outline\" class=\"compact-field\">\r\n              <mat-label>Category</mat-label>\r\n              <mat-select [(ngModel)]=\"selectedCategory\" (selectionChange)=\"onFilterChange()\">\r\n                <mat-option value=\"\">All Categories</mat-option>\r\n                <mat-option *ngFor=\"let category of categories\" [value]=\"category\">\r\n                  {{ category }}\r\n                </mat-option>\r\n              </mat-select>\r\n            </mat-form-field>\r\n          </div>\r\n\r\n          <div class=\"filter-field\">\r\n            <mat-form-field appearance=\"outline\" class=\"compact-field\">\r\n              <mat-label>Priority</mat-label>\r\n              <mat-select [(ngModel)]=\"selectedPriority\" (selectionChange)=\"onFilterChange()\">\r\n                <mat-option value=\"\">All Priorities</mat-option>\r\n                <mat-option *ngFor=\"let priority of priorities\" [value]=\"priority\">\r\n                  {{ priority }}\r\n                </mat-option>\r\n              </mat-select>\r\n            </mat-form-field>\r\n          </div>\r\n\r\n          <div class=\"filter-actions\">\r\n            <button mat-icon-button (click)=\"clearFilters()\" matTooltip=\"Clear Filters\" class=\"clear-btn\">\r\n              <mat-icon>clear</mat-icon>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n\r\n    <!-- Notices List -->\r\n    <div class=\"notices-list\" *ngIf=\"paginatedNotices.length > 0\">\r\n      <mat-card *ngFor=\"let notice of paginatedNotices\" class=\"notice-card\"\r\n                [class.unread]=\"!isNoticeRead(notice)\"\r\n                [class.pinned]=\"notice.isPinned\"\r\n                [class.expired]=\"isNoticeExpired(notice)\"\r\n                (click)=\"markAsRead(notice)\">\r\n\r\n        <mat-card-header>\r\n          <div class=\"notice-header\">\r\n            <div class=\"notice-meta\">\r\n              <mat-icon class=\"category-icon\">{{ getCategoryIcon(notice.category) }}</mat-icon>\r\n              <div class=\"notice-info\">\r\n                <h3 class=\"notice-title\">\r\n                  <mat-icon *ngIf=\"notice.isPinned\" class=\"pin-icon\">push_pin</mat-icon>\r\n                  {{ notice.title }}\r\n                  <mat-icon *ngIf=\"!isNoticeRead(notice)\" class=\"unread-indicator\">fiber_manual_record</mat-icon>\r\n                </h3>\r\n                <div class=\"notice-badges\">\r\n                  <span class=\"priority-badge\" [ngClass]=\"getPriorityClass(notice.priority)\">\r\n                    {{ notice.priority }}\r\n                  </span>\r\n                  <span class=\"category-badge\">{{ notice.category }}</span>\r\n                  <span class=\"date-badge\">{{ formatDate(notice.publishDate) }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </mat-card-header>\r\n\r\n        <mat-card-content>\r\n          <div class=\"notice-content\">\r\n            <p>{{ notice.content }}</p>\r\n          </div>\r\n\r\n          <div class=\"notice-footer\">\r\n            <div class=\"author-info\">\r\n              <mat-icon>person</mat-icon>\r\n              <span>{{ notice.createdBy.name }} ({{ notice.createdBy.role }})</span>\r\n            </div>\r\n            <div class=\"notice-stats\" *ngIf=\"notice.totalViews\">\r\n              <mat-icon>visibility</mat-icon>\r\n              <span>{{ notice.totalViews }} views</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"expiry-warning\" *ngIf=\"notice.expiryDate && !isNoticeExpired(notice)\">\r\n            <mat-icon>schedule</mat-icon>\r\n            <span>Expires on {{ formatDate(notice.expiryDate) }}</span>\r\n          </div>\r\n\r\n          <div class=\"expired-notice\" *ngIf=\"isNoticeExpired(notice)\">\r\n            <mat-icon>error</mat-icon>\r\n            <span>This notice has expired</span>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n    </div>\r\n\r\n    <!-- Empty State -->\r\n    <div *ngIf=\"!loading && paginatedNotices.length === 0\" class=\"empty-state\">\r\n      <mat-icon class=\"empty-icon\">campaign</mat-icon>\r\n      <h4>No Notices Found</h4>\r\n      <p *ngIf=\"searchQuery || selectedCategory || selectedPriority\">No notices match your current filters.</p>\r\n      <p *ngIf=\"!searchQuery && !selectedCategory && !selectedPriority\">No notices available at the moment.</p>\r\n      <button mat-button (click)=\"clearFilters()\" *ngIf=\"searchQuery || selectedCategory || selectedPriority\">\r\n        Clear Filters\r\n      </button>\r\n    </div>\r\n\r\n    <!-- Pagination -->\r\n    <div class=\"pagination-container\" *ngIf=\"totalPages > 1\">\r\n      <mat-paginator\r\n        [length]=\"filteredNotices.length\"\r\n        [pageSize]=\"itemsPerPage\"\r\n        [pageSizeOptions]=\"[5, 10, 20]\"\r\n        [pageIndex]=\"currentPage - 1\"\r\n        (page)=\"goToPage($event.pageIndex + 1)\"\r\n        showFirstLastButtons>\r\n      </mat-paginator>\r\n    </div>\r\n  </div>\r\n</div>"], "mappings": ";;;;;;;;;;;;;;;;;;;;ICOQA,EAAA,CAAAC,cAAA,eAA4C;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAArCH,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,eAAA,CAAAC,MAAA,MAA8B;;;;;IAYhFR,EAAA,CAAAC,cAAA,cAA2C;IACzCD,EAAA,CAAAS,SAAA,sBAAyC;IACzCT,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;IAI3BH,EAAA,CAAAC,cAAA,cAAmD;IACpBD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7CH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAClBH,EAAA,CAAAC,cAAA,iBAAkE;IAAxBD,EAAA,CAAAU,UAAA,mBAAAC,+DAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAC/DjB,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAE,MAAA,mBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAJNH,EAAA,CAAAI,SAAA,GAAW;IAAXJ,EAAA,CAAAkB,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IA2BFpB,EAAA,CAAAC,cAAA,qBAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFmCH,EAAA,CAAAqB,UAAA,UAAAC,YAAA,CAAkB;IAChEtB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAiB,YAAA,MACF;;;;;IAUAtB,EAAA,CAAAC,cAAA,qBAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFmCH,EAAA,CAAAqB,UAAA,UAAAE,YAAA,CAAkB;IAChEvB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAkB,YAAA,MACF;;;;;IA4BEvB,EAAA,CAAAC,cAAA,mBAAmD;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAEtEH,EAAA,CAAAC,cAAA,mBAAiE;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAwBrGH,EAAA,CAAAC,cAAA,cAAoD;IACxCD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAApCH,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAK,kBAAA,KAAAmB,UAAA,CAAAC,UAAA,WAA6B;;;;;IAIvCzB,EAAA,CAAAC,cAAA,cAAkF;IACtED,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAA8C;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAArDH,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAK,kBAAA,gBAAAqB,OAAA,CAAAC,UAAA,CAAAH,UAAA,CAAAI,UAAA,MAA8C;;;;;IAGtD5B,EAAA,CAAAC,cAAA,cAA4D;IAChDD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAnD1CH,EAAA,CAAAC,cAAA,mBAIuC;IAA7BD,EAAA,CAAAU,UAAA,mBAAAmB,mFAAA;MAAA,MAAAC,WAAA,GAAA9B,EAAA,CAAAY,aAAA,CAAAmB,IAAA;MAAA,MAAAP,UAAA,GAAAM,WAAA,CAAAE,SAAA;MAAA,MAAAC,OAAA,GAAAjC,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAiB,OAAA,CAAAC,UAAA,CAAAV,UAAA,CAAkB;IAAA,EAAC;IAEpCxB,EAAA,CAAAC,cAAA,sBAAiB;IAGqBD,EAAA,CAAAE,MAAA,GAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACjFH,EAAA,CAAAC,cAAA,cAAyB;IAErBD,EAAA,CAAAmC,UAAA,IAAAC,mEAAA,uBAAsE;IACtEpC,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAmC,UAAA,KAAAE,oEAAA,uBAA+F;IACjGrC,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,eAA2B;IAEvBD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAE,MAAA,IAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzDH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,IAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAO9EH,EAAA,CAAAC,cAAA,wBAAkB;IAEXD,EAAA,CAAAE,MAAA,IAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAG7BH,EAAA,CAAAC,cAAA,eAA2B;IAEbD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAyD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAExEH,EAAA,CAAAmC,UAAA,KAAAG,+DAAA,kBAGM;IACRtC,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAmC,UAAA,KAAAI,+DAAA,kBAGM;IAENvC,EAAA,CAAAmC,UAAA,KAAAK,+DAAA,kBAGM;IACRxC,EAAA,CAAAG,YAAA,EAAmB;;;;;IApDXH,EAAA,CAAAyC,WAAA,YAAAC,OAAA,CAAAC,YAAA,CAAAnB,UAAA,EAAsC,WAAAA,UAAA,CAAAoB,QAAA,aAAAF,OAAA,CAAAG,eAAA,CAAArB,UAAA;IAQRxB,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAkB,iBAAA,CAAAwB,OAAA,CAAAI,eAAA,CAAAtB,UAAA,CAAAuB,QAAA,EAAsC;IAGvD/C,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAqB,UAAA,SAAAG,UAAA,CAAAoB,QAAA,CAAqB;IAChC5C,EAAA,CAAAI,SAAA,GACA;IADAJ,EAAA,CAAAK,kBAAA,MAAAmB,UAAA,CAAAwB,KAAA,MACA;IAAWhD,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAqB,UAAA,UAAAqB,OAAA,CAAAC,YAAA,CAAAnB,UAAA,EAA2B;IAGTxB,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAqB,UAAA,YAAAqB,OAAA,CAAAO,gBAAA,CAAAzB,UAAA,CAAA0B,QAAA,EAA6C;IACxElD,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAmB,UAAA,CAAA0B,QAAA,MACF;IAC6BlD,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAkB,iBAAA,CAAAM,UAAA,CAAAuB,QAAA,CAAqB;IACzB/C,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAkB,iBAAA,CAAAwB,OAAA,CAAAf,UAAA,CAAAH,UAAA,CAAA2B,WAAA,EAAoC;IAShEnD,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAkB,iBAAA,CAAAM,UAAA,CAAA4B,OAAA,CAAoB;IAMfpD,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAAqD,kBAAA,KAAA7B,UAAA,CAAA8B,SAAA,CAAAC,IAAA,QAAA/B,UAAA,CAAA8B,SAAA,CAAAE,IAAA,MAAyD;IAEtCxD,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAqB,UAAA,SAAAG,UAAA,CAAAC,UAAA,CAAuB;IAMvBzB,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAqB,UAAA,SAAAG,UAAA,CAAAI,UAAA,KAAAc,OAAA,CAAAG,eAAA,CAAArB,UAAA,EAAmD;IAKnDxB,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAqB,UAAA,SAAAqB,OAAA,CAAAG,eAAA,CAAArB,UAAA,EAA6B;;;;;IAlDhExB,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAmC,UAAA,IAAAsB,wDAAA,yBAsDW;IACbzD,EAAA,CAAAG,YAAA,EAAM;;;;IAvDyBH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAqB,UAAA,YAAAqC,MAAA,CAAAC,gBAAA,CAAmB;;;;;IA6DhD3D,EAAA,CAAAC,cAAA,QAA+D;IAAAD,EAAA,CAAAE,MAAA,6CAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IACzGH,EAAA,CAAAC,cAAA,QAAkE;IAAAD,EAAA,CAAAE,MAAA,0CAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;IACzGH,EAAA,CAAAC,cAAA,iBAAwG;IAArFD,EAAA,CAAAU,UAAA,mBAAAkD,+EAAA;MAAA5D,EAAA,CAAAY,aAAA,CAAAiD,IAAA;MAAA,MAAAC,OAAA,GAAA9D,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAA8C,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IACzC/D,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAPXH,EAAA,CAAAC,cAAA,cAA2E;IAC5CD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAmC,UAAA,IAAA6B,iDAAA,gBAAyG;IACzGhE,EAAA,CAAAmC,UAAA,IAAA8B,iDAAA,gBAAyG;IACzGjE,EAAA,CAAAmC,UAAA,IAAA+B,sDAAA,qBAES;IACXlE,EAAA,CAAAG,YAAA,EAAM;;;;IALAH,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAAqB,UAAA,SAAA8C,MAAA,CAAAC,WAAA,IAAAD,MAAA,CAAAE,gBAAA,IAAAF,MAAA,CAAAG,gBAAA,CAAyD;IACzDtE,EAAA,CAAAI,SAAA,GAA4D;IAA5DJ,EAAA,CAAAqB,UAAA,UAAA8C,MAAA,CAAAC,WAAA,KAAAD,MAAA,CAAAE,gBAAA,KAAAF,MAAA,CAAAG,gBAAA,CAA4D;IACnBtE,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAAqB,UAAA,SAAA8C,MAAA,CAAAC,WAAA,IAAAD,MAAA,CAAAE,gBAAA,IAAAF,MAAA,CAAAG,gBAAA,CAAyD;;;;;;;;;IAMxGtE,EAAA,CAAAC,cAAA,cAAyD;IAMrDD,EAAA,CAAAU,UAAA,kBAAA6D,4EAAAC,MAAA;MAAAxE,EAAA,CAAAY,aAAA,CAAA6D,IAAA;MAAA,MAAAC,OAAA,GAAA1E,EAAA,CAAAe,aAAA;MAAA,OAAQf,EAAA,CAAAgB,WAAA,CAAA0D,OAAA,CAAAC,QAAA,CAAAH,MAAA,CAAAI,SAAA,GAA4B,CAAC,CAAC;IAAA,EAAC;IAEzC5E,EAAA,CAAAG,YAAA,EAAgB;;;;IANdH,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAqB,UAAA,WAAAwD,OAAA,CAAAtE,eAAA,CAAAC,MAAA,CAAiC,aAAAqE,OAAA,CAAAC,YAAA,qBAAA9E,EAAA,CAAA+E,eAAA,IAAAC,GAAA,gBAAAH,OAAA,CAAAI,WAAA;;;;;;IAxHvCjF,EAAA,CAAAC,cAAA,UAAgC;IAOTD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACrCH,EAAA,CAAAC,cAAA,gBACmD;IADnCD,EAAA,CAAAU,UAAA,2BAAAwE,sEAAAV,MAAA;MAAAxE,EAAA,CAAAY,aAAA,CAAAuE,IAAA;MAAA,MAAAC,OAAA,GAAApF,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAAoE,OAAA,CAAAhB,WAAA,GAAAI,MAAA;IAAA,EAAyB,mBAAAa,8DAAA;MAAArF,EAAA,CAAAY,aAAA,CAAAuE,IAAA;MAAA,MAAAG,OAAA,GAAAtF,EAAA,CAAAe,aAAA;MAAA,OAAUf,EAAA,CAAAgB,WAAA,CAAAsE,OAAA,CAAAC,QAAA,EAAU;IAAA,EAApB;IAAzCvF,EAAA,CAAAG,YAAA,EACmD;IACnDH,EAAA,CAAAC,cAAA,mBAAoB;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAIzCH,EAAA,CAAAC,cAAA,eAA0B;IAEXD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC/BH,EAAA,CAAAC,cAAA,sBAAgF;IAApED,EAAA,CAAAU,UAAA,2BAAA8E,4EAAAhB,MAAA;MAAAxE,EAAA,CAAAY,aAAA,CAAAuE,IAAA;MAAA,MAAAM,OAAA,GAAAzF,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAAyE,OAAA,CAAApB,gBAAA,GAAAG,MAAA;IAAA,EAA8B,6BAAAkB,8EAAA;MAAA1F,EAAA,CAAAY,aAAA,CAAAuE,IAAA;MAAA,MAAAQ,OAAA,GAAA3F,EAAA,CAAAe,aAAA;MAAA,OAAoBf,EAAA,CAAAgB,WAAA,CAAA2E,OAAA,CAAAC,cAAA,EAAgB;IAAA,EAApC;IACxC5F,EAAA,CAAAC,cAAA,sBAAqB;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAa;IAChDH,EAAA,CAAAmC,UAAA,KAAA0D,oDAAA,yBAEa;IACf7F,EAAA,CAAAG,YAAA,EAAa;IAIjBH,EAAA,CAAAC,cAAA,eAA0B;IAEXD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC/BH,EAAA,CAAAC,cAAA,sBAAgF;IAApED,EAAA,CAAAU,UAAA,2BAAAoF,4EAAAtB,MAAA;MAAAxE,EAAA,CAAAY,aAAA,CAAAuE,IAAA;MAAA,MAAAY,OAAA,GAAA/F,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAA+E,OAAA,CAAAzB,gBAAA,GAAAE,MAAA;IAAA,EAA8B,6BAAAwB,8EAAA;MAAAhG,EAAA,CAAAY,aAAA,CAAAuE,IAAA;MAAA,MAAAc,OAAA,GAAAjG,EAAA,CAAAe,aAAA;MAAA,OAAoBf,EAAA,CAAAgB,WAAA,CAAAiF,OAAA,CAAAL,cAAA,EAAgB;IAAA,EAApC;IACxC5F,EAAA,CAAAC,cAAA,sBAAqB;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAa;IAChDH,EAAA,CAAAmC,UAAA,KAAA+D,oDAAA,yBAEa;IACflG,EAAA,CAAAG,YAAA,EAAa;IAIjBH,EAAA,CAAAC,cAAA,eAA4B;IACFD,EAAA,CAAAU,UAAA,mBAAAyF,gEAAA;MAAAnG,EAAA,CAAAY,aAAA,CAAAuE,IAAA;MAAA,MAAAiB,OAAA,GAAApG,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAoF,OAAA,CAAArC,YAAA,EAAc;IAAA,EAAC;IAC9C/D,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAQpCH,EAAA,CAAAmC,UAAA,KAAAkE,6CAAA,kBAwDM;IAGNrG,EAAA,CAAAmC,UAAA,KAAAmE,6CAAA,kBAQM;IAGNtG,EAAA,CAAAmC,UAAA,KAAAoE,6CAAA,kBASM;IACRvG,EAAA,CAAAG,YAAA,EAAM;;;;IAxHsBH,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAqB,UAAA,YAAAmF,MAAA,CAAApC,WAAA,CAAyB;IAS7BpE,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAqB,UAAA,YAAAmF,MAAA,CAAAnC,gBAAA,CAA8B;IAEPrE,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAqB,UAAA,YAAAmF,MAAA,CAAAC,UAAA,CAAa;IAUpCzG,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAqB,UAAA,YAAAmF,MAAA,CAAAlC,gBAAA,CAA8B;IAEPtE,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAqB,UAAA,YAAAmF,MAAA,CAAAE,UAAA,CAAa;IAiB/B1G,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAqB,UAAA,SAAAmF,MAAA,CAAA7C,gBAAA,CAAAnD,MAAA,KAAiC;IA2DtDR,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAqB,UAAA,UAAAmF,MAAA,CAAAG,OAAA,IAAAH,MAAA,CAAA7C,gBAAA,CAAAnD,MAAA,OAA+C;IAWlBR,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAqB,UAAA,SAAAmF,MAAA,CAAAI,UAAA,KAAoB;;;ADxH3D,OAAM,MAAOC,sBAAsB;EA4BjCC,YACUC,MAAc,EACdC,aAA4B,EAC5BC,WAAwB,EACxBC,gBAAkC,EAClCC,QAAqB;IAJrB,KAAAJ,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,QAAQ,GAARA,QAAQ;IAhClB;IACA,KAAAR,OAAO,GAAG,IAAI;IACd,KAAAvF,KAAK,GAAkB,IAAI;IAI3B,KAAAgG,OAAO,GAAU,EAAE;IACnB,KAAA7G,eAAe,GAAa,EAAE;IAC9B,KAAA8G,eAAe,GAAa,EAAE;IAC9B,KAAAC,kBAAkB,GAAa,EAAE;IACjC,KAAAC,cAAc,GAAa,EAAE;IAC7B,KAAAC,eAAe,GAAa,EAAE;IAE9B;IACA,KAAApD,WAAW,GAAG,EAAE;IAChB,KAAAC,gBAAgB,GAAG,EAAE;IACrB,KAAAC,gBAAgB,GAAG,EAAE;IAErB;IACA,KAAAW,WAAW,GAAG,CAAC;IACf,KAAAH,YAAY,GAAG,EAAE;IACjB,KAAA2C,UAAU,GAAG,CAAC;IAEd;IACA,KAAAhB,UAAU,GAAG,CAAC,SAAS,EAAE,UAAU,EAAE,gBAAgB,EAAE,OAAO,EAAE,SAAS,EAAE,aAAa,EAAE,WAAW,CAAC;IACtG,KAAAC,UAAU,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC;EAQ7C;EAEHgB,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,GAAG,IAAI,CAACV,WAAW,CAACW,uBAAuB,EAAE,EAAEC,IAAI;IACnE,IAAI,IAAI,CAACF,WAAW,EAAE;MACpB,IAAI,CAACG,sBAAsB,EAAE;KAC9B,MAAM;MACL,IAAI,CAAC1G,KAAK,GAAG,gBAAgB;MAC7B,IAAI,CAACuF,OAAO,GAAG,KAAK;;EAExB;EAEAmB,sBAAsBA,CAAA;IACpB,IAAI,CAACnB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACvF,KAAK,GAAG,IAAI;IAEjB;IACA,IAAI,CAAC8F,gBAAgB,CAACa,qBAAqB,CAAC,IAAI,CAACJ,WAAW,CAACK,GAAG,CAAC,CAACC,SAAS,CAAC;MAC1EC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACC,yBAAyB,CAACF,QAAQ,CAACG,SAAS,CAAC;UAClD,IAAI,CAACC,WAAW,EAAE;SACnB,MAAM;UACL,IAAI,CAACA,WAAW,EAAE,CAAC,CAAC;;MAExB,CAAC;;MACDnH,KAAK,EAAGA,KAAK,IAAI;QACfoH,OAAO,CAACpH,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1D,IAAI,CAACmH,WAAW,EAAE,CAAC,CAAC;MACtB;KACD,CAAC;EACJ;;EAEAF,yBAAyBA,CAACI,aAAoB;IAC5C,MAAMC,QAAQ,GAAG,IAAIC,GAAG,EAAU;IAClC,MAAMC,WAAW,GAAG,IAAID,GAAG,EAAU;IACrC,MAAME,OAAO,GAAG,IAAIF,GAAG,EAAU;IACjC,MAAMG,QAAQ,GAAG,IAAIH,GAAG,EAAU;IAElCF,aAAa,CAACM,OAAO,CAACC,KAAK,IAAG;MAC5B,IAAIA,KAAK,CAACC,OAAO,EAAEjB,GAAG,EAAEU,QAAQ,CAACQ,GAAG,CAACF,KAAK,CAACC,OAAO,CAACjB,GAAG,CAAC;MACvD,IAAIgB,KAAK,CAACG,UAAU,EAAEnB,GAAG,EAAEY,WAAW,CAACM,GAAG,CAACF,KAAK,CAACG,UAAU,CAACnB,GAAG,CAAC;MAChE,IAAIgB,KAAK,CAACI,KAAK,EAAEpB,GAAG,EAAEa,OAAO,CAACK,GAAG,CAACF,KAAK,CAACI,KAAK,CAACpB,GAAG,CAAC;MAClD,IAAIgB,KAAK,CAACK,OAAO,EAAErB,GAAG,EAAEc,QAAQ,CAACI,GAAG,CAACF,KAAK,CAACK,OAAO,CAACrB,GAAG,CAAC;IACzD,CAAC,CAAC;IAEF,IAAI,CAACX,eAAe,GAAGiC,KAAK,CAACC,IAAI,CAACb,QAAQ,CAAC;IAC3C,IAAI,CAACpB,kBAAkB,GAAGgC,KAAK,CAACC,IAAI,CAACX,WAAW,CAAC;IACjD,IAAI,CAACrB,cAAc,GAAG+B,KAAK,CAACC,IAAI,CAACV,OAAO,CAAC;IACzC,IAAI,CAACrB,eAAe,GAAG8B,KAAK,CAACC,IAAI,CAACT,QAAQ,CAAC;EAC7C;EAEAP,WAAWA,CAAA;IACT,IAAI,CAAC5B,OAAO,GAAG,IAAI;IACnB,IAAI,CAACvF,KAAK,GAAG,IAAI;IAEjB;IACA,MAAMoI,OAAO,GAAG;MACdC,cAAc,EAAE,UAAU;MAC1BC,WAAW,EAAE,IAAI;MACjBC,IAAI,EAAE,IAAI,CAAC1E,WAAW;MACtB2E,KAAK,EAAE,IAAI,CAAC9E;KACb;IAED,IAAI,CAACkC,aAAa,CAAC6C,aAAa,CAACL,OAAO,CAAC,CAACvB,SAAS,CAAC;MAClDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAAChB,OAAO,GAAGe,QAAQ,EAAEf,OAAO,IAAI,EAAE;UACtC,IAAI,CAACK,UAAU,GAAGU,QAAQ,EAAE2B,UAAU,EAAEC,YAAY,IAAI5B,QAAQ,EAAEf,OAAO,EAAE5G,MAAM,IAAI,CAAC;UACtF,IAAI,CAACwJ,uBAAuB,EAAE;UAC9B,IAAI,CAACC,YAAY,EAAE;SACpB,MAAM;UACL,IAAI,CAAC7I,KAAK,GAAG,wBAAwB;;QAEvC,IAAI,CAACuF,OAAO,GAAG,KAAK;MACtB,CAAC;MACDvF,KAAK,EAAGA,KAAK,IAAI;QACfoH,OAAO,CAACpH,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACA,KAAK,GAAG,4BAA4B;QACzC,IAAI,CAACuF,OAAO,GAAG,KAAK;QACpB,IAAI,CAACQ,QAAQ,CAAC+C,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MAC3E;KACD,CAAC;EACJ;EAEAH,uBAAuBA,CAAA;IACrB;IACA,IAAI,CAAC5C,OAAO,GAAG,IAAI,CAACA,OAAO,CAACgD,MAAM,CAACC,MAAM,IAAG;MAC1C;MACA,IAAI,CAACA,MAAM,CAACC,cAAc,IAAI,CAACD,MAAM,CAACE,iBAAiB,IACnD,CAACF,MAAM,CAACG,aAAa,IAAI,CAACH,MAAM,CAACI,cAAc,EAAE;QACnD,OAAO,IAAI;;MAGb;MACA,IAAIJ,MAAM,CAACC,cAAc,IAAID,MAAM,CAACC,cAAc,CAAC9J,MAAM,GAAG,CAAC,EAAE;QAC7D,MAAMkK,kBAAkB,GAAGL,MAAM,CAACC,cAAc,CAACK,IAAI,CAAEC,SAAiB,IACtE,IAAI,CAACvD,eAAe,CAACwD,QAAQ,CAACD,SAAS,CAAC,CACzC;QACD,IAAIF,kBAAkB,EAAE,OAAO,IAAI;;MAGrC;MACA,IAAIL,MAAM,CAACE,iBAAiB,IAAIF,MAAM,CAACE,iBAAiB,CAAC/J,MAAM,GAAG,CAAC,EAAE;QACnE,MAAMsK,qBAAqB,GAAGT,MAAM,CAACE,iBAAiB,CAACI,IAAI,CAAEI,MAAc,IACzE,IAAI,CAACzD,kBAAkB,CAACuD,QAAQ,CAACE,MAAM,CAAC,CACzC;QACD,IAAID,qBAAqB,EAAE,OAAO,IAAI;;MAGxC;MACA,IAAIT,MAAM,CAACG,aAAa,IAAIH,MAAM,CAACG,aAAa,CAAChK,MAAM,GAAG,CAAC,EAAE;QAC3D,MAAMwK,gBAAgB,GAAGX,MAAM,CAACG,aAAa,CAACG,IAAI,CAAEM,OAAe,IACjE,IAAI,CAAC1D,cAAc,CAACsD,QAAQ,CAACI,OAAO,CAAC,CACtC;QACD,IAAID,gBAAgB,EAAE,OAAO,IAAI;;MAGnC;MACA,IAAIX,MAAM,CAACI,cAAc,IAAIJ,MAAM,CAACI,cAAc,CAACjK,MAAM,GAAG,CAAC,EAAE;QAC7D,MAAM0K,kBAAkB,GAAGb,MAAM,CAACI,cAAc,CAACE,IAAI,CAAEQ,SAAiB,IACtE,IAAI,CAAC3D,eAAe,CAACqD,QAAQ,CAACM,SAAS,CAAC,CACzC;QACD,IAAID,kBAAkB,EAAE,OAAO,IAAI;;MAGrC,OAAO,KAAK;IACd,CAAC,CAAC;EACJ;EAEAjB,YAAYA,CAAA;IACV,IAAImB,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAChE,OAAO,CAAC;IAEhC,IAAI,IAAI,CAAChD,WAAW,EAAE;MACpB,MAAMiH,KAAK,GAAG,IAAI,CAACjH,WAAW,CAACkH,WAAW,EAAE;MAC5CF,QAAQ,GAAGA,QAAQ,CAAChB,MAAM,CAACC,MAAM,IAC/BA,MAAM,CAACrH,KAAK,CAACsI,WAAW,EAAE,CAACT,QAAQ,CAACQ,KAAK,CAAC,IAC1ChB,MAAM,CAACjH,OAAO,CAACkI,WAAW,EAAE,CAACT,QAAQ,CAACQ,KAAK,CAAC,CAC7C;;IAGH,IAAI,IAAI,CAAChH,gBAAgB,EAAE;MACzB+G,QAAQ,GAAGA,QAAQ,CAAChB,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACtH,QAAQ,KAAK,IAAI,CAACsB,gBAAgB,CAAC;;IAGjF,IAAI,IAAI,CAACC,gBAAgB,EAAE;MACzB8G,QAAQ,GAAGA,QAAQ,CAAChB,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACnH,QAAQ,KAAK,IAAI,CAACoB,gBAAgB,CAAC;;IAGjF,IAAI,CAAC/D,eAAe,GAAG6K,QAAQ;EACjC;EAEA7F,QAAQA,CAAA;IACN,IAAI,CAAC0E,YAAY,EAAE;EACrB;EAEArE,cAAcA,CAAA;IACZ,IAAI,CAACqE,YAAY,EAAE;EACrB;EAEAlG,YAAYA,CAAA;IACV,IAAI,CAACK,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAAC2F,YAAY,EAAE;EACrB;EAEA/H,UAAUA,CAACmI,MAAc;IACvB,IAAI,IAAI,CAAC1C,WAAW,IAAI,CAAC,IAAI,CAAChF,YAAY,CAAC0H,MAAM,CAAC,EAAE;MAClD,IAAI,CAACrD,aAAa,CAAC9E,UAAU,CAACmI,MAAM,CAACrC,GAAG,EAAE,IAAI,CAACL,WAAW,CAACK,GAAG,CAAC,CAACC,SAAS,CAAC;QACxEC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;YACpB;YACA,MAAMmD,KAAK,GAAG,IAAI,CAACnE,OAAO,CAACoE,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACzD,GAAG,KAAKqC,MAAM,CAACrC,GAAG,CAAC;YAC/D,IAAIuD,KAAK,KAAK,CAAC,CAAC,EAAE;cAChB,IAAI,CAACnE,OAAO,CAACmE,KAAK,CAAC,CAACG,MAAM,GAAG,IAAI,CAACtE,OAAO,CAACmE,KAAK,CAAC,CAACG,MAAM,IAAI,EAAE;cAC7D,IAAI,CAACtE,OAAO,CAACmE,KAAK,CAAC,CAACG,MAAM,CAACC,IAAI,CAAC;gBAC9B9D,IAAI,EAAE,IAAI,CAACF,WAAW,CAACK,GAAG;gBAC1B4D,MAAM,EAAE,IAAIC,IAAI;eACjB,CAAC;cACF,IAAI,CAACzE,OAAO,CAACmE,KAAK,CAAC,CAAC9J,UAAU,GAAG,CAAC,IAAI,CAAC2F,OAAO,CAACmE,KAAK,CAAC,CAAC9J,UAAU,IAAI,CAAC,IAAI,CAAC;;;QAGhF,CAAC;QACDL,KAAK,EAAGA,KAAK,IAAI;UACfoH,OAAO,CAACpH,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACvD;OACD,CAAC;;EAEN;EAEAuB,YAAYA,CAAC0H,MAAc;IACzB,IAAI,CAAC,IAAI,CAAC1C,WAAW,IAAI,CAAC0C,MAAM,CAACqB,MAAM,EAAE,OAAO,KAAK;IACrD,OAAOrB,MAAM,CAACqB,MAAM,CAACf,IAAI,CAAEmB,IAAS,IAAKA,IAAI,CAACjE,IAAI,KAAK,IAAI,CAACF,WAAW,CAACK,GAAG,CAAC;EAC9E;EAEAnF,eAAeA,CAACwH,MAAc;IAC5B,IAAI,CAACA,MAAM,CAACzI,UAAU,EAAE,OAAO,KAAK;IACpC,OAAO,IAAIiK,IAAI,EAAE,GAAG,IAAIA,IAAI,CAACxB,MAAM,CAACzI,UAAU,CAAC;EACjD;EAEAX,WAAWA,CAAA;IACT,IAAI,CAACsH,WAAW,EAAE;EACpB;EAEA;EACA,IAAI5E,gBAAgBA,CAAA;IAClB,MAAMoI,UAAU,GAAG,CAAC,IAAI,CAAC9G,WAAW,GAAG,CAAC,IAAI,IAAI,CAACH,YAAY;IAC7D,OAAO,IAAI,CAACvE,eAAe,CAACyL,KAAK,CAACD,UAAU,EAAEA,UAAU,GAAG,IAAI,CAACjH,YAAY,CAAC;EAC/E;EAEA,IAAI8B,UAAUA,CAAA;IACZ,OAAOqF,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC3L,eAAe,CAACC,MAAM,GAAG,IAAI,CAACsE,YAAY,CAAC;EACnE;EAEAqH,QAAQA,CAAA;IACN,IAAI,IAAI,CAAClH,WAAW,GAAG,IAAI,CAAC2B,UAAU,EAAE;MACtC,IAAI,CAAC3B,WAAW,EAAE;;EAEtB;EAEAmH,YAAYA,CAAA;IACV,IAAI,IAAI,CAACnH,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;;EAEtB;EAEAN,QAAQA,CAACgF,IAAY;IACnB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAAC/C,UAAU,EAAE;MACxC,IAAI,CAAC3B,WAAW,GAAG0E,IAAI;;EAE3B;EAEA;EACA1G,gBAAgBA,CAACC,QAAgB;IAC/B,QAAQA,QAAQ,CAACoI,WAAW,EAAE;MAC5B,KAAK,QAAQ;QAAE,OAAO,iBAAiB;MACvC,KAAK,MAAM;QAAE,OAAO,eAAe;MACnC,KAAK,QAAQ;QAAE,OAAO,iBAAiB;MACvC,KAAK,KAAK;QAAE,OAAO,cAAc;MACjC;QAAS,OAAO,iBAAiB;;EAErC;EAEAxI,eAAeA,CAACC,QAAgB;IAC9B,QAAQA,QAAQ,CAACuI,WAAW,EAAE;MAC5B,KAAK,UAAU;QAAE,OAAO,QAAQ;MAChC,KAAK,gBAAgB;QAAE,OAAO,UAAU;MACxC,KAAK,OAAO;QAAE,OAAO,OAAO;MAC5B,KAAK,SAAS;QAAE,OAAO,cAAc;MACrC,KAAK,aAAa;QAAE,OAAO,MAAM;MACjC,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC;QAAS,OAAO,cAAc;;EAElC;EAEA3J,UAAUA,CAAC0K,IAAmB;IAC5B,OAAO,IAAIR,IAAI,CAACQ,IAAI,CAAC,CAACC,kBAAkB,EAAE;EAC5C;EAEAC,cAAcA,CAACF,IAAmB;IAChC,OAAO,IAAIR,IAAI,CAACQ,IAAI,CAAC,CAACG,cAAc,EAAE;EACxC;EAAC,QAAAC,CAAA,G;qBAxSU5F,sBAAsB,EAAA7G,EAAA,CAAA0M,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAA5M,EAAA,CAAA0M,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAA9M,EAAA,CAAA0M,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAhN,EAAA,CAAA0M,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAlN,EAAA,CAAA0M,iBAAA,CAAAS,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAtBxG,sBAAsB;IAAAyG,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QClCnC5N,EAAA,CAAAC,cAAA,aAAuC;QAKFD,EAAA,CAAAE,MAAA,eAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAChDH,EAAA,CAAAE,MAAA,gBACA;QAAAF,EAAA,CAAAmC,UAAA,IAAA2L,sCAAA,kBAAiF;QACnF9N,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAC,cAAA,WAAyB;QAAAD,EAAA,CAAAE,MAAA,0CAAmC;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAElEH,EAAA,CAAAC,cAAA,cAA4B;QACFD,EAAA,CAAAU,UAAA,mBAAAqN,yDAAA;UAAA,OAASF,GAAA,CAAA5M,WAAA,EAAa;QAAA,EAAC;QAC7CjB,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAMlCH,EAAA,CAAAmC,UAAA,KAAA6L,sCAAA,iBAGM;QAGNhO,EAAA,CAAAmC,UAAA,KAAA8L,sCAAA,mBAQM;QAGNjO,EAAA,CAAAmC,UAAA,KAAA+L,sCAAA,mBAgIM;QACRlO,EAAA,CAAAG,YAAA,EAAM;;;QA9J8BH,EAAA,CAAAI,SAAA,GAAc;QAAdJ,EAAA,CAAAqB,UAAA,UAAAwM,GAAA,CAAAlH,OAAA,CAAc;QAY1C3G,EAAA,CAAAI,SAAA,GAAa;QAAbJ,EAAA,CAAAqB,UAAA,SAAAwM,GAAA,CAAAlH,OAAA,CAAa;QAMb3G,EAAA,CAAAI,SAAA,GAAuB;QAAvBJ,EAAA,CAAAqB,UAAA,SAAAwM,GAAA,CAAAzM,KAAA,KAAAyM,GAAA,CAAAlH,OAAA,CAAuB;QAWvB3G,EAAA,CAAAI,SAAA,GAAwB;QAAxBJ,EAAA,CAAAqB,UAAA,UAAAwM,GAAA,CAAAlH,OAAA,KAAAkH,GAAA,CAAAzM,KAAA,CAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}