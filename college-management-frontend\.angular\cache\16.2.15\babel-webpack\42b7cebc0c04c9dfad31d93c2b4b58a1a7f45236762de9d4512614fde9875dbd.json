{"ast": null, "code": "import { HttpHeaders, HttpParams } from '@angular/common/http';\nimport { throwError } from 'rxjs';\nimport { catchError, tap } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ComplaintService {\n  updateComplaint(id, data) {\n    return this.http.put(`/api/complaints/${id}`, data);\n  }\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = environment.apiUrl;\n  }\n  getAuthHeaders() {\n    const token = localStorage.getItem('token');\n    return new HttpHeaders({\n      'Authorization': `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    });\n  }\n  // Create a new complaint\n  createComplaint(complaintData) {\n    return this.http.post(`${this.apiUrl}/complaints`, complaintData, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap({\n      next: response => {\n        console.log('Complaint created successfully:', response);\n      }\n    }), catchError(error => {\n      console.error('Error creating complaint:', error);\n      return throwError(() => error);\n    }));\n  }\n  // Get all complaints with filtering\n  getAllComplaints(params) {\n    let httpParams = new HttpParams();\n    if (params) {\n      Object.keys(params).forEach(key => {\n        const value = params[key];\n        if (value !== undefined && value !== null) {\n          httpParams = httpParams.set(key, value.toString());\n        }\n      });\n    }\n    return this.http.get(`${this.apiUrl}/complaints`, {\n      headers: this.getAuthHeaders(),\n      params: httpParams\n    }).pipe(catchError(error => {\n      console.error('Error fetching complaints:', error);\n      return throwError(() => error);\n    }));\n  }\n  // Get complaint by ID\n  getComplaintById(id) {\n    return this.http.get(`${this.apiUrl}/complaints/${id}`, {\n      headers: this.getAuthHeaders()\n    }).pipe(catchError(error => {\n      console.error('Error fetching complaint:', error);\n      return throwError(() => error);\n    }));\n  }\n  // Update complaint status\n  updateComplaintStatus(id, updateData) {\n    return this.http.put(`${this.apiUrl}/complaints/${id}/status`, updateData, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap({\n      next: response => {\n        console.log('Complaint status updated successfully:', response);\n      }\n    }), catchError(error => {\n      console.error('Error updating complaint status:', error);\n      return throwError(() => error);\n    }));\n  }\n  // Add comment to complaint\n  addComment(id, comment, userId) {\n    return this.http.post(`${this.apiUrl}/complaints/${id}/comments`, {\n      comment,\n      user: userId\n    }, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap({\n      next: response => {\n        console.log('Comment added successfully:', response);\n      }\n    }), catchError(error => {\n      console.error('Error adding comment:', error);\n      return throwError(() => error);\n    }));\n  }\n  // Get complaints by user\n  getComplaintsByUser(userId, page = 1, limit = 10) {\n    const params = new HttpParams().set('page', page.toString()).set('limit', limit.toString());\n    return this.http.get(`${this.apiUrl}/complaints/user/${userId}`, {\n      headers: this.getAuthHeaders(),\n      params\n    }).pipe(catchError(error => {\n      console.error('Error fetching user complaints:', error);\n      return throwError(() => error);\n    }));\n  }\n  // Delete complaint\n  deleteComplaint(id) {\n    return this.http.delete(`${this.apiUrl}/complaints/${id}`, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap({\n      next: response => {\n        console.log('Complaint deleted successfully:', response);\n      }\n    }), catchError(error => {\n      console.error('Error deleting complaint:', error);\n      return throwError(() => error);\n    }));\n  }\n  // Get complaint statistics\n  getComplaintStats() {\n    return this.http.get(`${this.apiUrl}/complaints/stats`, {\n      headers: this.getAuthHeaders()\n    }).pipe(catchError(error => {\n      console.error('Error fetching complaint stats:', error);\n      return throwError(() => error);\n    }));\n  }\n  static #_ = this.ɵfac = function ComplaintService_Factory(t) {\n    return new (t || ComplaintService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ComplaintService,\n    factory: ComplaintService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "HttpParams", "throwError", "catchError", "tap", "environment", "ComplaintService", "updateComplaint", "id", "data", "http", "put", "constructor", "apiUrl", "getAuthHeaders", "token", "localStorage", "getItem", "createComplaint", "complaintData", "post", "headers", "pipe", "next", "response", "console", "log", "error", "getAllComplaints", "params", "httpParams", "Object", "keys", "for<PERSON>ach", "key", "value", "undefined", "set", "toString", "get", "getComplaintById", "updateComplaintStatus", "updateData", "addComment", "comment", "userId", "user", "getComplaintsByUser", "page", "limit", "deleteComplaint", "delete", "getComplaintStats", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\services\\complaint.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';\r\nimport { Observable, throwError } from 'rxjs';\r\nimport { catchError, tap } from 'rxjs/operators';\r\nimport { environment } from '../../environments/environment';\r\n\r\nexport interface Complaint {\r\n  _id?: string;\r\n  title: string;\r\n  description: string;\r\n  complainant: string;\r\n  complainantRole: string;\r\n  category: 'Academic' | 'Infrastructure' | 'Software' | 'Administrative' | 'Suggestion' | 'Other';\r\n  priority: 'Low' | 'Medium' | 'High' | 'Critical';\r\n  status: 'Pending' | 'In Progress' | 'Resolved' | 'Closed' | 'Rejected';\r\n  assignedTo?: string;\r\n  resolution?: string;\r\n  resolvedBy?: string;\r\n  resolvedAt?: Date;\r\n  comments?: Array<{\r\n    user: string;\r\n    comment: string;\r\n    createdAt: Date;\r\n  }>;\r\n  createdAt?: Date;\r\n  updatedAt?: Date;\r\n  ageInDays?: number;\r\n    checked?: boolean; \r\n     date?: Date | string;\r\n  user?: string;        // Add if you are displaying user name/email\r\n  complaint?: string;   // Only if used — but probably this should be `description`\r\n}\r\n\r\nexport interface ComplaintResponse {\r\n  success: boolean;\r\n  message?: string;\r\n  complaint?: Complaint;\r\n  complaints?: Complaint[];\r\n  pagination?: {\r\n    currentPage: number;\r\n    totalPages: number;\r\n    totalComplaints: number;\r\n    hasNext: boolean;\r\n    hasPrev: boolean;\r\n  };\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ComplaintService {\r\n  updateComplaint(id: string, data: any): Observable<{ success: boolean }> {\r\n  return this.http.put<{ success: boolean }>(`/api/complaints/${id}`, data);\r\n}\r\n\r\n  private apiUrl = environment.apiUrl;\r\n\r\n  constructor(private http: HttpClient) { }\r\n\r\n  private getAuthHeaders(): HttpHeaders {\r\n    const token = localStorage.getItem('token');\r\n    return new HttpHeaders({\r\n      'Authorization': `Bearer ${token}`,\r\n      'Content-Type': 'application/json'\r\n    });\r\n  }\r\n\r\n  // Create a new complaint\r\n  createComplaint(complaintData: Partial<Complaint>): Observable<ComplaintResponse> {\r\n    return this.http.post<ComplaintResponse>(`${this.apiUrl}/complaints`, complaintData, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      tap({\r\n        next: (response) => {\r\n          console.log('Complaint created successfully:', response);\r\n        },\r\n      }),\r\n      catchError((error) => {\r\n        console.error('Error creating complaint:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Get all complaints with filtering\r\n  getAllComplaints(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    status?: string;\r\n    category?: string;\r\n    priority?: string;\r\n    complainant?: string;\r\n    assignedTo?: string;\r\n  }): Observable<ComplaintResponse> {\r\n    let httpParams = new HttpParams();\r\n    \r\n    if (params) {\r\n      Object.keys(params).forEach(key => {\r\n        const value = params[key as keyof typeof params];\r\n        if (value !== undefined && value !== null) {\r\n          httpParams = httpParams.set(key, value.toString());\r\n        }\r\n      });\r\n    }\r\n\r\n    return this.http.get<ComplaintResponse>(`${this.apiUrl}/complaints`, {\r\n      headers: this.getAuthHeaders(),\r\n      params: httpParams\r\n    }).pipe(\r\n      catchError((error) => {\r\n        console.error('Error fetching complaints:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Get complaint by ID\r\n  getComplaintById(id: string): Observable<ComplaintResponse> {\r\n    return this.http.get<ComplaintResponse>(`${this.apiUrl}/complaints/${id}`, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      catchError((error) => {\r\n        console.error('Error fetching complaint:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Update complaint status\r\n  updateComplaintStatus(id: string, updateData: {\r\n    status?: string;\r\n    assignedTo?: string;\r\n    resolution?: string;\r\n    resolvedBy?: string;\r\n  }): Observable<ComplaintResponse> {\r\n    return this.http.put<ComplaintResponse>(`${this.apiUrl}/complaints/${id}/status`, updateData, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      tap({\r\n        next: (response) => {\r\n          console.log('Complaint status updated successfully:', response);\r\n        },\r\n      }),\r\n      catchError((error) => {\r\n        console.error('Error updating complaint status:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Add comment to complaint\r\n  addComment(id: string, comment: string, userId: string): Observable<ComplaintResponse> {\r\n    return this.http.post<ComplaintResponse>(`${this.apiUrl}/complaints/${id}/comments`, {\r\n      comment,\r\n      user: userId\r\n    }, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      tap({\r\n        next: (response) => {\r\n          console.log('Comment added successfully:', response);\r\n        },\r\n      }),\r\n      catchError((error) => {\r\n        console.error('Error adding comment:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Get complaints by user\r\n  getComplaintsByUser(userId: string, page: number = 1, limit: number = 10): Observable<ComplaintResponse> {\r\n    const params = new HttpParams()\r\n      .set('page', page.toString())\r\n      .set('limit', limit.toString());\r\n\r\n    return this.http.get<ComplaintResponse>(`${this.apiUrl}/complaints/user/${userId}`, {\r\n      headers: this.getAuthHeaders(),\r\n      params\r\n    }).pipe(\r\n      catchError((error) => {\r\n        console.error('Error fetching user complaints:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Delete complaint\r\n  deleteComplaint(id: string): Observable<ComplaintResponse> {\r\n    return this.http.delete<ComplaintResponse>(`${this.apiUrl}/complaints/${id}`, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      tap({\r\n        next: (response) => {\r\n          console.log('Complaint deleted successfully:', response);\r\n        },\r\n      }),\r\n      catchError((error) => {\r\n        console.error('Error deleting complaint:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Get complaint statistics\r\n  getComplaintStats(): Observable<any> {\r\n    return this.http.get<any>(`${this.apiUrl}/complaints/stats`, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      catchError((error) => {\r\n        console.error('Error fetching complaint stats:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AACA,SAAqBA,WAAW,EAAEC,UAAU,QAAQ,sBAAsB;AAC1E,SAAqBC,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;AAChD,SAASC,WAAW,QAAQ,gCAAgC;;;AA8C5D,OAAM,MAAOC,gBAAgB;EAC3BC,eAAeA,CAACC,EAAU,EAAEC,IAAS;IACrC,OAAO,IAAI,CAACC,IAAI,CAACC,GAAG,CAAuB,mBAAmBH,EAAE,EAAE,EAAEC,IAAI,CAAC;EAC3E;EAIEG,YAAoBF,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAG,MAAM,GAAGR,WAAW,CAACQ,MAAM;EAEK;EAEhCC,cAAcA,CAAA;IACpB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,OAAO,IAAIjB,WAAW,CAAC;MACrB,eAAe,EAAE,UAAUe,KAAK,EAAE;MAClC,cAAc,EAAE;KACjB,CAAC;EACJ;EAEA;EACAG,eAAeA,CAACC,aAAiC;IAC/C,OAAO,IAAI,CAACT,IAAI,CAACU,IAAI,CAAoB,GAAG,IAAI,CAACP,MAAM,aAAa,EAAEM,aAAa,EAAE;MACnFE,OAAO,EAAE,IAAI,CAACP,cAAc;KAC7B,CAAC,CAACQ,IAAI,CACLlB,GAAG,CAAC;MACFmB,IAAI,EAAGC,QAAQ,IAAI;QACjBC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEF,QAAQ,CAAC;MAC1D;KACD,CAAC,EACFrB,UAAU,CAAEwB,KAAK,IAAI;MACnBF,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAOzB,UAAU,CAAC,MAAMyB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAC,gBAAgBA,CAACC,MAQhB;IACC,IAAIC,UAAU,GAAG,IAAI7B,UAAU,EAAE;IAEjC,IAAI4B,MAAM,EAAE;MACVE,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC,CAACI,OAAO,CAACC,GAAG,IAAG;QAChC,MAAMC,KAAK,GAAGN,MAAM,CAACK,GAA0B,CAAC;QAChD,IAAIC,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,EAAE;UACzCL,UAAU,GAAGA,UAAU,CAACO,GAAG,CAACH,GAAG,EAAEC,KAAK,CAACG,QAAQ,EAAE,CAAC;;MAEtD,CAAC,CAAC;;IAGJ,OAAO,IAAI,CAAC5B,IAAI,CAAC6B,GAAG,CAAoB,GAAG,IAAI,CAAC1B,MAAM,aAAa,EAAE;MACnEQ,OAAO,EAAE,IAAI,CAACP,cAAc,EAAE;MAC9Be,MAAM,EAAEC;KACT,CAAC,CAACR,IAAI,CACLnB,UAAU,CAAEwB,KAAK,IAAI;MACnBF,OAAO,CAACE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,OAAOzB,UAAU,CAAC,MAAMyB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAa,gBAAgBA,CAAChC,EAAU;IACzB,OAAO,IAAI,CAACE,IAAI,CAAC6B,GAAG,CAAoB,GAAG,IAAI,CAAC1B,MAAM,eAAeL,EAAE,EAAE,EAAE;MACzEa,OAAO,EAAE,IAAI,CAACP,cAAc;KAC7B,CAAC,CAACQ,IAAI,CACLnB,UAAU,CAAEwB,KAAK,IAAI;MACnBF,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAOzB,UAAU,CAAC,MAAMyB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAc,qBAAqBA,CAACjC,EAAU,EAAEkC,UAKjC;IACC,OAAO,IAAI,CAAChC,IAAI,CAACC,GAAG,CAAoB,GAAG,IAAI,CAACE,MAAM,eAAeL,EAAE,SAAS,EAAEkC,UAAU,EAAE;MAC5FrB,OAAO,EAAE,IAAI,CAACP,cAAc;KAC7B,CAAC,CAACQ,IAAI,CACLlB,GAAG,CAAC;MACFmB,IAAI,EAAGC,QAAQ,IAAI;QACjBC,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEF,QAAQ,CAAC;MACjE;KACD,CAAC,EACFrB,UAAU,CAAEwB,KAAK,IAAI;MACnBF,OAAO,CAACE,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,OAAOzB,UAAU,CAAC,MAAMyB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAgB,UAAUA,CAACnC,EAAU,EAAEoC,OAAe,EAAEC,MAAc;IACpD,OAAO,IAAI,CAACnC,IAAI,CAACU,IAAI,CAAoB,GAAG,IAAI,CAACP,MAAM,eAAeL,EAAE,WAAW,EAAE;MACnFoC,OAAO;MACPE,IAAI,EAAED;KACP,EAAE;MACDxB,OAAO,EAAE,IAAI,CAACP,cAAc;KAC7B,CAAC,CAACQ,IAAI,CACLlB,GAAG,CAAC;MACFmB,IAAI,EAAGC,QAAQ,IAAI;QACjBC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,QAAQ,CAAC;MACtD;KACD,CAAC,EACFrB,UAAU,CAAEwB,KAAK,IAAI;MACnBF,OAAO,CAACE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,OAAOzB,UAAU,CAAC,MAAMyB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAoB,mBAAmBA,CAACF,MAAc,EAAEG,IAAA,GAAe,CAAC,EAAEC,KAAA,GAAgB,EAAE;IACtE,MAAMpB,MAAM,GAAG,IAAI5B,UAAU,EAAE,CAC5BoC,GAAG,CAAC,MAAM,EAAEW,IAAI,CAACV,QAAQ,EAAE,CAAC,CAC5BD,GAAG,CAAC,OAAO,EAAEY,KAAK,CAACX,QAAQ,EAAE,CAAC;IAEjC,OAAO,IAAI,CAAC5B,IAAI,CAAC6B,GAAG,CAAoB,GAAG,IAAI,CAAC1B,MAAM,oBAAoBgC,MAAM,EAAE,EAAE;MAClFxB,OAAO,EAAE,IAAI,CAACP,cAAc,EAAE;MAC9Be;KACD,CAAC,CAACP,IAAI,CACLnB,UAAU,CAAEwB,KAAK,IAAI;MACnBF,OAAO,CAACE,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,OAAOzB,UAAU,CAAC,MAAMyB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAuB,eAAeA,CAAC1C,EAAU;IACxB,OAAO,IAAI,CAACE,IAAI,CAACyC,MAAM,CAAoB,GAAG,IAAI,CAACtC,MAAM,eAAeL,EAAE,EAAE,EAAE;MAC5Ea,OAAO,EAAE,IAAI,CAACP,cAAc;KAC7B,CAAC,CAACQ,IAAI,CACLlB,GAAG,CAAC;MACFmB,IAAI,EAAGC,QAAQ,IAAI;QACjBC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEF,QAAQ,CAAC;MAC1D;KACD,CAAC,EACFrB,UAAU,CAAEwB,KAAK,IAAI;MACnBF,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAOzB,UAAU,CAAC,MAAMyB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAyB,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAAC1C,IAAI,CAAC6B,GAAG,CAAM,GAAG,IAAI,CAAC1B,MAAM,mBAAmB,EAAE;MAC3DQ,OAAO,EAAE,IAAI,CAACP,cAAc;KAC7B,CAAC,CAACQ,IAAI,CACLnB,UAAU,CAAEwB,KAAK,IAAI;MACnBF,OAAO,CAACE,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,OAAOzB,UAAU,CAAC,MAAMyB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAAC,QAAA0B,CAAA,G;qBApKU/C,gBAAgB,EAAAgD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAhBpD,gBAAgB;IAAAqD,OAAA,EAAhBrD,gBAAgB,CAAAsD,IAAA;IAAAC,UAAA,EAFf;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}