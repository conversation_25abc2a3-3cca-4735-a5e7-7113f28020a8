.timetable-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.page-header h1 {
  margin: 0;
  color: #333;
  font-weight: 500;
}
th{
  color: green !important;
}

.form-card, .table-card {
  margin-bottom: 30px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.full-width {
  grid-column: 1 / -1;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 20px;
}

.form-actions button {
  display: flex;
  align-items: center;
  gap: 8px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

.table-container {
  overflow-x: auto;
}

.timetable-table {
  width: 100%;
  min-width: 800px;
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  color: #666;
}

.no-data mat-icon {
  font-size: 64px;
  height: 64px;
  width: 64px;
  margin-bottom: 16px;
  color: #ccc;
}

.no-data h3 {
  margin: 0 0 8px 0;
  color: #333;
}

.no-data p {
  margin: 0;
  color: #666;
}

/* Material Form Field Styling */
.mat-form-field {
  width: 100%;
}

.mat-form-field-appearance-outline .mat-form-field-outline {
  color: #e0e0e0;
}

.mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
  color: #1976d2;
}

/* Material Table Styling */
.mat-table {
  background: transparent;
}

.mat-header-cell {
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #e0e0e0;
}

.mat-cell {
  border-bottom: 1px solid #f0f0f0;
}

.mat-row:hover {
  background-color: #f9f9f9;
}

/* Responsive Design */
@media (max-width: 768px) {
  .timetable-container {
    padding: 10px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    justify-content: center;
  }
  
  .timetable-table {
    font-size: 0.9rem;
  }
}

/* Schedule Info Styling */
.schedule-info {
  line-height: 1.4;
}

.teacher-class {
  font-size: 14px;
  margin-bottom: 4px;
}

.teacher-class strong {
  color: #1976d2;
}

.subject-info {
  font-size: 12px;
}

.subject {
  color: #333;
  font-weight: 500;
}

.department {
  font-size: 11px;
  margin-left: 8px;
}

.text-muted {
  color: #666 !important;
}

/* Duration display */
.mat-cell small {
  display: block;
  font-size: 11px;
  color: #666;
  margin-top: 2px;
}
