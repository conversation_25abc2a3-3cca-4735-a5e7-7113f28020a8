{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/checkbox\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/forms\";\nfunction TeacherAttendnaceComponent_tr_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 15);\n    i0.ɵɵlistener(\"click\", function TeacherAttendnaceComponent_tr_27_Template_tr_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r3);\n      const class_r1 = restoredCtx.$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.gotostudents(class_r1.classname, class_r1.department, class_r1.section));\n    });\n    i0.ɵɵelementStart(1, \"td\", 16)(2, \"mat-checkbox\", 17);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\", 18);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\", 19);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 20);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\", 21);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const class_r1 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", class_r1.classname, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", class_r1.section, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", class_r1.department, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", class_r1.subjects, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", class_r1.students, \" \");\n  }\n}\nexport let TeacherAttendnaceComponent = /*#__PURE__*/(() => {\n  class TeacherAttendnaceComponent {\n    constructor(router) {\n      this.router = router;\n      this.isChecked = false;\n      this.isIndeterminate = true;\n      this.studentstable = false;\n      this.classes = [{\n        classname: '1st Year',\n        section: 'A',\n        department: 'Pre-Engineering',\n        subjects: 'Maths,urdu,english',\n        students: '20'\n      }, {\n        classname: '1st Year',\n        section: 'B',\n        department: 'Pre-Medical',\n        subjects: 'Maths,urdu,english',\n        students: '20'\n      }, {\n        classname: '2nd Year',\n        section: 'C',\n        department: 'Computer Science',\n        subjects: 'Maths,urdu,english',\n        students: '20'\n      }];\n    }\n    toggleCheckbox() {\n      if (this.isIndeterminate) {\n        this.isIndeterminate = false;\n        this.isChecked = true;\n      } else if (this.isChecked) {\n        this.isChecked = false;\n      } else {\n        this.isIndeterminate = true;\n      }\n    }\n    // In TeacherAttendanceComponent\n    gotostudents(selectedClass, selectedDepartment, selectedSection) {\n      this.router.navigate(['/dashboard/teacher/class-students'], {\n        queryParams: {\n          class: selectedClass,\n          department: selectedDepartment,\n          section: selectedSection\n        }\n      });\n    }\n    seestudents() {\n      this.studentstable = !this.studentstable;\n    }\n    static #_ = this.ɵfac = function TeacherAttendnaceComponent_Factory(t) {\n      return new (t || TeacherAttendnaceComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TeacherAttendnaceComponent,\n      selectors: [[\"app-teacher-attendnace\"]],\n      decls: 37,\n      vars: 3,\n      consts: [[1, \"maindiv\"], [1, \"secondarydiv\"], [1, \"my-3\", \"d-flex\", \"justify-content-between\", \"searchAndtab\"], [1, \"d-flex\", \"flex-wrap\", \"gap-3\"], [1, \"btn\", \"btn-top\", \"border\", \"d-flex\", \"align-items-center\"], [1, \"table-container\", \"mb-4\"], [1, \"table-responsive\"], [1, \"tablehead\"], [1, \"one\"], [\"color\", \"primary\", 3, \"ngModel\", \"indeterminate\", \"ngModelChange\", \"change\"], [1, \"two\"], [3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"p-3\", \"align-items-center\", \"justify-content-between\"], [1, \"btn\", \"btn-top\", \"border\"], [1, \"page\"], [3, \"click\"], [\"data-label\", \"Class Name\", 1, \"name\"], [\"color\", \"primary\"], [\"data-label\", \"Section\", 1, \"para\"], [\"data-label\", \"Department\", 1, \"para\"], [\"data-label\", \"Subjects\", 1, \"para\"], [\"data-label\", \"Students\", 1, \"para\"]],\n      template: function TeacherAttendnaceComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"button\", 4);\n          i0.ɵɵtext(5, \" Classes\");\n          i0.ɵɵelementStart(6, \"mat-icon\");\n          i0.ɵɵtext(7, \"arrow_downward\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(8, \"div\", 5)(9, \"div\", 6)(10, \"table\")(11, \"thead\")(12, \"tr\", 7)(13, \"th\", 8)(14, \"mat-checkbox\", 9);\n          i0.ɵɵlistener(\"ngModelChange\", function TeacherAttendnaceComponent_Template_mat_checkbox_ngModelChange_14_listener($event) {\n            return ctx.isChecked = $event;\n          })(\"change\", function TeacherAttendnaceComponent_Template_mat_checkbox_change_14_listener() {\n            return ctx.toggleCheckbox();\n          });\n          i0.ɵɵtext(15, \" Class Name \");\n          i0.ɵɵelementStart(16, \"mat-icon\");\n          i0.ɵɵtext(17, \"arrow_downward\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"th\");\n          i0.ɵɵtext(19, \"Section \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"th\");\n          i0.ɵɵtext(21, \"Department \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"th\");\n          i0.ɵɵtext(23, \"Subjects \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"th\", 10);\n          i0.ɵɵtext(25, \"Students\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(26, \"tbody\");\n          i0.ɵɵtemplate(27, TeacherAttendnaceComponent_tr_27_Template, 12, 5, \"tr\", 11);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(28, \"div\", 12)(29, \"div\")(30, \"button\", 13);\n          i0.ɵɵtext(31, \"Previous\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 14);\n          i0.ɵɵtext(33, \"Page 1 of 10\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"div\")(35, \"button\", 13);\n          i0.ɵɵtext(36, \"Next\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"ngModel\", ctx.isChecked)(\"indeterminate\", ctx.isIndeterminate);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngForOf\", ctx.classes);\n        }\n      },\n      dependencies: [i2.NgForOf, i3.MatCheckbox, i4.MatIcon, i5.NgControlStatus, i5.NgModel],\n      styles: [\".students[_ngcontent-%COMP%]{background-color:#f5fbff;color:#00a8d1;font-size:14px;font-weight:600;line-height:20px}\"]\n    });\n  }\n  return TeacherAttendnaceComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}