{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/role.service\";\nimport * as i2 from \"src/app/services/user.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/icon\";\nfunction SidebarComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"img\", 9);\n    i0.ɵɵelementStart(2, \"h3\");\n    i0.ɵɵtext(3, \"GPGC (Swabi)\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SidebarComponent_button_6_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r3.label);\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    exact: a0\n  };\n};\nfunction SidebarComponent_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 10)(1, \"mat-icon\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SidebarComponent_button_6_span_3_Template, 2, 1, \"span\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"routerLink\", item_r3.route)(\"routerLinkActiveOptions\", i0.ɵɵpureFunction1(4, _c0, item_r3.isExact));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r3.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isSidebarOpen);\n  }\n}\nfunction SidebarComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13);\n    i0.ɵɵelement(2, \"img\", 14);\n    i0.ɵɵelementStart(3, \"div\", 15)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_div_7_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.logout());\n    });\n    i0.ɵɵelement(9, \"i\", 17);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.profileName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.profileEmail);\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"fa-times\": a0,\n    \"fa-bars\": a1\n  };\n};\nexport let SidebarComponent = /*#__PURE__*/(() => {\n  class SidebarComponent {\n    constructor(roleService, userService, router) {\n      this.roleService = roleService;\n      this.userService = userService;\n      this.router = router;\n      this.isSidebarOpen = true;\n      this.toggle = new EventEmitter();\n      this.sidebarItems = [];\n      this.profileName = 'Muhammad Luqman';\n      this.profileEmail = '<EMAIL>';\n    }\n    ngOnInit() {\n      const role = this.roleService.getRole();\n      const user = this.userService.getUserFromLocalStorage().user;\n      this.profileName = user.name;\n      this.profileEmail = user.email;\n      // In your SidebarComponent's ngOnInit\n      if (role === 'Principal') {\n        this.sidebarItems = [{\n          label: 'Dashboard',\n          icon: 'dashboard',\n          route: '/dashboard',\n          isExact: true\n        }, {\n          label: 'Department',\n          icon: 'school',\n          route: '/dashboard/admin/department',\n          isExact: true\n        }, {\n          label: 'Subjects',\n          icon: 'settings',\n          route: '/dashboard/admin/subjects',\n          isExact: false\n        }, {\n          label: 'Classes',\n          icon: 'inventory',\n          route: '/dashboard/admin/classes',\n          isExact: false\n        }, {\n          label: 'Teachers',\n          icon: 'school',\n          route: '/dashboard/admin/teachers',\n          isExact: false\n        }, {\n          label: 'Students',\n          icon: 'people',\n          route: '/dashboard/admin/students/student-list',\n          isExact: false\n        }, {\n          label: 'Users',\n          icon: 'manage_accounts',\n          route: '/dashboard/admin/users',\n          isExact: false\n        }, {\n          label: 'Attendance',\n          icon: 'people',\n          route: '/dashboard/admin/students/attendenace',\n          isExact: false\n        }];\n      } else if (role === 'teacher') {\n        this.sidebarItems = [{\n          label: 'Dashboard',\n          icon: 'dashboard',\n          route: '/dashboard',\n          isExact: true\n        }, {\n          label: 'Classes',\n          icon: 'inventory',\n          route: '/dashboard/teacher/classes',\n          isExact: false\n        }, {\n          label: 'Students',\n          icon: 'people',\n          route: '/dashboard/teacher/all-students',\n          isExact: false\n        }, {\n          label: 'Attendance',\n          icon: 'people',\n          route: '/dashboard/teacher/attendance',\n          isExact: false\n        }, {\n          label: 'Notice',\n          icon: 'campaign',\n          route: '/dashboard/teacher/notice',\n          isExact: false\n        }, {\n          label: 'Complaint',\n          icon: 'chat',\n          route: '/dashboard/teacher/complaint',\n          isExact: false\n        }];\n      } else if (role === 'student') {\n        this.sidebarItems = [{\n          label: 'Dashboard',\n          icon: 'dashboard',\n          route: '/dashboard',\n          isExact: true\n        }, {\n          label: 'Classes',\n          icon: 'inventory',\n          route: '/dashboard/teacher/classes',\n          isExact: false\n        }, {\n          label: 'Subjects',\n          icon: 'settings',\n          route: '/dashboard/admin/subjects',\n          isExact: false\n        }, {\n          label: 'Teachers',\n          icon: 'school',\n          route: '/dashboard/student/teachers',\n          isExact: false\n        }];\n      }\n      this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(() => {\n        if (window.innerWidth <= 992) {\n          this.toggleSidebar();\n        }\n      });\n    }\n    toggleSidebar() {\n      this.toggle.emit();\n    }\n    logout() {\n      this.roleService.logout();\n      this.router.navigate(['/auth']);\n    }\n    static #_ = this.ɵfac = function SidebarComponent_Factory(t) {\n      return new (t || SidebarComponent)(i0.ɵɵdirectiveInject(i1.RoleService), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SidebarComponent,\n      selectors: [[\"app-sidebar\"]],\n      inputs: {\n        isSidebarOpen: \"isSidebarOpen\"\n      },\n      outputs: {\n        toggle: \"toggle\"\n      },\n      decls: 8,\n      vars: 7,\n      consts: [[1, \"sidebar\"], [1, \"sidebar-header\"], [1, \"toggle-btn\", 3, \"click\"], [1, \"fas\", 3, \"ngClass\"], [\"class\", \"brand\", 4, \"ngIf\"], [1, \"sidebar-menu\"], [\"class\", \"menu-item\", \"routerLinkActive\", \"active\", 3, \"routerLink\", \"routerLinkActiveOptions\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"sidebar-footer\", 4, \"ngIf\"], [1, \"brand\"], [\"src\", \"../../../assets/images/logo.jpeg\", \"alt\", \"Logo\"], [\"routerLinkActive\", \"active\", 1, \"menu-item\", 3, \"routerLink\", \"routerLinkActiveOptions\"], [4, \"ngIf\"], [1, \"sidebar-footer\"], [1, \"user-profile\"], [\"src\", \"../../../assets/images/logo.jpeg\", \"alt\", \"Profile\"], [1, \"user-info\"], [1, \"logout-btn\", 3, \"click\"], [1, \"fas\", \"fa-sign-out-alt\"]],\n      template: function SidebarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function SidebarComponent_Template_button_click_2_listener() {\n            return ctx.toggleSidebar();\n          });\n          i0.ɵɵelement(3, \"i\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, SidebarComponent_div_4_Template, 4, 0, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 5);\n          i0.ɵɵtemplate(6, SidebarComponent_button_6_Template, 4, 6, \"button\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, SidebarComponent_div_7_Template, 10, 2, \"div\", 7);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(4, _c1, ctx.isSidebarOpen, !ctx.isSidebarOpen));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isSidebarOpen);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.sidebarItems);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isSidebarOpen);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i3.RouterLink, i3.RouterLinkActive, i5.MatIcon],\n      styles: [\".sidebar[_ngcontent-%COMP%]{background-color:#fff;width:250px;height:100vh;position:relative;box-shadow:2px 0 10px #0000000d;transition:all .3s ease;display:flex;flex-direction:column;border-right:1px solid #eaeaea;overflow:hidden}.sidebar-header[_ngcontent-%COMP%]{padding:1rem;display:flex;align-items:center;border-bottom:1px solid #eaeaea;height:70px;background-color:#f8f9fa}.toggle-btn[_ngcontent-%COMP%]{background:none;border:none;font-size:1.2rem;cursor:pointer;padding:.5rem;border-radius:6px;transition:all .2s}.toggle-btn[_ngcontent-%COMP%]:hover{background-color:#fff3}.brand[_ngcontent-%COMP%]{display:flex;align-items:center;margin-left:1rem}.brand[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:30px;height:30px;margin-right:10px;border-radius:50%}.brand[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1rem;margin:0;font-weight:600}.sidebar-menu[_ngcontent-%COMP%]{flex:1;padding:1rem 0;overflow-y:auto}.menu-item[_ngcontent-%COMP%]{display:flex;width:100%!important;padding:.8rem 1.5rem;background:none;border:none;color:#515154;text-decoration:none;transition:all .2s;font-size:.9rem;font-weight:500;white-space:nowrap;border-radius:0}.menu-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:12px;font-size:1.2rem;width:24px;height:24px}.menu-item[_ngcontent-%COMP%]:hover{background-color:#f0f4f9;color:#11418e}.menu-item.active[_ngcontent-%COMP%]{background-color:#11418e;color:#fff}.sidebar-footer[_ngcontent-%COMP%]{padding:1rem;border-top:1px solid #eaeaea;background-color:#f8f9fa}.user-profile[_ngcontent-%COMP%]{display:flex;align-items:center}.user-profile[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;margin-right:10px;object-fit:cover}.user-info[_ngcontent-%COMP%]{flex:1;overflow:hidden}.user-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:.9rem;margin:0;color:#11418e;font-weight:600;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.user-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.8rem;margin:0;color:#515154;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.logout-btn[_ngcontent-%COMP%]{background:none;border:none;color:#515154;cursor:pointer;padding:.5rem;border-radius:6px;transition:all .2s}.logout-btn[_ngcontent-%COMP%]:hover{background-color:#e0e8f0;color:#11418e}.sidebar-closed[_nghost-%COMP%]   .sidebar[_ngcontent-%COMP%], .sidebar-closed   [_nghost-%COMP%]   .sidebar[_ngcontent-%COMP%]{width:70px}.sidebar-closed[_nghost-%COMP%]   .menu-item[_ngcontent-%COMP%], .sidebar-closed   [_nghost-%COMP%]   .menu-item[_ngcontent-%COMP%]{justify-content:center;padding:.8rem 0}.sidebar-closed[_nghost-%COMP%]   .menu-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%], .sidebar-closed   [_nghost-%COMP%]   .menu-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:0}@media (max-width: 991.98px){.sidebar[_ngcontent-%COMP%]{width:250px;position:fixed;z-index:1050}.sidebar-closed[_nghost-%COMP%]   .sidebar[_ngcontent-%COMP%], .sidebar-closed   [_nghost-%COMP%]   .sidebar[_ngcontent-%COMP%]{transform:translate(-100%)}.sidebar-open[_nghost-%COMP%]   .sidebar[_ngcontent-%COMP%], .sidebar-open   [_nghost-%COMP%]   .sidebar[_ngcontent-%COMP%]{transform:translate(0)}}\"]\n    });\n  }\n  return SidebarComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}