{"ast": null, "code": "import { Observable } from '../Observable';\nimport { iterator as Symbol_iterator } from '../symbol/iterator';\nimport { isFunction } from '../util/isFunction';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function scheduleIterable(input, scheduler) {\n  return new Observable(subscriber => {\n    let iterator;\n    executeSchedule(subscriber, scheduler, () => {\n      iterator = input[Symbol_iterator]();\n      executeSchedule(subscriber, scheduler, () => {\n        let value;\n        let done;\n        try {\n          ({\n            value,\n            done\n          } = iterator.next());\n        } catch (err) {\n          subscriber.error(err);\n          return;\n        }\n        if (done) {\n          subscriber.complete();\n        } else {\n          subscriber.next(value);\n        }\n      }, 0, true);\n    });\n    return () => isFunction(iterator === null || iterator === void 0 ? void 0 : iterator.return) && iterator.return();\n  });\n}\n//# sourceMappingURL=scheduleIterable.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}