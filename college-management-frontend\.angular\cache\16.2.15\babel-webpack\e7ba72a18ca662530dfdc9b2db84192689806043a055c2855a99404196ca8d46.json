{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ClassesRoutingModule } from './classes-routing.module';\nimport { MaterialModule } from 'src/app/material';\nimport * as i0 from \"@angular/core\";\nexport let ClassesModule = /*#__PURE__*/(() => {\n  class ClassesModule {\n    static #_ = this.ɵfac = function ClassesModule_Factory(t) {\n      return new (t || ClassesModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ClassesModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ClassesRoutingModule, MaterialModule]\n    });\n  }\n  return ClassesModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}