{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/dashboard.service\";\nimport * as i2 from \"../../../services/program.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/progress-spinner\";\nimport * as i9 from \"@angular/material/tooltip\";\nfunction DashboardOverviewComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading dashboard statistics...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardOverviewComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"mat-icon\", 6);\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function DashboardOverviewComponent_div_2_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.refreshData());\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" Retry \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction DashboardOverviewComponent_div_3_div_81_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"span\", 40);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 41);\n    i0.ɵɵelement(4, \"div\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 43);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r12 = ctx.$implicit;\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r12.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", item_r12.value / ctx_r11.getTotalActiveUsers() * 100, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r12.value);\n  }\n}\nfunction DashboardOverviewComponent_div_3_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"div\", 37);\n    i0.ɵɵtemplate(2, DashboardOverviewComponent_div_3_div_81_div_2_Template, 7, 4, \"div\", 38);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.programChartData);\n  }\n}\nfunction DashboardOverviewComponent_div_3_ng_template_82_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 44);\n    i0.ɵɵtext(1, \"No data available\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardOverviewComponent_div_3_div_95_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵelement(1, \"span\", 48);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r14 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(item_r14.name.toLowerCase());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", item_r14.name, \": \", item_r14.value, \"\");\n  }\n}\nfunction DashboardOverviewComponent_div_3_div_95_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtemplate(1, DashboardOverviewComponent_div_3_div_95_div_1_Template, 4, 4, \"div\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.attendanceChartData);\n  }\n}\nfunction DashboardOverviewComponent_div_3_div_102_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"span\", 40);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 41);\n    i0.ɵɵelement(4, \"div\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 43);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r16 = ctx.$implicit;\n    const ctx_r15 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r16.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", item_r16.value / ctx_r15.getTotalActiveUsers() * 100, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r16.value);\n  }\n}\nfunction DashboardOverviewComponent_div_3_div_102_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtemplate(1, DashboardOverviewComponent_div_3_div_102_div_1_Template, 7, 4, \"div\", 38);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r9.departmentChartData);\n  }\n}\nfunction DashboardOverviewComponent_div_3_div_108_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"span\", 40);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 41);\n    i0.ɵɵelement(4, \"div\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 43);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r18 = ctx.$implicit;\n    const ctx_r17 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r18.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", item_r18.value / ctx_r17.dashboardStats.totals.students * 100, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r18.value);\n  }\n}\nfunction DashboardOverviewComponent_div_3_div_108_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtemplate(1, DashboardOverviewComponent_div_3_div_108_div_1_Template, 7, 4, \"div\", 38);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.semesterChartData);\n  }\n}\nfunction DashboardOverviewComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"h1\");\n    i0.ɵɵtext(3, \"Principal Dashboard\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function DashboardOverviewComponent_div_3_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.refreshData());\n    });\n    i0.ɵɵelementStart(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"refresh\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 11)(8, \"mat-card\", 12)(9, \"mat-card-content\")(10, \"div\", 13)(11, \"div\", 14)(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"school\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 15)(15, \"h3\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p\");\n    i0.ɵɵtext(18, \"Total Students\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(19, \"mat-card\", 16)(20, \"mat-card-content\")(21, \"div\", 13)(22, \"div\", 14)(23, \"mat-icon\");\n    i0.ɵɵtext(24, \"person\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 15)(26, \"h3\");\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"p\");\n    i0.ɵɵtext(29, \"Total Teachers\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(30, \"mat-card\", 17)(31, \"mat-card-content\")(32, \"div\", 13)(33, \"div\", 14)(34, \"mat-icon\");\n    i0.ɵɵtext(35, \"library_books\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"div\", 15)(37, \"h3\");\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"p\");\n    i0.ɵɵtext(40, \"Active Programs\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(41, \"mat-card\", 18)(42, \"mat-card-content\")(43, \"div\", 13)(44, \"div\", 14)(45, \"mat-icon\");\n    i0.ɵɵtext(46, \"business\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"div\", 15)(48, \"h3\");\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"p\");\n    i0.ɵɵtext(51, \"Departments\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(52, \"mat-card\", 19)(53, \"mat-card-content\")(54, \"div\", 13)(55, \"div\", 14)(56, \"mat-icon\");\n    i0.ɵɵtext(57, \"class\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(58, \"div\", 15)(59, \"h3\");\n    i0.ɵɵtext(60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(61, \"p\");\n    i0.ɵɵtext(62, \"Active Classes\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(63, \"mat-card\", 20)(64, \"mat-card-content\")(65, \"div\", 13)(66, \"div\", 14)(67, \"mat-icon\");\n    i0.ɵɵtext(68, \"subject\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(69, \"div\", 15)(70, \"h3\");\n    i0.ɵɵtext(71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(72, \"p\");\n    i0.ɵɵtext(73, \"Total Subjects\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(74, \"div\", 21)(75, \"div\", 22)(76, \"mat-card\", 23)(77, \"mat-card-header\")(78, \"mat-card-title\");\n    i0.ɵɵtext(79, \"Students by Program\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(80, \"mat-card-content\");\n    i0.ɵɵtemplate(81, DashboardOverviewComponent_div_3_div_81_Template, 3, 1, \"div\", 24);\n    i0.ɵɵtemplate(82, DashboardOverviewComponent_div_3_ng_template_82_Template, 2, 0, \"ng-template\", null, 25, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(84, \"mat-card\", 23)(85, \"mat-card-header\")(86, \"mat-card-title\");\n    i0.ɵɵtext(87, \"Attendance Overview (Last 7 Days)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(88, \"mat-card-content\")(89, \"div\", 26)(90, \"div\", 27)(91, \"h2\");\n    i0.ɵɵtext(92);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(93, \"p\");\n    i0.ɵɵtext(94, \"Overall Attendance\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(95, DashboardOverviewComponent_div_3_div_95_Template, 2, 1, \"div\", 28);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(96, \"div\", 22)(97, \"mat-card\", 23)(98, \"mat-card-header\")(99, \"mat-card-title\");\n    i0.ɵɵtext(100, \"Students by Department\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(101, \"mat-card-content\");\n    i0.ɵɵtemplate(102, DashboardOverviewComponent_div_3_div_102_Template, 2, 1, \"div\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(103, \"mat-card\", 23)(104, \"mat-card-header\")(105, \"mat-card-title\");\n    i0.ɵɵtext(106, \"Students by Semester\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(107, \"mat-card-content\");\n    i0.ɵɵtemplate(108, DashboardOverviewComponent_div_3_div_108_Template, 2, 1, \"div\", 29);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(109, \"div\", 30)(110, \"h2\");\n    i0.ɵɵtext(111, \"\\uD83D\\uDE80 Quick Administrative Actions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(112, \"p\");\n    i0.ɵɵtext(113, \"Common tasks you can perform right away\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(114, \"div\", 31)(115, \"button\", 32)(116, \"mat-icon\");\n    i0.ɵɵtext(117, \"person_add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(118, \" Enroll New Student \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(119, \"button\", 33)(120, \"mat-icon\");\n    i0.ɵɵtext(121, \"person_add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(122, \" Add New Teacher \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(123, \"button\", 34)(124, \"mat-icon\");\n    i0.ɵɵtext(125, \"class\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(126, \" Create New Class \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(127, \"button\", 35)(128, \"mat-icon\");\n    i0.ɵɵtext(129, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(130, \" Add New Department \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const _r6 = i0.ɵɵreference(83);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(16);\n    i0.ɵɵtextInterpolate(ctx_r2.dashboardStats.totals.students);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r2.dashboardStats.totals.teachers);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r2.dashboardStats.totals.programs);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r2.dashboardStats.totals.departments);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r2.dashboardStats.totals.classes);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r2.dashboardStats.totals.subjects);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.programChartData.length > 0)(\"ngIfElse\", _r6);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.getAttendancePercentage(), \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.attendanceChartData.length > 0);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.departmentChartData.length > 0)(\"ngIfElse\", _r6);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.semesterChartData.length > 0)(\"ngIfElse\", _r6);\n  }\n}\nexport class DashboardOverviewComponent {\n  constructor(dashboardService, programService) {\n    this.dashboardService = dashboardService;\n    this.programService = programService;\n    this.dashboardStats = null;\n    this.loading = true;\n    this.error = null;\n    // Chart data\n    this.programChartData = [];\n    this.departmentChartData = [];\n    this.attendanceChartData = [];\n    this.semesterChartData = [];\n  }\n  ngOnInit() {\n    this.loadDashboardStats();\n  }\n  loadDashboardStats() {\n    this.loading = true;\n    this.error = null;\n    this.dashboardService.getPrincipalDashboardStats().subscribe({\n      next: response => {\n        if (response.success) {\n          this.dashboardStats = response.stats;\n          this.prepareChartData();\n        } else {\n          this.error = 'Failed to load dashboard statistics';\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading dashboard stats:', error);\n        this.error = 'Error loading dashboard statistics';\n        this.loading = false;\n      }\n    });\n  }\n  prepareChartData() {\n    if (!this.dashboardStats) return;\n    // Program chart data\n    this.programChartData = this.dashboardStats.programStats.map(stat => ({\n      name: stat.programName,\n      value: stat.studentCount\n    }));\n    // Department chart data\n    this.departmentChartData = this.dashboardStats.departmentStats.map(stat => ({\n      name: stat.departmentName,\n      value: stat.studentCount\n    }));\n    // Attendance chart data\n    this.attendanceChartData = this.dashboardStats.attendanceStats.map(stat => ({\n      name: this.capitalizeFirst(stat._id),\n      value: stat.count\n    }));\n    // Semester chart data\n    this.semesterChartData = this.dashboardStats.semesterStats.map(stat => ({\n      name: `Semester ${stat._id}`,\n      value: stat.count\n    }));\n  }\n  capitalizeFirst(str) {\n    return str.charAt(0).toUpperCase() + str.slice(1);\n  }\n  refreshData() {\n    this.loadDashboardStats();\n  }\n  getAttendancePercentage() {\n    if (!this.dashboardStats?.attendanceStats.length) return 0;\n    const totalAttendance = this.dashboardStats.attendanceStats.reduce((sum, stat) => sum + stat.count, 0);\n    const presentCount = this.dashboardStats.attendanceStats.find(stat => stat._id === 'present')?.count || 0;\n    return totalAttendance > 0 ? Math.round(presentCount / totalAttendance * 100) : 0;\n  }\n  getTotalActiveUsers() {\n    if (!this.dashboardStats) return 0;\n    return this.dashboardStats.totals.students + this.dashboardStats.totals.teachers;\n  }\n  static #_ = this.ɵfac = function DashboardOverviewComponent_Factory(t) {\n    return new (t || DashboardOverviewComponent)(i0.ɵɵdirectiveInject(i1.DashboardService), i0.ɵɵdirectiveInject(i2.ProgramService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: DashboardOverviewComponent,\n    selectors: [[\"app-dashboard-overview\"]],\n    decls: 4,\n    vars: 3,\n    consts: [[1, \"dashboard-overview\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"dashboard-content\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"error-container\"], [\"color\", \"warn\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"dashboard-content\"], [1, \"dashboard-header\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Refresh Data\", 3, \"click\"], [1, \"stats-grid\"], [1, \"stat-card\", \"students\"], [1, \"stat-content\"], [1, \"stat-icon\"], [1, \"stat-info\"], [1, \"stat-card\", \"teachers\"], [1, \"stat-card\", \"programs\"], [1, \"stat-card\", \"departments\"], [1, \"stat-card\", \"classes\"], [1, \"stat-card\", \"subjects\"], [1, \"charts-section\"], [1, \"chart-row\"], [1, \"chart-card\"], [\"class\", \"chart-container\", 4, \"ngIf\", \"ngIfElse\"], [\"noData\", \"\"], [1, \"attendance-summary\"], [1, \"attendance-percentage\"], [\"class\", \"attendance-breakdown\", 4, \"ngIf\"], [\"class\", \"simple-chart\", 4, \"ngIf\", \"ngIfElse\"], [1, \"quick-actions\"], [1, \"action-buttons\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/dashboard/admin/students/add-student\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/dashboard/admin/teacher/teacher-form\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"routerLink\", \"/dashboard/admin/classes/class-form\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"routerLink\", \"/dashboard/admin/department/department-form\"], [1, \"chart-container\"], [1, \"simple-chart\"], [\"class\", \"chart-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"chart-item\"], [1, \"chart-label\"], [1, \"chart-bar\"], [1, \"chart-fill\"], [1, \"chart-value\"], [1, \"no-data\"], [1, \"attendance-breakdown\"], [\"class\", \"attendance-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"attendance-item\"], [1, \"status-indicator\"]],\n    template: function DashboardOverviewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵtemplate(1, DashboardOverviewComponent_div_1_Template, 4, 0, \"div\", 1);\n        i0.ɵɵtemplate(2, DashboardOverviewComponent_div_2_Template, 9, 1, \"div\", 2);\n        i0.ɵɵtemplate(3, DashboardOverviewComponent_div_3_Template, 131, 14, \"div\", 3);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.dashboardStats && !ctx.loading && !ctx.error);\n      }\n    },\n    dependencies: [i3.NgForOf, i3.NgIf, i4.RouterLink, i5.MatButton, i5.MatIconButton, i6.MatCard, i6.MatCardContent, i6.MatCardHeader, i6.MatCardTitle, i7.MatIcon, i8.MatProgressSpinner, i9.MatTooltip],\n    styles: [\".dashboard-overview[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background-color: #f5f5f5;\\n  min-height: 100vh;\\n}\\n\\n.loading-container[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 400px;\\n  text-align: center;\\n}\\n\\n.error-container[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  margin-bottom: 16px;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 30px;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #333;\\n  font-weight: 500;\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 30px;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease-in-out;\\n}\\n\\n.stat-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 8px rgba(0,0,0,0.12);\\n}\\n\\n.stat-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n\\n.stat-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  color: white;\\n}\\n\\n.stat-card.students[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n}\\n\\n.stat-card.teachers[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\\n}\\n\\n.stat-card.programs[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\\n}\\n\\n.stat-card.departments[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\\n}\\n\\n.stat-card.classes[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);\\n}\\n\\n.stat-card.subjects[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);\\n}\\n\\n.stat-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  height: 28px;\\n  width: 28px;\\n}\\n\\n.stat-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 2rem;\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.stat-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 4px 0 0 0;\\n  color: #666;\\n  font-size: 0.9rem;\\n}\\n\\n.charts-section[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.chart-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 20px;\\n  margin-bottom: 20px;\\n}\\n\\n.chart-card[_ngcontent-%COMP%] {\\n  min-height: 300px;\\n}\\n\\n.chart-container[_ngcontent-%COMP%] {\\n  height: 200px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.simple-chart[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.chart-item[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 2fr auto;\\n  gap: 12px;\\n  align-items: center;\\n  margin-bottom: 12px;\\n}\\n\\n.chart-label[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #666;\\n}\\n\\n.chart-bar[_ngcontent-%COMP%] {\\n  height: 20px;\\n  background-color: #e0e0e0;\\n  border-radius: 10px;\\n  overflow: hidden;\\n}\\n\\n.chart-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 10px;\\n  transition: width 0.3s ease;\\n}\\n\\n.chart-value[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #333;\\n  font-size: 0.9rem;\\n}\\n\\n.attendance-summary[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 20px;\\n}\\n\\n.attendance-percentage[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.attendance-percentage[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 3rem;\\n  font-weight: 600;\\n  color: #4caf50;\\n}\\n\\n.attendance-percentage[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 8px 0 0 0;\\n  color: #666;\\n}\\n\\n.attendance-breakdown[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n  width: 100%;\\n}\\n\\n.attendance-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 8px;\\n  background-color: #f9f9f9;\\n  border-radius: 4px;\\n}\\n\\n.status-indicator[_ngcontent-%COMP%] {\\n  width: 12px;\\n  height: 12px;\\n  border-radius: 50%;\\n}\\n\\n.status-indicator.present[_ngcontent-%COMP%] {\\n  background-color: #4caf50;\\n}\\n\\n.status-indicator.absent[_ngcontent-%COMP%] {\\n  background-color: #f44336;\\n}\\n\\n.status-indicator.late[_ngcontent-%COMP%] {\\n  background-color: #ff9800;\\n}\\n\\n.no-data[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: #999;\\n  font-style: italic;\\n  padding: 40px;\\n}\\n\\n.quick-actions[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 24px;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\\n}\\n\\n.quick-actions[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0 0 20px 0;\\n  color: #333;\\n  font-weight: 500;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 12px;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n@media (max-width: 768px) {\\n  .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  \\n  .chart-row[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  \\n  .action-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  \\n  .action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "DashboardOverviewComponent_div_2_Template_button_click_5_listener", "ɵɵrestoreView", "_r4", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "refreshData", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "error", "item_r12", "name", "ɵɵstyleProp", "value", "ctx_r11", "getTotalActiveUsers", "ɵɵtemplate", "DashboardOverviewComponent_div_3_div_81_div_2_Template", "ɵɵproperty", "ctx_r5", "programChartData", "ɵɵclassMap", "item_r14", "toLowerCase", "ɵɵtextInterpolate2", "DashboardOverviewComponent_div_3_div_95_div_1_Template", "ctx_r8", "attendanceChartData", "item_r16", "ctx_r15", "DashboardOverviewComponent_div_3_div_102_div_1_Template", "ctx_r9", "departmentChartData", "item_r18", "ctx_r17", "dashboardStats", "totals", "students", "DashboardOverviewComponent_div_3_div_108_div_1_Template", "ctx_r10", "semesterChartData", "DashboardOverviewComponent_div_3_Template_button_click_4_listener", "_r20", "ctx_r19", "DashboardOverviewComponent_div_3_div_81_Template", "DashboardOverviewComponent_div_3_ng_template_82_Template", "ɵɵtemplateRefExtractor", "DashboardOverviewComponent_div_3_div_95_Template", "DashboardOverviewComponent_div_3_div_102_Template", "DashboardOverviewComponent_div_3_div_108_Template", "ctx_r2", "teachers", "programs", "departments", "classes", "subjects", "length", "_r6", "ɵɵtextInterpolate1", "getAttendancePercentage", "DashboardOverviewComponent", "constructor", "dashboardService", "programService", "loading", "ngOnInit", "loadDashboardStats", "getPrincipalDashboardStats", "subscribe", "next", "response", "success", "stats", "prepareChartData", "console", "programStats", "map", "stat", "programName", "studentCount", "departmentStats", "departmentName", "attendanceStats", "capitalizeFirst", "_id", "count", "semesterStats", "str", "char<PERSON>t", "toUpperCase", "slice", "totalAttendance", "reduce", "sum", "presentCount", "find", "Math", "round", "_", "ɵɵdirectiveInject", "i1", "DashboardService", "i2", "ProgramService", "_2", "selectors", "decls", "vars", "consts", "template", "DashboardOverviewComponent_Template", "rf", "ctx", "DashboardOverviewComponent_div_1_Template", "DashboardOverviewComponent_div_2_Template", "DashboardOverviewComponent_div_3_Template"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\dashboard-overview\\dashboard-overview.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\dashboard-overview\\dashboard-overview.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { DashboardService } from '../../../services/dashboard.service';\r\nimport { ProgramService } from '../../../services/program.service';\r\nimport { DashboardStats } from '../../../models/dashboard';\r\n\r\n@Component({\r\n  selector: 'app-dashboard-overview',\r\n  templateUrl: './dashboard-overview.component.html',\r\n  styleUrls: ['./dashboard-overview.component.css']\r\n})\r\nexport class DashboardOverviewComponent implements OnInit {\r\n  dashboardStats: DashboardStats | null = null;\r\n  loading = true;\r\n  error: string | null = null;\r\n\r\n  // Chart data\r\n  programChartData: any[] = [];\r\n  departmentChartData: any[] = [];\r\n  attendanceChartData: any[] = [];\r\n  semesterChartData: any[] = [];\r\n\r\n  constructor(\r\n    private dashboardService: DashboardService,\r\n    private programService: ProgramService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loadDashboardStats();\r\n  }\r\n\r\n  loadDashboardStats(): void {\r\n    this.loading = true;\r\n    this.error = null;\r\n\r\n    this.dashboardService.getPrincipalDashboardStats().subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.dashboardStats = response.stats;\r\n          this.prepareChartData();\r\n        } else {\r\n          this.error = 'Failed to load dashboard statistics';\r\n        }\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading dashboard stats:', error);\r\n        this.error = 'Error loading dashboard statistics';\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  private prepareChartData(): void {\r\n    if (!this.dashboardStats) return;\r\n\r\n    // Program chart data\r\n    this.programChartData = this.dashboardStats.programStats.map(stat => ({\r\n      name: stat.programName,\r\n      value: stat.studentCount\r\n    }));\r\n\r\n    // Department chart data\r\n    this.departmentChartData = this.dashboardStats.departmentStats.map(stat => ({\r\n      name: stat.departmentName,\r\n      value: stat.studentCount\r\n    }));\r\n\r\n    // Attendance chart data\r\n    this.attendanceChartData = this.dashboardStats.attendanceStats.map(stat => ({\r\n      name: this.capitalizeFirst(stat._id),\r\n      value: stat.count\r\n    }));\r\n\r\n    // Semester chart data\r\n    this.semesterChartData = this.dashboardStats.semesterStats.map(stat => ({\r\n      name: `Semester ${stat._id}`,\r\n      value: stat.count\r\n    }));\r\n  }\r\n\r\n  private capitalizeFirst(str: string): string {\r\n    return str.charAt(0).toUpperCase() + str.slice(1);\r\n  }\r\n\r\n  refreshData(): void {\r\n    this.loadDashboardStats();\r\n  }\r\n\r\n  getAttendancePercentage(): number {\r\n    if (!this.dashboardStats?.attendanceStats.length) return 0;\r\n    \r\n    const totalAttendance = this.dashboardStats.attendanceStats.reduce((sum, stat) => sum + stat.count, 0);\r\n    const presentCount = this.dashboardStats.attendanceStats.find(stat => stat._id === 'present')?.count || 0;\r\n    \r\n    return totalAttendance > 0 ? Math.round((presentCount / totalAttendance) * 100) : 0;\r\n  }\r\n\r\n  getTotalActiveUsers(): number {\r\n    if (!this.dashboardStats) return 0;\r\n    return this.dashboardStats.totals.students + this.dashboardStats.totals.teachers;\r\n  }\r\n}\r\n", "<div class=\"dashboard-overview\">\r\n  <!-- Loading State -->\r\n  <div *ngIf=\"loading\" class=\"loading-container\">\r\n    <mat-spinner></mat-spinner>\r\n    <p>Loading dashboard statistics...</p>\r\n  </div>\r\n\r\n  <!-- Error State -->\r\n  <div *ngIf=\"error && !loading\" class=\"error-container\">\r\n    <mat-icon color=\"warn\">error</mat-icon>\r\n    <p>{{ error }}</p>\r\n    <button mat-raised-button color=\"primary\" (click)=\"refreshData()\">\r\n      <mat-icon>refresh</mat-icon>\r\n      Retry\r\n    </button>\r\n  </div>\r\n\r\n  <!-- Dashboard Content -->\r\n  <div *ngIf=\"dashboardStats && !loading && !error\" class=\"dashboard-content\">\r\n    <!-- Header -->\r\n    <div class=\"dashboard-header\">\r\n      <h1>Principal Dashboard</h1>\r\n      <button mat-icon-button (click)=\"refreshData()\" matTooltip=\"Refresh Data\">\r\n        <mat-icon>refresh</mat-icon>\r\n      </button>\r\n    </div>\r\n\r\n    <!-- Statistics Cards -->\r\n    <div class=\"stats-grid\">\r\n      <mat-card class=\"stat-card students\">\r\n        <mat-card-content>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon\">\r\n              <mat-icon>school</mat-icon>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ dashboardStats.totals.students }}</h3>\r\n              <p>Total Students</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n\r\n      <mat-card class=\"stat-card teachers\">\r\n        <mat-card-content>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon\">\r\n              <mat-icon>person</mat-icon>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ dashboardStats.totals.teachers }}</h3>\r\n              <p>Total Teachers</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n\r\n      <mat-card class=\"stat-card programs\">\r\n        <mat-card-content>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon\">\r\n              <mat-icon>library_books</mat-icon>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ dashboardStats.totals.programs }}</h3>\r\n              <p>Active Programs</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n\r\n      <mat-card class=\"stat-card departments\">\r\n        <mat-card-content>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon\">\r\n              <mat-icon>business</mat-icon>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ dashboardStats.totals.departments }}</h3>\r\n              <p>Departments</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n\r\n      <mat-card class=\"stat-card classes\">\r\n        <mat-card-content>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon\">\r\n              <mat-icon>class</mat-icon>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ dashboardStats.totals.classes }}</h3>\r\n              <p>Active Classes</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n\r\n      <mat-card class=\"stat-card subjects\">\r\n        <mat-card-content>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon\">\r\n              <mat-icon>subject</mat-icon>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ dashboardStats.totals.subjects }}</h3>\r\n              <p>Total Subjects</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n    </div>\r\n\r\n    <!-- Charts Section -->\r\n    <div class=\"charts-section\">\r\n      <div class=\"chart-row\">\r\n        <!-- Program Distribution -->\r\n        <mat-card class=\"chart-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Students by Program</mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div class=\"chart-container\" *ngIf=\"programChartData.length > 0; else noData\">\r\n              <!-- Add chart component here when available -->\r\n              <div class=\"simple-chart\">\r\n                <div *ngFor=\"let item of programChartData\" class=\"chart-item\">\r\n                  <span class=\"chart-label\">{{ item.name }}</span>\r\n                  <div class=\"chart-bar\">\r\n                    <div class=\"chart-fill\" [style.width.%]=\"(item.value / getTotalActiveUsers()) * 100\"></div>\r\n                  </div>\r\n                  <span class=\"chart-value\">{{ item.value }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <ng-template #noData>\r\n              <p class=\"no-data\">No data available</p>\r\n            </ng-template>\r\n          </mat-card-content>\r\n        </mat-card>\r\n\r\n        <!-- Attendance Overview -->\r\n        <mat-card class=\"chart-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Attendance Overview (Last 7 Days)</mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div class=\"attendance-summary\">\r\n              <div class=\"attendance-percentage\">\r\n                <h2>{{ getAttendancePercentage() }}%</h2>\r\n                <p>Overall Attendance</p>\r\n              </div>\r\n              <div class=\"attendance-breakdown\" *ngIf=\"attendanceChartData.length > 0\">\r\n                <div *ngFor=\"let item of attendanceChartData\" class=\"attendance-item\">\r\n                  <span class=\"status-indicator\" [class]=\"item.name.toLowerCase()\"></span>\r\n                  <span>{{ item.name }}: {{ item.value }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n      </div>\r\n\r\n      <div class=\"chart-row\">\r\n        <!-- Department Distribution -->\r\n        <mat-card class=\"chart-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Students by Department</mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div class=\"simple-chart\" *ngIf=\"departmentChartData.length > 0; else noData\">\r\n              <div *ngFor=\"let item of departmentChartData\" class=\"chart-item\">\r\n                <span class=\"chart-label\">{{ item.name }}</span>\r\n                <div class=\"chart-bar\">\r\n                  <div class=\"chart-fill\" [style.width.%]=\"(item.value / getTotalActiveUsers()) * 100\"></div>\r\n                </div>\r\n                <span class=\"chart-value\">{{ item.value }}</span>\r\n              </div>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n\r\n        <!-- Semester Distribution -->\r\n        <mat-card class=\"chart-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Students by Semester</mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div class=\"simple-chart\" *ngIf=\"semesterChartData.length > 0; else noData\">\r\n              <div *ngFor=\"let item of semesterChartData\" class=\"chart-item\">\r\n                <span class=\"chart-label\">{{ item.name }}</span>\r\n                <div class=\"chart-bar\">\r\n                  <div class=\"chart-fill\" [style.width.%]=\"(item.value / dashboardStats.totals.students) * 100\"></div>\r\n                </div>\r\n                <span class=\"chart-value\">{{ item.value }}</span>\r\n              </div>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Quick Actions -->\r\n    <div class=\"quick-actions\">\r\n      <h2>🚀 Quick Administrative Actions</h2>\r\n      <p>Common tasks you can perform right away</p>\r\n      <div class=\"action-buttons\">\r\n        <button mat-raised-button color=\"primary\" routerLink=\"/dashboard/admin/students/add-student\">\r\n          <mat-icon>person_add</mat-icon>\r\n          Enroll New Student\r\n        </button>\r\n        <button mat-raised-button color=\"primary\" routerLink=\"/dashboard/admin/teacher/teacher-form\">\r\n          <mat-icon>person_add</mat-icon>\r\n          Add New Teacher\r\n        </button>\r\n        <button mat-raised-button color=\"accent\" routerLink=\"/dashboard/admin/classes/class-form\">\r\n          <mat-icon>class</mat-icon>\r\n          Create New Class\r\n        </button>\r\n        <button mat-raised-button color=\"accent\" routerLink=\"/dashboard/admin/department/department-form\">\r\n          <mat-icon>business</mat-icon>\r\n          Add New Department\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": ";;;;;;;;;;;;ICEEA,EAAA,CAAAC,cAAA,aAA+C;IAC7CD,EAAA,CAAAE,SAAA,kBAA2B;IAC3BF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,sCAA+B;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;;IAIxCJ,EAAA,CAAAC,cAAA,aAAuD;IAC9BD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACvCJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAW;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAClBJ,EAAA,CAAAC,cAAA,gBAAkE;IAAxBD,EAAA,CAAAK,UAAA,mBAAAC,kEAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAC/DZ,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC5BJ,EAAA,CAAAG,MAAA,cACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAJNJ,EAAA,CAAAa,SAAA,GAAW;IAAXb,EAAA,CAAAc,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IAoHFhB,EAAA,CAAAC,cAAA,cAA8D;IAClCD,EAAA,CAAAG,MAAA,GAAe;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAChDJ,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAE,SAAA,cAA2F;IAC7FF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAG,MAAA,GAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAJvBJ,EAAA,CAAAa,SAAA,GAAe;IAAfb,EAAA,CAAAc,iBAAA,CAAAG,QAAA,CAAAC,IAAA,CAAe;IAEflB,EAAA,CAAAa,SAAA,GAA4D;IAA5Db,EAAA,CAAAmB,WAAA,UAAAF,QAAA,CAAAG,KAAA,GAAAC,OAAA,CAAAC,mBAAA,cAA4D;IAE5DtB,EAAA,CAAAa,SAAA,GAAgB;IAAhBb,EAAA,CAAAc,iBAAA,CAAAG,QAAA,CAAAG,KAAA,CAAgB;;;;;IARhDpB,EAAA,CAAAC,cAAA,cAA8E;IAG1ED,EAAA,CAAAuB,UAAA,IAAAC,sDAAA,kBAMM;IACRxB,EAAA,CAAAI,YAAA,EAAM;;;;IAPkBJ,EAAA,CAAAa,SAAA,GAAmB;IAAnBb,EAAA,CAAAyB,UAAA,YAAAC,MAAA,CAAAC,gBAAA,CAAmB;;;;;IAU3C3B,EAAA,CAAAC,cAAA,YAAmB;IAAAD,EAAA,CAAAG,MAAA,wBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAiBtCJ,EAAA,CAAAC,cAAA,cAAsE;IACpED,EAAA,CAAAE,SAAA,eAAwE;IACxEF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,GAAiC;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IADfJ,EAAA,CAAAa,SAAA,GAAiC;IAAjCb,EAAA,CAAA4B,UAAA,CAAAC,QAAA,CAAAX,IAAA,CAAAY,WAAA,GAAiC;IAC1D9B,EAAA,CAAAa,SAAA,GAAiC;IAAjCb,EAAA,CAAA+B,kBAAA,KAAAF,QAAA,CAAAX,IAAA,QAAAW,QAAA,CAAAT,KAAA,KAAiC;;;;;IAH3CpB,EAAA,CAAAC,cAAA,cAAyE;IACvED,EAAA,CAAAuB,UAAA,IAAAS,sDAAA,kBAGM;IACRhC,EAAA,CAAAI,YAAA,EAAM;;;;IAJkBJ,EAAA,CAAAa,SAAA,GAAsB;IAAtBb,EAAA,CAAAyB,UAAA,YAAAQ,MAAA,CAAAC,mBAAA,CAAsB;;;;;IAkB9ClC,EAAA,CAAAC,cAAA,cAAiE;IACrCD,EAAA,CAAAG,MAAA,GAAe;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAChDJ,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAE,SAAA,cAA2F;IAC7FF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAG,MAAA,GAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAJvBJ,EAAA,CAAAa,SAAA,GAAe;IAAfb,EAAA,CAAAc,iBAAA,CAAAqB,QAAA,CAAAjB,IAAA,CAAe;IAEflB,EAAA,CAAAa,SAAA,GAA4D;IAA5Db,EAAA,CAAAmB,WAAA,UAAAgB,QAAA,CAAAf,KAAA,GAAAgB,OAAA,CAAAd,mBAAA,cAA4D;IAE5DtB,EAAA,CAAAa,SAAA,GAAgB;IAAhBb,EAAA,CAAAc,iBAAA,CAAAqB,QAAA,CAAAf,KAAA,CAAgB;;;;;IAN9CpB,EAAA,CAAAC,cAAA,cAA8E;IAC5ED,EAAA,CAAAuB,UAAA,IAAAc,uDAAA,kBAMM;IACRrC,EAAA,CAAAI,YAAA,EAAM;;;;IAPkBJ,EAAA,CAAAa,SAAA,GAAsB;IAAtBb,EAAA,CAAAyB,UAAA,YAAAa,MAAA,CAAAC,mBAAA,CAAsB;;;;;IAkB5CvC,EAAA,CAAAC,cAAA,cAA+D;IACnCD,EAAA,CAAAG,MAAA,GAAe;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAChDJ,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAE,SAAA,cAAoG;IACtGF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAG,MAAA,GAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAJvBJ,EAAA,CAAAa,SAAA,GAAe;IAAfb,EAAA,CAAAc,iBAAA,CAAA0B,QAAA,CAAAtB,IAAA,CAAe;IAEflB,EAAA,CAAAa,SAAA,GAAqE;IAArEb,EAAA,CAAAmB,WAAA,UAAAqB,QAAA,CAAApB,KAAA,GAAAqB,OAAA,CAAAC,cAAA,CAAAC,MAAA,CAAAC,QAAA,YAAqE;IAErE5C,EAAA,CAAAa,SAAA,GAAgB;IAAhBb,EAAA,CAAAc,iBAAA,CAAA0B,QAAA,CAAApB,KAAA,CAAgB;;;;;IAN9CpB,EAAA,CAAAC,cAAA,cAA4E;IAC1ED,EAAA,CAAAuB,UAAA,IAAAsB,uDAAA,kBAMM;IACR7C,EAAA,CAAAI,YAAA,EAAM;;;;IAPkBJ,EAAA,CAAAa,SAAA,GAAoB;IAApBb,EAAA,CAAAyB,UAAA,YAAAqB,OAAA,CAAAC,iBAAA,CAAoB;;;;;;IA3KtD/C,EAAA,CAAAC,cAAA,aAA4E;IAGpED,EAAA,CAAAG,MAAA,0BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC5BJ,EAAA,CAAAC,cAAA,iBAA0E;IAAlDD,EAAA,CAAAK,UAAA,mBAAA2C,kEAAA;MAAAhD,EAAA,CAAAO,aAAA,CAAA0C,IAAA;MAAA,MAAAC,OAAA,GAAAlD,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAuC,OAAA,CAAAtC,WAAA,EAAa;IAAA,EAAC;IAC7CZ,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAKhCJ,EAAA,CAAAC,cAAA,cAAwB;IAKJD,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAE7BJ,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAG,MAAA,IAAoC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC7CJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,sBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAM7BJ,EAAA,CAAAC,cAAA,oBAAqC;IAInBD,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAE7BJ,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAG,MAAA,IAAoC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC7CJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,sBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAM7BJ,EAAA,CAAAC,cAAA,oBAAqC;IAInBD,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAEpCJ,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAG,MAAA,IAAoC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC7CJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,uBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAM9BJ,EAAA,CAAAC,cAAA,oBAAwC;IAItBD,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAE/BJ,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAG,MAAA,IAAuC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAChDJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,mBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAM1BJ,EAAA,CAAAC,cAAA,oBAAoC;IAIlBD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAE5BJ,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAG,MAAA,IAAmC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC5CJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,sBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAM7BJ,EAAA,CAAAC,cAAA,oBAAqC;IAInBD,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAE9BJ,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAG,MAAA,IAAoC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC7CJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,sBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAQ/BJ,EAAA,CAAAC,cAAA,eAA4B;IAKJD,EAAA,CAAAG,MAAA,2BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IAEtDJ,EAAA,CAAAC,cAAA,wBAAkB;IAChBD,EAAA,CAAAuB,UAAA,KAAA4B,gDAAA,kBAWM;IACNnD,EAAA,CAAAuB,UAAA,KAAA6B,wDAAA,iCAAApD,EAAA,CAAAqD,sBAAA,CAEc;IAChBrD,EAAA,CAAAI,YAAA,EAAmB;IAIrBJ,EAAA,CAAAC,cAAA,oBAA6B;IAETD,EAAA,CAAAG,MAAA,yCAAiC;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IAEpEJ,EAAA,CAAAC,cAAA,wBAAkB;IAGRD,EAAA,CAAAG,MAAA,IAAgC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACzCJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,0BAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAE3BJ,EAAA,CAAAuB,UAAA,KAAA+B,gDAAA,kBAKM;IACRtD,EAAA,CAAAI,YAAA,EAAM;IAKZJ,EAAA,CAAAC,cAAA,eAAuB;IAIDD,EAAA,CAAAG,MAAA,+BAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IAEzDJ,EAAA,CAAAC,cAAA,yBAAkB;IAChBD,EAAA,CAAAuB,UAAA,MAAAgC,iDAAA,kBAQM;IACRvD,EAAA,CAAAI,YAAA,EAAmB;IAIrBJ,EAAA,CAAAC,cAAA,qBAA6B;IAETD,EAAA,CAAAG,MAAA,6BAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IAEvDJ,EAAA,CAAAC,cAAA,yBAAkB;IAChBD,EAAA,CAAAuB,UAAA,MAAAiC,iDAAA,kBAQM;IACRxD,EAAA,CAAAI,YAAA,EAAmB;IAMzBJ,EAAA,CAAAC,cAAA,gBAA2B;IACrBD,EAAA,CAAAG,MAAA,kDAA+B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACxCJ,EAAA,CAAAC,cAAA,UAAG;IAAAD,EAAA,CAAAG,MAAA,gDAAuC;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAC9CJ,EAAA,CAAAC,cAAA,gBAA4B;IAEdD,EAAA,CAAAG,MAAA,mBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC/BJ,EAAA,CAAAG,MAAA,6BACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,mBAA6F;IACjFD,EAAA,CAAAG,MAAA,mBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC/BJ,EAAA,CAAAG,MAAA,0BACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,mBAA0F;IAC9ED,EAAA,CAAAG,MAAA,cAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC1BJ,EAAA,CAAAG,MAAA,2BACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,mBAAkG;IACtFD,EAAA,CAAAG,MAAA,iBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC7BJ,EAAA,CAAAG,MAAA,6BACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;IA1LCJ,EAAA,CAAAa,SAAA,IAAoC;IAApCb,EAAA,CAAAc,iBAAA,CAAA2C,MAAA,CAAAf,cAAA,CAAAC,MAAA,CAAAC,QAAA,CAAoC;IAcpC5C,EAAA,CAAAa,SAAA,IAAoC;IAApCb,EAAA,CAAAc,iBAAA,CAAA2C,MAAA,CAAAf,cAAA,CAAAC,MAAA,CAAAe,QAAA,CAAoC;IAcpC1D,EAAA,CAAAa,SAAA,IAAoC;IAApCb,EAAA,CAAAc,iBAAA,CAAA2C,MAAA,CAAAf,cAAA,CAAAC,MAAA,CAAAgB,QAAA,CAAoC;IAcpC3D,EAAA,CAAAa,SAAA,IAAuC;IAAvCb,EAAA,CAAAc,iBAAA,CAAA2C,MAAA,CAAAf,cAAA,CAAAC,MAAA,CAAAiB,WAAA,CAAuC;IAcvC5D,EAAA,CAAAa,SAAA,IAAmC;IAAnCb,EAAA,CAAAc,iBAAA,CAAA2C,MAAA,CAAAf,cAAA,CAAAC,MAAA,CAAAkB,OAAA,CAAmC;IAcnC7D,EAAA,CAAAa,SAAA,IAAoC;IAApCb,EAAA,CAAAc,iBAAA,CAAA2C,MAAA,CAAAf,cAAA,CAAAC,MAAA,CAAAmB,QAAA,CAAoC;IAiBZ9D,EAAA,CAAAa,SAAA,IAAmC;IAAnCb,EAAA,CAAAyB,UAAA,SAAAgC,MAAA,CAAA9B,gBAAA,CAAAoC,MAAA,KAAmC,aAAAC,GAAA;IA0BzDhE,EAAA,CAAAa,SAAA,IAAgC;IAAhCb,EAAA,CAAAiE,kBAAA,KAAAR,MAAA,CAAAS,uBAAA,QAAgC;IAGHlE,EAAA,CAAAa,SAAA,GAAoC;IAApCb,EAAA,CAAAyB,UAAA,SAAAgC,MAAA,CAAAvB,mBAAA,CAAA6B,MAAA,KAAoC;IAkB9C/D,EAAA,CAAAa,SAAA,GAAsC;IAAtCb,EAAA,CAAAyB,UAAA,SAAAgC,MAAA,CAAAlB,mBAAA,CAAAwB,MAAA,KAAsC,aAAAC,GAAA;IAkBtChE,EAAA,CAAAa,SAAA,GAAoC;IAApCb,EAAA,CAAAyB,UAAA,SAAAgC,MAAA,CAAAV,iBAAA,CAAAgB,MAAA,KAAoC,aAAAC,GAAA;;;ADlL3E,OAAM,MAAOG,0BAA0B;EAWrCC,YACUC,gBAAkC,EAClCC,cAA8B;IAD9B,KAAAD,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,cAAc,GAAdA,cAAc;IAZxB,KAAA5B,cAAc,GAA0B,IAAI;IAC5C,KAAA6B,OAAO,GAAG,IAAI;IACd,KAAAvD,KAAK,GAAkB,IAAI;IAE3B;IACA,KAAAW,gBAAgB,GAAU,EAAE;IAC5B,KAAAY,mBAAmB,GAAU,EAAE;IAC/B,KAAAL,mBAAmB,GAAU,EAAE;IAC/B,KAAAa,iBAAiB,GAAU,EAAE;EAK1B;EAEHyB,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEAA,kBAAkBA,CAAA;IAChB,IAAI,CAACF,OAAO,GAAG,IAAI;IACnB,IAAI,CAACvD,KAAK,GAAG,IAAI;IAEjB,IAAI,CAACqD,gBAAgB,CAACK,0BAA0B,EAAE,CAACC,SAAS,CAAC;MAC3DC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACpC,cAAc,GAAGmC,QAAQ,CAACE,KAAK;UACpC,IAAI,CAACC,gBAAgB,EAAE;SACxB,MAAM;UACL,IAAI,CAAChE,KAAK,GAAG,qCAAqC;;QAEpD,IAAI,CAACuD,OAAO,GAAG,KAAK;MACtB,CAAC;MACDvD,KAAK,EAAGA,KAAK,IAAI;QACfiE,OAAO,CAACjE,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAACA,KAAK,GAAG,oCAAoC;QACjD,IAAI,CAACuD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEQS,gBAAgBA,CAAA;IACtB,IAAI,CAAC,IAAI,CAACtC,cAAc,EAAE;IAE1B;IACA,IAAI,CAACf,gBAAgB,GAAG,IAAI,CAACe,cAAc,CAACwC,YAAY,CAACC,GAAG,CAACC,IAAI,KAAK;MACpElE,IAAI,EAAEkE,IAAI,CAACC,WAAW;MACtBjE,KAAK,EAAEgE,IAAI,CAACE;KACb,CAAC,CAAC;IAEH;IACA,IAAI,CAAC/C,mBAAmB,GAAG,IAAI,CAACG,cAAc,CAAC6C,eAAe,CAACJ,GAAG,CAACC,IAAI,KAAK;MAC1ElE,IAAI,EAAEkE,IAAI,CAACI,cAAc;MACzBpE,KAAK,EAAEgE,IAAI,CAACE;KACb,CAAC,CAAC;IAEH;IACA,IAAI,CAACpD,mBAAmB,GAAG,IAAI,CAACQ,cAAc,CAAC+C,eAAe,CAACN,GAAG,CAACC,IAAI,KAAK;MAC1ElE,IAAI,EAAE,IAAI,CAACwE,eAAe,CAACN,IAAI,CAACO,GAAG,CAAC;MACpCvE,KAAK,EAAEgE,IAAI,CAACQ;KACb,CAAC,CAAC;IAEH;IACA,IAAI,CAAC7C,iBAAiB,GAAG,IAAI,CAACL,cAAc,CAACmD,aAAa,CAACV,GAAG,CAACC,IAAI,KAAK;MACtElE,IAAI,EAAE,YAAYkE,IAAI,CAACO,GAAG,EAAE;MAC5BvE,KAAK,EAAEgE,IAAI,CAACQ;KACb,CAAC,CAAC;EACL;EAEQF,eAAeA,CAACI,GAAW;IACjC,OAAOA,GAAG,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGF,GAAG,CAACG,KAAK,CAAC,CAAC,CAAC;EACnD;EAEArF,WAAWA,CAAA;IACT,IAAI,CAAC6D,kBAAkB,EAAE;EAC3B;EAEAP,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACxB,cAAc,EAAE+C,eAAe,CAAC1B,MAAM,EAAE,OAAO,CAAC;IAE1D,MAAMmC,eAAe,GAAG,IAAI,CAACxD,cAAc,CAAC+C,eAAe,CAACU,MAAM,CAAC,CAACC,GAAG,EAAEhB,IAAI,KAAKgB,GAAG,GAAGhB,IAAI,CAACQ,KAAK,EAAE,CAAC,CAAC;IACtG,MAAMS,YAAY,GAAG,IAAI,CAAC3D,cAAc,CAAC+C,eAAe,CAACa,IAAI,CAAClB,IAAI,IAAIA,IAAI,CAACO,GAAG,KAAK,SAAS,CAAC,EAAEC,KAAK,IAAI,CAAC;IAEzG,OAAOM,eAAe,GAAG,CAAC,GAAGK,IAAI,CAACC,KAAK,CAAEH,YAAY,GAAGH,eAAe,GAAI,GAAG,CAAC,GAAG,CAAC;EACrF;EAEA5E,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACoB,cAAc,EAAE,OAAO,CAAC;IAClC,OAAO,IAAI,CAACA,cAAc,CAACC,MAAM,CAACC,QAAQ,GAAG,IAAI,CAACF,cAAc,CAACC,MAAM,CAACe,QAAQ;EAClF;EAAC,QAAA+C,CAAA,G;qBA1FUtC,0BAA0B,EAAAnE,EAAA,CAAA0G,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAA5G,EAAA,CAAA0G,iBAAA,CAAAG,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA1B5C,0BAA0B;IAAA6C,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCVvCtH,EAAA,CAAAC,cAAA,aAAgC;QAE9BD,EAAA,CAAAuB,UAAA,IAAAiG,yCAAA,iBAGM;QAGNxH,EAAA,CAAAuB,UAAA,IAAAkG,yCAAA,iBAOM;QAGNzH,EAAA,CAAAuB,UAAA,IAAAmG,yCAAA,oBA+MM;QACR1H,EAAA,CAAAI,YAAA,EAAM;;;QAhOEJ,EAAA,CAAAa,SAAA,GAAa;QAAbb,EAAA,CAAAyB,UAAA,SAAA8F,GAAA,CAAAhD,OAAA,CAAa;QAMbvE,EAAA,CAAAa,SAAA,GAAuB;QAAvBb,EAAA,CAAAyB,UAAA,SAAA8F,GAAA,CAAAvG,KAAA,KAAAuG,GAAA,CAAAhD,OAAA,CAAuB;QAUvBvE,EAAA,CAAAa,SAAA,GAA0C;QAA1Cb,EAAA,CAAAyB,UAAA,SAAA8F,GAAA,CAAA7E,cAAA,KAAA6E,GAAA,CAAAhD,OAAA,KAAAgD,GAAA,CAAAvG,KAAA,CAA0C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}