import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { Timetable, TimetableCreateRequest } from '../models/timetable';

@Injectable({
  providedIn: 'root'
})
export class TimetableService {
  private apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) { }

  // Create new timetable entry
  createTimetableEntry(timetable: TimetableCreateRequest): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/timetable`, timetable);
  }

  // Get all timetable entries with filters
  getAllTimetableEntries(filters?: {
    program?: string;
    department?: string;
    class?: string;
    teacher?: string;
    dayOfWeek?: string;
    academicYear?: string;
    semester?: number;
    isActive?: boolean;
  }): Observable<any> {
    let params = new HttpParams();
    
    if (filters) {
      Object.keys(filters).forEach(key => {
        const value = (filters as any)[key];
        if (value !== undefined && value !== null) {
          params = params.set(key, value.toString());
        }
      });
    }

    return this.http.get<any>(`${this.apiUrl}/timetables`, { params });
  }

  // Get timetable by teacher
  getTimetableByTeacher(teacherId: string, academicYear?: string, dayOfWeek?: string): Observable<any> {
    let params = new HttpParams();
    if (academicYear) params = params.set('academicYear', academicYear);
    if (dayOfWeek) params = params.set('dayOfWeek', dayOfWeek);

    return this.http.get<any>(`${this.apiUrl}/timetable/teacher/${teacherId}`, { params });
  }

  // Get timetable by class
  getTimetableByClass(classId: string, academicYear?: string, dayOfWeek?: string): Observable<any> {
    let params = new HttpParams();
    if (academicYear) params = params.set('academicYear', academicYear);
    if (dayOfWeek) params = params.set('dayOfWeek', dayOfWeek);

    return this.http.get<any>(`${this.apiUrl}/timetable/class/${classId}`, { params });
  }

  // Get timetable by student
  getTimetableByStudent(studentId: string, academicYear?: string, dayOfWeek?: string): Observable<any> {
    let params = new HttpParams();
    if (academicYear) params = params.set('academicYear', academicYear);
    if (dayOfWeek) params = params.set('dayOfWeek', dayOfWeek);

    return this.http.get<any>(`${this.apiUrl}/timetable/student/${studentId}`, { params });
  }

  // Get timetable entry by ID
  getTimetableEntryById(id: string): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/timetable/${id}`);
  }

  // Update timetable entry
  updateTimetableEntry(id: string, timetable: Partial<TimetableCreateRequest>): Observable<any> {
    return this.http.put<any>(`${this.apiUrl}/timetable/${id}`, timetable);
  }

  // Update timetable status
  updateTimetableStatus(id: string, isActive: boolean): Observable<any> {
    return this.http.put<any>(`${this.apiUrl}/timetable/${id}`, { isActive });
  }

  // Delete timetable entry
  deleteTimetableEntry(id: string): Observable<any> {
    return this.http.delete<any>(`${this.apiUrl}/timetable/${id}`);
  }
}
