{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { MainComponent } from './main/main.component';\nimport { ChartsComponent } from './charts/charts.component';\nimport { AuthGuard } from '../guards/auth.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: MainComponent,\n  canActivate: [AuthGuard],\n  children: [{\n    path: '',\n    component: ChartsComponent\n  }, {\n    path: 'admin',\n    loadChildren: () => import('./admin-dashboard/admin-dashboard.module').then(m => m.AdminDashboardModule),\n    canActivate: [AuthGuard],\n    data: {\n      roles: ['Principal']\n    }\n  }, {\n    path: 'teacher',\n    loadChildren: () => import('./teacher-dashboard/teacher-dashboard.module').then(m => m.TeacherDashboardModule),\n    canActivate: [AuthGuard],\n    data: {\n      roles: ['Teacher']\n    }\n  }, {\n    path: 'student',\n    loadChildren: () => import('./student-dashboard/student-dashboard.module').then(m => m.StudentDashboardModule),\n    canActivate: [AuthGuard],\n    data: {\n      roles: ['Student']\n    }\n  }]\n}];\nexport class AdministrationRoutingModule {\n  static #_ = this.ɵfac = function AdministrationRoutingModule_Factory(t) {\n    return new (t || AdministrationRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AdministrationRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild(routes), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AdministrationRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "MainComponent", "ChartsComponent", "<PERSON><PERSON><PERSON><PERSON>", "routes", "path", "component", "canActivate", "children", "loadChildren", "then", "m", "AdminDashboardModule", "data", "roles", "TeacherDashboardModule", "StudentDashboardModule", "AdministrationRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\administration-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { MainComponent } from './main/main.component';\r\nimport { ChartsComponent } from './charts/charts.component';\r\nimport { AuthGuard } from '../guards/auth.guard';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: MainComponent,\r\n    canActivate: [AuthGuard],\r\n    children: [\r\n      { path: '', component: ChartsComponent }, // Default dashboard view\r\n      {\r\n        path: 'admin',\r\n        loadChildren: () => import('./admin-dashboard/admin-dashboard.module').then((m) => m.AdminDashboardModule),\r\n        canActivate: [AuthGuard],\r\n        data: { roles: ['Principal'] }\r\n      },\r\n      {\r\n        path: 'teacher',\r\n        loadChildren: () => import('./teacher-dashboard/teacher-dashboard.module').then((m) => m.TeacherDashboardModule),\r\n        canActivate: [AuthGuard],\r\n        data: { roles: ['Teacher'] }\r\n      },\r\n      {\r\n        path: 'student',\r\n        loadChildren: () => import('./student-dashboard/student-dashboard.module').then((m) => m.StudentDashboardModule),\r\n        canActivate: [AuthGuard],\r\n        data: { roles: ['Student'] }\r\n      },\r\n    ]\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class AdministrationRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,SAAS,QAAQ,sBAAsB;;;AAEhD,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEL,aAAa;EACxBM,WAAW,EAAE,CAACJ,SAAS,CAAC;EACxBK,QAAQ,EAAE,CACR;IAAEH,IAAI,EAAE,EAAE;IAAEC,SAAS,EAAEJ;EAAe,CAAE,EACxC;IACEG,IAAI,EAAE,OAAO;IACbI,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,oBAAoB,CAAC;IAC1GL,WAAW,EAAE,CAACJ,SAAS,CAAC;IACxBU,IAAI,EAAE;MAAEC,KAAK,EAAE,CAAC,WAAW;IAAC;GAC7B,EACD;IACET,IAAI,EAAE,SAAS;IACfI,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,8CAA8C,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACI,sBAAsB,CAAC;IAChHR,WAAW,EAAE,CAACJ,SAAS,CAAC;IACxBU,IAAI,EAAE;MAAEC,KAAK,EAAE,CAAC,SAAS;IAAC;GAC3B,EACD;IACET,IAAI,EAAE,SAAS;IACfI,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,8CAA8C,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACK,sBAAsB,CAAC;IAChHT,WAAW,EAAE,CAACJ,SAAS,CAAC;IACxBU,IAAI,EAAE;MAAEC,KAAK,EAAE,CAAC,SAAS;IAAC;GAC3B;CAEJ,CACF;AAMD,OAAM,MAAOG,2BAA2B;EAAA,QAAAC,CAAA,G;qBAA3BD,2BAA2B;EAAA;EAAA,QAAAE,EAAA,G;UAA3BF;EAA2B;EAAA,QAAAG,EAAA,G;cAH5BpB,YAAY,CAACqB,QAAQ,CAACjB,MAAM,CAAC,EAC7BJ,YAAY;EAAA;;;2EAEXiB,2BAA2B;IAAAK,OAAA,GAAAC,EAAA,CAAAvB,YAAA;IAAAwB,OAAA,GAF5BxB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}