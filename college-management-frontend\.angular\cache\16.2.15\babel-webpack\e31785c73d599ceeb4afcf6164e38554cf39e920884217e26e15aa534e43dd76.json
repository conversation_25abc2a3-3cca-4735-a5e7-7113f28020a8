{"ast": null, "code": "import { HttpHeaders, HttpParams } from '@angular/common/http';\nimport { throwError } from 'rxjs';\nimport { catchError, tap } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AttendanceService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = environment.apiUrl;\n  }\n  getAuthHeaders() {\n    const token = localStorage.getItem('token');\n    return new HttpHeaders({\n      'Authorization': `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    });\n  }\n  // Mark batch attendance\n  markBatchAttendance(attendanceData) {\n    return this.http.post(`${this.apiUrl}/mark-batch`, attendanceData, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap({\n      next: response => {\n        console.log('Batch attendance marked successfully:', response);\n      }\n    }), catchError(error => {\n      console.error('Error marking batch attendance:', error);\n      return throwError(() => error);\n    }));\n  }\n  // Get attendance by student\n  getAttendanceByStudent(studentId, date, subjectId) {\n    const params = new HttpParams().set('date', date).set('subjectId', subjectId);\n    return this.http.get(`${this.apiUrl}/student/${studentId}`, {\n      headers: this.getAuthHeaders(),\n      params\n    }).pipe(catchError(error => {\n      console.error('Error fetching student attendance:', error);\n      return throwError(() => error);\n    }));\n  }\n  // Get attendance by class\n  getAttendanceByClass(classId, date, subjectId) {\n    const params = new HttpParams().set('date', date).set('subjectId', subjectId);\n    return this.http.get(`${this.apiUrl}/class/${classId}`, {\n      headers: this.getAuthHeaders(),\n      params\n    }).pipe(catchError(error => {\n      console.error('Error fetching class attendance:', error);\n      return throwError(() => error);\n    }));\n  }\n  // Get all attendance records with filters (for admin)\n  getAllAttendanceRecords(filters) {\n    let params = new HttpParams();\n    if (filters) {\n      Object.keys(filters).forEach(key => {\n        const value = filters[key];\n        if (value !== undefined && value !== null) {\n          params = params.set(key, value.toString());\n        }\n      });\n    }\n    return this.http.get(`${this.apiUrl}/all`, {\n      params\n    });\n  }\n  // Get attendance statistics (for admin)\n  getAttendanceStatistics(filters) {\n    let params = new HttpParams();\n    if (filters) {\n      Object.keys(filters).forEach(key => {\n        const value = filters[key];\n        if (value !== undefined && value !== null) {\n          params = params.set(key, value.toString());\n        }\n      });\n    }\n    return this.http.get(`${this.apiUrl}/statistics`, {\n      params\n    });\n  }\n  // Get student attendance statistics\n  getStudentAttendanceStats(studentId, academicYear) {\n    let params = new HttpParams();\n    if (academicYear) params = params.set('academicYear', academicYear);\n    return this.http.get(`${this.apiUrl}/attendance/student/${studentId}/stats`, {\n      params\n    });\n  }\n  // Get all attendance records for a student\n  getStudentAttendance(studentId, academicYear) {\n    let params = new HttpParams();\n    if (academicYear) params = params.set('academicYear', academicYear);\n    return this.http.get(`${this.apiUrl}/student/${studentId}/all`, {\n      headers: this.getAuthHeaders(),\n      params\n    }).pipe(catchError(error => {\n      console.error('Error fetching student attendance records:', error);\n      return throwError(() => error);\n    }));\n  }\n  // Get class attendance statistics\n  getClassAttendanceStats(classId, date) {\n    let params = new HttpParams();\n    if (date) params = params.set('date', date);\n    return this.http.get(`${this.apiUrl}/attendance/class/${classId}/stats`, {\n      params\n    });\n  }\n  static #_ = this.ɵfac = function AttendanceService_Factory(t) {\n    return new (t || AttendanceService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AttendanceService,\n    factory: AttendanceService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "HttpParams", "throwError", "catchError", "tap", "environment", "AttendanceService", "constructor", "http", "apiUrl", "getAuthHeaders", "token", "localStorage", "getItem", "markBatchAttendance", "attendanceData", "post", "headers", "pipe", "next", "response", "console", "log", "error", "getAttendanceByStudent", "studentId", "date", "subjectId", "params", "set", "get", "getAttendanceByClass", "classId", "getAllAttendanceRecords", "filters", "Object", "keys", "for<PERSON>ach", "key", "value", "undefined", "toString", "getAttendanceStatistics", "getStudentAttendanceStats", "academicYear", "getStudentAttendance", "getClassAttendanceStats", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\services\\attendance.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';\r\nimport { Observable, throwError } from 'rxjs';\r\nimport { catchError, tap } from 'rxjs/operators';\r\nimport { environment } from 'src/environments/environment';\r\nimport { AttendanceRequest } from '../models/attendance';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AttendanceService {\r\n  private apiUrl = environment.apiUrl;\r\n\r\n  constructor(private http: HttpClient) { }\r\n\r\n  private getAuthHeaders(): HttpHeaders {\r\n    const token = localStorage.getItem('token');\r\n    return new HttpHeaders({\r\n      'Authorization': `Bearer ${token}`,\r\n      'Content-Type': 'application/json'\r\n    });\r\n  }\r\n\r\n  // Mark batch attendance\r\n  markBatchAttendance(attendanceData: AttendanceRequest): Observable<any> {\r\n    return this.http.post<any>(`${this.apiUrl}/mark-batch`, attendanceData, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      tap({\r\n        next: (response) => {\r\n          console.log('Batch attendance marked successfully:', response);\r\n        },\r\n      }),\r\n      catchError((error) => {\r\n        console.error('Error marking batch attendance:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Get attendance by student\r\n  getAttendanceByStudent(studentId: string, date: string, subjectId: string): Observable<any> {\r\n    const params = new HttpParams()\r\n      .set('date', date)\r\n      .set('subjectId', subjectId);\r\n\r\n    return this.http.get<any>(`${this.apiUrl}/student/${studentId}`, {\r\n      headers: this.getAuthHeaders(),\r\n      params\r\n    }).pipe(\r\n      catchError((error) => {\r\n        console.error('Error fetching student attendance:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Get attendance by class\r\n  getAttendanceByClass(classId: string, date: string, subjectId: string): Observable<any> {\r\n    const params = new HttpParams()\r\n      .set('date', date)\r\n      .set('subjectId', subjectId);\r\n\r\n    return this.http.get<any>(`${this.apiUrl}/class/${classId}`, {\r\n      headers: this.getAuthHeaders(),\r\n      params\r\n    }).pipe(\r\n      catchError((error) => {\r\n        console.error('Error fetching class attendance:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Get all attendance records with filters (for admin)\r\n  getAllAttendanceRecords(filters?: {\r\n    studentId?: string;\r\n    classId?: string;\r\n    teacherId?: string;\r\n    subjectId?: string;\r\n    startDate?: string;\r\n    endDate?: string;\r\n    status?: string;\r\n    page?: number;\r\n    limit?: number;\r\n  }): Observable<any> {\r\n    let params = new HttpParams();\r\n\r\n    if (filters) {\r\n      Object.keys(filters).forEach(key => {\r\n        const value = (filters as any)[key];\r\n        if (value !== undefined && value !== null) {\r\n          params = params.set(key, value.toString());\r\n        }\r\n      });\r\n    }\r\n\r\n    return this.http.get<any>(`${this.apiUrl}/all`, { params });\r\n  }\r\n\r\n  // Get attendance statistics (for admin)\r\n  getAttendanceStatistics(filters?: {\r\n    startDate?: string;\r\n    endDate?: string;\r\n    classId?: string;\r\n    subjectId?: string;\r\n  }): Observable<any> {\r\n    let params = new HttpParams();\r\n\r\n    if (filters) {\r\n      Object.keys(filters).forEach(key => {\r\n        const value = (filters as any)[key];\r\n        if (value !== undefined && value !== null) {\r\n          params = params.set(key, value.toString());\r\n        }\r\n      });\r\n    }\r\n\r\n    return this.http.get<any>(`${this.apiUrl}/statistics`, { params });\r\n  }\r\n\r\n  // Get student attendance statistics\r\n  getStudentAttendanceStats(studentId: string, academicYear?: string): Observable<any> {\r\n    let params = new HttpParams();\r\n    if (academicYear) params = params.set('academicYear', academicYear);\r\n\r\n    return this.http.get<any>(`${this.apiUrl}/attendance/student/${studentId}/stats`, { params });\r\n  }\r\n\r\n  // Get all attendance records for a student\r\n  getStudentAttendance(studentId: string, academicYear?: string): Observable<any> {\r\n    let params = new HttpParams();\r\n    if (academicYear) params = params.set('academicYear', academicYear);\r\n\r\n    return this.http.get<any>(`${this.apiUrl}/student/${studentId}/all`, {\r\n      headers: this.getAuthHeaders(),\r\n      params\r\n    }).pipe(\r\n      catchError((error) => {\r\n        console.error('Error fetching student attendance records:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Get class attendance statistics\r\n  getClassAttendanceStats(classId: string, date?: string): Observable<any> {\r\n    let params = new HttpParams();\r\n    if (date) params = params.set('date', date);\r\n\r\n    return this.http.get<any>(`${this.apiUrl}/attendance/class/${classId}/stats`, { params });\r\n  }\r\n}\r\n"], "mappings": "AACA,SAAqBA,WAAW,EAAEC,UAAU,QAAQ,sBAAsB;AAC1E,SAAqBC,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;AAChD,SAASC,WAAW,QAAQ,8BAA8B;;;AAM1D,OAAM,MAAOC,iBAAiB;EAG5BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAGJ,WAAW,CAACI,MAAM;EAEK;EAEhCC,cAAcA,CAAA;IACpB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,OAAO,IAAIb,WAAW,CAAC;MACrB,eAAe,EAAE,UAAUW,KAAK,EAAE;MAClC,cAAc,EAAE;KACjB,CAAC;EACJ;EAEA;EACAG,mBAAmBA,CAACC,cAAiC;IACnD,OAAO,IAAI,CAACP,IAAI,CAACQ,IAAI,CAAM,GAAG,IAAI,CAACP,MAAM,aAAa,EAAEM,cAAc,EAAE;MACtEE,OAAO,EAAE,IAAI,CAACP,cAAc;KAC7B,CAAC,CAACQ,IAAI,CACLd,GAAG,CAAC;MACFe,IAAI,EAAGC,QAAQ,IAAI;QACjBC,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEF,QAAQ,CAAC;MAChE;KACD,CAAC,EACFjB,UAAU,CAAEoB,KAAK,IAAI;MACnBF,OAAO,CAACE,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,OAAOrB,UAAU,CAAC,MAAMqB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAC,sBAAsBA,CAACC,SAAiB,EAAEC,IAAY,EAAEC,SAAiB;IACvE,MAAMC,MAAM,GAAG,IAAI3B,UAAU,EAAE,CAC5B4B,GAAG,CAAC,MAAM,EAAEH,IAAI,CAAC,CACjBG,GAAG,CAAC,WAAW,EAAEF,SAAS,CAAC;IAE9B,OAAO,IAAI,CAACnB,IAAI,CAACsB,GAAG,CAAM,GAAG,IAAI,CAACrB,MAAM,YAAYgB,SAAS,EAAE,EAAE;MAC/DR,OAAO,EAAE,IAAI,CAACP,cAAc,EAAE;MAC9BkB;KACD,CAAC,CAACV,IAAI,CACLf,UAAU,CAAEoB,KAAK,IAAI;MACnBF,OAAO,CAACE,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,OAAOrB,UAAU,CAAC,MAAMqB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAQ,oBAAoBA,CAACC,OAAe,EAAEN,IAAY,EAAEC,SAAiB;IACnE,MAAMC,MAAM,GAAG,IAAI3B,UAAU,EAAE,CAC5B4B,GAAG,CAAC,MAAM,EAAEH,IAAI,CAAC,CACjBG,GAAG,CAAC,WAAW,EAAEF,SAAS,CAAC;IAE9B,OAAO,IAAI,CAACnB,IAAI,CAACsB,GAAG,CAAM,GAAG,IAAI,CAACrB,MAAM,UAAUuB,OAAO,EAAE,EAAE;MAC3Df,OAAO,EAAE,IAAI,CAACP,cAAc,EAAE;MAC9BkB;KACD,CAAC,CAACV,IAAI,CACLf,UAAU,CAAEoB,KAAK,IAAI;MACnBF,OAAO,CAACE,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,OAAOrB,UAAU,CAAC,MAAMqB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAU,uBAAuBA,CAACC,OAUvB;IACC,IAAIN,MAAM,GAAG,IAAI3B,UAAU,EAAE;IAE7B,IAAIiC,OAAO,EAAE;MACXC,MAAM,CAACC,IAAI,CAACF,OAAO,CAAC,CAACG,OAAO,CAACC,GAAG,IAAG;QACjC,MAAMC,KAAK,GAAIL,OAAe,CAACI,GAAG,CAAC;QACnC,IAAIC,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,EAAE;UACzCX,MAAM,GAAGA,MAAM,CAACC,GAAG,CAACS,GAAG,EAAEC,KAAK,CAACE,QAAQ,EAAE,CAAC;;MAE9C,CAAC,CAAC;;IAGJ,OAAO,IAAI,CAACjC,IAAI,CAACsB,GAAG,CAAM,GAAG,IAAI,CAACrB,MAAM,MAAM,EAAE;MAAEmB;IAAM,CAAE,CAAC;EAC7D;EAEA;EACAc,uBAAuBA,CAACR,OAKvB;IACC,IAAIN,MAAM,GAAG,IAAI3B,UAAU,EAAE;IAE7B,IAAIiC,OAAO,EAAE;MACXC,MAAM,CAACC,IAAI,CAACF,OAAO,CAAC,CAACG,OAAO,CAACC,GAAG,IAAG;QACjC,MAAMC,KAAK,GAAIL,OAAe,CAACI,GAAG,CAAC;QACnC,IAAIC,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,EAAE;UACzCX,MAAM,GAAGA,MAAM,CAACC,GAAG,CAACS,GAAG,EAAEC,KAAK,CAACE,QAAQ,EAAE,CAAC;;MAE9C,CAAC,CAAC;;IAGJ,OAAO,IAAI,CAACjC,IAAI,CAACsB,GAAG,CAAM,GAAG,IAAI,CAACrB,MAAM,aAAa,EAAE;MAAEmB;IAAM,CAAE,CAAC;EACpE;EAEA;EACAe,yBAAyBA,CAAClB,SAAiB,EAAEmB,YAAqB;IAChE,IAAIhB,MAAM,GAAG,IAAI3B,UAAU,EAAE;IAC7B,IAAI2C,YAAY,EAAEhB,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,cAAc,EAAEe,YAAY,CAAC;IAEnE,OAAO,IAAI,CAACpC,IAAI,CAACsB,GAAG,CAAM,GAAG,IAAI,CAACrB,MAAM,uBAAuBgB,SAAS,QAAQ,EAAE;MAAEG;IAAM,CAAE,CAAC;EAC/F;EAEA;EACAiB,oBAAoBA,CAACpB,SAAiB,EAAEmB,YAAqB;IAC3D,IAAIhB,MAAM,GAAG,IAAI3B,UAAU,EAAE;IAC7B,IAAI2C,YAAY,EAAEhB,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,cAAc,EAAEe,YAAY,CAAC;IAEnE,OAAO,IAAI,CAACpC,IAAI,CAACsB,GAAG,CAAM,GAAG,IAAI,CAACrB,MAAM,YAAYgB,SAAS,MAAM,EAAE;MACnER,OAAO,EAAE,IAAI,CAACP,cAAc,EAAE;MAC9BkB;KACD,CAAC,CAACV,IAAI,CACLf,UAAU,CAAEoB,KAAK,IAAI;MACnBF,OAAO,CAACE,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MAClE,OAAOrB,UAAU,CAAC,MAAMqB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAuB,uBAAuBA,CAACd,OAAe,EAAEN,IAAa;IACpD,IAAIE,MAAM,GAAG,IAAI3B,UAAU,EAAE;IAC7B,IAAIyB,IAAI,EAAEE,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAEH,IAAI,CAAC;IAE3C,OAAO,IAAI,CAAClB,IAAI,CAACsB,GAAG,CAAM,GAAG,IAAI,CAACrB,MAAM,qBAAqBuB,OAAO,QAAQ,EAAE;MAAEJ;IAAM,CAAE,CAAC;EAC3F;EAAC,QAAAmB,CAAA,G;qBA7IUzC,iBAAiB,EAAA0C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAjB9C,iBAAiB;IAAA+C,OAAA,EAAjB/C,iBAAiB,CAAAgD,IAAA;IAAAC,UAAA,EAFhB;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}