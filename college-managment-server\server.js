const express = require("express");
require('dotenv').config();
const PORT = process.env.PORT || 5003;
const connectDb = require("./src/config/db.js");
const cors = require("cors");

// Import routes
const departmentRoutes = require('./src/routes/department-Route.js');
const checklistRoutes = require('./src/routes/questionaire-route.js');
const classesRoutes = require('./src/routes/classes-Routes.js');
const subjectRoutes = require('./src/routes/subject-Routes.js');
const programRoutes = require('./src/routes/program-routes.js');
const timetableRoutes = require('./src/routes/timetable-routes.js');
const attendanceRoutes = require('./src/routes/attendance-Routes.js');
const dashboardRoutes = require('./src/routes/dashboard-routes.js');
const authRoutes = require('./src/routes/authRoutes.js');
const complaintRoutes = require('./src/routes/complaint-routes.js');
const noticeRoutes = require('./src/routes/notice-routes.js');
const bulkUploadRoutes = require('./src/routes/bulk-upload-routes.js');

// Import initialization script
const initializeData = require('./src/scripts/initializeData.js');

// Create Express app
const app = express();

// Middleware
app.use(express.json());
app.use(cors());

// Routes
app.use("/api/v1", departmentRoutes, authRoutes, subjectRoutes, checklistRoutes, classesRoutes, programRoutes, timetableRoutes, attendanceRoutes, dashboardRoutes, complaintRoutes, noticeRoutes, bulkUploadRoutes);

// Root route
app.get("/", (req, res) => {
    res.json({ 
        message: "College Management System API",
        version: "1.0.0",
        endpoints: {
            auth: "/api/v1/login, /api/v1/signup",
            programs: "/api/v1/programs",
            departments: "/api/v1/departments",
            timetable: "/api/v1/timetables",
            dashboard: "/api/v1/dashboard"
        }
    });
});

// Database configuration
if (process.env.NODE_ENV !== 'production') {
    console.log('Connecting to database...');
}
connectDb().then(() => {
    if (process.env.NODE_ENV !== 'production') {
        console.log('Database connected successfully');
    }
    // Initialize default data after database connection
    initializeData();
}).catch((error) => {
    console.error('Database connection failed:', error);
});

// Start server
app.listen(PORT, () => {
    console.log(`SERVER IS RUNNING ON PORT ${PORT}`);
    console.log(`API Base URL: http://localhost:${PORT}/api/v1`);
    console.log(`Frontend should connect to: http://localhost:${PORT}`);
});
