.timetable-create-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.form-row mat-form-field {
  flex: 1;
  min-width: 250px;
}

.full-width {
  width: 100%;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
}

.form-actions button {
  min-width: 120px;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-overlay p {
  margin-top: 16px;
  font-size: 16px;
  color: #666;
}

/* Responsive design */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
  }
  
  .form-row mat-form-field {
    min-width: unset;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .form-actions button {
    width: 100%;
  }
}

/* Error styling */
mat-error {
  font-size: 12px;
}

/* Card styling */
mat-card {
  margin-bottom: 20px;
}

mat-card-header {
  margin-bottom: 20px;
}

mat-card-title {
  font-size: 24px;
  font-weight: 500;
  color: #333;
}

/* Form field styling */
mat-form-field {
  margin-bottom: 8px;
}

/* Button styling */
button[mat-raised-button] {
  height: 40px;
  font-weight: 500;
}

button[mat-raised-button][color="primary"] {
  background-color: #1976d2;
  color: white;
}

button[mat-raised-button]:not([color]) {
  background-color: #f5f5f5;
  color: #333;
}

/* Spinner in button */
button mat-spinner {
  margin-right: 8px;
}

/* Select dropdown styling */
mat-select {
  font-size: 14px;
}

/* Input styling */
input[matInput] {
  font-size: 14px;
}

textarea[matInput] {
  font-size: 14px;
  resize: vertical;
  min-height: 80px;
}

/* Disabled state */
button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Focus states */
mat-form-field.mat-focused .mat-form-field-label {
  color: #1976d2;
}

mat-form-field.mat-focused .mat-form-field-outline-thick {
  color: #1976d2;
}
