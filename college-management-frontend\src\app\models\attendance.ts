import { User, Class, Subject } from './user';

export interface Attendance {
  _id: string;
  student: User;
  class: Class;
  teacher: User;
  subject: Subject;
  date: Date;
  status: 'present' | 'absent' | 'late';
  createdAt: Date;
  updatedAt: Date;
}

export interface AttendanceRecord {
  studentId: string;
  teacherId: string;
  subjectId: string;
  status: 'present' | 'absent' | 'late';
}

export interface AttendanceRequest {
  classId: string;
  date: string;
  attendanceRecords: AttendanceRecord[];
  teacherId: string;
  subjectId: string;
}

export interface AttendanceStats {
  _id: string;
  count: number;
}

export interface SubjectAttendance {
  _id: string;
  subjectName: string;
  present: number;
  absent: number;
  total: number;
  percentage: number;
}
