{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/services/user.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/forms\";\nfunction StudentbyclassComponent_tr_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const student_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(student_r1.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(student_r1 == null ? null : student_r1.rollNo);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(student_r1 == null ? null : student_r1.regNo);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(student_r1 == null ? null : student_r1.classId == null ? null : student_r1.classId.className);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(student_r1 == null ? null : student_r1.classId == null ? null : student_r1.classId.section);\n  }\n}\nexport let StudentbyclassComponent = /*#__PURE__*/(() => {\n  class StudentbyclassComponent {\n    constructor(activatedRoute, userService) {\n      this.activatedRoute = activatedRoute;\n      this.userService = userService;\n      this.students = [];\n      this.searchQuery = '';\n      this.currentPage = 1;\n      this.itemsPerPage = 5;\n      this.id = this.activatedRoute.snapshot.paramMap.get('id');\n      this.fetchStudents();\n    }\n    fetchStudents() {\n      this.userService.getUsersByRole('Student').subscribe({\n        next: response => {\n          console.log(\"response\", response);\n          this.students = response.users;\n          const filterstudents = this.students.filter(i => i.classId?._id == this.id);\n          console.log(\"filterstudent\", filterstudents);\n          if (filterstudents) {\n            this.students = filterstudents;\n            this.students.map(i => {\n              this.classname = i.classId.className;\n            });\n          }\n        },\n        error: error => {\n          console.error('Error fetching students:', error);\n        }\n      });\n    }\n    // Filter students based on search query, selected class, department, and section\n    get filteredStudents() {\n      return this.students.filter(student => {\n        const searchLower = this.searchQuery.toLowerCase();\n        const matchesName = student.name?.toLowerCase().includes(searchLower) ?? false;\n        const matchesRollNumber = student.rollNo?.toLowerCase().includes(searchLower) ?? false;\n        const matchesDepartment = student.regNo?.toLowerCase().includes(searchLower) ?? false;\n        const matchesClass = student.class?.toLowerCase().includes(searchLower) ?? false;\n        const matchesSection = student.section?.toLowerCase().includes(searchLower) ?? false;\n        return matchesName || matchesRollNumber || matchesDepartment || matchesClass || matchesSection;\n      });\n    }\n    get paginatedStudents() {\n      const startIndex = (this.currentPage - 1) * this.itemsPerPage;\n      const endIndex = startIndex + this.itemsPerPage;\n      return this.filteredStudents.slice(startIndex, endIndex);\n    }\n    get totalPages() {\n      return Math.ceil(this.filteredStudents.length / this.itemsPerPage);\n    }\n    nextPage() {\n      if (this.currentPage < this.totalPages) {\n        this.currentPage++;\n      }\n    }\n    previousPage() {\n      if (this.currentPage > 1) {\n        this.currentPage--;\n      }\n    }\n    static #_ = this.ɵfac = function StudentbyclassComponent_Factory(t) {\n      return new (t || StudentbyclassComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.UserService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StudentbyclassComponent,\n      selectors: [[\"app-studentbyclass\"]],\n      decls: 38,\n      vars: 7,\n      consts: [[1, \"container\"], [1, \"d-flex\", \"justify-content-between\"], [\"mat-raised-button\", \"\", \"routerLink\", \"/dashboard/admin/students/add-student\"], [1, \"col-lg-6\", \"col-md-12\", \"mb-3\"], [\"for\", \"search\", 1, \"mb-2\"], [\"type\", \"text\", \"placeholder\", \"Search by roll number, name, department, class ...\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"table-container\", \"mb-4\"], [1, \"table-responsive\"], [1, \"tablehead\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"p-3\", \"align-items-center\", \"justify-content-between\"], [1, \"btn\", \"btn-top\", \"border\", 3, \"disabled\", \"click\"], [1, \"page\"]],\n      template: function StudentbyclassComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"button\", 2)(5, \"mat-icon\");\n          i0.ɵɵtext(6, \"add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(7, \" Add New Student \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 3)(9, \"label\", 4);\n          i0.ɵɵtext(10, \"Search\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"input\", 5);\n          i0.ɵɵlistener(\"ngModelChange\", function StudentbyclassComponent_Template_input_ngModelChange_11_listener($event) {\n            return ctx.searchQuery = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 6)(13, \"div\", 7)(14, \"table\")(15, \"thead\")(16, \"tr\", 8)(17, \"th\");\n          i0.ɵɵtext(18, \"Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"th\");\n          i0.ɵɵtext(20, \"Roll Number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"th\");\n          i0.ɵɵtext(22, \"Reg-NO\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"th\");\n          i0.ɵɵtext(24, \"Class\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"th\");\n          i0.ɵɵtext(26, \"Section\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(27, \"tbody\");\n          i0.ɵɵtemplate(28, StudentbyclassComponent_tr_28_Template, 11, 5, \"tr\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(29, \"div\", 10)(30, \"div\")(31, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function StudentbyclassComponent_Template_button_click_31_listener() {\n            return ctx.previousPage();\n          });\n          i0.ɵɵtext(32, \"Previous\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"div\", 12);\n          i0.ɵɵtext(34);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"div\")(36, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function StudentbyclassComponent_Template_button_click_36_listener() {\n            return ctx.nextPage();\n          });\n          i0.ɵɵtext(37, \"Next\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\"Student List of \", ctx.classname, \"\");\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"ngForOf\", ctx.paginatedStudents);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.currentPage === 1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate2(\"Page \", ctx.currentPage, \" of \", ctx.totalPages, \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.currentPage === ctx.totalPages);\n        }\n      },\n      dependencies: [i3.NgForOf, i1.RouterLink, i4.MatButton, i5.MatIcon, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgModel]\n    });\n  }\n  return StudentbyclassComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}