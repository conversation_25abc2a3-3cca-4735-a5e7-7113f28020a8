{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { DepartmentTableComponent } from './department-table/department-table.component';\nimport { DepartmentFormComponent } from './department-form/department-form.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: DepartmentTableComponent\n}, {\n  path: 'add-department',\n  component: DepartmentFormComponent\n}, {\n  path: 'add-department/:id',\n  component: DepartmentFormComponent\n}];\nexport let DepartmentRoutingModule = /*#__PURE__*/(() => {\n  class DepartmentRoutingModule {\n    static #_ = this.ɵfac = function DepartmentRoutingModule_Factory(t) {\n      return new (t || DepartmentRoutingModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: DepartmentRoutingModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n  return DepartmentRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}