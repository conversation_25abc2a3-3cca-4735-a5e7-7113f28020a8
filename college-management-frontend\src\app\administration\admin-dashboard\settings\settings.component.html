<div class="settings-container">
  <!-- Header -->
  <div class="page-header">
    <div class="header-content">
      <h1>
        <mat-icon>settings</mat-icon>
        Settings
      </h1>
      <p class="subtitle">Manage your profile and system settings</p>
    </div>
  </div>

  <!-- Settings Tabs -->
  <mat-card class="settings-card">
    <mat-tab-group [(selectedIndex)]="selectedTab" (selectedTabChange)="onTabChange($event)">
      
      <!-- Profile Settings Tab -->
      <mat-tab label="Profile">
        <div class="tab-content">
          <div class="section-header">
            <h2>Profile Information</h2>
            <p>Update your personal information and contact details</p>
          </div>

          <form [formGroup]="profileForm" (ngSubmit)="updateProfile()">
            <div class="form-grid">
              <mat-form-field appearance="outline">
                <mat-label>Full Name</mat-label>
                <input matInput formControlName="name" placeholder="Enter your full name">
                <mat-error *ngIf="name?.invalid && name?.touched">
                  <span *ngIf="name?.errors?.['required']">Name is required</span>
                  <span *ngIf="name?.errors?.['minlength']">Name must be at least 2 characters</span>
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Email Address</mat-label>
                <input matInput type="email" formControlName="email" placeholder="Enter your email">
                <mat-error *ngIf="email?.invalid && email?.touched">
                  <span *ngIf="email?.errors?.['required']">Email is required</span>
                  <span *ngIf="email?.errors?.['email']">Please enter a valid email</span>
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Contact Number</mat-label>
                <input matInput formControlName="contact" placeholder="Enter your contact number">
                <mat-error *ngIf="contact?.invalid && contact?.touched">
                  <span *ngIf="contact?.errors?.['required']">Contact number is required</span>
                  <span *ngIf="contact?.errors?.['pattern']">Please enter a valid contact number</span>
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Address</mat-label>
                <textarea matInput formControlName="address" rows="3" placeholder="Enter your address"></textarea>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Qualification</mat-label>
                <input matInput formControlName="qualification" placeholder="Enter your qualification">
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Experience</mat-label>
                <input matInput formControlName="experience" placeholder="Enter your experience">
              </mat-form-field>
            </div>

            <div class="form-actions">
              <button mat-raised-button color="primary" type="submit" [disabled]="loading || profileForm.invalid">
                <mat-icon>save</mat-icon>
                Update Profile
                <mat-spinner *ngIf="loading" diameter="20" class="spinner"></mat-spinner>
              </button>
            </div>
          </form>
        </div>
      </mat-tab>

      <!-- Password Settings Tab -->
      <mat-tab label="Password">
        <div class="tab-content">
          <div class="section-header">
            <h2>Change Password</h2>
            <p>Update your account password for security</p>
          </div>

          <form [formGroup]="passwordForm" (ngSubmit)="changePassword()">
            <div class="form-grid password-grid">
              <mat-form-field appearance="outline">
                <mat-label>Current Password</mat-label>
                <input matInput type="password" formControlName="currentPassword" placeholder="Enter current password">
                <mat-error *ngIf="currentPassword?.invalid && currentPassword?.touched">
                  Current password is required
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>New Password</mat-label>
                <input matInput type="password" formControlName="newPassword" placeholder="Enter new password">
                <mat-error *ngIf="newPassword?.invalid && newPassword?.touched">
                  <span *ngIf="newPassword?.errors?.['required']">New password is required</span>
                  <span *ngIf="newPassword?.errors?.['minlength']">Password must be at least 6 characters</span>
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Confirm New Password</mat-label>
                <input matInput type="password" formControlName="confirmPassword" placeholder="Confirm new password">
                <mat-error *ngIf="confirmPassword?.invalid && confirmPassword?.touched">
                  <span *ngIf="confirmPassword?.errors?.['required']">Please confirm your password</span>
                  <span *ngIf="confirmPassword?.errors?.['passwordMismatch']">Passwords do not match</span>
                </mat-error>
              </mat-form-field>
            </div>

            <div class="form-actions">
              <button mat-raised-button color="primary" type="submit" [disabled]="loading || passwordForm.invalid">
                <mat-icon>lock</mat-icon>
                Change Password
                <mat-spinner *ngIf="loading" diameter="20" class="spinner"></mat-spinner>
              </button>
            </div>
          </form>
        </div>
      </mat-tab>

      <!-- System Settings Tab -->
      <mat-tab label="System">
        <div class="tab-content">
          <div class="section-header">
            <h2>System Configuration</h2>
            <p>Configure system-wide settings and preferences</p>
          </div>

          <form [formGroup]="systemForm" (ngSubmit)="updateSystemSettings()">
            <!-- Institute Information -->
            <div class="settings-section">
              <h3>Institute Information</h3>
              <div class="form-grid">
                <mat-form-field appearance="outline">
                  <mat-label>Institute Name</mat-label>
                  <input matInput formControlName="instituteName">
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Institute Address</mat-label>
                  <textarea matInput formControlName="instituteAddress" rows="2"></textarea>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Contact Email</mat-label>
                  <input matInput type="email" formControlName="contactEmail">
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Contact Phone</mat-label>
                  <input matInput formControlName="contactPhone">
                </mat-form-field>
              </div>
            </div>

            <!-- Academic Settings -->
            <div class="settings-section">
              <h3>Academic Settings</h3>
              <div class="form-grid">
                <mat-form-field appearance="outline">
                  <mat-label>Academic Year</mat-label>
                  <input matInput formControlName="academicYear">
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Current Semester</mat-label>
                  <input matInput formControlName="currentSemester">
                </mat-form-field>
              </div>
            </div>

            <!-- Registration Settings -->
            <div class="settings-section">
              <h3>Registration Settings</h3>
              <div class="toggle-grid">
                <mat-slide-toggle formControlName="allowStudentRegistration" color="primary">
                  Allow Student Registration
                </mat-slide-toggle>
                <mat-slide-toggle formControlName="allowTeacherRegistration" color="primary">
                  Allow Teacher Registration
                </mat-slide-toggle>
              </div>
            </div>

            <!-- Notification Settings -->
            <div class="settings-section">
              <h3>Notification Settings</h3>
              <div class="toggle-grid">
                <mat-slide-toggle formControlName="enableNotifications" color="primary">
                  Enable Notifications
                </mat-slide-toggle>
                <mat-slide-toggle formControlName="enableEmailNotifications" color="primary">
                  Enable Email Notifications
                </mat-slide-toggle>
                <mat-slide-toggle formControlName="enableSMSNotifications" color="primary">
                  Enable SMS Notifications
                </mat-slide-toggle>
              </div>
            </div>

            <!-- System Maintenance -->
            <div class="settings-section">
              <h3>System Maintenance</h3>
              <div class="toggle-grid">
                <mat-slide-toggle formControlName="maintenanceMode" color="warn">
                  Maintenance Mode
                </mat-slide-toggle>
              </div>
            </div>

            <div class="form-actions">
              <button mat-stroked-button type="button" (click)="resetSystemSettings()">
                <mat-icon>refresh</mat-icon>
                Reset
              </button>
              <button mat-raised-button color="primary" type="submit" [disabled]="loading || systemForm.invalid">
                <mat-icon>save</mat-icon>
                Save Settings
                <mat-spinner *ngIf="loading" diameter="20" class="spinner"></mat-spinner>
              </button>
            </div>
          </form>
        </div>
      </mat-tab>

      <!-- Data Management Tab -->
      <mat-tab label="Data">
        <div class="tab-content">
          <div class="section-header">
            <h2>Data Management</h2>
            <p>Export, import, and backup your data</p>
          </div>

          <div class="data-actions">
            <mat-card class="action-card">
              <mat-card-content>
                <div class="action-content">
                  <mat-icon class="action-icon">download</mat-icon>
                  <div class="action-info">
                    <h3>Export Data</h3>
                    <p>Download all system data as backup files</p>
                  </div>
                  <button mat-raised-button color="primary" (click)="exportData()">
                    <mat-icon>download</mat-icon>
                    Export
                  </button>
                </div>
              </mat-card-content>
            </mat-card>

            <mat-card class="action-card">
              <mat-card-content>
                <div class="action-content">
                  <mat-icon class="action-icon">upload</mat-icon>
                  <div class="action-info">
                    <h3>Import Data</h3>
                    <p>Import data from backup files</p>
                  </div>
                  <button mat-raised-button color="accent" (click)="importData()">
                    <mat-icon>upload</mat-icon>
                    Import
                  </button>
                </div>
              </mat-card-content>
            </mat-card>

            <mat-card class="action-card">
              <mat-card-content>
                <div class="action-content">
                  <mat-icon class="action-icon">backup</mat-icon>
                  <div class="action-info">
                    <h3>Database Backup</h3>
                    <p>Create a complete database backup</p>
                  </div>
                  <button mat-raised-button color="warn" (click)="backupDatabase()">
                    <mat-icon>backup</mat-icon>
                    Backup
                  </button>
                </div>
              </mat-card-content>
            </mat-card>
          </div>
        </div>
      </mat-tab>

    </mat-tab-group>
  </mat-card>
</div>
