{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { SubjectRoutingModule } from './subject-routing.module';\nimport { MaterialModule } from 'src/app/material';\nimport * as i0 from \"@angular/core\";\nexport let SubjectModule = /*#__PURE__*/(() => {\n  class SubjectModule {\n    static #_ = this.ɵfac = function SubjectModule_Factory(t) {\n      return new (t || SubjectModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SubjectModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, SubjectRoutingModule, MaterialModule]\n    });\n  }\n  return SubjectModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}