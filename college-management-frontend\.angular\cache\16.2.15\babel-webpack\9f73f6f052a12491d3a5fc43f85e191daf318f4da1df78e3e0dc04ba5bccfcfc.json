{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let TeacherViewComponent = /*#__PURE__*/(() => {\n  class TeacherViewComponent {\n    static #_ = this.ɵfac = function TeacherViewComponent_Factory(t) {\n      return new (t || TeacherViewComponent)();\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TeacherViewComponent,\n      selectors: [[\"app-teacher-view\"]],\n      decls: 24,\n      vars: 0,\n      consts: [[1, \"container\", \"mt-5\"], [1, \"row\", \"justify-content-center\"], [1, \"col-lg-6\", \"col-md-8\"], [1, \"card\", \"shadow-sm\"], [1, \"card-header\", \"text-center\"], [1, \"mb-0\"], [1, \"card-body\"], [1, \"d-flex\", \"flex-column\", \"align-items-center\"], [\"src\", \"../../../../../assets/image-w856.webp\", \"alt\", \"Teacher Photo\", 1, \"rounded-circle\", \"mb-3\", 2, \"width\", \"150px\", \"height\", \"150px\", \"object-fit\", \"cover\"], [1, \"card-title\"], [1, \"card-text\", \"text-muted\", \"mb-1\"], [1, \"card-text\", \"text-muted\", \"mb-3\"], [1, \"card-text\"]],\n      template: function TeacherViewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"h2\", 5);\n          i0.ɵɵtext(6, \"Teacher Details\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7);\n          i0.ɵɵelement(9, \"img\", 8);\n          i0.ɵɵelementStart(10, \"h4\", 9);\n          i0.ɵɵtext(11, \"Ihtizaz Ahmad\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"p\", 10);\n          i0.ɵɵtext(13, \"Email: <EMAIL>\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"p\", 11);\n          i0.ɵɵtext(15, \"Password: *******\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"p\", 12)(17, \"strong\");\n          i0.ɵɵtext(18, \"Class:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(19, \" 1st\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"p\", 12)(21, \"strong\");\n          i0.ɵɵtext(22, \"Subject:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(23, \" Maths\");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n      }\n    });\n  }\n  return TeacherViewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}