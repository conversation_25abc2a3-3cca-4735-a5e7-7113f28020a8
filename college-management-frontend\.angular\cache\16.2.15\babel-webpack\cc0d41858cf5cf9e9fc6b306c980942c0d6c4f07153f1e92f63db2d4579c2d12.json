{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../auth.service\";\nexport class ForgotPasswordComponent {\n  constructor(fb, router, authService) {\n    this.fb = fb;\n    this.router = router;\n    this.authService = authService;\n    this.loading = false;\n  }\n  ngOnInit() {\n    this.forgotPasswordForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]]\n    });\n  }\n  onSubmit() {\n    if (this.forgotPasswordForm.valid) {\n      this.loading = true;\n      const email = this.forgotPasswordForm.value.email;\n      this.authService.forgotPassword(email).subscribe({\n        next: res => {\n          this.loading = false;\n          console.log('Forgot password response:', res);\n          if (res.success) {\n            localStorage.setItem('resetEmail', email);\n            if (res.user && res.user._id) {\n              localStorage.setItem('resetUserId', res.user._id);\n            }\n            Swal.fire({\n              icon: 'success',\n              title: 'OTP Sent',\n              text: res.message || 'OTP has been sent to your email address.',\n              confirmButtonColor: '#29578c'\n            }).then(() => {\n              this.router.navigate(['/auth/reset-password']);\n            });\n          } else {\n            Swal.fire({\n              icon: 'error',\n              title: 'Error',\n              text: res.message || 'Failed to send reset email'\n            });\n          }\n        },\n        error: error => {\n          this.loading = false;\n          console.error('Forgot password error:', error);\n          Swal.fire({\n            icon: 'error',\n            title: 'Error',\n            text: error.error?.message || 'Failed to send reset email'\n          });\n        }\n      });\n    }\n  }\n  static #_ = this.ɵfac = function ForgotPasswordComponent_Factory(t) {\n    return new (t || ForgotPasswordComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.AuthService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ForgotPasswordComponent,\n    selectors: [[\"app-forgot-password\"]],\n    decls: 38,\n    vars: 0,\n    consts: [[1, \"container-fluid\"], [1, \"row\", \"vh-100\"], [1, \"col-lg-5\", \"d-flex\", \"flex-column\", \"justify-content-center\", \"align-items-center\", \"p-3\", \"bg-white\"], [1, \"w-100\", 2, \"max-width\", \"400px\"], [1, \"text-center\"], [1, \"mb-4\"], [\"src\", \"../../../assets/images/logo.jpeg\"], [1, \"d-flex\", \"justify-content-center\", \"mt-5\", \"mb-2\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"fa-key\", \"key\"], [1, \"mb-3\"], [\"for\", \"email\", 1, \"form-label\"], [\"type\", \"email\", \"id\", \"email\", \"placeholder\", \"Enter your email\", 1, \"form-control\"], [1, \"d-grid\", \"gap-2\"], [\"type\", \"submit\", \"routerLink\", \"/auth/reset-password\", 1, \"btn\", \"submit\"], [1, \"mt-3\", \"text-center\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"fa-arrow-left\"], [\"routerLink\", \"/auth\"], [1, \"col-lg-7\", \"d-none\", \"d-lg-flex\", \"flex-column\", \"align-items-start\", \"bg-light\", \"position-relative\"], [1, \"text-start\", \"p-5\", \"w-100\"], [1, \"blockquote\"], [1, \"blockquote-footer\"], [\"title\", \"Source Title\"], [1, \"image-container\"], [\"src\", \"../../../assets/images/background.jpeg\", \"alt\", \"Stats graph\", 1, \"img-fluid\", \"position-absolute\"]],\n    template: function ForgotPasswordComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"h2\", 5);\n        i0.ɵɵelement(6, \"img\", 6);\n        i0.ɵɵtext(7, \" GPGC (Swabi)\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"div\", 7);\n        i0.ɵɵelement(9, \"i\", 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"h1\", 5);\n        i0.ɵɵtext(11, \"Forgot Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"p\");\n        i0.ɵɵtext(13, \"No Worries , we will send you reset instructions.\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(14, \"form\")(15, \"div\", 9)(16, \"label\", 10);\n        i0.ɵɵtext(17, \"Email\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(18, \"input\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(19, \"div\", 12)(20, \"button\", 13);\n        i0.ɵɵtext(21, \"Reset Password\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(22, \"p\", 14);\n        i0.ɵɵelement(23, \"i\", 15);\n        i0.ɵɵtext(24, \" \\u00A0\");\n        i0.ɵɵelementStart(25, \"a\", 16);\n        i0.ɵɵtext(26, \"Back to log in \");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(27, \"div\", 17)(28, \"div\", 18)(29, \"blockquote\", 19)(30, \"h2\", 5);\n        i0.ɵɵtext(31, \"College management system Login page\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(32, \"footer\", 20);\n        i0.ɵɵtext(33, \"Name\");\n        i0.ɵɵelementStart(34, \"cite\", 21);\n        i0.ɵɵtext(35, \"Owner ~ GPGC SWABI\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(36, \"div\", 22);\n        i0.ɵɵelement(37, \"img\", 23);\n        i0.ɵɵelementEnd()()()();\n      }\n    },\n    dependencies: [i2.RouterLink, i1.ɵNgNoValidate, i1.NgControlStatusGroup, i1.NgForm],\n    styles: [\"body[_ngcontent-%COMP%], html[_ngcontent-%COMP%] {\\n    margin: 0;\\n    padding: 0;\\n    overflow-x: hidden; \\n\\n    height: 100%;\\n}\\n\\n\\n\\n.image-container[_ngcontent-%COMP%] {\\n    width: 100%;\\n    height: 100%;\\n    position: relative;\\n}\\n\\n.image-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    max-width: 80%;\\n    height: auto;\\n    position: absolute;\\n    right: 0;\\n    bottom: 0;\\n    object-fit: contain; \\n\\n}\\n.submit[_ngcontent-%COMP%]{\\n    background-color: #29578c;\\n    color: white;\\n}\\n.forgot[_ngcontent-%COMP%]{\\n    color: #29578c;\\n    text-decoration: none;\\n    font-weight: 700;\\n}\\na[_ngcontent-%COMP%]{\\n    cursor: pointer;\\n    text-decoration: none;\\n    color: black;\\n    font-weight: 600;\\n}\\n.key[_ngcontent-%COMP%]{\\n    background-color: #b8d1ef !important;\\n    border-radius: 50%;\\n    height: 50px;\\n    width: 50px;\\n    display: flex;\\n    justify-content: center;\\n    align-items: center;\\n    color: #29578c;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "<PERSON><PERSON>", "ForgotPasswordComponent", "constructor", "fb", "router", "authService", "loading", "ngOnInit", "forgotPasswordForm", "group", "email", "required", "onSubmit", "valid", "value", "forgotPassword", "subscribe", "next", "res", "console", "log", "success", "localStorage", "setItem", "user", "_id", "fire", "icon", "title", "text", "message", "confirmButtonColor", "then", "navigate", "error", "_", "i0", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "Router", "i3", "AuthService", "_2", "selectors", "decls", "vars", "consts", "template", "ForgotPasswordComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\auth\\forgot-password\\forgot-password.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\auth\\forgot-password\\forgot-password.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { AuthService } from '../auth.service';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-forgot-password',\r\n  templateUrl: './forgot-password.component.html',\r\n  styleUrls: ['./forgot-password.component.css']\r\n})\r\nexport class ForgotPasswordComponent implements OnInit {\r\n  forgotPasswordForm!: FormGroup;\r\n  loading = false;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private router: Router,\r\n    private authService: AuthService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.forgotPasswordForm = this.fb.group({\r\n      email: ['', [Validators.required, Validators.email]]\r\n    });\r\n  }\r\n\r\n  onSubmit() {\r\n    if (this.forgotPasswordForm.valid) {\r\n      this.loading = true;\r\n      const email = this.forgotPasswordForm.value.email;\r\n\r\n      this.authService.forgotPassword(email).subscribe({\r\n        next: (res) => {\r\n          this.loading = false;\r\n          console.log('Forgot password response:', res);\r\n          \r\n          if (res.success) {\r\n            localStorage.setItem('resetEmail', email);\r\n            if (res.user && res.user._id) {\r\n              localStorage.setItem('resetUserId', res.user._id);\r\n            }\r\n            \r\n            Swal.fire({\r\n              icon: 'success',\r\n              title: 'OTP Sent',\r\n              text: res.message || 'OTP has been sent to your email address.',\r\n              confirmButtonColor: '#29578c'\r\n            }).then(() => {\r\n              this.router.navigate(['/auth/reset-password']);\r\n            });\r\n          } else {\r\n            Swal.fire({\r\n              icon: 'error',\r\n              title: 'Error',\r\n              text: res.message || 'Failed to send reset email'\r\n            });\r\n          }\r\n        },\r\n        error: (error) => {\r\n          this.loading = false;\r\n          console.error('Forgot password error:', error);\r\n          Swal.fire({\r\n            icon: 'error',\r\n            title: 'Error',\r\n            text: error.error?.message || 'Failed to send reset email',\r\n          });\r\n        }\r\n      });\r\n    }\r\n  }\r\n}\r\n", "<div class=\"container-fluid\">\r\n    <div class=\"row vh-100\">\r\n        <div class=\"col-lg-5 d-flex flex-column justify-content-center align-items-center p-3 bg-white\">\r\n            <div class=\"w-100\" style=\"max-width: 400px;\">\r\n                <div class=\"text-center\">\r\n                <h2 class=\"mb-4\"><img src=\"../../../assets/images/logo.jpeg\"/> GPGC (Swabi)</h2>\r\n                <div class=\"d-flex justify-content-center mt-5 mb-2\">\r\n                    <i class=\"fa fa-key key\" aria-hidden=\"true\"></i>\r\n                </div>\r\n                \r\n                <h1 class=\"mb-4\">Forgot Password</h1>\r\n                <p>No Worries , we will send you reset instructions.</p>\r\n            </div>\r\n                <form>\r\n                    <div class=\"mb-3\">\r\n                        <label for=\"email\" class=\"form-label\">Email</label>\r\n                        <input type=\"email\" class=\"form-control\" id=\"email\" placeholder=\"Enter your email\">\r\n                    </div>\r\n                   \r\n                  \r\n                    <div class=\"d-grid gap-2\">\r\n                        <button type=\"submit\" class=\"btn submit\" routerLink=\"/auth/reset-password\">Reset Password</button>\r\n                    </div>\r\n                    <p class=\"mt-3 text-center\"> <i class=\"fa fa-arrow-left\" aria-hidden=\"true\"></i> &nbsp;<a routerLink=\"/auth\">Back to log in </a></p>\r\n                </form>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-lg-7 d-none d-lg-flex flex-column align-items-start bg-light position-relative\">\r\n            <div class=\"text-start p-5 w-100\">\r\n                <blockquote class=\"blockquote\">\r\n                    <h2 class=\"mb-4\">College management system Login page</h2>\r\n                    <footer class=\"blockquote-footer\">Name<cite title=\"Source Title\">Owner ~ GPGC SWABI</cite></footer>\r\n                </blockquote>\r\n            </div>\r\n            <div class=\"image-container\">\r\n                <img src=\"../../../assets/images/background.jpeg\" class=\"img-fluid position-absolute\" alt=\"Stats graph\">\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAGnE,OAAOC,IAAI,MAAM,aAAa;;;;;AAO9B,OAAM,MAAOC,uBAAuB;EAIlCC,YACUC,EAAe,EACfC,MAAc,EACdC,WAAwB;IAFxB,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IALrB,KAAAC,OAAO,GAAG,KAAK;EAMZ;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACL,EAAE,CAACM,KAAK,CAAC;MACtCC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACX,UAAU,CAACY,QAAQ,EAAEZ,UAAU,CAACW,KAAK,CAAC;KACpD,CAAC;EACJ;EAEAE,QAAQA,CAAA;IACN,IAAI,IAAI,CAACJ,kBAAkB,CAACK,KAAK,EAAE;MACjC,IAAI,CAACP,OAAO,GAAG,IAAI;MACnB,MAAMI,KAAK,GAAG,IAAI,CAACF,kBAAkB,CAACM,KAAK,CAACJ,KAAK;MAEjD,IAAI,CAACL,WAAW,CAACU,cAAc,CAACL,KAAK,CAAC,CAACM,SAAS,CAAC;QAC/CC,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAACZ,OAAO,GAAG,KAAK;UACpBa,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEF,GAAG,CAAC;UAE7C,IAAIA,GAAG,CAACG,OAAO,EAAE;YACfC,YAAY,CAACC,OAAO,CAAC,YAAY,EAAEb,KAAK,CAAC;YACzC,IAAIQ,GAAG,CAACM,IAAI,IAAIN,GAAG,CAACM,IAAI,CAACC,GAAG,EAAE;cAC5BH,YAAY,CAACC,OAAO,CAAC,aAAa,EAAEL,GAAG,CAACM,IAAI,CAACC,GAAG,CAAC;;YAGnDzB,IAAI,CAAC0B,IAAI,CAAC;cACRC,IAAI,EAAE,SAAS;cACfC,KAAK,EAAE,UAAU;cACjBC,IAAI,EAAEX,GAAG,CAACY,OAAO,IAAI,0CAA0C;cAC/DC,kBAAkB,EAAE;aACrB,CAAC,CAACC,IAAI,CAAC,MAAK;cACX,IAAI,CAAC5B,MAAM,CAAC6B,QAAQ,CAAC,CAAC,sBAAsB,CAAC,CAAC;YAChD,CAAC,CAAC;WACH,MAAM;YACLjC,IAAI,CAAC0B,IAAI,CAAC;cACRC,IAAI,EAAE,OAAO;cACbC,KAAK,EAAE,OAAO;cACdC,IAAI,EAAEX,GAAG,CAACY,OAAO,IAAI;aACtB,CAAC;;QAEN,CAAC;QACDI,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAC5B,OAAO,GAAG,KAAK;UACpBa,OAAO,CAACe,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;UAC9ClC,IAAI,CAAC0B,IAAI,CAAC;YACRC,IAAI,EAAE,OAAO;YACbC,KAAK,EAAE,OAAO;YACdC,IAAI,EAAEK,KAAK,CAACA,KAAK,EAAEJ,OAAO,IAAI;WAC/B,CAAC;QACJ;OACD,CAAC;;EAEN;EAAC,QAAAK,CAAA,G;qBA3DUlC,uBAAuB,EAAAmC,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAvB3C,uBAAuB;IAAA4C,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCXpCf,EAAA,CAAAiB,cAAA,aAA6B;QAKIjB,EAAA,CAAAkB,SAAA,aAA6C;QAAClB,EAAA,CAAAmB,MAAA,oBAAY;QAAAnB,EAAA,CAAAoB,YAAA,EAAK;QAChFpB,EAAA,CAAAiB,cAAA,aAAqD;QACjDjB,EAAA,CAAAkB,SAAA,WAAgD;QACpDlB,EAAA,CAAAoB,YAAA,EAAM;QAENpB,EAAA,CAAAiB,cAAA,aAAiB;QAAAjB,EAAA,CAAAmB,MAAA,uBAAe;QAAAnB,EAAA,CAAAoB,YAAA,EAAK;QACrCpB,EAAA,CAAAiB,cAAA,SAAG;QAAAjB,EAAA,CAAAmB,MAAA,yDAAiD;QAAAnB,EAAA,CAAAoB,YAAA,EAAI;QAExDpB,EAAA,CAAAiB,cAAA,YAAM;QAEwCjB,EAAA,CAAAmB,MAAA,aAAK;QAAAnB,EAAA,CAAAoB,YAAA,EAAQ;QACnDpB,EAAA,CAAAkB,SAAA,iBAAmF;QACvFlB,EAAA,CAAAoB,YAAA,EAAM;QAGNpB,EAAA,CAAAiB,cAAA,eAA0B;QACqDjB,EAAA,CAAAmB,MAAA,sBAAc;QAAAnB,EAAA,CAAAoB,YAAA,EAAS;QAEtGpB,EAAA,CAAAiB,cAAA,aAA4B;QAACjB,EAAA,CAAAkB,SAAA,aAAmD;QAAClB,EAAA,CAAAmB,MAAA,eAAM;QAAAnB,EAAA,CAAAiB,cAAA,aAAsB;QAAAjB,EAAA,CAAAmB,MAAA,uBAAe;QAAAnB,EAAA,CAAAoB,YAAA,EAAI;QAI5IpB,EAAA,CAAAiB,cAAA,eAAgG;QAGnEjB,EAAA,CAAAmB,MAAA,4CAAoC;QAAAnB,EAAA,CAAAoB,YAAA,EAAK;QAC1DpB,EAAA,CAAAiB,cAAA,kBAAkC;QAAAjB,EAAA,CAAAmB,MAAA,YAAI;QAAAnB,EAAA,CAAAiB,cAAA,gBAA2B;QAAAjB,EAAA,CAAAmB,MAAA,0BAAkB;QAAAnB,EAAA,CAAAoB,YAAA,EAAO;QAGlGpB,EAAA,CAAAiB,cAAA,eAA6B;QACzBjB,EAAA,CAAAkB,SAAA,eAAwG;QAC5GlB,EAAA,CAAAoB,YAAA,EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}