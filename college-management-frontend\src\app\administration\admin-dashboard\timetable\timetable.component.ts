import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { TimetableService } from '../../../services/timetable.service';
import { ProgramService } from '../../../services/program.service';
import { DepartmentService } from '../../../services/department.service';
import { ClassesService } from '../../../services/classes.service';
import { SubjectService } from '../../../services/subject.service';
import { UserService } from '../../../services/user.service';
import { Timetable } from '../../../models/timetable';
import { Program, Department, Class, Subject, User } from '../../../models/user';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-timetable',
  templateUrl: './timetable.component.html',
  styleUrls: ['./timetable.component.css']
})
export class TimetableComponent implements OnInit {
  timetableForm: FormGroup;
  timetables: Timetable[] = [];
  programs: Program[] = [];
  departments: Department[] = [];
  filteredDepartments: Department[] = [];
  classes: Class[] = [];
  filteredClasses: Class[] = [];
  subjects: Subject[] = [];
  filteredSubjects: Subject[] = [];
  teachers: User[] = [];
  filteredTeachers: User[] = [];

  loading = false;
  submitting = false;
  editingTimetableId: string | null = null;
  
  daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  baseTimeSlots = [
    '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
    '12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30',
    '16:00', '16:30', '17:00', '17:30', '18:00'
  ];

  availableStartTimes: string[] = [];

  displayedColumns: string[] = ['dayOfWeek', 'scheduleInfo', 'timeSlot', 'program', 'room', 'actions'];

  // Program-specific duration options
  programDurations: { [key: string]: { value: number; label: string }[] } = {
    'Intermediate': [
      { value: 40, label: '40 minutes' },
      { value: 60, label: '1 hour' }
    ],
    'BS': [
      { value: 60, label: '1 hour' },
      { value: 90, label: '1.5 hours' },
      { value: 120, label: '2 hours' }
    ],
    'MS': [
      { value: 60, label: '1 hour' },
      { value: 90, label: '1.5 hours' },
      { value: 120, label: '2 hours' }
    ],
    'PhD': [
      { value: 90, label: '1.5 hours' },
      { value: 120, label: '2 hours' },
      { value: 180, label: '3 hours' }
    ]
  };

  availableDurations: { value: number; label: string }[] = [];
  selectedProgram: Program | null = null;

  constructor(
    private fb: FormBuilder,
    private timetableService: TimetableService,
    private programService: ProgramService,
    private departmentService: DepartmentService,
    private classesService: ClassesService,
    private subjectService: SubjectService,
    private userService: UserService,
    private snackBar: MatSnackBar
  ) {
    this.timetableForm = this.fb.group({
      program: ['', Validators.required],
      department: ['', Validators.required],
      class: ['', Validators.required],
      subject: ['', Validators.required],
      teacher: ['', Validators.required],
      dayOfWeek: ['', Validators.required],
      startTime: ['', Validators.required],
      duration: ['', Validators.required],
      endTime: [{ value: '', disabled: true }], // Auto-calculated
      room: [''],
      semester: ['', Validators.required],
      academicYear: ['2024-2025', Validators.required],
      notes: ['']
    });
  }

  ngOnInit(): void {
    this.loadInitialData();
    this.setupFormSubscriptions();
  }

  isIntermediateProgram(): boolean {
    const programId = this.timetableForm.get('program')?.value;
    const selectedProgram = this.programs.find(p => p._id === programId);
    return selectedProgram?.name === 'Intermediate';
  }

  getSemesterDisplayText(semester: number): string {
    const semesterMap: { [key: number]: string } = {
      1: '1st', 2: '2nd', 3: '3rd', 4: '4th',
      5: '5th', 6: '6th', 7: '7th', 8: '8th'
    };
    return semesterMap[semester] || `${semester}th`;
  }

  loadInitialData(): void {
    this.loading = true;
    
    // Load programs
    this.programService.getAllPrograms(true).subscribe({
      next: (response) => {
        if (response.success) {
          this.programs = response.programs;
        }
      },
      error: (error) => console.error('Error loading programs:', error)
    });

    // Load teachers
    this.userService.getUsersByRole('Teacher').subscribe({
      next: (response) => {
        if (response.success) {
          this.teachers = response.users;
        }
      },
      error: (error) => console.error('Error loading teachers:', error)
    });

    // Load existing timetables
    this.loadTimetables();
  }

  setupFormSubscriptions(): void {
    // When program changes, load departments and update duration options
    this.timetableForm.get('program')?.valueChanges.subscribe(programId => {
      if (programId) {
        this.selectedProgram = this.programs.find(p => p._id === programId) || null;
        this.updateAvailableDurations();
        this.loadDepartmentsByProgram(programId);
        this.loadTeachersByProgram(programId);
        this.timetableForm.patchValue({
          department: '',
          class: '',
          subject: '',
          duration: '',
          endTime: ''
        });
      }
    });

    // When department changes, load classes and subjects
    this.timetableForm.get('department')?.valueChanges.subscribe(departmentId => {
      if (departmentId) {
        this.loadClassesByDepartment(departmentId);
        this.loadSubjectsByDepartment(departmentId);
        this.timetableForm.patchValue({ class: '', subject: '' });
      }
    });

    // When start time or duration changes, calculate end time
    this.timetableForm.get('startTime')?.valueChanges.subscribe(() => {
      this.calculateEndTime();
    });

    this.timetableForm.get('duration')?.valueChanges.subscribe(() => {
      this.calculateEndTime();
    });
  }

  updateAvailableDurations(): void {
    if (this.selectedProgram) {
      this.availableDurations = this.programDurations[this.selectedProgram.name] || [];
      this.generateAvailableStartTimes();
    } else {
      this.availableDurations = [];
      this.availableStartTimes = [];
    }
  }

  generateAvailableStartTimes(): void {
    if (this.selectedProgram) {
      // For different programs, we might want different time slot granularities
      if (this.selectedProgram.name === 'Intermediate') {
        // For Intermediate, allow more flexible timing (every 30 minutes)
        this.availableStartTimes = this.baseTimeSlots.filter(time => {
          const hour = parseInt(time.split(':')[0]);
          return hour >= 8 && hour <= 16; // 8 AM to 4 PM
        });
      } else {
        // For BS/MS/PhD, standard hourly slots
        this.availableStartTimes = this.baseTimeSlots.filter(time => {
          const [hour, minute] = time.split(':').map(Number);
          return hour >= 8 && hour <= 17 && (minute === 0 || minute === 30); // 8 AM to 5 PM
        });
      }
    } else {
      this.availableStartTimes = this.baseTimeSlots;
    }
  }

  calculateEndTime(): void {
    const startTime = this.timetableForm.get('startTime')?.value;
    const duration = this.timetableForm.get('duration')?.value;

    if (startTime && duration) {
      const [hours, minutes] = startTime.split(':').map(Number);
      const startMinutes = hours * 60 + minutes;
      const endMinutes = startMinutes + parseInt(duration);

      const endHours = Math.floor(endMinutes / 60);
      const endMins = endMinutes % 60;

      const endTime = `${endHours.toString().padStart(2, '0')}:${endMins.toString().padStart(2, '0')}`;
      this.timetableForm.get('endTime')?.setValue(endTime);
    }
  }

  loadTeachers(): void {
    this.userService.getUsersByRole('Teacher').subscribe({
      next: (response) => {
        if (response.success) {
          this.teachers = response.users;
        }
      },
      error: (error) => {
        console.error('Error loading teachers:', error);
        Swal.fire('Error', 'Failed to load teachers', 'error');
      }
    });
  }

  loadDepartmentsByProgram(programId: string): void {
    this.departmentService.getDepartmentsByProgram(programId, true).subscribe({
      next: (response) => {
        if (response.success) {
          this.filteredDepartments = response.departments;
        }
      },
      error: (error) => {
        console.error('Error loading departments:', error);
        this.filteredDepartments = [];
      }
    });
  }

  loadClassesByDepartment(departmentId: string): void {
    const programId = this.timetableForm.get('program')?.value;
    this.classesService.getAllClasses({
      program: programId,
      department: departmentId,
      isActive: true
    }).subscribe({
      next: (response) => {
        if (response.success) {
          this.filteredClasses = response.classes;
        }
      },
      error: (error) => {
        console.error('Error loading classes:', error);
        this.filteredClasses = [];
      }
    });
  }

  loadSubjectsByDepartment(departmentId: string): void {
    this.subjectService.getSubjectsByDepartment(departmentId, true).subscribe({
      next: (response) => {
        if (response.success) {
          this.filteredSubjects = response.subjects;
        }
      },
      error: (error) => {
        console.error('Error loading subjects:', error);
        this.filteredSubjects = [];
      }
    });
  }

  loadTeachersByProgram(programId: string): void {
    this.userService.getUsersByProgram(programId, 'Teacher').subscribe({
      next: (response) => {
        if (response.success) {
          this.filteredTeachers = response.users;
        }
      },
      error: (error) => {
        console.error('Error loading teachers by program:', error);
        // Fallback to all teachers
        this.filteredTeachers = this.teachers;
      }
    });
  }

  loadTimetables(): void {
    this.timetableService.getAllTimetableEntries().subscribe({
      next: (response) => {
        if (response.success) {
          this.timetables = response.timetable;
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading timetables:', error);
        this.loading = false;
      }
    });
  }

  onSubmit(): void {
    if (this.timetableForm.valid) {
      this.submitting = true;
      const formData = this.timetableForm.value;

      // Ensure proper data structure for backend
      const timetableData = {
        program: formData.program,
        department: formData.department,
        class: formData.class,
        subject: formData.subject,
        teacher: formData.teacher,
        dayOfWeek: formData.dayOfWeek,
        timeSlot: {
          startTime: formData.startTime,
          endTime: formData.endTime,
          duration: parseInt(formData.duration)
        },
        room: formData.room || '',
        semester: parseInt(formData.semester),
        academicYear: formData.academicYear,
        notes: formData.notes || ''
      };

      console.log('Submitting timetable data:', timetableData); // Debug log

      const request = this.editingTimetableId
        ? this.timetableService.updateTimetableEntry(this.editingTimetableId, timetableData)
        : this.timetableService.createTimetableEntry(timetableData);

      request.subscribe({
        next: (response) => {
          if (response.success) {
            const action = this.editingTimetableId ? 'updated' : 'created';
            this.snackBar.open(`Timetable entry ${action} successfully`, 'Close', {
              duration: 3000
            });
            this.timetableForm.reset();
            this.timetableForm.patchValue({ academicYear: '2024-2025' });
            this.editingTimetableId = null;
            this.loadTimetables();
          } else {
            const action = this.editingTimetableId ? 'update' : 'create';
            this.snackBar.open(response.message || `Failed to ${action} timetable entry`, 'Close', {
              duration: 3000
            });
          }
          this.submitting = false;
        },
        error: (error) => {
          const action = this.editingTimetableId ? 'updating' : 'creating';
          console.error(`Error ${action} timetable entry:`, error);
          this.snackBar.open(error.error?.message || `Error ${action} timetable entry`, 'Close', {
            duration: 3000
          });
          this.submitting = false;
        }
      });
    } else {
      console.log('Form is invalid:', this.timetableForm.errors);
      this.markFormGroupTouched(this.timetableForm);
    }
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  editTimetableEntry(timetable: any): void {
    this.editingTimetableId = timetable._id;

    // Load related data first
    this.loadDepartmentsByProgram(timetable.program._id);
    this.loadClassesByDepartment(timetable.department._id);
    this.loadSubjectsByDepartment(timetable.department._id);
    this.loadTeachersByProgram(timetable.program._id);

    // Populate form with existing data
    setTimeout(() => {
      this.timetableForm.patchValue({
        program: timetable.program._id,
        department: timetable.department._id,
        class: timetable.class._id,
        subject: timetable.subject._id,
        teacher: timetable.teacher._id,
        dayOfWeek: timetable.dayOfWeek,
        startTime: timetable.timeSlot.startTime,
        endTime: timetable.timeSlot.endTime,
        duration: timetable.timeSlot.duration,
        room: timetable.room || '',
        semester: timetable.semester,
        academicYear: timetable.academicYear,
        notes: timetable.notes || ''
      });

      // Calculate end time to ensure consistency
      this.calculateEndTime();
    }, 1000);

    // Scroll to form
    document.querySelector('.form-card')?.scrollIntoView({ behavior: 'smooth' });
  }

  cancelEdit(): void {
    this.editingTimetableId = null;
    this.timetableForm.reset();
    this.timetableForm.patchValue({
      academicYear: '2024-2025'
    });
  }

  deleteTimetableEntry(timetable: Timetable): void {
    if (confirm('Are you sure you want to delete this timetable entry?')) {
      this.timetableService.deleteTimetableEntry(timetable._id).subscribe({
        next: (response) => {
          if (response.success) {
            this.snackBar.open('Timetable entry deleted successfully', 'Close', {
              duration: 3000
            });
            this.loadTimetables();
          }
        },
        error: (error) => {
          console.error('Error deleting timetable entry:', error);
          this.snackBar.open('Error deleting timetable entry', 'Close', {
            duration: 3000
          });
        }
      });
    }
  }

  formatTimeSlot(timeSlot: any): string {
    return `${timeSlot.startTime} - ${timeSlot.endTime}`;
  }

  formatTimetableEntry(timetable: any): string {
    const timeSlot = this.formatTimeSlot(timetable.timeSlot);
    const teacherName = timetable.teacher?.name || 'Unknown Teacher';
    const className = timetable.class?.className || 'Unknown Class';
    const programName = timetable.program?.name || '';

    // Format based on program type
    if (programName === 'Intermediate') {
      return `${timeSlot} ${teacherName} with ${className}`;
    } else {
      return `${timeSlot} ${teacherName} with ${className}`;
    }
  }

  getClassDisplayName(classData: any): string {
    if (!classData) return 'Unknown Class';

    const program = classData.program?.name || '';
    if (program === 'Intermediate') {
      return `${classData.className}${classData.section ? ' - Section ' + classData.section : ''}`;
    } else {
      return classData.className;
    }
  }

  getDurationLabel(duration: number): string {
    if (duration < 60) {
      return `${duration} min`;
    } else if (duration === 60) {
      return '1 hour';
    } else if (duration === 90) {
      return '1.5 hours';
    } else if (duration === 120) {
      return '2 hours';
    } else if (duration === 180) {
      return '3 hours';
    } else {
      return `${Math.floor(duration / 60)}h ${duration % 60}m`;
    }
  }
}
