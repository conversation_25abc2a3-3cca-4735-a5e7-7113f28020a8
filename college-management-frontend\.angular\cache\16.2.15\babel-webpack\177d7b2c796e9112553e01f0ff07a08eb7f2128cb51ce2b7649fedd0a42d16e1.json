{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction ForgotPasswordComponent_div_19_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Email is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ForgotPasswordComponent_div_19_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Please enter a valid email\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ForgotPasswordComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtemplate(1, ForgotPasswordComponent_div_19_small_1_Template, 2, 0, \"small\", 28);\n    i0.ɵɵtemplate(2, ForgotPasswordComponent_div_19_small_2_Template, 2, 0, \"small\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r0.forgotPasswordForm.get(\"email\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r0.forgotPasswordForm.get(\"email\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"email\"]);\n  }\n}\nfunction ForgotPasswordComponent_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 29);\n  }\n}\nexport class ForgotPasswordComponent {\n  constructor(fb, authService, router) {\n    this.fb = fb;\n    this.authService = authService;\n    this.router = router;\n    this.loading = false;\n  }\n  ngOnInit() {\n    this.forgotPasswordForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]]\n    });\n  }\n  onSubmit() {\n    if (this.forgotPasswordForm.valid) {\n      this.loading = true;\n      const email = this.forgotPasswordForm.value.email;\n      this.authService.forgotPassword(email).subscribe({\n        next: response => {\n          this.loading = false;\n          if (response.success) {\n            // Store user data for OTP verification\n            localStorage.setItem('resetUser', JSON.stringify(response.user));\n            Swal.fire({\n              title: 'OTP Sent!',\n              text: `We've sent a verification code to ${email}. Please check your email.`,\n              icon: 'success',\n              confirmButtonText: 'Enter OTP',\n              confirmButtonColor: '#29578c'\n            }).then(result => {\n              if (result.isConfirmed) {\n                this.router.navigate(['/auth/Enter-code']);\n              }\n            });\n          }\n        },\n        error: error => {\n          this.loading = false;\n          console.error('Forgot password error:', error);\n          const errorMessage = error.error?.error || error.error?.message || 'Failed to send OTP. Please try again.';\n          Swal.fire({\n            title: 'Error',\n            text: errorMessage,\n            icon: 'error',\n            confirmButtonColor: '#29578c'\n          });\n        }\n      });\n    } else {\n      Swal.fire({\n        title: 'Invalid Email',\n        text: 'Please enter a valid email address.',\n        icon: 'warning',\n        confirmButtonColor: '#29578c'\n      });\n    }\n  }\n  static #_ = this.ɵfac = function ForgotPasswordComponent_Factory(t) {\n    return new (t || ForgotPasswordComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ForgotPasswordComponent,\n    selectors: [[\"app-forgot-password\"]],\n    decls: 40,\n    vars: 7,\n    consts: [[1, \"container-fluid\"], [1, \"row\", \"vh-100\"], [1, \"col-lg-5\", \"d-flex\", \"flex-column\", \"justify-content-center\", \"align-items-center\", \"p-3\", \"bg-white\"], [1, \"w-100\", 2, \"max-width\", \"400px\"], [1, \"text-center\"], [1, \"mb-4\"], [\"src\", \"../../../assets/images/logo.jpeg\"], [1, \"d-flex\", \"justify-content-center\", \"mt-5\", \"mb-2\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"fa-key\", \"key\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"mb-3\"], [\"for\", \"email\", 1, \"form-label\"], [\"type\", \"email\", \"id\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter your email\", 1, \"form-control\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [1, \"d-grid\", \"gap-2\"], [\"type\", \"submit\", 1, \"btn\", \"submit\", 3, \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", \"role\", \"status\", \"aria-hidden\", \"true\", 4, \"ngIf\"], [1, \"mt-3\", \"text-center\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"fa-arrow-left\"], [\"routerLink\", \"/auth\"], [1, \"col-lg-7\", \"d-none\", \"d-lg-flex\", \"flex-column\", \"align-items-start\", \"bg-light\", \"position-relative\"], [1, \"text-start\", \"p-5\", \"w-100\"], [1, \"blockquote\"], [1, \"blockquote-footer\"], [\"title\", \"Source Title\"], [1, \"image-container\"], [\"src\", \"../../../assets/images/background.jpeg\", \"alt\", \"Stats graph\", 1, \"img-fluid\", \"position-absolute\"], [1, \"invalid-feedback\"], [4, \"ngIf\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"]],\n    template: function ForgotPasswordComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"h2\", 5);\n        i0.ɵɵelement(6, \"img\", 6);\n        i0.ɵɵtext(7, \" GPGC (Swabi)\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"div\", 7);\n        i0.ɵɵelement(9, \"i\", 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"h1\", 5);\n        i0.ɵɵtext(11, \"Forgot Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"p\");\n        i0.ɵɵtext(13, \"No Worries , we will send you reset instructions.\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(14, \"form\", 9);\n        i0.ɵɵlistener(\"ngSubmit\", function ForgotPasswordComponent_Template_form_ngSubmit_14_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵelementStart(15, \"div\", 10)(16, \"label\", 11);\n        i0.ɵɵtext(17, \"Email\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(18, \"input\", 12);\n        i0.ɵɵtemplate(19, ForgotPasswordComponent_div_19_Template, 3, 2, \"div\", 13);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"div\", 14)(21, \"button\", 15);\n        i0.ɵɵtemplate(22, ForgotPasswordComponent_span_22_Template, 1, 0, \"span\", 16);\n        i0.ɵɵtext(23);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(24, \"p\", 17);\n        i0.ɵɵelement(25, \"i\", 18);\n        i0.ɵɵtext(26, \" \\u00A0\");\n        i0.ɵɵelementStart(27, \"a\", 19);\n        i0.ɵɵtext(28, \"Back to log in \");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(29, \"div\", 20)(30, \"div\", 21)(31, \"blockquote\", 22)(32, \"h2\", 5);\n        i0.ɵɵtext(33, \"College management system Login page\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(34, \"footer\", 23);\n        i0.ɵɵtext(35, \"Name\");\n        i0.ɵɵelementStart(36, \"cite\", 24);\n        i0.ɵɵtext(37, \"Owner ~ GPGC SWABI\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(38, \"div\", 25);\n        i0.ɵɵelement(39, \"img\", 26);\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        let tmp_1_0;\n        let tmp_2_0;\n        i0.ɵɵadvance(14);\n        i0.ɵɵproperty(\"formGroup\", ctx.forgotPasswordForm);\n        i0.ɵɵadvance(4);\n        i0.ɵɵclassProp(\"is-invalid\", ((tmp_1_0 = ctx.forgotPasswordForm.get(\"email\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.forgotPasswordForm.get(\"email\")) == null ? null : tmp_1_0.touched));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.forgotPasswordForm.get(\"email\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.forgotPasswordForm.get(\"email\")) == null ? null : tmp_2_0.touched));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"disabled\", ctx.loading || ctx.forgotPasswordForm.invalid);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate1(\" \", ctx.loading ? \"Sending...\" : \"Reset Password\", \" \");\n      }\n    },\n    dependencies: [i4.NgIf, i3.RouterLink, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n    styles: [\"body[_ngcontent-%COMP%], html[_ngcontent-%COMP%] {\\n    margin: 0;\\n    padding: 0;\\n    overflow-x: hidden; \\n\\n    height: 100%;\\n}\\n\\n\\n\\n.image-container[_ngcontent-%COMP%] {\\n    width: 100%;\\n    height: 100%;\\n    position: relative;\\n}\\n\\n.image-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    max-width: 80%;\\n    height: auto;\\n    position: absolute;\\n    right: 0;\\n    bottom: 0;\\n    object-fit: contain; \\n\\n}\\n.submit[_ngcontent-%COMP%]{\\n    background-color: #29578c;\\n    color: white;\\n}\\n.forgot[_ngcontent-%COMP%]{\\n    color: #29578c;\\n    text-decoration: none;\\n    font-weight: 700;\\n}\\na[_ngcontent-%COMP%]{\\n    cursor: pointer;\\n    text-decoration: none;\\n    color: black;\\n    font-weight: 600;\\n}\\n.key[_ngcontent-%COMP%]{\\n    background-color: #b8d1ef !important;\\n    border-radius: 50%;\\n    height: 50px;\\n    width: 50px;\\n    display: flex;\\n    justify-content: center;\\n    align-items: center;\\n    color: #29578c;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "ForgotPasswordComponent_div_19_small_1_Template", "ForgotPasswordComponent_div_19_small_2_Template", "ɵɵadvance", "ɵɵproperty", "tmp_0_0", "ctx_r0", "forgotPasswordForm", "get", "errors", "tmp_1_0", "ɵɵelement", "ForgotPasswordComponent", "constructor", "fb", "authService", "router", "loading", "ngOnInit", "group", "email", "required", "onSubmit", "valid", "value", "forgotPassword", "subscribe", "next", "response", "success", "localStorage", "setItem", "JSON", "stringify", "user", "fire", "title", "text", "icon", "confirmButtonText", "confirmButtonColor", "then", "result", "isConfirmed", "navigate", "error", "console", "errorMessage", "message", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "_2", "selectors", "decls", "vars", "consts", "template", "ForgotPasswordComponent_Template", "rf", "ctx", "ɵɵlistener", "ForgotPasswordComponent_Template_form_ngSubmit_14_listener", "ForgotPasswordComponent_div_19_Template", "ForgotPasswordComponent_span_22_Template", "ɵɵclassProp", "invalid", "touched", "tmp_2_0", "ɵɵtextInterpolate1"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\auth\\forgot-password\\forgot-password.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\auth\\forgot-password\\forgot-password.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { AuthService } from 'src/app/services/auth.service';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-forgot-password',\r\n  templateUrl: './forgot-password.component.html',\r\n  styleUrls: ['./forgot-password.component.css']\r\n})\r\nexport class ForgotPasswordComponent implements OnInit {\r\n  forgotPasswordForm!: FormGroup;\r\n  loading = false;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private authService: AuthService,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.forgotPasswordForm = this.fb.group({\r\n      email: ['', [Validators.required, Validators.email]]\r\n    });\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (this.forgotPasswordForm.valid) {\r\n      this.loading = true;\r\n      const email = this.forgotPasswordForm.value.email;\r\n\r\n      this.authService.forgotPassword(email).subscribe({\r\n        next: (response) => {\r\n          this.loading = false;\r\n          if (response.success) {\r\n            // Store user data for OTP verification\r\n            localStorage.setItem('resetUser', JSON.stringify(response.user));\r\n\r\n            Swal.fire({\r\n              title: 'OTP Sent!',\r\n              text: `We've sent a verification code to ${email}. Please check your email.`,\r\n              icon: 'success',\r\n              confirmButtonText: 'Enter OTP',\r\n              confirmButtonColor: '#29578c'\r\n            }).then((result) => {\r\n              if (result.isConfirmed) {\r\n                this.router.navigate(['/auth/Enter-code']);\r\n              }\r\n            });\r\n          }\r\n        },\r\n        error: (error) => {\r\n          this.loading = false;\r\n          console.error('Forgot password error:', error);\r\n\r\n          const errorMessage = error.error?.error || error.error?.message || 'Failed to send OTP. Please try again.';\r\n\r\n          Swal.fire({\r\n            title: 'Error',\r\n            text: errorMessage,\r\n            icon: 'error',\r\n            confirmButtonColor: '#29578c'\r\n          });\r\n        }\r\n      });\r\n    } else {\r\n      Swal.fire({\r\n        title: 'Invalid Email',\r\n        text: 'Please enter a valid email address.',\r\n        icon: 'warning',\r\n        confirmButtonColor: '#29578c'\r\n      });\r\n    }\r\n  }\r\n}\r\n", "<div class=\"container-fluid\">\r\n    <div class=\"row vh-100\">\r\n        <div class=\"col-lg-5 d-flex flex-column justify-content-center align-items-center p-3 bg-white\">\r\n            <div class=\"w-100\" style=\"max-width: 400px;\">\r\n                <div class=\"text-center\">\r\n                <h2 class=\"mb-4\"><img src=\"../../../assets/images/logo.jpeg\"/> GPGC (Swabi)</h2>\r\n                <div class=\"d-flex justify-content-center mt-5 mb-2\">\r\n                    <i class=\"fa fa-key key\" aria-hidden=\"true\"></i>\r\n                </div>\r\n                \r\n                <h1 class=\"mb-4\">Forgot Password</h1>\r\n                <p>No Worries , we will send you reset instructions.</p>\r\n            </div>\r\n                <form [formGroup]=\"forgotPasswordForm\" (ngSubmit)=\"onSubmit()\">\r\n                    <div class=\"mb-3\">\r\n                        <label for=\"email\" class=\"form-label\">Email</label>\r\n                        <input\r\n                            type=\"email\"\r\n                            class=\"form-control\"\r\n                            id=\"email\"\r\n                            formControlName=\"email\"\r\n                            placeholder=\"Enter your email\"\r\n                            [class.is-invalid]=\"forgotPasswordForm.get('email')?.invalid && forgotPasswordForm.get('email')?.touched\">\r\n                        <div class=\"invalid-feedback\" *ngIf=\"forgotPasswordForm.get('email')?.invalid && forgotPasswordForm.get('email')?.touched\">\r\n                            <small *ngIf=\"forgotPasswordForm.get('email')?.errors?.['required']\">Email is required</small>\r\n                            <small *ngIf=\"forgotPasswordForm.get('email')?.errors?.['email']\">Please enter a valid email</small>\r\n                        </div>\r\n                    </div>\r\n\r\n\r\n                    <div class=\"d-grid gap-2\">\r\n                        <button\r\n                            type=\"submit\"\r\n                            class=\"btn submit\"\r\n                            [disabled]=\"loading || forgotPasswordForm.invalid\">\r\n                            <span *ngIf=\"loading\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\r\n                            {{ loading ? 'Sending...' : 'Reset Password' }}\r\n                        </button>\r\n                    </div>\r\n                    <p class=\"mt-3 text-center\"> <i class=\"fa fa-arrow-left\" aria-hidden=\"true\"></i> &nbsp;<a routerLink=\"/auth\">Back to log in </a></p>\r\n                </form>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-lg-7 d-none d-lg-flex flex-column align-items-start bg-light position-relative\">\r\n            <div class=\"text-start p-5 w-100\">\r\n                <blockquote class=\"blockquote\">\r\n                    <h2 class=\"mb-4\">College management system Login page</h2>\r\n                    <footer class=\"blockquote-footer\">Name<cite title=\"Source Title\">Owner ~ GPGC SWABI</cite></footer>\r\n                </blockquote>\r\n            </div>\r\n            <div class=\"image-container\">\r\n                <img src=\"../../../assets/images/background.jpeg\" class=\"img-fluid position-absolute\" alt=\"Stats graph\">\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAGnE,OAAOC,IAAI,MAAM,aAAa;;;;;;;;ICoBFC,EAAA,CAAAC,cAAA,YAAqE;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAC9FH,EAAA,CAAAC,cAAA,YAAkE;IAAAD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAFxGH,EAAA,CAAAC,cAAA,cAA2H;IACvHD,EAAA,CAAAI,UAAA,IAAAC,+CAAA,oBAA8F;IAC9FL,EAAA,CAAAI,UAAA,IAAAE,+CAAA,oBAAoG;IACxGN,EAAA,CAAAG,YAAA,EAAM;;;;;;IAFMH,EAAA,CAAAO,SAAA,GAA2D;IAA3DP,EAAA,CAAAQ,UAAA,UAAAC,OAAA,GAAAC,MAAA,CAAAC,kBAAA,CAAAC,GAAA,4BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAA2D;IAC3Db,EAAA,CAAAO,SAAA,GAAwD;IAAxDP,EAAA,CAAAQ,UAAA,UAAAM,OAAA,GAAAJ,MAAA,CAAAC,kBAAA,CAAAC,GAAA,4BAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,UAAwD;;;;;IAUhEb,EAAA,CAAAe,SAAA,eAA4G;;;ADxBxI,OAAM,MAAOC,uBAAuB;EAIlCC,YACUC,EAAe,EACfC,WAAwB,EACxBC,MAAc;IAFd,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IALhB,KAAAC,OAAO,GAAG,KAAK;EAMZ;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACX,kBAAkB,GAAG,IAAI,CAACO,EAAE,CAACK,KAAK,CAAC;MACtCC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC1B,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC0B,KAAK,CAAC;KACpD,CAAC;EACJ;EAEAE,QAAQA,CAAA;IACN,IAAI,IAAI,CAACf,kBAAkB,CAACgB,KAAK,EAAE;MACjC,IAAI,CAACN,OAAO,GAAG,IAAI;MACnB,MAAMG,KAAK,GAAG,IAAI,CAACb,kBAAkB,CAACiB,KAAK,CAACJ,KAAK;MAEjD,IAAI,CAACL,WAAW,CAACU,cAAc,CAACL,KAAK,CAAC,CAACM,SAAS,CAAC;QAC/CC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAACX,OAAO,GAAG,KAAK;UACpB,IAAIW,QAAQ,CAACC,OAAO,EAAE;YACpB;YACAC,YAAY,CAACC,OAAO,CAAC,WAAW,EAAEC,IAAI,CAACC,SAAS,CAACL,QAAQ,CAACM,IAAI,CAAC,CAAC;YAEhEvC,IAAI,CAACwC,IAAI,CAAC;cACRC,KAAK,EAAE,WAAW;cAClBC,IAAI,EAAE,qCAAqCjB,KAAK,4BAA4B;cAC5EkB,IAAI,EAAE,SAAS;cACfC,iBAAiB,EAAE,WAAW;cAC9BC,kBAAkB,EAAE;aACrB,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAI;cACjB,IAAIA,MAAM,CAACC,WAAW,EAAE;gBACtB,IAAI,CAAC3B,MAAM,CAAC4B,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;;YAE9C,CAAC,CAAC;;QAEN,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAC5B,OAAO,GAAG,KAAK;UACpB6B,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;UAE9C,MAAME,YAAY,GAAGF,KAAK,CAACA,KAAK,EAAEA,KAAK,IAAIA,KAAK,CAACA,KAAK,EAAEG,OAAO,IAAI,uCAAuC;UAE1GrD,IAAI,CAACwC,IAAI,CAAC;YACRC,KAAK,EAAE,OAAO;YACdC,IAAI,EAAEU,YAAY;YAClBT,IAAI,EAAE,OAAO;YACbE,kBAAkB,EAAE;WACrB,CAAC;QACJ;OACD,CAAC;KACH,MAAM;MACL7C,IAAI,CAACwC,IAAI,CAAC;QACRC,KAAK,EAAE,eAAe;QACtBC,IAAI,EAAE,qCAAqC;QAC3CC,IAAI,EAAE,SAAS;QACfE,kBAAkB,EAAE;OACrB,CAAC;;EAEN;EAAC,QAAAS,CAAA,G;qBA/DUrC,uBAAuB,EAAAhB,EAAA,CAAAsD,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxD,EAAA,CAAAsD,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA1D,EAAA,CAAAsD,iBAAA,CAAAK,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAvB7C,uBAAuB;IAAA8C,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCXpCpE,EAAA,CAAAC,cAAA,aAA6B;QAKID,EAAA,CAAAe,SAAA,aAA6C;QAACf,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAChFH,EAAA,CAAAC,cAAA,aAAqD;QACjDD,EAAA,CAAAe,SAAA,WAAgD;QACpDf,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,aAAiB;QAAAD,EAAA,CAAAE,MAAA,uBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACrCH,EAAA,CAAAC,cAAA,SAAG;QAAAD,EAAA,CAAAE,MAAA,yDAAiD;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAExDH,EAAA,CAAAC,cAAA,eAA+D;QAAxBD,EAAA,CAAAsE,UAAA,sBAAAC,2DAAA;UAAA,OAAYF,GAAA,CAAA3C,QAAA,EAAU;QAAA,EAAC;QAC1D1B,EAAA,CAAAC,cAAA,eAAkB;QACwBD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACnDH,EAAA,CAAAe,SAAA,iBAM8G;QAC9Gf,EAAA,CAAAI,UAAA,KAAAoE,uCAAA,kBAGM;QACVxE,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAAC,cAAA,eAA0B;QAKlBD,EAAA,CAAAI,UAAA,KAAAqE,wCAAA,mBAA4G;QAC5GzE,EAAA,CAAAE,MAAA,IACJ;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAEbH,EAAA,CAAAC,cAAA,aAA4B;QAACD,EAAA,CAAAe,SAAA,aAAmD;QAACf,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAC,cAAA,aAAsB;QAAAD,EAAA,CAAAE,MAAA,uBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAI5IH,EAAA,CAAAC,cAAA,eAAgG;QAGnED,EAAA,CAAAE,MAAA,4CAAoC;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC1DH,EAAA,CAAAC,cAAA,kBAAkC;QAAAD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAC,cAAA,gBAA2B;QAAAD,EAAA,CAAAE,MAAA,0BAAkB;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAGlGH,EAAA,CAAAC,cAAA,eAA6B;QACzBD,EAAA,CAAAe,SAAA,eAAwG;QAC5Gf,EAAA,CAAAG,YAAA,EAAM;;;;;QAvCIH,EAAA,CAAAO,SAAA,IAAgC;QAAhCP,EAAA,CAAAQ,UAAA,cAAA6D,GAAA,CAAA1D,kBAAA,CAAgC;QAS1BX,EAAA,CAAAO,SAAA,GAAyG;QAAzGP,EAAA,CAAA0E,WAAA,iBAAA5D,OAAA,GAAAuD,GAAA,CAAA1D,kBAAA,CAAAC,GAAA,4BAAAE,OAAA,CAAA6D,OAAA,OAAA7D,OAAA,GAAAuD,GAAA,CAAA1D,kBAAA,CAAAC,GAAA,4BAAAE,OAAA,CAAA8D,OAAA,EAAyG;QAC9E5E,EAAA,CAAAO,SAAA,GAA0F;QAA1FP,EAAA,CAAAQ,UAAA,WAAAqE,OAAA,GAAAR,GAAA,CAAA1D,kBAAA,CAAAC,GAAA,4BAAAiE,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAAR,GAAA,CAAA1D,kBAAA,CAAAC,GAAA,4BAAAiE,OAAA,CAAAD,OAAA,EAA0F;QAWrH5E,EAAA,CAAAO,SAAA,GAAkD;QAAlDP,EAAA,CAAAQ,UAAA,aAAA6D,GAAA,CAAAhD,OAAA,IAAAgD,GAAA,CAAA1D,kBAAA,CAAAgE,OAAA,CAAkD;QAC3C3E,EAAA,CAAAO,SAAA,GAAa;QAAbP,EAAA,CAAAQ,UAAA,SAAA6D,GAAA,CAAAhD,OAAA,CAAa;QACpBrB,EAAA,CAAAO,SAAA,GACJ;QADIP,EAAA,CAAA8E,kBAAA,MAAAT,GAAA,CAAAhD,OAAA,wCACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}