{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/services/user.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"src/app/services/program.service\";\nimport * as i5 from \"src/app/services/department.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/core\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/card\";\nimport * as i10 from \"@angular/material/checkbox\";\nimport * as i11 from \"@angular/material/icon\";\nimport * as i12 from \"@angular/material/input\";\nimport * as i13 from \"@angular/material/form-field\";\nimport * as i14 from \"@angular/material/progress-spinner\";\nimport * as i15 from \"@angular/material/select\";\nimport * as i16 from \"@angular/material/tooltip\";\nfunction TeacherFormComponent_mat_error_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Full name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TeacherFormComponent_mat_error_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Name must be at least 2 characters long \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TeacherFormComponent_mat_error_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Contact number is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TeacherFormComponent_mat_error_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Please enter a valid 11-digit contact number \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TeacherFormComponent_mat_error_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Email is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TeacherFormComponent_mat_error_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Please enter a valid email address \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TeacherFormComponent_mat_option_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const program_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", program_r15._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", program_r15.name, \" - \", program_r15.fullName, \" \");\n  }\n}\nfunction TeacherFormComponent_mat_error_74_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Program selection is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TeacherFormComponent_mat_option_82_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const department_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", department_r16._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", department_r16.name, \" \");\n  }\n}\nfunction TeacherFormComponent_mat_error_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Department selection is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TeacherFormComponent_mat_hint_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-hint\");\n    i0.ɵɵtext(1, \"Please select a program first\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TeacherFormComponent_mat_error_109_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Designation is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TeacherFormComponent_mat_error_143_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Password is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TeacherFormComponent_mat_error_143_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Password must be at least 6 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TeacherFormComponent_mat_error_143_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtemplate(1, TeacherFormComponent_mat_error_143_span_1_Template, 2, 0, \"span\", 17);\n    i0.ɵɵtemplate(2, TeacherFormComponent_mat_error_143_span_2_Template, 2, 0, \"span\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r12.teacherForm.get(\"password\")) == null ? null : tmp_0_0.hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r12.teacherForm.get(\"password\")) == null ? null : tmp_1_0.hasError(\"minlength\"));\n  }\n}\nfunction TeacherFormComponent_mat_icon_161_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"save\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TeacherFormComponent_mat_spinner_162_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 49);\n  }\n}\nexport class TeacherFormComponent {\n  constructor(fb, userService, router, route, programService, departmentService) {\n    this.fb = fb;\n    this.userService = userService;\n    this.router = router;\n    this.route = route;\n    this.programService = programService;\n    this.departmentService = departmentService;\n    this.programs = [];\n    this.departments = [];\n    this.filteredDepartments = [];\n    this.loading = false;\n    this.isEditing = false;\n    this.teacherId = null;\n    this.selectedFile = null;\n    this.hidePassword = true;\n    this.initializeForm();\n  }\n  initializeForm() {\n    this.teacherForm = this.fb.group({\n      name: ['', [Validators.required, Validators.minLength(2)]],\n      father_name: [''],\n      email: ['', [Validators.required, Validators.email]],\n      contact: ['', [Validators.required, Validators.pattern(/^[0-9]{11}$/)]],\n      program: ['', Validators.required],\n      department: ['', Validators.required],\n      designation: ['', Validators.required],\n      position: [''],\n      isVisiting: [false],\n      password: ['', [Validators.required, Validators.minLength(6)]],\n      address: ['']\n    });\n    this.setupFormSubscriptions();\n  }\n  setupFormSubscriptions() {\n    this.teacherForm.get('program')?.valueChanges.subscribe(programId => {\n      if (programId) {\n        this.loadDepartmentsByProgram(programId);\n        this.teacherForm.get('department')?.setValue('');\n      } else {\n        this.filteredDepartments = [];\n      }\n    });\n  }\n  ngOnInit() {\n    this.teacherId = this.route.snapshot.paramMap.get('id');\n    this.isEditing = !!this.teacherId;\n    this.loadPrograms();\n    if (this.isEditing) {\n      this.loadTeacherData();\n      this.teacherForm.get('password')?.clearValidators();\n      this.teacherForm.get('password')?.updateValueAndValidity();\n    }\n  }\n  loadPrograms() {\n    this.programService.getAllPrograms(true).subscribe({\n      next: response => {\n        if (response.success) {\n          this.programs = response.programs;\n        }\n      },\n      error: error => {\n        console.error('Error loading programs:', error);\n        Swal.fire('Error', 'Failed to load programs', 'error');\n      }\n    });\n  }\n  loadDepartmentsByProgram(programId) {\n    this.departmentService.getDepartmentsByProgram(programId, true).subscribe({\n      next: response => {\n        if (response.success) {\n          this.filteredDepartments = response.departments;\n        }\n      },\n      error: error => {\n        console.error('Error loading departments:', error);\n        this.filteredDepartments = [];\n      }\n    });\n  }\n  loadTeacherData() {\n    if (this.teacherId) {\n      this.userService.getUserProfile(this.teacherId).subscribe({\n        next: response => {\n          if (response.success) {\n            const teacher = response.user;\n            this.teacherForm.patchValue({\n              name: teacher.name,\n              father_name: teacher.father_name,\n              email: teacher.email,\n              contact: teacher.contact,\n              program: teacher.program?._id,\n              department: teacher.department?._id,\n              designation: teacher.designation,\n              position: teacher.position,\n              isVisiting: teacher.isVisiting,\n              address: teacher.address\n            });\n            if (teacher.program?._id) {\n              this.loadDepartmentsByProgram(teacher.program._id);\n            }\n          }\n        },\n        error: error => {\n          console.error('Error loading teacher data:', error);\n          Swal.fire('Error', 'Failed to load teacher data', 'error');\n        }\n      });\n    }\n  }\n  // File selection handler\n  onFileSelected(event) {\n    const input = event.target;\n    if (input.files && input.files.length > 0) {\n      this.selectedFile = input.files[0];\n      console.log('File selected:', this.selectedFile);\n    }\n  }\n  // File drop handler\n  onFileDrop(event) {\n    event.preventDefault();\n    if (event.dataTransfer?.files && event.dataTransfer.files.length > 0) {\n      this.selectedFile = event.dataTransfer.files[0];\n      console.log('File dropped:', this.selectedFile);\n    }\n  }\n  // Prevent default drag behavior\n  onDragOver(event) {\n    event.preventDefault();\n  }\n  onSave() {\n    if (this.teacherForm.invalid) {\n      this.markFormGroupTouched();\n      Swal.fire('Validation Error', 'Please fill in all required fields correctly.', 'warning');\n      return;\n    }\n    this.loading = true;\n    const formData = this.teacherForm.value;\n    const teacherData = {\n      ...formData,\n      role: 'Teacher'\n    };\n    const request = this.isEditing ? this.userService.updateUserProfile(this.teacherId, teacherData) : this.userService.register(teacherData);\n    request.subscribe({\n      next: response => {\n        this.loading = false;\n        const message = this.isEditing ? 'Teacher updated successfully!' : 'Teacher added successfully!';\n        Swal.fire({\n          title: 'Success!',\n          text: message,\n          icon: 'success',\n          confirmButtonColor: '#3085d6',\n          timer: 1500\n        }).then(() => {\n          this.router.navigate(['/dashboard/admin/teacher']);\n        });\n      },\n      error: error => {\n        this.loading = false;\n        console.error('Error saving teacher:', error);\n        const errorMessage = error.error?.message || 'Something went wrong while saving the teacher.';\n        Swal.fire('Error', errorMessage, 'error');\n      }\n    });\n  }\n  markFormGroupTouched() {\n    Object.keys(this.teacherForm.controls).forEach(key => {\n      const control = this.teacherForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  onCancel() {\n    this.router.navigate(['/dashboard/admin/teacher']);\n  }\n  static #_ = this.ɵfac = function TeacherFormComponent_Factory(t) {\n    return new (t || TeacherFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.ProgramService), i0.ɵɵdirectiveInject(i5.DepartmentService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: TeacherFormComponent,\n    selectors: [[\"app-teacher-form\"]],\n    decls: 164,\n    vars: 25,\n    consts: [[1, \"teacher-form-container\"], [1, \"container-fluid\"], [1, \"form-header\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"form-title\"], [1, \"title-icon\"], [1, \"form-subtitle\"], [\"mat-icon-button\", \"\", \"routerLink\", \"/dashboard/admin/teacher\", \"matTooltip\", \"Back to Teachers List\"], [1, \"teacher-form-card\"], [1, \"teacher-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"form-section\"], [1, \"section-title\"], [1, \"row\"], [1, \"col-md-6\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"name\", \"placeholder\", \"Enter teacher's full name\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"father_name\", \"placeholder\", \"Enter father's name\"], [\"matInput\", \"\", \"formControlName\", \"contact\", \"placeholder\", \"Enter contact number (11 digits)\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter email address\"], [\"formControlName\", \"program\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"formControlName\", \"department\", 3, \"disabled\"], [\"formControlName\", \"designation\"], [\"value\", \"Professor\"], [\"value\", \"Associate Professor\"], [\"value\", \"Assistant Professor\"], [\"value\", \"Lecturer\"], [\"value\", \"Senior Lecturer\"], [\"value\", \"Visiting Faculty\"], [\"formControlName\", \"position\"], [\"value\", \"Head of Department\"], [\"value\", \"Program Coordinator\"], [\"value\", \"Faculty Member\"], [\"value\", \"Lab Instructor\"], [\"value\", \"Research Supervisor\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"placeholder\", \"Enter password\", \"minlength\", \"6\", \"required\", \"\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [1, \"checkbox-section\"], [\"name\", \"is_visiting\", \"formControlName\", \"isVisiting\", \"color\", \"primary\"], [1, \"checkbox-label\"], [1, \"form-text\", \"text-muted\"], [1, \"form-actions\"], [\"mat-button\", \"\", \"type\", \"button\", \"routerLink\", \"/dashboard/admin/teacher\", 1, \"cancel-btn\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"save-btn\", 3, \"disabled\"], [\"diameter\", \"20\", \"class\", \"spinner\", 4, \"ngIf\"], [3, \"value\"], [\"diameter\", \"20\", 1, \"spinner\"]],\n    template: function TeacherFormComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\")(5, \"h2\", 4)(6, \"mat-icon\", 5);\n        i0.ɵɵtext(7);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"p\", 6);\n        i0.ɵɵtext(10);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"button\", 7)(12, \"mat-icon\");\n        i0.ɵɵtext(13, \"arrow_back\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(14, \"mat-card\", 8)(15, \"mat-card-content\")(16, \"form\", 9);\n        i0.ɵɵlistener(\"ngSubmit\", function TeacherFormComponent_Template_form_ngSubmit_16_listener() {\n          return ctx.onSave();\n        });\n        i0.ɵɵelementStart(17, \"div\", 10)(18, \"h3\", 11)(19, \"mat-icon\");\n        i0.ɵɵtext(20, \"person\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(21, \" Personal Information \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(22, \"div\", 12)(23, \"div\", 13)(24, \"mat-form-field\", 14)(25, \"mat-label\");\n        i0.ɵɵtext(26, \"Full Name *\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(27, \"input\", 15);\n        i0.ɵɵelementStart(28, \"mat-icon\", 16);\n        i0.ɵɵtext(29, \"person\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(30, TeacherFormComponent_mat_error_30_Template, 2, 0, \"mat-error\", 17);\n        i0.ɵɵtemplate(31, TeacherFormComponent_mat_error_31_Template, 2, 0, \"mat-error\", 17);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(32, \"div\", 13)(33, \"mat-form-field\", 14)(34, \"mat-label\");\n        i0.ɵɵtext(35, \"Father's Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(36, \"input\", 18);\n        i0.ɵɵelementStart(37, \"mat-icon\", 16);\n        i0.ɵɵtext(38, \"family_restroom\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(39, \"div\", 12)(40, \"div\", 13)(41, \"mat-form-field\", 14)(42, \"mat-label\");\n        i0.ɵɵtext(43, \"Contact Number *\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(44, \"input\", 19);\n        i0.ɵɵelementStart(45, \"mat-icon\", 16);\n        i0.ɵɵtext(46, \"phone\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(47, TeacherFormComponent_mat_error_47_Template, 2, 0, \"mat-error\", 17);\n        i0.ɵɵtemplate(48, TeacherFormComponent_mat_error_48_Template, 2, 0, \"mat-error\", 17);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(49, \"div\", 13)(50, \"mat-form-field\", 14)(51, \"mat-label\");\n        i0.ɵɵtext(52, \"Email Address *\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(53, \"input\", 20);\n        i0.ɵɵelementStart(54, \"mat-icon\", 16);\n        i0.ɵɵtext(55, \"email\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(56, TeacherFormComponent_mat_error_56_Template, 2, 0, \"mat-error\", 17);\n        i0.ɵɵtemplate(57, TeacherFormComponent_mat_error_57_Template, 2, 0, \"mat-error\", 17);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(58, \"div\", 10)(59, \"h3\", 11)(60, \"mat-icon\");\n        i0.ɵɵtext(61, \"school\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(62, \" Academic Information \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(63, \"div\", 12)(64, \"div\", 13)(65, \"mat-form-field\", 14)(66, \"mat-label\");\n        i0.ɵɵtext(67, \"Program *\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(68, \"mat-select\", 21)(69, \"mat-option\", 22);\n        i0.ɵɵtext(70, \"Select Program\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(71, TeacherFormComponent_mat_option_71_Template, 2, 3, \"mat-option\", 23);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(72, \"mat-icon\", 16);\n        i0.ɵɵtext(73, \"category\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(74, TeacherFormComponent_mat_error_74_Template, 2, 0, \"mat-error\", 17);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(75, \"div\", 13)(76, \"mat-form-field\", 14)(77, \"mat-label\");\n        i0.ɵɵtext(78, \"Department *\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(79, \"mat-select\", 24)(80, \"mat-option\", 22);\n        i0.ɵɵtext(81);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(82, TeacherFormComponent_mat_option_82_Template, 2, 2, \"mat-option\", 23);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(83, \"mat-icon\", 16);\n        i0.ɵɵtext(84, \"business\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(85, TeacherFormComponent_mat_error_85_Template, 2, 0, \"mat-error\", 17);\n        i0.ɵɵtemplate(86, TeacherFormComponent_mat_hint_86_Template, 2, 0, \"mat-hint\", 17);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(87, \"div\", 12)(88, \"div\", 13)(89, \"mat-form-field\", 14)(90, \"mat-label\");\n        i0.ɵɵtext(91, \"Designation *\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(92, \"mat-select\", 25)(93, \"mat-option\", 22);\n        i0.ɵɵtext(94, \"Select Designation\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(95, \"mat-option\", 26);\n        i0.ɵɵtext(96, \"Professor\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(97, \"mat-option\", 27);\n        i0.ɵɵtext(98, \"Associate Professor\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(99, \"mat-option\", 28);\n        i0.ɵɵtext(100, \"Assistant Professor\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(101, \"mat-option\", 29);\n        i0.ɵɵtext(102, \"Lecturer\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(103, \"mat-option\", 30);\n        i0.ɵɵtext(104, \"Senior Lecturer\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(105, \"mat-option\", 31);\n        i0.ɵɵtext(106, \"Visiting Faculty\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(107, \"mat-icon\", 16);\n        i0.ɵɵtext(108, \"work\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(109, TeacherFormComponent_mat_error_109_Template, 2, 0, \"mat-error\", 17);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(110, \"div\", 13)(111, \"mat-form-field\", 14)(112, \"mat-label\");\n        i0.ɵɵtext(113, \"Position\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(114, \"mat-select\", 32)(115, \"mat-option\", 22);\n        i0.ɵɵtext(116, \"Select Position\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(117, \"mat-option\", 33);\n        i0.ɵɵtext(118, \"Head of Department\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(119, \"mat-option\", 34);\n        i0.ɵɵtext(120, \"Program Coordinator\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(121, \"mat-option\", 35);\n        i0.ɵɵtext(122, \"Faculty Member\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(123, \"mat-option\", 36);\n        i0.ɵɵtext(124, \"Lab Instructor\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(125, \"mat-option\", 37);\n        i0.ɵɵtext(126, \"Research Supervisor\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(127, \"mat-icon\", 16);\n        i0.ɵɵtext(128, \"badge\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(129, \"div\", 10)(130, \"h3\", 11)(131, \"mat-icon\");\n        i0.ɵɵtext(132, \"security\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(133, \" Account Information \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(134, \"div\", 12)(135, \"div\", 13)(136, \"mat-form-field\", 14)(137, \"mat-label\");\n        i0.ɵɵtext(138, \"Password *\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(139, \"input\", 38);\n        i0.ɵɵelementStart(140, \"button\", 39);\n        i0.ɵɵlistener(\"click\", function TeacherFormComponent_Template_button_click_140_listener() {\n          return ctx.hidePassword = !ctx.hidePassword;\n        });\n        i0.ɵɵelementStart(141, \"mat-icon\");\n        i0.ɵɵtext(142);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(143, TeacherFormComponent_mat_error_143_Template, 3, 2, \"mat-error\", 17);\n        i0.ɵɵelementStart(144, \"mat-hint\");\n        i0.ɵɵtext(145, \"Minimum 6 characters required\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(146, \"div\", 13)(147, \"div\", 40)(148, \"mat-checkbox\", 41)(149, \"span\", 42)(150, \"mat-icon\");\n        i0.ɵɵtext(151, \"flight\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(152, \" Visiting Teacher \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(153, \"small\", 43);\n        i0.ɵɵtext(154, \" Check if this is a visiting or temporary faculty member \");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(155, \"div\", 44)(156, \"button\", 45)(157, \"mat-icon\");\n        i0.ɵɵtext(158, \"cancel\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(159, \" Cancel \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(160, \"button\", 46);\n        i0.ɵɵtemplate(161, TeacherFormComponent_mat_icon_161_Template, 2, 0, \"mat-icon\", 17);\n        i0.ɵɵtemplate(162, TeacherFormComponent_mat_spinner_162_Template, 1, 0, \"mat-spinner\", 47);\n        i0.ɵɵtext(163);\n        i0.ɵɵelementEnd()()()()()()();\n      }\n      if (rf & 2) {\n        let tmp_4_0;\n        let tmp_5_0;\n        let tmp_6_0;\n        let tmp_7_0;\n        let tmp_8_0;\n        let tmp_9_0;\n        let tmp_11_0;\n        let tmp_12_0;\n        let tmp_13_0;\n        let tmp_15_0;\n        let tmp_16_0;\n        let tmp_17_0;\n        let tmp_20_0;\n        i0.ɵɵadvance(7);\n        i0.ɵɵtextInterpolate(ctx.isEditing ? \"edit\" : \"person_add\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate1(\" \", ctx.isEditing ? \"Edit Teacher\" : \"Add New Teacher\", \" \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.isEditing ? \"Update teacher information\" : \"Create a new teacher account and assign to department\");\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"formGroup\", ctx.teacherForm);\n        i0.ɵɵadvance(14);\n        i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx.teacherForm.get(\"name\")) == null ? null : tmp_4_0.hasError(\"required\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (tmp_5_0 = ctx.teacherForm.get(\"name\")) == null ? null : tmp_5_0.hasError(\"minlength\"));\n        i0.ɵɵadvance(16);\n        i0.ɵɵproperty(\"ngIf\", (tmp_6_0 = ctx.teacherForm.get(\"contact\")) == null ? null : tmp_6_0.hasError(\"required\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (tmp_7_0 = ctx.teacherForm.get(\"contact\")) == null ? null : tmp_7_0.hasError(\"pattern\"));\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"ngIf\", (tmp_8_0 = ctx.teacherForm.get(\"email\")) == null ? null : tmp_8_0.hasError(\"required\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (tmp_9_0 = ctx.teacherForm.get(\"email\")) == null ? null : tmp_9_0.hasError(\"email\"));\n        i0.ɵɵadvance(14);\n        i0.ɵɵproperty(\"ngForOf\", ctx.programs);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", (tmp_11_0 = ctx.teacherForm.get(\"program\")) == null ? null : tmp_11_0.hasError(\"required\"));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"disabled\", !((tmp_12_0 = ctx.teacherForm.get(\"program\")) == null ? null : tmp_12_0.value));\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(!((tmp_13_0 = ctx.teacherForm.get(\"program\")) == null ? null : tmp_13_0.value) ? \"Select Program First\" : \"Select Department\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngForOf\", ctx.filteredDepartments);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", (tmp_15_0 = ctx.teacherForm.get(\"department\")) == null ? null : tmp_15_0.hasError(\"required\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !((tmp_16_0 = ctx.teacherForm.get(\"program\")) == null ? null : tmp_16_0.value));\n        i0.ɵɵadvance(23);\n        i0.ɵɵproperty(\"ngIf\", (tmp_17_0 = ctx.teacherForm.get(\"designation\")) == null ? null : tmp_17_0.hasError(\"required\"));\n        i0.ɵɵadvance(30);\n        i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_20_0 = ctx.teacherForm.get(\"password\")) == null ? null : tmp_20_0.invalid) && ((tmp_20_0 = ctx.teacherForm.get(\"password\")) == null ? null : tmp_20_0.touched));\n        i0.ɵɵadvance(17);\n        i0.ɵɵproperty(\"disabled\", !ctx.teacherForm.valid || ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate1(\" \", ctx.loading ? \"Saving...\" : ctx.isEditing ? \"Update Teacher\" : \"Save Teacher\", \" \");\n      }\n    },\n    dependencies: [i6.NgForOf, i6.NgIf, i3.RouterLink, i7.MatOption, i8.MatButton, i8.MatIconButton, i9.MatCard, i9.MatCardContent, i10.MatCheckbox, i11.MatIcon, i12.MatInput, i13.MatFormField, i13.MatLabel, i13.MatHint, i13.MatError, i13.MatSuffix, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.MinLengthValidator, i1.FormGroupDirective, i1.FormControlName, i14.MatProgressSpinner, i15.MatSelect, i16.MatTooltip],\n    styles: [\".teacher-form-container[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  min-height: 100vh;\\n  padding: 20px;\\n}\\n\\n\\n\\n.form-header[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n  padding: 20px 0;\\n}\\n\\n.form-title[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-size: 2rem;\\n  font-weight: 600;\\n  margin: 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n\\n.title-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  color: #3498db;\\n}\\n\\n.form-subtitle[_ngcontent-%COMP%] {\\n  color: #7f8c8d;\\n  margin: 5px 0 0 0;\\n  font-size: 1rem;\\n}\\n\\n\\n\\n.teacher-form-card[_ngcontent-%COMP%] {\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n  border-radius: 12px;\\n  border: none;\\n  overflow: hidden;\\n}\\n\\n.teacher-form[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n\\n\\n\\n.form-section[_ngcontent-%COMP%] {\\n  margin-bottom: 40px;\\n  padding: 20px;\\n  background-color: #ffffff;\\n  border-radius: 8px;\\n  border-left: 4px solid #3498db;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  margin-bottom: 20px;\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  padding-bottom: 10px;\\n  border-bottom: 2px solid #ecf0f1;\\n}\\n\\n.section-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #3498db;\\n  font-size: 1.5rem;\\n}\\n\\n\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 15px;\\n}\\n\\nmat-form-field[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\nmat-form-field[_ngcontent-%COMP%]   .mat-form-field-wrapper[_ngcontent-%COMP%] {\\n  padding-bottom: 0;\\n}\\n\\n\\n\\n.checkbox-section[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background-color: #f8f9fa;\\n  border-radius: 8px;\\n  border: 1px solid #e9ecef;\\n  margin-top: 10px;\\n}\\n\\n.checkbox-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-weight: 500;\\n  color: #495057;\\n}\\n\\n.checkbox-label[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  color: #6c757d;\\n}\\n\\n\\n\\n.form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  gap: 15px;\\n  padding: 30px 20px 20px;\\n  border-top: 1px solid #e9ecef;\\n  margin-top: 30px;\\n}\\n\\n.cancel-btn[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  border-color: #6c757d;\\n  min-width: 120px;\\n}\\n\\n.cancel-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #6c757d;\\n  color: white;\\n}\\n\\n.save-btn[_ngcontent-%COMP%] {\\n  min-width: 150px;\\n  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);\\n  border: none;\\n  box-shadow: 0 4px 6px rgba(52, 152, 219, 0.3);\\n  transition: all 0.3s ease;\\n}\\n\\n.save-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 12px rgba(52, 152, 219, 0.4);\\n}\\n\\n.save-btn[_ngcontent-%COMP%]:disabled {\\n  background: #bdc3c7;\\n  box-shadow: none;\\n  transform: none;\\n}\\n\\n.spinner[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .teacher-form-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n\\n  .form-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  .form-section[_ngcontent-%COMP%] {\\n    padding: 15px;\\n    margin-bottom: 20px;\\n  }\\n\\n  .form-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 10px;\\n  }\\n\\n  .cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "program_r15", "_id", "ɵɵadvance", "ɵɵtextInterpolate2", "name", "fullName", "department_r16", "ɵɵtextInterpolate1", "ɵɵtemplate", "TeacherFormComponent_mat_error_143_span_1_Template", "TeacherFormComponent_mat_error_143_span_2_Template", "tmp_0_0", "ctx_r12", "teacherForm", "get", "<PERSON><PERSON><PERSON><PERSON>", "tmp_1_0", "ɵɵelement", "TeacherFormComponent", "constructor", "fb", "userService", "router", "route", "programService", "departmentService", "programs", "departments", "filteredDepartments", "loading", "isEditing", "teacherId", "selectedFile", "hidePassword", "initializeForm", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "father_name", "email", "contact", "pattern", "program", "department", "designation", "position", "isVisiting", "password", "address", "setupFormSubscriptions", "valueChanges", "subscribe", "programId", "loadDepartmentsByProgram", "setValue", "ngOnInit", "snapshot", "paramMap", "loadPrograms", "loadTeacherData", "clearValidators", "updateValueAndValidity", "getAllPrograms", "next", "response", "success", "error", "console", "fire", "getDepartmentsByProgram", "getUserProfile", "teacher", "user", "patchValue", "onFileSelected", "event", "input", "target", "files", "length", "log", "onFileDrop", "preventDefault", "dataTransfer", "onDragOver", "onSave", "invalid", "markFormGroupTouched", "formData", "value", "teacher<PERSON><PERSON>", "role", "request", "updateUserProfile", "register", "message", "title", "text", "icon", "confirmButtonColor", "timer", "then", "navigate", "errorMessage", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "onCancel", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "UserService", "i3", "Router", "ActivatedRoute", "i4", "ProgramService", "i5", "DepartmentService", "_2", "selectors", "decls", "vars", "consts", "template", "TeacherFormComponent_Template", "rf", "ctx", "ɵɵlistener", "TeacherFormComponent_Template_form_ngSubmit_16_listener", "TeacherFormComponent_mat_error_30_Template", "TeacherFormComponent_mat_error_31_Template", "TeacherFormComponent_mat_error_47_Template", "TeacherFormComponent_mat_error_48_Template", "TeacherFormComponent_mat_error_56_Template", "TeacherFormComponent_mat_error_57_Template", "TeacherFormComponent_mat_option_71_Template", "TeacherFormComponent_mat_error_74_Template", "TeacherFormComponent_mat_option_82_Template", "TeacherFormComponent_mat_error_85_Template", "TeacherFormComponent_mat_hint_86_Template", "TeacherFormComponent_mat_error_109_Template", "TeacherFormComponent_Template_button_click_140_listener", "TeacherFormComponent_mat_error_143_Template", "TeacherFormComponent_mat_icon_161_Template", "TeacherFormComponent_mat_spinner_162_Template", "ɵɵtextInterpolate", "tmp_4_0", "tmp_5_0", "tmp_6_0", "tmp_7_0", "tmp_8_0", "tmp_9_0", "tmp_11_0", "tmp_12_0", "tmp_13_0", "tmp_15_0", "tmp_16_0", "tmp_17_0", "tmp_20_0", "touched", "valid"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\teacher\\teacher-form\\teacher-form.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\teacher\\teacher-form\\teacher-form.component.html"], "sourcesContent": ["import { Component, ElementRef, ViewChild, OnInit } from '@angular/core';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { UserService } from 'src/app/services/user.service';\r\nimport { ProgramService } from 'src/app/services/program.service';\r\nimport { DepartmentService } from 'src/app/services/department.service';\r\nimport { Program, Department } from 'src/app/models/user';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-teacher-form',\r\n  templateUrl: './teacher-form.component.html',\r\n  styleUrls: ['./teacher-form.component.css']\r\n})\r\nexport class TeacherFormComponent implements OnInit {\r\n  teacherForm!: FormGroup;\r\n  programs: Program[] = [];\r\n  departments: Department[] = [];\r\n  filteredDepartments: Department[] = [];\r\n  loading = false;\r\n  isEditing = false;\r\n  teacherId: string | null = null;\r\n  selectedFile: File | null = null;\r\n  hidePassword = true;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private userService: UserService,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private programService: ProgramService,\r\n    private departmentService: DepartmentService\r\n  ) {\r\n    this.initializeForm();\r\n  }\r\n\r\n  initializeForm(): void {\r\n    this.teacherForm = this.fb.group({\r\n      name: ['', [Validators.required, Validators.minLength(2)]],\r\n      father_name: [''],\r\n      email: ['', [Validators.required, Validators.email]],\r\n      contact: ['', [Validators.required, Validators.pattern(/^[0-9]{11}$/)]],\r\n      program: ['', Validators.required],\r\n      department: ['', Validators.required],\r\n      designation: ['', Validators.required],\r\n      position: [''],\r\n      isVisiting: [false],\r\n      password: ['', [Validators.required, Validators.minLength(6)]],\r\n      address: ['']\r\n    });\r\n\r\n    this.setupFormSubscriptions();\r\n  }\r\n\r\n  setupFormSubscriptions(): void {\r\n    this.teacherForm.get('program')?.valueChanges.subscribe(programId => {\r\n      if (programId) {\r\n        this.loadDepartmentsByProgram(programId);\r\n        this.teacherForm.get('department')?.setValue('');\r\n      } else {\r\n        this.filteredDepartments = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.teacherId = this.route.snapshot.paramMap.get('id');\r\n    this.isEditing = !!this.teacherId;\r\n\r\n    this.loadPrograms();\r\n\r\n    if (this.isEditing) {\r\n      this.loadTeacherData();\r\n      this.teacherForm.get('password')?.clearValidators();\r\n      this.teacherForm.get('password')?.updateValueAndValidity();\r\n    }\r\n  }\r\n\r\n  loadPrograms(): void {\r\n    this.programService.getAllPrograms(true).subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.programs = response.programs;\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading programs:', error);\r\n        Swal.fire('Error', 'Failed to load programs', 'error');\r\n      }\r\n    });\r\n  }\r\n\r\n  loadDepartmentsByProgram(programId: string): void {\r\n    this.departmentService.getDepartmentsByProgram(programId, true).subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.filteredDepartments = response.departments;\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading departments:', error);\r\n        this.filteredDepartments = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  loadTeacherData(): void {\r\n    if (this.teacherId) {\r\n      this.userService.getUserProfile(this.teacherId).subscribe({\r\n        next: (response) => {\r\n          if (response.success) {\r\n            const teacher = response.user;\r\n            this.teacherForm.patchValue({\r\n              name: teacher.name,\r\n              father_name: teacher.father_name,\r\n              email: teacher.email,\r\n              contact: teacher.contact,\r\n              program: teacher.program?._id,\r\n              department: teacher.department?._id,\r\n              designation: teacher.designation,\r\n              position: teacher.position,\r\n              isVisiting: teacher.isVisiting,\r\n              address: teacher.address\r\n            });\r\n\r\n            if (teacher.program?._id) {\r\n              this.loadDepartmentsByProgram(teacher.program._id);\r\n            }\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('Error loading teacher data:', error);\r\n          Swal.fire('Error', 'Failed to load teacher data', 'error');\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  // File selection handler\r\n  onFileSelected(event: Event) {\r\n    const input = event.target as HTMLInputElement;\r\n    if (input.files && input.files.length > 0) {\r\n      this.selectedFile = input.files[0];\r\n      console.log('File selected:', this.selectedFile);\r\n    }\r\n  }\r\n\r\n  // File drop handler\r\n  onFileDrop(event: DragEvent) {\r\n    event.preventDefault();\r\n    if (event.dataTransfer?.files && event.dataTransfer.files.length > 0) {\r\n      this.selectedFile = event.dataTransfer.files[0];\r\n      console.log('File dropped:', this.selectedFile);\r\n    }\r\n  }\r\n\r\n  // Prevent default drag behavior\r\n  onDragOver(event: DragEvent) {\r\n    event.preventDefault();\r\n  }\r\n\r\n  onSave(): void {\r\n    if (this.teacherForm.invalid) {\r\n      this.markFormGroupTouched();\r\n      Swal.fire('Validation Error', 'Please fill in all required fields correctly.', 'warning');\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n    const formData = this.teacherForm.value;\r\n\r\n    const teacherData = {\r\n      ...formData,\r\n      role: 'Teacher'\r\n    };\r\n\r\n    const request = this.isEditing\r\n      ? this.userService.updateUserProfile(this.teacherId!, teacherData)\r\n      : this.userService.register(teacherData);\r\n\r\n    request.subscribe({\r\n      next: (response) => {\r\n        this.loading = false;\r\n        const message = this.isEditing ? 'Teacher updated successfully!' : 'Teacher added successfully!';\r\n\r\n        Swal.fire({\r\n          title: 'Success!',\r\n          text: message,\r\n          icon: 'success',\r\n          confirmButtonColor: '#3085d6',\r\n          timer: 1500\r\n        }).then(() => {\r\n          this.router.navigate(['/dashboard/admin/teacher']);\r\n        });\r\n      },\r\n      error: (error) => {\r\n        this.loading = false;\r\n        console.error('Error saving teacher:', error);\r\n        const errorMessage = error.error?.message || 'Something went wrong while saving the teacher.';\r\n        Swal.fire('Error', errorMessage, 'error');\r\n      }\r\n    });\r\n  }\r\n\r\n  markFormGroupTouched(): void {\r\n    Object.keys(this.teacherForm.controls).forEach(key => {\r\n      const control = this.teacherForm.get(key);\r\n      control?.markAsTouched();\r\n    });\r\n  }\r\n\r\n  onCancel(): void {\r\n    this.router.navigate(['/dashboard/admin/teacher']);\r\n  }\r\n}\r\n", "<div class=\"teacher-form-container\">\r\n  <div class=\"container-fluid\">\r\n    <!-- Header Section -->\r\n    <div class=\"form-header\">\r\n      <div class=\"d-flex justify-content-between align-items-center\">\r\n        <div>\r\n          <h2 class=\"form-title\">\r\n            <mat-icon class=\"title-icon\">{{ isEditing ? 'edit' : 'person_add' }}</mat-icon>\r\n            {{ isEditing ? 'Edit Teacher' : 'Add New Teacher' }}\r\n          </h2>\r\n          <p class=\"form-subtitle\">{{ isEditing ? 'Update teacher information' : 'Create a new teacher account and assign to department' }}</p>\r\n        </div>\r\n        <button mat-icon-button routerLink=\"/dashboard/admin/teacher\" matTooltip=\"Back to Teachers List\">\r\n          <mat-icon>arrow_back</mat-icon>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Form Card -->\r\n    <mat-card class=\"teacher-form-card\">\r\n      <mat-card-content>\r\n        <form [formGroup]=\"teacherForm\" (ngSubmit)=\"onSave()\" class=\"teacher-form\">\r\n\r\n          <!-- Personal Information Section -->\r\n          <div class=\"form-section\">\r\n            <h3 class=\"section-title\">\r\n              <mat-icon>person</mat-icon>\r\n              Personal Information\r\n            </h3>\r\n\r\n            <div class=\"row\">\r\n              <div class=\"col-md-6\">\r\n                <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n                  <mat-label>Full Name *</mat-label>\r\n                  <input matInput\r\n                         formControlName=\"name\"\r\n                         placeholder=\"Enter teacher's full name\">\r\n                  <mat-icon matSuffix>person</mat-icon>\r\n                  <mat-error *ngIf=\"teacherForm.get('name')?.hasError('required')\">\r\n                    Full name is required\r\n                  </mat-error>\r\n                  <mat-error *ngIf=\"teacherForm.get('name')?.hasError('minlength')\">\r\n                    Name must be at least 2 characters long\r\n                  </mat-error>\r\n                </mat-form-field>\r\n              </div>\r\n\r\n              <div class=\"col-md-6\">\r\n                <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n                  <mat-label>Father's Name</mat-label>\r\n                  <input matInput\r\n                         formControlName=\"father_name\"\r\n                         placeholder=\"Enter father's name\">\r\n                  <mat-icon matSuffix>family_restroom</mat-icon>\r\n                </mat-form-field>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"row\">\r\n              <div class=\"col-md-6\">\r\n                <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n                  <mat-label>Contact Number *</mat-label>\r\n                  <input matInput\r\n                         formControlName=\"contact\"\r\n                         placeholder=\"Enter contact number (11 digits)\">\r\n                  <mat-icon matSuffix>phone</mat-icon>\r\n                  <mat-error *ngIf=\"teacherForm.get('contact')?.hasError('required')\">\r\n                    Contact number is required\r\n                  </mat-error>\r\n                  <mat-error *ngIf=\"teacherForm.get('contact')?.hasError('pattern')\">\r\n                    Please enter a valid 11-digit contact number\r\n                  </mat-error>\r\n                </mat-form-field>\r\n              </div>\r\n\r\n              <div class=\"col-md-6\">\r\n                <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n                  <mat-label>Email Address *</mat-label>\r\n                  <input matInput\r\n                         type=\"email\"\r\n                         formControlName=\"email\"\r\n                         placeholder=\"Enter email address\">\r\n                  <mat-icon matSuffix>email</mat-icon>\r\n                  <mat-error *ngIf=\"teacherForm.get('email')?.hasError('required')\">\r\n                    Email is required\r\n                  </mat-error>\r\n                  <mat-error *ngIf=\"teacherForm.get('email')?.hasError('email')\">\r\n                    Please enter a valid email address\r\n                  </mat-error>\r\n                </mat-form-field>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Academic Information Section -->\r\n          <div class=\"form-section\">\r\n            <h3 class=\"section-title\">\r\n              <mat-icon>school</mat-icon>\r\n              Academic Information\r\n            </h3>\r\n\r\n            <div class=\"row\">\r\n              <div class=\"col-md-6\">\r\n                <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n                  <mat-label>Program *</mat-label>\r\n                  <mat-select formControlName=\"program\">\r\n                    <mat-option value=\"\">Select Program</mat-option>\r\n                    <mat-option *ngFor=\"let program of programs\" [value]=\"program._id\">\r\n                      {{ program.name }} - {{ program.fullName }}\r\n                    </mat-option>\r\n                  </mat-select>\r\n                  <mat-icon matSuffix>category</mat-icon>\r\n                  <mat-error *ngIf=\"teacherForm.get('program')?.hasError('required')\">\r\n                    Program selection is required\r\n                  </mat-error>\r\n                </mat-form-field>\r\n              </div>\r\n\r\n              <div class=\"col-md-6\">\r\n                <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n                  <mat-label>Department *</mat-label>\r\n                  <mat-select formControlName=\"department\" [disabled]=\"!teacherForm.get('program')?.value\">\r\n                    <mat-option value=\"\">{{ !teacherForm.get('program')?.value ? 'Select Program First' : 'Select Department' }}</mat-option>\r\n                    <mat-option *ngFor=\"let department of filteredDepartments\" [value]=\"department._id\">\r\n                      {{ department.name }}\r\n                    </mat-option>\r\n                  </mat-select>\r\n                  <mat-icon matSuffix>business</mat-icon>\r\n                  <mat-error *ngIf=\"teacherForm.get('department')?.hasError('required')\">\r\n                    Department selection is required\r\n                  </mat-error>\r\n                  <mat-hint *ngIf=\"!teacherForm.get('program')?.value\">Please select a program first</mat-hint>\r\n                </mat-form-field>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"row\">\r\n              <div class=\"col-md-6\">\r\n                <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n                  <mat-label>Designation *</mat-label>\r\n                  <mat-select formControlName=\"designation\">\r\n                    <mat-option value=\"\">Select Designation</mat-option>\r\n                    <mat-option value=\"Professor\">Professor</mat-option>\r\n                    <mat-option value=\"Associate Professor\">Associate Professor</mat-option>\r\n                    <mat-option value=\"Assistant Professor\">Assistant Professor</mat-option>\r\n                    <mat-option value=\"Lecturer\">Lecturer</mat-option>\r\n                    <mat-option value=\"Senior Lecturer\">Senior Lecturer</mat-option>\r\n                    <mat-option value=\"Visiting Faculty\">Visiting Faculty</mat-option>\r\n                  </mat-select>\r\n                  <mat-icon matSuffix>work</mat-icon>\r\n                  <mat-error *ngIf=\"teacherForm.get('designation')?.hasError('required')\">\r\n                    Designation is required\r\n                  </mat-error>\r\n                </mat-form-field>\r\n              </div>\r\n\r\n              <div class=\"col-md-6\">\r\n                <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n                  <mat-label>Position</mat-label>\r\n                  <mat-select formControlName=\"position\">\r\n                    <mat-option value=\"\">Select Position</mat-option>\r\n                    <mat-option value=\"Head of Department\">Head of Department</mat-option>\r\n                    <mat-option value=\"Program Coordinator\">Program Coordinator</mat-option>\r\n                    <mat-option value=\"Faculty Member\">Faculty Member</mat-option>\r\n                    <mat-option value=\"Lab Instructor\">Lab Instructor</mat-option>\r\n                    <mat-option value=\"Research Supervisor\">Research Supervisor</mat-option>\r\n                  </mat-select>\r\n                  <mat-icon matSuffix>badge</mat-icon>\r\n                </mat-form-field>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Account Information Section -->\r\n          <div class=\"form-section\">\r\n            <h3 class=\"section-title\">\r\n              <mat-icon>security</mat-icon>\r\n              Account Information\r\n            </h3>\r\n\r\n            <div class=\"row\">\r\n              <div class=\"col-md-6\">\r\n                <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n                  <mat-label>Password *</mat-label>\r\n                  <input matInput\r\n                         [type]=\"hidePassword ? 'password' : 'text'\"\r\n                         formControlName=\"password\"\r\n                         placeholder=\"Enter password\"\r\n                         minlength=\"6\"\r\n                         required>\r\n                  <button mat-icon-button matSuffix (click)=\"hidePassword = !hidePassword\" type=\"button\">\r\n                    <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\r\n                  </button>\r\n                  <mat-error *ngIf=\"teacherForm.get('password')?.invalid && teacherForm.get('password')?.touched\">\r\n                    <span *ngIf=\"teacherForm.get('password')?.hasError('required')\">Password is required</span>\r\n                    <span *ngIf=\"teacherForm.get('password')?.hasError('minlength')\">Password must be at least 6 characters</span>\r\n                  </mat-error>\r\n                  <mat-hint>Minimum 6 characters required</mat-hint>\r\n                </mat-form-field>\r\n              </div>\r\n\r\n              <div class=\"col-md-6\">\r\n                <div class=\"checkbox-section\">\r\n                  <mat-checkbox name=\"is_visiting\"\r\n                                formControlName=\"isVisiting\"\r\n                                color=\"primary\">\r\n                    <span class=\"checkbox-label\">\r\n                      <mat-icon>flight</mat-icon>\r\n                      Visiting Teacher\r\n                    </span>\r\n                  </mat-checkbox>\r\n                  <small class=\"form-text text-muted\">\r\n                    Check if this is a visiting or temporary faculty member\r\n                  </small>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Form Actions -->\r\n          <div class=\"form-actions\">\r\n            <button mat-button\r\n                    type=\"button\"\r\n                    routerLink=\"/dashboard/admin/teacher\"\r\n                    class=\"cancel-btn\">\r\n              <mat-icon>cancel</mat-icon>\r\n              Cancel\r\n            </button>\r\n\r\n            <button mat-raised-button\r\n                    color=\"primary\"\r\n                    type=\"submit\"\r\n                    [disabled]=\"!teacherForm.valid || loading\"\r\n                    class=\"save-btn\">\r\n              <mat-icon *ngIf=\"!loading\">save</mat-icon>\r\n              <mat-spinner *ngIf=\"loading\" diameter=\"20\" class=\"spinner\"></mat-spinner>\r\n              {{ loading ? 'Saving...' : (isEditing ? 'Update Teacher' : 'Save Teacher') }}\r\n            </button>\r\n          </div>\r\n        </form>\r\n      </mat-card-content>\r\n    </mat-card>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAEA,SAAiCA,UAAU,QAAQ,gBAAgB;AAKnE,OAAOC,IAAI,MAAM,aAAa;;;;;;;;;;;;;;;;;;;;IC+BZC,EAAA,CAAAC,cAAA,gBAAiE;IAC/DD,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAAkE;IAChED,EAAA,CAAAE,MAAA,gDACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAuBZH,EAAA,CAAAC,cAAA,gBAAoE;IAClED,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAAmE;IACjED,EAAA,CAAAE,MAAA,qDACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAYZH,EAAA,CAAAC,cAAA,gBAAkE;IAChED,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAA+D;IAC7DD,EAAA,CAAAE,MAAA,2CACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAmBVH,EAAA,CAAAC,cAAA,qBAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFgCH,EAAA,CAAAI,UAAA,UAAAC,WAAA,CAAAC,GAAA,CAAqB;IAChEN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,WAAA,CAAAI,IAAA,SAAAJ,WAAA,CAAAK,QAAA,MACF;;;;;IAGFV,EAAA,CAAAC,cAAA,gBAAoE;IAClED,EAAA,CAAAE,MAAA,sCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IASVH,EAAA,CAAAC,cAAA,qBAAoF;IAClFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF8CH,EAAA,CAAAI,UAAA,UAAAO,cAAA,CAAAL,GAAA,CAAwB;IACjFN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAY,kBAAA,MAAAD,cAAA,CAAAF,IAAA,MACF;;;;;IAGFT,EAAA,CAAAC,cAAA,gBAAuE;IACrED,EAAA,CAAAE,MAAA,yCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,oCAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAmB7FH,EAAA,CAAAC,cAAA,gBAAwE;IACtED,EAAA,CAAAE,MAAA,gCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IA0CVH,EAAA,CAAAC,cAAA,WAAgE;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC3FH,EAAA,CAAAC,cAAA,WAAiE;IAAAD,EAAA,CAAAE,MAAA,6CAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFhHH,EAAA,CAAAC,cAAA,gBAAgG;IAC9FD,EAAA,CAAAa,UAAA,IAAAC,kDAAA,mBAA2F;IAC3Fd,EAAA,CAAAa,UAAA,IAAAE,kDAAA,mBAA8G;IAChHf,EAAA,CAAAG,YAAA,EAAY;;;;;;IAFHH,EAAA,CAAAO,SAAA,GAAuD;IAAvDP,EAAA,CAAAI,UAAA,UAAAY,OAAA,GAAAC,OAAA,CAAAC,WAAA,CAAAC,GAAA,+BAAAH,OAAA,CAAAI,QAAA,aAAuD;IACvDpB,EAAA,CAAAO,SAAA,GAAwD;IAAxDP,EAAA,CAAAI,UAAA,UAAAiB,OAAA,GAAAJ,OAAA,CAAAC,WAAA,CAAAC,GAAA,+BAAAE,OAAA,CAAAD,QAAA,cAAwD;;;;;IAuCrEpB,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAC1CH,EAAA,CAAAsB,SAAA,sBAAyE;;;AD7NvF,OAAM,MAAOC,oBAAoB;EAW/BC,YACUC,EAAe,EACfC,WAAwB,EACxBC,MAAc,EACdC,KAAqB,EACrBC,cAA8B,EAC9BC,iBAAoC;IALpC,KAAAL,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAf3B,KAAAC,QAAQ,GAAc,EAAE;IACxB,KAAAC,WAAW,GAAiB,EAAE;IAC9B,KAAAC,mBAAmB,GAAiB,EAAE;IACtC,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,SAAS,GAAkB,IAAI;IAC/B,KAAAC,YAAY,GAAgB,IAAI;IAChC,KAAAC,YAAY,GAAG,IAAI;IAUjB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAA,cAAcA,CAAA;IACZ,IAAI,CAACrB,WAAW,GAAG,IAAI,CAACO,EAAE,CAACe,KAAK,CAAC;MAC/B/B,IAAI,EAAE,CAAC,EAAE,EAAE,CAACX,UAAU,CAAC2C,QAAQ,EAAE3C,UAAU,CAAC4C,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1DC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC9C,UAAU,CAAC2C,QAAQ,EAAE3C,UAAU,CAAC8C,KAAK,CAAC,CAAC;MACpDC,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC/C,UAAU,CAAC2C,QAAQ,EAAE3C,UAAU,CAACgD,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;MACvEC,OAAO,EAAE,CAAC,EAAE,EAAEjD,UAAU,CAAC2C,QAAQ,CAAC;MAClCO,UAAU,EAAE,CAAC,EAAE,EAAElD,UAAU,CAAC2C,QAAQ,CAAC;MACrCQ,WAAW,EAAE,CAAC,EAAE,EAAEnD,UAAU,CAAC2C,QAAQ,CAAC;MACtCS,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,UAAU,EAAE,CAAC,KAAK,CAAC;MACnBC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACtD,UAAU,CAAC2C,QAAQ,EAAE3C,UAAU,CAAC4C,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DW,OAAO,EAAE,CAAC,EAAE;KACb,CAAC;IAEF,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEAA,sBAAsBA,CAAA;IACpB,IAAI,CAACpC,WAAW,CAACC,GAAG,CAAC,SAAS,CAAC,EAAEoC,YAAY,CAACC,SAAS,CAACC,SAAS,IAAG;MAClE,IAAIA,SAAS,EAAE;QACb,IAAI,CAACC,wBAAwB,CAACD,SAAS,CAAC;QACxC,IAAI,CAACvC,WAAW,CAACC,GAAG,CAAC,YAAY,CAAC,EAAEwC,QAAQ,CAAC,EAAE,CAAC;OACjD,MAAM;QACL,IAAI,CAAC1B,mBAAmB,GAAG,EAAE;;IAEjC,CAAC,CAAC;EACJ;EAEA2B,QAAQA,CAAA;IACN,IAAI,CAACxB,SAAS,GAAG,IAAI,CAACR,KAAK,CAACiC,QAAQ,CAACC,QAAQ,CAAC3C,GAAG,CAAC,IAAI,CAAC;IACvD,IAAI,CAACgB,SAAS,GAAG,CAAC,CAAC,IAAI,CAACC,SAAS;IAEjC,IAAI,CAAC2B,YAAY,EAAE;IAEnB,IAAI,IAAI,CAAC5B,SAAS,EAAE;MAClB,IAAI,CAAC6B,eAAe,EAAE;MACtB,IAAI,CAAC9C,WAAW,CAACC,GAAG,CAAC,UAAU,CAAC,EAAE8C,eAAe,EAAE;MACnD,IAAI,CAAC/C,WAAW,CAACC,GAAG,CAAC,UAAU,CAAC,EAAE+C,sBAAsB,EAAE;;EAE9D;EAEAH,YAAYA,CAAA;IACV,IAAI,CAAClC,cAAc,CAACsC,cAAc,CAAC,IAAI,CAAC,CAACX,SAAS,CAAC;MACjDY,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACvC,QAAQ,GAAGsC,QAAQ,CAACtC,QAAQ;;MAErC,CAAC;MACDwC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/CxE,IAAI,CAAC0E,IAAI,CAAC,OAAO,EAAE,yBAAyB,EAAE,OAAO,CAAC;MACxD;KACD,CAAC;EACJ;EAEAf,wBAAwBA,CAACD,SAAiB;IACxC,IAAI,CAAC3B,iBAAiB,CAAC4C,uBAAuB,CAACjB,SAAS,EAAE,IAAI,CAAC,CAACD,SAAS,CAAC;MACxEY,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACrC,mBAAmB,GAAGoC,QAAQ,CAACrC,WAAW;;MAEnD,CAAC;MACDuC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,IAAI,CAACtC,mBAAmB,GAAG,EAAE;MAC/B;KACD,CAAC;EACJ;EAEA+B,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC5B,SAAS,EAAE;MAClB,IAAI,CAACV,WAAW,CAACiD,cAAc,CAAC,IAAI,CAACvC,SAAS,CAAC,CAACoB,SAAS,CAAC;QACxDY,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;YACpB,MAAMM,OAAO,GAAGP,QAAQ,CAACQ,IAAI;YAC7B,IAAI,CAAC3D,WAAW,CAAC4D,UAAU,CAAC;cAC1BrE,IAAI,EAAEmE,OAAO,CAACnE,IAAI;cAClBkC,WAAW,EAAEiC,OAAO,CAACjC,WAAW;cAChCC,KAAK,EAAEgC,OAAO,CAAChC,KAAK;cACpBC,OAAO,EAAE+B,OAAO,CAAC/B,OAAO;cACxBE,OAAO,EAAE6B,OAAO,CAAC7B,OAAO,EAAEzC,GAAG;cAC7B0C,UAAU,EAAE4B,OAAO,CAAC5B,UAAU,EAAE1C,GAAG;cACnC2C,WAAW,EAAE2B,OAAO,CAAC3B,WAAW;cAChCC,QAAQ,EAAE0B,OAAO,CAAC1B,QAAQ;cAC1BC,UAAU,EAAEyB,OAAO,CAACzB,UAAU;cAC9BE,OAAO,EAAEuB,OAAO,CAACvB;aAClB,CAAC;YAEF,IAAIuB,OAAO,CAAC7B,OAAO,EAAEzC,GAAG,EAAE;cACxB,IAAI,CAACoD,wBAAwB,CAACkB,OAAO,CAAC7B,OAAO,CAACzC,GAAG,CAAC;;;QAGxD,CAAC;QACDiE,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;UACnDxE,IAAI,CAAC0E,IAAI,CAAC,OAAO,EAAE,6BAA6B,EAAE,OAAO,CAAC;QAC5D;OACD,CAAC;;EAEN;EAEA;EACAM,cAAcA,CAACC,KAAY;IACzB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,IAAID,KAAK,CAACE,KAAK,IAAIF,KAAK,CAACE,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MACzC,IAAI,CAAC/C,YAAY,GAAG4C,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC;MAClCX,OAAO,CAACa,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAChD,YAAY,CAAC;;EAEpD;EAEA;EACAiD,UAAUA,CAACN,KAAgB;IACzBA,KAAK,CAACO,cAAc,EAAE;IACtB,IAAIP,KAAK,CAACQ,YAAY,EAAEL,KAAK,IAAIH,KAAK,CAACQ,YAAY,CAACL,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MACpE,IAAI,CAAC/C,YAAY,GAAG2C,KAAK,CAACQ,YAAY,CAACL,KAAK,CAAC,CAAC,CAAC;MAC/CX,OAAO,CAACa,GAAG,CAAC,eAAe,EAAE,IAAI,CAAChD,YAAY,CAAC;;EAEnD;EAEA;EACAoD,UAAUA,CAACT,KAAgB;IACzBA,KAAK,CAACO,cAAc,EAAE;EACxB;EAEAG,MAAMA,CAAA;IACJ,IAAI,IAAI,CAACxE,WAAW,CAACyE,OAAO,EAAE;MAC5B,IAAI,CAACC,oBAAoB,EAAE;MAC3B7F,IAAI,CAAC0E,IAAI,CAAC,kBAAkB,EAAE,+CAA+C,EAAE,SAAS,CAAC;MACzF;;IAGF,IAAI,CAACvC,OAAO,GAAG,IAAI;IACnB,MAAM2D,QAAQ,GAAG,IAAI,CAAC3E,WAAW,CAAC4E,KAAK;IAEvC,MAAMC,WAAW,GAAG;MAClB,GAAGF,QAAQ;MACXG,IAAI,EAAE;KACP;IAED,MAAMC,OAAO,GAAG,IAAI,CAAC9D,SAAS,GAC1B,IAAI,CAACT,WAAW,CAACwE,iBAAiB,CAAC,IAAI,CAAC9D,SAAU,EAAE2D,WAAW,CAAC,GAChE,IAAI,CAACrE,WAAW,CAACyE,QAAQ,CAACJ,WAAW,CAAC;IAE1CE,OAAO,CAACzC,SAAS,CAAC;MAChBY,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACnC,OAAO,GAAG,KAAK;QACpB,MAAMkE,OAAO,GAAG,IAAI,CAACjE,SAAS,GAAG,+BAA+B,GAAG,6BAA6B;QAEhGpC,IAAI,CAAC0E,IAAI,CAAC;UACR4B,KAAK,EAAE,UAAU;UACjBC,IAAI,EAAEF,OAAO;UACbG,IAAI,EAAE,SAAS;UACfC,kBAAkB,EAAE,SAAS;UAC7BC,KAAK,EAAE;SACR,CAAC,CAACC,IAAI,CAAC,MAAK;UACX,IAAI,CAAC/E,MAAM,CAACgF,QAAQ,CAAC,CAAC,0BAA0B,CAAC,CAAC;QACpD,CAAC,CAAC;MACJ,CAAC;MACDpC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACrC,OAAO,GAAG,KAAK;QACpBsC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,MAAMqC,YAAY,GAAGrC,KAAK,CAACA,KAAK,EAAE6B,OAAO,IAAI,gDAAgD;QAC7FrG,IAAI,CAAC0E,IAAI,CAAC,OAAO,EAAEmC,YAAY,EAAE,OAAO,CAAC;MAC3C;KACD,CAAC;EACJ;EAEAhB,oBAAoBA,CAAA;IAClBiB,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC5F,WAAW,CAAC6F,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MACnD,MAAMC,OAAO,GAAG,IAAI,CAAChG,WAAW,CAACC,GAAG,CAAC8F,GAAG,CAAC;MACzCC,OAAO,EAAEC,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACzF,MAAM,CAACgF,QAAQ,CAAC,CAAC,0BAA0B,CAAC,CAAC;EACpD;EAAC,QAAAU,CAAA,G;qBAvMU9F,oBAAoB,EAAAvB,EAAA,CAAAsH,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxH,EAAA,CAAAsH,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA1H,EAAA,CAAAsH,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAA5H,EAAA,CAAAsH,iBAAA,CAAAK,EAAA,CAAAE,cAAA,GAAA7H,EAAA,CAAAsH,iBAAA,CAAAQ,EAAA,CAAAC,cAAA,GAAA/H,EAAA,CAAAsH,iBAAA,CAAAU,EAAA,CAAAC,iBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAApB3G,oBAAoB;IAAA4G,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCdjCzI,EAAA,CAAAC,cAAA,aAAoC;QAOKD,EAAA,CAAAE,MAAA,GAAuC;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC/EH,EAAA,CAAAE,MAAA,GACF;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAC,cAAA,WAAyB;QAAAD,EAAA,CAAAE,MAAA,IAAwG;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAEvIH,EAAA,CAAAC,cAAA,iBAAiG;QACrFD,EAAA,CAAAE,MAAA,kBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAMrCH,EAAA,CAAAC,cAAA,mBAAoC;QAEAD,EAAA,CAAA2I,UAAA,sBAAAC,wDAAA;UAAA,OAAYF,GAAA,CAAAhD,MAAA,EAAQ;QAAA,EAAC;QAGnD1F,EAAA,CAAAC,cAAA,eAA0B;QAEZD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC3BH,EAAA,CAAAE,MAAA,8BACF;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAELH,EAAA,CAAAC,cAAA,eAAiB;QAGAD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAClCH,EAAA,CAAAsB,SAAA,iBAE+C;QAC/CtB,EAAA,CAAAC,cAAA,oBAAoB;QAAAD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACrCH,EAAA,CAAAa,UAAA,KAAAgI,0CAAA,wBAEY;QACZ7I,EAAA,CAAAa,UAAA,KAAAiI,0CAAA,wBAEY;QACd9I,EAAA,CAAAG,YAAA,EAAiB;QAGnBH,EAAA,CAAAC,cAAA,eAAsB;QAEPD,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACpCH,EAAA,CAAAsB,SAAA,iBAEyC;QACzCtB,EAAA,CAAAC,cAAA,oBAAoB;QAAAD,EAAA,CAAAE,MAAA,uBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAKpDH,EAAA,CAAAC,cAAA,eAAiB;QAGAD,EAAA,CAAAE,MAAA,wBAAgB;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACvCH,EAAA,CAAAsB,SAAA,iBAEsD;QACtDtB,EAAA,CAAAC,cAAA,oBAAoB;QAAAD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACpCH,EAAA,CAAAa,UAAA,KAAAkI,0CAAA,wBAEY;QACZ/I,EAAA,CAAAa,UAAA,KAAAmI,0CAAA,wBAEY;QACdhJ,EAAA,CAAAG,YAAA,EAAiB;QAGnBH,EAAA,CAAAC,cAAA,eAAsB;QAEPD,EAAA,CAAAE,MAAA,uBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACtCH,EAAA,CAAAsB,SAAA,iBAGyC;QACzCtB,EAAA,CAAAC,cAAA,oBAAoB;QAAAD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACpCH,EAAA,CAAAa,UAAA,KAAAoI,0CAAA,wBAEY;QACZjJ,EAAA,CAAAa,UAAA,KAAAqI,0CAAA,wBAEY;QACdlJ,EAAA,CAAAG,YAAA,EAAiB;QAMvBH,EAAA,CAAAC,cAAA,eAA0B;QAEZD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC3BH,EAAA,CAAAE,MAAA,8BACF;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAELH,EAAA,CAAAC,cAAA,eAAiB;QAGAD,EAAA,CAAAE,MAAA,iBAAS;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAChCH,EAAA,CAAAC,cAAA,sBAAsC;QACfD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAa;QAChDH,EAAA,CAAAa,UAAA,KAAAsI,2CAAA,yBAEa;QACfnJ,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAAC,cAAA,oBAAoB;QAAAD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACvCH,EAAA,CAAAa,UAAA,KAAAuI,0CAAA,wBAEY;QACdpJ,EAAA,CAAAG,YAAA,EAAiB;QAGnBH,EAAA,CAAAC,cAAA,eAAsB;QAEPD,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACnCH,EAAA,CAAAC,cAAA,sBAAyF;QAClED,EAAA,CAAAE,MAAA,IAAuF;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACzHH,EAAA,CAAAa,UAAA,KAAAwI,2CAAA,yBAEa;QACfrJ,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAAC,cAAA,oBAAoB;QAAAD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACvCH,EAAA,CAAAa,UAAA,KAAAyI,0CAAA,wBAEY;QACZtJ,EAAA,CAAAa,UAAA,KAAA0I,yCAAA,uBAA6F;QAC/FvJ,EAAA,CAAAG,YAAA,EAAiB;QAIrBH,EAAA,CAAAC,cAAA,eAAiB;QAGAD,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACpCH,EAAA,CAAAC,cAAA,sBAA0C;QACnBD,EAAA,CAAAE,MAAA,0BAAkB;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACpDH,EAAA,CAAAC,cAAA,sBAA8B;QAAAD,EAAA,CAAAE,MAAA,iBAAS;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACpDH,EAAA,CAAAC,cAAA,sBAAwC;QAAAD,EAAA,CAAAE,MAAA,2BAAmB;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACxEH,EAAA,CAAAC,cAAA,sBAAwC;QAAAD,EAAA,CAAAE,MAAA,4BAAmB;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACxEH,EAAA,CAAAC,cAAA,uBAA6B;QAAAD,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAa;QAClDH,EAAA,CAAAC,cAAA,uBAAoC;QAAAD,EAAA,CAAAE,MAAA,wBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAa;QAChEH,EAAA,CAAAC,cAAA,uBAAqC;QAAAD,EAAA,CAAAE,MAAA,yBAAgB;QAAAF,EAAA,CAAAG,YAAA,EAAa;QAEpEH,EAAA,CAAAC,cAAA,qBAAoB;QAAAD,EAAA,CAAAE,MAAA,aAAI;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACnCH,EAAA,CAAAa,UAAA,MAAA2I,2CAAA,wBAEY;QACdxJ,EAAA,CAAAG,YAAA,EAAiB;QAGnBH,EAAA,CAAAC,cAAA,gBAAsB;QAEPD,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC/BH,EAAA,CAAAC,cAAA,uBAAuC;QAChBD,EAAA,CAAAE,MAAA,wBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACjDH,EAAA,CAAAC,cAAA,uBAAuC;QAAAD,EAAA,CAAAE,MAAA,2BAAkB;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACtEH,EAAA,CAAAC,cAAA,uBAAwC;QAAAD,EAAA,CAAAE,MAAA,4BAAmB;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACxEH,EAAA,CAAAC,cAAA,uBAAmC;QAAAD,EAAA,CAAAE,MAAA,uBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAa;QAC9DH,EAAA,CAAAC,cAAA,uBAAmC;QAAAD,EAAA,CAAAE,MAAA,uBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAa;QAC9DH,EAAA,CAAAC,cAAA,uBAAwC;QAAAD,EAAA,CAAAE,MAAA,4BAAmB;QAAAF,EAAA,CAAAG,YAAA,EAAa;QAE1EH,EAAA,CAAAC,cAAA,qBAAoB;QAAAD,EAAA,CAAAE,MAAA,cAAK;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAO5CH,EAAA,CAAAC,cAAA,gBAA0B;QAEZD,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC7BH,EAAA,CAAAE,MAAA,8BACF;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAELH,EAAA,CAAAC,cAAA,gBAAiB;QAGAD,EAAA,CAAAE,MAAA,mBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACjCH,EAAA,CAAAsB,SAAA,kBAKgB;QAChBtB,EAAA,CAAAC,cAAA,mBAAuF;QAArDD,EAAA,CAAA2I,UAAA,mBAAAc,wDAAA;UAAA,OAAAf,GAAA,CAAApG,YAAA,IAAAoG,GAAA,CAAApG,YAAA;QAAA,EAAsC;QACtEtC,EAAA,CAAAC,cAAA,iBAAU;QAAAD,EAAA,CAAAE,MAAA,KAAkD;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAEzEH,EAAA,CAAAa,UAAA,MAAA6I,2CAAA,wBAGY;QACZ1J,EAAA,CAAAC,cAAA,iBAAU;QAAAD,EAAA,CAAAE,MAAA,sCAA6B;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAItDH,EAAA,CAAAC,cAAA,gBAAsB;QAMJD,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC3BH,EAAA,CAAAE,MAAA,2BACF;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAETH,EAAA,CAAAC,cAAA,kBAAoC;QAClCD,EAAA,CAAAE,MAAA,kEACF;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAOhBH,EAAA,CAAAC,cAAA,gBAA0B;QAKZD,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC3BH,EAAA,CAAAE,MAAA,iBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAETH,EAAA,CAAAC,cAAA,mBAIyB;QACvBD,EAAA,CAAAa,UAAA,MAAA8I,0CAAA,uBAA0C;QAC1C3J,EAAA,CAAAa,UAAA,MAAA+I,6CAAA,0BAAyE;QACzE5J,EAAA,CAAAE,MAAA,KACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;;;;;;;;;QAtOoBH,EAAA,CAAAO,SAAA,GAAuC;QAAvCP,EAAA,CAAA6J,iBAAA,CAAAnB,GAAA,CAAAvG,SAAA,yBAAuC;QACpEnC,EAAA,CAAAO,SAAA,GACF;QADEP,EAAA,CAAAY,kBAAA,MAAA8H,GAAA,CAAAvG,SAAA,2CACF;QACyBnC,EAAA,CAAAO,SAAA,GAAwG;QAAxGP,EAAA,CAAA6J,iBAAA,CAAAnB,GAAA,CAAAvG,SAAA,0FAAwG;QAW7HnC,EAAA,CAAAO,SAAA,GAAyB;QAAzBP,EAAA,CAAAI,UAAA,cAAAsI,GAAA,CAAAxH,WAAA,CAAyB;QAiBTlB,EAAA,CAAAO,SAAA,IAAmD;QAAnDP,EAAA,CAAAI,UAAA,UAAA0J,OAAA,GAAApB,GAAA,CAAAxH,WAAA,CAAAC,GAAA,2BAAA2I,OAAA,CAAA1I,QAAA,aAAmD;QAGnDpB,EAAA,CAAAO,SAAA,GAAoD;QAApDP,EAAA,CAAAI,UAAA,UAAA2J,OAAA,GAAArB,GAAA,CAAAxH,WAAA,CAAAC,GAAA,2BAAA4I,OAAA,CAAA3I,QAAA,cAAoD;QAyBpDpB,EAAA,CAAAO,SAAA,IAAsD;QAAtDP,EAAA,CAAAI,UAAA,UAAA4J,OAAA,GAAAtB,GAAA,CAAAxH,WAAA,CAAAC,GAAA,8BAAA6I,OAAA,CAAA5I,QAAA,aAAsD;QAGtDpB,EAAA,CAAAO,SAAA,GAAqD;QAArDP,EAAA,CAAAI,UAAA,UAAA6J,OAAA,GAAAvB,GAAA,CAAAxH,WAAA,CAAAC,GAAA,8BAAA8I,OAAA,CAAA7I,QAAA,YAAqD;QAcrDpB,EAAA,CAAAO,SAAA,GAAoD;QAApDP,EAAA,CAAAI,UAAA,UAAA8J,OAAA,GAAAxB,GAAA,CAAAxH,WAAA,CAAAC,GAAA,4BAAA+I,OAAA,CAAA9I,QAAA,aAAoD;QAGpDpB,EAAA,CAAAO,SAAA,GAAiD;QAAjDP,EAAA,CAAAI,UAAA,UAAA+J,OAAA,GAAAzB,GAAA,CAAAxH,WAAA,CAAAC,GAAA,4BAAAgJ,OAAA,CAAA/I,QAAA,UAAiD;QAqB3BpB,EAAA,CAAAO,SAAA,IAAW;QAAXP,EAAA,CAAAI,UAAA,YAAAsI,GAAA,CAAA3G,QAAA,CAAW;QAKjC/B,EAAA,CAAAO,SAAA,GAAsD;QAAtDP,EAAA,CAAAI,UAAA,UAAAgK,QAAA,GAAA1B,GAAA,CAAAxH,WAAA,CAAAC,GAAA,8BAAAiJ,QAAA,CAAAhJ,QAAA,aAAsD;QASzBpB,EAAA,CAAAO,SAAA,GAA+C;QAA/CP,EAAA,CAAAI,UAAA,gBAAAiK,QAAA,GAAA3B,GAAA,CAAAxH,WAAA,CAAAC,GAAA,8BAAAkJ,QAAA,CAAAvE,KAAA,EAA+C;QACjE9F,EAAA,CAAAO,SAAA,GAAuF;QAAvFP,EAAA,CAAA6J,iBAAA,IAAAS,QAAA,GAAA5B,GAAA,CAAAxH,WAAA,CAAAC,GAAA,8BAAAmJ,QAAA,CAAAxE,KAAA,iDAAuF;QACzE9F,EAAA,CAAAO,SAAA,GAAsB;QAAtBP,EAAA,CAAAI,UAAA,YAAAsI,GAAA,CAAAzG,mBAAA,CAAsB;QAK/CjC,EAAA,CAAAO,SAAA,GAAyD;QAAzDP,EAAA,CAAAI,UAAA,UAAAmK,QAAA,GAAA7B,GAAA,CAAAxH,WAAA,CAAAC,GAAA,iCAAAoJ,QAAA,CAAAnJ,QAAA,aAAyD;QAG1DpB,EAAA,CAAAO,SAAA,GAAwC;QAAxCP,EAAA,CAAAI,UAAA,YAAAoK,QAAA,GAAA9B,GAAA,CAAAxH,WAAA,CAAAC,GAAA,8BAAAqJ,QAAA,CAAA1E,KAAA,EAAwC;QAmBvC9F,EAAA,CAAAO,SAAA,IAA0D;QAA1DP,EAAA,CAAAI,UAAA,UAAAqK,QAAA,GAAA/B,GAAA,CAAAxH,WAAA,CAAAC,GAAA,kCAAAsJ,QAAA,CAAArJ,QAAA,aAA0D;QAmC/DpB,EAAA,CAAAO,SAAA,IAA2C;QAA3CP,EAAA,CAAAI,UAAA,SAAAsI,GAAA,CAAApG,YAAA,uBAA2C;QAMtCtC,EAAA,CAAAO,SAAA,GAAkD;QAAlDP,EAAA,CAAA6J,iBAAA,CAAAnB,GAAA,CAAApG,YAAA,mCAAkD;QAElDtC,EAAA,CAAAO,SAAA,GAAkF;QAAlFP,EAAA,CAAAI,UAAA,WAAAsK,QAAA,GAAAhC,GAAA,CAAAxH,WAAA,CAAAC,GAAA,+BAAAuJ,QAAA,CAAA/E,OAAA,OAAA+E,QAAA,GAAAhC,GAAA,CAAAxH,WAAA,CAAAC,GAAA,+BAAAuJ,QAAA,CAAAC,OAAA,EAAkF;QAuC5F3K,EAAA,CAAAO,SAAA,IAA0C;QAA1CP,EAAA,CAAAI,UAAA,cAAAsI,GAAA,CAAAxH,WAAA,CAAA0J,KAAA,IAAAlC,GAAA,CAAAxG,OAAA,CAA0C;QAErClC,EAAA,CAAAO,SAAA,GAAc;QAAdP,EAAA,CAAAI,UAAA,UAAAsI,GAAA,CAAAxG,OAAA,CAAc;QACXlC,EAAA,CAAAO,SAAA,GAAa;QAAbP,EAAA,CAAAI,UAAA,SAAAsI,GAAA,CAAAxG,OAAA,CAAa;QAC3BlC,EAAA,CAAAO,SAAA,GACF;QADEP,EAAA,CAAAY,kBAAA,MAAA8H,GAAA,CAAAxG,OAAA,iBAAAwG,GAAA,CAAAvG,SAAA,0CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}