{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { TimetableListComponent } from './timetable-list/timetable-list.component';\nimport { TimetableCreateComponent } from './timetable-create/timetable-create.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: TimetableListComponent\n}, {\n  path: 'list',\n  component: TimetableListComponent\n}, {\n  path: 'create',\n  component: TimetableCreateComponent\n}, {\n  path: 'edit/:id',\n  component: TimetableCreateComponent\n}, {\n  path: 'view/:id',\n  component: TimetableCreateComponent\n}];\nexport class TimetableRoutingModule {\n  static #_ = this.ɵfac = function TimetableRoutingModule_Factory(t) {\n    return new (t || TimetableRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: TimetableRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild(routes), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(TimetableRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "TimetableListComponent", "TimetableCreateComponent", "routes", "path", "component", "TimetableRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\timetable\\timetable-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { TimetableListComponent } from './timetable-list/timetable-list.component';\r\nimport { TimetableCreateComponent } from './timetable-create/timetable-create.component';\r\n\r\nconst routes: Routes = [\r\n  { path: '', component: TimetableListComponent },\r\n  { path: 'list', component: TimetableListComponent },\r\n  { path: 'create', component: TimetableCreateComponent },\r\n  { path: 'edit/:id', component: TimetableCreateComponent },\r\n  { path: 'view/:id', component: TimetableCreateComponent }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class TimetableRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,wBAAwB,QAAQ,+CAA+C;;;AAExF,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEJ;AAAsB,CAAE,EAC/C;EAAEG,IAAI,EAAE,MAAM;EAAEC,SAAS,EAAEJ;AAAsB,CAAE,EACnD;EAAEG,IAAI,EAAE,QAAQ;EAAEC,SAAS,EAAEH;AAAwB,CAAE,EACvD;EAAEE,IAAI,EAAE,UAAU;EAAEC,SAAS,EAAEH;AAAwB,CAAE,EACzD;EAAEE,IAAI,EAAE,UAAU;EAAEC,SAAS,EAAEH;AAAwB,CAAE,CAC1D;AAMD,OAAM,MAAOI,sBAAsB;EAAA,QAAAC,CAAA,G;qBAAtBD,sBAAsB;EAAA;EAAA,QAAAE,EAAA,G;UAAtBF;EAAsB;EAAA,QAAAG,EAAA,G;cAHvBT,YAAY,CAACU,QAAQ,CAACP,MAAM,CAAC,EAC7BH,YAAY;EAAA;;;2EAEXM,sBAAsB;IAAAK,OAAA,GAAAC,EAAA,CAAAZ,YAAA;IAAAa,OAAA,GAFvBb,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}