{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction VerifyOtpComponent_p_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"We sent a 6-digit verification code to \");\n    i0.ɵɵelementStart(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.user.email);\n  }\n}\nfunction VerifyOtpComponent_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 36);\n  }\n}\nfunction VerifyOtpComponent_span_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 37);\n  }\n}\nfunction VerifyOtpComponent_span_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"Resend in \", ctx_r3.countdown, \"s\");\n  }\n}\nfunction VerifyOtpComponent_span_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Resend OTP\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction VerifyOtpComponent_span_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sending...\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let VerifyOtpComponent = /*#__PURE__*/(() => {\n  class VerifyOtpComponent {\n    constructor(fb, authService, router) {\n      this.fb = fb;\n      this.authService = authService;\n      this.router = router;\n      this.loading = false;\n      this.resendLoading = false;\n      this.user = null;\n      this.otpId = null;\n      this.countdown = 0;\n    }\n    ngOnInit() {\n      // Get user data from localStorage (set during forgot password)\n      const resetUser = localStorage.getItem('resetUser');\n      if (resetUser) {\n        this.user = JSON.parse(resetUser);\n      } else {\n        // If no user data, redirect to forgot password\n        Swal.fire({\n          title: 'Session Expired',\n          text: 'Please start the password reset process again.',\n          icon: 'warning',\n          confirmButtonColor: '#29578c'\n        }).then(() => {\n          this.router.navigate(['/auth/forgot-password']);\n        });\n        return;\n      }\n      this.initializeForm();\n      this.startCountdown();\n    }\n    ngOnDestroy() {\n      if (this.countdownInterval) {\n        clearInterval(this.countdownInterval);\n      }\n    }\n    initializeForm() {\n      this.otpForm = this.fb.group({\n        digit1: ['', [Validators.required, Validators.pattern(/^\\d$/)]],\n        digit2: ['', [Validators.required, Validators.pattern(/^\\d$/)]],\n        digit3: ['', [Validators.required, Validators.pattern(/^\\d$/)]],\n        digit4: ['', [Validators.required, Validators.pattern(/^\\d$/)]],\n        digit5: ['', [Validators.required, Validators.pattern(/^\\d$/)]],\n        digit6: ['', [Validators.required, Validators.pattern(/^\\d$/)]]\n      });\n    }\n    startCountdown() {\n      this.countdown = 60; // 60 seconds countdown\n      this.countdownInterval = setInterval(() => {\n        this.countdown--;\n        if (this.countdown <= 0) {\n          clearInterval(this.countdownInterval);\n        }\n      }, 1000);\n    }\n    onDigitInput(event, nextInput) {\n      const input = event.target;\n      const value = input.value;\n      if (value && /^\\d$/.test(value)) {\n        // Move to next input\n        const nextElement = document.getElementById(nextInput);\n        if (nextElement) {\n          nextElement.focus();\n        }\n      }\n    }\n    onDigitKeydown(event, prevInput) {\n      const input = event.target;\n      if (event.key === 'Backspace' && !input.value && prevInput) {\n        // Move to previous input on backspace\n        const prevElement = document.getElementById(prevInput);\n        if (prevElement) {\n          prevElement.focus();\n        }\n      }\n    }\n    onPaste(event) {\n      event.preventDefault();\n      const pastedData = event.clipboardData?.getData('text') || '';\n      if (/^\\d{6}$/.test(pastedData)) {\n        // Valid 6-digit OTP pasted\n        const digits = pastedData.split('');\n        this.otpForm.patchValue({\n          digit1: digits[0],\n          digit2: digits[1],\n          digit3: digits[2],\n          digit4: digits[3],\n          digit5: digits[4],\n          digit6: digits[5]\n        });\n        // Focus on last input\n        const lastInput = document.getElementById('digit6');\n        if (lastInput) {\n          lastInput.focus();\n        }\n      }\n    }\n    getOtpValue() {\n      const formValue = this.otpForm.value;\n      return `${formValue.digit1}${formValue.digit2}${formValue.digit3}${formValue.digit4}${formValue.digit5}${formValue.digit6}`;\n    }\n    verifyOtp() {\n      if (this.otpForm.valid && this.user) {\n        this.loading = true;\n        const otp = this.getOtpValue();\n        this.authService.verifyOtp(this.user._id, otp).subscribe({\n          next: response => {\n            this.loading = false;\n            if (response.success) {\n              // Store verified user data for password reset\n              localStorage.setItem('verifiedUser', JSON.stringify(this.user));\n              Swal.fire({\n                title: 'OTP Verified!',\n                text: 'Your OTP has been successfully verified. You can now reset your password.',\n                icon: 'success',\n                confirmButtonText: 'Reset Password',\n                confirmButtonColor: '#29578c'\n              }).then(result => {\n                if (result.isConfirmed) {\n                  this.router.navigate(['/auth/reset-password']);\n                }\n              });\n            }\n          },\n          error: error => {\n            this.loading = false;\n            console.error('OTP verification error:', error);\n            const errorMessage = error.error?.error || error.error?.message || 'Invalid OTP. Please try again.';\n            Swal.fire({\n              title: 'Verification Failed',\n              text: errorMessage,\n              icon: 'error',\n              confirmButtonColor: '#29578c'\n            });\n            // Clear the form on error\n            this.otpForm.reset();\n            const firstInput = document.getElementById('digit1');\n            if (firstInput) {\n              firstInput.focus();\n            }\n          }\n        });\n      } else {\n        Swal.fire({\n          title: 'Invalid OTP',\n          text: 'Please enter all 6 digits of the OTP.',\n          icon: 'warning',\n          confirmButtonColor: '#29578c'\n        });\n      }\n    }\n    resendOtp() {\n      if (!this.user || this.countdown > 0) return;\n      this.resendLoading = true;\n      const resendData = {\n        userId: this.user._id,\n        ...(this.otpId && {\n          otpId: this.otpId\n        })\n      };\n      this.authService.resendOtp(resendData).subscribe({\n        next: response => {\n          this.resendLoading = false;\n          if (response.success) {\n            if (response.otpId) {\n              this.otpId = response.otpId;\n            }\n            Swal.fire({\n              title: 'OTP Resent!',\n              text: 'A new OTP has been sent to your email.',\n              icon: 'success',\n              confirmButtonColor: '#29578c'\n            });\n            // Restart countdown\n            this.startCountdown();\n            // Clear the form\n            this.otpForm.reset();\n            const firstInput = document.getElementById('digit1');\n            if (firstInput) {\n              firstInput.focus();\n            }\n          }\n        },\n        error: error => {\n          this.resendLoading = false;\n          console.error('Resend OTP error:', error);\n          const errorMessage = error.error?.message || 'Failed to resend OTP. Please try again.';\n          Swal.fire({\n            title: 'Resend Failed',\n            text: errorMessage,\n            icon: 'error',\n            confirmButtonColor: '#29578c'\n          });\n        }\n      });\n    }\n    goBack() {\n      this.router.navigate(['/auth/forgot-password']);\n    }\n    static #_ = this.ɵfac = function VerifyOtpComponent_Factory(t) {\n      return new (t || VerifyOtpComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: VerifyOtpComponent,\n      selectors: [[\"app-verify-otp\"]],\n      decls: 53,\n      vars: 22,\n      consts: [[1, \"container-fluid\"], [1, \"row\", \"vh-100\"], [1, \"col-lg-5\", \"d-flex\", \"flex-column\", \"justify-content-center\", \"align-items-center\", \"p-3\", \"bg-white\"], [1, \"w-100\", 2, \"max-width\", \"400px\"], [1, \"text-center\"], [1, \"mb-4\"], [\"src\", \"../../../assets/images/logo.jpeg\"], [1, \"d-flex\", \"justify-content-center\", \"mt-5\", \"mb-2\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"fa-envelope\", \"key\"], [4, \"ngIf\"], [1, \"text-muted\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"otp-container\", \"mb-4\"], [\"type\", \"text\", \"id\", \"digit1\", \"formControlName\", \"digit1\", \"maxlength\", \"1\", 1, \"otp-input\", 3, \"input\", \"keydown\", \"paste\"], [\"type\", \"text\", \"id\", \"digit2\", \"formControlName\", \"digit2\", \"maxlength\", \"1\", 1, \"otp-input\", 3, \"input\", \"keydown\"], [\"type\", \"text\", \"id\", \"digit3\", \"formControlName\", \"digit3\", \"maxlength\", \"1\", 1, \"otp-input\", 3, \"input\", \"keydown\"], [\"type\", \"text\", \"id\", \"digit4\", \"formControlName\", \"digit4\", \"maxlength\", \"1\", 1, \"otp-input\", 3, \"input\", \"keydown\"], [\"type\", \"text\", \"id\", \"digit5\", \"formControlName\", \"digit5\", \"maxlength\", \"1\", 1, \"otp-input\", 3, \"input\", \"keydown\"], [\"type\", \"text\", \"id\", \"digit6\", \"formControlName\", \"digit6\", \"maxlength\", \"1\", 1, \"otp-input\", 3, \"input\", \"keydown\"], [1, \"d-grid\", \"gap-2\"], [\"type\", \"submit\", 1, \"btn\", \"submit\", 3, \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", \"role\", \"status\", \"aria-hidden\", \"true\", 4, \"ngIf\"], [1, \"text-center\", \"mt-4\"], [1, \"mb-2\"], [\"type\", \"button\", 1, \"btn\", \"resend\", 3, \"disabled\", \"click\"], [\"class\", \"spinner-border spinner-border-sm me-1\", \"role\", \"status\", \"aria-hidden\", \"true\", 4, \"ngIf\"], [1, \"mt-4\", \"text-center\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"fa-arrow-left\"], [2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"col-lg-7\", \"d-none\", \"d-lg-flex\", \"flex-column\", \"align-items-start\", \"bg-light\", \"position-relative\"], [1, \"text-start\", \"p-5\", \"w-100\"], [1, \"blockquote\"], [1, \"blockquote-footer\"], [\"title\", \"Source Title\"], [1, \"image-container\"], [\"src\", \"../../../assets/images/background.jpeg\", \"alt\", \"College background\", 1, \"img-fluid\", \"position-absolute\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-1\"]],\n      template: function VerifyOtpComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"h2\", 5);\n          i0.ɵɵelement(6, \"img\", 6);\n          i0.ɵɵtext(7, \" GPGC (Swabi)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 7);\n          i0.ɵɵelement(9, \"i\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"h1\", 5);\n          i0.ɵɵtext(11, \"Enter Verification Code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, VerifyOtpComponent_p_12_Template, 4, 1, \"p\", 9);\n          i0.ɵɵelementStart(13, \"p\", 10);\n          i0.ɵɵtext(14, \"Please enter the code below to verify your identity.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"form\", 11);\n          i0.ɵɵlistener(\"ngSubmit\", function VerifyOtpComponent_Template_form_ngSubmit_15_listener() {\n            return ctx.verifyOtp();\n          });\n          i0.ɵɵelementStart(16, \"div\", 12)(17, \"input\", 13);\n          i0.ɵɵlistener(\"input\", function VerifyOtpComponent_Template_input_input_17_listener($event) {\n            return ctx.onDigitInput($event, \"digit2\");\n          })(\"keydown\", function VerifyOtpComponent_Template_input_keydown_17_listener($event) {\n            return ctx.onDigitKeydown($event, \"\");\n          })(\"paste\", function VerifyOtpComponent_Template_input_paste_17_listener($event) {\n            return ctx.onPaste($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"input\", 14);\n          i0.ɵɵlistener(\"input\", function VerifyOtpComponent_Template_input_input_18_listener($event) {\n            return ctx.onDigitInput($event, \"digit3\");\n          })(\"keydown\", function VerifyOtpComponent_Template_input_keydown_18_listener($event) {\n            return ctx.onDigitKeydown($event, \"digit1\");\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"input\", 15);\n          i0.ɵɵlistener(\"input\", function VerifyOtpComponent_Template_input_input_19_listener($event) {\n            return ctx.onDigitInput($event, \"digit4\");\n          })(\"keydown\", function VerifyOtpComponent_Template_input_keydown_19_listener($event) {\n            return ctx.onDigitKeydown($event, \"digit2\");\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"input\", 16);\n          i0.ɵɵlistener(\"input\", function VerifyOtpComponent_Template_input_input_20_listener($event) {\n            return ctx.onDigitInput($event, \"digit5\");\n          })(\"keydown\", function VerifyOtpComponent_Template_input_keydown_20_listener($event) {\n            return ctx.onDigitKeydown($event, \"digit3\");\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"input\", 17);\n          i0.ɵɵlistener(\"input\", function VerifyOtpComponent_Template_input_input_21_listener($event) {\n            return ctx.onDigitInput($event, \"digit6\");\n          })(\"keydown\", function VerifyOtpComponent_Template_input_keydown_21_listener($event) {\n            return ctx.onDigitKeydown($event, \"digit4\");\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"input\", 18);\n          i0.ɵɵlistener(\"input\", function VerifyOtpComponent_Template_input_input_22_listener($event) {\n            return ctx.onDigitInput($event, \"\");\n          })(\"keydown\", function VerifyOtpComponent_Template_input_keydown_22_listener($event) {\n            return ctx.onDigitKeydown($event, \"digit5\");\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"div\", 19)(24, \"button\", 20);\n          i0.ɵɵtemplate(25, VerifyOtpComponent_span_25_Template, 1, 0, \"span\", 21);\n          i0.ɵɵtext(26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 22)(28, \"p\", 23);\n          i0.ɵɵtext(29, \"Didn't receive the code?\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"button\", 24);\n          i0.ɵɵlistener(\"click\", function VerifyOtpComponent_Template_button_click_30_listener() {\n            return ctx.resendOtp();\n          });\n          i0.ɵɵtemplate(31, VerifyOtpComponent_span_31_Template, 1, 0, \"span\", 25);\n          i0.ɵɵtemplate(32, VerifyOtpComponent_span_32_Template, 2, 1, \"span\", 9);\n          i0.ɵɵtemplate(33, VerifyOtpComponent_span_33_Template, 2, 0, \"span\", 9);\n          i0.ɵɵtemplate(34, VerifyOtpComponent_span_34_Template, 2, 0, \"span\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"p\", 26);\n          i0.ɵɵelement(36, \"i\", 27);\n          i0.ɵɵtext(37, \" \\u00A0 \");\n          i0.ɵɵelementStart(38, \"a\", 28);\n          i0.ɵɵlistener(\"click\", function VerifyOtpComponent_Template_a_click_38_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵtext(39, \"Back to forgot password\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(40, \"div\", 29)(41, \"div\", 30)(42, \"blockquote\", 31)(43, \"h2\", 5);\n          i0.ɵɵtext(44, \"College management system\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"p\", 5);\n          i0.ɵɵtext(46, \"Secure password reset process\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"footer\", 32);\n          i0.ɵɵtext(48, \"GPGC SWABI \");\n          i0.ɵɵelementStart(49, \"cite\", 33);\n          i0.ɵɵtext(50, \"Government Post Graduate College\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(51, \"div\", 34);\n          i0.ɵɵelement(52, \"img\", 35);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          let tmp_2_0;\n          let tmp_3_0;\n          let tmp_4_0;\n          let tmp_5_0;\n          let tmp_6_0;\n          let tmp_7_0;\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngIf\", ctx.user);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"formGroup\", ctx.otpForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_2_0 = ctx.otpForm.get(\"digit1\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.otpForm.get(\"digit1\")) == null ? null : tmp_2_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_3_0 = ctx.otpForm.get(\"digit2\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.otpForm.get(\"digit2\")) == null ? null : tmp_3_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_4_0 = ctx.otpForm.get(\"digit3\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.otpForm.get(\"digit3\")) == null ? null : tmp_4_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_5_0 = ctx.otpForm.get(\"digit4\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.otpForm.get(\"digit4\")) == null ? null : tmp_5_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_6_0 = ctx.otpForm.get(\"digit5\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx.otpForm.get(\"digit5\")) == null ? null : tmp_6_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_7_0 = ctx.otpForm.get(\"digit6\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx.otpForm.get(\"digit6\")) == null ? null : tmp_7_0.touched));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.loading || ctx.otpForm.invalid);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.loading ? \"Verifying...\" : \"Verify OTP\", \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"disabled\", ctx.resendLoading || ctx.countdown > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.resendLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.countdown > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.countdown <= 0 && !ctx.resendLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.resendLoading);\n        }\n      },\n      dependencies: [i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MaxLengthValidator, i1.FormGroupDirective, i1.FormControlName],\n      styles: [\".container-fluid[_ngcontent-%COMP%]{height:100vh;overflow:hidden}.key[_ngcontent-%COMP%]{font-size:3rem;color:#29578c;margin-bottom:1rem}h1[_ngcontent-%COMP%]{color:#29578c;font-weight:600;font-size:2rem}h2[_ngcontent-%COMP%]{color:#29578c;font-weight:500}p[_ngcontent-%COMP%]{color:#6c757d;margin-bottom:1rem}.text-muted[_ngcontent-%COMP%]{font-size:.9rem}.otp-container[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:10px;margin:2rem 0}.otp-input[_ngcontent-%COMP%]{width:50px;height:50px;text-align:center;font-size:1.5rem;font-weight:600;border:2px solid #ced4da;border-radius:8px;background-color:#f8f9fa;transition:all .3s ease}.otp-input[_ngcontent-%COMP%]:focus{outline:none;border-color:#29578c;background-color:#fff;box-shadow:0 0 0 .2rem #29578c40}.otp-input.is-invalid[_ngcontent-%COMP%]{border-color:#dc3545;background-color:#fff5f5}.otp-input[_ngcontent-%COMP%]:valid{border-color:#28a745;background-color:#f0fff4}.btn[_ngcontent-%COMP%]{padding:12px 24px;border-radius:6px;font-weight:500;transition:all .3s ease;border:none;cursor:pointer}.submit[_ngcontent-%COMP%]{background-color:#29578c;color:#fff;font-size:1rem}.submit[_ngcontent-%COMP%]:hover:not(:disabled){background-color:#1e3f63;transform:translateY(-1px)}.submit[_ngcontent-%COMP%]:disabled{background-color:#6c757d;cursor:not-allowed;transform:none}.resend[_ngcontent-%COMP%]{background-color:transparent;color:#29578c;border:2px solid #29578c;padding:8px 16px;font-size:.9rem}.resend[_ngcontent-%COMP%]:hover:not(:disabled){background-color:#29578c;color:#fff}.resend[_ngcontent-%COMP%]:disabled{background-color:transparent;color:#6c757d;border-color:#6c757d;cursor:not-allowed}.spinner-border-sm[_ngcontent-%COMP%]{width:1rem;height:1rem}a[_ngcontent-%COMP%]{color:#29578c;text-decoration:none;font-weight:500}a[_ngcontent-%COMP%]:hover{color:#1e3f63;text-decoration:underline}.image-container[_ngcontent-%COMP%]{position:absolute;bottom:0;right:0;width:100%;height:60%;overflow:hidden}.image-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;opacity:.8}.blockquote[_ngcontent-%COMP%]{border-left:4px solid #29578c;padding-left:1rem}.blockquote[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin-bottom:1rem}.blockquote-footer[_ngcontent-%COMP%]{color:#6c757d;font-size:.9rem}@media (max-width: 768px){.otp-container[_ngcontent-%COMP%]{gap:8px}.otp-input[_ngcontent-%COMP%]{width:40px;height:40px;font-size:1.2rem}h1[_ngcontent-%COMP%]{font-size:1.5rem}.key[_ngcontent-%COMP%]{font-size:2.5rem}}@media (max-width: 480px){.otp-container[_ngcontent-%COMP%]{gap:5px}.otp-input[_ngcontent-%COMP%]{width:35px;height:35px;font-size:1rem}.container-fluid[_ngcontent-%COMP%]{padding:10px}}.otp-input.is-invalid[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_shake .5s ease-in-out}@keyframes _ngcontent-%COMP%_shake{0%,to{transform:translate(0)}25%{transform:translate(-5px)}75%{transform:translate(5px)}}.otp-input[_ngcontent-%COMP%]:valid:not(:placeholder-shown){animation:_ngcontent-%COMP%_pulse .3s ease-in-out}@keyframes _ngcontent-%COMP%_pulse{0%{transform:scale(1)}50%{transform:scale(1.05)}to{transform:scale(1)}}\"]\n    });\n  }\n  return VerifyOtpComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}