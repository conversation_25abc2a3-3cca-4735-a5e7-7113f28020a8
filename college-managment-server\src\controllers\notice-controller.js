const Notice = require('../models/notice');
const User = require('../models/userModel');

// Create a new notice
const createNotice = async (req, res) => {
    try {
        const {
            title,
            content,
            author,
            category,
            priority,
            targetAudience,
            targetPrograms,
            targetDepartments,
            targetClasses,
            targetSemesters,
            publishDate,
            expiryDate,
            isPublished,
            isPinned
        } = req.body;

        // Validate required fields
        if (!title || !content || !author) {
            return res.status(400).json({
                success: false,
                message: 'Title, content, and author are required'
            });
        }

        // Get author details
        const authorUser = await User.findById(author);
        if (!authorUser) {
            return res.status(404).json({
                success: false,
                message: 'Author not found'
            });
        }

        const notice = new Notice({
            title,
            content,
            author,
            authorRole: authorUser.role,
            category: category || 'General',
            priority: priority || 'Medium',
            targetAudience: targetAudience || ['All'],
            targetPrograms: targetPrograms || [],
            targetDepartments: targetDepartments || [],
            targetClasses: targetClasses || [],
            targetSemesters: targetSemesters || [],
            publishDate: publishDate || new Date(),
            expiryDate,
            isPublished: isPublished || false,
            isPinned: isPinned || false
        });

        await notice.save();
        await notice.populate([
            { path: 'author', select: 'name email role' },
            { path: 'targetPrograms', select: 'name' },
            { path: 'targetDepartments', select: 'name' },
            { path: 'targetClasses', select: 'className section' }
        ]);

        res.status(201).json({
            success: true,
            message: 'Notice created successfully',
            notice
        });
    } catch (error) {
        console.error('Error creating notice:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

// Get all notices with filtering and pagination
const getAllNotices = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            category,
            priority,
            author,
            targetAudience,
            isPublished,
            isPinned,
            sortBy = 'publishDate',
            sortOrder = 'desc'
        } = req.query;

        // Build filter object
        const filter = { isActive: true };
        if (category) filter.category = category;
        if (priority) filter.priority = priority;
        if (author) filter.author = author;
        if (targetAudience) filter.targetAudience = { $in: [targetAudience] };
        if (isPublished !== undefined) filter.isPublished = isPublished === 'true';
        if (isPinned !== undefined) filter.isPinned = isPinned === 'true';

        // Only show published notices for non-admin users
        if (!req.query.includeUnpublished) {
            filter.isPublished = true;
            filter.publishDate = { $lte: new Date() };
        }

        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sortOptions = {};
        sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

        const notices = await Notice.find(filter)
            .populate('author', 'name email role')
            .populate('targetPrograms', 'name')
            .populate('targetDepartments', 'name')
            .populate('targetClasses', 'className section')
            .sort(sortOptions)
            .skip(skip)
            .limit(parseInt(limit));

        const totalNotices = await Notice.countDocuments(filter);
        const totalPages = Math.ceil(totalNotices / parseInt(limit));

        res.status(200).json({
            success: true,
            notices,
            pagination: {
                currentPage: parseInt(page),
                totalPages,
                totalNotices,
                hasNext: parseInt(page) < totalPages,
                hasPrev: parseInt(page) > 1
            }
        });
    } catch (error) {
        console.error('Error fetching notices:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

// Get notice by ID
const getNoticeById = async (req, res) => {
    try {
        const { id } = req.params;

        const notice = await Notice.findById(id)
            .populate('author', 'name email role')
            .populate('targetPrograms', 'name')
            .populate('targetDepartments', 'name')
            .populate('targetClasses', 'className section')
            .populate('readBy.user', 'name email role');

        if (!notice) {
            return res.status(404).json({
                success: false,
                message: 'Notice not found'
            });
        }

        // Increment views
        notice.views += 1;
        await notice.save();

        res.status(200).json({
            success: true,
            notice
        });
    } catch (error) {
        console.error('Error fetching notice:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

// Update notice
const updateNotice = async (req, res) => {
    try {
        const { id } = req.params;
        const updateData = req.body;

        const notice = await Notice.findByIdAndUpdate(
            id,
            updateData,
            { new: true, runValidators: true }
        ).populate([
            { path: 'author', select: 'name email role' },
            { path: 'targetPrograms', select: 'name' },
            { path: 'targetDepartments', select: 'name' },
            { path: 'targetClasses', select: 'className section' }
        ]);

        if (!notice) {
            return res.status(404).json({
                success: false,
                message: 'Notice not found'
            });
        }

        res.status(200).json({
            success: true,
            message: 'Notice updated successfully',
            notice
        });
    } catch (error) {
        console.error('Error updating notice:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

// Mark notice as read
const markAsRead = async (req, res) => {
    try {
        const { id } = req.params;
        const { userId } = req.body;

        if (!userId) {
            return res.status(400).json({
                success: false,
                message: 'User ID is required'
            });
        }

        const notice = await Notice.findById(id);
        if (!notice) {
            return res.status(404).json({
                success: false,
                message: 'Notice not found'
            });
        }

        // Check if user already marked as read
        const alreadyRead = notice.readBy.some(read => read.user.toString() === userId);
        
        if (!alreadyRead) {
            notice.readBy.push({
                user: userId,
                readAt: new Date()
            });
            await notice.save();
        }

        res.status(200).json({
            success: true,
            message: 'Notice marked as read'
        });
    } catch (error) {
        console.error('Error marking notice as read:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

// Get notices for specific user based on their role and targeting
const getNoticesForUser = async (req, res) => {
    try {
        const { userId } = req.params;
        const { page = 1, limit = 10 } = req.query;

        const user = await User.findById(userId)
            .populate('program')
            .populate('department')
            .populate('classId');

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Build filter for user-specific notices
        const filter = {
            isActive: true,
            isPublished: true,
            publishDate: { $lte: new Date() },
            $or: [
                { targetAudience: { $in: ['All'] } },
                { targetAudience: { $in: [user.role] } }
            ]
        };

        // Add specific targeting filters
        if (user.program) {
            filter.$or.push({ targetPrograms: user.program._id });
        }
        if (user.department) {
            filter.$or.push({ targetDepartments: user.department._id });
        }
        if (user.classId) {
            filter.$or.push({ targetClasses: user.classId._id });
        }
        if (user.semester) {
            filter.$or.push({ targetSemesters: user.semester });
        }

        const skip = (parseInt(page) - 1) * parseInt(limit);

        const notices = await Notice.find(filter)
            .populate('author', 'name email role')
            .sort({ isPinned: -1, publishDate: -1 })
            .skip(skip)
            .limit(parseInt(limit));

        const totalNotices = await Notice.countDocuments(filter);

        res.status(200).json({
            success: true,
            notices,
            totalNotices
        });
    } catch (error) {
        console.error('Error fetching user notices:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

// Delete notice (soft delete)
const deleteNotice = async (req, res) => {
    try {
        const { id } = req.params;

        const notice = await Notice.findByIdAndUpdate(
            id,
            { isActive: false },
            { new: true }
        );

        if (!notice) {
            return res.status(404).json({
                success: false,
                message: 'Notice not found'
            });
        }

        res.status(200).json({
            success: true,
            message: 'Notice deleted successfully'
        });
    } catch (error) {
        console.error('Error deleting notice:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

module.exports = {
    createNotice,
    getAllNotices,
    getNoticeById,
    updateNotice,
    markAsRead,
    getNoticesForUser,
    deleteNotice
};
