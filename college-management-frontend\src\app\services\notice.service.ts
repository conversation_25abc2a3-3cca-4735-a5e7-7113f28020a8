import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { environment } from '../../environments/environment';

export interface Notice {
  _id?: string;
  title: string;
  content: string;
  author: string;
  authorRole: string;
  category: 'General' | 'Academic' | 'Administrative' | 'Event' | 'Holiday' | 'Examination' | 'Emergency';
  priority: 'Low' | 'Medium' | 'High' | 'Urgent';
  targetAudience: string[];
  targetPrograms?: string[];
  targetDepartments?: string[];
  targetClasses?: string[];
  targetSemesters?: number[];
  publishDate: Date;
  expiryDate?: Date;
  isPublished: boolean;
  isPinned: boolean;
  readBy?: Array<{
    user: string;
    readAt: Date;
  }>;
  views: number;
  createdAt?: Date;
  updatedAt?: Date;
  status?: string;
  daysRemaining?: number;
}

export interface NoticeResponse {
  success: boolean;
  message?: string;
  notice?: Notice;
  notices?: Notice[];
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalNotices: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

@Injectable({
  providedIn: 'root'
})
export class NoticeService {
  private apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) { }

  private getAuthHeaders(): HttpHeaders {
    const token = localStorage.getItem('token');
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });
  }

  // Create a new notice
  createNotice(noticeData: Partial<Notice>): Observable<NoticeResponse> {
    return this.http.post<NoticeResponse>(`${this.apiUrl}/notices`, noticeData, {
      headers: this.getAuthHeaders()
    }).pipe(
      tap({
        next: (response) => {
          console.log('Notice created successfully:', response);
        },
      }),
      catchError((error) => {
        console.error('Error creating notice:', error);
        return throwError(() => error);
      })
    );
  }

  // Get all notices with filtering
  getAllNotices(params?: {
    page?: number;
    limit?: number;
    category?: string;
    priority?: string;
    author?: string;
    targetAudience?: string;
    isPublished?: boolean;
    isPinned?: boolean;
    includeUnpublished?: boolean;
  }): Observable<NoticeResponse> {
    let httpParams = new HttpParams();
    
    if (params) {
      Object.keys(params).forEach(key => {
        const value = params[key as keyof typeof params];
        if (value !== undefined && value !== null) {
          httpParams = httpParams.set(key, value.toString());
        }
      });
    }

    return this.http.get<NoticeResponse>(`${this.apiUrl}/notices`, {
      headers: this.getAuthHeaders(),
      params: httpParams
    }).pipe(
      catchError((error) => {
        console.error('Error fetching notices:', error);
        return throwError(() => error);
      })
    );
  }

  // Get notice by ID
  getNoticeById(id: string): Observable<NoticeResponse> {
    return this.http.get<NoticeResponse>(`${this.apiUrl}/notices/${id}`, {
      headers: this.getAuthHeaders()
    }).pipe(
      catchError((error) => {
        console.error('Error fetching notice:', error);
        return throwError(() => error);
      })
    );
  }

  // Update notice
  updateNotice(id: string, updateData: Partial<Notice>): Observable<NoticeResponse> {
    return this.http.put<NoticeResponse>(`${this.apiUrl}/notices/${id}`, updateData, {
      headers: this.getAuthHeaders()
    }).pipe(
      tap({
        next: (response) => {
          console.log('Notice updated successfully:', response);
        },
      }),
      catchError((error) => {
        console.error('Error updating notice:', error);
        return throwError(() => error);
      })
    );
  }

  // Mark notice as read
  markAsRead(id: string, userId: string): Observable<NoticeResponse> {
    return this.http.post<NoticeResponse>(`${this.apiUrl}/notices/${id}/read`, {
      userId
    }, {
      headers: this.getAuthHeaders()
    }).pipe(
      tap({
        next: (response) => {
          console.log('Notice marked as read:', response);
        },
      }),
      catchError((error) => {
        console.error('Error marking notice as read:', error);
        return throwError(() => error);
      })
    );
  }

  // Get notices for specific user
  getNoticesForUser(userId: string, page: number = 1, limit: number = 10): Observable<NoticeResponse> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    return this.http.get<NoticeResponse>(`${this.apiUrl}/notices/user/${userId}`, {
      headers: this.getAuthHeaders(),
      params
    }).pipe(
      catchError((error) => {
        console.error('Error fetching user notices:', error);
        return throwError(() => error);
      })
    );
  }

  // Delete notice
  deleteNotice(id: string): Observable<NoticeResponse> {
    return this.http.delete<NoticeResponse>(`${this.apiUrl}/notices/${id}`, {
      headers: this.getAuthHeaders()
    }).pipe(
      tap({
        next: (response) => {
          console.log('Notice deleted successfully:', response);
        },
      }),
      catchError((error) => {
        console.error('Error deleting notice:', error);
        return throwError(() => error);
      })
    );
  }

  // Publish notice
  publishNotice(id: string): Observable<NoticeResponse> {
    return this.updateNotice(id, { isPublished: true });
  }

  // Unpublish notice
  unpublishNotice(id: string): Observable<NoticeResponse> {
    return this.updateNotice(id, { isPublished: false });
  }

  // Pin notice
  pinNotice(id: string): Observable<NoticeResponse> {
    return this.updateNotice(id, { isPinned: true });
  }

  // Unpin notice
  unpinNotice(id: string): Observable<NoticeResponse> {
    return this.updateNotice(id, { isPinned: false });
  }

  // Get notice statistics
  getNoticeStats(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/notices/stats`, {
      headers: this.getAuthHeaders()
    }).pipe(
      catchError((error) => {
        console.error('Error fetching notice stats:', error);
        return throwError(() => error);
      })
    );
  }
}
