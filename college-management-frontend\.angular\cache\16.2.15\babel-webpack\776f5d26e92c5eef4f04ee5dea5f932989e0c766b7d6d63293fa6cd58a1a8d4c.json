{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { TeacherRoutingModule } from './teacher-routing.module';\nimport { MaterialModule } from 'src/app/material';\nimport * as i0 from \"@angular/core\";\nexport let TeacherModule = /*#__PURE__*/(() => {\n  class TeacherModule {\n    static #_ = this.ɵfac = function TeacherModule_Factory(t) {\n      return new (t || TeacherModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: TeacherModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, TeacherRoutingModule, MaterialModule]\n    });\n  }\n  return TeacherModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}