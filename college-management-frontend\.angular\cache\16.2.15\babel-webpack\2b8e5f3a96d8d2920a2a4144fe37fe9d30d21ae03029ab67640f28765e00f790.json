{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let ClassesService = /*#__PURE__*/(() => {\n  class ClassesService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = environment.apiUrl;\n    }\n    // Add class\n    addClass(classData) {\n      return this.http.post(`${this.apiUrl}/add-class`, classData);\n    }\n    // Update class\n    updateClass(id, classData) {\n      return this.http.put(`${this.apiUrl}/update-class/${id}`, classData);\n    }\n    // Delete class\n    deleteClass(id) {\n      return this.http.delete(`${this.apiUrl}/delete-class/${id}`);\n    }\n    // Get all classes\n    getClasses() {\n      return this.http.get(`${this.apiUrl}/get-classes`);\n    }\n    // Get class by ID\n    getClassById(classId) {\n      return this.http.get(`${this.apiUrl}/${classId}`);\n    }\n    // Get all subjects (for dynamic subject selection)\n    getSubjects() {\n      return this.http.get(`${this.apiUrl}/subjects`);\n    }\n    static #_ = this.ɵfac = function ClassesService_Factory(t) {\n      return new (t || ClassesService)(i0.ɵɵinject(i1.HttpClient));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ClassesService,\n      factory: ClassesService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return ClassesService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}