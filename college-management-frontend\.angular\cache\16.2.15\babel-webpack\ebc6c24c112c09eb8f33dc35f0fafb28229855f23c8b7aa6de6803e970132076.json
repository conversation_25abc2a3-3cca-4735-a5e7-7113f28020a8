{"ast": null, "code": "export const environment = {\n  production: false,\n  name: \"{DEV}\",\n  // apiUrl : 'http://localhost:5004/api/v1',\n  // apiUrl : 'https://college-managment-server-production.up.railway.app/api/v1',\n  apiUrl: 'https://college-managment-server.onrender.com/api/v1',\n  stripeKey: 'pk_live_51MiZTVF1YkHoz4Y5sBZLklUnnkRrmCZ0yfEeWnPUAluKFbvMEGPjCl7u7VEHlnQLT2PcBa3zviwQMr6ihIsQhjR900ZTqbI4eb'\n};", "map": {"version": 3, "names": ["environment", "production", "name", "apiUrl", "<PERSON><PERSON><PERSON>"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\environments\\environment.ts"], "sourcesContent": ["export const environment = {\r\n    production: false,\r\n    name: \"{DEV}\",\r\n    // apiUrl : 'http://localhost:5004/api/v1',\r\n    // apiUrl : 'https://college-managment-server-production.up.railway.app/api/v1',\r\n    apiUrl : 'https://college-managment-server.onrender.com/api/v1',\r\n    stripeKey:'pk_live_51MiZTVF1YkHoz4Y5sBZLklUnnkRrmCZ0yfEeWnPUAluKFbvMEGPjCl7u7VEHlnQLT2PcBa3zviwQMr6ihIsQhjR900ZTqbI4eb',\r\n  };\r\n  "], "mappings": "AAAA,OAAO,MAAMA,WAAW,GAAG;EACvBC,UAAU,EAAE,KAAK;EACjBC,IAAI,EAAE,OAAO;EACb;EACA;EACAC,MAAM,EAAG,sDAAsD;EAC/DC,SAAS,EAAC;CACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}