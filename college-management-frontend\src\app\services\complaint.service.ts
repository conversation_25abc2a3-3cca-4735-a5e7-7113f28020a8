import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { environment } from '../../environments/environment';

export interface Complaint {
  _id?: string;
  title: string;
  description: string;
  complainant: string;
  complainantRole: string;
  category: 'Academic' | 'Infrastructure' | 'Software' | 'Administrative' | 'Suggestion' | 'Other';
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  status: 'Pending' | 'In Progress' | 'Resolved' | 'Closed' | 'Rejected';
  assignedTo?: string;
  resolution?: string;
  resolvedBy?: string;
  resolvedAt?: Date;
  comments?: Array<{
    user: string;
    comment: string;
    createdAt: Date;
  }>;
  createdAt?: Date;
  updatedAt?: Date;
  ageInDays?: number;
    checked?: boolean; 
     date?: Date | string;
  user?: string;        // Add if you are displaying user name/email
  complaint?: string;   // Only if used — but probably this should be `description`
}

export interface ComplaintResponse {
  success: boolean;
  message?: string;
  complaint?: Complaint;
  complaints?: Complaint[];
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalComplaints: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

@Injectable({
  providedIn: 'root'
})
export class ComplaintService {
  updateComplaint(id: string, data: any): Observable<{ success: boolean }> {
  return this.http.put<{ success: boolean }>(`/api/complaints/${id}`, data);
}

  private apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) { }

  private getAuthHeaders(): HttpHeaders {
    const token = localStorage.getItem('token');
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });
  }

  // Create a new complaint
  createComplaint(complaintData: Partial<Complaint>): Observable<ComplaintResponse> {
    return this.http.post<ComplaintResponse>(`${this.apiUrl}/complaints`, complaintData, {
      headers: this.getAuthHeaders()
    }).pipe(
      catchError((error) => {
        return throwError(() => error);
      })
    );
  }

  // Get all complaints with filtering
  getAllComplaints(params?: {
    page?: number;
    limit?: number;
    status?: string;
    category?: string;
    priority?: string;
    complainant?: string;
    assignedTo?: string;
  }): Observable<ComplaintResponse> {
    let httpParams = new HttpParams();
    
    if (params) {
      Object.keys(params).forEach(key => {
        const value = params[key as keyof typeof params];
        if (value !== undefined && value !== null) {
          httpParams = httpParams.set(key, value.toString());
        }
      });
    }

    return this.http.get<ComplaintResponse>(`${this.apiUrl}/complaints`, {
      headers: this.getAuthHeaders(),
      params: httpParams
    }).pipe(
      catchError((error) => {
        console.error('Error fetching complaints:', error);
        return throwError(() => error);
      })
    );
  }

  // Get complaint by ID
  getComplaintById(id: string): Observable<ComplaintResponse> {
    return this.http.get<ComplaintResponse>(`${this.apiUrl}/complaints/${id}`, {
      headers: this.getAuthHeaders()
    }).pipe(
      catchError((error) => {
        console.error('Error fetching complaint:', error);
        return throwError(() => error);
      })
    );
  }

  // Update complaint status
  updateComplaintStatus(id: string, updateData: {
    status?: string;
    assignedTo?: string;
    resolution?: string;
    resolvedBy?: string;
  }): Observable<ComplaintResponse> {
    return this.http.put<ComplaintResponse>(`${this.apiUrl}/complaints/${id}/status`, updateData, {
      headers: this.getAuthHeaders()
    }).pipe(
      tap({
        next: (response) => {
          console.log('Complaint status updated successfully:', response);
        },
      }),
      catchError((error) => {
        console.error('Error updating complaint status:', error);
        return throwError(() => error);
      })
    );
  }

  // Add comment to complaint
  addComment(id: string, comment: string, userId: string): Observable<ComplaintResponse> {
    return this.http.post<ComplaintResponse>(`${this.apiUrl}/complaints/${id}/comments`, {
      comment,
      user: userId
    }, {
      headers: this.getAuthHeaders()
    }).pipe(
      tap({
        next: (response) => {
          console.log('Comment added successfully:', response);
        },
      }),
      catchError((error) => {
        console.error('Error adding comment:', error);
        return throwError(() => error);
      })
    );
  }

  // Get complaints by user
  getComplaintsByUser(userId: string, page: number = 1, limit: number = 10): Observable<ComplaintResponse> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    return this.http.get<ComplaintResponse>(`${this.apiUrl}/complaints/user/${userId}`, {
      headers: this.getAuthHeaders(),
      params
    }).pipe(
      catchError((error) => {
        console.error('Error fetching user complaints:', error);
        return throwError(() => error);
      })
    );
  }

  // Delete complaint
  deleteComplaint(id: string): Observable<ComplaintResponse> {
    return this.http.delete<ComplaintResponse>(`${this.apiUrl}/complaints/${id}`, {
      headers: this.getAuthHeaders()
    }).pipe(
      tap({
        next: (response) => {
          console.log('Complaint deleted successfully:', response);
        },
      }),
      catchError((error) => {
        console.error('Error deleting complaint:', error);
        return throwError(() => error);
      })
    );
  }

  // Get complaint statistics
  getComplaintStats(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/complaints/stats`, {
      headers: this.getAuthHeaders()
    }).pipe(
      catchError((error) => {
        console.error('Error fetching complaint stats:', error);
        return throwError(() => error);
      })
    );
  }
}
