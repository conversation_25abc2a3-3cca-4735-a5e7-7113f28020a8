{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/services/user.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction SignupComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"small\", 27);\n    i0.ɵɵtext(2, \"Name is required\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SignupComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"small\", 27);\n    i0.ɵɵtext(2, \"Please enter a valid email\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SignupComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"small\", 27);\n    i0.ɵɵtext(2, \"Role is required\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SignupComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"small\", 27);\n    i0.ɵɵtext(2, \"Password is required (min 6 characters)\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let SignupComponent = /*#__PURE__*/(() => {\n  class SignupComponent {\n    constructor(fb, userService, router) {\n      this.fb = fb;\n      this.userService = userService;\n      this.router = router;\n    }\n    ngOnInit() {\n      // Initialize the form with validations\n      this.signupForm = this.fb.group({\n        name: ['', [Validators.required]],\n        email: ['', [Validators.required, Validators.email]],\n        role: ['', Validators.required],\n        password: ['', [Validators.required, Validators.minLength(6)]]\n      });\n    }\n    onSubmit() {\n      if (this.signupForm.valid) {\n        const signupData = this.signupForm.value;\n        // Call the UserService's signup function\n        this.userService.register(signupData).subscribe({\n          next: res => {\n            console.log(res, \"ressss\");\n            // Store user data for potential OTP verification\n            if (res.success && res.user) {\n              localStorage.setItem('signupUser', JSON.stringify(res.user));\n            }\n            // Assuming API responds with success on valid registration\n            Swal.fire({\n              title: `<i style=\"color: #29578c\" class=\"fa fa-envelope fa-2x\" aria-hidden=\"true\"></i>`,\n              html: `<h1 class=\"mb-4\">Registration Successful!</h1><p>Your account has been created successfully. You can now log in with your credentials.</p>`,\n              showCloseButton: true,\n              showCancelButton: true,\n              cancelButtonText: 'Enter Verification Code Manually',\n              showConfirmButton: true,\n              confirmButtonText: 'Go to Login',\n              confirmButtonColor: '#29578c'\n            }).then(result => {\n              if (result.dismiss === Swal.DismissReason.cancel) {\n                // Navigate to OTP verification page\n                this.router.navigate(['/auth/Enter-code']);\n              } else if (result.isConfirmed) {\n                // Navigate to login page\n                this.router.navigate(['/auth']);\n              }\n            });\n          },\n          error: error => {\n            console.log(error, \"ressss\");\n            // Handle errors, such as email already taken\n            console.error('Signup failed', error);\n            Swal.fire(error?.error?.message, 'error');\n          }\n        });\n      } else {\n        // If the form is invalid\n        Swal.fire('Error', 'Please fill in all required fields correctly.', 'error');\n      }\n    }\n    static #_ = this.ɵfac = function SignupComponent_Factory(t) {\n      return new (t || SignupComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SignupComponent,\n      selectors: [[\"app-signup\"]],\n      decls: 53,\n      vars: 6,\n      consts: [[1, \"container-fluid\"], [1, \"row\", \"vh-100\"], [1, \"col-lg-5\", \"d-flex\", \"flex-column\", \"justify-content-center\", \"align-items-center\", \"p-3\", \"bg-white\"], [1, \"w-100\", 2, \"max-width\", \"400px\"], [1, \"mb-4\"], [\"src\", \"../../../assets/images/logo.jpeg\", \"alt\", \"Logo\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"mb-3\"], [\"for\", \"name\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"name\", \"formControlName\", \"name\", \"placeholder\", \"Enter your name\", 1, \"form-control\"], [4, \"ngIf\"], [\"for\", \"email\", 1, \"form-label\"], [\"type\", \"email\", \"id\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter your email\", 1, \"form-control\"], [\"for\", \"role\", 1, \"form-label\"], [\"formControlName\", \"role\", 1, \"form-control\"], [\"for\", \"password\", 1, \"form-label\"], [\"type\", \"password\", \"id\", \"password\", \"formControlName\", \"password\", \"placeholder\", \"Enter your password\", 1, \"form-control\"], [1, \"d-grid\", \"gap-2\"], [\"type\", \"submit\", 1, \"btn\", \"submit\", 3, \"disabled\"], [1, \"mt-3\", \"text-center\"], [\"routerLink\", \"/auth\"], [1, \"col-lg-7\", \"d-none\", \"d-lg-flex\", \"flex-column\", \"align-items-start\", \"bg-light\", \"position-relative\"], [1, \"text-start\", \"p-5\", \"w-100\"], [1, \"blockquote-footer\"], [\"title\", \"Source Title\"], [1, \"image-container\"], [\"src\", \"../../../assets/images/background.jpeg\", \"alt\", \"Stats graph\", 1, \"img-fluid\", \"position-absolute\"], [1, \"text-danger\"]],\n      template: function SignupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h2\", 4);\n          i0.ɵɵelement(5, \"img\", 5);\n          i0.ɵɵtext(6, \" GPGC (Swabi) \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"h1\", 4);\n          i0.ɵɵtext(8, \"Sign Up\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"form\", 6);\n          i0.ɵɵlistener(\"ngSubmit\", function SignupComponent_Template_form_ngSubmit_9_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(10, \"div\", 7)(11, \"label\", 8);\n          i0.ɵɵtext(12, \"Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(13, \"input\", 9);\n          i0.ɵɵtemplate(14, SignupComponent_div_14_Template, 3, 0, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 7)(16, \"label\", 11);\n          i0.ɵɵtext(17, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(18, \"input\", 12);\n          i0.ɵɵtemplate(19, SignupComponent_div_19_Template, 3, 0, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\", 7)(21, \"label\", 13);\n          i0.ɵɵtext(22, \"Role\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"select\", 14)(24, \"option\");\n          i0.ɵɵtext(25, \"Principal\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"option\");\n          i0.ɵɵtext(27, \"Student\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"option\");\n          i0.ɵɵtext(29, \"Teacher\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(30, SignupComponent_div_30_Template, 3, 0, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"div\", 7)(32, \"label\", 15);\n          i0.ɵɵtext(33, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(34, \"input\", 16);\n          i0.ɵɵtemplate(35, SignupComponent_div_35_Template, 3, 0, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"div\", 17)(37, \"button\", 18);\n          i0.ɵɵtext(38, \"Get started\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"p\", 19);\n          i0.ɵɵtext(40, \"Already have an account? \");\n          i0.ɵɵelementStart(41, \"a\", 20);\n          i0.ɵɵtext(42, \"Login\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(43, \"div\", 21)(44, \"div\", 22)(45, \"h2\", 4);\n          i0.ɵɵtext(46, \"College management system Signup page\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"footer\", 23);\n          i0.ɵɵtext(48, \"Name\");\n          i0.ɵɵelementStart(49, \"cite\", 24);\n          i0.ɵɵtext(50, \"Owner ~ GPGC SWABI\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(51, \"div\", 25);\n          i0.ɵɵelement(52, \"img\", 26);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_2_0;\n          let tmp_3_0;\n          let tmp_4_0;\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"formGroup\", ctx.signupForm);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx.signupForm.get(\"name\")) == null ? null : tmp_1_0.touched) && ((tmp_1_0 = ctx.signupForm.get(\"name\")) == null ? null : tmp_1_0.invalid));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.signupForm.get(\"email\")) == null ? null : tmp_2_0.touched) && ((tmp_2_0 = ctx.signupForm.get(\"email\")) == null ? null : tmp_2_0.invalid));\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx.signupForm.get(\"role\")) == null ? null : tmp_3_0.touched) && ((tmp_3_0 = ctx.signupForm.get(\"role\")) == null ? null : tmp_3_0.invalid));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.signupForm.get(\"password\")) == null ? null : tmp_4_0.touched) && ((tmp_4_0 = ctx.signupForm.get(\"password\")) == null ? null : tmp_4_0.invalid));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.signupForm.invalid);\n        }\n      },\n      dependencies: [i4.NgIf, i3.RouterLink, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n      styles: [\".image-container[_ngcontent-%COMP%]{width:100%;height:100%;position:relative}.image-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:80%;height:auto;position:absolute;right:0;bottom:0;object-fit:contain}.submit[_ngcontent-%COMP%]{background-color:#29578c;color:#fff}a[_ngcontent-%COMP%]{cursor:pointer;color:#29578c;font-weight:700}\"]\n    });\n  }\n  return SignupComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}