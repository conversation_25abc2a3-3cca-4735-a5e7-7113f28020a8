{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { StudentDashboardRoutingModule } from './student-dashboard-routing.module';\nimport { MaterialModule } from 'src/app/material';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nexport let StudentDashboardModule = /*#__PURE__*/(() => {\n  class StudentDashboardModule {\n    static #_ = this.ɵfac = function StudentDashboardModule_Factory(t) {\n      return new (t || StudentDashboardModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: StudentDashboardModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, StudentDashboardRoutingModule, MaterialModule, FormsModule]\n    });\n  }\n  return StudentDashboardModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}