{"ast": null, "code": "import { HttpEventType } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/user.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common/http\";\nimport * as i4 from \"src/app/services/program.service\";\nimport * as i5 from \"src/app/services/department.service\";\nimport * as i6 from \"src/app/services/classes.service\";\nimport * as i7 from \"src/app/services/bulk-upload.service\";\nimport * as i8 from \"@angular/material/snack-bar\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"@angular/forms\";\nimport * as i11 from \"@angular/material/core\";\nimport * as i12 from \"@angular/material/button\";\nimport * as i13 from \"@angular/material/card\";\nimport * as i14 from \"@angular/material/icon\";\nimport * as i15 from \"@angular/material/input\";\nimport * as i16 from \"@angular/material/form-field\";\nimport * as i17 from \"@angular/material/progress-bar\";\nimport * as i18 from \"@angular/material/progress-spinner\";\nimport * as i19 from \"@angular/material/select\";\nimport * as i20 from \"@angular/material/tooltip\";\nfunction AdminStudentListComponent_mat_option_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 50);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const program_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", program_r7._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", program_r7.name, \" \");\n  }\n}\nfunction AdminStudentListComponent_mat_option_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 50);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dept_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", dept_r8._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", dept_r8.name, \" \");\n  }\n}\nfunction AdminStudentListComponent_mat_option_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 50);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const cls_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", cls_r9._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", cls_r9.className, \"-\", cls_r9.section, \" \");\n  }\n}\nfunction AdminStudentListComponent_tr_93_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 51)(1, \"td\", 33)(2, \"div\", 52)(3, \"div\", 53);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"small\", 54);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 55)(8, \"small\", 44);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(10, \"td\", 34)(11, \"div\", 56)(12, \"span\", 57);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"small\", 58);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"td\", 35)(17, \"div\", 59)(18, \"span\", 60);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"small\", 61);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"td\", 36)(23, \"span\", 62);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"td\", 37)(26, \"span\", 63);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"td\", 38)(29, \"div\", 64)(30, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function AdminStudentListComponent_tr_93_Template_button_click_30_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r13);\n      const student_r10 = restoredCtx.$implicit;\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.viewStudent(student_r10));\n    });\n    i0.ɵɵelementStart(31, \"mat-icon\");\n    i0.ɵɵtext(32, \"visibility\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function AdminStudentListComponent_tr_93_Template_button_click_33_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r13);\n      const student_r10 = restoredCtx.$implicit;\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.editStudent(student_r10));\n    });\n    i0.ɵɵelementStart(34, \"mat-icon\");\n    i0.ɵɵtext(35, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function AdminStudentListComponent_tr_93_Template_button_click_36_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r13);\n      const student_r10 = restoredCtx.$implicit;\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.deleteStudent(student_r10));\n    });\n    i0.ɵɵelementStart(37, \"mat-icon\");\n    i0.ɵɵtext(38, \"delete\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const student_r10 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(student_r10.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(student_r10.email);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" Roll: \", (student_r10 == null ? null : student_r10.rollNo) || \"N/A\", \" | \", (student_r10 == null ? null : student_r10.program == null ? null : student_r10.program.name) || \"N/A\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((student_r10 == null ? null : student_r10.rollNo) || \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((student_r10 == null ? null : student_r10.regNo) || \"N/A\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((student_r10 == null ? null : student_r10.program == null ? null : student_r10.program.name) || \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((student_r10 == null ? null : student_r10.department == null ? null : student_r10.department.name) || \"N/A\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r3.getClassName(student_r10), \"-\", ctx_r3.getClassSection(student_r10), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((student_r10 == null ? null : student_r10.contact) || \"N/A\");\n  }\n}\nfunction AdminStudentListComponent_div_94_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵelement(1, \"mat-spinner\", 69);\n    i0.ɵɵelementStart(2, \"p\", 70);\n    i0.ɵɵtext(3, \"Loading students...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AdminStudentListComponent_div_95_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"mat-icon\", 72);\n    i0.ɵɵtext(2, \"school\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No Students Found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Try adjusting your filters or add new students to get started.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 73)(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"person_add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Add First Student \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AdminStudentListComponent_div_109_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 95)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r17.selectedFileName);\n  }\n}\nfunction AdminStudentListComponent_div_109_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 96);\n    i0.ɵɵelement(1, \"mat-progress-bar\", 97);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", ctx_r18.uploadProgress);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Uploading... \", ctx_r18.uploadProgress, \"%\");\n  }\n}\nfunction AdminStudentListComponent_div_109_div_44_div_20_div_4_small_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const failed_r23 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"(\", failed_r23.data.name, \")\");\n  }\n}\nfunction AdminStudentListComponent_div_109_div_44_div_20_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 108)(1, \"strong\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, AdminStudentListComponent_div_109_div_44_div_20_div_4_small_4_Template, 2, 1, \"small\", 109);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const failed_r23 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Row \", failed_r23.row, \":\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", failed_r23.error, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", failed_r23.data == null ? null : failed_r23.data.name);\n  }\n}\nfunction AdminStudentListComponent_div_109_div_44_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105)(1, \"h5\");\n    i0.ɵɵtext(2, \"Failed Records:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 106);\n    i0.ɵɵtemplate(4, AdminStudentListComponent_div_109_div_44_div_20_div_4_Template, 5, 3, \"div\", 107);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r21.uploadResults.failed);\n  }\n}\nfunction AdminStudentListComponent_div_109_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98)(1, \"div\", 99)(2, \"h4\");\n    i0.ɵɵtext(3, \"Upload Results\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 100)(5, \"div\", 101)(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 102)(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 103)(16, \"mat-icon\");\n    i0.ɵɵtext(17, \"assessment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\");\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(20, AdminStudentListComponent_div_109_div_44_div_20_Template, 5, 1, \"div\", 104);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r19.uploadResults.successful == null ? null : ctx_r19.uploadResults.successful.length) || 0, \" Successful\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r19.uploadResults.failed == null ? null : ctx_r19.uploadResults.failed.length) || 0, \" Failed\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r19.uploadResults.totalProcessed || 0, \" Total\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r19.uploadResults.failed == null ? null : ctx_r19.uploadResults.failed.length) > 0);\n  }\n}\nfunction AdminStudentListComponent_div_109_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110)(1, \"mat-icon\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap((ctx_r20.uploadResults == null ? null : ctx_r20.uploadResults.successful == null ? null : ctx_r20.uploadResults.successful.length) > 0 ? \"success\" : \"error\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r20.uploadResults == null ? null : ctx_r20.uploadResults.successful == null ? null : ctx_r20.uploadResults.successful.length) > 0 ? \"check_circle\" : \"error\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r20.responseMessage);\n  }\n}\nfunction AdminStudentListComponent_div_109_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 74);\n    i0.ɵɵlistener(\"click\", function AdminStudentListComponent_div_109_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.closeUploadDialog());\n    });\n    i0.ɵɵelementStart(1, \"div\", 75);\n    i0.ɵɵlistener(\"click\", function AdminStudentListComponent_div_109_Template_div_click_1_listener($event) {\n      return $event.stopPropagation();\n    });\n    i0.ɵɵelementStart(2, \"div\", 76)(3, \"h3\")(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"upload_file\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" Bulk Upload Students \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function AdminStudentListComponent_div_109_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.closeUploadDialog());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"close\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 78)(11, \"div\", 79)(12, \"h4\")(13, \"mat-icon\");\n    i0.ɵɵtext(14, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15, \" Instructions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"ol\")(17, \"li\");\n    i0.ɵɵtext(18, \"Download the Excel template using the \\\"Download Template\\\" button\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"li\");\n    i0.ɵɵtext(20, \"Fill in all required student information in the template\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"li\");\n    i0.ɵɵtext(22, \"Make sure all programs, departments, and classes exist in the system\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"li\");\n    i0.ɵɵtext(24, \"Upload the completed Excel file\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 80)(26, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function AdminStudentListComponent_div_109_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.downloadTemplate());\n    });\n    i0.ɵɵelementStart(27, \"mat-icon\");\n    i0.ɵɵtext(28, \"download\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(29, \" Download Excel Template \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"p\", 82)(31, \"mat-icon\");\n    i0.ɵɵtext(32, \"lightbulb\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(33, \" The template includes sample data and all available programs, departments, and classes for reference. \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 83)(35, \"div\", 84)(36, \"input\", 85, 86);\n    i0.ɵɵlistener(\"change\", function AdminStudentListComponent_div_109_Template_input_change_36_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function AdminStudentListComponent_div_109_Template_button_click_38_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const _r16 = i0.ɵɵreference(37);\n      return i0.ɵɵresetView(_r16.click());\n    });\n    i0.ɵɵelementStart(39, \"mat-icon\");\n    i0.ɵɵtext(40, \"attach_file\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(41, \" Select Excel File \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(42, AdminStudentListComponent_div_109_div_42_Template, 5, 1, \"div\", 88);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(43, AdminStudentListComponent_div_109_div_43_Template, 4, 2, \"div\", 89);\n    i0.ɵɵtemplate(44, AdminStudentListComponent_div_109_div_44_Template, 21, 4, \"div\", 90);\n    i0.ɵɵtemplate(45, AdminStudentListComponent_div_109_div_45_Template, 5, 4, \"div\", 91);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 92)(47, \"button\", 93);\n    i0.ɵɵlistener(\"click\", function AdminStudentListComponent_div_109_Template_button_click_47_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r33 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r33.closeUploadDialog());\n    });\n    i0.ɵɵtext(48, \" Cancel \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"button\", 94);\n    i0.ɵɵlistener(\"click\", function AdminStudentListComponent_div_109_Template_button_click_49_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r34.onUpload());\n    });\n    i0.ɵɵelementStart(50, \"mat-icon\");\n    i0.ɵɵtext(51, \"upload\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(52);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(38);\n    i0.ɵɵproperty(\"disabled\", ctx_r6.uploading);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.selectedFileName);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.uploading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.uploadResults);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.responseMessage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r6.uploading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r6.selectedFile || ctx_r6.uploading);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.uploading ? \"Uploading...\" : \"Upload Students\", \" \");\n  }\n}\nexport class AdminStudentListComponent {\n  constructor(userService, router, http, programService, departmentService, classesService, bulkUploadService, snackBar) {\n    this.userService = userService;\n    this.router = router;\n    this.http = http;\n    this.programService = programService;\n    this.departmentService = departmentService;\n    this.classesService = classesService;\n    this.bulkUploadService = bulkUploadService;\n    this.snackBar = snackBar;\n    this.searchQuery = '';\n    this.currentPage = 1;\n    this.itemsPerPage = 10;\n    this.totalPages = 1;\n    this.totalRecords = 0;\n    this.loading = false;\n    this.Math = Math; // Make Math available in template\n    // Data arrays\n    this.programs = [];\n    this.departments = [];\n    this.classes = [];\n    this.students = [];\n    this.filteredDepartments = [];\n    this.filteredClasses = [];\n    // Selected filters\n    this.selectedProgram = '';\n    this.selectedDepartment = '';\n    this.selectedClass = '';\n    this.selectedSection = '';\n    // Bulk upload properties\n    this.selectedFile = null;\n    this.selectedFileName = '';\n    this.responseMessage = '';\n    this.uploading = false;\n    this.uploadProgress = 0;\n    this.showUploadDialog = false;\n    this.uploadResults = null;\n    // Statistics\n    this.totalStudents = 0;\n    this.activeStudents = 0;\n  }\n  ngOnInit() {\n    this.loadInitialData();\n    this.setupFormSubscriptions();\n    this.fetchStudents();\n    this.calculateStatistics();\n  }\n  // Search and filter methods\n  onSearch() {\n    this.currentPage = 1;\n    this.fetchStudents();\n  }\n  onProgramChange() {\n    // Filter departments by selected program\n    if (this.selectedProgram) {\n      this.filteredDepartments = this.departments.filter(dept => {\n        const programId = typeof dept.program === 'object' ? dept.program._id : dept.program;\n        return programId === this.selectedProgram;\n      });\n    } else {\n      this.filteredDepartments = this.departments;\n    }\n    // Reset dependent selections\n    this.selectedDepartment = '';\n    this.selectedClass = '';\n    this.filteredClasses = [];\n    this.applyFilters();\n  }\n  onDepartmentChange() {\n    // Filter classes by selected program and department\n    if (this.selectedProgram && this.selectedDepartment) {\n      this.filteredClasses = this.classes.filter(cls => {\n        const programId = typeof cls.program === 'object' ? cls.program._id : cls.program;\n        const departmentId = typeof cls.department === 'object' ? cls.department._id : cls.department;\n        return programId === this.selectedProgram && departmentId === this.selectedDepartment;\n      });\n    } else {\n      this.filteredClasses = [];\n    }\n    // Reset class selection\n    this.selectedClass = '';\n    this.applyFilters();\n  }\n  onClassChange() {\n    this.applyFilters();\n  }\n  applyFilters() {\n    this.currentPage = 1;\n    this.fetchStudents();\n  }\n  clearFilters() {\n    this.searchQuery = '';\n    this.selectedProgram = '';\n    this.selectedDepartment = '';\n    this.selectedClass = '';\n    this.selectedSection = '';\n    this.filteredDepartments = this.departments;\n    this.filteredClasses = this.classes;\n    this.fetchStudents();\n  }\n  calculateStatistics() {\n    // This would typically come from a dashboard API\n    // For now, we'll calculate from the current students array\n    this.totalStudents = this.students.length;\n    this.activeStudents = this.students.filter(student => student.isActive !== false).length;\n  }\n  loadInitialData() {\n    this.loadPrograms();\n    this.loadDepartments();\n    this.loadClasses();\n  }\n  loadPrograms() {\n    this.programService.getAllPrograms(true).subscribe({\n      next: response => {\n        if (response.success) {\n          this.programs = response.programs;\n        }\n      },\n      error: error => {\n        console.error('Error loading programs:', error);\n      }\n    });\n  }\n  loadDepartments() {\n    this.departmentService.getAllDepartments().subscribe({\n      next: response => {\n        if (response.success) {\n          this.departments = response.departments;\n          this.filteredDepartments = this.departments;\n        }\n      },\n      error: error => {\n        console.error('Error loading departments:', error);\n      }\n    });\n  }\n  loadClasses() {\n    this.classesService.getAllClasses().subscribe({\n      next: response => {\n        if (response.success) {\n          this.classes = response.classes;\n          this.filteredClasses = this.classes;\n        }\n      },\n      error: error => {\n        console.error('Error loading classes:', error);\n      }\n    });\n  }\n  fetchStudents() {\n    this.loading = true;\n    // Build filters for API call\n    const filters = {\n      role: 'Student',\n      page: this.currentPage,\n      limit: this.itemsPerPage\n    };\n    if (this.selectedProgram) filters.program = this.selectedProgram;\n    if (this.selectedDepartment) filters.department = this.selectedDepartment;\n    if (this.selectedClass) filters.classId = this.selectedClass;\n    if (this.searchQuery) filters.search = this.searchQuery;\n    this.userService.getAllUsers(filters).subscribe({\n      next: response => {\n        console.log(\"Students response:\", response);\n        if (response.success) {\n          this.students = response.users || response.data || [];\n          this.totalRecords = response.pagination?.totalRecords || response.total || this.students.length;\n          this.totalPages = response.pagination?.totalPages || Math.ceil(this.totalRecords / this.itemsPerPage);\n          this.currentPage = response.pagination?.currentPage || this.currentPage;\n          this.calculateStatistics();\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error fetching students:', error);\n        this.loading = false;\n        this.showError('Failed to load students');\n      }\n    });\n  }\n  setupFormSubscriptions() {\n    // Setup any form subscriptions if needed\n  }\n  // Bulk upload methods\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      // Validate file type\n      const allowedTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'];\n      if (!allowedTypes.includes(file.type)) {\n        this.showError('Please select a valid Excel file (.xlsx or .xls)');\n        return;\n      }\n      // Validate file size (5MB limit)\n      if (file.size > 5 * 1024 * 1024) {\n        this.showError('File size must be less than 5MB');\n        return;\n      }\n      this.selectedFile = file;\n      this.selectedFileName = file.name;\n      this.responseMessage = '';\n    }\n  }\n  downloadTemplate() {\n    this.bulkUploadService.downloadStudentTemplate().subscribe({\n      next: blob => {\n        const url = window.URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = 'student_bulk_upload_template.xlsx';\n        link.click();\n        window.URL.revokeObjectURL(url);\n        this.showSuccess('Template downloaded successfully');\n      },\n      error: error => {\n        console.error('Error downloading template:', error);\n        this.showError('Failed to download template');\n      }\n    });\n  }\n  openUploadDialog() {\n    this.showUploadDialog = true;\n    this.selectedFile = null;\n    this.selectedFileName = '';\n    this.responseMessage = '';\n    this.uploadProgress = 0;\n    this.uploadResults = null;\n  }\n  closeUploadDialog() {\n    this.showUploadDialog = false;\n    this.selectedFile = null;\n    this.selectedFileName = '';\n    this.responseMessage = '';\n    this.uploadProgress = 0;\n    this.uploadResults = null;\n  }\n  onUpload() {\n    if (!this.selectedFile) {\n      this.showError('Please select a file before uploading');\n      return;\n    }\n    this.uploading = true;\n    this.uploadProgress = 0;\n    this.responseMessage = '';\n    this.bulkUploadService.uploadStudentsBulkWithProgress(this.selectedFile).subscribe({\n      next: event => {\n        if (event.type === HttpEventType.UploadProgress) {\n          this.uploadProgress = Math.round(100 * event.loaded / (event.total || 1));\n        } else if (event.type === HttpEventType.Response) {\n          this.uploading = false;\n          const response = event.body;\n          if (response.success) {\n            this.uploadResults = response.results;\n            this.responseMessage = response.message;\n            this.showSuccess(response.message);\n            this.fetchStudents(); // Refresh the student list\n          } else {\n            this.showError(response.message || 'Upload failed');\n          }\n        }\n      },\n      error: error => {\n        this.uploading = false;\n        console.error('Error uploading file:', error);\n        this.showError(error.error?.message || 'Upload failed. Please try again.');\n      }\n    });\n  }\n  // Helper methods\n  showSuccess(message) {\n    this.snackBar.open(message, 'Close', {\n      duration: 3000,\n      panelClass: ['success-snackbar']\n    });\n  }\n  showError(message) {\n    this.snackBar.open(message, 'Close', {\n      duration: 5000,\n      panelClass: ['error-snackbar']\n    });\n  }\n  // Student management methods\n  editStudent(student) {\n    this.router.navigate(['/dashboard/admin/students/edit', student._id]);\n  }\n  viewStudent(student) {\n    this.router.navigate(['/dashboard/admin/students/view', student._id]);\n  }\n  deleteStudent(student) {\n    if (confirm(`Are you sure you want to delete ${student.name}?`)) {\n      this.userService.deleteUser(student._id).subscribe({\n        next: response => {\n          if (response.success) {\n            this.showSuccess('Student deleted successfully');\n            this.fetchStudents();\n          } else {\n            this.showError('Failed to delete student');\n          }\n        },\n        error: error => {\n          console.error('Error deleting student:', error);\n          this.showError('Failed to delete student');\n        }\n      });\n    }\n  }\n  // Pagination methods\n  nextPage() {\n    if (this.currentPage < this.totalPages) {\n      this.currentPage++;\n      this.fetchStudents();\n    }\n  }\n  previousPage() {\n    if (this.currentPage > 1) {\n      this.currentPage--;\n      this.fetchStudents();\n    }\n  }\n  // Helper methods for template\n  getClassName(student) {\n    if (student?.classId && typeof student.classId === 'object') {\n      return student.classId.className || 'N/A';\n    }\n    return 'N/A';\n  }\n  getClassSection(student) {\n    if (student?.classId && typeof student.classId === 'object') {\n      return student.classId.section || '';\n    }\n    return '';\n  }\n  // Additional helper methods\n  getDepartmentName(student) {\n    if (student?.department && typeof student.department === 'object') {\n      return student.department.name || 'N/A';\n    }\n    return 'N/A';\n  }\n  getProgramName(student) {\n    if (student?.program && typeof student.program === 'object') {\n      return student.program.name || 'N/A';\n    }\n    return 'N/A';\n  }\n  // Pagination helper methods\n  goToPage(page) {\n    if (page >= 1 && page <= this.totalPages) {\n      this.currentPage = page;\n      this.fetchStudents();\n    }\n  }\n  getPageNumbers() {\n    const pages = [];\n    const maxPagesToShow = 5;\n    const startPage = Math.max(1, this.currentPage - Math.floor(maxPagesToShow / 2));\n    const endPage = Math.min(this.totalPages, startPage + maxPagesToShow - 1);\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // Filter helper methods\n  resetFilters() {\n    this.searchQuery = '';\n    this.selectedProgram = '';\n    this.selectedDepartment = '';\n    this.selectedClass = '';\n    this.selectedSection = '';\n    this.filteredDepartments = this.departments;\n    this.filteredClasses = this.classes;\n    this.currentPage = 1;\n    this.fetchStudents();\n  }\n  addnewstudent() {\n    this.router.navigate(['/dashboard/admin/students/add-student']);\n  }\n  static #_ = this.ɵfac = function AdminStudentListComponent_Factory(t) {\n    return new (t || AdminStudentListComponent)(i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.HttpClient), i0.ɵɵdirectiveInject(i4.ProgramService), i0.ɵɵdirectiveInject(i5.DepartmentService), i0.ɵɵdirectiveInject(i6.ClassesService), i0.ɵɵdirectiveInject(i7.BulkUploadService), i0.ɵɵdirectiveInject(i8.MatSnackBar));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AdminStudentListComponent,\n    selectors: [[\"app-admin-student-list\"]],\n    decls: 110,\n    vars: 21,\n    consts: [[1, \"student-list-container\"], [1, \"container-fluid\"], [1, \"page-header\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"header-content\"], [1, \"page-title\"], [1, \"title-icon\"], [1, \"page-subtitle\"], [1, \"header-actions\"], [\"mat-stroked-button\", \"\", \"color\", \"accent\", 1, \"action-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", 1, \"action-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/dashboard/admin/students/add-student\", 1, \"add-btn\"], [1, \"filters-card\"], [1, \"compact-filters\"], [1, \"row\", \"g-2\"], [1, \"col-12\", \"col-md-4\"], [\"appearance\", \"outline\", 1, \"compact-field\"], [\"matInput\", \"\", \"placeholder\", \"Name, Roll No...\", 3, \"ngModel\", \"ngModelChange\", \"keyup.enter\"], [\"matSuffix\", \"\"], [1, \"col-6\", \"col-md-2\"], [3, \"ngModel\", \"ngModelChange\", \"selectionChange\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"ngModel\", \"disabled\", \"ngModelChange\", \"selectionChange\"], [1, \"col-6\", \"col-md-2\", \"d-flex\", \"align-items-center\", \"gap-1\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"compact-btn\", 3, \"click\"], [1, \"d-none\", \"d-lg-inline\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Reset\", 3, \"click\"], [1, \"table-card\"], [1, \"table-container\"], [1, \"table-responsive\"], [1, \"compact-table\"], [1, \"table-header\"], [1, \"col-student\"], [1, \"col-ids\", \"d-none\", \"d-md-table-cell\"], [1, \"col-program\", \"d-none\", \"d-lg-table-cell\"], [1, \"col-class\"], [1, \"col-contact\", \"d-none\", \"d-sm-table-cell\"], [1, \"col-actions\"], [\"class\", \"student-row\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"pagination-container\"], [1, \"pagination-info\"], [1, \"text-muted\"], [1, \"pagination-controls\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Previous Page\", 3, \"disabled\", \"click\"], [1, \"page-info\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Next Page\", 3, \"disabled\", \"click\"], [\"class\", \"upload-overlay\", 3, \"click\", 4, \"ngIf\"], [3, \"value\"], [1, \"student-row\"], [1, \"student-info\"], [1, \"student-name\"], [1, \"student-email\"], [1, \"mobile-info\", \"d-md-none\"], [1, \"ids-info\"], [1, \"roll-badge\"], [1, \"reg-number\"], [1, \"program-info\"], [1, \"program-badge\"], [1, \"department-name\"], [1, \"class-badge\"], [1, \"contact-info\"], [1, \"action-buttons\"], [\"mat-icon-button\", \"\", \"color\", \"primary\", \"matTooltip\", \"View\", 1, \"action-btn\", \"compact-btn\", 3, \"click\"], [\"mat-icon-button\", \"\", \"color\", \"accent\", \"matTooltip\", \"Edit\", 1, \"action-btn\", \"compact-btn\", 3, \"click\"], [\"mat-icon-button\", \"\", \"color\", \"warn\", \"matTooltip\", \"Delete\", 1, \"action-btn\", \"compact-btn\", 3, \"click\"], [1, \"loading-container\"], [\"diameter\", \"50\"], [1, \"loading-text\"], [1, \"empty-state\"], [1, \"empty-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/dashboard/admin/students/add-student\"], [1, \"upload-overlay\", 3, \"click\"], [1, \"upload-dialog\", 3, \"click\"], [1, \"upload-header\"], [\"mat-icon-button\", \"\", 3, \"click\"], [1, \"upload-content\"], [1, \"upload-instructions\"], [1, \"template-section\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 1, \"template-btn\", 3, \"click\"], [1, \"template-note\"], [1, \"file-upload-section\"], [1, \"file-input-wrapper\"], [\"type\", \"file\", \"accept\", \".xlsx,.xls\", 2, \"display\", \"none\", 3, \"change\"], [\"fileInput\", \"\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", 1, \"file-select-btn\", 3, \"disabled\", \"click\"], [\"class\", \"file-info\", 4, \"ngIf\"], [\"class\", \"upload-progress\", 4, \"ngIf\"], [\"class\", \"upload-results\", 4, \"ngIf\"], [\"class\", \"response-message\", 4, \"ngIf\"], [1, \"upload-actions\"], [\"mat-stroked-button\", \"\", 3, \"disabled\", \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"disabled\", \"click\"], [1, \"file-info\"], [1, \"upload-progress\"], [\"mode\", \"determinate\", 3, \"value\"], [1, \"upload-results\"], [1, \"results-summary\"], [1, \"result-stats\"], [1, \"stat\", \"success\"], [1, \"stat\", \"error\"], [1, \"stat\", \"total\"], [\"class\", \"failed-records\", 4, \"ngIf\"], [1, \"failed-records\"], [1, \"failed-list\"], [\"class\", \"failed-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"failed-item\"], [4, \"ngIf\"], [1, \"response-message\"]],\n    template: function AdminStudentListComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"h1\", 5)(6, \"mat-icon\", 6);\n        i0.ɵɵtext(7, \"school\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(8, \" Student Management \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"p\", 7);\n        i0.ɵɵtext(10, \"Manage and view all student records\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"div\", 8)(12, \"button\", 9);\n        i0.ɵɵlistener(\"click\", function AdminStudentListComponent_Template_button_click_12_listener() {\n          return ctx.downloadTemplate();\n        });\n        i0.ɵɵelementStart(13, \"mat-icon\");\n        i0.ɵɵtext(14, \"download\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(15, \" Download Template \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(16, \"button\", 10);\n        i0.ɵɵlistener(\"click\", function AdminStudentListComponent_Template_button_click_16_listener() {\n          return ctx.openUploadDialog();\n        });\n        i0.ɵɵelementStart(17, \"mat-icon\");\n        i0.ɵɵtext(18, \"upload_file\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(19, \" Bulk Upload \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"button\", 11)(21, \"mat-icon\");\n        i0.ɵɵtext(22, \"person_add\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(23, \" Add New Student \");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(24, \"mat-card\", 12)(25, \"mat-card-content\")(26, \"div\", 13)(27, \"div\", 14)(28, \"div\", 15)(29, \"mat-form-field\", 16)(30, \"mat-label\");\n        i0.ɵɵtext(31, \"Search Students\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(32, \"input\", 17);\n        i0.ɵɵlistener(\"ngModelChange\", function AdminStudentListComponent_Template_input_ngModelChange_32_listener($event) {\n          return ctx.searchQuery = $event;\n        })(\"keyup.enter\", function AdminStudentListComponent_Template_input_keyup_enter_32_listener() {\n          return ctx.onSearch();\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(33, \"mat-icon\", 18);\n        i0.ɵɵtext(34, \"search\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(35, \"div\", 19)(36, \"mat-form-field\", 16)(37, \"mat-label\");\n        i0.ɵɵtext(38, \"Program\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(39, \"mat-select\", 20);\n        i0.ɵɵlistener(\"ngModelChange\", function AdminStudentListComponent_Template_mat_select_ngModelChange_39_listener($event) {\n          return ctx.selectedProgram = $event;\n        })(\"selectionChange\", function AdminStudentListComponent_Template_mat_select_selectionChange_39_listener() {\n          return ctx.onProgramChange();\n        });\n        i0.ɵɵelementStart(40, \"mat-option\", 21);\n        i0.ɵɵtext(41, \"All\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(42, AdminStudentListComponent_mat_option_42_Template, 2, 2, \"mat-option\", 22);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(43, \"div\", 19)(44, \"mat-form-field\", 16)(45, \"mat-label\");\n        i0.ɵɵtext(46, \"Department\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(47, \"mat-select\", 23);\n        i0.ɵɵlistener(\"ngModelChange\", function AdminStudentListComponent_Template_mat_select_ngModelChange_47_listener($event) {\n          return ctx.selectedDepartment = $event;\n        })(\"selectionChange\", function AdminStudentListComponent_Template_mat_select_selectionChange_47_listener() {\n          return ctx.onDepartmentChange();\n        });\n        i0.ɵɵelementStart(48, \"mat-option\", 21);\n        i0.ɵɵtext(49, \"All\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(50, AdminStudentListComponent_mat_option_50_Template, 2, 2, \"mat-option\", 22);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(51, \"div\", 19)(52, \"mat-form-field\", 16)(53, \"mat-label\");\n        i0.ɵɵtext(54, \"Class\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(55, \"mat-select\", 23);\n        i0.ɵɵlistener(\"ngModelChange\", function AdminStudentListComponent_Template_mat_select_ngModelChange_55_listener($event) {\n          return ctx.selectedClass = $event;\n        })(\"selectionChange\", function AdminStudentListComponent_Template_mat_select_selectionChange_55_listener() {\n          return ctx.onClassChange();\n        });\n        i0.ɵɵelementStart(56, \"mat-option\", 21);\n        i0.ɵɵtext(57, \"All\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(58, AdminStudentListComponent_mat_option_58_Template, 2, 3, \"mat-option\", 22);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(59, \"div\", 24)(60, \"button\", 25);\n        i0.ɵɵlistener(\"click\", function AdminStudentListComponent_Template_button_click_60_listener() {\n          return ctx.applyFilters();\n        });\n        i0.ɵɵelementStart(61, \"mat-icon\");\n        i0.ɵɵtext(62, \"filter_list\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(63, \"span\", 26);\n        i0.ɵɵtext(64, \"Apply\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(65, \"button\", 27);\n        i0.ɵɵlistener(\"click\", function AdminStudentListComponent_Template_button_click_65_listener() {\n          return ctx.resetFilters();\n        });\n        i0.ɵɵelementStart(66, \"mat-icon\");\n        i0.ɵɵtext(67, \"refresh\");\n        i0.ɵɵelementEnd()()()()()()();\n        i0.ɵɵelementStart(68, \"mat-card\", 28)(69, \"mat-card-header\")(70, \"mat-card-title\")(71, \"mat-icon\");\n        i0.ɵɵtext(72, \"list\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(73);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(74, \"mat-card-content\")(75, \"div\", 29)(76, \"div\", 30)(77, \"table\", 31)(78, \"thead\")(79, \"tr\", 32)(80, \"th\", 33);\n        i0.ɵɵtext(81, \"Student\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(82, \"th\", 34);\n        i0.ɵɵtext(83, \"IDs\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(84, \"th\", 35);\n        i0.ɵɵtext(85, \"Program\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(86, \"th\", 36);\n        i0.ɵɵtext(87, \"Class\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(88, \"th\", 37);\n        i0.ɵɵtext(89, \"Contact\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(90, \"th\", 38);\n        i0.ɵɵtext(91, \"Actions\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(92, \"tbody\");\n        i0.ɵɵtemplate(93, AdminStudentListComponent_tr_93_Template, 39, 11, \"tr\", 39);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(94, AdminStudentListComponent_div_94_Template, 4, 0, \"div\", 40);\n        i0.ɵɵtemplate(95, AdminStudentListComponent_div_95_Template, 11, 0, \"div\", 41);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(96, \"div\", 42)(97, \"div\", 43)(98, \"span\", 44);\n        i0.ɵɵtext(99);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(100, \"div\", 45)(101, \"button\", 46);\n        i0.ɵɵlistener(\"click\", function AdminStudentListComponent_Template_button_click_101_listener() {\n          return ctx.previousPage();\n        });\n        i0.ɵɵelementStart(102, \"mat-icon\");\n        i0.ɵɵtext(103, \"chevron_left\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(104, \"span\", 47);\n        i0.ɵɵtext(105);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(106, \"button\", 48);\n        i0.ɵɵlistener(\"click\", function AdminStudentListComponent_Template_button_click_106_listener() {\n          return ctx.nextPage();\n        });\n        i0.ɵɵelementStart(107, \"mat-icon\");\n        i0.ɵɵtext(108, \"chevron_right\");\n        i0.ɵɵelementEnd()()()()()()()()();\n        i0.ɵɵtemplate(109, AdminStudentListComponent_div_109_Template, 53, 8, \"div\", 49);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(32);\n        i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngModel\", ctx.selectedProgram);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.programs);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngModel\", ctx.selectedDepartment)(\"disabled\", !ctx.selectedProgram);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.filteredDepartments);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngModel\", ctx.selectedClass)(\"disabled\", !ctx.selectedDepartment);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.filteredClasses);\n        i0.ɵɵadvance(15);\n        i0.ɵɵtextInterpolate1(\" Students (\", ctx.totalRecords, \") \");\n        i0.ɵɵadvance(20);\n        i0.ɵɵproperty(\"ngForOf\", ctx.students);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.students.length === 0);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate3(\" Showing \", (ctx.currentPage - 1) * ctx.itemsPerPage + 1, \" to \", ctx.Math.min(ctx.currentPage * ctx.itemsPerPage, ctx.totalRecords), \" of \", ctx.totalRecords, \" students \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"disabled\", ctx.currentPage === 1);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate2(\"Page \", ctx.currentPage, \" of \", ctx.totalPages, \"\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"disabled\", ctx.currentPage === ctx.totalPages || ctx.totalPages === 0);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.showUploadDialog);\n      }\n    },\n    dependencies: [i9.NgForOf, i9.NgIf, i2.RouterLink, i10.DefaultValueAccessor, i10.NgControlStatus, i10.NgModel, i11.MatOption, i12.MatButton, i12.MatIconButton, i13.MatCard, i13.MatCardContent, i13.MatCardHeader, i13.MatCardTitle, i14.MatIcon, i15.MatInput, i16.MatFormField, i16.MatLabel, i16.MatSuffix, i17.MatProgressBar, i18.MatProgressSpinner, i19.MatSelect, i20.MatTooltip],\n    styles: [\".student-list-container[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  min-height: 100vh;\\n  padding: 15px;\\n}\\n\\n\\n\\n.page-header[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  padding: 15px 0;\\n}\\n\\n.page-title[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-size: 1.8rem;\\n  font-weight: 600;\\n  margin: 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n\\n.title-icon[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  color: #3498db;\\n}\\n\\n.page-subtitle[_ngcontent-%COMP%] {\\n  color: #7f8c8d;\\n  margin: 5px 0 0 0;\\n  font-size: 0.9rem;\\n}\\n\\n.header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  flex-wrap: wrap;\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  min-width: 140px;\\n  height: 40px;\\n}\\n\\n.add-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);\\n  border: none;\\n  box-shadow: 0 4px 6px rgba(52, 152, 219, 0.3);\\n  transition: all 0.3s ease;\\n}\\n\\n.add-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 6px 10px rgba(52, 152, 219, 0.4);\\n}\\n\\n\\n\\n.stats-section[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.stat-card.compact-stat[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 8px;\\n  padding: 15px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  border-left: 3px solid #3498db;\\n  transition: transform 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  min-height: 70px;\\n}\\n\\n.stat-card.compact-stat[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\\n}\\n\\n.stat-card.compact-stat[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  color: #3498db;\\n  width: auto;\\n  height: auto;\\n  background: none;\\n}\\n\\n.stat-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 2px;\\n}\\n\\n.stat-number[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  color: #2c3e50;\\n  line-height: 1;\\n}\\n\\n.stat-label[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #7f8c8d;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n\\n\\n\\n.filters-card[_ngcontent-%COMP%], .upload-card[_ngcontent-%COMP%], .table-card[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  border: none;\\n}\\n\\n.filters-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%], .upload-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%], .table-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  border-radius: 8px 8px 0 0;\\n  padding: 15px;\\n}\\n\\n.filters-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%], .upload-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%], .table-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  color: #2c3e50;\\n  font-weight: 600;\\n  font-size: 1.1rem;\\n}\\n\\n.filters-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%], .upload-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  padding: 15px !important;\\n}\\n\\n.table-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  padding: 0 !important;\\n}\\n\\n\\n\\n.compact-filters[_ngcontent-%COMP%] {\\n  margin: 0;\\n}\\n\\n.compact-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.compact-field[_ngcontent-%COMP%]   .mat-form-field-wrapper[_ngcontent-%COMP%] {\\n  padding-bottom: 0;\\n}\\n\\n.compact-field[_ngcontent-%COMP%]   .mat-form-field-infix[_ngcontent-%COMP%] {\\n  padding: 8px 0;\\n  border-top: none;\\n}\\n\\n.compact-field[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n\\n.compact-btn[_ngcontent-%COMP%] {\\n  min-width: auto;\\n  padding: 6px 12px;\\n  font-size: 0.875rem;\\n}\\n\\n\\n\\n.compact-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: collapse;\\n  font-size: 0.875rem;\\n}\\n\\n.table-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);\\n  color: white;\\n}\\n\\n.table-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  padding: 12px 8px;\\n  text-align: left;\\n  font-weight: 600;\\n  font-size: 0.8rem;\\n  border: none;\\n}\\n\\n.student-row[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #ecf0f1;\\n  transition: background-color 0.2s ease;\\n}\\n\\n.student-row[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n\\n.student-row[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 10px 8px;\\n  vertical-align: middle;\\n}\\n\\n\\n\\n.col-student[_ngcontent-%COMP%] {\\n  min-width: 180px;\\n}\\n\\n.col-ids[_ngcontent-%COMP%] {\\n  width: 120px;\\n}\\n\\n.col-program[_ngcontent-%COMP%] {\\n  width: 140px;\\n}\\n\\n.col-class[_ngcontent-%COMP%] {\\n  width: 100px;\\n}\\n\\n.col-contact[_ngcontent-%COMP%] {\\n  width: 120px;\\n}\\n\\n.col-actions[_ngcontent-%COMP%] {\\n  width: 80px;\\n  text-align: center;\\n}\\n\\n\\n\\n.student-info[_ngcontent-%COMP%] {\\n  line-height: 1.3;\\n}\\n\\n.student-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2c3e50;\\n  font-size: 0.9rem;\\n}\\n\\n.student-email[_ngcontent-%COMP%] {\\n  color: #7f8c8d;\\n  font-size: 0.75rem;\\n}\\n\\n.mobile-info[_ngcontent-%COMP%] {\\n  margin-top: 3px;\\n}\\n\\n\\n\\n.ids-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 3px;\\n}\\n\\n.roll-badge[_ngcontent-%COMP%] {\\n  background: #3498db;\\n  color: white;\\n  padding: 2px 6px;\\n  border-radius: 10px;\\n  font-size: 0.7rem;\\n  font-weight: 600;\\n  align-self: flex-start;\\n}\\n\\n.reg-number[_ngcontent-%COMP%] {\\n  color: #7f8c8d;\\n  font-size: 0.7rem;\\n  font-family: 'Courier New', monospace;\\n}\\n\\n\\n\\n.program-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 3px;\\n}\\n\\n.program-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);\\n  color: white;\\n  padding: 2px 8px;\\n  border-radius: 10px;\\n  font-size: 0.7rem;\\n  font-weight: 600;\\n  align-self: flex-start;\\n}\\n\\n.department-name[_ngcontent-%COMP%] {\\n  color: #27ae60;\\n  font-size: 0.7rem;\\n  font-weight: 600;\\n}\\n\\n\\n\\n.class-badge[_ngcontent-%COMP%] {\\n  background: #f39c12;\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n}\\n\\n\\n\\n.contact-info[_ngcontent-%COMP%] {\\n  font-family: 'Courier New', monospace;\\n  color: #34495e;\\n  font-size: 0.8rem;\\n}\\n\\n\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 4px;\\n  justify-content: center;\\n}\\n\\n.action-btn.compact-btn[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  min-width: 32px;\\n  padding: 0;\\n}\\n\\n.action-btn.compact-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n\\n\\n\\n.loading-state[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n  text-align: center;\\n}\\n\\n.loading-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n  color: #7f8c8d;\\n}\\n\\n.empty-state[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  margin-bottom: 10px;\\n}\\n\\n.empty-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  color: #bdc3c7;\\n  margin-bottom: 15px;\\n}\\n\\n\\n\\n@media (max-width: 1200px) {\\n  .student-list-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .page-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 15px;\\n  }\\n\\n  .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  .header-actions[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: space-between;\\n  }\\n\\n  .stat-card.compact-stat[_ngcontent-%COMP%] {\\n    padding: 12px;\\n    min-height: 60px;\\n  }\\n\\n  .stat-number[_ngcontent-%COMP%] {\\n    font-size: 1.25rem;\\n  }\\n\\n  .stat-label[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n  }\\n\\n  .compact-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%] {\\n    margin: 0;\\n  }\\n\\n  .compact-filters[_ngcontent-%COMP%]   .col-6[_ngcontent-%COMP%], .compact-filters[_ngcontent-%COMP%]   .col-12[_ngcontent-%COMP%] {\\n    padding: 2px;\\n    margin-bottom: 8px;\\n  }\\n\\n  .compact-table[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n\\n  .table-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .student-row[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    padding: 8px 4px;\\n  }\\n\\n  .col-student[_ngcontent-%COMP%] {\\n    min-width: 140px;\\n  }\\n\\n  .student-name[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n\\n  .student-email[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n  }\\n}\\n\\n@media (max-width: 576px) {\\n  .student-list-container[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n\\n  .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.3rem;\\n  }\\n\\n  .title-icon[_ngcontent-%COMP%] {\\n    font-size: 1.3rem;\\n  }\\n\\n  .stat-card.compact-stat[_ngcontent-%COMP%] {\\n    padding: 10px;\\n    min-height: 55px;\\n    gap: 8px;\\n  }\\n\\n  .stat-card.compact-stat[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n    font-size: 1.2rem;\\n  }\\n\\n  .stat-number[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n\\n  .stat-label[_ngcontent-%COMP%] {\\n    font-size: 0.65rem;\\n  }\\n\\n  .compact-table[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n  }\\n\\n  .table-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n    padding: 6px 3px;\\n    font-size: 0.7rem;\\n  }\\n\\n  .student-row[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    padding: 6px 3px;\\n  }\\n\\n  .student-name[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n\\n  .student-email[_ngcontent-%COMP%] {\\n    font-size: 0.65rem;\\n  }\\n\\n  .action-btn.compact-btn[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n    min-width: 28px;\\n  }\\n\\n  .action-btn.compact-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n\\n  .class-badge[_ngcontent-%COMP%], .program-badge[_ngcontent-%COMP%], .roll-badge[_ngcontent-%COMP%] {\\n    font-size: 0.65rem;\\n    padding: 1px 4px;\\n  }\\n}\\n\\n\\n\\n.upload-section[_ngcontent-%COMP%] {\\n  padding: 20px 0;\\n}\\n\\n.select-file-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);\\n  color: white;\\n  border: none;\\n}\\n\\n.upload-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);\\n  border: none;\\n}\\n\\n\\n\\n.upload-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: rgba(0, 0, 0, 0.5);\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  z-index: 1000;\\n}\\n\\n.upload-dialog[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  width: 90%;\\n  max-width: 700px;\\n  max-height: 90vh;\\n  overflow-y: auto;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\\n}\\n\\n.upload-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 20px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.upload-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  color: #2c3e50;\\n}\\n\\n.upload-content[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n\\n.upload-instructions[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border-radius: 8px;\\n  padding: 15px;\\n  margin-bottom: 20px;\\n}\\n\\n.upload-instructions[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 10px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  color: #2c3e50;\\n}\\n\\n.upload-instructions[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding-left: 20px;\\n}\\n\\n.upload-instructions[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 5px;\\n}\\n\\n.template-section[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 30px;\\n  padding: 20px;\\n  background: #f0f8ff;\\n  border-radius: 8px;\\n}\\n\\n.template-btn[_ngcontent-%COMP%] {\\n  margin-bottom: 10px;\\n}\\n\\n.template-note[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  margin: 0;\\n  color: #666;\\n  font-size: 14px;\\n}\\n\\n.file-upload-section[_ngcontent-%COMP%] {\\n  border: 2px dashed #ddd;\\n  border-radius: 8px;\\n  padding: 30px;\\n  text-align: center;\\n  background: #fafafa;\\n}\\n\\n.file-input-wrapper[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.file-select-btn[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n}\\n\\n.file-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  color: #2c3e50;\\n  font-weight: 500;\\n}\\n\\n.upload-progress[_ngcontent-%COMP%] {\\n  margin: 20px 0;\\n}\\n\\n.upload-progress[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 10px 0 0 0;\\n  color: #666;\\n}\\n\\n.upload-results[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  text-align: left;\\n}\\n\\n.results-summary[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 15px 0;\\n  color: #2c3e50;\\n}\\n\\n.result-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n  margin-bottom: 20px;\\n}\\n\\n.stat[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 10px 15px;\\n  border-radius: 6px;\\n  font-weight: 500;\\n}\\n\\n.stat.success[_ngcontent-%COMP%] {\\n  background: #d4edda;\\n  color: #155724;\\n}\\n\\n.stat.error[_ngcontent-%COMP%] {\\n  background: #f8d7da;\\n  color: #721c24;\\n}\\n\\n.stat.total[_ngcontent-%COMP%] {\\n  background: #d1ecf1;\\n  color: #0c5460;\\n}\\n\\n.failed-records[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n}\\n\\n.failed-records[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  margin: 0 0 10px 0;\\n  color: #721c24;\\n}\\n\\n.failed-list[_ngcontent-%COMP%] {\\n  max-height: 200px;\\n  overflow-y: auto;\\n  border: 1px solid #f8d7da;\\n  border-radius: 4px;\\n  background: #f8d7da;\\n}\\n\\n.failed-item[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  border-bottom: 1px solid #f5c6cb;\\n  font-size: 14px;\\n  color: #721c24;\\n}\\n\\n.failed-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n\\n.failed-item[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 2px;\\n}\\n\\n.failed-item[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  color: #856404;\\n  font-style: italic;\\n}\\n\\n.response-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  padding: 15px;\\n  border-radius: 6px;\\n  margin-top: 15px;\\n}\\n\\n.response-message.success[_ngcontent-%COMP%] {\\n  background: #d4edda;\\n  color: #155724;\\n}\\n\\n.response-message.error[_ngcontent-%COMP%] {\\n  background: #f8d7da;\\n  color: #721c24;\\n}\\n\\n.response-message[_ngcontent-%COMP%]   mat-icon.success[_ngcontent-%COMP%] {\\n  color: #28a745;\\n}\\n\\n.response-message[_ngcontent-%COMP%]   mat-icon.error[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n\\n.upload-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  gap: 15px;\\n  padding: 20px;\\n  border-top: 1px solid #e0e0e0;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .upload-dialog[_ngcontent-%COMP%] {\\n    width: 95%;\\n    margin: 10px;\\n  }\\n\\n  .result-stats[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 10px;\\n  }\\n\\n  .upload-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["HttpEventType", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "program_r7", "_id", "ɵɵadvance", "ɵɵtextInterpolate1", "name", "dept_r8", "cls_r9", "ɵɵtextInterpolate2", "className", "section", "ɵɵlistener", "AdminStudentListComponent_tr_93_Template_button_click_30_listener", "restoredCtx", "ɵɵrestoreView", "_r13", "student_r10", "$implicit", "ctx_r12", "ɵɵnextContext", "ɵɵresetView", "viewStudent", "AdminStudentListComponent_tr_93_Template_button_click_33_listener", "ctx_r14", "editStudent", "AdminStudentListComponent_tr_93_Template_button_click_36_listener", "ctx_r15", "deleteStudent", "ɵɵtextInterpolate", "email", "rollNo", "program", "regNo", "department", "ctx_r3", "getClassName", "getClassSection", "contact", "ɵɵelement", "ctx_r17", "selected<PERSON><PERSON><PERSON><PERSON>", "ctx_r18", "uploadProgress", "failed_r23", "data", "ɵɵtemplate", "AdminStudentListComponent_div_109_div_44_div_20_div_4_small_4_Template", "row", "error", "AdminStudentListComponent_div_109_div_44_div_20_div_4_Template", "ctx_r21", "uploadResults", "failed", "AdminStudentListComponent_div_109_div_44_div_20_Template", "ctx_r19", "successful", "length", "totalProcessed", "ɵɵclassMap", "ctx_r20", "responseMessage", "AdminStudentListComponent_div_109_Template_div_click_0_listener", "_r27", "ctx_r26", "closeUploadDialog", "AdminStudentListComponent_div_109_Template_div_click_1_listener", "$event", "stopPropagation", "AdminStudentListComponent_div_109_Template_button_click_7_listener", "ctx_r29", "AdminStudentListComponent_div_109_Template_button_click_26_listener", "ctx_r30", "downloadTemplate", "AdminStudentListComponent_div_109_Template_input_change_36_listener", "ctx_r31", "onFileSelected", "AdminStudentListComponent_div_109_Template_button_click_38_listener", "_r16", "ɵɵreference", "click", "AdminStudentListComponent_div_109_div_42_Template", "AdminStudentListComponent_div_109_div_43_Template", "AdminStudentListComponent_div_109_div_44_Template", "AdminStudentListComponent_div_109_div_45_Template", "AdminStudentListComponent_div_109_Template_button_click_47_listener", "ctx_r33", "AdminStudentListComponent_div_109_Template_button_click_49_listener", "ctx_r34", "onUpload", "ctx_r6", "uploading", "selectedFile", "AdminStudentListComponent", "constructor", "userService", "router", "http", "programService", "departmentService", "classesService", "bulkUploadService", "snackBar", "searchQuery", "currentPage", "itemsPerPage", "totalPages", "totalRecords", "loading", "Math", "programs", "departments", "classes", "students", "filteredDepartments", "filteredClasses", "selectedProgram", "selectedDepartment", "selectedClass", "selectedSection", "showUploadDialog", "totalStudents", "activeStudents", "ngOnInit", "loadInitialData", "setupFormSubscriptions", "fetchStudents", "calculateStatistics", "onSearch", "onProgramChange", "filter", "dept", "programId", "applyFilters", "onDepartmentChange", "cls", "departmentId", "onClassChange", "clearFilters", "student", "isActive", "loadPrograms", "loadDepartments", "loadClasses", "getAllPrograms", "subscribe", "next", "response", "success", "console", "getAllDepartments", "getAllClasses", "filters", "role", "page", "limit", "classId", "search", "getAllUsers", "log", "users", "pagination", "total", "ceil", "showError", "event", "file", "target", "files", "allowedTypes", "includes", "type", "size", "downloadStudentTemplate", "blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "revokeObjectURL", "showSuccess", "openUploadDialog", "uploadStudentsBulkWithProgress", "UploadProgress", "round", "loaded", "Response", "body", "results", "message", "open", "duration", "panelClass", "navigate", "confirm", "deleteUser", "nextPage", "previousPage", "getDepartmentName", "getProgramName", "goToPage", "getPageNumbers", "pages", "maxPagesToShow", "startPage", "max", "floor", "endPage", "min", "i", "push", "resetFilters", "addnewstudent", "_", "ɵɵdirectiveInject", "i1", "UserService", "i2", "Router", "i3", "HttpClient", "i4", "ProgramService", "i5", "DepartmentService", "i6", "ClassesService", "i7", "BulkUploadService", "i8", "MatSnackBar", "_2", "selectors", "decls", "vars", "consts", "template", "AdminStudentListComponent_Template", "rf", "ctx", "AdminStudentListComponent_Template_button_click_12_listener", "AdminStudentListComponent_Template_button_click_16_listener", "AdminStudentListComponent_Template_input_ngModelChange_32_listener", "AdminStudentListComponent_Template_input_keyup_enter_32_listener", "AdminStudentListComponent_Template_mat_select_ngModelChange_39_listener", "AdminStudentListComponent_Template_mat_select_selectionChange_39_listener", "AdminStudentListComponent_mat_option_42_Template", "AdminStudentListComponent_Template_mat_select_ngModelChange_47_listener", "AdminStudentListComponent_Template_mat_select_selectionChange_47_listener", "AdminStudentListComponent_mat_option_50_Template", "AdminStudentListComponent_Template_mat_select_ngModelChange_55_listener", "AdminStudentListComponent_Template_mat_select_selectionChange_55_listener", "AdminStudentListComponent_mat_option_58_Template", "AdminStudentListComponent_Template_button_click_60_listener", "AdminStudentListComponent_Template_button_click_65_listener", "AdminStudentListComponent_tr_93_Template", "AdminStudentListComponent_div_94_Template", "AdminStudentListComponent_div_95_Template", "AdminStudentListComponent_Template_button_click_101_listener", "AdminStudentListComponent_Template_button_click_106_listener", "AdminStudentListComponent_div_109_Template", "ɵɵtextInterpolate3"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\students\\admin-student-list\\admin-student-list.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\students\\admin-student-list\\admin-student-list.component.html"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { UserService } from 'src/app/services/user.service';\r\nimport { ProgramService } from 'src/app/services/program.service';\r\nimport { DepartmentService } from 'src/app/services/department.service';\r\nimport { ClassesService } from 'src/app/services/classes.service';\r\nimport { BulkUploadService } from 'src/app/services/bulk-upload.service';\r\nimport { Student } from 'src/app/models/studnet';\r\nimport { Program, Department, Class, User } from 'src/app/models/user';\r\nimport { HttpEventType } from '@angular/common/http';\r\n\r\n@Component({\r\n  selector: 'app-admin-student-list',\r\n  templateUrl: './admin-student-list.component.html',\r\n  styleUrls: ['./admin-student-list.component.css']\r\n})\r\nexport class AdminStudentListComponent implements OnInit {\r\n  searchQuery: string = '';\r\n  currentPage: number = 1;\r\n  itemsPerPage: number = 10;\r\n  totalPages: number = 1;\r\n  totalRecords: number = 0;\r\n  loading: boolean = false;\r\n  Math = Math; // Make Math available in template\r\n\r\n  // Data arrays\r\n  programs: Program[] = [];\r\n  departments: Department[] = [];\r\n  classes: Class[] = [];\r\n  students: User[] = [];\r\n  filteredDepartments: Department[] = [];\r\n  filteredClasses: Class[] = [];\r\n\r\n  // Selected filters\r\n  selectedProgram: string = '';\r\n  selectedDepartment: string = '';\r\n  selectedClass: string = '';\r\n  selectedSection: string = '';\r\n\r\n  // Bulk upload properties\r\n  selectedFile: File | null = null;\r\n  selectedFileName: string = '';\r\n  responseMessage: string = '';\r\n  uploading: boolean = false;\r\n  uploadProgress: number = 0;\r\n  showUploadDialog: boolean = false;\r\n  uploadResults: any = null;\r\n\r\n  // Statistics\r\n  totalStudents: number = 0;\r\n  activeStudents: number = 0;\r\n\r\n  constructor(\r\n    private userService: UserService,\r\n    private router: Router,\r\n    private http: HttpClient,\r\n    private programService: ProgramService,\r\n    private departmentService: DepartmentService,\r\n    private classesService: ClassesService,\r\n    private bulkUploadService: BulkUploadService,\r\n    private snackBar: MatSnackBar\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.loadInitialData();\r\n    this.setupFormSubscriptions();\r\n    this.fetchStudents();\r\n    this.calculateStatistics();\r\n  }\r\n\r\n  // Search and filter methods\r\n  onSearch(): void {\r\n    this.currentPage = 1;\r\n    this.fetchStudents();\r\n  }\r\n\r\n  onProgramChange(): void {\r\n    // Filter departments by selected program\r\n    if (this.selectedProgram) {\r\n      this.filteredDepartments = this.departments.filter(dept => {\r\n        const programId = typeof dept.program === 'object' ? dept.program._id : dept.program;\r\n        return programId === this.selectedProgram;\r\n      });\r\n    } else {\r\n      this.filteredDepartments = this.departments;\r\n    }\r\n\r\n    // Reset dependent selections\r\n    this.selectedDepartment = '';\r\n    this.selectedClass = '';\r\n    this.filteredClasses = [];\r\n\r\n    this.applyFilters();\r\n  }\r\n\r\n  onDepartmentChange(): void {\r\n    // Filter classes by selected program and department\r\n    if (this.selectedProgram && this.selectedDepartment) {\r\n      this.filteredClasses = this.classes.filter(cls => {\r\n        const programId = typeof cls.program === 'object' ? cls.program._id : cls.program;\r\n        const departmentId = typeof cls.department === 'object' ? cls.department._id : cls.department;\r\n        return programId === this.selectedProgram && departmentId === this.selectedDepartment;\r\n      });\r\n    } else {\r\n      this.filteredClasses = [];\r\n    }\r\n\r\n    // Reset class selection\r\n    this.selectedClass = '';\r\n\r\n    this.applyFilters();\r\n  }\r\n\r\n  onClassChange(): void {\r\n    this.applyFilters();\r\n  }\r\n\r\n  applyFilters(): void {\r\n    this.currentPage = 1;\r\n    this.fetchStudents();\r\n  }\r\n\r\n  clearFilters(): void {\r\n    this.searchQuery = '';\r\n    this.selectedProgram = '';\r\n    this.selectedDepartment = '';\r\n    this.selectedClass = '';\r\n    this.selectedSection = '';\r\n    this.filteredDepartments = this.departments;\r\n    this.filteredClasses = this.classes;\r\n    this.fetchStudents();\r\n  }\r\n\r\n  calculateStatistics(): void {\r\n    // This would typically come from a dashboard API\r\n    // For now, we'll calculate from the current students array\r\n    this.totalStudents = this.students.length;\r\n    this.activeStudents = this.students.filter(student => student.isActive !== false).length;\r\n  }\r\n\r\n  loadInitialData(): void {\r\n    this.loadPrograms();\r\n    this.loadDepartments();\r\n    this.loadClasses();\r\n  }\r\n\r\n  loadPrograms(): void {\r\n    this.programService.getAllPrograms(true).subscribe({\r\n      next: (response: any) => {\r\n        if (response.success) {\r\n          this.programs = response.programs;\r\n        }\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Error loading programs:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  loadDepartments(): void {\r\n    this.departmentService.getAllDepartments().subscribe({\r\n      next: (response: any) => {\r\n        if (response.success) {\r\n          this.departments = response.departments;\r\n          this.filteredDepartments = this.departments;\r\n        }\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Error loading departments:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  loadClasses(): void {\r\n    this.classesService.getAllClasses().subscribe({\r\n      next: (response: any) => {\r\n        if (response.success) {\r\n          this.classes = response.classes;\r\n          this.filteredClasses = this.classes;\r\n        }\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Error loading classes:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  fetchStudents(): void {\r\n    this.loading = true;\r\n\r\n    // Build filters for API call\r\n    const filters: any = {\r\n      role: 'Student',\r\n      page: this.currentPage,\r\n      limit: this.itemsPerPage\r\n    };\r\n\r\n    if (this.selectedProgram) filters.program = this.selectedProgram;\r\n    if (this.selectedDepartment) filters.department = this.selectedDepartment;\r\n    if (this.selectedClass) filters.classId = this.selectedClass;\r\n    if (this.searchQuery) filters.search = this.searchQuery;\r\n\r\n    this.userService.getAllUsers(filters).subscribe({\r\n      next: (response: any) => {\r\n        console.log(\"Students response:\", response);\r\n        if (response.success) {\r\n          this.students = response.users || response.data || [];\r\n          this.totalRecords = response.pagination?.totalRecords || response.total || this.students.length;\r\n          this.totalPages = response.pagination?.totalPages || Math.ceil(this.totalRecords / this.itemsPerPage);\r\n          this.currentPage = response.pagination?.currentPage || this.currentPage;\r\n          this.calculateStatistics();\r\n        }\r\n        this.loading = false;\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Error fetching students:', error);\r\n        this.loading = false;\r\n        this.showError('Failed to load students');\r\n      }\r\n    });\r\n  }\r\n\r\n  setupFormSubscriptions(): void {\r\n    // Setup any form subscriptions if needed\r\n  }\r\n\r\n  // Bulk upload methods\r\n  onFileSelected(event: any): void {\r\n    const file = event.target.files[0];\r\n    if (file) {\r\n      // Validate file type\r\n      const allowedTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'];\r\n      if (!allowedTypes.includes(file.type)) {\r\n        this.showError('Please select a valid Excel file (.xlsx or .xls)');\r\n        return;\r\n      }\r\n\r\n      // Validate file size (5MB limit)\r\n      if (file.size > 5 * 1024 * 1024) {\r\n        this.showError('File size must be less than 5MB');\r\n        return;\r\n      }\r\n\r\n      this.selectedFile = file;\r\n      this.selectedFileName = file.name;\r\n      this.responseMessage = '';\r\n    }\r\n  }\r\n\r\n  downloadTemplate(): void {\r\n    this.bulkUploadService.downloadStudentTemplate().subscribe({\r\n      next: (blob) => {\r\n        const url = window.URL.createObjectURL(blob);\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.download = 'student_bulk_upload_template.xlsx';\r\n        link.click();\r\n        window.URL.revokeObjectURL(url);\r\n        this.showSuccess('Template downloaded successfully');\r\n      },\r\n      error: (error) => {\r\n        console.error('Error downloading template:', error);\r\n        this.showError('Failed to download template');\r\n      }\r\n    });\r\n  }\r\n\r\n  openUploadDialog(): void {\r\n    this.showUploadDialog = true;\r\n    this.selectedFile = null;\r\n    this.selectedFileName = '';\r\n    this.responseMessage = '';\r\n    this.uploadProgress = 0;\r\n    this.uploadResults = null;\r\n  }\r\n\r\n  closeUploadDialog(): void {\r\n    this.showUploadDialog = false;\r\n    this.selectedFile = null;\r\n    this.selectedFileName = '';\r\n    this.responseMessage = '';\r\n    this.uploadProgress = 0;\r\n    this.uploadResults = null;\r\n  }\r\n\r\n  onUpload(): void {\r\n    if (!this.selectedFile) {\r\n      this.showError('Please select a file before uploading');\r\n      return;\r\n    }\r\n\r\n    this.uploading = true;\r\n    this.uploadProgress = 0;\r\n    this.responseMessage = '';\r\n\r\n    this.bulkUploadService.uploadStudentsBulkWithProgress(this.selectedFile).subscribe({\r\n      next: (event) => {\r\n        if (event.type === HttpEventType.UploadProgress) {\r\n          this.uploadProgress = Math.round(100 * event.loaded / (event.total || 1));\r\n        } else if (event.type === HttpEventType.Response) {\r\n          this.uploading = false;\r\n          const response = event.body as any;\r\n\r\n          if (response.success) {\r\n            this.uploadResults = response.results;\r\n            this.responseMessage = response.message;\r\n            this.showSuccess(response.message);\r\n            this.fetchStudents(); // Refresh the student list\r\n          } else {\r\n            this.showError(response.message || 'Upload failed');\r\n          }\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.uploading = false;\r\n        console.error('Error uploading file:', error);\r\n        this.showError(error.error?.message || 'Upload failed. Please try again.');\r\n      }\r\n    });\r\n  }\r\n\r\n  // Helper methods\r\n  private showSuccess(message: string): void {\r\n    this.snackBar.open(message, 'Close', {\r\n      duration: 3000,\r\n      panelClass: ['success-snackbar']\r\n    });\r\n  }\r\n\r\n  private showError(message: string): void {\r\n    this.snackBar.open(message, 'Close', {\r\n      duration: 5000,\r\n      panelClass: ['error-snackbar']\r\n    });\r\n  }\r\n\r\n  // Student management methods\r\n  editStudent(student: User): void {\r\n    this.router.navigate(['/dashboard/admin/students/edit', student._id]);\r\n  }\r\n\r\n  viewStudent(student: User): void {\r\n    this.router.navigate(['/dashboard/admin/students/view', student._id]);\r\n  }\r\n\r\n  deleteStudent(student: User): void {\r\n    if (confirm(`Are you sure you want to delete ${student.name}?`)) {\r\n      this.userService.deleteUser(student._id).subscribe({\r\n        next: (response) => {\r\n          if (response.success) {\r\n            this.showSuccess('Student deleted successfully');\r\n            this.fetchStudents();\r\n          } else {\r\n            this.showError('Failed to delete student');\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('Error deleting student:', error);\r\n          this.showError('Failed to delete student');\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  // Pagination methods\r\n  nextPage(): void {\r\n    if (this.currentPage < this.totalPages) {\r\n      this.currentPage++;\r\n      this.fetchStudents();\r\n    }\r\n  }\r\n\r\n  previousPage(): void {\r\n    if (this.currentPage > 1) {\r\n      this.currentPage--;\r\n      this.fetchStudents();\r\n    }\r\n  }\r\n\r\n\r\n\r\n  // Helper methods for template\r\n  getClassName(student: User): string {\r\n    if (student?.classId && typeof student.classId === 'object') {\r\n      return (student.classId as any).className || 'N/A';\r\n    }\r\n    return 'N/A';\r\n  }\r\n\r\n  getClassSection(student: User): string {\r\n    if (student?.classId && typeof student.classId === 'object') {\r\n      return (student.classId as any).section || '';\r\n    }\r\n    return '';\r\n  }\r\n\r\n  // Additional helper methods\r\n  getDepartmentName(student: User): string {\r\n    if (student?.department && typeof student.department === 'object') {\r\n      return (student.department as any).name || 'N/A';\r\n    }\r\n    return 'N/A';\r\n  }\r\n\r\n  getProgramName(student: User): string {\r\n    if (student?.program && typeof student.program === 'object') {\r\n      return (student.program as any).name || 'N/A';\r\n    }\r\n    return 'N/A';\r\n  }\r\n\r\n  // Pagination helper methods\r\n  goToPage(page: number): void {\r\n    if (page >= 1 && page <= this.totalPages) {\r\n      this.currentPage = page;\r\n      this.fetchStudents();\r\n    }\r\n  }\r\n\r\n  getPageNumbers(): number[] {\r\n    const pages: number[] = [];\r\n    const maxPagesToShow = 5;\r\n    const startPage = Math.max(1, this.currentPage - Math.floor(maxPagesToShow / 2));\r\n    const endPage = Math.min(this.totalPages, startPage + maxPagesToShow - 1);\r\n\r\n    for (let i = startPage; i <= endPage; i++) {\r\n      pages.push(i);\r\n    }\r\n    return pages;\r\n  }\r\n\r\n  // Filter helper methods\r\n  resetFilters(): void {\r\n    this.searchQuery = '';\r\n    this.selectedProgram = '';\r\n    this.selectedDepartment = '';\r\n    this.selectedClass = '';\r\n    this.selectedSection = '';\r\n    this.filteredDepartments = this.departments;\r\n    this.filteredClasses = this.classes;\r\n    this.currentPage = 1;\r\n    this.fetchStudents();\r\n  }\r\n\r\n  addnewstudent(): void {\r\n    this.router.navigate(['/dashboard/admin/students/add-student']);\r\n  }\r\n}\r\n", "<div class=\"student-list-container\">\r\n  <div class=\"container-fluid\">\r\n\r\n    <!-- Header Section -->\r\n    <div class=\"page-header\">\r\n      <div class=\"d-flex justify-content-between align-items-center\">\r\n        <div class=\"header-content\">\r\n          <h1 class=\"page-title\">\r\n            <mat-icon class=\"title-icon\">school</mat-icon>\r\n            Student Management\r\n          </h1>\r\n          <p class=\"page-subtitle\">Manage and view all student records</p>\r\n        </div>\r\n        <div class=\"header-actions\">\r\n          <button mat-stroked-button color=\"accent\" (click)=\"downloadTemplate()\" class=\"action-btn\">\r\n            <mat-icon>download</mat-icon>\r\n            Download Template\r\n          </button>\r\n\r\n          <button mat-raised-button color=\"warn\" (click)=\"openUploadDialog()\" class=\"action-btn\">\r\n            <mat-icon>upload_file</mat-icon>\r\n            Bulk Upload\r\n          </button>\r\n\r\n          <button mat-raised-button color=\"primary\" routerLink=\"/dashboard/admin/students/add-student\" class=\"add-btn\">\r\n            <mat-icon>person_add</mat-icon>\r\n            Add New Student\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Compact Filters Section -->\r\n    <mat-card class=\"filters-card\">\r\n      <mat-card-content>\r\n        <div class=\"compact-filters\">\r\n          <div class=\"row g-2\">\r\n            <!-- Search Input -->\r\n            <div class=\"col-12 col-md-4\">\r\n              <mat-form-field appearance=\"outline\" class=\"compact-field\">\r\n                <mat-label>Search Students</mat-label>\r\n                <input matInput\r\n                       placeholder=\"Name, Roll No...\"\r\n                       [(ngModel)]=\"searchQuery\"\r\n                       (keyup.enter)=\"onSearch()\">\r\n                <mat-icon matSuffix>search</mat-icon>\r\n              </mat-form-field>\r\n            </div>\r\n\r\n            <!-- Program Filter -->\r\n            <div class=\"col-6 col-md-2\">\r\n              <mat-form-field appearance=\"outline\" class=\"compact-field\">\r\n                <mat-label>Program</mat-label>\r\n                <mat-select [(ngModel)]=\"selectedProgram\" (selectionChange)=\"onProgramChange()\">\r\n                  <mat-option value=\"\">All</mat-option>\r\n                  <mat-option *ngFor=\"let program of programs\" [value]=\"program._id\">\r\n                    {{ program.name }}\r\n                  </mat-option>\r\n                </mat-select>\r\n              </mat-form-field>\r\n            </div>\r\n\r\n            <!-- Department Filter -->\r\n            <div class=\"col-6 col-md-2\">\r\n              <mat-form-field appearance=\"outline\" class=\"compact-field\">\r\n                <mat-label>Department</mat-label>\r\n                <mat-select [(ngModel)]=\"selectedDepartment\"\r\n                           (selectionChange)=\"onDepartmentChange()\"\r\n                           [disabled]=\"!selectedProgram\">\r\n                  <mat-option value=\"\">All</mat-option>\r\n                  <mat-option *ngFor=\"let dept of filteredDepartments\" [value]=\"dept._id\">\r\n                    {{ dept.name }}\r\n                  </mat-option>\r\n                </mat-select>\r\n              </mat-form-field>\r\n            </div>\r\n\r\n            <!-- Class Filter -->\r\n            <div class=\"col-6 col-md-2\">\r\n              <mat-form-field appearance=\"outline\" class=\"compact-field\">\r\n                <mat-label>Class</mat-label>\r\n                <mat-select [(ngModel)]=\"selectedClass\"\r\n                           (selectionChange)=\"onClassChange()\"\r\n                           [disabled]=\"!selectedDepartment\">\r\n                  <mat-option value=\"\">All</mat-option>\r\n                  <mat-option *ngFor=\"let cls of filteredClasses\" [value]=\"cls._id\">\r\n                    {{ cls.className }}-{{ cls.section }}\r\n                  </mat-option>\r\n                </mat-select>\r\n              </mat-form-field>\r\n            </div>\r\n\r\n            <!-- Filter Actions -->\r\n            <div class=\"col-6 col-md-2 d-flex align-items-center gap-1\">\r\n              <button mat-raised-button color=\"primary\" (click)=\"applyFilters()\" class=\"compact-btn\">\r\n                <mat-icon>filter_list</mat-icon>\r\n                <span class=\"d-none d-lg-inline\">Apply</span>\r\n              </button>\r\n              <button mat-icon-button (click)=\"resetFilters()\" matTooltip=\"Reset\">\r\n                <mat-icon>refresh</mat-icon>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n \r\n\r\n    <!-- Compact Students Table -->\r\n    <mat-card class=\"table-card\">\r\n      <mat-card-header>\r\n        <mat-card-title>\r\n          <mat-icon>list</mat-icon>\r\n          Students ({{ totalRecords }})\r\n        </mat-card-title>\r\n      </mat-card-header>\r\n      <mat-card-content>\r\n        <div class=\"table-container\">\r\n          <div class=\"table-responsive\">\r\n            <table class=\"compact-table\">\r\n              <thead>\r\n                <tr class=\"table-header\">\r\n                  <th class=\"col-student\">Student</th>\r\n                  <th class=\"col-ids d-none d-md-table-cell\">IDs</th>\r\n                  <th class=\"col-program d-none d-lg-table-cell\">Program</th>\r\n                  <th class=\"col-class\">Class</th>\r\n                  <th class=\"col-contact d-none d-sm-table-cell\">Contact</th>\r\n                  <th class=\"col-actions\">Actions</th>\r\n                </tr>\r\n              </thead>\r\n              <tbody>\r\n                <tr *ngFor=\"let student of students; let i = index;\" class=\"student-row\">\r\n                  <td class=\"col-student\">\r\n                    <div class=\"student-info\">\r\n                      <div class=\"student-name\">{{ student.name }}</div>\r\n                      <small class=\"student-email\">{{ student.email }}</small>\r\n                      <!-- Mobile info -->\r\n                      <div class=\"mobile-info d-md-none\">\r\n                        <small class=\"text-muted\">\r\n                          Roll: {{ student?.rollNo || 'N/A' }} |\r\n                          {{ student?.program?.name || 'N/A' }}\r\n                        </small>\r\n                      </div>\r\n                    </div>\r\n                  </td>\r\n                  <td class=\"col-ids d-none d-md-table-cell\">\r\n                    <div class=\"ids-info\">\r\n                      <span class=\"roll-badge\">{{ student?.rollNo || 'N/A' }}</span>\r\n                      <small class=\"reg-number\">{{ student?.regNo || 'N/A' }}</small>\r\n                    </div>\r\n                  </td>\r\n                  <td class=\"col-program d-none d-lg-table-cell\">\r\n                    <div class=\"program-info\">\r\n                      <span class=\"program-badge\">{{ student?.program?.name || 'N/A' }}</span>\r\n                      <small class=\"department-name\">{{ student?.department?.name || 'N/A' }}</small>\r\n                    </div>\r\n                  </td>\r\n                  <td class=\"col-class\">\r\n                    <span class=\"class-badge\">\r\n                      {{ getClassName(student) }}-{{ getClassSection(student) }}\r\n                    </span>\r\n                  </td>\r\n                  <td class=\"col-contact d-none d-sm-table-cell\">\r\n                    <span class=\"contact-info\">{{ student?.contact || 'N/A' }}</span>\r\n                  </td>\r\n                  <td class=\"col-actions\">\r\n                    <div class=\"action-buttons\">\r\n                      <button mat-icon-button\r\n                              color=\"primary\"\r\n                              (click)=\"viewStudent(student)\"\r\n                              matTooltip=\"View\"\r\n                              class=\"action-btn compact-btn\">\r\n                        <mat-icon>visibility</mat-icon>\r\n                      </button>\r\n                      <button mat-icon-button\r\n                              color=\"accent\"\r\n                              (click)=\"editStudent(student)\"\r\n                              matTooltip=\"Edit\"\r\n                              class=\"action-btn compact-btn\">\r\n                        <mat-icon>edit</mat-icon>\r\n                      </button>\r\n                      <button mat-icon-button\r\n                              color=\"warn\"\r\n                              (click)=\"deleteStudent(student)\"\r\n                              matTooltip=\"Delete\"\r\n                              class=\"action-btn compact-btn\">\r\n                        <mat-icon>delete</mat-icon>\r\n                      </button>\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n\r\n            <!-- Loading State -->\r\n            <div *ngIf=\"loading\" class=\"loading-container\">\r\n              <mat-spinner diameter=\"50\"></mat-spinner>\r\n              <p class=\"loading-text\">Loading students...</p>\r\n            </div>\r\n\r\n            <!-- Empty State -->\r\n            <div *ngIf=\"!loading && students.length === 0\" class=\"empty-state\">\r\n              <mat-icon class=\"empty-icon\">school</mat-icon>\r\n              <h3>No Students Found</h3>\r\n              <p>Try adjusting your filters or add new students to get started.</p>\r\n              <button mat-raised-button color=\"primary\" routerLink=\"/dashboard/admin/students/add-student\">\r\n                <mat-icon>person_add</mat-icon>\r\n                Add First Student\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Pagination -->\r\n          <div class=\"pagination-container\">\r\n            <div class=\"pagination-info\">\r\n              <span class=\"text-muted\">\r\n                Showing {{ ((currentPage - 1) * itemsPerPage) + 1 }} to\r\n                {{ Math.min(currentPage * itemsPerPage, totalRecords) }} of\r\n                {{ totalRecords }} students\r\n              </span>\r\n            </div>\r\n            <div class=\"pagination-controls\">\r\n              <button mat-icon-button [disabled]=\"currentPage === 1\" (click)=\"previousPage()\" matTooltip=\"Previous Page\">\r\n                <mat-icon>chevron_left</mat-icon>\r\n              </button>\r\n              <span class=\"page-info\">Page {{ currentPage }} of {{ totalPages }}</span>\r\n              <button mat-icon-button [disabled]=\"currentPage === totalPages || totalPages === 0\" (click)=\"nextPage()\" matTooltip=\"Next Page\">\r\n                <mat-icon>chevron_right</mat-icon>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n  </div>\r\n</div>\r\n\r\n<!-- Bulk Upload Dialog -->\r\n<div class=\"upload-overlay\" *ngIf=\"showUploadDialog\" (click)=\"closeUploadDialog()\">\r\n  <div class=\"upload-dialog\" (click)=\"$event.stopPropagation()\">\r\n    <div class=\"upload-header\">\r\n      <h3>\r\n        <mat-icon>upload_file</mat-icon>\r\n        Bulk Upload Students\r\n      </h3>\r\n      <button mat-icon-button (click)=\"closeUploadDialog()\">\r\n        <mat-icon>close</mat-icon>\r\n      </button>\r\n    </div>\r\n\r\n    <div class=\"upload-content\">\r\n      <!-- Instructions -->\r\n      <div class=\"upload-instructions\">\r\n        <h4><mat-icon>info</mat-icon> Instructions</h4>\r\n        <ol>\r\n          <li>Download the Excel template using the \"Download Template\" button</li>\r\n          <li>Fill in all required student information in the template</li>\r\n          <li>Make sure all programs, departments, and classes exist in the system</li>\r\n          <li>Upload the completed Excel file</li>\r\n        </ol>\r\n      </div>\r\n\r\n      <!-- Template Download -->\r\n      <div class=\"template-section\">\r\n        <button mat-stroked-button color=\"primary\" (click)=\"downloadTemplate()\" class=\"template-btn\">\r\n          <mat-icon>download</mat-icon>\r\n          Download Excel Template\r\n        </button>\r\n        <p class=\"template-note\">\r\n          <mat-icon>lightbulb</mat-icon>\r\n          The template includes sample data and all available programs, departments, and classes for reference.\r\n        </p>\r\n      </div>\r\n\r\n      <!-- File Upload -->\r\n      <div class=\"file-upload-section\">\r\n        <div class=\"file-input-wrapper\">\r\n          <input type=\"file\"\r\n                 #fileInput\r\n                 (change)=\"onFileSelected($event)\"\r\n                 accept=\".xlsx,.xls\"\r\n                 style=\"display: none;\">\r\n\r\n          <button mat-raised-button\r\n                  color=\"accent\"\r\n                  (click)=\"fileInput.click()\"\r\n                  [disabled]=\"uploading\"\r\n                  class=\"file-select-btn\">\r\n            <mat-icon>attach_file</mat-icon>\r\n            Select Excel File\r\n          </button>\r\n\r\n          <div class=\"file-info\" *ngIf=\"selectedFileName\">\r\n            <mat-icon>description</mat-icon>\r\n            <span>{{ selectedFileName }}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Upload Progress -->\r\n        <div class=\"upload-progress\" *ngIf=\"uploading\">\r\n          <mat-progress-bar mode=\"determinate\" [value]=\"uploadProgress\"></mat-progress-bar>\r\n          <p>Uploading... {{ uploadProgress }}%</p>\r\n        </div>\r\n\r\n        <!-- Upload Results -->\r\n        <div class=\"upload-results\" *ngIf=\"uploadResults\">\r\n          <div class=\"results-summary\">\r\n            <h4>Upload Results</h4>\r\n            <div class=\"result-stats\">\r\n              <div class=\"stat success\">\r\n                <mat-icon>check_circle</mat-icon>\r\n                <span>{{ uploadResults.successful?.length || 0 }} Successful</span>\r\n              </div>\r\n              <div class=\"stat error\">\r\n                <mat-icon>error</mat-icon>\r\n                <span>{{ uploadResults.failed?.length || 0 }} Failed</span>\r\n              </div>\r\n              <div class=\"stat total\">\r\n                <mat-icon>assessment</mat-icon>\r\n                <span>{{ uploadResults.totalProcessed || 0 }} Total</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Failed Records -->\r\n          <div class=\"failed-records\" *ngIf=\"uploadResults.failed?.length > 0\">\r\n            <h5>Failed Records:</h5>\r\n            <div class=\"failed-list\">\r\n              <div class=\"failed-item\" *ngFor=\"let failed of uploadResults.failed\">\r\n                <strong>Row {{ failed.row }}:</strong> {{ failed.error }}\r\n                <small *ngIf=\"failed.data?.name\">({{ failed.data.name }})</small>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Response Message -->\r\n        <div class=\"response-message\" *ngIf=\"responseMessage\">\r\n          <mat-icon [class]=\"uploadResults?.successful?.length > 0 ? 'success' : 'error'\">\r\n            {{ uploadResults?.successful?.length > 0 ? 'check_circle' : 'error' }}\r\n          </mat-icon>\r\n          <span>{{ responseMessage }}</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"upload-actions\">\r\n      <button mat-stroked-button (click)=\"closeUploadDialog()\" [disabled]=\"uploading\">\r\n        Cancel\r\n      </button>\r\n      <button mat-raised-button\r\n              color=\"primary\"\r\n              (click)=\"onUpload()\"\r\n              [disabled]=\"!selectedFile || uploading\">\r\n        <mat-icon>upload</mat-icon>\r\n        {{ uploading ? 'Uploading...' : 'Upload Students' }}\r\n      </button>\r\n    </div>\r\n  </div>\r\n</div>"], "mappings": "AAWA,SAASA,aAAa,QAAQ,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;IC4ClCC,EAAA,CAAAC,cAAA,qBAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFgCH,EAAA,CAAAI,UAAA,UAAAC,UAAA,CAAAC,GAAA,CAAqB;IAChEN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,UAAA,CAAAI,IAAA,MACF;;;;;IAaAT,EAAA,CAAAC,cAAA,qBAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFwCH,EAAA,CAAAI,UAAA,UAAAM,OAAA,CAAAJ,GAAA,CAAkB;IACrEN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAE,OAAA,CAAAD,IAAA,MACF;;;;;IAaAT,EAAA,CAAAC,cAAA,qBAAkE;IAChED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFmCH,EAAA,CAAAI,UAAA,UAAAO,MAAA,CAAAL,GAAA,CAAiB;IAC/DN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAY,kBAAA,MAAAD,MAAA,CAAAE,SAAA,OAAAF,MAAA,CAAAG,OAAA,MACF;;;;;;IA4CFd,EAAA,CAAAC,cAAA,aAAyE;IAGzCD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAClDH,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAExDH,EAAA,CAAAC,cAAA,cAAmC;IAE/BD,EAAA,CAAAE,MAAA,GAEF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAIdH,EAAA,CAAAC,cAAA,cAA2C;IAEdD,EAAA,CAAAE,MAAA,IAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9DH,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAE,MAAA,IAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAGnEH,EAAA,CAAAC,cAAA,cAA+C;IAEfD,EAAA,CAAAE,MAAA,IAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxEH,EAAA,CAAAC,cAAA,iBAA+B;IAAAD,EAAA,CAAAE,MAAA,IAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAGnFH,EAAA,CAAAC,cAAA,cAAsB;IAElBD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,cAA+C;IAClBD,EAAA,CAAAE,MAAA,IAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEnEH,EAAA,CAAAC,cAAA,cAAwB;IAIZD,EAAA,CAAAe,UAAA,mBAAAC,kEAAA;MAAA,MAAAC,WAAA,GAAAjB,EAAA,CAAAkB,aAAA,CAAAC,IAAA;MAAA,MAAAC,WAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,OAAA,GAAAtB,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAF,OAAA,CAAAG,WAAA,CAAAL,WAAA,CAAoB;IAAA,EAAC;IAGpCpB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEjCH,EAAA,CAAAC,cAAA,kBAIuC;IAF/BD,EAAA,CAAAe,UAAA,mBAAAW,kEAAA;MAAA,MAAAT,WAAA,GAAAjB,EAAA,CAAAkB,aAAA,CAAAC,IAAA;MAAA,MAAAC,WAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAM,OAAA,GAAA3B,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAG,OAAA,CAAAC,WAAA,CAAAR,WAAA,CAAoB;IAAA,EAAC;IAGpCpB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE3BH,EAAA,CAAAC,cAAA,kBAIuC;IAF/BD,EAAA,CAAAe,UAAA,mBAAAc,kEAAA;MAAA,MAAAZ,WAAA,GAAAjB,EAAA,CAAAkB,aAAA,CAAAC,IAAA;MAAA,MAAAC,WAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAS,OAAA,GAAA9B,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAM,OAAA,CAAAC,aAAA,CAAAX,WAAA,CAAsB;IAAA,EAAC;IAGtCpB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IApDHH,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAgC,iBAAA,CAAAZ,WAAA,CAAAX,IAAA,CAAkB;IACfT,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAgC,iBAAA,CAAAZ,WAAA,CAAAa,KAAA,CAAmB;IAI5CjC,EAAA,CAAAO,SAAA,GAEF;IAFEP,EAAA,CAAAY,kBAAA,aAAAQ,WAAA,kBAAAA,WAAA,CAAAc,MAAA,oBAAAd,WAAA,kBAAAA,WAAA,CAAAe,OAAA,kBAAAf,WAAA,CAAAe,OAAA,CAAA1B,IAAA,gBAEF;IAMuBT,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAAgC,iBAAA,EAAAZ,WAAA,kBAAAA,WAAA,CAAAc,MAAA,WAA8B;IAC7BlC,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAgC,iBAAA,EAAAZ,WAAA,kBAAAA,WAAA,CAAAgB,KAAA,WAA6B;IAK3BpC,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAgC,iBAAA,EAAAZ,WAAA,kBAAAA,WAAA,CAAAe,OAAA,kBAAAf,WAAA,CAAAe,OAAA,CAAA1B,IAAA,WAAqC;IAClCT,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAgC,iBAAA,EAAAZ,WAAA,kBAAAA,WAAA,CAAAiB,UAAA,kBAAAjB,WAAA,CAAAiB,UAAA,CAAA5B,IAAA,WAAwC;IAKvET,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAY,kBAAA,MAAA0B,MAAA,CAAAC,YAAA,CAAAnB,WAAA,QAAAkB,MAAA,CAAAE,eAAA,CAAApB,WAAA,OACF;IAG2BpB,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAgC,iBAAA,EAAAZ,WAAA,kBAAAA,WAAA,CAAAqB,OAAA,WAA+B;;;;;IAgClEzC,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAA0C,SAAA,sBAAyC;IACzC1C,EAAA,CAAAC,cAAA,YAAwB;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAIjDH,EAAA,CAAAC,cAAA,cAAmE;IACpCD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9CH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,qEAA8D;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACrEH,EAAA,CAAAC,cAAA,iBAA6F;IACjFD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAoFbH,EAAA,CAAAC,cAAA,cAAgD;IACpCD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA7BH,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAgC,iBAAA,CAAAW,OAAA,CAAAC,gBAAA,CAAsB;;;;;IAKhC5C,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAA0C,SAAA,2BAAiF;IACjF1C,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADJH,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAI,UAAA,UAAAyC,OAAA,CAAAC,cAAA,CAAwB;IAC1D9C,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAQ,kBAAA,kBAAAqC,OAAA,CAAAC,cAAA,MAAkC;;;;;IA6B/B9C,EAAA,CAAAC,cAAA,YAAiC;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAhCH,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAQ,kBAAA,MAAAuC,UAAA,CAAAC,IAAA,CAAAvC,IAAA,MAAwB;;;;;IAF3DT,EAAA,CAAAC,cAAA,eAAqE;IAC3DD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GACvC;IAAAF,EAAA,CAAAiD,UAAA,IAAAC,sEAAA,qBAAiE;IACnElD,EAAA,CAAAG,YAAA,EAAM;;;;IAFIH,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAQ,kBAAA,SAAAuC,UAAA,CAAAI,GAAA,MAAqB;IAAUnD,EAAA,CAAAO,SAAA,GACvC;IADuCP,EAAA,CAAAQ,kBAAA,MAAAuC,UAAA,CAAAK,KAAA,MACvC;IAAQpD,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAI,UAAA,SAAA2C,UAAA,CAAAC,IAAA,kBAAAD,UAAA,CAAAC,IAAA,CAAAvC,IAAA,CAAuB;;;;;IALrCT,EAAA,CAAAC,cAAA,eAAqE;IAC/DD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxBH,EAAA,CAAAC,cAAA,eAAyB;IACvBD,EAAA,CAAAiD,UAAA,IAAAI,8DAAA,mBAGM;IACRrD,EAAA,CAAAG,YAAA,EAAM;;;;IAJwCH,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAI,UAAA,YAAAkD,OAAA,CAAAC,aAAA,CAAAC,MAAA,CAAuB;;;;;IAvBzExD,EAAA,CAAAC,cAAA,cAAkD;IAE1CD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,eAA0B;IAEZD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACjCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAsD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAErEH,EAAA,CAAAC,cAAA,gBAAwB;IACZD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAA8C;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE7DH,EAAA,CAAAC,cAAA,gBAAwB;IACZD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAMhEH,EAAA,CAAAiD,UAAA,KAAAQ,wDAAA,mBAQM;IACRzD,EAAA,CAAAG,YAAA,EAAM;;;;IAvBQH,EAAA,CAAAO,SAAA,GAAsD;IAAtDP,EAAA,CAAAQ,kBAAA,MAAAkD,OAAA,CAAAH,aAAA,CAAAI,UAAA,kBAAAD,OAAA,CAAAH,aAAA,CAAAI,UAAA,CAAAC,MAAA,sBAAsD;IAItD5D,EAAA,CAAAO,SAAA,GAA8C;IAA9CP,EAAA,CAAAQ,kBAAA,MAAAkD,OAAA,CAAAH,aAAA,CAAAC,MAAA,kBAAAE,OAAA,CAAAH,aAAA,CAAAC,MAAA,CAAAI,MAAA,kBAA8C;IAI9C5D,EAAA,CAAAO,SAAA,GAA6C;IAA7CP,EAAA,CAAAQ,kBAAA,KAAAkD,OAAA,CAAAH,aAAA,CAAAM,cAAA,gBAA6C;IAM5B7D,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAAI,UAAA,UAAAsD,OAAA,CAAAH,aAAA,CAAAC,MAAA,kBAAAE,OAAA,CAAAH,aAAA,CAAAC,MAAA,CAAAI,MAAA,MAAsC;;;;;IAYrE5D,EAAA,CAAAC,cAAA,eAAsD;IAElDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACXH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAHxBH,EAAA,CAAAO,SAAA,GAAqE;IAArEP,EAAA,CAAA8D,UAAA,EAAAC,OAAA,CAAAR,aAAA,kBAAAQ,OAAA,CAAAR,aAAA,CAAAI,UAAA,kBAAAI,OAAA,CAAAR,aAAA,CAAAI,UAAA,CAAAC,MAAA,4BAAqE;IAC7E5D,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,OAAAuD,OAAA,CAAAR,aAAA,kBAAAQ,OAAA,CAAAR,aAAA,CAAAI,UAAA,kBAAAI,OAAA,CAAAR,aAAA,CAAAI,UAAA,CAAAC,MAAA,sCACF;IACM5D,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAgC,iBAAA,CAAA+B,OAAA,CAAAC,eAAA,CAAqB;;;;;;IAvGrChE,EAAA,CAAAC,cAAA,cAAmF;IAA9BD,EAAA,CAAAe,UAAA,mBAAAkD,gEAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAAgD,IAAA;MAAA,MAAAC,OAAA,GAAAnE,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAA2C,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IAChFpE,EAAA,CAAAC,cAAA,cAA8D;IAAnCD,EAAA,CAAAe,UAAA,mBAAAsD,gEAAAC,MAAA;MAAA,OAASA,MAAA,CAAAC,eAAA,EAAwB;IAAA,EAAC;IAC3DvE,EAAA,CAAAC,cAAA,cAA2B;IAEbD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,iBAAsD;IAA9BD,EAAA,CAAAe,UAAA,mBAAAyD,mEAAA;MAAAxE,EAAA,CAAAkB,aAAA,CAAAgD,IAAA;MAAA,MAAAO,OAAA,GAAAzE,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAiD,OAAA,CAAAL,iBAAA,EAAmB;IAAA,EAAC;IACnDpE,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAI9BH,EAAA,CAAAC,cAAA,eAA4B;IAGVD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,qBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/CH,EAAA,CAAAC,cAAA,UAAI;IACED,EAAA,CAAAE,MAAA,0EAAgE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzEH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,gEAAwD;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjEH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,4EAAoE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7EH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,uCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAK5CH,EAAA,CAAAC,cAAA,eAA8B;IACeD,EAAA,CAAAe,UAAA,mBAAA2D,oEAAA;MAAA1E,EAAA,CAAAkB,aAAA,CAAAgD,IAAA;MAAA,MAAAS,OAAA,GAAA3E,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAmD,OAAA,CAAAC,gBAAA,EAAkB;IAAA,EAAC;IACrE5E,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,aAAyB;IACbD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAE,MAAA,+GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAINH,EAAA,CAAAC,cAAA,eAAiC;IAItBD,EAAA,CAAAe,UAAA,oBAAA8D,oEAAAP,MAAA;MAAAtE,EAAA,CAAAkB,aAAA,CAAAgD,IAAA;MAAA,MAAAY,OAAA,GAAA9E,EAAA,CAAAuB,aAAA;MAAA,OAAUvB,EAAA,CAAAwB,WAAA,CAAAsD,OAAA,CAAAC,cAAA,CAAAT,MAAA,CAAsB;IAAA,EAAC;IAFxCtE,EAAA,CAAAG,YAAA,EAI8B;IAE9BH,EAAA,CAAAC,cAAA,kBAIgC;IAFxBD,EAAA,CAAAe,UAAA,mBAAAiE,oEAAA;MAAAhF,EAAA,CAAAkB,aAAA,CAAAgD,IAAA;MAAA,MAAAe,IAAA,GAAAjF,EAAA,CAAAkF,WAAA;MAAA,OAASlF,EAAA,CAAAwB,WAAA,CAAAyD,IAAA,CAAAE,KAAA,EAAiB;IAAA,EAAC;IAGjCnF,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAiD,UAAA,KAAAmC,iDAAA,kBAGM;IACRpF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAiD,UAAA,KAAAoC,iDAAA,kBAGM;IAGNrF,EAAA,CAAAiD,UAAA,KAAAqC,iDAAA,mBA6BM;IAGNtF,EAAA,CAAAiD,UAAA,KAAAsC,iDAAA,kBAKM;IACRvF,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,eAA4B;IACCD,EAAA,CAAAe,UAAA,mBAAAyE,oEAAA;MAAAxF,EAAA,CAAAkB,aAAA,CAAAgD,IAAA;MAAA,MAAAuB,OAAA,GAAAzF,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAiE,OAAA,CAAArB,iBAAA,EAAmB;IAAA,EAAC;IACtDpE,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAGgD;IADxCD,EAAA,CAAAe,UAAA,mBAAA2E,oEAAA;MAAA1F,EAAA,CAAAkB,aAAA,CAAAgD,IAAA;MAAA,MAAAyB,OAAA,GAAA3F,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAmE,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IAE1B5F,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAtEGH,EAAA,CAAAO,SAAA,IAAsB;IAAtBP,EAAA,CAAAI,UAAA,aAAAyF,MAAA,CAAAC,SAAA,CAAsB;IAMN9F,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAI,UAAA,SAAAyF,MAAA,CAAAjD,gBAAA,CAAsB;IAOlB5C,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAI,UAAA,SAAAyF,MAAA,CAAAC,SAAA,CAAe;IAMhB9F,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAI,UAAA,SAAAyF,MAAA,CAAAtC,aAAA,CAAmB;IAgCjBvD,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAI,UAAA,SAAAyF,MAAA,CAAA7B,eAAA,CAAqB;IAUGhE,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAI,UAAA,aAAAyF,MAAA,CAAAC,SAAA,CAAsB;IAMvE9F,EAAA,CAAAO,SAAA,GAAuC;IAAvCP,EAAA,CAAAI,UAAA,cAAAyF,MAAA,CAAAE,YAAA,IAAAF,MAAA,CAAAC,SAAA,CAAuC;IAE7C9F,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAqF,MAAA,CAAAC,SAAA,2CACF;;;ADlVN,OAAM,MAAOE,yBAAyB;EAoCpCC,YACUC,WAAwB,EACxBC,MAAc,EACdC,IAAgB,EAChBC,cAA8B,EAC9BC,iBAAoC,EACpCC,cAA8B,EAC9BC,iBAAoC,EACpCC,QAAqB;IAPrB,KAAAP,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,QAAQ,GAARA,QAAQ;IA3ClB,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,UAAU,GAAW,CAAC;IACtB,KAAAC,YAAY,GAAW,CAAC;IACxB,KAAAC,OAAO,GAAY,KAAK;IACxB,KAAAC,IAAI,GAAGA,IAAI,CAAC,CAAC;IAEb;IACA,KAAAC,QAAQ,GAAc,EAAE;IACxB,KAAAC,WAAW,GAAiB,EAAE;IAC9B,KAAAC,OAAO,GAAY,EAAE;IACrB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,mBAAmB,GAAiB,EAAE;IACtC,KAAAC,eAAe,GAAY,EAAE;IAE7B;IACA,KAAAC,eAAe,GAAW,EAAE;IAC5B,KAAAC,kBAAkB,GAAW,EAAE;IAC/B,KAAAC,aAAa,GAAW,EAAE;IAC1B,KAAAC,eAAe,GAAW,EAAE;IAE5B;IACA,KAAA3B,YAAY,GAAgB,IAAI;IAChC,KAAAnD,gBAAgB,GAAW,EAAE;IAC7B,KAAAoB,eAAe,GAAW,EAAE;IAC5B,KAAA8B,SAAS,GAAY,KAAK;IAC1B,KAAAhD,cAAc,GAAW,CAAC;IAC1B,KAAA6E,gBAAgB,GAAY,KAAK;IACjC,KAAApE,aAAa,GAAQ,IAAI;IAEzB;IACA,KAAAqE,aAAa,GAAW,CAAC;IACzB,KAAAC,cAAc,GAAW,CAAC;EAWvB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,sBAAsB,EAAE;IAC7B,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEA;EACAC,QAAQA,CAAA;IACN,IAAI,CAACxB,WAAW,GAAG,CAAC;IACpB,IAAI,CAACsB,aAAa,EAAE;EACtB;EAEAG,eAAeA,CAAA;IACb;IACA,IAAI,IAAI,CAACb,eAAe,EAAE;MACxB,IAAI,CAACF,mBAAmB,GAAG,IAAI,CAACH,WAAW,CAACmB,MAAM,CAACC,IAAI,IAAG;QACxD,MAAMC,SAAS,GAAG,OAAOD,IAAI,CAACnG,OAAO,KAAK,QAAQ,GAAGmG,IAAI,CAACnG,OAAO,CAAC7B,GAAG,GAAGgI,IAAI,CAACnG,OAAO;QACpF,OAAOoG,SAAS,KAAK,IAAI,CAAChB,eAAe;MAC3C,CAAC,CAAC;KACH,MAAM;MACL,IAAI,CAACF,mBAAmB,GAAG,IAAI,CAACH,WAAW;;IAG7C;IACA,IAAI,CAACM,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACH,eAAe,GAAG,EAAE;IAEzB,IAAI,CAACkB,YAAY,EAAE;EACrB;EAEAC,kBAAkBA,CAAA;IAChB;IACA,IAAI,IAAI,CAAClB,eAAe,IAAI,IAAI,CAACC,kBAAkB,EAAE;MACnD,IAAI,CAACF,eAAe,GAAG,IAAI,CAACH,OAAO,CAACkB,MAAM,CAACK,GAAG,IAAG;QAC/C,MAAMH,SAAS,GAAG,OAAOG,GAAG,CAACvG,OAAO,KAAK,QAAQ,GAAGuG,GAAG,CAACvG,OAAO,CAAC7B,GAAG,GAAGoI,GAAG,CAACvG,OAAO;QACjF,MAAMwG,YAAY,GAAG,OAAOD,GAAG,CAACrG,UAAU,KAAK,QAAQ,GAAGqG,GAAG,CAACrG,UAAU,CAAC/B,GAAG,GAAGoI,GAAG,CAACrG,UAAU;QAC7F,OAAOkG,SAAS,KAAK,IAAI,CAAChB,eAAe,IAAIoB,YAAY,KAAK,IAAI,CAACnB,kBAAkB;MACvF,CAAC,CAAC;KACH,MAAM;MACL,IAAI,CAACF,eAAe,GAAG,EAAE;;IAG3B;IACA,IAAI,CAACG,aAAa,GAAG,EAAE;IAEvB,IAAI,CAACe,YAAY,EAAE;EACrB;EAEAI,aAAaA,CAAA;IACX,IAAI,CAACJ,YAAY,EAAE;EACrB;EAEAA,YAAYA,CAAA;IACV,IAAI,CAAC7B,WAAW,GAAG,CAAC;IACpB,IAAI,CAACsB,aAAa,EAAE;EACtB;EAEAY,YAAYA,CAAA;IACV,IAAI,CAACnC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACa,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACL,mBAAmB,GAAG,IAAI,CAACH,WAAW;IAC3C,IAAI,CAACI,eAAe,GAAG,IAAI,CAACH,OAAO;IACnC,IAAI,CAACc,aAAa,EAAE;EACtB;EAEAC,mBAAmBA,CAAA;IACjB;IACA;IACA,IAAI,CAACN,aAAa,GAAG,IAAI,CAACR,QAAQ,CAACxD,MAAM;IACzC,IAAI,CAACiE,cAAc,GAAG,IAAI,CAACT,QAAQ,CAACiB,MAAM,CAACS,OAAO,IAAIA,OAAO,CAACC,QAAQ,KAAK,KAAK,CAAC,CAACnF,MAAM;EAC1F;EAEAmE,eAAeA,CAAA;IACb,IAAI,CAACiB,YAAY,EAAE;IACnB,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAF,YAAYA,CAAA;IACV,IAAI,CAAC3C,cAAc,CAAC8C,cAAc,CAAC,IAAI,CAAC,CAACC,SAAS,CAAC;MACjDC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACtC,QAAQ,GAAGqC,QAAQ,CAACrC,QAAQ;;MAErC,CAAC;MACD7D,KAAK,EAAGA,KAAU,IAAI;QACpBoG,OAAO,CAACpG,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD;KACD,CAAC;EACJ;EAEA6F,eAAeA,CAAA;IACb,IAAI,CAAC3C,iBAAiB,CAACmD,iBAAiB,EAAE,CAACL,SAAS,CAAC;MACnDC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACrC,WAAW,GAAGoC,QAAQ,CAACpC,WAAW;UACvC,IAAI,CAACG,mBAAmB,GAAG,IAAI,CAACH,WAAW;;MAE/C,CAAC;MACD9D,KAAK,EAAGA,KAAU,IAAI;QACpBoG,OAAO,CAACpG,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MACpD;KACD,CAAC;EACJ;EAEA8F,WAAWA,CAAA;IACT,IAAI,CAAC3C,cAAc,CAACmD,aAAa,EAAE,CAACN,SAAS,CAAC;MAC5CC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACpC,OAAO,GAAGmC,QAAQ,CAACnC,OAAO;UAC/B,IAAI,CAACG,eAAe,GAAG,IAAI,CAACH,OAAO;;MAEvC,CAAC;MACD/D,KAAK,EAAGA,KAAU,IAAI;QACpBoG,OAAO,CAACpG,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD;KACD,CAAC;EACJ;EAEA6E,aAAaA,CAAA;IACX,IAAI,CAAClB,OAAO,GAAG,IAAI;IAEnB;IACA,MAAM4C,OAAO,GAAQ;MACnBC,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,IAAI,CAAClD,WAAW;MACtBmD,KAAK,EAAE,IAAI,CAAClD;KACb;IAED,IAAI,IAAI,CAACW,eAAe,EAAEoC,OAAO,CAACxH,OAAO,GAAG,IAAI,CAACoF,eAAe;IAChE,IAAI,IAAI,CAACC,kBAAkB,EAAEmC,OAAO,CAACtH,UAAU,GAAG,IAAI,CAACmF,kBAAkB;IACzE,IAAI,IAAI,CAACC,aAAa,EAAEkC,OAAO,CAACI,OAAO,GAAG,IAAI,CAACtC,aAAa;IAC5D,IAAI,IAAI,CAACf,WAAW,EAAEiD,OAAO,CAACK,MAAM,GAAG,IAAI,CAACtD,WAAW;IAEvD,IAAI,CAACR,WAAW,CAAC+D,WAAW,CAACN,OAAO,CAAC,CAACP,SAAS,CAAC;MAC9CC,IAAI,EAAGC,QAAa,IAAI;QACtBE,OAAO,CAACU,GAAG,CAAC,oBAAoB,EAAEZ,QAAQ,CAAC;QAC3C,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACnC,QAAQ,GAAGkC,QAAQ,CAACa,KAAK,IAAIb,QAAQ,CAACtG,IAAI,IAAI,EAAE;UACrD,IAAI,CAAC8D,YAAY,GAAGwC,QAAQ,CAACc,UAAU,EAAEtD,YAAY,IAAIwC,QAAQ,CAACe,KAAK,IAAI,IAAI,CAACjD,QAAQ,CAACxD,MAAM;UAC/F,IAAI,CAACiD,UAAU,GAAGyC,QAAQ,CAACc,UAAU,EAAEvD,UAAU,IAAIG,IAAI,CAACsD,IAAI,CAAC,IAAI,CAACxD,YAAY,GAAG,IAAI,CAACF,YAAY,CAAC;UACrG,IAAI,CAACD,WAAW,GAAG2C,QAAQ,CAACc,UAAU,EAAEzD,WAAW,IAAI,IAAI,CAACA,WAAW;UACvE,IAAI,CAACuB,mBAAmB,EAAE;;QAE5B,IAAI,CAACnB,OAAO,GAAG,KAAK;MACtB,CAAC;MACD3D,KAAK,EAAGA,KAAU,IAAI;QACpBoG,OAAO,CAACpG,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,IAAI,CAAC2D,OAAO,GAAG,KAAK;QACpB,IAAI,CAACwD,SAAS,CAAC,yBAAyB,CAAC;MAC3C;KACD,CAAC;EACJ;EAEAvC,sBAAsBA,CAAA;IACpB;EAAA;EAGF;EACAjD,cAAcA,CAACyF,KAAU;IACvB,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR;MACA,MAAMG,YAAY,GAAG,CAAC,mEAAmE,EAAE,0BAA0B,CAAC;MACtH,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACJ,IAAI,CAACK,IAAI,CAAC,EAAE;QACrC,IAAI,CAACP,SAAS,CAAC,kDAAkD,CAAC;QAClE;;MAGF;MACA,IAAIE,IAAI,CAACM,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;QAC/B,IAAI,CAACR,SAAS,CAAC,iCAAiC,CAAC;QACjD;;MAGF,IAAI,CAACxE,YAAY,GAAG0E,IAAI;MACxB,IAAI,CAAC7H,gBAAgB,GAAG6H,IAAI,CAAChK,IAAI;MACjC,IAAI,CAACuD,eAAe,GAAG,EAAE;;EAE7B;EAEAY,gBAAgBA,CAAA;IACd,IAAI,CAAC4B,iBAAiB,CAACwE,uBAAuB,EAAE,CAAC5B,SAAS,CAAC;MACzDC,IAAI,EAAG4B,IAAI,IAAI;QACb,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;QAC5C,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;QACfI,IAAI,CAACI,QAAQ,GAAG,mCAAmC;QACnDJ,IAAI,CAACnG,KAAK,EAAE;QACZgG,MAAM,CAACC,GAAG,CAACO,eAAe,CAACT,GAAG,CAAC;QAC/B,IAAI,CAACU,WAAW,CAAC,kCAAkC,CAAC;MACtD,CAAC;MACDxI,KAAK,EAAGA,KAAK,IAAI;QACfoG,OAAO,CAACpG,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,IAAI,CAACmH,SAAS,CAAC,6BAA6B,CAAC;MAC/C;KACD,CAAC;EACJ;EAEAsB,gBAAgBA,CAAA;IACd,IAAI,CAAClE,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAAC5B,YAAY,GAAG,IAAI;IACxB,IAAI,CAACnD,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACoB,eAAe,GAAG,EAAE;IACzB,IAAI,CAAClB,cAAc,GAAG,CAAC;IACvB,IAAI,CAACS,aAAa,GAAG,IAAI;EAC3B;EAEAa,iBAAiBA,CAAA;IACf,IAAI,CAACuD,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAC5B,YAAY,GAAG,IAAI;IACxB,IAAI,CAACnD,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACoB,eAAe,GAAG,EAAE;IACzB,IAAI,CAAClB,cAAc,GAAG,CAAC;IACvB,IAAI,CAACS,aAAa,GAAG,IAAI;EAC3B;EAEAqC,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACG,YAAY,EAAE;MACtB,IAAI,CAACwE,SAAS,CAAC,uCAAuC,CAAC;MACvD;;IAGF,IAAI,CAACzE,SAAS,GAAG,IAAI;IACrB,IAAI,CAAChD,cAAc,GAAG,CAAC;IACvB,IAAI,CAACkB,eAAe,GAAG,EAAE;IAEzB,IAAI,CAACwC,iBAAiB,CAACsF,8BAA8B,CAAC,IAAI,CAAC/F,YAAY,CAAC,CAACqD,SAAS,CAAC;MACjFC,IAAI,EAAGmB,KAAK,IAAI;QACd,IAAIA,KAAK,CAACM,IAAI,KAAK/K,aAAa,CAACgM,cAAc,EAAE;UAC/C,IAAI,CAACjJ,cAAc,GAAGkE,IAAI,CAACgF,KAAK,CAAC,GAAG,GAAGxB,KAAK,CAACyB,MAAM,IAAIzB,KAAK,CAACH,KAAK,IAAI,CAAC,CAAC,CAAC;SAC1E,MAAM,IAAIG,KAAK,CAACM,IAAI,KAAK/K,aAAa,CAACmM,QAAQ,EAAE;UAChD,IAAI,CAACpG,SAAS,GAAG,KAAK;UACtB,MAAMwD,QAAQ,GAAGkB,KAAK,CAAC2B,IAAW;UAElC,IAAI7C,QAAQ,CAACC,OAAO,EAAE;YACpB,IAAI,CAAChG,aAAa,GAAG+F,QAAQ,CAAC8C,OAAO;YACrC,IAAI,CAACpI,eAAe,GAAGsF,QAAQ,CAAC+C,OAAO;YACvC,IAAI,CAACT,WAAW,CAACtC,QAAQ,CAAC+C,OAAO,CAAC;YAClC,IAAI,CAACpE,aAAa,EAAE,CAAC,CAAC;WACvB,MAAM;YACL,IAAI,CAACsC,SAAS,CAACjB,QAAQ,CAAC+C,OAAO,IAAI,eAAe,CAAC;;;MAGzD,CAAC;MACDjJ,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC0C,SAAS,GAAG,KAAK;QACtB0D,OAAO,CAACpG,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,IAAI,CAACmH,SAAS,CAACnH,KAAK,CAACA,KAAK,EAAEiJ,OAAO,IAAI,kCAAkC,CAAC;MAC5E;KACD,CAAC;EACJ;EAEA;EACQT,WAAWA,CAACS,OAAe;IACjC,IAAI,CAAC5F,QAAQ,CAAC6F,IAAI,CAACD,OAAO,EAAE,OAAO,EAAE;MACnCE,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,CAAC,kBAAkB;KAChC,CAAC;EACJ;EAEQjC,SAASA,CAAC8B,OAAe;IAC/B,IAAI,CAAC5F,QAAQ,CAAC6F,IAAI,CAACD,OAAO,EAAE,OAAO,EAAE;MACnCE,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,CAAC,gBAAgB;KAC9B,CAAC;EACJ;EAEA;EACA5K,WAAWA,CAACkH,OAAa;IACvB,IAAI,CAAC3C,MAAM,CAACsG,QAAQ,CAAC,CAAC,gCAAgC,EAAE3D,OAAO,CAACxI,GAAG,CAAC,CAAC;EACvE;EAEAmB,WAAWA,CAACqH,OAAa;IACvB,IAAI,CAAC3C,MAAM,CAACsG,QAAQ,CAAC,CAAC,gCAAgC,EAAE3D,OAAO,CAACxI,GAAG,CAAC,CAAC;EACvE;EAEAyB,aAAaA,CAAC+G,OAAa;IACzB,IAAI4D,OAAO,CAAC,mCAAmC5D,OAAO,CAACrI,IAAI,GAAG,CAAC,EAAE;MAC/D,IAAI,CAACyF,WAAW,CAACyG,UAAU,CAAC7D,OAAO,CAACxI,GAAG,CAAC,CAAC8I,SAAS,CAAC;QACjDC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;YACpB,IAAI,CAACqC,WAAW,CAAC,8BAA8B,CAAC;YAChD,IAAI,CAAC3D,aAAa,EAAE;WACrB,MAAM;YACL,IAAI,CAACsC,SAAS,CAAC,0BAA0B,CAAC;;QAE9C,CAAC;QACDnH,KAAK,EAAGA,KAAK,IAAI;UACfoG,OAAO,CAACpG,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAC/C,IAAI,CAACmH,SAAS,CAAC,0BAA0B,CAAC;QAC5C;OACD,CAAC;;EAEN;EAEA;EACAqC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACjG,WAAW,GAAG,IAAI,CAACE,UAAU,EAAE;MACtC,IAAI,CAACF,WAAW,EAAE;MAClB,IAAI,CAACsB,aAAa,EAAE;;EAExB;EAEA4E,YAAYA,CAAA;IACV,IAAI,IAAI,CAAClG,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;MAClB,IAAI,CAACsB,aAAa,EAAE;;EAExB;EAIA;EACA1F,YAAYA,CAACuG,OAAa;IACxB,IAAIA,OAAO,EAAEiB,OAAO,IAAI,OAAOjB,OAAO,CAACiB,OAAO,KAAK,QAAQ,EAAE;MAC3D,OAAQjB,OAAO,CAACiB,OAAe,CAAClJ,SAAS,IAAI,KAAK;;IAEpD,OAAO,KAAK;EACd;EAEA2B,eAAeA,CAACsG,OAAa;IAC3B,IAAIA,OAAO,EAAEiB,OAAO,IAAI,OAAOjB,OAAO,CAACiB,OAAO,KAAK,QAAQ,EAAE;MAC3D,OAAQjB,OAAO,CAACiB,OAAe,CAACjJ,OAAO,IAAI,EAAE;;IAE/C,OAAO,EAAE;EACX;EAEA;EACAgM,iBAAiBA,CAAChE,OAAa;IAC7B,IAAIA,OAAO,EAAEzG,UAAU,IAAI,OAAOyG,OAAO,CAACzG,UAAU,KAAK,QAAQ,EAAE;MACjE,OAAQyG,OAAO,CAACzG,UAAkB,CAAC5B,IAAI,IAAI,KAAK;;IAElD,OAAO,KAAK;EACd;EAEAsM,cAAcA,CAACjE,OAAa;IAC1B,IAAIA,OAAO,EAAE3G,OAAO,IAAI,OAAO2G,OAAO,CAAC3G,OAAO,KAAK,QAAQ,EAAE;MAC3D,OAAQ2G,OAAO,CAAC3G,OAAe,CAAC1B,IAAI,IAAI,KAAK;;IAE/C,OAAO,KAAK;EACd;EAEA;EACAuM,QAAQA,CAACnD,IAAY;IACnB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAAChD,UAAU,EAAE;MACxC,IAAI,CAACF,WAAW,GAAGkD,IAAI;MACvB,IAAI,CAAC5B,aAAa,EAAE;;EAExB;EAEAgF,cAAcA,CAAA;IACZ,MAAMC,KAAK,GAAa,EAAE;IAC1B,MAAMC,cAAc,GAAG,CAAC;IACxB,MAAMC,SAAS,GAAGpG,IAAI,CAACqG,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC1G,WAAW,GAAGK,IAAI,CAACsG,KAAK,CAACH,cAAc,GAAG,CAAC,CAAC,CAAC;IAChF,MAAMI,OAAO,GAAGvG,IAAI,CAACwG,GAAG,CAAC,IAAI,CAAC3G,UAAU,EAAEuG,SAAS,GAAGD,cAAc,GAAG,CAAC,CAAC;IAEzE,KAAK,IAAIM,CAAC,GAAGL,SAAS,EAAEK,CAAC,IAAIF,OAAO,EAAEE,CAAC,EAAE,EAAE;MACzCP,KAAK,CAACQ,IAAI,CAACD,CAAC,CAAC;;IAEf,OAAOP,KAAK;EACd;EAEA;EACAS,YAAYA,CAAA;IACV,IAAI,CAACjH,WAAW,GAAG,EAAE;IACrB,IAAI,CAACa,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACL,mBAAmB,GAAG,IAAI,CAACH,WAAW;IAC3C,IAAI,CAACI,eAAe,GAAG,IAAI,CAACH,OAAO;IACnC,IAAI,CAACR,WAAW,GAAG,CAAC;IACpB,IAAI,CAACsB,aAAa,EAAE;EACtB;EAEA2F,aAAaA,CAAA;IACX,IAAI,CAACzH,MAAM,CAACsG,QAAQ,CAAC,CAAC,uCAAuC,CAAC,CAAC;EACjE;EAAC,QAAAoB,CAAA,G;qBA9aU7H,yBAAyB,EAAAhG,EAAA,CAAA8N,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAhO,EAAA,CAAA8N,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAlO,EAAA,CAAA8N,iBAAA,CAAAK,EAAA,CAAAC,UAAA,GAAApO,EAAA,CAAA8N,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAtO,EAAA,CAAA8N,iBAAA,CAAAS,EAAA,CAAAC,iBAAA,GAAAxO,EAAA,CAAA8N,iBAAA,CAAAW,EAAA,CAAAC,cAAA,GAAA1O,EAAA,CAAA8N,iBAAA,CAAAa,EAAA,CAAAC,iBAAA,GAAA5O,EAAA,CAAA8N,iBAAA,CAAAe,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAzB/I,yBAAyB;IAAAgJ,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QClBtCtP,EAAA,CAAAC,cAAA,aAAoC;QAQKD,EAAA,CAAAE,MAAA,aAAM;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC9CH,EAAA,CAAAE,MAAA,2BACF;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAC,cAAA,WAAyB;QAAAD,EAAA,CAAAE,MAAA,2CAAmC;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAElEH,EAAA,CAAAC,cAAA,cAA4B;QACgBD,EAAA,CAAAe,UAAA,mBAAAyO,4DAAA;UAAA,OAASD,GAAA,CAAA3K,gBAAA,EAAkB;QAAA,EAAC;QACpE5E,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC7BH,EAAA,CAAAE,MAAA,2BACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAETH,EAAA,CAAAC,cAAA,kBAAuF;QAAhDD,EAAA,CAAAe,UAAA,mBAAA0O,4DAAA;UAAA,OAASF,GAAA,CAAA1D,gBAAA,EAAkB;QAAA,EAAC;QACjE7L,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAChCH,EAAA,CAAAE,MAAA,qBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAETH,EAAA,CAAAC,cAAA,kBAA6G;QACjGD,EAAA,CAAAE,MAAA,kBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC/BH,EAAA,CAAAE,MAAA,yBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAMfH,EAAA,CAAAC,cAAA,oBAA+B;QAORD,EAAA,CAAAE,MAAA,uBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACtCH,EAAA,CAAAC,cAAA,iBAGkC;QAD3BD,EAAA,CAAAe,UAAA,2BAAA2O,mEAAApL,MAAA;UAAA,OAAAiL,GAAA,CAAA7I,WAAA,GAAApC,MAAA;QAAA,EAAyB,yBAAAqL,iEAAA;UAAA,OACVJ,GAAA,CAAApH,QAAA,EAAU;QAAA,EADA;QAFhCnI,EAAA,CAAAG,YAAA,EAGkC;QAClCH,EAAA,CAAAC,cAAA,oBAAoB;QAAAD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAKzCH,EAAA,CAAAC,cAAA,eAA4B;QAEbD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC9BH,EAAA,CAAAC,cAAA,sBAAgF;QAApED,EAAA,CAAAe,UAAA,2BAAA6O,wEAAAtL,MAAA;UAAA,OAAAiL,GAAA,CAAAhI,eAAA,GAAAjD,MAAA;QAAA,EAA6B,6BAAAuL,0EAAA;UAAA,OAAoBN,GAAA,CAAAnH,eAAA,EAAiB;QAAA,EAArC;QACvCpI,EAAA,CAAAC,cAAA,sBAAqB;QAAAD,EAAA,CAAAE,MAAA,WAAG;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACrCH,EAAA,CAAAiD,UAAA,KAAA6M,gDAAA,yBAEa;QACf9P,EAAA,CAAAG,YAAA,EAAa;QAKjBH,EAAA,CAAAC,cAAA,eAA4B;QAEbD,EAAA,CAAAE,MAAA,kBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACjCH,EAAA,CAAAC,cAAA,sBAEyC;QAF7BD,EAAA,CAAAe,UAAA,2BAAAgP,wEAAAzL,MAAA;UAAA,OAAAiL,GAAA,CAAA/H,kBAAA,GAAAlD,MAAA;QAAA,EAAgC,6BAAA0L,0EAAA;UAAA,OACdT,GAAA,CAAA9G,kBAAA,EAAoB;QAAA,EADN;QAG1CzI,EAAA,CAAAC,cAAA,sBAAqB;QAAAD,EAAA,CAAAE,MAAA,WAAG;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACrCH,EAAA,CAAAiD,UAAA,KAAAgN,gDAAA,yBAEa;QACfjQ,EAAA,CAAAG,YAAA,EAAa;QAKjBH,EAAA,CAAAC,cAAA,eAA4B;QAEbD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC5BH,EAAA,CAAAC,cAAA,sBAE4C;QAFhCD,EAAA,CAAAe,UAAA,2BAAAmP,wEAAA5L,MAAA;UAAA,OAAAiL,GAAA,CAAA9H,aAAA,GAAAnD,MAAA;QAAA,EAA2B,6BAAA6L,0EAAA;UAAA,OACTZ,GAAA,CAAA3G,aAAA,EAAe;QAAA,EADN;QAGrC5I,EAAA,CAAAC,cAAA,sBAAqB;QAAAD,EAAA,CAAAE,MAAA,WAAG;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACrCH,EAAA,CAAAiD,UAAA,KAAAmN,gDAAA,yBAEa;QACfpQ,EAAA,CAAAG,YAAA,EAAa;QAKjBH,EAAA,CAAAC,cAAA,eAA4D;QAChBD,EAAA,CAAAe,UAAA,mBAAAsP,4DAAA;UAAA,OAASd,GAAA,CAAA/G,YAAA,EAAc;QAAA,EAAC;QAChExI,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAChCH,EAAA,CAAAC,cAAA,gBAAiC;QAAAD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAE/CH,EAAA,CAAAC,cAAA,kBAAoE;QAA5CD,EAAA,CAAAe,UAAA,mBAAAuP,4DAAA;UAAA,OAASf,GAAA,CAAA5B,YAAA,EAAc;QAAA,EAAC;QAC9C3N,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAUxCH,EAAA,CAAAC,cAAA,oBAA6B;QAGbD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACzBH,EAAA,CAAAE,MAAA,IACF;QAAAF,EAAA,CAAAG,YAAA,EAAiB;QAEnBH,EAAA,CAAAC,cAAA,wBAAkB;QAMkBD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACpCH,EAAA,CAAAC,cAAA,cAA2C;QAAAD,EAAA,CAAAE,MAAA,WAAG;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACnDH,EAAA,CAAAC,cAAA,cAA+C;QAAAD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC3DH,EAAA,CAAAC,cAAA,cAAsB;QAAAD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAChCH,EAAA,CAAAC,cAAA,cAA+C;QAAAD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC3DH,EAAA,CAAAC,cAAA,cAAwB;QAAAD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAGxCH,EAAA,CAAAC,cAAA,aAAO;QACLD,EAAA,CAAAiD,UAAA,KAAAsN,wCAAA,mBA2DK;QACPvQ,EAAA,CAAAG,YAAA,EAAQ;QAIVH,EAAA,CAAAiD,UAAA,KAAAuN,yCAAA,kBAGM;QAGNxQ,EAAA,CAAAiD,UAAA,KAAAwN,yCAAA,mBAQM;QACRzQ,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAAC,cAAA,eAAkC;QAG5BD,EAAA,CAAAE,MAAA,IAGF;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAETH,EAAA,CAAAC,cAAA,gBAAiC;QACwBD,EAAA,CAAAe,UAAA,mBAAA2P,6DAAA;UAAA,OAASnB,GAAA,CAAA1C,YAAA,EAAc;QAAA,EAAC;QAC7E7M,EAAA,CAAAC,cAAA,iBAAU;QAAAD,EAAA,CAAAE,MAAA,qBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAEnCH,EAAA,CAAAC,cAAA,iBAAwB;QAAAD,EAAA,CAAAE,MAAA,KAA0C;QAAAF,EAAA,CAAAG,YAAA,EAAO;QACzEH,EAAA,CAAAC,cAAA,mBAAgI;QAA5CD,EAAA,CAAAe,UAAA,mBAAA4P,6DAAA;UAAA,OAASpB,GAAA,CAAA3C,QAAA,EAAU;QAAA,EAAC;QACtG5M,EAAA,CAAAC,cAAA,iBAAU;QAAAD,EAAA,CAAAE,MAAA,sBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAWlDH,EAAA,CAAAiD,UAAA,MAAA2N,0CAAA,mBAyHM;;;QA5TiB5Q,EAAA,CAAAO,SAAA,IAAyB;QAAzBP,EAAA,CAAAI,UAAA,YAAAmP,GAAA,CAAA7I,WAAA,CAAyB;QAUpB1G,EAAA,CAAAO,SAAA,GAA6B;QAA7BP,EAAA,CAAAI,UAAA,YAAAmP,GAAA,CAAAhI,eAAA,CAA6B;QAEPvH,EAAA,CAAAO,SAAA,GAAW;QAAXP,EAAA,CAAAI,UAAA,YAAAmP,GAAA,CAAAtI,QAAA,CAAW;QAWjCjH,EAAA,CAAAO,SAAA,GAAgC;QAAhCP,EAAA,CAAAI,UAAA,YAAAmP,GAAA,CAAA/H,kBAAA,CAAgC,cAAA+H,GAAA,CAAAhI,eAAA;QAIbvH,EAAA,CAAAO,SAAA,GAAsB;QAAtBP,EAAA,CAAAI,UAAA,YAAAmP,GAAA,CAAAlI,mBAAA,CAAsB;QAWzCrH,EAAA,CAAAO,SAAA,GAA2B;QAA3BP,EAAA,CAAAI,UAAA,YAAAmP,GAAA,CAAA9H,aAAA,CAA2B,cAAA8H,GAAA,CAAA/H,kBAAA;QAITxH,EAAA,CAAAO,SAAA,GAAkB;QAAlBP,EAAA,CAAAI,UAAA,YAAAmP,GAAA,CAAAjI,eAAA,CAAkB;QA4BtDtH,EAAA,CAAAO,SAAA,IACF;QADEP,EAAA,CAAAQ,kBAAA,gBAAA+O,GAAA,CAAAzI,YAAA,OACF;QAiBgC9G,EAAA,CAAAO,SAAA,IAAa;QAAbP,EAAA,CAAAI,UAAA,YAAAmP,GAAA,CAAAnI,QAAA,CAAa;QAgEnCpH,EAAA,CAAAO,SAAA,GAAa;QAAbP,EAAA,CAAAI,UAAA,SAAAmP,GAAA,CAAAxI,OAAA,CAAa;QAMb/G,EAAA,CAAAO,SAAA,GAAuC;QAAvCP,EAAA,CAAAI,UAAA,UAAAmP,GAAA,CAAAxI,OAAA,IAAAwI,GAAA,CAAAnI,QAAA,CAAAxD,MAAA,OAAuC;QAezC5D,EAAA,CAAAO,SAAA,GAGF;QAHEP,EAAA,CAAA6Q,kBAAA,eAAAtB,GAAA,CAAA5I,WAAA,QAAA4I,GAAA,CAAA3I,YAAA,cAAA2I,GAAA,CAAAvI,IAAA,CAAAwG,GAAA,CAAA+B,GAAA,CAAA5I,WAAA,GAAA4I,GAAA,CAAA3I,YAAA,EAAA2I,GAAA,CAAAzI,YAAA,WAAAyI,GAAA,CAAAzI,YAAA,eAGF;QAGwB9G,EAAA,CAAAO,SAAA,GAA8B;QAA9BP,EAAA,CAAAI,UAAA,aAAAmP,GAAA,CAAA5I,WAAA,OAA8B;QAG9B3G,EAAA,CAAAO,SAAA,GAA0C;QAA1CP,EAAA,CAAAY,kBAAA,UAAA2O,GAAA,CAAA5I,WAAA,UAAA4I,GAAA,CAAA1I,UAAA,KAA0C;QAC1C7G,EAAA,CAAAO,SAAA,GAA2D;QAA3DP,EAAA,CAAAI,UAAA,aAAAmP,GAAA,CAAA5I,WAAA,KAAA4I,GAAA,CAAA1I,UAAA,IAAA0I,GAAA,CAAA1I,UAAA,OAA2D;QAYpE7G,EAAA,CAAAO,SAAA,GAAsB;QAAtBP,EAAA,CAAAI,UAAA,SAAAmP,GAAA,CAAA5H,gBAAA,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}