<div class="student-view-container">
  <!-- Header -->
  <div class="page-header">
    <div class="header-content">
      <button mat-icon-button (click)="goBack()" class="back-btn">
        <mat-icon>arrow_back</mat-icon>
      </button>
      <div class="title-section">
        <h1 class="page-title">
          <mat-icon class="title-icon">person</mat-icon>
          Student Details
        </h1>
        <p class="page-subtitle">View comprehensive student information and attendance records</p>
      </div>
    </div>
    <div class="header-actions" *ngIf="student">
      <button mat-raised-button color="primary" (click)="editStudent()">
        <mat-icon>edit</mat-icon>
        Edit Student
      </button>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="loading-container">
    <mat-spinner></mat-spinner>
    <p>Loading student details...</p>
  </div>

  <!-- Student Details -->
  <div *ngIf="!loading && student" class="student-details">
    <!-- Basic Information Card -->
    <mat-card class="info-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>account_circle</mat-icon>
          Basic Information
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="info-grid">
          <div class="info-item">
            <label>Full Name</label>
            <h3>{{ student.name }}</h3>
          </div>
          <div class="info-item">
            <label>Email</label>
            <p>{{ student.email }}</p>
          </div>
          <div class="info-item">
            <label>Roll Number</label>
            <h3>{{ student.rollNo || 'N/A' }}</h3>
          </div>
          <div class="info-item">
            <label>Registration Number</label>
            <p>{{ student.regNo || 'N/A' }}</p>
          </div>
          <div class="info-item">
            <label>Contact</label>
            <p>{{ student.contact || 'N/A' }}</p>
          </div>
          <div class="info-item">
            <label>Status</label>
            <span class="status-badge" [ngClass]="student.isActive ? 'active' : 'inactive'">
              {{ student.isActive ? 'Active' : 'Inactive' }}
            </span>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Academic Information Card -->
    <mat-card class="info-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>school</mat-icon>
          Academic Information
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="info-grid">
          <div class="info-item">
            <label>Program</label>
            <h3>{{ student.program?.name || 'N/A' }}</h3>
            <small>{{ student.program?.fullName || '' }}</small>
          </div>
          <div class="info-item">
            <label>Department</label>
            <h3>{{ student.department?.name || 'N/A' }}</h3>
          </div>
          <div class="info-item">
            <label>Class</label>
            <h3>{{ getClassName(student) }}</h3>
          </div>
          <div class="info-item">
            <label>Section</label>
            <h3>{{ getClassSection(student) || 'N/A' }}</h3>
          </div>
          <div class="info-item">
            <label>Semester</label>
            <h3>{{ student.semester || 'N/A' }}</h3>
          </div>
          <div class="info-item">
            <label>Academic Year</label>
            <p>{{ student.academicYear || 'N/A' }}</p>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Attendance Section -->
    <mat-card class="attendance-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>access_time</mat-icon>
          Attendance Records
        </mat-card-title>
        <div class="card-actions">
          <button mat-raised-button color="accent" (click)="toggleAttendanceDetails()">
            <mat-icon>{{ showAttendanceDetails ? 'visibility_off' : 'visibility' }}</mat-icon>
            {{ showAttendanceDetails ? 'Hide' : 'View' }} Attendance
          </button>
        </div>
      </mat-card-header>
      
      <!-- Attendance Summary -->
      <mat-card-content *ngIf="showAttendanceDetails">
        <div *ngIf="attendanceLoading" class="loading-state">
          <mat-spinner diameter="40"></mat-spinner>
          <p>Loading attendance data...</p>
        </div>

        <div *ngIf="!attendanceLoading && attendanceRecords.length > 0" class="attendance-content">
          <!-- Summary Stats -->
          <div class="attendance-summary">
            <div class="summary-item overall">
              <div class="summary-icon">
                <mat-icon>assessment</mat-icon>
              </div>
              <div class="summary-info">
                <h3>{{ overallAttendance }}%</h3>
                <p>Overall Attendance</p>
              </div>
            </div>
            <div class="summary-item subjects">
              <div class="summary-icon">
                <mat-icon>subject</mat-icon>
              </div>
              <div class="summary-info">
                <h3>{{ totalSubjects }}</h3>
                <p>Total Subjects</p>
              </div>
            </div>
          </div>

          <!-- Attendance Records -->
          <div class="attendance-records">
            <h4>Subject-wise Attendance</h4>
            <div class="records-grid">
              <div *ngFor="let record of paginatedRecords" class="attendance-record"
                   [ngClass]="getAttendanceClass(record.attendancePercentage)">
                <div class="record-header">
                  <div class="subject-info">
                    <h5>{{ record.subject.subjectName }}</h5>
                    <span class="subject-code">{{ record.subject.code }}</span>
                  </div>
                  <div class="attendance-percentage" [ngClass]="getAttendanceClass(record.attendancePercentage)">
                    {{ record.attendancePercentage }}%
                  </div>
                </div>
                <div class="record-stats">
                  <div class="stat-item present">
                    <mat-icon>check_circle</mat-icon>
                    <span>{{ record.presentClasses }} Present</span>
                  </div>
                  <div class="stat-item absent">
                    <mat-icon>cancel</mat-icon>
                    <span>{{ record.absentClasses }} Absent</span>
                  </div>
                  <div class="stat-item late" *ngIf="record.lateClasses > 0">
                    <mat-icon>schedule</mat-icon>
                    <span>{{ record.lateClasses }} Late</span>
                  </div>
                  <div class="stat-item total">
                    <mat-icon>event</mat-icon>
                    <span>{{ record.totalClasses }} Total</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Pagination -->
            <div class="pagination-container" *ngIf="totalPages > 1">
              <button mat-icon-button [disabled]="currentPage === 1" (click)="changePage(currentPage - 1)">
                <mat-icon>chevron_left</mat-icon>
              </button>
              <span class="page-info">Page {{ currentPage }} of {{ totalPages }}</span>
              <button mat-icon-button [disabled]="currentPage === totalPages" (click)="changePage(currentPage + 1)">
                <mat-icon>chevron_right</mat-icon>
              </button>
            </div>
          </div>
        </div>

        <div *ngIf="!attendanceLoading && attendanceRecords.length === 0" class="empty-state">
          <mat-icon class="empty-icon">event_busy</mat-icon>
          <h4>No Attendance Records</h4>
          <p>No attendance data found for this student.</p>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Error State -->
  <div *ngIf="!loading && !student" class="error-state">
    <mat-icon class="error-icon">error</mat-icon>
    <h3>Student Not Found</h3>
    <p>The requested student could not be found.</p>
    <button mat-raised-button color="primary" (click)="goBack()">
      <mat-icon>arrow_back</mat-icon>
      Back to Students
    </button>
  </div>
</div>
