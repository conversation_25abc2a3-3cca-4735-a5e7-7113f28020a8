.student-view-container {
  background-color: #f8f9fa;
  min-height: 100vh;
  padding: 20px;
}

/* Header Section */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding: 15px 0;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.back-btn {
  background-color: #e9ecef;
  color: #495057;
}

.title-section {
  display: flex;
  flex-direction: column;
}

.page-title {
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.title-icon {
  font-size: 1.8rem;
  color: #3498db;
}

.page-subtitle {
  color: #7f8c8d;
  margin: 5px 0 0 0;
  font-size: 0.9rem;
}

.header-actions {
  display: flex;
  gap: 10px;
}

/* Loading and Error States */
.loading-container, .error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-container p, .error-state p {
  margin-top: 15px;
  color: #7f8c8d;
}

.error-icon {
  font-size: 4rem;
  color: #e74c3c;
  margin-bottom: 20px;
}

/* Cards */
.info-card, .attendance-card {
  margin-bottom: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.info-card mat-card-header, .attendance-card mat-card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;
  padding: 20px;
}

.info-card mat-card-title, .attendance-card mat-card-title {
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0;
}

.card-actions {
  margin-left: auto;
}

/* Info Grid */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  padding: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.info-item label {
  font-size: 0.85rem;
  font-weight: 600;
  color: #7f8c8d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-item h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
}

.info-item p {
  margin: 0;
  color: #495057;
  font-size: 0.95rem;
}

.info-item small {
  color: #6c757d;
  font-size: 0.8rem;
}

/* Status Badge */
.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  width: fit-content;
}

.status-badge.active {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.inactive {
  background-color: #f8d7da;
  color: #721c24;
}

/* Attendance Section */
.attendance-content {
  padding: 20px;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px;
  gap: 15px;
}

.attendance-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.summary-item {
  background: white;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #3498db;
}

.summary-item.overall {
  border-left-color: #27ae60;
}

.summary-item.subjects {
  border-left-color: #f39c12;
}

.summary-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.summary-info h3 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: #2c3e50;
}

.summary-info p {
  margin: 0;
  color: #7f8c8d;
  font-size: 0.9rem;
}

/* Attendance Records */
.attendance-records h4 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.2rem;
  font-weight: 600;
}

.records-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.attendance-record {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #bdc3c7;
  transition: transform 0.2s ease;
}

.attendance-record:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.attendance-record.excellent {
  border-left-color: #27ae60;
}

.attendance-record.good {
  border-left-color: #f39c12;
}

.attendance-record.average {
  border-left-color: #e67e22;
}

.attendance-record.poor {
  border-left-color: #e74c3c;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.subject-info h5 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
}

.subject-code {
  background: #e9ecef;
  color: #495057;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.attendance-percentage {
  padding: 8px 12px;
  border-radius: 20px;
  font-weight: 700;
  font-size: 0.9rem;
}

.attendance-percentage.excellent {
  background: #d4edda;
  color: #155724;
}

.attendance-percentage.good {
  background: #fff3cd;
  color: #856404;
}

.attendance-percentage.average {
  background: #ffeaa7;
  color: #b8860b;
}

.attendance-percentage.poor {
  background: #f8d7da;
  color: #721c24;
}

.record-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.85rem;
  color: #495057;
}

.stat-item mat-icon {
  font-size: 1rem;
  width: 16px;
  height: 16px;
}

.stat-item.present mat-icon {
  color: #27ae60;
}

.stat-item.absent mat-icon {
  color: #e74c3c;
}

.stat-item.late mat-icon {
  color: #f39c12;
}

.stat-item.total mat-icon {
  color: #3498db;
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  margin-top: 20px;
}

.page-info {
  color: #7f8c8d;
  font-size: 0.9rem;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-icon {
  font-size: 4rem;
  color: #bdc3c7;
  margin-bottom: 20px;
}

.empty-state h4 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.empty-state p {
  color: #7f8c8d;
  margin-bottom: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .student-view-container {
    padding: 15px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 15px;
    padding: 15px;
  }

  .attendance-summary {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .records-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .record-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .record-stats {
    grid-template-columns: 1fr;
    gap: 8px;
  }
}
