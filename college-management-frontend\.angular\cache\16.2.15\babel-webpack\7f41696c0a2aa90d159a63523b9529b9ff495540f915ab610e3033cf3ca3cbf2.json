{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AuthGuard } from './guards/auth.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: 'auth',\n  pathMatch: 'full'\n}, {\n  path: 'auth',\n  loadChildren: () => import('./auth/auth.module').then(m => m.AuthModule)\n}, {\n  path: 'dashboard',\n  loadChildren: () => import('./administration/administration.module').then(m => m.AdministrationModule),\n  canActivate: [AuthGuard]\n}];\nexport class AppRoutingModule {\n  static #_ = this.ɵfac = function AppRoutingModule_Factory(t) {\n    return new (t || AppRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AppRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forRoot(routes), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "<PERSON><PERSON><PERSON><PERSON>", "routes", "path", "redirectTo", "pathMatch", "loadChildren", "then", "m", "AuthModule", "AdministrationModule", "canActivate", "AppRoutingModule", "_", "_2", "_3", "forRoot", "imports", "i1", "exports"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { AuthGuard } from './guards/auth.guard';\r\n\r\nconst routes: Routes = [\r\n  { path: '', redirectTo: 'auth', pathMatch: 'full' }, // Default route\r\n  { path: 'auth', loadChildren: () => import('./auth/auth.module').then((m) => m.AuthModule) },\r\n  {\r\n    path: 'dashboard',\r\n    loadChildren: () => import('./administration/administration.module').then((m) => m.AdministrationModule),\r\n    canActivate: [AuthGuard]\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forRoot(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class AppRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,SAAS,QAAQ,qBAAqB;;;AAE/C,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,UAAU,EAAE,MAAM;EAAEC,SAAS,EAAE;AAAM,CAAE,EACnD;EAAEF,IAAI,EAAE,MAAM;EAAEG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,oBAAoB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,UAAU;AAAC,CAAE,EAC5F;EACEN,IAAI,EAAE,WAAW;EACjBG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACE,oBAAoB,CAAC;EACxGC,WAAW,EAAE,CAACV,SAAS;CACxB,CACF;AAMD,OAAM,MAAOW,gBAAgB;EAAA,QAAAC,CAAA,G;qBAAhBD,gBAAgB;EAAA;EAAA,QAAAE,EAAA,G;UAAhBF;EAAgB;EAAA,QAAAG,EAAA,G;cAHjBf,YAAY,CAACgB,OAAO,CAACd,MAAM,CAAC,EAC5BF,YAAY;EAAA;;;2EAEXY,gBAAgB;IAAAK,OAAA,GAAAC,EAAA,CAAAlB,YAAA;IAAAmB,OAAA,GAFjBnB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}