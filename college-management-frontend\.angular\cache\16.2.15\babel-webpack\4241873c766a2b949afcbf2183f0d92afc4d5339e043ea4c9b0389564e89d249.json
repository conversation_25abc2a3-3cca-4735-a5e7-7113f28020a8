{"ast": null, "code": "import Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/auth.service\";\nimport * as i2 from \"src/app/services/user.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/material/icon\";\nfunction UserListComponent_option_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const role_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", role_r4 === \"All\" ? \"\" : role_r4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(role_r4);\n  }\n}\nconst _c0 = function (a0, a1, a2, a3) {\n  return {\n    \"badge-student\": a0,\n    \"badge-teacher\": a1,\n    \"badge-principal\": a2,\n    \"badge-admin\": a3\n  };\n};\nconst _c1 = function (a0, a1) {\n  return {\n    \"badge-active\": a0,\n    \"badge-inactive\": a1\n  };\n};\nfunction UserListComponent_div_16_tr_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\")(6, \"span\", 23);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\")(15, \"span\", 23);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"td\")(18, \"div\", 24)(19, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function UserListComponent_div_16_tr_22_Template_button_click_19_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const user_r6 = restoredCtx.$implicit;\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.editUser(user_r6));\n    });\n    i0.ɵɵelementStart(20, \"mat-icon\");\n    i0.ɵɵtext(21, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function UserListComponent_div_16_tr_22_Template_button_click_22_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const user_r6 = restoredCtx.$implicit;\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.deleteUser(user_r6));\n    });\n    i0.ɵɵelementStart(23, \"mat-icon\");\n    i0.ɵɵtext(24, \"delete\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const user_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r6.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(9, _c0, user_r6.role === \"Student\", user_r6.role === \"Teacher\", user_r6.role === \"Principal\", user_r6.role === \"Admin\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", user_r6.role, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r6.regNo || user_r6.rollNo || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r6.department || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r6.contact || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(14, _c1, user_r6.isActive, !user_r6.isActive));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", user_r6.isActive ? \"Active\" : \"Inactive\", \" \");\n  }\n}\nfunction UserListComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"table\")(3, \"thead\")(4, \"tr\", 16)(5, \"th\");\n    i0.ɵɵtext(6, \"Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"Role\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\");\n    i0.ɵɵtext(12, \"Reg/Roll No\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\");\n    i0.ɵɵtext(14, \"Department\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"th\");\n    i0.ɵɵtext(16, \"Contact\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"th\");\n    i0.ɵɵtext(18, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"th\");\n    i0.ɵɵtext(20, \"Actions\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"tbody\");\n    i0.ɵɵtemplate(22, UserListComponent_div_16_tr_22_Template, 25, 17, \"tr\", 17);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"div\", 18)(24, \"div\");\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 19)(27, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function UserListComponent_div_16_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.previousPage());\n    });\n    i0.ɵɵtext(28, \" Previous \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"span\", 21);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function UserListComponent_div_16_Template_button_click_31_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.nextPage());\n    });\n    i0.ɵɵtext(32, \" Next \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(22);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.paginatedUsers);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate3(\" Showing \", (ctx_r1.currentPage - 1) * ctx_r1.itemsPerPage + 1, \" to \", ctx_r1.currentPage * ctx_r1.itemsPerPage > ctx_r1.filteredUsers.length ? ctx_r1.filteredUsers.length : ctx_r1.currentPage * ctx_r1.itemsPerPage, \" of \", ctx_r1.filteredUsers.length, \" users \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentPage === 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"Page \", ctx_r1.currentPage, \" of \", ctx_r1.totalPages, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentPage === ctx_r1.totalPages);\n  }\n}\nfunction UserListComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 28)(2, \"span\", 29);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 30);\n    i0.ɵɵtext(5, \"Loading users...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserListComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"mat-icon\", 31);\n    i0.ɵɵtext(2, \"people\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\", 32);\n    i0.ɵɵtext(4, \"No users found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 33);\n    i0.ɵɵtext(6, \"Try adjusting your search criteria or add a new user.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let UserListComponent = /*#__PURE__*/(() => {\n  class UserListComponent {\n    constructor(authService, userService, router) {\n      this.authService = authService;\n      this.userService = userService;\n      this.router = router;\n      this.users = [];\n      this.filteredUsers = [];\n      this.searchQuery = '';\n      this.selectedRole = '';\n      this.currentPage = 1;\n      this.itemsPerPage = 10;\n      this.loading = false;\n      this.roles = ['All', 'Student', 'Teacher', 'Principal', 'Admin'];\n    }\n    ngOnInit() {\n      this.loadUsers();\n    }\n    loadUsers() {\n      this.loading = true;\n      this.authService.getAllUsers().subscribe({\n        next: response => {\n          this.loading = false;\n          if (response.success) {\n            this.users = response.users || [];\n            this.filteredUsers = [...this.users];\n          }\n        },\n        error: error => {\n          this.loading = false;\n          console.error('Error loading users:', error);\n          Swal.fire({\n            title: 'Error',\n            text: 'Failed to load users. Please try again.',\n            icon: 'error',\n            confirmButtonColor: '#29578c'\n          });\n        }\n      });\n    }\n    filterUsers() {\n      this.filteredUsers = this.users.filter(user => {\n        const matchesSearch = !this.searchQuery || user.name.toLowerCase().includes(this.searchQuery.toLowerCase()) || user.email.toLowerCase().includes(this.searchQuery.toLowerCase()) || user.regNo && user.regNo.toLowerCase().includes(this.searchQuery.toLowerCase()) || user.rollNo && user.rollNo.toLowerCase().includes(this.searchQuery.toLowerCase());\n        const matchesRole = !this.selectedRole || this.selectedRole === 'All' || user.role === this.selectedRole;\n        return matchesSearch && matchesRole;\n      });\n      this.currentPage = 1;\n    }\n    onSearchChange() {\n      this.filterUsers();\n    }\n    onRoleChange() {\n      this.filterUsers();\n    }\n    get paginatedUsers() {\n      const startIndex = (this.currentPage - 1) * this.itemsPerPage;\n      const endIndex = startIndex + this.itemsPerPage;\n      return this.filteredUsers.slice(startIndex, endIndex);\n    }\n    get totalPages() {\n      return Math.ceil(this.filteredUsers.length / this.itemsPerPage);\n    }\n    nextPage() {\n      if (this.currentPage < this.totalPages) {\n        this.currentPage++;\n      }\n    }\n    previousPage() {\n      if (this.currentPage > 1) {\n        this.currentPage--;\n      }\n    }\n    editUser(user) {\n      this.router.navigate(['/dashboard/admin/users/edit-user', user._id]);\n    }\n    deleteUser(user) {\n      Swal.fire({\n        title: 'Are you sure?',\n        text: `Do you want to delete ${user.name}? This action cannot be undone.`,\n        icon: 'warning',\n        showCancelButton: true,\n        confirmButtonColor: '#d33',\n        cancelButtonColor: '#29578c',\n        confirmButtonText: 'Yes, delete it!'\n      }).then(result => {\n        if (result.isConfirmed) {\n          this.performDelete(user);\n        }\n      });\n    }\n    performDelete(user) {\n      if (!user._id) return;\n      this.authService.deleteUser(user._id).subscribe({\n        next: response => {\n          if (response.success) {\n            Swal.fire({\n              title: 'Deleted!',\n              text: `${user.name} has been deleted successfully.`,\n              icon: 'success',\n              confirmButtonColor: '#29578c'\n            });\n            this.loadUsers(); // Reload the users list\n          }\n        },\n\n        error: error => {\n          console.error('Error deleting user:', error);\n          Swal.fire({\n            title: 'Error',\n            text: 'Failed to delete user. Please try again.',\n            icon: 'error',\n            confirmButtonColor: '#29578c'\n          });\n        }\n      });\n    }\n    addNewUser() {\n      this.router.navigate(['/dashboard/admin/users/add-user']);\n    }\n    static #_ = this.ɵfac = function UserListComponent_Factory(t) {\n      return new (t || UserListComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: UserListComponent,\n      selectors: [[\"app-user-list\"]],\n      decls: 19,\n      vars: 6,\n      consts: [[1, \"maindiv\"], [1, \"secondarydiv\"], [1, \"my-3\", \"d-flex\", \"justify-content-between\", \"searchAndtab\"], [1, \"d-flex\", \"flex-wrap\", \"gap-3\"], [1, \"btn\", \"btn-top\", \"border\", \"d-flex\", \"align-items-center\", 3, \"click\"], [1, \"form-select\", 2, \"width\", \"auto\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"search-container\"], [1, \"search-input-wrapper\"], [\"type\", \"text\", \"placeholder\", \"Search users...\", 1, \"search-input\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [1, \"search-icon\"], [\"class\", \"table-container\", 4, \"ngIf\"], [\"class\", \"text-center py-5\", 4, \"ngIf\"], [3, \"value\"], [1, \"table-container\"], [1, \"table-responsive\"], [1, \"tablehead\"], [4, \"ngFor\", \"ngForOf\"], [1, \"pagination-container\", \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mt-3\"], [1, \"pagination\"], [1, \"btn\", \"btn-outline-secondary\", \"me-2\", 3, \"disabled\", \"click\"], [1, \"page-info\"], [1, \"btn\", \"btn-outline-secondary\", \"ms-2\", 3, \"disabled\", \"click\"], [1, \"badge\", 3, \"ngClass\"], [1, \"action-buttons\"], [\"title\", \"Edit User\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", \"me-2\", 3, \"click\"], [\"title\", \"Delete User\", 1, \"btn\", \"btn-sm\", \"btn-outline-danger\", 3, \"click\"], [1, \"text-center\", \"py-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-2\"], [2, \"font-size\", \"48px\", \"color\", \"#ccc\"], [1, \"mt-3\"], [1, \"text-muted\"]],\n      template: function UserListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function UserListComponent_Template_button_click_4_listener() {\n            return ctx.addNewUser();\n          });\n          i0.ɵɵelementStart(5, \"mat-icon\");\n          i0.ɵɵtext(6, \"add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(7, \"\\u00A0 Add New User \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"select\", 5);\n          i0.ɵɵlistener(\"ngModelChange\", function UserListComponent_Template_select_ngModelChange_8_listener($event) {\n            return ctx.selectedRole = $event;\n          })(\"change\", function UserListComponent_Template_select_change_8_listener() {\n            return ctx.onRoleChange();\n          });\n          i0.ɵɵtemplate(9, UserListComponent_option_9_Template, 2, 2, \"option\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\")(11, \"div\", 7)(12, \"div\", 8)(13, \"input\", 9);\n          i0.ɵɵlistener(\"ngModelChange\", function UserListComponent_Template_input_ngModelChange_13_listener($event) {\n            return ctx.searchQuery = $event;\n          })(\"input\", function UserListComponent_Template_input_input_13_listener() {\n            return ctx.onSearchChange();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"mat-icon\", 10);\n          i0.ɵɵtext(15, \"search\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(16, UserListComponent_div_16_Template, 33, 8, \"div\", 11);\n          i0.ɵɵtemplate(17, UserListComponent_div_17_Template, 6, 0, \"div\", 12);\n          i0.ɵɵtemplate(18, UserListComponent_div_18_Template, 7, 0, \"div\", 12);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngModel\", ctx.selectedRole);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.roles);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.filteredUsers.length === 0);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.DefaultValueAccessor, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgModel, i6.MatIcon],\n      styles: [\".maindiv[_ngcontent-%COMP%]{padding:20px;background-color:#f8f9fa;min-height:100vh}.secondarydiv[_ngcontent-%COMP%]{background:white;border-radius:8px;padding:20px;box-shadow:0 2px 4px #0000001a}.searchAndtab[_ngcontent-%COMP%]{margin-bottom:20px}.btn-top[_ngcontent-%COMP%]{background-color:#29578c;color:#fff;border:none;padding:8px 16px;border-radius:4px;font-size:14px}.btn-top[_ngcontent-%COMP%]:hover{background-color:#1e3f63;color:#fff}.search-container[_ngcontent-%COMP%]{position:relative}.search-input-wrapper[_ngcontent-%COMP%]{position:relative;display:flex;align-items:center}.search-input[_ngcontent-%COMP%]{padding:8px 40px 8px 12px;border:1px solid #ddd;border-radius:4px;width:250px;font-size:14px}.search-icon[_ngcontent-%COMP%]{position:absolute;right:12px;color:#666;font-size:18px}.table-container[_ngcontent-%COMP%]{overflow-x:auto}table[_ngcontent-%COMP%]{width:100%;border-collapse:collapse;margin-top:10px}.tablehead[_ngcontent-%COMP%]{background-color:#f8f9fa;border-bottom:2px solid #dee2e6}.tablehead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{padding:12px 8px;text-align:left;font-weight:600;color:#495057;font-size:14px}tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]{border-bottom:1px solid #dee2e6}tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover{background-color:#f8f9fa}tbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:12px 8px;font-size:14px;color:#495057}.badge[_ngcontent-%COMP%]{padding:4px 8px;border-radius:12px;font-size:12px;font-weight:500;text-transform:uppercase}.badge-student[_ngcontent-%COMP%]{background-color:#e3f2fd;color:#1976d2}.badge-teacher[_ngcontent-%COMP%]{background-color:#f3e5f5;color:#7b1fa2}.badge-principal[_ngcontent-%COMP%]{background-color:#fff3e0;color:#f57c00}.badge-admin[_ngcontent-%COMP%]{background-color:#ffebee;color:#d32f2f}.badge-active[_ngcontent-%COMP%]{background-color:#e8f5e8;color:#2e7d32}.badge-inactive[_ngcontent-%COMP%]{background-color:#ffebee;color:#d32f2f}.action-buttons[_ngcontent-%COMP%]{display:flex;gap:5px}.action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:4px 8px;border-radius:4px}.action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px}.pagination-container[_ngcontent-%COMP%]{margin-top:20px;padding-top:20px;border-top:1px solid #dee2e6}.pagination[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px}.page-info[_ngcontent-%COMP%]{padding:8px 12px;background-color:#f8f9fa;border-radius:4px;font-size:14px}.form-select[_ngcontent-%COMP%]{padding:6px 12px;border:1px solid #ddd;border-radius:4px;font-size:14px;background-color:#fff}.spinner-border[_ngcontent-%COMP%]{width:3rem;height:3rem}@media (max-width: 768px){.searchAndtab[_ngcontent-%COMP%]{flex-direction:column;gap:15px}.search-input[_ngcontent-%COMP%]{width:100%}.pagination-container[_ngcontent-%COMP%]{flex-direction:column;gap:15px;text-align:center}}\"]\n    });\n  }\n  return UserListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}