{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/user.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/classes.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction AddStudentComponent_option_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const class_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", class_r1._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate3(\" \", class_r1.className, \"+ (\", class_r1.department, \")+ (\", class_r1.section, \") \");\n  }\n}\nexport let AddStudentComponent = /*#__PURE__*/(() => {\n  class AddStudentComponent {\n    constructor(userService, router, classService) {\n      this.userService = userService;\n      this.router = router;\n      this.classService = classService;\n      this.classes = []; // Holds the classes data\n      // Student model to store form data\n      this.student = {\n        name: '',\n        email: '',\n        password: '',\n        rollno: '',\n        reg_no: '',\n        father_name: '',\n        contact_no: '',\n        address: '',\n        className: '',\n        section: '',\n        department: '',\n        classId: ''\n      };\n      this.getClasses();\n    }\n    // Fetch all classes from the API\n    getClasses() {\n      this.classService.getClasses().subscribe(response => {\n        if (response.success) {\n          this.classes = response.classes;\n          console.log(\"this is clases\", this.classes);\n        }\n      }, error => {\n        console.error('Error fetching classes:', error);\n      });\n    }\n    // Save student data and file (if any)\n    onSave() {\n      const studentData = {\n        name: this.student.name,\n        email: this.student.email,\n        password: this.student.password,\n        rollNo: this.student.rollno,\n        regNo: this.student.reg_no,\n        father_name: this.student.father_name,\n        contact: this.student.contact_no,\n        address: this.student.address,\n        role: 'Student',\n        className: this.student.className,\n        section: this.student.section,\n        department: this.student.department,\n        classId: this.student.classId\n      };\n      // Send student data to the backend using userService\n      this.userService.register(studentData).subscribe({\n        next: response => {\n          // Handle success response\n          console.log('Registration successful:', response);\n          alert('Student registered successfully!');\n          this.router.navigate(['/dashboard/admin/students/student-list']);\n          // Optionally, clear the form\n          this.resetForm();\n        },\n        error: error => {\n          // Handle error response\n          console.error('Error registering student:', error);\n          alert(`Error: ${error.error.message || 'Failed to register student'}`);\n        }\n      });\n    }\n    resetForm() {\n      this.student = {\n        name: '',\n        email: '',\n        password: '',\n        rollno: '',\n        reg_no: '',\n        father_name: '',\n        contact_no: '',\n        address: '',\n        className: '',\n        section: '',\n        department: '',\n        classId: ''\n      };\n    }\n    static #_ = this.ɵfac = function AddStudentComponent_Factory(t) {\n      return new (t || AddStudentComponent)(i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.ClassesService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AddStudentComponent,\n      selectors: [[\"app-add-student\"]],\n      decls: 49,\n      vars: 10,\n      consts: [[1, \"maindiv\"], [1, \"row\"], [1, \"col-lg-6\", \"col-md-12\", \"mb-3\"], [1, \"col-md-12\", \"mb-3\"], [\"for\", \"name\", 1, \"mb-2\"], [\"type\", \"text\", \"name\", \"name\", \"id\", \"name\", \"placeholder\", \"Enter Student Name\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"email\", 1, \"mb-2\"], [\"type\", \"email\", \"name\", \"email\", \"id\", \"email\", \"placeholder\", \"Enter Gmail\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"rollno\", 1, \"mb-2\"], [\"type\", \"text\", \"name\", \"rollno\", \"id\", \"rollno\", \"placeholder\", \"Enter Roll Number\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"reg_no\", 1, \"mb-2\"], [\"type\", \"text\", \"name\", \"reg_no\", \"id\", \"reg_no\", \"placeholder\", \"09039489\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"class\", 1, \"mb-2\"], [\"name\", \"class\", \"id\", \"class\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"selected\", \"\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"father_name\", 1, \"mb-2\"], [\"type\", \"text\", \"name\", \"father_name\", \"id\", \"father_name\", \"placeholder\", \"Father's Name\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"contact_no\", 1, \"mb-2\"], [\"type\", \"text\", \"name\", \"contact_no\", \"id\", \"contact_no\", \"placeholder\", \"Contact Number\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"address\", 1, \"mb-2\"], [\"type\", \"text\", \"name\", \"address\", \"id\", \"address\", \"placeholder\", \"Address\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"password\", 1, \"mb-2\"], [\"type\", \"password\", \"name\", \"password\", \"id\", \"password\", \"placeholder\", \"*******\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-center\", \"my-4\"], [1, \"btn\", \"save\", 3, \"click\"], [\"selected\", \"\", 3, \"value\"]],\n      template: function AddStudentComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\");\n          i0.ɵɵtext(2, \"Add New Student\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 1)(4, \"div\", 2)(5, \"div\", 1)(6, \"div\", 3)(7, \"label\", 4);\n          i0.ɵɵtext(8, \"Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"input\", 5);\n          i0.ɵɵlistener(\"ngModelChange\", function AddStudentComponent_Template_input_ngModelChange_9_listener($event) {\n            return ctx.student.name = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 3)(11, \"label\", 6);\n          i0.ɵɵtext(12, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"input\", 7);\n          i0.ɵɵlistener(\"ngModelChange\", function AddStudentComponent_Template_input_ngModelChange_13_listener($event) {\n            return ctx.student.email = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 3)(15, \"label\", 8);\n          i0.ɵɵtext(16, \"Roll Number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"input\", 9);\n          i0.ɵɵlistener(\"ngModelChange\", function AddStudentComponent_Template_input_ngModelChange_17_listener($event) {\n            return ctx.student.rollno = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 3)(19, \"label\", 10);\n          i0.ɵɵtext(20, \"Reg Number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"input\", 11);\n          i0.ɵɵlistener(\"ngModelChange\", function AddStudentComponent_Template_input_ngModelChange_21_listener($event) {\n            return ctx.student.reg_no = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(22, \"div\", 3)(23, \"label\", 12);\n          i0.ɵɵtext(24, \"Class\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"select\", 13);\n          i0.ɵɵlistener(\"ngModelChange\", function AddStudentComponent_Template_select_ngModelChange_25_listener($event) {\n            return ctx.student.classId = $event;\n          });\n          i0.ɵɵtemplate(26, AddStudentComponent_option_26_Template, 2, 4, \"option\", 14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(27, \"div\", 2)(28, \"div\", 1)(29, \"div\", 3)(30, \"label\", 15);\n          i0.ɵɵtext(31, \"Father's Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"input\", 16);\n          i0.ɵɵlistener(\"ngModelChange\", function AddStudentComponent_Template_input_ngModelChange_32_listener($event) {\n            return ctx.student.father_name = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"div\", 3)(34, \"label\", 17);\n          i0.ɵɵtext(35, \"Contact Number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"input\", 18);\n          i0.ɵɵlistener(\"ngModelChange\", function AddStudentComponent_Template_input_ngModelChange_36_listener($event) {\n            return ctx.student.contact_no = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"div\", 3)(38, \"label\", 19);\n          i0.ɵɵtext(39, \"Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"input\", 20);\n          i0.ɵɵlistener(\"ngModelChange\", function AddStudentComponent_Template_input_ngModelChange_40_listener($event) {\n            return ctx.student.address = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"div\", 3)(42, \"label\", 21);\n          i0.ɵɵtext(43, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"input\", 22);\n          i0.ɵɵlistener(\"ngModelChange\", function AddStudentComponent_Template_input_ngModelChange_44_listener($event) {\n            return ctx.student.password = $event;\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(45, \"div\", 23)(46, \"div\", 24)(47, \"button\", 25);\n          i0.ɵɵlistener(\"click\", function AddStudentComponent_Template_button_click_47_listener() {\n            return ctx.onSave();\n          });\n          i0.ɵɵtext(48, \"Save\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngModel\", ctx.student.name);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.student.email);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.student.rollno);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.student.reg_no);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.student.classId);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.classes);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngModel\", ctx.student.father_name);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.student.contact_no);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.student.address);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.student.password);\n        }\n      },\n      dependencies: [i4.NgForOf, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.DefaultValueAccessor, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgModel],\n      styles: [\"@media screen and (max-width:600px){.maindiv[_ngcontent-%COMP%]{padding:10px}}\"]\n    });\n  }\n  return AddStudentComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}