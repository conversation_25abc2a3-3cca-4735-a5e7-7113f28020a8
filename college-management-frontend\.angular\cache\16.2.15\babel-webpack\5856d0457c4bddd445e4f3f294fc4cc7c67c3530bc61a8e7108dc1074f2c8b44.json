{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/classes.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/icon\";\nfunction ClassFormComponent_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"save\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Save \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction ClassFormComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 19);\n    i0.ɵɵtext(1, \" Saving... \");\n  }\n}\nfunction ClassFormComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"label\", 21)(2, \"input\", 22);\n    i0.ɵɵlistener(\"change\", function ClassFormComponent_div_31_Template_input_change_2_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onSubjectChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"span\", 23);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const subject_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", subject_r4._id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", subject_r4.subjectName, \" \");\n  }\n}\nexport let ClassFormComponent = /*#__PURE__*/(() => {\n  class ClassFormComponent {\n    constructor(classesService, ActivatedRoute, fb, router) {\n      this.classesService = classesService;\n      this.ActivatedRoute = ActivatedRoute;\n      this.fb = fb;\n      this.router = router;\n      this.subjects = [];\n      this.isLoading = false;\n      this.classForm = this.fb.group({\n        className: ['', Validators.required],\n        section: ['', Validators.required],\n        department: ['', Validators.required],\n        subjects: this.fb.array([]) // Array for dynamically selected subjects\n      });\n    }\n\n    ngOnInit() {\n      this.editingSubjectId = this.ActivatedRoute.snapshot.paramMap.get('id');\n      this.getSubjects(); // Fetch subjects when component loads\n    }\n\n    get subjectsFormArray() {\n      return this.classForm.get('subjects');\n    }\n    onSubjectTeacherSelect(subjectId, event) {\n      const teacherId = event.target.value; // Get the selected teacher ID from the event\n      const subjectsArray = this.classForm.get('subjects');\n      const existingIndex = subjectsArray.controls.findIndex(control => control.value.subject === subjectId);\n      if (existingIndex !== -1) {\n        subjectsArray.at(existingIndex).patchValue({\n          teacher: teacherId\n        });\n      } else {\n        subjectsArray.push(this.fb.group({\n          subject: [subjectId],\n          teacher: [teacherId]\n        }));\n      }\n    }\n    // Fetch subjects from API\n    getSubjects() {\n      this.classesService.getSubjects().subscribe(response => {\n        if (response.success) {\n          this.subjects = response.subjects;\n        }\n      });\n    }\n    // Handle checkbox change for subjects\n    onSubjectChange(event) {\n      const subjectsArray = this.classForm.get('subjects');\n      if (event.target.checked) {\n        subjectsArray.push(this.fb.control(event.target.value));\n      } else {\n        const index = subjectsArray.controls.findIndex(x => x.value === event.target.value);\n        subjectsArray.removeAt(index);\n      }\n    }\n    // Submit the form to add a new class\n    onSubmit() {\n      this.isLoading = true;\n      if (this.classForm.valid) {\n        this.classesService.addClass(this.classForm.value).subscribe(response => {\n          if (response.success) {\n            this.isLoading = false;\n            this.router.navigate(['/dashboard/admin/classes']);\n            alert('Class added successfully');\n            this.classForm.reset();\n          } else {\n            alert('Failed to add class');\n          }\n        });\n      }\n    }\n    // Navigate back\n    goBack() {\n      this.router.navigate(['/dashboard/admin/classes']);\n    }\n    static #_ = this.ɵfac = function ClassFormComponent_Factory(t) {\n      return new (t || ClassFormComponent)(i0.ɵɵdirectiveInject(i1.ClassesService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i2.Router));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ClassFormComponent,\n      selectors: [[\"app-class-form\"]],\n      decls: 32,\n      vars: 6,\n      consts: [[1, \"form-container\"], [1, \"form-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"d-flex\", \"gap-2\"], [1, \"btn-back\", 3, \"click\"], [1, \"btn\", \"btn-primary\", \"btn-save\", 3, \"disabled\", \"click\"], [4, \"ngIf\", \"ngIfElse\"], [\"loading\", \"\"], [3, \"formGroup\"], [1, \"form-grid\"], [1, \"form-group\"], [\"for\", \"classname\"], [\"type\", \"text\", \"placeholder\", \"1st year\", \"formControlName\", \"className\", \"name\", \"className\", 1, \"form-input\"], [\"for\", \"section\"], [\"type\", \"text\", \"placeholder\", \"A\", \"formControlName\", \"section\", \"name\", \"section\", 1, \"form-input\"], [\"type\", \"text\", \"placeholder\", \"computer science\", \"formControlName\", \"department\", \"name\", \"section\", 1, \"form-input\"], [1, \"subjects-section\"], [1, \"subjects-label\"], [1, \"subjects-grid\"], [\"class\", \"subject-option\", 4, \"ngFor\", \"ngForOf\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"text-light\", \"me-1\"], [1, \"subject-option\"], [1, \"checkbox-container\"], [\"type\", \"checkbox\", 3, \"value\", \"change\"], [1, \"checkmark\"]],\n      template: function ClassFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 2)(5, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function ClassFormComponent_Template_button_click_5_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵelementStart(6, \"mat-icon\");\n          i0.ɵɵtext(7, \"arrow_back\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(8, \" Back \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function ClassFormComponent_Template_button_click_9_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtemplate(10, ClassFormComponent_ng_container_10_Template, 4, 0, \"ng-container\", 5);\n          i0.ɵɵtemplate(11, ClassFormComponent_ng_template_11_Template, 2, 0, \"ng-template\", null, 6, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"form\", 7)(14, \"div\", 8)(15, \"div\", 9)(16, \"label\", 10);\n          i0.ɵɵtext(17, \"Class Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(18, \"input\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 9)(20, \"label\", 12);\n          i0.ɵɵtext(21, \"Section\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(22, \"input\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 9)(24, \"label\", 12);\n          i0.ɵɵtext(25, \"Department\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(26, \"input\", 14);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 15)(28, \"label\", 16);\n          i0.ɵɵtext(29, \"Select Subjects:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"div\", 17);\n          i0.ɵɵtemplate(31, ClassFormComponent_div_31_Template, 5, 2, \"div\", 18);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const _r1 = i0.ɵɵreference(12);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.editingSubjectId ? \"Update Class\" : \"Add New Class\");\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", ctx.classForm.invalid || ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading)(\"ngIfElse\", _r1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"formGroup\", ctx.classForm);\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"ngForOf\", ctx.subjects);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i5.MatIcon, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.FormGroupDirective, i3.FormControlName],\n      styles: [\".subjects-section[_ngcontent-%COMP%]{margin-top:24px}.subjects-label[_ngcontent-%COMP%]{display:block;margin-bottom:12px;color:#515154;font-weight:500;font-size:14px}.subjects-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(200px,1fr));gap:12px}.checkbox-container[_ngcontent-%COMP%]{display:block;position:relative;padding-left:30px;margin-bottom:12px;cursor:pointer;font-size:14px;color:#000;-webkit-user-select:none;user-select:none}.checkbox-container[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{position:absolute;opacity:0;cursor:pointer;height:0;width:0}.checkmark[_ngcontent-%COMP%]{position:absolute;top:0;left:0;height:20px;width:20px;background-color:#fff;border:1px solid #515154;border-radius:4px}.checkbox-container[_ngcontent-%COMP%]:hover   input[_ngcontent-%COMP%] ~ .checkmark[_ngcontent-%COMP%]{background-color:#f5f5f5}.checkbox-container[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked ~ .checkmark[_ngcontent-%COMP%]{background-color:#11418e;border-color:#11418e}.checkmark[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;display:none}.checkbox-container[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked ~ .checkmark[_ngcontent-%COMP%]:after{display:block}.checkbox-container[_ngcontent-%COMP%]   .checkmark[_ngcontent-%COMP%]:after{left:7px;top:3px;width:5px;height:10px;border:solid white;border-width:0 2px 2px 0;transform:rotate(45deg)}\"]\n    });\n  }\n  return ClassFormComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}