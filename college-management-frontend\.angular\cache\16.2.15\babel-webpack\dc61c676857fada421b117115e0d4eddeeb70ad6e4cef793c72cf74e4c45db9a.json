{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../services/user.service\";\nimport * as i3 from \"../../../services/timetable.service\";\nimport * as i4 from \"../../../services/classes.service\";\nimport * as i5 from \"@angular/material/snack-bar\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/core\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/card\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/input\";\nimport * as i12 from \"@angular/material/form-field\";\nimport * as i13 from \"@angular/forms\";\nimport * as i14 from \"@angular/material/paginator\";\nimport * as i15 from \"@angular/material/progress-spinner\";\nimport * as i16 from \"@angular/material/select\";\nimport * as i17 from \"@angular/material/tooltip\";\nfunction TeacherStudentsComponent_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r0.students.length, \")\");\n  }\n}\nfunction TeacherStudentsComponent_p_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 15);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r1.routeClassName, \" - \", ctx_r1.routeSection, \" \");\n  }\n}\nfunction TeacherStudentsComponent_p_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 15);\n    i0.ɵɵtext(1, \" Students from your assigned classes \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TeacherStudentsComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵelement(1, \"mat-spinner\", 17);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading students...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TeacherStudentsComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"mat-icon\", 19);\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"Error Loading Students\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function TeacherStudentsComponent_div_20_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.refreshData());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r4.error);\n  }\n}\nfunction TeacherStudentsComponent_div_21_mat_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const cls_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", cls_r12._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", cls_r12.className, \" - \", cls_r12.section, \" \");\n  }\n}\nfunction TeacherStudentsComponent_div_21_div_23_mat_card_1_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const student_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(student_r14.contact);\n  }\n}\nfunction TeacherStudentsComponent_div_21_div_23_mat_card_1_div_31_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const student_r14 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(student_r14.program.name);\n  }\n}\nfunction TeacherStudentsComponent_div_21_div_23_mat_card_1_div_31_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const student_r14 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(student_r14.department.name);\n  }\n}\nfunction TeacherStudentsComponent_div_21_div_23_mat_card_1_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtemplate(1, TeacherStudentsComponent_div_21_div_23_mat_card_1_div_31_div_1_Template, 5, 1, \"div\", 47);\n    i0.ɵɵtemplate(2, TeacherStudentsComponent_div_21_div_23_mat_card_1_div_31_div_2_Template, 5, 1, \"div\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const student_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", student_r14.program);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", student_r14.department);\n  }\n}\nfunction TeacherStudentsComponent_div_21_div_23_mat_card_1_button_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 51)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"phone\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TeacherStudentsComponent_div_21_div_23_mat_card_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 39)(1, \"mat-card-header\")(2, \"div\", 40)(3, \"div\", 41)(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"person\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 42)(7, \"h3\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 43);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(11, \"mat-card-content\")(12, \"div\", 44)(13, \"div\", 45)(14, \"div\", 46)(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"badge\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 46)(20, \"mat-icon\");\n    i0.ɵɵtext(21, \"confirmation_number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\");\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 45)(25, \"div\", 46)(26, \"mat-icon\");\n    i0.ɵɵtext(27, \"class\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"span\");\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(30, TeacherStudentsComponent_div_21_div_23_mat_card_1_div_30_Template, 5, 1, \"div\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(31, TeacherStudentsComponent_div_21_div_23_mat_card_1_div_31_Template, 3, 2, \"div\", 48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"mat-card-actions\")(33, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function TeacherStudentsComponent_div_21_div_23_mat_card_1_Template_button_click_33_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r25);\n      const student_r14 = restoredCtx.$implicit;\n      const ctx_r24 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r24.viewStudentDetails(student_r14));\n    });\n    i0.ɵɵelementStart(34, \"mat-icon\");\n    i0.ɵɵtext(35, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(36, \" View Details \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(37, TeacherStudentsComponent_div_21_div_23_mat_card_1_button_37_Template, 3, 0, \"button\", 50);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const student_r14 = ctx.$implicit;\n    const ctx_r13 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(student_r14.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(student_r14.email);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(student_r14.rollNo || \"N/A\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(student_r14.regNo || \"N/A\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r13.getStudentDisplayInfo(student_r14));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", student_r14.contact);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", student_r14.program || student_r14.department);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", student_r14.contact);\n  }\n}\nfunction TeacherStudentsComponent_div_21_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtemplate(1, TeacherStudentsComponent_div_21_div_23_mat_card_1_Template, 38, 8, \"mat-card\", 38);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r9.paginatedStudents);\n  }\n}\nfunction TeacherStudentsComponent_div_21_div_24_p_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"No students match your current filters.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TeacherStudentsComponent_div_21_div_24_p_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"You don't have any students assigned yet.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TeacherStudentsComponent_div_21_div_24_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function TeacherStudentsComponent_div_21_div_24_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r29 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r29.clearFilters());\n    });\n    i0.ɵɵtext(1, \" Clear Filters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TeacherStudentsComponent_div_21_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"mat-icon\", 53);\n    i0.ɵɵtext(2, \"groups\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"No Students Found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, TeacherStudentsComponent_div_21_div_24_p_5_Template, 2, 0, \"p\", 13);\n    i0.ɵɵtemplate(6, TeacherStudentsComponent_div_21_div_24_p_6_Template, 2, 0, \"p\", 13);\n    i0.ɵɵtemplate(7, TeacherStudentsComponent_div_21_div_24_button_7_Template, 2, 0, \"button\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.searchQuery || ctx_r10.selectedClassId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.searchQuery && !ctx_r10.selectedClassId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.searchQuery || ctx_r10.selectedClassId);\n  }\n}\nconst _c0 = function () {\n  return [10, 20, 50];\n};\nfunction TeacherStudentsComponent_div_21_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"mat-paginator\", 57);\n    i0.ɵɵlistener(\"page\", function TeacherStudentsComponent_div_21_div_25_Template_mat_paginator_page_1_listener($event) {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r31 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r31.goToPage($event.pageIndex + 1));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"length\", ctx_r11.students.length)(\"pageSize\", ctx_r11.itemsPerPage)(\"pageSizeOptions\", i0.ɵɵpureFunction0(4, _c0))(\"pageIndex\", ctx_r11.currentPage - 1);\n  }\n}\nfunction TeacherStudentsComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"mat-card\", 21)(2, \"mat-card-content\")(3, \"div\", 22)(4, \"div\", 23)(5, \"mat-form-field\", 24)(6, \"mat-label\");\n    i0.ɵɵtext(7, \"Search Students\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"input\", 25);\n    i0.ɵɵlistener(\"ngModelChange\", function TeacherStudentsComponent_div_21_Template_input_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r33 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r33.searchQuery = $event);\n    })(\"input\", function TeacherStudentsComponent_div_21_Template_input_input_8_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.onSearch());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-icon\", 26);\n    i0.ɵɵtext(10, \"search\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 27)(12, \"mat-form-field\", 24)(13, \"mat-label\");\n    i0.ɵɵtext(14, \"Class\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"mat-select\", 28);\n    i0.ɵɵlistener(\"ngModelChange\", function TeacherStudentsComponent_div_21_Template_mat_select_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.selectedClassId = $event);\n    })(\"selectionChange\", function TeacherStudentsComponent_div_21_Template_mat_select_selectionChange_15_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r37 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r37.onClassChange());\n    });\n    i0.ɵɵelementStart(16, \"mat-option\", 29);\n    i0.ɵɵtext(17, \"All Classes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, TeacherStudentsComponent_div_21_mat_option_18_Template, 2, 3, \"mat-option\", 30);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"div\", 31)(20, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function TeacherStudentsComponent_div_21_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.clearFilters());\n    });\n    i0.ɵɵelementStart(21, \"mat-icon\");\n    i0.ɵɵtext(22, \"clear\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵtemplate(23, TeacherStudentsComponent_div_21_div_23_Template, 2, 1, \"div\", 33);\n    i0.ɵɵtemplate(24, TeacherStudentsComponent_div_21_div_24_Template, 8, 3, \"div\", 34);\n    i0.ɵɵtemplate(25, TeacherStudentsComponent_div_21_div_25_Template, 2, 5, \"div\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngModel\", ctx_r5.searchQuery);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngModel\", ctx_r5.selectedClassId);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.teacherClasses);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.paginatedStudents.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.loading && ctx_r5.paginatedStudents.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.totalPages > 1);\n  }\n}\nexport class TeacherStudentsComponent {\n  constructor(route, router, userService, timetableService, classesService, snackBar) {\n    this.route = route;\n    this.router = router;\n    this.userService = userService;\n    this.timetableService = timetableService;\n    this.classesService = classesService;\n    this.snackBar = snackBar;\n    // UI State\n    this.loading = true;\n    this.error = null;\n    this.students = [];\n    this.allStudents = [];\n    this.teacherClasses = [];\n    // Filters\n    this.searchQuery = '';\n    this.selectedClassId = '';\n    this.selectedDepartment = '';\n    this.selectedProgram = '';\n    // Pagination\n    this.currentPage = 1;\n    this.itemsPerPage = 20;\n    this.totalItems = 0;\n    // Route params\n    this.routeClassId = '';\n    this.routeClassName = '';\n    this.routeSection = '';\n  }\n  ngOnInit() {\n    this.currentUser = this.userService.getUserFromLocalStorage()?.user;\n    // Get route parameters\n    this.route.queryParams.subscribe(params => {\n      this.routeClassId = params['classId'] || '';\n      this.routeClassName = params['className'] || '';\n      this.routeSection = params['section'] || '';\n      if (this.routeClassId) {\n        this.selectedClassId = this.routeClassId;\n      }\n    });\n    if (this.currentUser) {\n      this.loadTeacherData();\n    } else {\n      this.error = 'User not found';\n      this.loading = false;\n    }\n  }\n  loadTeacherData() {\n    this.loading = true;\n    this.error = null;\n    // First load teacher's classes\n    this.timetableService.getTimetableByTeacher(this.currentUser._id).subscribe({\n      next: response => {\n        if (response.success) {\n          this.processTeacherClasses(response.timetable);\n          this.loadStudents();\n        } else {\n          this.error = 'Failed to load teacher classes';\n          this.loading = false;\n        }\n      },\n      error: error => {\n        console.error('Error loading teacher classes:', error);\n        this.error = 'Error loading teacher data';\n        this.loading = false;\n        this.snackBar.open('Failed to load teacher classes', 'Close', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  processTeacherClasses(timetableData) {\n    // Group timetable entries by class to get unique classes\n    const classMap = new Map();\n    timetableData.forEach(entry => {\n      const classId = entry.class._id;\n      if (!classMap.has(classId)) {\n        classMap.set(classId, {\n          _id: entry.class._id,\n          className: entry.class.className,\n          section: entry.class.section,\n          department: entry.department,\n          program: entry.program,\n          semester: entry.class.semester\n        });\n      }\n    });\n    this.teacherClasses = Array.from(classMap.values());\n  }\n  loadStudents() {\n    // Load all students and filter by teacher's classes\n    this.userService.getUsersByRole('Student').subscribe({\n      next: response => {\n        if (response.success) {\n          this.allStudents = response.users;\n          this.filterStudentsByTeacherClasses();\n        } else {\n          this.error = 'Failed to load students';\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading students:', error);\n        this.error = 'Error loading students data';\n        this.loading = false;\n        this.snackBar.open('Failed to load students', 'Close', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  filterStudentsByTeacherClasses() {\n    // Get class IDs that the teacher teaches\n    const teacherClassIds = this.teacherClasses.map(cls => cls._id);\n    // Filter students who belong to teacher's classes\n    let filteredStudents = this.allStudents.filter(student => student.classId && teacherClassIds.includes(student.classId._id));\n    // Apply additional filters\n    if (this.selectedClassId) {\n      filteredStudents = filteredStudents.filter(student => student.classId && student.classId._id === this.selectedClassId);\n    }\n    if (this.selectedDepartment) {\n      filteredStudents = filteredStudents.filter(student => student.department && student.department._id === this.selectedDepartment);\n    }\n    if (this.selectedProgram) {\n      filteredStudents = filteredStudents.filter(student => student.program && student.program._id === this.selectedProgram);\n    }\n    if (this.searchQuery) {\n      const query = this.searchQuery.toLowerCase();\n      filteredStudents = filteredStudents.filter(student => student.name.toLowerCase().includes(query) || student.email.toLowerCase().includes(query) || student.rollNo && student.rollNo.toLowerCase().includes(query) || student.regNo && student.regNo.toLowerCase().includes(query));\n    }\n    this.students = filteredStudents;\n    this.totalItems = this.students.length;\n  }\n  // Filter methods\n  applyFilters() {\n    this.currentPage = 1;\n    this.filterStudentsByTeacherClasses();\n  }\n  clearFilters() {\n    this.searchQuery = '';\n    this.selectedClassId = this.routeClassId; // Keep route class if provided\n    this.selectedDepartment = '';\n    this.selectedProgram = '';\n    this.currentPage = 1;\n    this.filterStudentsByTeacherClasses();\n  }\n  onClassChange() {\n    this.applyFilters();\n  }\n  onSearch() {\n    this.applyFilters();\n  }\n  // Pagination\n  get paginatedStudents() {\n    const startIndex = (this.currentPage - 1) * this.itemsPerPage;\n    return this.students.slice(startIndex, startIndex + this.itemsPerPage);\n  }\n  get totalPages() {\n    return Math.ceil(this.students.length / this.itemsPerPage);\n  }\n  nextPage() {\n    if (this.currentPage < this.totalPages) {\n      this.currentPage++;\n    }\n  }\n  previousPage() {\n    if (this.currentPage > 1) {\n      this.currentPage--;\n    }\n  }\n  goToPage(page) {\n    if (page >= 1 && page <= this.totalPages) {\n      this.currentPage = page;\n    }\n  }\n  // Navigation methods\n  viewStudentDetails(student) {\n    this.router.navigate(['/dashboard/teacher/student-detail'], {\n      queryParams: {\n        studentId: student._id,\n        studentName: student.name\n      }\n    });\n  }\n  markAttendanceForClass() {\n    if (this.selectedClassId) {\n      this.router.navigate(['/dashboard/teacher/attendance'], {\n        queryParams: {\n          classId: this.selectedClassId\n        }\n      });\n    } else {\n      this.snackBar.open('Please select a class first', 'Close', {\n        duration: 3000\n      });\n    }\n  }\n  // Utility methods\n  refreshData() {\n    this.loadTeacherData();\n  }\n  getClassDisplayName(classId) {\n    const cls = this.teacherClasses.find(c => c._id === classId);\n    return cls ? `${cls.className} - ${cls.section}` : 'Unknown Class';\n  }\n  getStudentDisplayInfo(student) {\n    if (student.classId) {\n      return `${student.classId.className} - ${student.classId.section}`;\n    }\n    return 'No Class Assigned';\n  }\n  static #_ = this.ɵfac = function TeacherStudentsComponent_Factory(t) {\n    return new (t || TeacherStudentsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.TimetableService), i0.ɵɵdirectiveInject(i4.ClassesService), i0.ɵɵdirectiveInject(i5.MatSnackBar));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: TeacherStudentsComponent,\n    selectors: [[\"app-teacher-students\"]],\n    decls: 22,\n    vars: 7,\n    consts: [[1, \"teacher-students-container\"], [1, \"page-header\"], [1, \"header-content\"], [1, \"page-title\"], [1, \"title-icon\"], [\"class\", \"student-count\", 4, \"ngIf\"], [\"class\", \"page-subtitle\", 4, \"ngIf\"], [1, \"header-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Refresh\", 1, \"refresh-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"attendance-btn\", 3, \"disabled\", \"click\"], [1, \"d-none\", \"d-md-inline\"], [\"class\", \"loading-state\", 4, \"ngIf\"], [\"class\", \"error-state\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"student-count\"], [1, \"page-subtitle\"], [1, \"loading-state\"], [\"diameter\", \"40\"], [1, \"error-state\"], [1, \"error-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"filters-card\"], [1, \"filters-row\"], [1, \"search-field\"], [\"appearance\", \"outline\", 1, \"compact-field\"], [\"matInput\", \"\", \"placeholder\", \"Name, roll number, email...\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [\"matSuffix\", \"\"], [1, \"filter-field\"], [3, \"ngModel\", \"ngModelChange\", \"selectionChange\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"filter-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Clear Filters\", 1, \"clear-btn\", 3, \"click\"], [\"class\", \"students-grid\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"pagination-container\", 4, \"ngIf\"], [3, \"value\"], [1, \"students-grid\"], [\"class\", \"student-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"student-card\"], [1, \"student-header\"], [1, \"student-avatar\"], [1, \"student-title\"], [1, \"student-email\"], [1, \"student-details\"], [1, \"detail-row\"], [1, \"detail-item\"], [\"class\", \"detail-item\", 4, \"ngIf\"], [\"class\", \"detail-row\", 4, \"ngIf\"], [\"mat-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Contact Student\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Contact Student\"], [1, \"empty-state\"], [1, \"empty-icon\"], [\"mat-button\", \"\", 3, \"click\", 4, \"ngIf\"], [\"mat-button\", \"\", 3, \"click\"], [1, \"pagination-container\"], [\"showFirstLastButtons\", \"\", 3, \"length\", \"pageSize\", \"pageSizeOptions\", \"pageIndex\", \"page\"]],\n    template: function TeacherStudentsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3)(4, \"mat-icon\", 4);\n        i0.ɵɵtext(5, \"groups\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(6, \" My Students \");\n        i0.ɵɵtemplate(7, TeacherStudentsComponent_span_7_Template, 2, 1, \"span\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(8, TeacherStudentsComponent_p_8_Template, 2, 2, \"p\", 6);\n        i0.ɵɵtemplate(9, TeacherStudentsComponent_p_9_Template, 2, 0, \"p\", 6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"div\", 7)(11, \"button\", 8);\n        i0.ɵɵlistener(\"click\", function TeacherStudentsComponent_Template_button_click_11_listener() {\n          return ctx.refreshData();\n        });\n        i0.ɵɵelementStart(12, \"mat-icon\");\n        i0.ɵɵtext(13, \"refresh\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(14, \"button\", 9);\n        i0.ɵɵlistener(\"click\", function TeacherStudentsComponent_Template_button_click_14_listener() {\n          return ctx.markAttendanceForClass();\n        });\n        i0.ɵɵelementStart(15, \"mat-icon\");\n        i0.ɵɵtext(16, \"fact_check\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"span\", 10);\n        i0.ɵɵtext(18, \"Mark Attendance\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵtemplate(19, TeacherStudentsComponent_div_19_Template, 4, 0, \"div\", 11);\n        i0.ɵɵtemplate(20, TeacherStudentsComponent_div_20_Template, 11, 1, \"div\", 12);\n        i0.ɵɵtemplate(21, TeacherStudentsComponent_div_21_Template, 26, 6, \"div\", 13);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.routeClassName);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.routeClassName);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"disabled\", !ctx.selectedClassId);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error);\n      }\n    },\n    dependencies: [i6.NgForOf, i6.NgIf, i7.MatOption, i8.MatButton, i8.MatIconButton, i9.MatCard, i9.MatCardActions, i9.MatCardContent, i9.MatCardHeader, i10.MatIcon, i11.MatInput, i12.MatFormField, i12.MatLabel, i12.MatSuffix, i13.DefaultValueAccessor, i13.NgControlStatus, i13.NgModel, i14.MatPaginator, i15.MatProgressSpinner, i16.MatSelect, i17.MatTooltip],\n    styles: [\".teacher-students-container[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  min-height: 100vh;\\n  padding: 15px;\\n}\\n\\n\\n\\n.page-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n  padding: 15px 0;\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.page-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #2c3e50;\\n  font-size: 1.8rem;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n\\n.title-icon[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  color: #3498db;\\n}\\n\\n.student-count[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);\\n  color: white;\\n  padding: 2px 8px;\\n  border-radius: 10px;\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n  margin-left: 10px;\\n}\\n\\n.page-subtitle[_ngcontent-%COMP%] {\\n  margin: 5px 0 0 0;\\n  color: #7f8c8d;\\n  font-size: 0.9rem;\\n}\\n\\n.header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.refresh-btn[_ngcontent-%COMP%], .attendance-btn[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n\\n.attendance-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);\\n  border: none;\\n  box-shadow: 0 4px 6px rgba(39, 174, 96, 0.3);\\n}\\n\\n.attendance-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 6px 10px rgba(39, 174, 96, 0.4);\\n}\\n\\n.attendance-btn[_ngcontent-%COMP%]:disabled {\\n  background: #bdc3c7;\\n  box-shadow: none;\\n  transform: none;\\n}\\n\\n\\n\\n.filters-card[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.filters-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  padding: 15px !important;\\n}\\n\\n.filters-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n}\\n\\n.search-field[_ngcontent-%COMP%] {\\n  flex: 2;\\n}\\n\\n.filter-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.compact-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.compact-field[_ngcontent-%COMP%]   .mat-form-field-wrapper[_ngcontent-%COMP%] {\\n  padding-bottom: 0;\\n}\\n\\n.compact-field[_ngcontent-%COMP%]   .mat-form-field-infix[_ngcontent-%COMP%] {\\n  padding: 8px 0;\\n  border-top: none;\\n}\\n\\n.compact-field[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n\\n.clear-btn[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  transition: all 0.3s ease;\\n}\\n\\n.clear-btn[_ngcontent-%COMP%]:hover {\\n  color: #495057;\\n  background-color: #f8f9fa;\\n}\\n\\n\\n\\n.students-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 20px;\\n}\\n\\n.student-card[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease;\\n  border: 2px solid transparent;\\n}\\n\\n.student-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\\n  border-color: #3498db;\\n}\\n\\n.student-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  width: 100%;\\n}\\n\\n.student-avatar[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n}\\n\\n.student-avatar[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n}\\n\\n.student-title[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.student-title[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #2c3e50;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n}\\n\\n.student-email[_ngcontent-%COMP%] {\\n  color: #7f8c8d;\\n  font-size: 0.8rem;\\n}\\n\\n.student-details[_ngcontent-%COMP%] {\\n  margin: 15px 0;\\n}\\n\\n.detail-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n  margin-bottom: 10px;\\n}\\n\\n.detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  color: #34495e;\\n  font-size: 0.85rem;\\n  flex: 1;\\n}\\n\\n.detail-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  width: 16px;\\n  height: 16px;\\n  color: #7f8c8d;\\n}\\n\\n\\n\\n.student-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  border-top: 1px solid #ecf0f1;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.student-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  padding: 6px 12px;\\n}\\n\\n\\n\\n.loading-state[_ngcontent-%COMP%], .error-state[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n  text-align: center;\\n}\\n\\n.loading-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .error-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n  color: #7f8c8d;\\n}\\n\\n.error-state[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  margin-bottom: 10px;\\n}\\n\\n.error-icon[_ngcontent-%COMP%], .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  color: #bdc3c7;\\n  margin-bottom: 15px;\\n}\\n\\n.error-icon[_ngcontent-%COMP%] {\\n  color: #e74c3c;\\n}\\n\\n\\n\\n.pagination-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-top: 20px;\\n}\\n\\n\\n\\n@media (max-width: 1200px) {\\n  .teacher-students-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n\\n  .students-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\\n    gap: 15px;\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .page-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 15px;\\n  }\\n\\n  .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  .header-actions[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: space-between;\\n  }\\n\\n  .filters-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 10px;\\n  }\\n\\n  .search-field[_ngcontent-%COMP%], .filter-field[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n\\n  .filter-actions[_ngcontent-%COMP%] {\\n    width: 100%;\\n    display: flex;\\n    justify-content: flex-end;\\n  }\\n\\n  .students-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 15px;\\n  }\\n\\n  .student-card[_ngcontent-%COMP%] {\\n    margin-bottom: 15px;\\n  }\\n\\n  .student-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 8px;\\n  }\\n\\n  .detail-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 8px;\\n  }\\n\\n  .student-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 8px;\\n  }\\n\\n  .student-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n}\\n\\n@media (max-width: 576px) {\\n  .teacher-students-container[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n\\n  .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.3rem;\\n  }\\n\\n  .title-icon[_ngcontent-%COMP%] {\\n    font-size: 1.3rem;\\n  }\\n\\n  .student-count[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n    padding: 1px 6px;\\n  }\\n\\n  .student-card[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n\\n  .student-avatar[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n\\n  .student-avatar[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n    font-size: 1.2rem;\\n  }\\n\\n  .student-title[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n\\n  .student-email[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n  }\\n\\n  .detail-item[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "students", "length", "ɵɵtextInterpolate2", "ctx_r1", "routeClassName", "routeSection", "ɵɵelement", "ɵɵlistener", "TeacherStudentsComponent_div_20_Template_button_click_7_listener", "ɵɵrestoreView", "_r7", "ctx_r6", "ɵɵnextContext", "ɵɵresetView", "refreshData", "ɵɵtextInterpolate", "ctx_r4", "error", "ɵɵproperty", "cls_r12", "_id", "className", "section", "student_r14", "contact", "program", "name", "department", "ɵɵtemplate", "TeacherStudentsComponent_div_21_div_23_mat_card_1_div_31_div_1_Template", "TeacherStudentsComponent_div_21_div_23_mat_card_1_div_31_div_2_Template", "TeacherStudentsComponent_div_21_div_23_mat_card_1_div_30_Template", "TeacherStudentsComponent_div_21_div_23_mat_card_1_div_31_Template", "TeacherStudentsComponent_div_21_div_23_mat_card_1_Template_button_click_33_listener", "restoredCtx", "_r25", "$implicit", "ctx_r24", "viewStudentDetails", "TeacherStudentsComponent_div_21_div_23_mat_card_1_button_37_Template", "email", "rollNo", "regNo", "ctx_r13", "getStudentDisplayInfo", "TeacherStudentsComponent_div_21_div_23_mat_card_1_Template", "ctx_r9", "paginatedStudents", "TeacherStudentsComponent_div_21_div_24_button_7_Template_button_click_0_listener", "_r30", "ctx_r29", "clearFilters", "TeacherStudentsComponent_div_21_div_24_p_5_Template", "TeacherStudentsComponent_div_21_div_24_p_6_Template", "TeacherStudentsComponent_div_21_div_24_button_7_Template", "ctx_r10", "searchQuery", "selectedClassId", "TeacherStudentsComponent_div_21_div_25_Template_mat_paginator_page_1_listener", "$event", "_r32", "ctx_r31", "goToPage", "pageIndex", "ctx_r11", "itemsPerPage", "ɵɵpureFunction0", "_c0", "currentPage", "TeacherStudentsComponent_div_21_Template_input_ngModelChange_8_listener", "_r34", "ctx_r33", "TeacherStudentsComponent_div_21_Template_input_input_8_listener", "ctx_r35", "onSearch", "TeacherStudentsComponent_div_21_Template_mat_select_ngModelChange_15_listener", "ctx_r36", "TeacherStudentsComponent_div_21_Template_mat_select_selectionChange_15_listener", "ctx_r37", "onClassChange", "TeacherStudentsComponent_div_21_mat_option_18_Template", "TeacherStudentsComponent_div_21_Template_button_click_20_listener", "ctx_r38", "TeacherStudentsComponent_div_21_div_23_Template", "TeacherStudentsComponent_div_21_div_24_Template", "TeacherStudentsComponent_div_21_div_25_Template", "ctx_r5", "teacherClasses", "loading", "totalPages", "TeacherStudentsComponent", "constructor", "route", "router", "userService", "timetableService", "classesService", "snackBar", "allStudents", "selectedDepartment", "selectedProgram", "totalItems", "routeClassId", "ngOnInit", "currentUser", "getUserFromLocalStorage", "user", "queryParams", "subscribe", "params", "loadTeacherData", "getTimetableByTeacher", "next", "response", "success", "processTeacherClasses", "timetable", "loadStudents", "console", "open", "duration", "timetableData", "classMap", "Map", "for<PERSON>ach", "entry", "classId", "class", "has", "set", "semester", "Array", "from", "values", "getUsersByRole", "users", "filterStudentsByTeacherClasses", "teacherClassIds", "map", "cls", "filteredStudents", "filter", "student", "includes", "query", "toLowerCase", "applyFilters", "startIndex", "slice", "Math", "ceil", "nextPage", "previousPage", "page", "navigate", "studentId", "studentName", "markAttendanceForClass", "getClassDisplayName", "find", "c", "_", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "UserService", "i3", "TimetableService", "i4", "ClassesService", "i5", "MatSnackBar", "_2", "selectors", "decls", "vars", "consts", "template", "TeacherStudentsComponent_Template", "rf", "ctx", "TeacherStudentsComponent_span_7_Template", "TeacherStudentsComponent_p_8_Template", "TeacherStudentsComponent_p_9_Template", "TeacherStudentsComponent_Template_button_click_11_listener", "TeacherStudentsComponent_Template_button_click_14_listener", "TeacherStudentsComponent_div_19_Template", "TeacherStudentsComponent_div_20_Template", "TeacherStudentsComponent_div_21_Template"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\teacher-dashboard\\teacher-students\\teacher-students.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\teacher-dashboard\\teacher-students\\teacher-students.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { UserService } from '../../../services/user.service';\r\nimport { TimetableService } from '../../../services/timetable.service';\r\nimport { ClassesService } from '../../../services/classes.service';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\n\r\ninterface Student {\r\n  _id: string;\r\n  name: string;\r\n  email: string;\r\n  rollNo: string;\r\n  regNo: string;\r\n  contact?: string;\r\n  father_name?: string;\r\n  father_contact?: string;\r\n  address?: string;\r\n  program?: {\r\n    _id: string;\r\n    name: string;\r\n  };\r\n  department?: {\r\n    _id: string;\r\n    name: string;\r\n  };\r\n  classId?: {\r\n    _id: string;\r\n    className: string;\r\n    section: string;\r\n  };\r\n  semester?: number;\r\n  academicYear?: string;\r\n  isActive: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-teacher-students',\r\n  templateUrl: './teacher-students.component.html',\r\n  styleUrls: ['./teacher-students.component.css']\r\n})\r\nexport class TeacherStudentsComponent implements OnInit {\r\n  // UI State\r\n  loading = true;\r\n  error: string | null = null;\r\n\r\n  // Data\r\n  currentUser: any;\r\n  students: Student[] = [];\r\n  allStudents: Student[] = [];\r\n  teacherClasses: any[] = [];\r\n\r\n  // Filters\r\n  searchQuery = '';\r\n  selectedClassId = '';\r\n  selectedDepartment = '';\r\n  selectedProgram = '';\r\n\r\n  // Pagination\r\n  currentPage = 1;\r\n  itemsPerPage = 20;\r\n  totalItems = 0;\r\n\r\n  // Route params\r\n  routeClassId = '';\r\n  routeClassName = '';\r\n  routeSection = '';\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private userService: UserService,\r\n    private timetableService: TimetableService,\r\n    private classesService: ClassesService,\r\n    private snackBar: MatSnackBar\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.currentUser = this.userService.getUserFromLocalStorage()?.user;\r\n\r\n    // Get route parameters\r\n    this.route.queryParams.subscribe(params => {\r\n      this.routeClassId = params['classId'] || '';\r\n      this.routeClassName = params['className'] || '';\r\n      this.routeSection = params['section'] || '';\r\n\r\n      if (this.routeClassId) {\r\n        this.selectedClassId = this.routeClassId;\r\n      }\r\n    });\r\n\r\n    if (this.currentUser) {\r\n      this.loadTeacherData();\r\n    } else {\r\n      this.error = 'User not found';\r\n      this.loading = false;\r\n    }\r\n  }\r\n\r\n  loadTeacherData(): void {\r\n    this.loading = true;\r\n    this.error = null;\r\n\r\n    // First load teacher's classes\r\n    this.timetableService.getTimetableByTeacher(this.currentUser._id).subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.processTeacherClasses(response.timetable);\r\n          this.loadStudents();\r\n        } else {\r\n          this.error = 'Failed to load teacher classes';\r\n          this.loading = false;\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading teacher classes:', error);\r\n        this.error = 'Error loading teacher data';\r\n        this.loading = false;\r\n        this.snackBar.open('Failed to load teacher classes', 'Close', { duration: 3000 });\r\n      }\r\n    });\r\n  }\r\n\r\n  processTeacherClasses(timetableData: any[]): void {\r\n    // Group timetable entries by class to get unique classes\r\n    const classMap = new Map<string, any>();\r\n\r\n    timetableData.forEach(entry => {\r\n      const classId = entry.class._id;\r\n\r\n      if (!classMap.has(classId)) {\r\n        classMap.set(classId, {\r\n          _id: entry.class._id,\r\n          className: entry.class.className,\r\n          section: entry.class.section,\r\n          department: entry.department,\r\n          program: entry.program,\r\n          semester: entry.class.semester\r\n        });\r\n      }\r\n    });\r\n\r\n    this.teacherClasses = Array.from(classMap.values());\r\n  }\r\n\r\n  loadStudents(): void {\r\n    // Load all students and filter by teacher's classes\r\n    this.userService.getUsersByRole('Student').subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.allStudents = response.users;\r\n          this.filterStudentsByTeacherClasses();\r\n        } else {\r\n          this.error = 'Failed to load students';\r\n        }\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading students:', error);\r\n        this.error = 'Error loading students data';\r\n        this.loading = false;\r\n        this.snackBar.open('Failed to load students', 'Close', { duration: 3000 });\r\n      }\r\n    });\r\n  }\r\n\r\n  filterStudentsByTeacherClasses(): void {\r\n    // Get class IDs that the teacher teaches\r\n    const teacherClassIds = this.teacherClasses.map(cls => cls._id);\r\n\r\n    // Filter students who belong to teacher's classes\r\n    let filteredStudents = this.allStudents.filter(student =>\r\n      student.classId && teacherClassIds.includes(student.classId._id)\r\n    );\r\n\r\n    // Apply additional filters\r\n    if (this.selectedClassId) {\r\n      filteredStudents = filteredStudents.filter(student =>\r\n        student.classId && student.classId._id === this.selectedClassId\r\n      );\r\n    }\r\n\r\n    if (this.selectedDepartment) {\r\n      filteredStudents = filteredStudents.filter(student =>\r\n        student.department && student.department._id === this.selectedDepartment\r\n      );\r\n    }\r\n\r\n    if (this.selectedProgram) {\r\n      filteredStudents = filteredStudents.filter(student =>\r\n        student.program && student.program._id === this.selectedProgram\r\n      );\r\n    }\r\n\r\n    if (this.searchQuery) {\r\n      const query = this.searchQuery.toLowerCase();\r\n      filteredStudents = filteredStudents.filter(student =>\r\n        student.name.toLowerCase().includes(query) ||\r\n        student.email.toLowerCase().includes(query) ||\r\n        (student.rollNo && student.rollNo.toLowerCase().includes(query)) ||\r\n        (student.regNo && student.regNo.toLowerCase().includes(query))\r\n      );\r\n    }\r\n\r\n    this.students = filteredStudents;\r\n    this.totalItems = this.students.length;\r\n  }\r\n\r\n  // Filter methods\r\n  applyFilters(): void {\r\n    this.currentPage = 1;\r\n    this.filterStudentsByTeacherClasses();\r\n  }\r\n\r\n  clearFilters(): void {\r\n    this.searchQuery = '';\r\n    this.selectedClassId = this.routeClassId; // Keep route class if provided\r\n    this.selectedDepartment = '';\r\n    this.selectedProgram = '';\r\n    this.currentPage = 1;\r\n    this.filterStudentsByTeacherClasses();\r\n  }\r\n\r\n  onClassChange(): void {\r\n    this.applyFilters();\r\n  }\r\n\r\n  onSearch(): void {\r\n    this.applyFilters();\r\n  }\r\n\r\n  // Pagination\r\n  get paginatedStudents(): Student[] {\r\n    const startIndex = (this.currentPage - 1) * this.itemsPerPage;\r\n    return this.students.slice(startIndex, startIndex + this.itemsPerPage);\r\n  }\r\n\r\n  get totalPages(): number {\r\n    return Math.ceil(this.students.length / this.itemsPerPage);\r\n  }\r\n\r\n  nextPage(): void {\r\n    if (this.currentPage < this.totalPages) {\r\n      this.currentPage++;\r\n    }\r\n  }\r\n\r\n  previousPage(): void {\r\n    if (this.currentPage > 1) {\r\n      this.currentPage--;\r\n    }\r\n  }\r\n\r\n  goToPage(page: number): void {\r\n    if (page >= 1 && page <= this.totalPages) {\r\n      this.currentPage = page;\r\n    }\r\n  }\r\n\r\n  // Navigation methods\r\n  viewStudentDetails(student: Student): void {\r\n    this.router.navigate(['/dashboard/teacher/student-detail'], {\r\n      queryParams: {\r\n        studentId: student._id,\r\n        studentName: student.name\r\n      }\r\n    });\r\n  }\r\n\r\n  markAttendanceForClass(): void {\r\n    if (this.selectedClassId) {\r\n      this.router.navigate(['/dashboard/teacher/attendance'], {\r\n        queryParams: {\r\n          classId: this.selectedClassId\r\n        }\r\n      });\r\n    } else {\r\n      this.snackBar.open('Please select a class first', 'Close', { duration: 3000 });\r\n    }\r\n  }\r\n\r\n  // Utility methods\r\n  refreshData(): void {\r\n    this.loadTeacherData();\r\n  }\r\n\r\n  getClassDisplayName(classId: string): string {\r\n    const cls = this.teacherClasses.find(c => c._id === classId);\r\n    return cls ? `${cls.className} - ${cls.section}` : 'Unknown Class';\r\n  }\r\n\r\n  getStudentDisplayInfo(student: Student): string {\r\n    if (student.classId) {\r\n      return `${student.classId.className} - ${student.classId.section}`;\r\n    }\r\n    return 'No Class Assigned';\r\n  }\r\n}\r\n", "\r\n<div class=\"teacher-students-container\">\r\n  <!-- Header -->\r\n  <div class=\"page-header\">\r\n    <div class=\"header-content\">\r\n      <h1 class=\"page-title\">\r\n        <mat-icon class=\"title-icon\">groups</mat-icon>\r\n        My Students\r\n        <span class=\"student-count\" *ngIf=\"!loading\">({{ students.length }})</span>\r\n      </h1>\r\n      <p class=\"page-subtitle\" *ngIf=\"routeClassName\">\r\n        {{ routeClassName }} - {{ routeSection }}\r\n      </p>\r\n      <p class=\"page-subtitle\" *ngIf=\"!routeClassName\">\r\n        Students from your assigned classes\r\n      </p>\r\n    </div>\r\n    <div class=\"header-actions\">\r\n      <button mat-icon-button (click)=\"refreshData()\" matTooltip=\"Refresh\" class=\"refresh-btn\">\r\n        <mat-icon>refresh</mat-icon>\r\n      </button>\r\n      <button mat-raised-button color=\"primary\" (click)=\"markAttendanceForClass()\"\r\n              [disabled]=\"!selectedClassId\" class=\"attendance-btn\">\r\n        <mat-icon>fact_check</mat-icon>\r\n        <span class=\"d-none d-md-inline\">Mark Attendance</span>\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Loading State -->\r\n  <div *ngIf=\"loading\" class=\"loading-state\">\r\n    <mat-spinner diameter=\"40\"></mat-spinner>\r\n    <p>Loading students...</p>\r\n  </div>\r\n\r\n  <!-- Error State -->\r\n  <div *ngIf=\"error && !loading\" class=\"error-state\">\r\n    <mat-icon class=\"error-icon\">error</mat-icon>\r\n    <h4>Error Loading Students</h4>\r\n    <p>{{ error }}</p>\r\n    <button mat-raised-button color=\"primary\" (click)=\"refreshData()\">\r\n      <mat-icon>refresh</mat-icon>\r\n      Try Again\r\n    </button>\r\n  </div>\r\n\r\n  <!-- Main Content -->\r\n  <div *ngIf=\"!loading && !error\">\r\n    <!-- Filters -->\r\n    <mat-card class=\"filters-card\">\r\n      <mat-card-content>\r\n        <div class=\"filters-row\">\r\n          <div class=\"search-field\">\r\n            <mat-form-field appearance=\"outline\" class=\"compact-field\">\r\n              <mat-label>Search Students</mat-label>\r\n              <input matInput [(ngModel)]=\"searchQuery\" (input)=\"onSearch()\"\r\n                     placeholder=\"Name, roll number, email...\">\r\n              <mat-icon matSuffix>search</mat-icon>\r\n            </mat-form-field>\r\n          </div>\r\n\r\n          <div class=\"filter-field\">\r\n            <mat-form-field appearance=\"outline\" class=\"compact-field\">\r\n              <mat-label>Class</mat-label>\r\n              <mat-select [(ngModel)]=\"selectedClassId\" (selectionChange)=\"onClassChange()\">\r\n                <mat-option value=\"\">All Classes</mat-option>\r\n                <mat-option *ngFor=\"let cls of teacherClasses\" [value]=\"cls._id\">\r\n                  {{ cls.className }} - {{ cls.section }}\r\n                </mat-option>\r\n              </mat-select>\r\n            </mat-form-field>\r\n          </div>\r\n\r\n          <div class=\"filter-actions\">\r\n            <button mat-icon-button (click)=\"clearFilters()\" matTooltip=\"Clear Filters\" class=\"clear-btn\">\r\n              <mat-icon>clear</mat-icon>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n\r\n    <!-- Students Grid -->\r\n    <div class=\"students-grid\" *ngIf=\"paginatedStudents.length > 0\">\r\n      <mat-card *ngFor=\"let student of paginatedStudents\" class=\"student-card\">\r\n        <mat-card-header>\r\n          <div class=\"student-header\">\r\n            <div class=\"student-avatar\">\r\n              <mat-icon>person</mat-icon>\r\n            </div>\r\n            <div class=\"student-title\">\r\n              <h3>{{ student.name }}</h3>\r\n              <span class=\"student-email\">{{ student.email }}</span>\r\n            </div>\r\n          </div>\r\n        </mat-card-header>\r\n\r\n        <mat-card-content>\r\n          <div class=\"student-details\">\r\n            <div class=\"detail-row\">\r\n              <div class=\"detail-item\">\r\n                <mat-icon>badge</mat-icon>\r\n                <span>{{ student.rollNo || 'N/A' }}</span>\r\n              </div>\r\n              <div class=\"detail-item\">\r\n                <mat-icon>confirmation_number</mat-icon>\r\n                <span>{{ student.regNo || 'N/A' }}</span>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"detail-row\">\r\n              <div class=\"detail-item\">\r\n                <mat-icon>class</mat-icon>\r\n                <span>{{ getStudentDisplayInfo(student) }}</span>\r\n              </div>\r\n              <div class=\"detail-item\" *ngIf=\"student.contact\">\r\n                <mat-icon>phone</mat-icon>\r\n                <span>{{ student.contact }}</span>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"detail-row\" *ngIf=\"student.program || student.department\">\r\n              <div class=\"detail-item\" *ngIf=\"student.program\">\r\n                <mat-icon>category</mat-icon>\r\n                <span>{{ student.program.name }}</span>\r\n              </div>\r\n              <div class=\"detail-item\" *ngIf=\"student.department\">\r\n                <mat-icon>business</mat-icon>\r\n                <span>{{ student.department.name }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n\r\n        <mat-card-actions>\r\n          <button mat-button color=\"primary\" (click)=\"viewStudentDetails(student)\">\r\n            <mat-icon>visibility</mat-icon>\r\n            View Details\r\n          </button>\r\n          <button mat-icon-button matTooltip=\"Contact Student\" *ngIf=\"student.contact\">\r\n            <mat-icon>phone</mat-icon>\r\n          </button>\r\n        </mat-card-actions>\r\n      </mat-card>\r\n    </div>\r\n\r\n    <!-- Empty State -->\r\n    <div *ngIf=\"!loading && paginatedStudents.length === 0\" class=\"empty-state\">\r\n      <mat-icon class=\"empty-icon\">groups</mat-icon>\r\n      <h4>No Students Found</h4>\r\n      <p *ngIf=\"searchQuery || selectedClassId\">No students match your current filters.</p>\r\n      <p *ngIf=\"!searchQuery && !selectedClassId\">You don't have any students assigned yet.</p>\r\n      <button mat-button (click)=\"clearFilters()\" *ngIf=\"searchQuery || selectedClassId\">\r\n        Clear Filters\r\n      </button>\r\n    </div>\r\n\r\n    <!-- Pagination -->\r\n    <div class=\"pagination-container\" *ngIf=\"totalPages > 1\">\r\n      <mat-paginator\r\n        [length]=\"students.length\"\r\n        [pageSize]=\"itemsPerPage\"\r\n        [pageSizeOptions]=\"[10, 20, 50]\"\r\n        [pageIndex]=\"currentPage - 1\"\r\n        (page)=\"goToPage($event.pageIndex + 1)\"\r\n        showFirstLastButtons>\r\n      </mat-paginator>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;ICQQA,EAAA,CAAAC,cAAA,eAA6C;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA9BH,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,QAAA,CAAAC,MAAA,MAAuB;;;;;IAEtER,EAAA,CAAAC,cAAA,YAAgD;IAC9CD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAS,kBAAA,MAAAC,MAAA,CAAAC,cAAA,SAAAD,MAAA,CAAAE,YAAA,MACF;;;;;IACAZ,EAAA,CAAAC,cAAA,YAAiD;IAC/CD,EAAA,CAAAE,MAAA,4CACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAeRH,EAAA,CAAAC,cAAA,cAA2C;IACzCD,EAAA,CAAAa,SAAA,sBAAyC;IACzCb,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;IAI5BH,EAAA,CAAAC,cAAA,cAAmD;IACpBD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7CH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAClBH,EAAA,CAAAC,cAAA,iBAAkE;IAAxBD,EAAA,CAAAc,UAAA,mBAAAC,iEAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAASnB,EAAA,CAAAoB,WAAA,CAAAF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAC/DrB,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAE,MAAA,mBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAJNH,EAAA,CAAAI,SAAA,GAAW;IAAXJ,EAAA,CAAAsB,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IA2BFxB,EAAA,CAAAC,cAAA,qBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkCH,EAAA,CAAAyB,UAAA,UAAAC,OAAA,CAAAC,GAAA,CAAiB;IAC9D3B,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAS,kBAAA,MAAAiB,OAAA,CAAAE,SAAA,SAAAF,OAAA,CAAAG,OAAA,MACF;;;;;IA+CF7B,EAAA,CAAAC,cAAA,cAAiD;IACrCD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA5BH,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAsB,iBAAA,CAAAQ,WAAA,CAAAC,OAAA,CAAqB;;;;;IAK7B/B,EAAA,CAAAC,cAAA,cAAiD;IACrCD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAjCH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAsB,iBAAA,CAAAQ,WAAA,CAAAE,OAAA,CAAAC,IAAA,CAA0B;;;;;IAElCjC,EAAA,CAAAC,cAAA,cAAoD;IACxCD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAApCH,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAsB,iBAAA,CAAAQ,WAAA,CAAAI,UAAA,CAAAD,IAAA,CAA6B;;;;;IAPvCjC,EAAA,CAAAC,cAAA,cAAsE;IACpED,EAAA,CAAAmC,UAAA,IAAAC,uEAAA,kBAGM;IACNpC,EAAA,CAAAmC,UAAA,IAAAE,uEAAA,kBAGM;IACRrC,EAAA,CAAAG,YAAA,EAAM;;;;IARsBH,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAyB,UAAA,SAAAK,WAAA,CAAAE,OAAA,CAAqB;IAIrBhC,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAyB,UAAA,SAAAK,WAAA,CAAAI,UAAA,CAAwB;;;;;IAatDlC,EAAA,CAAAC,cAAA,iBAA6E;IACjED,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;;IAxDhCH,EAAA,CAAAC,cAAA,mBAAyE;IAIvDD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE7BH,EAAA,CAAAC,cAAA,cAA2B;IACrBD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAK5DH,EAAA,CAAAC,cAAA,wBAAkB;IAIAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE5CH,EAAA,CAAAC,cAAA,eAAyB;IACbD,EAAA,CAAAE,MAAA,2BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACxCH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAI7CH,EAAA,CAAAC,cAAA,eAAwB;IAEVD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEnDH,EAAA,CAAAmC,UAAA,KAAAG,iEAAA,kBAGM;IACRtC,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAmC,UAAA,KAAAI,iEAAA,kBASM;IACRvC,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,wBAAkB;IACmBD,EAAA,CAAAc,UAAA,mBAAA0B,oFAAA;MAAA,MAAAC,WAAA,GAAAzC,EAAA,CAAAgB,aAAA,CAAA0B,IAAA;MAAA,MAAAZ,WAAA,GAAAW,WAAA,CAAAE,SAAA;MAAA,MAAAC,OAAA,GAAA5C,EAAA,CAAAmB,aAAA;MAAA,OAASnB,EAAA,CAAAoB,WAAA,CAAAwB,OAAA,CAAAC,kBAAA,CAAAf,WAAA,CAA2B;IAAA,EAAC;IACtE9B,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAmC,UAAA,KAAAW,oEAAA,qBAES;IACX9C,EAAA,CAAAG,YAAA,EAAmB;;;;;IAnDTH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAsB,iBAAA,CAAAQ,WAAA,CAAAG,IAAA,CAAkB;IACMjC,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAsB,iBAAA,CAAAQ,WAAA,CAAAiB,KAAA,CAAmB;IAUvC/C,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAsB,iBAAA,CAAAQ,WAAA,CAAAkB,MAAA,UAA6B;IAI7BhD,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAsB,iBAAA,CAAAQ,WAAA,CAAAmB,KAAA,UAA4B;IAO5BjD,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAsB,iBAAA,CAAA4B,OAAA,CAAAC,qBAAA,CAAArB,WAAA,EAAoC;IAElB9B,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAyB,UAAA,SAAAK,WAAA,CAAAC,OAAA,CAAqB;IAMxB/B,EAAA,CAAAI,SAAA,GAA2C;IAA3CJ,EAAA,CAAAyB,UAAA,SAAAK,WAAA,CAAAE,OAAA,IAAAF,WAAA,CAAAI,UAAA,CAA2C;IAkBhBlC,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAyB,UAAA,SAAAK,WAAA,CAAAC,OAAA,CAAqB;;;;;IAxDjF/B,EAAA,CAAAC,cAAA,cAAgE;IAC9DD,EAAA,CAAAmC,UAAA,IAAAiB,0DAAA,wBA2DW;IACbpD,EAAA,CAAAG,YAAA,EAAM;;;;IA5D0BH,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAyB,UAAA,YAAA4B,MAAA,CAAAC,iBAAA,CAAoB;;;;;IAkElDtD,EAAA,CAAAC,cAAA,QAA0C;IAAAD,EAAA,CAAAE,MAAA,8CAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IACrFH,EAAA,CAAAC,cAAA,QAA4C;IAAAD,EAAA,CAAAE,MAAA,gDAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;IACzFH,EAAA,CAAAC,cAAA,iBAAmF;IAAhED,EAAA,CAAAc,UAAA,mBAAAyC,iFAAA;MAAAvD,EAAA,CAAAgB,aAAA,CAAAwC,IAAA;MAAA,MAAAC,OAAA,GAAAzD,EAAA,CAAAmB,aAAA;MAAA,OAASnB,EAAA,CAAAoB,WAAA,CAAAqC,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IACzC1D,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAPXH,EAAA,CAAAC,cAAA,cAA4E;IAC7CD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9CH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAmC,UAAA,IAAAwB,mDAAA,gBAAqF;IACrF3D,EAAA,CAAAmC,UAAA,IAAAyB,mDAAA,gBAAyF;IACzF5D,EAAA,CAAAmC,UAAA,IAAA0B,wDAAA,qBAES;IACX7D,EAAA,CAAAG,YAAA,EAAM;;;;IALAH,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAyB,UAAA,SAAAqC,OAAA,CAAAC,WAAA,IAAAD,OAAA,CAAAE,eAAA,CAAoC;IACpChE,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAyB,UAAA,UAAAqC,OAAA,CAAAC,WAAA,KAAAD,OAAA,CAAAE,eAAA,CAAsC;IACGhE,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAyB,UAAA,SAAAqC,OAAA,CAAAC,WAAA,IAAAD,OAAA,CAAAE,eAAA,CAAoC;;;;;;;;;IAMnFhE,EAAA,CAAAC,cAAA,cAAyD;IAMrDD,EAAA,CAAAc,UAAA,kBAAAmD,8EAAAC,MAAA;MAAAlE,EAAA,CAAAgB,aAAA,CAAAmD,IAAA;MAAA,MAAAC,OAAA,GAAApE,EAAA,CAAAmB,aAAA;MAAA,OAAQnB,EAAA,CAAAoB,WAAA,CAAAgD,OAAA,CAAAC,QAAA,CAAAH,MAAA,CAAAI,SAAA,GAA4B,CAAC,CAAC;IAAA,EAAC;IAEzCtE,EAAA,CAAAG,YAAA,EAAgB;;;;IANdH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAyB,UAAA,WAAA8C,OAAA,CAAAhE,QAAA,CAAAC,MAAA,CAA0B,aAAA+D,OAAA,CAAAC,YAAA,qBAAAxE,EAAA,CAAAyE,eAAA,IAAAC,GAAA,gBAAAH,OAAA,CAAAI,WAAA;;;;;;IAjHhC3E,EAAA,CAAAC,cAAA,UAAgC;IAOTD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACtCH,EAAA,CAAAC,cAAA,gBACiD;IADjCD,EAAA,CAAAc,UAAA,2BAAA8D,wEAAAV,MAAA;MAAAlE,EAAA,CAAAgB,aAAA,CAAA6D,IAAA;MAAA,MAAAC,OAAA,GAAA9E,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAA0D,OAAA,CAAAf,WAAA,GAAAG,MAAA;IAAA,EAAyB,mBAAAa,gEAAA;MAAA/E,EAAA,CAAAgB,aAAA,CAAA6D,IAAA;MAAA,MAAAG,OAAA,GAAAhF,EAAA,CAAAmB,aAAA;MAAA,OAAUnB,EAAA,CAAAoB,WAAA,CAAA4D,OAAA,CAAAC,QAAA,EAAU;IAAA,EAApB;IAAzCjF,EAAA,CAAAG,YAAA,EACiD;IACjDH,EAAA,CAAAC,cAAA,mBAAoB;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAIzCH,EAAA,CAAAC,cAAA,eAA0B;IAEXD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC5BH,EAAA,CAAAC,cAAA,sBAA8E;IAAlED,EAAA,CAAAc,UAAA,2BAAAoE,8EAAAhB,MAAA;MAAAlE,EAAA,CAAAgB,aAAA,CAAA6D,IAAA;MAAA,MAAAM,OAAA,GAAAnF,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAA+D,OAAA,CAAAnB,eAAA,GAAAE,MAAA;IAAA,EAA6B,6BAAAkB,gFAAA;MAAApF,EAAA,CAAAgB,aAAA,CAAA6D,IAAA;MAAA,MAAAQ,OAAA,GAAArF,EAAA,CAAAmB,aAAA;MAAA,OAAoBnB,EAAA,CAAAoB,WAAA,CAAAiE,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAnC;IACvCtF,EAAA,CAAAC,cAAA,sBAAqB;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAa;IAC7CH,EAAA,CAAAmC,UAAA,KAAAoD,sDAAA,yBAEa;IACfvF,EAAA,CAAAG,YAAA,EAAa;IAIjBH,EAAA,CAAAC,cAAA,eAA4B;IACFD,EAAA,CAAAc,UAAA,mBAAA0E,kEAAA;MAAAxF,EAAA,CAAAgB,aAAA,CAAA6D,IAAA;MAAA,MAAAY,OAAA,GAAAzF,EAAA,CAAAmB,aAAA;MAAA,OAASnB,EAAA,CAAAoB,WAAA,CAAAqE,OAAA,CAAA/B,YAAA,EAAc;IAAA,EAAC;IAC9C1D,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAQpCH,EAAA,CAAAmC,UAAA,KAAAuD,+CAAA,kBA6DM;IAGN1F,EAAA,CAAAmC,UAAA,KAAAwD,+CAAA,kBAQM;IAGN3F,EAAA,CAAAmC,UAAA,KAAAyD,+CAAA,kBASM;IACR5F,EAAA,CAAAG,YAAA,EAAM;;;;IAjHsBH,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAyB,UAAA,YAAAoE,MAAA,CAAA9B,WAAA,CAAyB;IAS7B/D,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAyB,UAAA,YAAAoE,MAAA,CAAA7B,eAAA,CAA6B;IAEXhE,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAyB,UAAA,YAAAoE,MAAA,CAAAC,cAAA,CAAiB;IAiB7B9F,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAyB,UAAA,SAAAoE,MAAA,CAAAvC,iBAAA,CAAA9C,MAAA,KAAkC;IAgExDR,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAyB,UAAA,UAAAoE,MAAA,CAAAE,OAAA,IAAAF,MAAA,CAAAvC,iBAAA,CAAA9C,MAAA,OAAgD;IAWnBR,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAyB,UAAA,SAAAoE,MAAA,CAAAG,UAAA,KAAoB;;;ADtH3D,OAAM,MAAOC,wBAAwB;EA2BnCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,WAAwB,EACxBC,gBAAkC,EAClCC,cAA8B,EAC9BC,QAAqB;IALrB,KAAAL,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,QAAQ,GAARA,QAAQ;IAhClB;IACA,KAAAT,OAAO,GAAG,IAAI;IACd,KAAAvE,KAAK,GAAkB,IAAI;IAI3B,KAAAjB,QAAQ,GAAc,EAAE;IACxB,KAAAkG,WAAW,GAAc,EAAE;IAC3B,KAAAX,cAAc,GAAU,EAAE;IAE1B;IACA,KAAA/B,WAAW,GAAG,EAAE;IAChB,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAA0C,kBAAkB,GAAG,EAAE;IACvB,KAAAC,eAAe,GAAG,EAAE;IAEpB;IACA,KAAAhC,WAAW,GAAG,CAAC;IACf,KAAAH,YAAY,GAAG,EAAE;IACjB,KAAAoC,UAAU,GAAG,CAAC;IAEd;IACA,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAlG,cAAc,GAAG,EAAE;IACnB,KAAAC,YAAY,GAAG,EAAE;EASd;EAEHkG,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,GAAG,IAAI,CAACV,WAAW,CAACW,uBAAuB,EAAE,EAAEC,IAAI;IAEnE;IACA,IAAI,CAACd,KAAK,CAACe,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;MACxC,IAAI,CAACP,YAAY,GAAGO,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE;MAC3C,IAAI,CAACzG,cAAc,GAAGyG,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE;MAC/C,IAAI,CAACxG,YAAY,GAAGwG,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE;MAE3C,IAAI,IAAI,CAACP,YAAY,EAAE;QACrB,IAAI,CAAC7C,eAAe,GAAG,IAAI,CAAC6C,YAAY;;IAE5C,CAAC,CAAC;IAEF,IAAI,IAAI,CAACE,WAAW,EAAE;MACpB,IAAI,CAACM,eAAe,EAAE;KACvB,MAAM;MACL,IAAI,CAAC7F,KAAK,GAAG,gBAAgB;MAC7B,IAAI,CAACuE,OAAO,GAAG,KAAK;;EAExB;EAEAsB,eAAeA,CAAA;IACb,IAAI,CAACtB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACvE,KAAK,GAAG,IAAI;IAEjB;IACA,IAAI,CAAC8E,gBAAgB,CAACgB,qBAAqB,CAAC,IAAI,CAACP,WAAW,CAACpF,GAAG,CAAC,CAACwF,SAAS,CAAC;MAC1EI,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACC,qBAAqB,CAACF,QAAQ,CAACG,SAAS,CAAC;UAC9C,IAAI,CAACC,YAAY,EAAE;SACpB,MAAM;UACL,IAAI,CAACpG,KAAK,GAAG,gCAAgC;UAC7C,IAAI,CAACuE,OAAO,GAAG,KAAK;;MAExB,CAAC;MACDvE,KAAK,EAAGA,KAAK,IAAI;QACfqG,OAAO,CAACrG,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAACA,KAAK,GAAG,4BAA4B;QACzC,IAAI,CAACuE,OAAO,GAAG,KAAK;QACpB,IAAI,CAACS,QAAQ,CAACsB,IAAI,CAAC,gCAAgC,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MACnF;KACD,CAAC;EACJ;EAEAL,qBAAqBA,CAACM,aAAoB;IACxC;IACA,MAAMC,QAAQ,GAAG,IAAIC,GAAG,EAAe;IAEvCF,aAAa,CAACG,OAAO,CAACC,KAAK,IAAG;MAC5B,MAAMC,OAAO,GAAGD,KAAK,CAACE,KAAK,CAAC3G,GAAG;MAE/B,IAAI,CAACsG,QAAQ,CAACM,GAAG,CAACF,OAAO,CAAC,EAAE;QAC1BJ,QAAQ,CAACO,GAAG,CAACH,OAAO,EAAE;UACpB1G,GAAG,EAAEyG,KAAK,CAACE,KAAK,CAAC3G,GAAG;UACpBC,SAAS,EAAEwG,KAAK,CAACE,KAAK,CAAC1G,SAAS;UAChCC,OAAO,EAAEuG,KAAK,CAACE,KAAK,CAACzG,OAAO;UAC5BK,UAAU,EAAEkG,KAAK,CAAClG,UAAU;UAC5BF,OAAO,EAAEoG,KAAK,CAACpG,OAAO;UACtByG,QAAQ,EAAEL,KAAK,CAACE,KAAK,CAACG;SACvB,CAAC;;IAEN,CAAC,CAAC;IAEF,IAAI,CAAC3C,cAAc,GAAG4C,KAAK,CAACC,IAAI,CAACV,QAAQ,CAACW,MAAM,EAAE,CAAC;EACrD;EAEAhB,YAAYA,CAAA;IACV;IACA,IAAI,CAACvB,WAAW,CAACwC,cAAc,CAAC,SAAS,CAAC,CAAC1B,SAAS,CAAC;MACnDI,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAAChB,WAAW,GAAGe,QAAQ,CAACsB,KAAK;UACjC,IAAI,CAACC,8BAA8B,EAAE;SACtC,MAAM;UACL,IAAI,CAACvH,KAAK,GAAG,yBAAyB;;QAExC,IAAI,CAACuE,OAAO,GAAG,KAAK;MACtB,CAAC;MACDvE,KAAK,EAAGA,KAAK,IAAI;QACfqG,OAAO,CAACrG,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACA,KAAK,GAAG,6BAA6B;QAC1C,IAAI,CAACuE,OAAO,GAAG,KAAK;QACpB,IAAI,CAACS,QAAQ,CAACsB,IAAI,CAAC,yBAAyB,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MAC5E;KACD,CAAC;EACJ;EAEAgB,8BAA8BA,CAAA;IAC5B;IACA,MAAMC,eAAe,GAAG,IAAI,CAAClD,cAAc,CAACmD,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACvH,GAAG,CAAC;IAE/D;IACA,IAAIwH,gBAAgB,GAAG,IAAI,CAAC1C,WAAW,CAAC2C,MAAM,CAACC,OAAO,IACpDA,OAAO,CAAChB,OAAO,IAAIW,eAAe,CAACM,QAAQ,CAACD,OAAO,CAAChB,OAAO,CAAC1G,GAAG,CAAC,CACjE;IAED;IACA,IAAI,IAAI,CAACqC,eAAe,EAAE;MACxBmF,gBAAgB,GAAGA,gBAAgB,CAACC,MAAM,CAACC,OAAO,IAChDA,OAAO,CAAChB,OAAO,IAAIgB,OAAO,CAAChB,OAAO,CAAC1G,GAAG,KAAK,IAAI,CAACqC,eAAe,CAChE;;IAGH,IAAI,IAAI,CAAC0C,kBAAkB,EAAE;MAC3ByC,gBAAgB,GAAGA,gBAAgB,CAACC,MAAM,CAACC,OAAO,IAChDA,OAAO,CAACnH,UAAU,IAAImH,OAAO,CAACnH,UAAU,CAACP,GAAG,KAAK,IAAI,CAAC+E,kBAAkB,CACzE;;IAGH,IAAI,IAAI,CAACC,eAAe,EAAE;MACxBwC,gBAAgB,GAAGA,gBAAgB,CAACC,MAAM,CAACC,OAAO,IAChDA,OAAO,CAACrH,OAAO,IAAIqH,OAAO,CAACrH,OAAO,CAACL,GAAG,KAAK,IAAI,CAACgF,eAAe,CAChE;;IAGH,IAAI,IAAI,CAAC5C,WAAW,EAAE;MACpB,MAAMwF,KAAK,GAAG,IAAI,CAACxF,WAAW,CAACyF,WAAW,EAAE;MAC5CL,gBAAgB,GAAGA,gBAAgB,CAACC,MAAM,CAACC,OAAO,IAChDA,OAAO,CAACpH,IAAI,CAACuH,WAAW,EAAE,CAACF,QAAQ,CAACC,KAAK,CAAC,IAC1CF,OAAO,CAACtG,KAAK,CAACyG,WAAW,EAAE,CAACF,QAAQ,CAACC,KAAK,CAAC,IAC1CF,OAAO,CAACrG,MAAM,IAAIqG,OAAO,CAACrG,MAAM,CAACwG,WAAW,EAAE,CAACF,QAAQ,CAACC,KAAK,CAAE,IAC/DF,OAAO,CAACpG,KAAK,IAAIoG,OAAO,CAACpG,KAAK,CAACuG,WAAW,EAAE,CAACF,QAAQ,CAACC,KAAK,CAAE,CAC/D;;IAGH,IAAI,CAAChJ,QAAQ,GAAG4I,gBAAgB;IAChC,IAAI,CAACvC,UAAU,GAAG,IAAI,CAACrG,QAAQ,CAACC,MAAM;EACxC;EAEA;EACAiJ,YAAYA,CAAA;IACV,IAAI,CAAC9E,WAAW,GAAG,CAAC;IACpB,IAAI,CAACoE,8BAA8B,EAAE;EACvC;EAEArF,YAAYA,CAAA;IACV,IAAI,CAACK,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,eAAe,GAAG,IAAI,CAAC6C,YAAY,CAAC,CAAC;IAC1C,IAAI,CAACH,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAAChC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACoE,8BAA8B,EAAE;EACvC;EAEAzD,aAAaA,CAAA;IACX,IAAI,CAACmE,YAAY,EAAE;EACrB;EAEAxE,QAAQA,CAAA;IACN,IAAI,CAACwE,YAAY,EAAE;EACrB;EAEA;EACA,IAAInG,iBAAiBA,CAAA;IACnB,MAAMoG,UAAU,GAAG,CAAC,IAAI,CAAC/E,WAAW,GAAG,CAAC,IAAI,IAAI,CAACH,YAAY;IAC7D,OAAO,IAAI,CAACjE,QAAQ,CAACoJ,KAAK,CAACD,UAAU,EAAEA,UAAU,GAAG,IAAI,CAAClF,YAAY,CAAC;EACxE;EAEA,IAAIwB,UAAUA,CAAA;IACZ,OAAO4D,IAAI,CAACC,IAAI,CAAC,IAAI,CAACtJ,QAAQ,CAACC,MAAM,GAAG,IAAI,CAACgE,YAAY,CAAC;EAC5D;EAEAsF,QAAQA,CAAA;IACN,IAAI,IAAI,CAACnF,WAAW,GAAG,IAAI,CAACqB,UAAU,EAAE;MACtC,IAAI,CAACrB,WAAW,EAAE;;EAEtB;EAEAoF,YAAYA,CAAA;IACV,IAAI,IAAI,CAACpF,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;;EAEtB;EAEAN,QAAQA,CAAC2F,IAAY;IACnB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAAChE,UAAU,EAAE;MACxC,IAAI,CAACrB,WAAW,GAAGqF,IAAI;;EAE3B;EAEA;EACAnH,kBAAkBA,CAACwG,OAAgB;IACjC,IAAI,CAACjD,MAAM,CAAC6D,QAAQ,CAAC,CAAC,mCAAmC,CAAC,EAAE;MAC1D/C,WAAW,EAAE;QACXgD,SAAS,EAAEb,OAAO,CAAC1H,GAAG;QACtBwI,WAAW,EAAEd,OAAO,CAACpH;;KAExB,CAAC;EACJ;EAEAmI,sBAAsBA,CAAA;IACpB,IAAI,IAAI,CAACpG,eAAe,EAAE;MACxB,IAAI,CAACoC,MAAM,CAAC6D,QAAQ,CAAC,CAAC,+BAA+B,CAAC,EAAE;QACtD/C,WAAW,EAAE;UACXmB,OAAO,EAAE,IAAI,CAACrE;;OAEjB,CAAC;KACH,MAAM;MACL,IAAI,CAACwC,QAAQ,CAACsB,IAAI,CAAC,6BAA6B,EAAE,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;;EAElF;EAEA;EACA1G,WAAWA,CAAA;IACT,IAAI,CAACgG,eAAe,EAAE;EACxB;EAEAgD,mBAAmBA,CAAChC,OAAe;IACjC,MAAMa,GAAG,GAAG,IAAI,CAACpD,cAAc,CAACwE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5I,GAAG,KAAK0G,OAAO,CAAC;IAC5D,OAAOa,GAAG,GAAG,GAAGA,GAAG,CAACtH,SAAS,MAAMsH,GAAG,CAACrH,OAAO,EAAE,GAAG,eAAe;EACpE;EAEAsB,qBAAqBA,CAACkG,OAAgB;IACpC,IAAIA,OAAO,CAAChB,OAAO,EAAE;MACnB,OAAO,GAAGgB,OAAO,CAAChB,OAAO,CAACzG,SAAS,MAAMyH,OAAO,CAAChB,OAAO,CAACxG,OAAO,EAAE;;IAEpE,OAAO,mBAAmB;EAC5B;EAAC,QAAA2I,CAAA,G;qBA/PUvE,wBAAwB,EAAAjG,EAAA,CAAAyK,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA3K,EAAA,CAAAyK,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA5K,EAAA,CAAAyK,iBAAA,CAAAI,EAAA,CAAAC,WAAA,GAAA9K,EAAA,CAAAyK,iBAAA,CAAAM,EAAA,CAAAC,gBAAA,GAAAhL,EAAA,CAAAyK,iBAAA,CAAAQ,EAAA,CAAAC,cAAA,GAAAlL,EAAA,CAAAyK,iBAAA,CAAAU,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAxBpF,wBAAwB;IAAAqF,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCvCrC5L,EAAA,CAAAC,cAAA,aAAwC;QAKHD,EAAA,CAAAE,MAAA,aAAM;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC9CH,EAAA,CAAAE,MAAA,oBACA;QAAAF,EAAA,CAAAmC,UAAA,IAAA2J,wCAAA,kBAA2E;QAC7E9L,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAmC,UAAA,IAAA4J,qCAAA,eAEI;QACJ/L,EAAA,CAAAmC,UAAA,IAAA6J,qCAAA,eAEI;QACNhM,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,cAA4B;QACFD,EAAA,CAAAc,UAAA,mBAAAmL,2DAAA;UAAA,OAASJ,GAAA,CAAAxK,WAAA,EAAa;QAAA,EAAC;QAC7CrB,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAE9BH,EAAA,CAAAC,cAAA,iBAC6D;QADnBD,EAAA,CAAAc,UAAA,mBAAAoL,2DAAA;UAAA,OAASL,GAAA,CAAAzB,sBAAA,EAAwB;QAAA,EAAC;QAE1EpK,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,kBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC/BH,EAAA,CAAAC,cAAA,gBAAiC;QAAAD,EAAA,CAAAE,MAAA,uBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAM7DH,EAAA,CAAAmC,UAAA,KAAAgK,wCAAA,kBAGM;QAGNnM,EAAA,CAAAmC,UAAA,KAAAiK,wCAAA,mBAQM;QAGNpM,EAAA,CAAAmC,UAAA,KAAAkK,wCAAA,mBAyHM;QACRrM,EAAA,CAAAG,YAAA,EAAM;;;QAjK+BH,EAAA,CAAAI,SAAA,GAAc;QAAdJ,EAAA,CAAAyB,UAAA,UAAAoK,GAAA,CAAA9F,OAAA,CAAc;QAEnB/F,EAAA,CAAAI,SAAA,GAAoB;QAApBJ,EAAA,CAAAyB,UAAA,SAAAoK,GAAA,CAAAlL,cAAA,CAAoB;QAGpBX,EAAA,CAAAI,SAAA,GAAqB;QAArBJ,EAAA,CAAAyB,UAAA,UAAAoK,GAAA,CAAAlL,cAAA,CAAqB;QASvCX,EAAA,CAAAI,SAAA,GAA6B;QAA7BJ,EAAA,CAAAyB,UAAA,cAAAoK,GAAA,CAAA7H,eAAA,CAA6B;QAQnChE,EAAA,CAAAI,SAAA,GAAa;QAAbJ,EAAA,CAAAyB,UAAA,SAAAoK,GAAA,CAAA9F,OAAA,CAAa;QAMb/F,EAAA,CAAAI,SAAA,GAAuB;QAAvBJ,EAAA,CAAAyB,UAAA,SAAAoK,GAAA,CAAArK,KAAA,KAAAqK,GAAA,CAAA9F,OAAA,CAAuB;QAWvB/F,EAAA,CAAAI,SAAA,GAAwB;QAAxBJ,EAAA,CAAAyB,UAAA,UAAAoK,GAAA,CAAA9F,OAAA,KAAA8F,GAAA,CAAArK,KAAA,CAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}