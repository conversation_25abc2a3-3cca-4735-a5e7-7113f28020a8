{"ast": null, "code": "import { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class BulkUploadService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = environment.apiUrl;\n  }\n  // Upload Excel file for bulk student registration\n  uploadStudentsBulk(file) {\n    const formData = new FormData();\n    formData.append('file', file);\n    return this.http.post(`${this.apiUrl}/bulk-upload-students`, formData, {\n      reportProgress: true,\n      observe: 'events'\n    });\n  }\n  // Download Excel template for bulk upload\n  downloadStudentTemplate() {\n    return this.http.get(`${this.apiUrl}/download-student-template`, {\n      responseType: 'blob'\n    });\n  }\n  // Get upload progress\n  uploadStudentsBulkWithProgress(file) {\n    const formData = new FormData();\n    formData.append('file', file);\n    return this.http.post(`${this.apiUrl}/bulk-upload-students`, formData, {\n      reportProgress: true,\n      observe: 'events'\n    });\n  }\n  static #_ = this.ɵfac = function BulkUploadService_Factory(t) {\n    return new (t || BulkUploadService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: BulkUploadService,\n    factory: BulkUploadService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["environment", "BulkUploadService", "constructor", "http", "apiUrl", "uploadStudentsBulk", "file", "formData", "FormData", "append", "post", "reportProgress", "observe", "downloadStudentTemplate", "get", "responseType", "uploadStudentsBulkWithProgress", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\services\\bulk-upload.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { environment } from '../../environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class BulkUploadService {\r\n  private apiUrl = environment.apiUrl;\r\n\r\n  constructor(private http: HttpClient) { }\r\n\r\n  // Upload Excel file for bulk student registration\r\n  uploadStudentsBulk(file: File): Observable<any> {\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n\r\n    return this.http.post(`${this.apiUrl}/bulk-upload-students`, formData, {\r\n      reportProgress: true,\r\n      observe: 'events'\r\n    });\r\n  }\r\n\r\n  // Download Excel template for bulk upload\r\n  downloadStudentTemplate(): Observable<Blob> {\r\n    return this.http.get(`${this.apiUrl}/download-student-template`, {\r\n      responseType: 'blob'\r\n    });\r\n  }\r\n\r\n  // Get upload progress\r\n  uploadStudentsBulkWithProgress(file: File): Observable<any> {\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n\r\n    return this.http.post(`${this.apiUrl}/bulk-upload-students`, formData, {\r\n      reportProgress: true,\r\n      observe: 'events'\r\n    });\r\n  }\r\n}\r\n"], "mappings": "AAGA,SAASA,WAAW,QAAQ,gCAAgC;;;AAK5D,OAAM,MAAOC,iBAAiB;EAG5BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAGJ,WAAW,CAACI,MAAM;EAEK;EAExC;EACAC,kBAAkBA,CAACC,IAAU;IAC3B,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;IAE7B,OAAO,IAAI,CAACH,IAAI,CAACO,IAAI,CAAC,GAAG,IAAI,CAACN,MAAM,uBAAuB,EAAEG,QAAQ,EAAE;MACrEI,cAAc,EAAE,IAAI;MACpBC,OAAO,EAAE;KACV,CAAC;EACJ;EAEA;EACAC,uBAAuBA,CAAA;IACrB,OAAO,IAAI,CAACV,IAAI,CAACW,GAAG,CAAC,GAAG,IAAI,CAACV,MAAM,4BAA4B,EAAE;MAC/DW,YAAY,EAAE;KACf,CAAC;EACJ;EAEA;EACAC,8BAA8BA,CAACV,IAAU;IACvC,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;IAE7B,OAAO,IAAI,CAACH,IAAI,CAACO,IAAI,CAAC,GAAG,IAAI,CAACN,MAAM,uBAAuB,EAAEG,QAAQ,EAAE;MACrEI,cAAc,EAAE,IAAI;MACpBC,OAAO,EAAE;KACV,CAAC;EACJ;EAAC,QAAAK,CAAA,G;qBAhCUhB,iBAAiB,EAAAiB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAjBrB,iBAAiB;IAAAsB,OAAA,EAAjBtB,iBAAiB,CAAAuB,IAAA;IAAAC,UAAA,EAFhB;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}