.student-classes-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.header-content h1 {
  margin: 0;
  color: #333;
  font-size: 2rem;
  font-weight: 500;
}

.header-content p {
  margin: 5px 0 0 0;
  color: #666;
  font-size: 1rem;
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-container p, .error-container p {
  margin-top: 20px;
  color: #666;
}

.error-container mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  color: #f44336;
  margin-bottom: 20px;
}

.classes-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.class-info-card {
  margin-bottom: 20px;
}

.class-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.detail-item mat-icon {
  color: #2196f3;
  font-size: 24px;
  width: 24px;
  height: 24px;
}

.detail-content h4 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 0.9rem;
  font-weight: 500;
}

.detail-content p {
  margin: 0;
  color: #666;
  font-size: 1rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon mat-icon {
  font-size: 32px;
  width: 32px;
  height: 32px;
  opacity: 0.8;
}

.stat-info h3 {
  margin: 0 0 5px 0;
  font-size: 1.8rem;
  font-weight: 600;
}

.stat-info p {
  margin: 0;
  opacity: 0.9;
  font-size: 0.9rem;
}

.subjects-card {
  margin-bottom: 20px;
}

.subjects-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 20px;
}

.subject-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #2196f3;
}

.subject-info h4 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 500;
}

.subject-info p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.subject-stats {
  display: flex;
  align-items: center;
  gap: 15px;
}

.attendance-percentage {
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
  min-width: 60px;
  text-align: center;
}

.attendance-percentage.good {
  background: #e8f5e8;
  color: #2e7d32;
}

.attendance-percentage.warning {
  background: #fff3e0;
  color: #f57c00;
}

.attendance-percentage.danger {
  background: #ffebee;
  color: #d32f2f;
}

.quick-actions {
  margin-top: 30px;
}

.quick-actions h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.3rem;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.action-buttons button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
}

.action-buttons button mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
}

@media (max-width: 768px) {
  .student-classes-container {
    padding: 15px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .class-details {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .subject-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons button {
    width: 100%;
    justify-content: center;
  }
}
