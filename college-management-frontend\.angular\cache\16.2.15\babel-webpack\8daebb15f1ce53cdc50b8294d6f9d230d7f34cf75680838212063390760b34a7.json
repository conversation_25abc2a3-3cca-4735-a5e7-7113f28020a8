{"ast": null, "code": "import { MatAutocompleteModule } from '@angular/material/autocomplete';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatButtonToggleModule } from '@angular/material/button-toggle';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatOptionModule, MatRippleModule } from '@angular/material/core';\nimport { MatGridListModule } from '@angular/material/grid-list';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatSliderModule } from '@angular/material/slider';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { DragDropModule } from '@angular/cdk/drag-drop';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatListModule } from '@angular/material/list';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { PortalModule } from '@angular/cdk/portal';\nimport { MatTreeModule } from '@angular/material/tree';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nexport class MaterialModule {\n  static #_ = this.ɵfac = function MaterialModule_Factory(t) {\n    return new (t || MaterialModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: MaterialModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [MatAutocompleteModule, MatButtonModule, MatButtonToggleModule, MatCardModule, MatCheckboxModule, MatChipsModule, MatDatepickerModule, MatDialogModule, MatExpansionModule, MatGridListModule, MatIconModule, MatInputModule, MatListModule, MatMenuModule, MatNativeDateModule, FormsModule, ReactiveFormsModule, MatPaginatorModule, MatProgressBarModule, MatProgressSpinnerModule, MatRadioModule, MatRippleModule, MatSelectModule, MatSidenavModule, MatSliderModule, MatSlideToggleModule, MatSnackBarModule, MatSortModule, MatTableModule, MatTabsModule, MatToolbarModule, MatTooltipModule, MatOptionModule, DragDropModule, MatDividerModule, PortalModule, MatTreeModule, MatBadgeModule, MatFormFieldModule, MatAutocompleteModule, MatButtonModule, MatButtonToggleModule, MatCardModule, MatCheckboxModule, MatChipsModule, MatDatepickerModule, MatDialogModule, MatExpansionModule, MatGridListModule, MatIconModule, MatInputModule, MatListModule, MatMenuModule, MatNativeDateModule, ReactiveFormsModule, FormsModule, MatPaginatorModule, MatProgressBarModule, MatProgressSpinnerModule, MatRadioModule, MatRippleModule, MatSelectModule, MatSidenavModule, MatSliderModule, MatSlideToggleModule, MatSnackBarModule, MatSortModule, MatTableModule, MatTabsModule, MatToolbarModule, MatTooltipModule, MatOptionModule, DragDropModule, MatDividerModule, PortalModule, MatTreeModule, MatBadgeModule, MatFormFieldModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(MaterialModule, {\n    imports: [MatAutocompleteModule, MatButtonModule, MatButtonToggleModule, MatCardModule, MatCheckboxModule, MatChipsModule, MatDatepickerModule, MatDialogModule, MatExpansionModule, MatGridListModule, MatIconModule, MatInputModule, MatListModule, MatMenuModule, MatNativeDateModule, FormsModule, ReactiveFormsModule, MatPaginatorModule, MatProgressBarModule, MatProgressSpinnerModule, MatRadioModule, MatRippleModule, MatSelectModule, MatSidenavModule, MatSliderModule, MatSlideToggleModule, MatSnackBarModule, MatSortModule, MatTableModule, MatTabsModule, MatToolbarModule, MatTooltipModule, MatOptionModule, DragDropModule, MatDividerModule, PortalModule, MatTreeModule, MatBadgeModule, MatFormFieldModule],\n    exports: [MatAutocompleteModule, MatButtonModule, MatButtonToggleModule, MatCardModule, MatCheckboxModule, MatChipsModule, MatDatepickerModule, MatDialogModule, MatExpansionModule, MatGridListModule, MatIconModule, MatInputModule, MatListModule, MatMenuModule, MatNativeDateModule, ReactiveFormsModule, FormsModule, MatPaginatorModule, MatProgressBarModule, MatProgressSpinnerModule, MatRadioModule, MatRippleModule, MatSelectModule, MatSidenavModule, MatSliderModule, MatSlideToggleModule, MatSnackBarModule, MatSortModule, MatTableModule, MatTabsModule, MatToolbarModule, MatTooltipModule, MatOptionModule, DragDropModule, MatDividerModule, PortalModule, MatTreeModule, MatBadgeModule, MatFormFieldModule]\n  });\n})();", "map": {"version": 3, "names": ["MatAutocompleteModule", "MatButtonModule", "MatButtonToggleModule", "MatCheckboxModule", "MatChipsModule", "MatOptionModule", "MatRippleModule", "MatGridListModule", "MatMenuModule", "MatPaginatorModule", "MatProgressBarModule", "MatProgressSpinnerModule", "MatSelectModule", "MatSidenavModule", "MatSlideToggleModule", "MatSliderModule", "MatSnackBarModule", "MatSortModule", "MatTableModule", "MatTooltipModule", "MatInputModule", "MatToolbarModule", "MatIconModule", "MatCardModule", "MatTabsModule", "DragDropModule", "MatExpansionModule", "MatRadioModule", "MatDatepickerModule", "MatDividerModule", "MatNativeDateModule", "MatListModule", "MatDialogModule", "PortalModule", "MatTreeModule", "MatBadgeModule", "MatFormFieldModule", "FormsModule", "ReactiveFormsModule", "MaterialModule", "_", "_2", "_3", "imports", "exports"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\material.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatButtonToggleModule } from '@angular/material/button-toggle';\r\nimport { MatCheckboxModule } from '@angular/material/checkbox';\r\nimport { MatChipsModule } from '@angular/material/chips';\r\nimport { MatOptionModule, MatRippleModule } from '@angular/material/core';\r\nimport { MatGridListModule } from '@angular/material/grid-list';\r\nimport { MatMenuModule } from '@angular/material/menu';\r\nimport { MatPaginatorModule } from '@angular/material/paginator';\r\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\r\nimport { MatSpinner, MatProgressSpinnerModule } from '@angular/material/progress-spinner';\r\nimport { MatSelectModule } from '@angular/material/select';\r\nimport { MatSidenavModule } from '@angular/material/sidenav';\r\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\r\nimport { MatSliderModule } from '@angular/material/slider';\r\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\r\nimport { MatSortModule } from '@angular/material/sort';\r\nimport { MatTableModule } from '@angular/material/table';\r\nimport { MatTooltipModule } from '@angular/material/tooltip';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { MatToolbarModule } from '@angular/material/toolbar';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatCardModule } from '@angular/material/card';\r\nimport { MatTabsModule } from '@angular/material/tabs';\r\nimport { DragDropModule } from '@angular/cdk/drag-drop';\r\nimport { MatExpansionModule } from '@angular/material/expansion';\r\nimport { MatRadioModule } from '@angular/material/radio';\r\nimport { MatDatepickerModule } from '@angular/material/datepicker';\r\nimport { MatDividerModule } from '@angular/material/divider';\r\nimport { MatNativeDateModule } from '@angular/material/core';\r\nimport { MatListModule } from '@angular/material/list';\r\nimport { MatDialogModule } from '@angular/material/dialog';\r\nimport { PortalModule } from '@angular/cdk/portal';\r\nimport { MatTreeModule } from '@angular/material/tree';\r\nimport { MatBadgeModule } from '@angular/material/badge';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\n\r\n\r\n@NgModule({\r\n  imports: [\r\n    MatAutocompleteModule, MatButtonModule, MatButtonToggleModule, MatCardModule, MatCheckboxModule, MatChipsModule, MatDatepickerModule,\r\n    MatDialogModule, MatExpansionModule, MatGridListModule, MatIconModule,\r\n    MatInputModule, MatListModule, MatMenuModule, MatNativeDateModule,FormsModule,ReactiveFormsModule,\r\n    MatPaginatorModule, MatProgressBarModule, MatProgressSpinnerModule, MatRadioModule, MatRippleModule, MatSelectModule, MatSidenavModule,\r\n    MatSliderModule, MatSlideToggleModule, MatSnackBarModule, MatSortModule, MatTableModule, MatTabsModule, MatToolbarModule,\r\n    MatTooltipModule, MatOptionModule, DragDropModule, MatDividerModule, PortalModule, MatTreeModule, MatBadgeModule,MatFormFieldModule\r\n  ],\r\n  exports: [\r\n    MatAutocompleteModule, MatButtonModule, MatButtonToggleModule, MatCardModule, MatCheckboxModule, MatChipsModule, MatDatepickerModule,\r\n    MatDialogModule, MatExpansionModule, MatGridListModule, MatIconModule,\r\n    MatInputModule, MatListModule, MatMenuModule, MatNativeDateModule,ReactiveFormsModule,FormsModule,\r\n    MatPaginatorModule, MatProgressBarModule, MatProgressSpinnerModule, MatRadioModule, MatRippleModule, MatSelectModule, MatSidenavModule,\r\n    MatSliderModule, MatSlideToggleModule, MatSnackBarModule, MatSortModule, MatTableModule, MatTabsModule, MatToolbarModule,\r\n    MatTooltipModule, MatOptionModule, DragDropModule, MatDividerModule, PortalModule, MatTreeModule, MatBadgeModule, MatFormFieldModule\r\n  ],\r\n})\r\nexport class MaterialModule {\r\n}\r\n"], "mappings": "AACA,SAASA,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,qBAAqB,QAAQ,iCAAiC;AACvE,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,EAAEC,eAAe,QAAQ,wBAAwB;AACzE,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAAqBC,wBAAwB,QAAQ,oCAAoC;AACzF,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,YAAY,QAAQ,qBAAqB;AAClD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;;AAqBjE,OAAM,MAAOC,cAAc;EAAA,QAAAC,CAAA,G;qBAAdD,cAAc;EAAA;EAAA,QAAAE,EAAA,G;UAAdF;EAAc;EAAA,QAAAG,EAAA,G;cAhBvB1C,qBAAqB,EAAEC,eAAe,EAAEC,qBAAqB,EAAEqB,aAAa,EAAEpB,iBAAiB,EAAEC,cAAc,EAAEwB,mBAAmB,EACpII,eAAe,EAAEN,kBAAkB,EAAEnB,iBAAiB,EAAEe,aAAa,EACrEF,cAAc,EAAEW,aAAa,EAAEvB,aAAa,EAAEsB,mBAAmB,EAACO,WAAW,EAACC,mBAAmB,EACjG7B,kBAAkB,EAAEC,oBAAoB,EAAEC,wBAAwB,EAAEgB,cAAc,EAAErB,eAAe,EAAEM,eAAe,EAAEC,gBAAgB,EACtIE,eAAe,EAAED,oBAAoB,EAAEE,iBAAiB,EAAEC,aAAa,EAAEC,cAAc,EAAEM,aAAa,EAAEH,gBAAgB,EACxHF,gBAAgB,EAAEd,eAAe,EAAEoB,cAAc,EAAEI,gBAAgB,EAAEI,YAAY,EAAEC,aAAa,EAAEC,cAAc,EAACC,kBAAkB,EAGnIpC,qBAAqB,EAAEC,eAAe,EAAEC,qBAAqB,EAAEqB,aAAa,EAAEpB,iBAAiB,EAAEC,cAAc,EAAEwB,mBAAmB,EACpII,eAAe,EAAEN,kBAAkB,EAAEnB,iBAAiB,EAAEe,aAAa,EACrEF,cAAc,EAAEW,aAAa,EAAEvB,aAAa,EAAEsB,mBAAmB,EAACQ,mBAAmB,EAACD,WAAW,EACjG5B,kBAAkB,EAAEC,oBAAoB,EAAEC,wBAAwB,EAAEgB,cAAc,EAAErB,eAAe,EAAEM,eAAe,EAAEC,gBAAgB,EACtIE,eAAe,EAAED,oBAAoB,EAAEE,iBAAiB,EAAEC,aAAa,EAAEC,cAAc,EAAEM,aAAa,EAAEH,gBAAgB,EACxHF,gBAAgB,EAAEd,eAAe,EAAEoB,cAAc,EAAEI,gBAAgB,EAAEI,YAAY,EAAEC,aAAa,EAAEC,cAAc,EAAEC,kBAAkB;EAAA;;;2EAG3HG,cAAc;IAAAI,OAAA,GAhBvB3C,qBAAqB,EAAEC,eAAe,EAAEC,qBAAqB,EAAEqB,aAAa,EAAEpB,iBAAiB,EAAEC,cAAc,EAAEwB,mBAAmB,EACpII,eAAe,EAAEN,kBAAkB,EAAEnB,iBAAiB,EAAEe,aAAa,EACrEF,cAAc,EAAEW,aAAa,EAAEvB,aAAa,EAAEsB,mBAAmB,EAACO,WAAW,EAACC,mBAAmB,EACjG7B,kBAAkB,EAAEC,oBAAoB,EAAEC,wBAAwB,EAAEgB,cAAc,EAAErB,eAAe,EAAEM,eAAe,EAAEC,gBAAgB,EACtIE,eAAe,EAAED,oBAAoB,EAAEE,iBAAiB,EAAEC,aAAa,EAAEC,cAAc,EAAEM,aAAa,EAAEH,gBAAgB,EACxHF,gBAAgB,EAAEd,eAAe,EAAEoB,cAAc,EAAEI,gBAAgB,EAAEI,YAAY,EAAEC,aAAa,EAAEC,cAAc,EAACC,kBAAkB;IAAAQ,OAAA,GAGnI5C,qBAAqB,EAAEC,eAAe,EAAEC,qBAAqB,EAAEqB,aAAa,EAAEpB,iBAAiB,EAAEC,cAAc,EAAEwB,mBAAmB,EACpII,eAAe,EAAEN,kBAAkB,EAAEnB,iBAAiB,EAAEe,aAAa,EACrEF,cAAc,EAAEW,aAAa,EAAEvB,aAAa,EAAEsB,mBAAmB,EAACQ,mBAAmB,EAACD,WAAW,EACjG5B,kBAAkB,EAAEC,oBAAoB,EAAEC,wBAAwB,EAAEgB,cAAc,EAAErB,eAAe,EAAEM,eAAe,EAAEC,gBAAgB,EACtIE,eAAe,EAAED,oBAAoB,EAAEE,iBAAiB,EAAEC,aAAa,EAAEC,cAAc,EAAEM,aAAa,EAAEH,gBAAgB,EACxHF,gBAAgB,EAAEd,eAAe,EAAEoB,cAAc,EAAEI,gBAAgB,EAAEI,YAAY,EAAEC,aAAa,EAAEC,cAAc,EAAEC,kBAAkB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}