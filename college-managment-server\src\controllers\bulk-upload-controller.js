const XLSX = require('xlsx');
const bcrypt = require('bcrypt');
const User = require('../models/userModel');
const Program = require('../models/program');
const Department = require('../models/department');
const Class = require('../models/classes');

// Bulk upload students from Excel file
const bulkUploadStudents = async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({
                success: false,
                message: 'No file uploaded. Please select an Excel file.'
            });
        }

        // Read the Excel file
        const workbook = XLSX.readFile(req.file.path);
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const data = XLSX.utils.sheet_to_json(worksheet);

        if (!data || data.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Excel file is empty or invalid format.'
            });
        }

        const results = {
            successful: [],
            failed: [],
            totalProcessed: data.length
        };

        // Process each row
        for (let i = 0; i < data.length; i++) {
            const row = data[i];
            const rowNumber = i + 2; // Excel row number (starting from 2, accounting for header)

            try {
                // Validate required fields
                const requiredFields = ['name', 'email', 'password', 'rollNo', 'regNo', 'father_name', 'father_contact', 'contact', 'address', 'date_of_birth', 'program', 'department', 'class', 'semester', 'academicYear'];
                const missingFields = requiredFields.filter(field => !row[field] || row[field].toString().trim() === '');

                if (missingFields.length > 0) {
                    results.failed.push({
                        row: rowNumber,
                        data: row,
                        error: `Missing required fields: ${missingFields.join(', ')}`
                    });
                    continue;
                }

                // Check if email already exists
                const existingUser = await User.findOne({ email: row.email.trim() });
                if (existingUser) {
                    results.failed.push({
                        row: rowNumber,
                        data: row,
                        error: `Email ${row.email} already exists`
                    });
                    continue;
                }

                // Check if rollNo already exists
                const existingRollNo = await User.findOne({ rollNo: row.rollNo.toString().trim() });
                if (existingRollNo) {
                    results.failed.push({
                        row: rowNumber,
                        data: row,
                        error: `Roll Number ${row.rollNo} already exists`
                    });
                    continue;
                }

                // Check if regNo already exists
                const existingRegNo = await User.findOne({ regNo: row.regNo.toString().trim() });
                if (existingRegNo) {
                    results.failed.push({
                        row: rowNumber,
                        data: row,
                        error: `Registration Number ${row.regNo} already exists`
                    });
                    continue;
                }

                // Find program by name
                const program = await Program.findOne({ name: row.program.trim(), isActive: true });
                if (!program) {
                    results.failed.push({
                        row: rowNumber,
                        data: row,
                        error: `Program '${row.program}' not found`
                    });
                    continue;
                }

                // Find department by name and program
                const department = await Department.findOne({ 
                    name: row.department.trim(), 
                    program: program._id, 
                    isActive: true 
                });
                if (!department) {
                    results.failed.push({
                        row: rowNumber,
                        data: row,
                        error: `Department '${row.department}' not found in program '${row.program}'`
                    });
                    continue;
                }

                // Find class by name, program, and department
                const classDoc = await Class.findOne({ 
                    className: row.class.trim(), 
                    program: program._id, 
                    department: department._id, 
                    isActive: true 
                });
                if (!classDoc) {
                    results.failed.push({
                        row: rowNumber,
                        data: row,
                        error: `Class '${row.class}' not found in department '${row.department}' of program '${row.program}'`
                    });
                    continue;
                }

                // Hash password
                const hashedPassword = await bcrypt.hash(row.password.toString().trim(), 10);

                // Parse date of birth
                let dateOfBirth;
                try {
                    dateOfBirth = new Date(row.date_of_birth);
                    if (isNaN(dateOfBirth.getTime())) {
                        throw new Error('Invalid date format');
                    }
                } catch (error) {
                    results.failed.push({
                        row: rowNumber,
                        data: row,
                        error: `Invalid date of birth format. Use YYYY-MM-DD or MM/DD/YYYY`
                    });
                    continue;
                }

                // Create student user
                const studentData = {
                    name: row.name.trim(),
                    email: row.email.trim().toLowerCase(),
                    password: hashedPassword,
                    rollNo: row.rollNo.toString().trim(),
                    regNo: row.regNo.toString().trim(),
                    father_name: row.father_name.trim(),
                    father_contact: row.father_contact.toString().trim(),
                    contact: row.contact.toString().trim(),
                    address: row.address.trim(),
                    date_of_birth: dateOfBirth,
                    school_last_attendance: row.school_last_attendance ? row.school_last_attendance.trim() : '',
                    year_of_passing: row.year_of_passing ? parseInt(row.year_of_passing) : null,
                    program: program._id,
                    department: department._id,
                    classId: classDoc._id,
                    semester: parseInt(row.semester),
                    academicYear: row.academicYear.toString().trim(),
                    role: 'Student',
                    isActive: true
                };

                const newStudent = await User.create(studentData);
                results.successful.push({
                    row: rowNumber,
                    studentId: newStudent._id,
                    name: newStudent.name,
                    email: newStudent.email,
                    rollNo: newStudent.rollNo
                });

            } catch (error) {
                results.failed.push({
                    row: rowNumber,
                    data: row,
                    error: error.message || 'Unknown error occurred'
                });
            }
        }

        // Clean up uploaded file
        const fs = require('fs');
        if (fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
        }

        res.status(200).json({
            success: true,
            message: `Bulk upload completed. ${results.successful.length} students created successfully, ${results.failed.length} failed.`,
            results
        });

    } catch (error) {
        console.error('Error in bulk upload:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error during bulk upload',
            error: error.message
        });
    }
};

// Generate Excel template for bulk upload
const generateExcelTemplate = async (req, res) => {
    try {
        // Get all programs, departments, and classes for reference
        const programs = await Program.find({ isActive: true }).select('name');
        const departments = await Department.find({ isActive: true }).populate('program', 'name').select('name program');
        const classes = await Class.find({ isActive: true }).populate(['program', 'department'], 'name').select('className program department');

        // Create sample data with instructions
        const sampleData = [
            {
                name: 'John Doe',
                email: '<EMAIL>',
                password: 'password123',
                rollNo: 'STU001',
                regNo: 'REG001',
                father_name: 'Robert Doe',
                father_contact: '+1234567890',
                contact: '+1234567891',
                address: '123 Main Street, City',
                date_of_birth: '2000-01-15',
                school_last_attendance: 'ABC High School',
                year_of_passing: 2018,
                program: 'BS',
                department: 'Computer Science',
                class: '1st Year',
                semester: 1,
                academicYear: '2024-2025'
            }
        ];

        // Create workbook
        const workbook = XLSX.utils.book_new();
        
        // Add sample data sheet
        const sampleSheet = XLSX.utils.json_to_sheet(sampleData);
        XLSX.utils.book_append_sheet(workbook, sampleSheet, 'Sample Data');

        // Add instructions sheet
        const instructions = [
            { Field: 'name', Description: 'Full name of the student', Required: 'Yes', Example: 'John Doe' },
            { Field: 'email', Description: 'Unique email address', Required: 'Yes', Example: '<EMAIL>' },
            { Field: 'password', Description: 'Initial password for student', Required: 'Yes', Example: 'password123' },
            { Field: 'rollNo', Description: 'Unique roll number', Required: 'Yes', Example: 'STU001' },
            { Field: 'regNo', Description: 'Unique registration number', Required: 'Yes', Example: 'REG001' },
            { Field: 'father_name', Description: 'Father\'s full name', Required: 'Yes', Example: 'Robert Doe' },
            { Field: 'father_contact', Description: 'Father\'s contact number', Required: 'Yes', Example: '+1234567890' },
            { Field: 'contact', Description: 'Student\'s contact number', Required: 'Yes', Example: '+1234567891' },
            { Field: 'address', Description: 'Complete address', Required: 'Yes', Example: '123 Main Street, City' },
            { Field: 'date_of_birth', Description: 'Date of birth (YYYY-MM-DD)', Required: 'Yes', Example: '2000-01-15' },
            { Field: 'school_last_attendance', Description: 'Last school attended', Required: 'No', Example: 'ABC High School' },
            { Field: 'year_of_passing', Description: 'Year of passing from last school', Required: 'No', Example: '2018' },
            { Field: 'program', Description: 'Program name (must exist)', Required: 'Yes', Example: 'BS' },
            { Field: 'department', Description: 'Department name (must exist)', Required: 'Yes', Example: 'Computer Science' },
            { Field: 'class', Description: 'Class name (must exist)', Required: 'Yes', Example: '1st Year' },
            { Field: 'semester', Description: 'Current semester number', Required: 'Yes', Example: '1' },
            { Field: 'academicYear', Description: 'Academic year', Required: 'Yes', Example: '2024-2025' }
        ];
        const instructionsSheet = XLSX.utils.json_to_sheet(instructions);
        XLSX.utils.book_append_sheet(workbook, instructionsSheet, 'Instructions');

        // Add reference data sheets
        const programsSheet = XLSX.utils.json_to_sheet(programs.map(p => ({ 'Program Name': p.name })));
        XLSX.utils.book_append_sheet(workbook, programsSheet, 'Available Programs');

        const departmentsSheet = XLSX.utils.json_to_sheet(departments.map(d => ({ 
            'Department Name': d.name, 
            'Program': d.program.name 
        })));
        XLSX.utils.book_append_sheet(workbook, departmentsSheet, 'Available Departments');

        const classesSheet = XLSX.utils.json_to_sheet(classes.map(c => ({ 
            'Class Name': c.className, 
            'Program': c.program.name, 
            'Department': c.department.name 
        })));
        XLSX.utils.book_append_sheet(workbook, classesSheet, 'Available Classes');

        // Generate buffer
        const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

        // Set response headers
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', 'attachment; filename=student_bulk_upload_template.xlsx');

        res.send(buffer);

    } catch (error) {
        console.error('Error generating Excel template:', error);
        res.status(500).json({
            success: false,
            message: 'Error generating Excel template',
            error: error.message
        });
    }
};

module.exports = {
    bulkUploadStudents,
    generateExcelTemplate
};
