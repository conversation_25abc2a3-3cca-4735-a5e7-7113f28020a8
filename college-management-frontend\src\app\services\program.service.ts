import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { Program } from '../models/user';

@Injectable({
  providedIn: 'root'
})
export class ProgramService {
  private apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) { }

  // Get all programs
  getAllPrograms(isActive?: boolean): Observable<any> {
    let params = new HttpParams();
    if (isActive !== undefined) {
      params = params.set('isActive', isActive.toString());
    }
    return this.http.get<any>(`${this.apiUrl}/programs`, { params });
  }

  // Get program by ID
  getProgramById(id: string): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/program/${id}`);
  }

  // Create new program
  createProgram(program: Partial<Program>): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/program`, program);
  }

  // Update program
  updateProgram(id: string, program: Partial<Program>): Observable<any> {
    return this.http.put<any>(`${this.apiUrl}/program/${id}`, program);
  }

  // Delete program (soft delete)
  deleteProgram(id: string): Observable<any> {
    return this.http.delete<any>(`${this.apiUrl}/program/${id}`);
  }

  // Permanently delete program
  permanentDeleteProgram(id: string): Observable<any> {
    return this.http.delete<any>(`${this.apiUrl}/program/${id}/permanent`);
  }
}
