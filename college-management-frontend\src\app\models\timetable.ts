import { User, Program, Department, Class, Subject } from './user';

export interface TimeSlot {
  startTime: string;
  endTime: string;
  duration: number;
}

export interface Timetable {
  _id: string;
  program: Program;
  department: Department;
  class: Class;
  subject: Subject;
  teacher: User;
  dayOfWeek: 'Monday' | 'Tuesday' | 'Wednesday' | 'Thursday' | 'Friday' | 'Saturday' | 'Sunday';
  timeSlot: TimeSlot;
  room?: string;
  semester: number;
  academicYear: string;
  isActive: boolean;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface TimetableCreateRequest {
  program: string;
  department: string;
  class: string;
  subject: string;
  teacher: string;
  dayOfWeek: string;
  timeSlot: {
    startTime: string;
    endTime: string;
  };
  room?: string;
  semester: number;
  academicYear: string;
  notes?: string;
}
