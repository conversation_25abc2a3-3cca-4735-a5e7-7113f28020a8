import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from 'src/app/services/auth.service';
import { UserService } from 'src/app/services/user.service';
import { User } from 'src/app/models/user';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-user-list',
  templateUrl: './user-list.component.html',
  styleUrls: ['./user-list.component.css']
})
export class UserListComponent implements OnInit {
  users: User[] = [];
  filteredUsers: User[] = [];
  searchQuery: string = '';
  selectedRole: string = '';
  currentPage: number = 1;
  itemsPerPage: number = 10;
  loading: boolean = false;

  roles = ['All', 'Student', 'Teacher', 'Principal', 'Admin'];

  constructor(
    private authService: AuthService,
    private userService: UserService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadUsers();
  }

  loadUsers(): void {
    this.loading = true;
    this.authService.getAllUsers().subscribe({
      next: (response) => {
        this.loading = false;
        if (response.success) {
          this.users = response.users || [];
          this.filteredUsers = [...this.users];
        }
      },
      error: (error) => {
        this.loading = false;
        console.error('Error loading users:', error);
        Swal.fire({
          title: 'Error',
          text: 'Failed to load users. Please try again.',
          icon: 'error',
          confirmButtonColor: '#29578c'
        });
      }
    });
  }

  filterUsers(): void {
    this.filteredUsers = this.users.filter(user => {
      const matchesSearch = !this.searchQuery || 
        user.name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
        user.email.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
        (user.regNo && user.regNo.toLowerCase().includes(this.searchQuery.toLowerCase())) ||
        (user.rollNo && user.rollNo.toLowerCase().includes(this.searchQuery.toLowerCase()));

      const matchesRole = !this.selectedRole || this.selectedRole === 'All' || 
        user.role === this.selectedRole;

      return matchesSearch && matchesRole;
    });
    this.currentPage = 1;
  }

  onSearchChange(): void {
    this.filterUsers();
  }

  onRoleChange(): void {
    this.filterUsers();
  }

  get paginatedUsers(): User[] {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    return this.filteredUsers.slice(startIndex, endIndex);
  }

  get totalPages(): number {
    return Math.ceil(this.filteredUsers.length / this.itemsPerPage);
  }

  nextPage(): void {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
    }
  }

  previousPage(): void {
    if (this.currentPage > 1) {
      this.currentPage--;
    }
  }

  editUser(user: User): void {
    this.router.navigate(['/dashboard/admin/users/edit-user', user._id]);
  }

  deleteUser(user: User): void {
    Swal.fire({
      title: 'Are you sure?',
      text: `Do you want to delete ${user.name}? This action cannot be undone.`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#29578c',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (result.isConfirmed) {
        this.performDelete(user);
      }
    });
  }

  private performDelete(user: User): void {
    if (!user._id) return;

    this.authService.deleteUser(user._id).subscribe({
      next: (response) => {
        if (response.success) {
          Swal.fire({
            title: 'Deleted!',
            text: `${user.name} has been deleted successfully.`,
            icon: 'success',
            confirmButtonColor: '#29578c'
          });
          this.loadUsers(); // Reload the users list
        }
      },
      error: (error) => {
        console.error('Error deleting user:', error);
        Swal.fire({
          title: 'Error',
          text: 'Failed to delete user. Please try again.',
          icon: 'error',
          confirmButtonColor: '#29578c'
        });
      }
    });
  }

  addNewUser(): void {
    this.router.navigate(['/dashboard/admin/users/add-user']);
  }
}
