{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let AppComponent = /*#__PURE__*/(() => {\n  class AppComponent {\n    constructor() {\n      this.title = 'college-managemnt-frontend';\n    }\n    static #_ = this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)();\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 1,\n      vars: 0,\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"router-outlet\");\n        }\n      },\n      dependencies: [i1.RouterOutlet]\n    });\n  }\n  return AppComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}