{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, TemplateRef, Directive, Inject, ViewChild, Input, Output, Component, ViewEncapsulation, ChangeDetectionStrategy, ContentChildren, forwardRef, Optional, Host, NgModule } from '@angular/core';\nimport { mixinDisableRipple, MAT_OPTION_PARENT_COMPONENT, MAT_OPTGROUP, MatOption, MatOptionSelectionChange, _countGroupLabelsBeforeOption, _getOptionScrollPosition, MatOptionModule, MatCommonModule } from '@angular/material/core';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i3 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayConfig, OverlayModule } from '@angular/cdk/overlay';\nimport { ActiveDescendantKeyManager, addAriaReferencedId, removeAriaReferencedId } from '@angular/cdk/a11y';\nimport { coerceBooleanProperty, coerceStringArray } from '@angular/cdk/coercion';\nimport * as i1 from '@angular/cdk/platform';\nimport { _getEventTarget } from '@angular/cdk/platform';\nimport { trigger, state, style, transition, group, animate } from '@angular/animations';\nimport { Subscription, Subject, defer, merge, of, fromEvent } from 'rxjs';\nimport { ESCAPE, hasModifierKey, UP_ARROW, ENTER, DOWN_ARROW, TAB } from '@angular/cdk/keycodes';\nimport { TemplatePortal } from '@angular/cdk/portal';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i4 from '@angular/material/form-field';\nimport { MAT_FORM_FIELD } from '@angular/material/form-field';\nimport { startWith, switchMap, take, filter, map, tap, delay } from 'rxjs/operators';\nimport * as i2$1 from '@angular/cdk/bidi';\n\n// Animation values come from\n// https://github.com/material-components/material-components-web/blob/master/packages/mdc-menu-surface/_mixins.scss\n// TODO(mmalerba): Ideally find a way to import the values from MDC's code.\nconst _c0 = [\"panel\"];\nfunction MatAutocomplete_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 0, 1);\n    i0.ɵɵlistener(\"@panelAnimation.done\", function MatAutocomplete_ng_template_0_Template_div_animation_panelAnimation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3._animationDone.next($event));\n    });\n    i0.ɵɵprojection(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formFieldId_r1 = ctx.id;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"id\", ctx_r0.id)(\"ngClass\", ctx_r0._classList)(\"@panelAnimation\", ctx_r0.isOpen ? \"visible\" : \"hidden\");\n    i0.ɵɵattribute(\"aria-label\", ctx_r0.ariaLabel || null)(\"aria-labelledby\", ctx_r0._getPanelAriaLabelledby(formFieldId_r1));\n  }\n}\nconst _c1 = [\"*\"];\nconst panelAnimation = /*#__PURE__*/trigger('panelAnimation', [/*#__PURE__*/state('void, hidden', /*#__PURE__*/style({\n  opacity: 0,\n  transform: 'scaleY(0.8)'\n})), /*#__PURE__*/transition(':enter, hidden => visible', [/*#__PURE__*/group([/*#__PURE__*/animate('0.03s linear', /*#__PURE__*/style({\n  opacity: 1\n})), /*#__PURE__*/animate('0.12s cubic-bezier(0, 0, 0.2, 1)', /*#__PURE__*/style({\n  transform: 'scaleY(1)'\n}))])]), /*#__PURE__*/transition(':leave, visible => hidden', [/*#__PURE__*/animate('0.075s linear', /*#__PURE__*/style({\n  opacity: 0\n}))])]);\n\n/**\n * Autocomplete IDs need to be unique across components, so this counter exists outside of\n * the component definition.\n */\nlet _uniqueAutocompleteIdCounter = 0;\n/** Event object that is emitted when an autocomplete option is selected. */\nclass MatAutocompleteSelectedEvent {\n  constructor( /** Reference to the autocomplete panel that emitted the event. */\n  source, /** Option that was selected. */\n  option) {\n    this.source = source;\n    this.option = option;\n  }\n}\n// Boilerplate for applying mixins to MatAutocomplete.\n/** @docs-private */\nconst _MatAutocompleteMixinBase = /*#__PURE__*/mixinDisableRipple(class {});\n/** Injection token to be used to override the default options for `mat-autocomplete`. */\nconst MAT_AUTOCOMPLETE_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('mat-autocomplete-default-options', {\n  providedIn: 'root',\n  factory: MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY\n});\n/** @docs-private */\nfunction MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    autoActiveFirstOption: false,\n    autoSelectActiveOption: false,\n    hideSingleSelectionIndicator: false,\n    requireSelection: false\n  };\n}\n/** Base class with all of the `MatAutocomplete` functionality. */\nlet _MatAutocompleteBase = /*#__PURE__*/(() => {\n  class _MatAutocompleteBase extends _MatAutocompleteMixinBase {\n    /** Whether the autocomplete panel is open. */\n    get isOpen() {\n      return this._isOpen && this.showPanel;\n    }\n    /** @docs-private Sets the theme color of the panel. */\n    _setColor(value) {\n      this._color = value;\n      this._setThemeClasses(this._classList);\n    }\n    /**\n     * Whether the first option should be highlighted when the autocomplete panel is opened.\n     * Can be configured globally through the `MAT_AUTOCOMPLETE_DEFAULT_OPTIONS` token.\n     */\n    get autoActiveFirstOption() {\n      return this._autoActiveFirstOption;\n    }\n    set autoActiveFirstOption(value) {\n      this._autoActiveFirstOption = coerceBooleanProperty(value);\n    }\n    /** Whether the active option should be selected as the user is navigating. */\n    get autoSelectActiveOption() {\n      return this._autoSelectActiveOption;\n    }\n    set autoSelectActiveOption(value) {\n      this._autoSelectActiveOption = coerceBooleanProperty(value);\n    }\n    /**\n     * Whether the user is required to make a selection when they're interacting with the\n     * autocomplete. If the user moves away from the autocomplete without selecting an option from\n     * the list, the value will be reset. If the user opens the panel and closes it without\n     * interacting or selecting a value, the initial value will be kept.\n     */\n    get requireSelection() {\n      return this._requireSelection;\n    }\n    set requireSelection(value) {\n      this._requireSelection = coerceBooleanProperty(value);\n    }\n    /**\n     * Takes classes set on the host mat-autocomplete element and applies them to the panel\n     * inside the overlay container to allow for easy styling.\n     */\n    set classList(value) {\n      if (value && value.length) {\n        this._classList = coerceStringArray(value).reduce((classList, className) => {\n          classList[className] = true;\n          return classList;\n        }, {});\n      } else {\n        this._classList = {};\n      }\n      this._setVisibilityClasses(this._classList);\n      this._setThemeClasses(this._classList);\n      this._elementRef.nativeElement.className = '';\n    }\n    constructor(_changeDetectorRef, _elementRef, _defaults, platform) {\n      super();\n      this._changeDetectorRef = _changeDetectorRef;\n      this._elementRef = _elementRef;\n      this._defaults = _defaults;\n      this._activeOptionChanges = Subscription.EMPTY;\n      /** Whether the autocomplete panel should be visible, depending on option length. */\n      this.showPanel = false;\n      this._isOpen = false;\n      /** Function that maps an option's control value to its display value in the trigger. */\n      this.displayWith = null;\n      /** Event that is emitted whenever an option from the list is selected. */\n      this.optionSelected = new EventEmitter();\n      /** Event that is emitted when the autocomplete panel is opened. */\n      this.opened = new EventEmitter();\n      /** Event that is emitted when the autocomplete panel is closed. */\n      this.closed = new EventEmitter();\n      /** Emits whenever an option is activated. */\n      this.optionActivated = new EventEmitter();\n      this._classList = {};\n      /** Unique ID to be used by autocomplete trigger's \"aria-owns\" property. */\n      this.id = `mat-autocomplete-${_uniqueAutocompleteIdCounter++}`;\n      // TODO(crisbeto): the problem that the `inertGroups` option resolves is only present on\n      // Safari using VoiceOver. We should occasionally check back to see whether the bug\n      // wasn't resolved in VoiceOver, and if it has, we can remove this and the `inertGroups`\n      // option altogether.\n      this.inertGroups = platform?.SAFARI || false;\n      this._autoActiveFirstOption = !!_defaults.autoActiveFirstOption;\n      this._autoSelectActiveOption = !!_defaults.autoSelectActiveOption;\n      this._requireSelection = !!_defaults.requireSelection;\n    }\n    ngAfterContentInit() {\n      this._keyManager = new ActiveDescendantKeyManager(this.options).withWrap().skipPredicate(this._skipPredicate);\n      this._activeOptionChanges = this._keyManager.change.subscribe(index => {\n        if (this.isOpen) {\n          this.optionActivated.emit({\n            source: this,\n            option: this.options.toArray()[index] || null\n          });\n        }\n      });\n      // Set the initial visibility state.\n      this._setVisibility();\n    }\n    ngOnDestroy() {\n      this._keyManager?.destroy();\n      this._activeOptionChanges.unsubscribe();\n    }\n    /**\n     * Sets the panel scrollTop. This allows us to manually scroll to display options\n     * above or below the fold, as they are not actually being focused when active.\n     */\n    _setScrollTop(scrollTop) {\n      if (this.panel) {\n        this.panel.nativeElement.scrollTop = scrollTop;\n      }\n    }\n    /** Returns the panel's scrollTop. */\n    _getScrollTop() {\n      return this.panel ? this.panel.nativeElement.scrollTop : 0;\n    }\n    /** Panel should hide itself when the option list is empty. */\n    _setVisibility() {\n      this.showPanel = !!this.options.length;\n      this._setVisibilityClasses(this._classList);\n      this._changeDetectorRef.markForCheck();\n    }\n    /** Emits the `select` event. */\n    _emitSelectEvent(option) {\n      const event = new MatAutocompleteSelectedEvent(this, option);\n      this.optionSelected.emit(event);\n    }\n    /** Gets the aria-labelledby for the autocomplete panel. */\n    _getPanelAriaLabelledby(labelId) {\n      if (this.ariaLabel) {\n        return null;\n      }\n      const labelExpression = labelId ? labelId + ' ' : '';\n      return this.ariaLabelledby ? labelExpression + this.ariaLabelledby : labelId;\n    }\n    /** Sets the autocomplete visibility classes on a classlist based on the panel is visible. */\n    _setVisibilityClasses(classList) {\n      classList[this._visibleClass] = this.showPanel;\n      classList[this._hiddenClass] = !this.showPanel;\n    }\n    /** Sets the theming classes on a classlist based on the theme of the panel. */\n    _setThemeClasses(classList) {\n      classList['mat-primary'] = this._color === 'primary';\n      classList['mat-warn'] = this._color === 'warn';\n      classList['mat-accent'] = this._color === 'accent';\n    }\n    _skipPredicate(option) {\n      return option.disabled;\n    }\n    static #_ = this.ɵfac = function _MatAutocompleteBase_Factory(t) {\n      return new (t || _MatAutocompleteBase)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MAT_AUTOCOMPLETE_DEFAULT_OPTIONS), i0.ɵɵdirectiveInject(i1.Platform));\n    };\n    static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: _MatAutocompleteBase,\n      viewQuery: function _MatAutocompleteBase_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(TemplateRef, 7);\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.template = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.panel = _t.first);\n        }\n      },\n      inputs: {\n        ariaLabel: [\"aria-label\", \"ariaLabel\"],\n        ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"],\n        displayWith: \"displayWith\",\n        autoActiveFirstOption: \"autoActiveFirstOption\",\n        autoSelectActiveOption: \"autoSelectActiveOption\",\n        requireSelection: \"requireSelection\",\n        panelWidth: \"panelWidth\",\n        classList: [\"class\", \"classList\"]\n      },\n      outputs: {\n        optionSelected: \"optionSelected\",\n        opened: \"opened\",\n        closed: \"closed\",\n        optionActivated: \"optionActivated\"\n      },\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return _MatAutocompleteBase;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatAutocomplete = /*#__PURE__*/(() => {\n  class MatAutocomplete extends _MatAutocompleteBase {\n    constructor() {\n      super(...arguments);\n      this._visibleClass = 'mat-mdc-autocomplete-visible';\n      this._hiddenClass = 'mat-mdc-autocomplete-hidden';\n      this._animationDone = new EventEmitter();\n      this._hideSingleSelectionIndicator = this._defaults.hideSingleSelectionIndicator ?? false;\n    }\n    /** Whether checkmark indicator for single-selection options is hidden. */\n    get hideSingleSelectionIndicator() {\n      return this._hideSingleSelectionIndicator;\n    }\n    set hideSingleSelectionIndicator(value) {\n      this._hideSingleSelectionIndicator = coerceBooleanProperty(value);\n      this._syncParentProperties();\n    }\n    /** Syncs the parent state with the individual options. */\n    _syncParentProperties() {\n      if (this.options) {\n        for (const option of this.options) {\n          option._changeDetectorRef.markForCheck();\n        }\n      }\n    }\n    ngOnDestroy() {\n      super.ngOnDestroy();\n      this._animationDone.complete();\n    }\n    // `skipPredicate` determines if key manager should avoid putting a given option in the tab\n    // order. Allow disabled list items to receive focus via keyboard to align with WAI ARIA\n    // recommendation.\n    //\n    // Normally WAI ARIA's instructions are to exclude disabled items from the tab order, but it\n    // makes a few exceptions for compound widgets.\n    //\n    // From [Developing a Keyboard Interface](\n    // https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n    //   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n    //   Listbox...\"\n    //\n    // The user can focus disabled options using the keyboard, but the user cannot click disabled\n    // options.\n    _skipPredicate(_option) {\n      return false;\n    }\n    static #_ = this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatAutocomplete_BaseFactory;\n      return function MatAutocomplete_Factory(t) {\n        return (ɵMatAutocomplete_BaseFactory || (ɵMatAutocomplete_BaseFactory = i0.ɵɵgetInheritedFactory(MatAutocomplete)))(t || MatAutocomplete);\n      };\n    }();\n    static #_2 = this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatAutocomplete,\n      selectors: [[\"mat-autocomplete\"]],\n      contentQueries: function MatAutocomplete_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MAT_OPTGROUP, 5);\n          i0.ɵɵcontentQuery(dirIndex, MatOption, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.optionGroups = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.options = _t);\n        }\n      },\n      hostAttrs: [\"ngSkipHydration\", \"\", 1, \"mat-mdc-autocomplete\"],\n      inputs: {\n        disableRipple: \"disableRipple\",\n        hideSingleSelectionIndicator: \"hideSingleSelectionIndicator\"\n      },\n      exportAs: [\"matAutocomplete\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_OPTION_PARENT_COMPONENT,\n        useExisting: MatAutocomplete\n      }]), i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c1,\n      decls: 1,\n      vars: 0,\n      consts: [[\"role\", \"listbox\", 1, \"mat-mdc-autocomplete-panel\", \"mdc-menu-surface\", \"mdc-menu-surface--open\", 3, \"id\", \"ngClass\"], [\"panel\", \"\"]],\n      template: function MatAutocomplete_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, MatAutocomplete_ng_template_0_Template, 3, 5, \"ng-template\");\n        }\n      },\n      dependencies: [i2.NgClass],\n      styles: [\"div.mat-mdc-autocomplete-panel{box-shadow:0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);width:100%;max-height:256px;visibility:hidden;transform-origin:center top;overflow:auto;padding:8px 0;border-radius:4px;box-sizing:border-box;position:static;background-color:var(--mat-autocomplete-background-color)}.cdk-high-contrast-active div.mat-mdc-autocomplete-panel{outline:solid 1px}.cdk-overlay-pane:not(.mat-mdc-autocomplete-panel-above) div.mat-mdc-autocomplete-panel{border-top-left-radius:0;border-top-right-radius:0}.mat-mdc-autocomplete-panel-above div.mat-mdc-autocomplete-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:center bottom}div.mat-mdc-autocomplete-panel.mat-mdc-autocomplete-visible{visibility:visible}div.mat-mdc-autocomplete-panel.mat-mdc-autocomplete-hidden{visibility:hidden}mat-autocomplete{display:none}\"],\n      encapsulation: 2,\n      data: {\n        animation: [panelAnimation]\n      },\n      changeDetection: 0\n    });\n  }\n  return MatAutocomplete;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Base class containing all of the functionality for `MatAutocompleteOrigin`. */\nlet _MatAutocompleteOriginBase = /*#__PURE__*/(() => {\n  class _MatAutocompleteOriginBase {\n    constructor( /** Reference to the element on which the directive is applied. */\n    elementRef) {\n      this.elementRef = elementRef;\n    }\n    static #_ = this.ɵfac = function _MatAutocompleteOriginBase_Factory(t) {\n      return new (t || _MatAutocompleteOriginBase)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n    static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: _MatAutocompleteOriginBase\n    });\n  }\n  return _MatAutocompleteOriginBase;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Directive applied to an element to make it usable\n * as a connection point for an autocomplete panel.\n */\nlet MatAutocompleteOrigin = /*#__PURE__*/(() => {\n  class MatAutocompleteOrigin extends _MatAutocompleteOriginBase {\n    static #_ = this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatAutocompleteOrigin_BaseFactory;\n      return function MatAutocompleteOrigin_Factory(t) {\n        return (ɵMatAutocompleteOrigin_BaseFactory || (ɵMatAutocompleteOrigin_BaseFactory = i0.ɵɵgetInheritedFactory(MatAutocompleteOrigin)))(t || MatAutocompleteOrigin);\n      };\n    }();\n    static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatAutocompleteOrigin,\n      selectors: [[\"\", \"matAutocompleteOrigin\", \"\"]],\n      exportAs: [\"matAutocompleteOrigin\"],\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return MatAutocompleteOrigin;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Provider that allows the autocomplete to register as a ControlValueAccessor.\n * @docs-private\n */\nconst MAT_AUTOCOMPLETE_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: /*#__PURE__*/forwardRef(() => MatAutocompleteTrigger),\n  multi: true\n};\n/**\n * Creates an error to be thrown when attempting to use an autocomplete trigger without a panel.\n * @docs-private\n */\nfunction getMatAutocompleteMissingPanelError() {\n  return Error('Attempting to open an undefined instance of `mat-autocomplete`. ' + 'Make sure that the id passed to the `matAutocomplete` is correct and that ' + \"you're attempting to open it after the ngAfterContentInit hook.\");\n}\n/** Injection token that determines the scroll handling while the autocomplete panel is open. */\nconst MAT_AUTOCOMPLETE_SCROLL_STRATEGY = /*#__PURE__*/new InjectionToken('mat-autocomplete-scroll-strategy');\n/** @docs-private */\nfunction MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition();\n}\n/** @docs-private */\nconst MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n  provide: MAT_AUTOCOMPLETE_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY\n};\n/** Base class with all of the `MatAutocompleteTrigger` functionality. */\nlet _MatAutocompleteTriggerBase = /*#__PURE__*/(() => {\n  class _MatAutocompleteTriggerBase {\n    /**\n     * Whether the autocomplete is disabled. When disabled, the element will\n     * act as a regular input and the user won't be able to open the panel.\n     */\n    get autocompleteDisabled() {\n      return this._autocompleteDisabled;\n    }\n    set autocompleteDisabled(value) {\n      this._autocompleteDisabled = coerceBooleanProperty(value);\n    }\n    constructor(_element, _overlay, _viewContainerRef, _zone, _changeDetectorRef, scrollStrategy, _dir, _formField, _document, _viewportRuler, _defaults) {\n      this._element = _element;\n      this._overlay = _overlay;\n      this._viewContainerRef = _viewContainerRef;\n      this._zone = _zone;\n      this._changeDetectorRef = _changeDetectorRef;\n      this._dir = _dir;\n      this._formField = _formField;\n      this._document = _document;\n      this._viewportRuler = _viewportRuler;\n      this._defaults = _defaults;\n      this._componentDestroyed = false;\n      this._autocompleteDisabled = false;\n      /** Whether or not the label state is being overridden. */\n      this._manuallyFloatingLabel = false;\n      /** Subscription to viewport size changes. */\n      this._viewportSubscription = Subscription.EMPTY;\n      /**\n       * Whether the autocomplete can open the next time it is focused. Used to prevent a focused,\n       * closed autocomplete from being reopened if the user switches to another browser tab and then\n       * comes back.\n       */\n      this._canOpenOnNextFocus = true;\n      /** Stream of keyboard events that can close the panel. */\n      this._closeKeyEventStream = new Subject();\n      /**\n       * Event handler for when the window is blurred. Needs to be an\n       * arrow function in order to preserve the context.\n       */\n      this._windowBlurHandler = () => {\n        // If the user blurred the window while the autocomplete is focused, it means that it'll be\n        // refocused when they come back. In this case we want to skip the first focus event, if the\n        // pane was closed, in order to avoid reopening it unintentionally.\n        this._canOpenOnNextFocus = this._document.activeElement !== this._element.nativeElement || this.panelOpen;\n      };\n      /** `View -> model callback called when value changes` */\n      this._onChange = () => {};\n      /** `View -> model callback called when autocomplete has been touched` */\n      this._onTouched = () => {};\n      /**\n       * Position of the autocomplete panel relative to the trigger element. A position of `auto`\n       * will render the panel underneath the trigger if there is enough space for it to fit in\n       * the viewport, otherwise the panel will be shown above it. If the position is set to\n       * `above` or `below`, the panel will always be shown above or below the trigger. no matter\n       * whether it fits completely in the viewport.\n       */\n      this.position = 'auto';\n      /**\n       * `autocomplete` attribute to be set on the input element.\n       * @docs-private\n       */\n      this.autocompleteAttribute = 'off';\n      this._overlayAttached = false;\n      /** Stream of changes to the selection state of the autocomplete options. */\n      this.optionSelections = defer(() => {\n        const options = this.autocomplete ? this.autocomplete.options : null;\n        if (options) {\n          return options.changes.pipe(startWith(options), switchMap(() => merge(...options.map(option => option.onSelectionChange))));\n        }\n        // If there are any subscribers before `ngAfterViewInit`, the `autocomplete` will be undefined.\n        // Return a stream that we'll replace with the real one once everything is in place.\n        return this._zone.onStable.pipe(take(1), switchMap(() => this.optionSelections));\n      });\n      /** Handles keyboard events coming from the overlay panel. */\n      this._handlePanelKeydown = event => {\n        // Close when pressing ESCAPE or ALT + UP_ARROW, based on the a11y guidelines.\n        // See: https://www.w3.org/TR/wai-aria-practices-1.1/#textbox-keyboard-interaction\n        if (event.keyCode === ESCAPE && !hasModifierKey(event) || event.keyCode === UP_ARROW && hasModifierKey(event, 'altKey')) {\n          // If the user had typed something in before we autoselected an option, and they decided\n          // to cancel the selection, restore the input value to the one they had typed in.\n          if (this._pendingAutoselectedOption) {\n            this._updateNativeInputValue(this._valueBeforeAutoSelection ?? '');\n            this._pendingAutoselectedOption = null;\n          }\n          this._closeKeyEventStream.next();\n          this._resetActiveItem();\n          // We need to stop propagation, otherwise the event will eventually\n          // reach the input itself and cause the overlay to be reopened.\n          event.stopPropagation();\n          event.preventDefault();\n        }\n      };\n      /**\n       * Track which modal we have modified the `aria-owns` attribute of. When the combobox trigger is\n       * inside an aria-modal, we apply aria-owns to the parent modal with the `id` of the options\n       * panel. Track the modal we have changed so we can undo the changes on destroy.\n       */\n      this._trackedModal = null;\n      this._scrollStrategy = scrollStrategy;\n    }\n    ngAfterViewInit() {\n      const window = this._getWindow();\n      if (typeof window !== 'undefined') {\n        this._zone.runOutsideAngular(() => window.addEventListener('blur', this._windowBlurHandler));\n      }\n    }\n    ngOnChanges(changes) {\n      if (changes['position'] && this._positionStrategy) {\n        this._setStrategyPositions(this._positionStrategy);\n        if (this.panelOpen) {\n          this._overlayRef.updatePosition();\n        }\n      }\n    }\n    ngOnDestroy() {\n      const window = this._getWindow();\n      if (typeof window !== 'undefined') {\n        window.removeEventListener('blur', this._windowBlurHandler);\n      }\n      this._viewportSubscription.unsubscribe();\n      this._componentDestroyed = true;\n      this._destroyPanel();\n      this._closeKeyEventStream.complete();\n      this._clearFromModal();\n    }\n    /** Whether or not the autocomplete panel is open. */\n    get panelOpen() {\n      return this._overlayAttached && this.autocomplete.showPanel;\n    }\n    /** Opens the autocomplete suggestion panel. */\n    openPanel() {\n      this._attachOverlay();\n      this._floatLabel();\n      // Add aria-owns attribute when the autocomplete becomes visible.\n      if (this._trackedModal) {\n        const panelId = this.autocomplete.id;\n        addAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n      }\n    }\n    /** Closes the autocomplete suggestion panel. */\n    closePanel() {\n      this._resetLabel();\n      if (!this._overlayAttached) {\n        return;\n      }\n      if (this.panelOpen) {\n        // Only emit if the panel was visible.\n        // The `NgZone.onStable` always emits outside of the Angular zone,\n        // so all the subscriptions from `_subscribeToClosingActions()` are also outside of the Angular zone.\n        // We should manually run in Angular zone to update UI after panel closing.\n        this._zone.run(() => {\n          this.autocomplete.closed.emit();\n        });\n      }\n      this.autocomplete._isOpen = this._overlayAttached = false;\n      this._pendingAutoselectedOption = null;\n      if (this._overlayRef && this._overlayRef.hasAttached()) {\n        this._overlayRef.detach();\n        this._closingActionsSubscription.unsubscribe();\n      }\n      this._updatePanelState();\n      // Note that in some cases this can end up being called after the component is destroyed.\n      // Add a check to ensure that we don't try to run change detection on a destroyed view.\n      if (!this._componentDestroyed) {\n        // We need to trigger change detection manually, because\n        // `fromEvent` doesn't seem to do it at the proper time.\n        // This ensures that the label is reset when the\n        // user clicks outside.\n        this._changeDetectorRef.detectChanges();\n      }\n      // Remove aria-owns attribute when the autocomplete is no longer visible.\n      if (this._trackedModal) {\n        const panelId = this.autocomplete.id;\n        removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n      }\n    }\n    /**\n     * Updates the position of the autocomplete suggestion panel to ensure that it fits all options\n     * within the viewport.\n     */\n    updatePosition() {\n      if (this._overlayAttached) {\n        this._overlayRef.updatePosition();\n      }\n    }\n    /**\n     * A stream of actions that should close the autocomplete panel, including\n     * when an option is selected, on blur, and when TAB is pressed.\n     */\n    get panelClosingActions() {\n      return merge(this.optionSelections, this.autocomplete._keyManager.tabOut.pipe(filter(() => this._overlayAttached)), this._closeKeyEventStream, this._getOutsideClickStream(), this._overlayRef ? this._overlayRef.detachments().pipe(filter(() => this._overlayAttached)) : of()).pipe(\n      // Normalize the output so we return a consistent type.\n      map(event => event instanceof MatOptionSelectionChange ? event : null));\n    }\n    /** The currently active option, coerced to MatOption type. */\n    get activeOption() {\n      if (this.autocomplete && this.autocomplete._keyManager) {\n        return this.autocomplete._keyManager.activeItem;\n      }\n      return null;\n    }\n    /** Stream of clicks outside of the autocomplete panel. */\n    _getOutsideClickStream() {\n      return merge(fromEvent(this._document, 'click'), fromEvent(this._document, 'auxclick'), fromEvent(this._document, 'touchend')).pipe(filter(event => {\n        // If we're in the Shadow DOM, the event target will be the shadow root, so we have to\n        // fall back to check the first element in the path of the click event.\n        const clickTarget = _getEventTarget(event);\n        const formField = this._formField ? this._formField._elementRef.nativeElement : null;\n        const customOrigin = this.connectedTo ? this.connectedTo.elementRef.nativeElement : null;\n        return this._overlayAttached && clickTarget !== this._element.nativeElement &&\n        // Normally focus moves inside `mousedown` so this condition will almost always be\n        // true. Its main purpose is to handle the case where the input is focused from an\n        // outside click which propagates up to the `body` listener within the same sequence\n        // and causes the panel to close immediately (see #3106).\n        this._document.activeElement !== this._element.nativeElement && (!formField || !formField.contains(clickTarget)) && (!customOrigin || !customOrigin.contains(clickTarget)) && !!this._overlayRef && !this._overlayRef.overlayElement.contains(clickTarget);\n      }));\n    }\n    // Implemented as part of ControlValueAccessor.\n    writeValue(value) {\n      Promise.resolve(null).then(() => this._assignOptionValue(value));\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnChange(fn) {\n      this._onChange = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnTouched(fn) {\n      this._onTouched = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    setDisabledState(isDisabled) {\n      this._element.nativeElement.disabled = isDisabled;\n    }\n    _handleKeydown(event) {\n      const keyCode = event.keyCode;\n      const hasModifier = hasModifierKey(event);\n      // Prevent the default action on all escape key presses. This is here primarily to bring IE\n      // in line with other browsers. By default, pressing escape on IE will cause it to revert\n      // the input value to the one that it had on focus, however it won't dispatch any events\n      // which means that the model value will be out of sync with the view.\n      if (keyCode === ESCAPE && !hasModifier) {\n        event.preventDefault();\n      }\n      if (this.activeOption && keyCode === ENTER && this.panelOpen && !hasModifier) {\n        this.activeOption._selectViaInteraction();\n        this._resetActiveItem();\n        event.preventDefault();\n      } else if (this.autocomplete) {\n        const prevActiveItem = this.autocomplete._keyManager.activeItem;\n        const isArrowKey = keyCode === UP_ARROW || keyCode === DOWN_ARROW;\n        if (keyCode === TAB || isArrowKey && !hasModifier && this.panelOpen) {\n          this.autocomplete._keyManager.onKeydown(event);\n        } else if (isArrowKey && this._canOpen()) {\n          this.openPanel();\n        }\n        if (isArrowKey || this.autocomplete._keyManager.activeItem !== prevActiveItem) {\n          this._scrollToOption(this.autocomplete._keyManager.activeItemIndex || 0);\n          if (this.autocomplete.autoSelectActiveOption && this.activeOption) {\n            if (!this._pendingAutoselectedOption) {\n              this._valueBeforeAutoSelection = this._element.nativeElement.value;\n            }\n            this._pendingAutoselectedOption = this.activeOption;\n            this._assignOptionValue(this.activeOption.value);\n          }\n        }\n      }\n    }\n    _handleInput(event) {\n      let target = event.target;\n      let value = target.value;\n      // Based on `NumberValueAccessor` from forms.\n      if (target.type === 'number') {\n        value = value == '' ? null : parseFloat(value);\n      }\n      // If the input has a placeholder, IE will fire the `input` event on page load,\n      // focus and blur, in addition to when the user actually changed the value. To\n      // filter out all of the extra events, we save the value on focus and between\n      // `input` events, and we check whether it changed.\n      // See: https://connect.microsoft.com/IE/feedback/details/885747/\n      if (this._previousValue !== value) {\n        this._previousValue = value;\n        this._pendingAutoselectedOption = null;\n        // If selection is required we don't write to the CVA while the user is typing.\n        // At the end of the selection either the user will have picked something\n        // or we'll reset the value back to null.\n        if (!this.autocomplete || !this.autocomplete.requireSelection) {\n          this._onChange(value);\n        }\n        if (!value) {\n          this._clearPreviousSelectedOption(null, false);\n        }\n        if (this._canOpen() && this._document.activeElement === event.target) {\n          this.openPanel();\n        }\n      }\n    }\n    _handleFocus() {\n      if (!this._canOpenOnNextFocus) {\n        this._canOpenOnNextFocus = true;\n      } else if (this._canOpen()) {\n        this._previousValue = this._element.nativeElement.value;\n        this._attachOverlay();\n        this._floatLabel(true);\n      }\n    }\n    _handleClick() {\n      if (this._canOpen() && !this.panelOpen) {\n        this.openPanel();\n      }\n    }\n    /**\n     * In \"auto\" mode, the label will animate down as soon as focus is lost.\n     * This causes the value to jump when selecting an option with the mouse.\n     * This method manually floats the label until the panel can be closed.\n     * @param shouldAnimate Whether the label should be animated when it is floated.\n     */\n    _floatLabel(shouldAnimate = false) {\n      if (this._formField && this._formField.floatLabel === 'auto') {\n        if (shouldAnimate) {\n          this._formField._animateAndLockLabel();\n        } else {\n          this._formField.floatLabel = 'always';\n        }\n        this._manuallyFloatingLabel = true;\n      }\n    }\n    /** If the label has been manually elevated, return it to its normal state. */\n    _resetLabel() {\n      if (this._manuallyFloatingLabel) {\n        if (this._formField) {\n          this._formField.floatLabel = 'auto';\n        }\n        this._manuallyFloatingLabel = false;\n      }\n    }\n    /**\n     * This method listens to a stream of panel closing actions and resets the\n     * stream every time the option list changes.\n     */\n    _subscribeToClosingActions() {\n      const firstStable = this._zone.onStable.pipe(take(1));\n      const optionChanges = this.autocomplete.options.changes.pipe(tap(() => this._positionStrategy.reapplyLastPosition()),\n      // Defer emitting to the stream until the next tick, because changing\n      // bindings in here will cause \"changed after checked\" errors.\n      delay(0));\n      // When the zone is stable initially, and when the option list changes...\n      return merge(firstStable, optionChanges).pipe(\n      // create a new stream of panelClosingActions, replacing any previous streams\n      // that were created, and flatten it so our stream only emits closing events...\n      switchMap(() => {\n        // The `NgZone.onStable` always emits outside of the Angular zone, thus we have to re-enter\n        // the Angular zone. This will lead to change detection being called outside of the Angular\n        // zone and the `autocomplete.opened` will also emit outside of the Angular.\n        this._zone.run(() => {\n          const wasOpen = this.panelOpen;\n          this._resetActiveItem();\n          this._updatePanelState();\n          this._changeDetectorRef.detectChanges();\n          if (this.panelOpen) {\n            this._overlayRef.updatePosition();\n          }\n          if (wasOpen !== this.panelOpen) {\n            // If the `panelOpen` state changed, we need to make sure to emit the `opened` or\n            // `closed` event, because we may not have emitted it. This can happen\n            // - if the users opens the panel and there are no options, but the\n            //   options come in slightly later or as a result of the value changing,\n            // - if the panel is closed after the user entered a string that did not match any\n            //   of the available options,\n            // - if a valid string is entered after an invalid one.\n            if (this.panelOpen) {\n              this._captureValueOnAttach();\n              this._emitOpened();\n            } else {\n              this.autocomplete.closed.emit();\n            }\n          }\n        });\n        return this.panelClosingActions;\n      }),\n      // when the first closing event occurs...\n      take(1))\n      // set the value, close the panel, and complete.\n      .subscribe(event => this._setValueAndClose(event));\n    }\n    /**\n     * Emits the opened event once it's known that the panel will be shown and stores\n     * the state of the trigger right before the opening sequence was finished.\n     */\n    _emitOpened() {\n      this.autocomplete.opened.emit();\n    }\n    /** Intended to be called when the panel is attached. Captures the current value of the input. */\n    _captureValueOnAttach() {\n      this._valueOnAttach = this._element.nativeElement.value;\n    }\n    /** Destroys the autocomplete suggestion panel. */\n    _destroyPanel() {\n      if (this._overlayRef) {\n        this.closePanel();\n        this._overlayRef.dispose();\n        this._overlayRef = null;\n      }\n    }\n    _assignOptionValue(value) {\n      const toDisplay = this.autocomplete && this.autocomplete.displayWith ? this.autocomplete.displayWith(value) : value;\n      // Simply falling back to an empty string if the display value is falsy does not work properly.\n      // The display value can also be the number zero and shouldn't fall back to an empty string.\n      this._updateNativeInputValue(toDisplay != null ? toDisplay : '');\n    }\n    _updateNativeInputValue(value) {\n      // If it's used within a `MatFormField`, we should set it through the property so it can go\n      // through change detection.\n      if (this._formField) {\n        this._formField._control.value = value;\n      } else {\n        this._element.nativeElement.value = value;\n      }\n      this._previousValue = value;\n    }\n    /**\n     * This method closes the panel, and if a value is specified, also sets the associated\n     * control to that value. It will also mark the control as dirty if this interaction\n     * stemmed from the user.\n     */\n    _setValueAndClose(event) {\n      const panel = this.autocomplete;\n      const toSelect = event ? event.source : this._pendingAutoselectedOption;\n      if (toSelect) {\n        this._clearPreviousSelectedOption(toSelect);\n        this._assignOptionValue(toSelect.value);\n        // TODO(crisbeto): this should wait until the animation is done, otherwise the value\n        // gets reset while the panel is still animating which looks glitchy. It'll likely break\n        // some tests to change it at this point.\n        this._onChange(toSelect.value);\n        panel._emitSelectEvent(toSelect);\n        this._element.nativeElement.focus();\n      } else if (panel.requireSelection && this._element.nativeElement.value !== this._valueOnAttach) {\n        this._clearPreviousSelectedOption(null);\n        this._assignOptionValue(null);\n        // Wait for the animation to finish before clearing the form control value, otherwise\n        // the options might change while the animation is running which looks glitchy.\n        if (panel._animationDone) {\n          panel._animationDone.pipe(take(1)).subscribe(() => this._onChange(null));\n        } else {\n          this._onChange(null);\n        }\n      }\n      this.closePanel();\n    }\n    /**\n     * Clear any previous selected option and emit a selection change event for this option\n     */\n    _clearPreviousSelectedOption(skip, emitEvent) {\n      // Null checks are necessary here, because the autocomplete\n      // or its options may not have been assigned yet.\n      this.autocomplete?.options?.forEach(option => {\n        if (option !== skip && option.selected) {\n          option.deselect(emitEvent);\n        }\n      });\n    }\n    _attachOverlay() {\n      if (!this.autocomplete && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getMatAutocompleteMissingPanelError();\n      }\n      let overlayRef = this._overlayRef;\n      if (!overlayRef) {\n        this._portal = new TemplatePortal(this.autocomplete.template, this._viewContainerRef, {\n          id: this._formField?.getLabelId()\n        });\n        overlayRef = this._overlay.create(this._getOverlayConfig());\n        this._overlayRef = overlayRef;\n        this._viewportSubscription = this._viewportRuler.change().subscribe(() => {\n          if (this.panelOpen && overlayRef) {\n            overlayRef.updateSize({\n              width: this._getPanelWidth()\n            });\n          }\n        });\n      } else {\n        // Update the trigger, panel width and direction, in case anything has changed.\n        this._positionStrategy.setOrigin(this._getConnectedElement());\n        overlayRef.updateSize({\n          width: this._getPanelWidth()\n        });\n      }\n      if (overlayRef && !overlayRef.hasAttached()) {\n        overlayRef.attach(this._portal);\n        this._closingActionsSubscription = this._subscribeToClosingActions();\n      }\n      const wasOpen = this.panelOpen;\n      this.autocomplete._isOpen = this._overlayAttached = true;\n      this.autocomplete._setColor(this._formField?.color);\n      this._updatePanelState();\n      this._applyModalPanelOwnership();\n      this._captureValueOnAttach();\n      // We need to do an extra `panelOpen` check in here, because the\n      // autocomplete won't be shown if there are no options.\n      if (this.panelOpen && wasOpen !== this.panelOpen) {\n        this._emitOpened();\n      }\n    }\n    /** Updates the panel's visibility state and any trigger state tied to id. */\n    _updatePanelState() {\n      this.autocomplete._setVisibility();\n      // Note that here we subscribe and unsubscribe based on the panel's visiblity state,\n      // because the act of subscribing will prevent events from reaching other overlays and\n      // we don't want to block the events if there are no options.\n      if (this.panelOpen) {\n        const overlayRef = this._overlayRef;\n        if (!this._keydownSubscription) {\n          // Use the `keydownEvents` in order to take advantage of\n          // the overlay event targeting provided by the CDK overlay.\n          this._keydownSubscription = overlayRef.keydownEvents().subscribe(this._handlePanelKeydown);\n        }\n        if (!this._outsideClickSubscription) {\n          // Subscribe to the pointer events stream so that it doesn't get picked up by other overlays.\n          // TODO(crisbeto): we should switch `_getOutsideClickStream` eventually to use this stream,\n          // but the behvior isn't exactly the same and it ends up breaking some internal tests.\n          this._outsideClickSubscription = overlayRef.outsidePointerEvents().subscribe();\n        }\n      } else {\n        this._keydownSubscription?.unsubscribe();\n        this._outsideClickSubscription?.unsubscribe();\n        this._keydownSubscription = this._outsideClickSubscription = null;\n      }\n    }\n    _getOverlayConfig() {\n      return new OverlayConfig({\n        positionStrategy: this._getOverlayPosition(),\n        scrollStrategy: this._scrollStrategy(),\n        width: this._getPanelWidth(),\n        direction: this._dir ?? undefined,\n        panelClass: this._defaults?.overlayPanelClass\n      });\n    }\n    _getOverlayPosition() {\n      const strategy = this._overlay.position().flexibleConnectedTo(this._getConnectedElement()).withFlexibleDimensions(false).withPush(false);\n      this._setStrategyPositions(strategy);\n      this._positionStrategy = strategy;\n      return strategy;\n    }\n    /** Sets the positions on a position strategy based on the directive's input state. */\n    _setStrategyPositions(positionStrategy) {\n      // Note that we provide horizontal fallback positions, even though by default the dropdown\n      // width matches the input, because consumers can override the width. See #18854.\n      const belowPositions = [{\n        originX: 'start',\n        originY: 'bottom',\n        overlayX: 'start',\n        overlayY: 'top'\n      }, {\n        originX: 'end',\n        originY: 'bottom',\n        overlayX: 'end',\n        overlayY: 'top'\n      }];\n      // The overlay edge connected to the trigger should have squared corners, while\n      // the opposite end has rounded corners. We apply a CSS class to swap the\n      // border-radius based on the overlay position.\n      const panelClass = this._aboveClass;\n      const abovePositions = [{\n        originX: 'start',\n        originY: 'top',\n        overlayX: 'start',\n        overlayY: 'bottom',\n        panelClass\n      }, {\n        originX: 'end',\n        originY: 'top',\n        overlayX: 'end',\n        overlayY: 'bottom',\n        panelClass\n      }];\n      let positions;\n      if (this.position === 'above') {\n        positions = abovePositions;\n      } else if (this.position === 'below') {\n        positions = belowPositions;\n      } else {\n        positions = [...belowPositions, ...abovePositions];\n      }\n      positionStrategy.withPositions(positions);\n    }\n    _getConnectedElement() {\n      if (this.connectedTo) {\n        return this.connectedTo.elementRef;\n      }\n      return this._formField ? this._formField.getConnectedOverlayOrigin() : this._element;\n    }\n    _getPanelWidth() {\n      return this.autocomplete.panelWidth || this._getHostWidth();\n    }\n    /** Returns the width of the input element, so the panel width can match it. */\n    _getHostWidth() {\n      return this._getConnectedElement().nativeElement.getBoundingClientRect().width;\n    }\n    /**\n     * Reset the active item to -1. This is so that pressing arrow keys will activate the correct\n     * option.\n     *\n     * If the consumer opted-in to automatically activatating the first option, activate the first\n     * *enabled* option.\n     */\n    _resetActiveItem() {\n      const autocomplete = this.autocomplete;\n      if (autocomplete.autoActiveFirstOption) {\n        // Find the index of the first *enabled* option. Avoid calling `_keyManager.setActiveItem`\n        // because it activates the first option that passes the skip predicate, rather than the\n        // first *enabled* option.\n        let firstEnabledOptionIndex = -1;\n        for (let index = 0; index < autocomplete.options.length; index++) {\n          const option = autocomplete.options.get(index);\n          if (!option.disabled) {\n            firstEnabledOptionIndex = index;\n            break;\n          }\n        }\n        autocomplete._keyManager.setActiveItem(firstEnabledOptionIndex);\n      } else {\n        autocomplete._keyManager.setActiveItem(-1);\n      }\n    }\n    /** Determines whether the panel can be opened. */\n    _canOpen() {\n      const element = this._element.nativeElement;\n      return !element.readOnly && !element.disabled && !this._autocompleteDisabled;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n      return this._document?.defaultView || window;\n    }\n    /** Scrolls to a particular option in the list. */\n    _scrollToOption(index) {\n      // Given that we are not actually focusing active options, we must manually adjust scroll\n      // to reveal options below the fold. First, we find the offset of the option from the top\n      // of the panel. If that offset is below the fold, the new scrollTop will be the offset -\n      // the panel height + the option height, so the active option will be just visible at the\n      // bottom of the panel. If that offset is above the top of the visible panel, the new scrollTop\n      // will become the offset. If that offset is visible within the panel already, the scrollTop is\n      // not adjusted.\n      const autocomplete = this.autocomplete;\n      const labelCount = _countGroupLabelsBeforeOption(index, autocomplete.options, autocomplete.optionGroups);\n      if (index === 0 && labelCount === 1) {\n        // If we've got one group label before the option and we're at the top option,\n        // scroll the list to the top. This is better UX than scrolling the list to the\n        // top of the option, because it allows the user to read the top group's label.\n        autocomplete._setScrollTop(0);\n      } else if (autocomplete.panel) {\n        const option = autocomplete.options.toArray()[index];\n        if (option) {\n          const element = option._getHostElement();\n          const newScrollPosition = _getOptionScrollPosition(element.offsetTop, element.offsetHeight, autocomplete._getScrollTop(), autocomplete.panel.nativeElement.offsetHeight);\n          autocomplete._setScrollTop(newScrollPosition);\n        }\n      }\n    }\n    /**\n     * If the autocomplete trigger is inside of an `aria-modal` element, connect\n     * that modal to the options panel with `aria-owns`.\n     *\n     * For some browser + screen reader combinations, when navigation is inside\n     * of an `aria-modal` element, the screen reader treats everything outside\n     * of that modal as hidden or invisible.\n     *\n     * This causes a problem when the combobox trigger is _inside_ of a modal, because the\n     * options panel is rendered _outside_ of that modal, preventing screen reader navigation\n     * from reaching the panel.\n     *\n     * We can work around this issue by applying `aria-owns` to the modal with the `id` of\n     * the options panel. This effectively communicates to assistive technology that the\n     * options panel is part of the same interaction as the modal.\n     *\n     * At time of this writing, this issue is present in VoiceOver.\n     * See https://github.com/angular/components/issues/20694\n     */\n    _applyModalPanelOwnership() {\n      // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n      // the `LiveAnnouncer` and any other usages.\n      //\n      // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n      // section of the DOM we need to look through. This should cover all the cases we support, but\n      // the selector can be expanded if it turns out to be too narrow.\n      const modal = this._element.nativeElement.closest('body > .cdk-overlay-container [aria-modal=\"true\"]');\n      if (!modal) {\n        // Most commonly, the autocomplete trigger is not inside a modal.\n        return;\n      }\n      const panelId = this.autocomplete.id;\n      if (this._trackedModal) {\n        removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n      }\n      addAriaReferencedId(modal, 'aria-owns', panelId);\n      this._trackedModal = modal;\n    }\n    /** Clears the references to the listbox overlay element from the modal it was added to. */\n    _clearFromModal() {\n      if (this._trackedModal) {\n        const panelId = this.autocomplete.id;\n        removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n        this._trackedModal = null;\n      }\n    }\n    static #_ = this.ɵfac = function _MatAutocompleteTriggerBase_Factory(t) {\n      return new (t || _MatAutocompleteTriggerBase)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1$1.Overlay), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_AUTOCOMPLETE_SCROLL_STRATEGY), i0.ɵɵdirectiveInject(i2$1.Directionality, 8), i0.ɵɵdirectiveInject(MAT_FORM_FIELD, 9), i0.ɵɵdirectiveInject(DOCUMENT, 8), i0.ɵɵdirectiveInject(i3.ViewportRuler), i0.ɵɵdirectiveInject(MAT_AUTOCOMPLETE_DEFAULT_OPTIONS, 8));\n    };\n    static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: _MatAutocompleteTriggerBase,\n      inputs: {\n        autocomplete: [\"matAutocomplete\", \"autocomplete\"],\n        position: [\"matAutocompletePosition\", \"position\"],\n        connectedTo: [\"matAutocompleteConnectedTo\", \"connectedTo\"],\n        autocompleteAttribute: [\"autocomplete\", \"autocompleteAttribute\"],\n        autocompleteDisabled: [\"matAutocompleteDisabled\", \"autocompleteDisabled\"]\n      },\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n  return _MatAutocompleteTriggerBase;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatAutocompleteTrigger = /*#__PURE__*/(() => {\n  class MatAutocompleteTrigger extends _MatAutocompleteTriggerBase {\n    constructor() {\n      super(...arguments);\n      this._aboveClass = 'mat-mdc-autocomplete-panel-above';\n    }\n    static #_ = this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatAutocompleteTrigger_BaseFactory;\n      return function MatAutocompleteTrigger_Factory(t) {\n        return (ɵMatAutocompleteTrigger_BaseFactory || (ɵMatAutocompleteTrigger_BaseFactory = i0.ɵɵgetInheritedFactory(MatAutocompleteTrigger)))(t || MatAutocompleteTrigger);\n      };\n    }();\n    static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatAutocompleteTrigger,\n      selectors: [[\"input\", \"matAutocomplete\", \"\"], [\"textarea\", \"matAutocomplete\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-autocomplete-trigger\"],\n      hostVars: 7,\n      hostBindings: function MatAutocompleteTrigger_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"focusin\", function MatAutocompleteTrigger_focusin_HostBindingHandler() {\n            return ctx._handleFocus();\n          })(\"blur\", function MatAutocompleteTrigger_blur_HostBindingHandler() {\n            return ctx._onTouched();\n          })(\"input\", function MatAutocompleteTrigger_input_HostBindingHandler($event) {\n            return ctx._handleInput($event);\n          })(\"keydown\", function MatAutocompleteTrigger_keydown_HostBindingHandler($event) {\n            return ctx._handleKeydown($event);\n          })(\"click\", function MatAutocompleteTrigger_click_HostBindingHandler() {\n            return ctx._handleClick();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"autocomplete\", ctx.autocompleteAttribute)(\"role\", ctx.autocompleteDisabled ? null : \"combobox\")(\"aria-autocomplete\", ctx.autocompleteDisabled ? null : \"list\")(\"aria-activedescendant\", ctx.panelOpen && ctx.activeOption ? ctx.activeOption.id : null)(\"aria-expanded\", ctx.autocompleteDisabled ? null : ctx.panelOpen.toString())(\"aria-controls\", ctx.autocompleteDisabled || !ctx.panelOpen ? null : ctx.autocomplete == null ? null : ctx.autocomplete.id)(\"aria-haspopup\", ctx.autocompleteDisabled ? null : \"listbox\");\n        }\n      },\n      exportAs: [\"matAutocompleteTrigger\"],\n      features: [i0.ɵɵProvidersFeature([MAT_AUTOCOMPLETE_VALUE_ACCESSOR]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return MatAutocompleteTrigger;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatAutocompleteModule = /*#__PURE__*/(() => {\n  class MatAutocompleteModule {\n    static #_ = this.ɵfac = function MatAutocompleteModule_Factory(t) {\n      return new (t || MatAutocompleteModule)();\n    };\n    static #_2 = this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatAutocompleteModule\n    });\n    static #_3 = this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER],\n      imports: [OverlayModule, MatOptionModule, MatCommonModule, CommonModule, CdkScrollableModule, MatOptionModule, MatCommonModule]\n    });\n  }\n  return MatAutocompleteModule;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_AUTOCOMPLETE_DEFAULT_OPTIONS, MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER, MAT_AUTOCOMPLETE_VALUE_ACCESSOR, MatAutocomplete, MatAutocompleteModule, MatAutocompleteOrigin, MatAutocompleteSelectedEvent, MatAutocompleteTrigger, _MatAutocompleteBase, _MatAutocompleteOriginBase, _MatAutocompleteTriggerBase, getMatAutocompleteMissingPanelError };\n//# sourceMappingURL=autocomplete.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}