{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/user.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/checkbox\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/forms\";\nfunction TeachertableComponent_tr_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 19)(2, \"mat-checkbox\", 20);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\", 21);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\", 22);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 23)(9, \"div\", 24)(10, \"button\", 25)(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"delete\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 26)(14, \"mat-icon\");\n    i0.ɵɵtext(15, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"button\", 27)(17, \"mat-icon\");\n    i0.ɵɵtext(18, \"remove_red_eye\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const teacher_r1 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", teacher_r1.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", teacher_r1.subject, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", teacher_r1.class, \" \");\n  }\n}\n// import { TeacherService } from 'src/app/services/teacher.service'; // Import your service\nexport let TeachertableComponent = /*#__PURE__*/(() => {\n  class TeachertableComponent {\n    constructor(teacherService) {\n      this.teacherService = teacherService;\n      this.isChecked = false;\n      this.isIndeterminate = true;\n      this.searchQuery = '';\n      this.teachers = []; // Define the teacher type if available\n    }\n\n    ngOnInit() {\n      this.fetchTeachers();\n    }\n    fetchTeachers() {\n      this.teacherService.getUsersByRole('Teacher').subscribe({\n        next: response => {\n          // Assuming response contains an array of teachers\n          this.teachers = response.users; // Adjust according to your API response\n        },\n\n        error: error => {\n          console.error('Error fetching teachers:', error);\n        }\n      });\n    }\n    toggleCheckbox() {\n      if (this.isIndeterminate) {\n        this.isIndeterminate = false;\n        this.isChecked = true;\n      } else if (this.isChecked) {\n        this.isChecked = false;\n      } else {\n        this.isIndeterminate = true;\n      }\n    }\n    get filteredTeachers() {\n      const searchLower = this.searchQuery.toLowerCase();\n      return this.teachers.filter(teacher => {\n        return teacher.name.toLowerCase().includes(searchLower) || teacher.subject.toLowerCase().includes(searchLower) || teacher.class.toLowerCase().includes(searchLower);\n      });\n    }\n    static #_ = this.ɵfac = function TeachertableComponent_Factory(t) {\n      return new (t || TeachertableComponent)(i0.ɵɵdirectiveInject(i1.UserService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TeachertableComponent,\n      selectors: [[\"app-teachertable\"]],\n      decls: 41,\n      vars: 4,\n      consts: [[1, \"maindiv\"], [1, \"secondarydiv\"], [1, \"my-3\", \"d-flex\", \"justify-content-between\", \"searchAndtab\"], [1, \"d-flex\", \"flex-wrap\", \"gap-3\"], [\"routerLink\", \"/dashboard/admin/teachers/add-teacher\", 1, \"btn\", \"btn-top\", \"border\", \"d-flex\", \"align-items-center\"], [1, \"search-container\"], [1, \"search-input-wrapper\"], [\"type\", \"text\", \"placeholder\", \"Search\", 1, \"search-input\", 3, \"ngModel\", \"ngModelChange\"], [1, \"search-icon\"], [1, \"table-container\"], [1, \"table-responsive\"], [1, \"tablehead\"], [1, \"one\"], [\"color\", \"primary\", 3, \"ngModel\", \"indeterminate\", \"ngModelChange\", \"change\"], [1, \"two\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"p-3\", \"align-items-center\", \"justify-content-between\"], [1, \"btn\", \"btn-top\", \"border\"], [1, \"page\"], [\"data-label\", \"Teacher Name\", 1, \"name\"], [\"color\", \"primary\"], [\"data-label\", \"Subject\", 1, \"para\"], [\"data-label\", \"Class\", 1, \"para\"], [\"data-label\", \"Action\", 1, \"para\"], [1, \"d-flex\", \"gap-2\"], [1, \"btn\", \"btndelete\", \"d-flex\", \"justify-content-center\", \"align-items-center\"], [1, \"btn\", \"btnedit\", \"d-flex\", \"justify-content-center\", \"align-items-center\"], [\"routerLink\", \"/dashboard/teachers/view-teacher\", 1, \"btn\", \"btnedit\", \"d-flex\", \"justify-content-center\", \"align-items-center\"]],\n      template: function TeachertableComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"button\", 4)(5, \"mat-icon\");\n          i0.ɵɵtext(6, \"add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(7, \"\\u00A0 Add New Teacher \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\")(9, \"div\", 5)(10, \"div\", 6)(11, \"input\", 7);\n          i0.ɵɵlistener(\"ngModelChange\", function TeachertableComponent_Template_input_ngModelChange_11_listener($event) {\n            return ctx.searchQuery = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"mat-icon\", 8);\n          i0.ɵɵtext(13, \"search\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(14, \"div\", 9)(15, \"div\", 10)(16, \"table\")(17, \"thead\")(18, \"tr\", 11)(19, \"th\", 12)(20, \"mat-checkbox\", 13);\n          i0.ɵɵlistener(\"ngModelChange\", function TeachertableComponent_Template_mat_checkbox_ngModelChange_20_listener($event) {\n            return ctx.isChecked = $event;\n          })(\"change\", function TeachertableComponent_Template_mat_checkbox_change_20_listener() {\n            return ctx.toggleCheckbox();\n          });\n          i0.ɵɵtext(21, \" Teacher Name \");\n          i0.ɵɵelementStart(22, \"mat-icon\");\n          i0.ɵɵtext(23, \"arrow_downward\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(24, \"th\");\n          i0.ɵɵtext(25, \"Subject\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"th\");\n          i0.ɵɵtext(27, \"Class\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"th\", 14);\n          i0.ɵɵtext(29, \"Actions\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(30, \"tbody\");\n          i0.ɵɵtemplate(31, TeachertableComponent_tr_31_Template, 19, 3, \"tr\", 15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(32, \"div\", 16)(33, \"div\")(34, \"button\", 17);\n          i0.ɵɵtext(35, \"Previous\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"div\", 18);\n          i0.ɵɵtext(37, \"Page 1 of 10\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"div\")(39, \"button\", 17);\n          i0.ɵɵtext(40, \"Next\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngModel\", ctx.isChecked)(\"indeterminate\", ctx.isIndeterminate);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredTeachers);\n        }\n      },\n      dependencies: [i2.NgForOf, i3.RouterLink, i4.MatCheckbox, i5.MatIcon, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgModel]\n    });\n  }\n  return TeachertableComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}