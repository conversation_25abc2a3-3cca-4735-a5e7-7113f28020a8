{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction VerifyOtpComponent_p_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"We sent a 6-digit verification code to \");\n    i0.ɵɵelementStart(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.user.email);\n  }\n}\nfunction VerifyOtpComponent_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 36);\n  }\n}\nfunction VerifyOtpComponent_span_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 37);\n  }\n}\nfunction VerifyOtpComponent_span_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"Resend in \", ctx_r3.countdown, \"s\");\n  }\n}\nfunction VerifyOtpComponent_span_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Resend OTP\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction VerifyOtpComponent_span_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sending...\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class VerifyOtpComponent {\n  constructor(fb, authService, router) {\n    this.fb = fb;\n    this.authService = authService;\n    this.router = router;\n    this.loading = false;\n    this.resendLoading = false;\n    this.user = null;\n    this.otpId = null;\n    this.countdown = 0;\n  }\n  ngOnInit() {\n    // Get user data from localStorage (set during forgot password)\n    const resetUser = localStorage.getItem('resetUser');\n    if (resetUser) {\n      this.user = JSON.parse(resetUser);\n    } else {\n      // If no user data, redirect to forgot password\n      Swal.fire({\n        title: 'Session Expired',\n        text: 'Please start the password reset process again.',\n        icon: 'warning',\n        confirmButtonColor: '#29578c'\n      }).then(() => {\n        this.router.navigate(['/auth/forgot-password']);\n      });\n      return;\n    }\n    this.initializeForm();\n    this.startCountdown();\n  }\n  ngOnDestroy() {\n    if (this.countdownInterval) {\n      clearInterval(this.countdownInterval);\n    }\n  }\n  initializeForm() {\n    this.otpForm = this.fb.group({\n      digit1: ['', [Validators.required, Validators.pattern(/^\\d$/)]],\n      digit2: ['', [Validators.required, Validators.pattern(/^\\d$/)]],\n      digit3: ['', [Validators.required, Validators.pattern(/^\\d$/)]],\n      digit4: ['', [Validators.required, Validators.pattern(/^\\d$/)]],\n      digit5: ['', [Validators.required, Validators.pattern(/^\\d$/)]],\n      digit6: ['', [Validators.required, Validators.pattern(/^\\d$/)]]\n    });\n  }\n  startCountdown() {\n    this.countdown = 60; // 60 seconds countdown\n    this.countdownInterval = setInterval(() => {\n      this.countdown--;\n      if (this.countdown <= 0) {\n        clearInterval(this.countdownInterval);\n      }\n    }, 1000);\n  }\n  onDigitInput(event, nextInput) {\n    const input = event.target;\n    const value = input.value;\n    if (value && /^\\d$/.test(value)) {\n      // Move to next input\n      const nextElement = document.getElementById(nextInput);\n      if (nextElement) {\n        nextElement.focus();\n      }\n    }\n  }\n  onDigitKeydown(event, prevInput) {\n    const input = event.target;\n    if (event.key === 'Backspace' && !input.value && prevInput) {\n      // Move to previous input on backspace\n      const prevElement = document.getElementById(prevInput);\n      if (prevElement) {\n        prevElement.focus();\n      }\n    }\n  }\n  onPaste(event) {\n    event.preventDefault();\n    const pastedData = event.clipboardData?.getData('text') || '';\n    if (/^\\d{6}$/.test(pastedData)) {\n      // Valid 6-digit OTP pasted\n      const digits = pastedData.split('');\n      this.otpForm.patchValue({\n        digit1: digits[0],\n        digit2: digits[1],\n        digit3: digits[2],\n        digit4: digits[3],\n        digit5: digits[4],\n        digit6: digits[5]\n      });\n      // Focus on last input\n      const lastInput = document.getElementById('digit6');\n      if (lastInput) {\n        lastInput.focus();\n      }\n    }\n  }\n  getOtpValue() {\n    const formValue = this.otpForm.value;\n    return `${formValue.digit1}${formValue.digit2}${formValue.digit3}${formValue.digit4}${formValue.digit5}${formValue.digit6}`;\n  }\n  verifyOtp() {\n    if (this.otpForm.valid && this.user) {\n      this.loading = true;\n      const otp = this.getOtpValue();\n      this.authService.verifyOtp(this.user._id, otp).subscribe({\n        next: response => {\n          this.loading = false;\n          if (response.success) {\n            // Store verified user data for password reset\n            localStorage.setItem('verifiedUser', JSON.stringify(this.user));\n            Swal.fire({\n              title: 'OTP Verified!',\n              text: 'Your OTP has been successfully verified. You can now reset your password.',\n              icon: 'success',\n              confirmButtonText: 'Reset Password',\n              confirmButtonColor: '#29578c'\n            }).then(result => {\n              if (result.isConfirmed) {\n                this.router.navigate(['/auth/reset-password']);\n              }\n            });\n          }\n        },\n        error: error => {\n          this.loading = false;\n          console.error('OTP verification error:', error);\n          const errorMessage = error.error?.error || error.error?.message || 'Invalid OTP. Please try again.';\n          Swal.fire({\n            title: 'Verification Failed',\n            text: errorMessage,\n            icon: 'error',\n            confirmButtonColor: '#29578c'\n          });\n          // Clear the form on error\n          this.otpForm.reset();\n          const firstInput = document.getElementById('digit1');\n          if (firstInput) {\n            firstInput.focus();\n          }\n        }\n      });\n    } else {\n      Swal.fire({\n        title: 'Invalid OTP',\n        text: 'Please enter all 6 digits of the OTP.',\n        icon: 'warning',\n        confirmButtonColor: '#29578c'\n      });\n    }\n  }\n  resendOtp() {\n    if (!this.user || this.countdown > 0) return;\n    this.resendLoading = true;\n    const resendData = {\n      userId: this.user._id,\n      ...(this.otpId && {\n        otpId: this.otpId\n      })\n    };\n    this.authService.resendOtp(resendData).subscribe({\n      next: response => {\n        this.resendLoading = false;\n        if (response.success) {\n          if (response.otpId) {\n            this.otpId = response.otpId;\n          }\n          Swal.fire({\n            title: 'OTP Resent!',\n            text: 'A new OTP has been sent to your email.',\n            icon: 'success',\n            confirmButtonColor: '#29578c'\n          });\n          // Restart countdown\n          this.startCountdown();\n          // Clear the form\n          this.otpForm.reset();\n          const firstInput = document.getElementById('digit1');\n          if (firstInput) {\n            firstInput.focus();\n          }\n        }\n      },\n      error: error => {\n        this.resendLoading = false;\n        console.error('Resend OTP error:', error);\n        const errorMessage = error.error?.message || 'Failed to resend OTP. Please try again.';\n        Swal.fire({\n          title: 'Resend Failed',\n          text: errorMessage,\n          icon: 'error',\n          confirmButtonColor: '#29578c'\n        });\n      }\n    });\n  }\n  goBack() {\n    this.router.navigate(['/auth/forgot-password']);\n  }\n  static #_ = this.ɵfac = function VerifyOtpComponent_Factory(t) {\n    return new (t || VerifyOtpComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: VerifyOtpComponent,\n    selectors: [[\"app-verify-otp\"]],\n    decls: 53,\n    vars: 22,\n    consts: [[1, \"container-fluid\"], [1, \"row\", \"vh-100\"], [1, \"col-lg-5\", \"d-flex\", \"flex-column\", \"justify-content-center\", \"align-items-center\", \"p-3\", \"bg-white\"], [1, \"w-100\", 2, \"max-width\", \"400px\"], [1, \"text-center\"], [1, \"mb-4\"], [\"src\", \"../../../assets/images/logo.jpeg\"], [1, \"d-flex\", \"justify-content-center\", \"mt-5\", \"mb-2\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"fa-envelope\", \"key\"], [4, \"ngIf\"], [1, \"text-muted\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"otp-container\", \"mb-4\"], [\"type\", \"text\", \"id\", \"digit1\", \"formControlName\", \"digit1\", \"maxlength\", \"1\", 1, \"otp-input\", 3, \"input\", \"keydown\", \"paste\"], [\"type\", \"text\", \"id\", \"digit2\", \"formControlName\", \"digit2\", \"maxlength\", \"1\", 1, \"otp-input\", 3, \"input\", \"keydown\"], [\"type\", \"text\", \"id\", \"digit3\", \"formControlName\", \"digit3\", \"maxlength\", \"1\", 1, \"otp-input\", 3, \"input\", \"keydown\"], [\"type\", \"text\", \"id\", \"digit4\", \"formControlName\", \"digit4\", \"maxlength\", \"1\", 1, \"otp-input\", 3, \"input\", \"keydown\"], [\"type\", \"text\", \"id\", \"digit5\", \"formControlName\", \"digit5\", \"maxlength\", \"1\", 1, \"otp-input\", 3, \"input\", \"keydown\"], [\"type\", \"text\", \"id\", \"digit6\", \"formControlName\", \"digit6\", \"maxlength\", \"1\", 1, \"otp-input\", 3, \"input\", \"keydown\"], [1, \"d-grid\", \"gap-2\"], [\"type\", \"submit\", 1, \"btn\", \"submit\", 3, \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", \"role\", \"status\", \"aria-hidden\", \"true\", 4, \"ngIf\"], [1, \"text-center\", \"mt-4\"], [1, \"mb-2\"], [\"type\", \"button\", 1, \"btn\", \"resend\", 3, \"disabled\", \"click\"], [\"class\", \"spinner-border spinner-border-sm me-1\", \"role\", \"status\", \"aria-hidden\", \"true\", 4, \"ngIf\"], [1, \"mt-4\", \"text-center\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"fa-arrow-left\"], [2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"col-lg-7\", \"d-none\", \"d-lg-flex\", \"flex-column\", \"align-items-start\", \"bg-light\", \"position-relative\"], [1, \"text-start\", \"p-5\", \"w-100\"], [1, \"blockquote\"], [1, \"blockquote-footer\"], [\"title\", \"Source Title\"], [1, \"image-container\"], [\"src\", \"../../../assets/images/background.jpeg\", \"alt\", \"College background\", 1, \"img-fluid\", \"position-absolute\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-1\"]],\n    template: function VerifyOtpComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"h2\", 5);\n        i0.ɵɵelement(6, \"img\", 6);\n        i0.ɵɵtext(7, \" GPGC (Swabi)\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"div\", 7);\n        i0.ɵɵelement(9, \"i\", 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"h1\", 5);\n        i0.ɵɵtext(11, \"Enter Verification Code\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(12, VerifyOtpComponent_p_12_Template, 4, 1, \"p\", 9);\n        i0.ɵɵelementStart(13, \"p\", 10);\n        i0.ɵɵtext(14, \"Please enter the code below to verify your identity.\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(15, \"form\", 11);\n        i0.ɵɵlistener(\"ngSubmit\", function VerifyOtpComponent_Template_form_ngSubmit_15_listener() {\n          return ctx.verifyOtp();\n        });\n        i0.ɵɵelementStart(16, \"div\", 12)(17, \"input\", 13);\n        i0.ɵɵlistener(\"input\", function VerifyOtpComponent_Template_input_input_17_listener($event) {\n          return ctx.onDigitInput($event, \"digit2\");\n        })(\"keydown\", function VerifyOtpComponent_Template_input_keydown_17_listener($event) {\n          return ctx.onDigitKeydown($event, \"\");\n        })(\"paste\", function VerifyOtpComponent_Template_input_paste_17_listener($event) {\n          return ctx.onPaste($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(18, \"input\", 14);\n        i0.ɵɵlistener(\"input\", function VerifyOtpComponent_Template_input_input_18_listener($event) {\n          return ctx.onDigitInput($event, \"digit3\");\n        })(\"keydown\", function VerifyOtpComponent_Template_input_keydown_18_listener($event) {\n          return ctx.onDigitKeydown($event, \"digit1\");\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(19, \"input\", 15);\n        i0.ɵɵlistener(\"input\", function VerifyOtpComponent_Template_input_input_19_listener($event) {\n          return ctx.onDigitInput($event, \"digit4\");\n        })(\"keydown\", function VerifyOtpComponent_Template_input_keydown_19_listener($event) {\n          return ctx.onDigitKeydown($event, \"digit2\");\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"input\", 16);\n        i0.ɵɵlistener(\"input\", function VerifyOtpComponent_Template_input_input_20_listener($event) {\n          return ctx.onDigitInput($event, \"digit5\");\n        })(\"keydown\", function VerifyOtpComponent_Template_input_keydown_20_listener($event) {\n          return ctx.onDigitKeydown($event, \"digit3\");\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(21, \"input\", 17);\n        i0.ɵɵlistener(\"input\", function VerifyOtpComponent_Template_input_input_21_listener($event) {\n          return ctx.onDigitInput($event, \"digit6\");\n        })(\"keydown\", function VerifyOtpComponent_Template_input_keydown_21_listener($event) {\n          return ctx.onDigitKeydown($event, \"digit4\");\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(22, \"input\", 18);\n        i0.ɵɵlistener(\"input\", function VerifyOtpComponent_Template_input_input_22_listener($event) {\n          return ctx.onDigitInput($event, \"\");\n        })(\"keydown\", function VerifyOtpComponent_Template_input_keydown_22_listener($event) {\n          return ctx.onDigitKeydown($event, \"digit5\");\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(23, \"div\", 19)(24, \"button\", 20);\n        i0.ɵɵtemplate(25, VerifyOtpComponent_span_25_Template, 1, 0, \"span\", 21);\n        i0.ɵɵtext(26);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(27, \"div\", 22)(28, \"p\", 23);\n        i0.ɵɵtext(29, \"Didn't receive the code?\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(30, \"button\", 24);\n        i0.ɵɵlistener(\"click\", function VerifyOtpComponent_Template_button_click_30_listener() {\n          return ctx.resendOtp();\n        });\n        i0.ɵɵtemplate(31, VerifyOtpComponent_span_31_Template, 1, 0, \"span\", 25);\n        i0.ɵɵtemplate(32, VerifyOtpComponent_span_32_Template, 2, 1, \"span\", 9);\n        i0.ɵɵtemplate(33, VerifyOtpComponent_span_33_Template, 2, 0, \"span\", 9);\n        i0.ɵɵtemplate(34, VerifyOtpComponent_span_34_Template, 2, 0, \"span\", 9);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(35, \"p\", 26);\n        i0.ɵɵelement(36, \"i\", 27);\n        i0.ɵɵtext(37, \" \\u00A0 \");\n        i0.ɵɵelementStart(38, \"a\", 28);\n        i0.ɵɵlistener(\"click\", function VerifyOtpComponent_Template_a_click_38_listener() {\n          return ctx.goBack();\n        });\n        i0.ɵɵtext(39, \"Back to forgot password\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(40, \"div\", 29)(41, \"div\", 30)(42, \"blockquote\", 31)(43, \"h2\", 5);\n        i0.ɵɵtext(44, \"College management system\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(45, \"p\", 5);\n        i0.ɵɵtext(46, \"Secure password reset process\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(47, \"footer\", 32);\n        i0.ɵɵtext(48, \"GPGC SWABI \");\n        i0.ɵɵelementStart(49, \"cite\", 33);\n        i0.ɵɵtext(50, \"Government Post Graduate College\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(51, \"div\", 34);\n        i0.ɵɵelement(52, \"img\", 35);\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        let tmp_2_0;\n        let tmp_3_0;\n        let tmp_4_0;\n        let tmp_5_0;\n        let tmp_6_0;\n        let tmp_7_0;\n        i0.ɵɵadvance(12);\n        i0.ɵɵproperty(\"ngIf\", ctx.user);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"formGroup\", ctx.otpForm);\n        i0.ɵɵadvance(2);\n        i0.ɵɵclassProp(\"is-invalid\", ((tmp_2_0 = ctx.otpForm.get(\"digit1\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.otpForm.get(\"digit1\")) == null ? null : tmp_2_0.touched));\n        i0.ɵɵadvance(1);\n        i0.ɵɵclassProp(\"is-invalid\", ((tmp_3_0 = ctx.otpForm.get(\"digit2\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.otpForm.get(\"digit2\")) == null ? null : tmp_3_0.touched));\n        i0.ɵɵadvance(1);\n        i0.ɵɵclassProp(\"is-invalid\", ((tmp_4_0 = ctx.otpForm.get(\"digit3\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.otpForm.get(\"digit3\")) == null ? null : tmp_4_0.touched));\n        i0.ɵɵadvance(1);\n        i0.ɵɵclassProp(\"is-invalid\", ((tmp_5_0 = ctx.otpForm.get(\"digit4\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.otpForm.get(\"digit4\")) == null ? null : tmp_5_0.touched));\n        i0.ɵɵadvance(1);\n        i0.ɵɵclassProp(\"is-invalid\", ((tmp_6_0 = ctx.otpForm.get(\"digit5\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx.otpForm.get(\"digit5\")) == null ? null : tmp_6_0.touched));\n        i0.ɵɵadvance(1);\n        i0.ɵɵclassProp(\"is-invalid\", ((tmp_7_0 = ctx.otpForm.get(\"digit6\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx.otpForm.get(\"digit6\")) == null ? null : tmp_7_0.touched));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"disabled\", ctx.loading || ctx.otpForm.invalid);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate1(\" \", ctx.loading ? \"Verifying...\" : \"Verify OTP\", \" \");\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"disabled\", ctx.resendLoading || ctx.countdown > 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.resendLoading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.countdown > 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.countdown <= 0 && !ctx.resendLoading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.resendLoading);\n      }\n    },\n    dependencies: [i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MaxLengthValidator, i1.FormGroupDirective, i1.FormControlName],\n    styles: [\".container-fluid[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  overflow: hidden;\\n}\\n\\n.key[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  color: #29578c;\\n  margin-bottom: 1rem;\\n}\\n\\nh1[_ngcontent-%COMP%] {\\n  color: #29578c;\\n  font-weight: 600;\\n  font-size: 2rem;\\n}\\n\\nh2[_ngcontent-%COMP%] {\\n  color: #29578c;\\n  font-weight: 500;\\n}\\n\\np[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  margin-bottom: 1rem;\\n}\\n\\n.text-muted[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n\\n\\n\\n.otp-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 10px;\\n  margin: 2rem 0;\\n}\\n\\n.otp-input[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  text-align: center;\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  border: 2px solid #ced4da;\\n  border-radius: 8px;\\n  background-color: #f8f9fa;\\n  transition: all 0.3s ease;\\n}\\n\\n.otp-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #29578c;\\n  background-color: white;\\n  box-shadow: 0 0 0 0.2rem rgba(41, 87, 140, 0.25);\\n}\\n\\n.otp-input.is-invalid[_ngcontent-%COMP%] {\\n  border-color: #dc3545;\\n  background-color: #fff5f5;\\n}\\n\\n.otp-input[_ngcontent-%COMP%]:valid {\\n  border-color: #28a745;\\n  background-color: #f0fff4;\\n}\\n\\n\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 12px 24px;\\n  border-radius: 6px;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n  border: none;\\n  cursor: pointer;\\n}\\n\\n.submit[_ngcontent-%COMP%] {\\n  background-color: #29578c;\\n  color: white;\\n  font-size: 1rem;\\n}\\n\\n.submit[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #1e3f63;\\n  transform: translateY(-1px);\\n}\\n\\n.submit[_ngcontent-%COMP%]:disabled {\\n  background-color: #6c757d;\\n  cursor: not-allowed;\\n  transform: none;\\n}\\n\\n.resend[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  color: #29578c;\\n  border: 2px solid #29578c;\\n  padding: 8px 16px;\\n  font-size: 0.9rem;\\n}\\n\\n.resend[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #29578c;\\n  color: white;\\n}\\n\\n.resend[_ngcontent-%COMP%]:disabled {\\n  background-color: transparent;\\n  color: #6c757d;\\n  border-color: #6c757d;\\n  cursor: not-allowed;\\n}\\n\\n\\n\\n.spinner-border-sm[_ngcontent-%COMP%] {\\n  width: 1rem;\\n  height: 1rem;\\n}\\n\\n\\n\\na[_ngcontent-%COMP%] {\\n  color: #29578c;\\n  text-decoration: none;\\n  font-weight: 500;\\n}\\n\\na[_ngcontent-%COMP%]:hover {\\n  color: #1e3f63;\\n  text-decoration: underline;\\n}\\n\\n\\n\\n.image-container[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  right: 0;\\n  width: 100%;\\n  height: 60%;\\n  overflow: hidden;\\n}\\n\\n.image-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  opacity: 0.8;\\n}\\n\\n\\n\\n.blockquote[_ngcontent-%COMP%] {\\n  border-left: 4px solid #29578c;\\n  padding-left: 1rem;\\n}\\n\\n.blockquote[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n\\n.blockquote-footer[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 0.9rem;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .otp-container[_ngcontent-%COMP%] {\\n    gap: 8px;\\n  }\\n  \\n  .otp-input[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n    font-size: 1.2rem;\\n  }\\n  \\n  h1[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  \\n  .key[_ngcontent-%COMP%] {\\n    font-size: 2.5rem;\\n  }\\n}\\n\\n@media (max-width: 480px) {\\n  .otp-container[_ngcontent-%COMP%] {\\n    gap: 5px;\\n  }\\n  \\n  .otp-input[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n    font-size: 1rem;\\n  }\\n  \\n  .container-fluid[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n}\\n\\n\\n\\n.otp-input.is-invalid[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_shake 0.5s ease-in-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_shake {\\n  0%, 100% { transform: translateX(0); }\\n  25% { transform: translateX(-5px); }\\n  75% { transform: translateX(5px); }\\n}\\n\\n\\n\\n.otp-input[_ngcontent-%COMP%]:valid:not(:placeholder-shown) {\\n  animation: _ngcontent-%COMP%_pulse 0.3s ease-in-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% { transform: scale(1); }\\n  50% { transform: scale(1.05); }\\n  100% { transform: scale(1); }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "user", "email", "ɵɵelement", "ɵɵtextInterpolate1", "ctx_r3", "countdown", "VerifyOtpComponent", "constructor", "fb", "authService", "router", "loading", "resendLoading", "otpId", "ngOnInit", "resetUser", "localStorage", "getItem", "JSON", "parse", "fire", "title", "text", "icon", "confirmButtonColor", "then", "navigate", "initializeForm", "startCountdown", "ngOnDestroy", "countdownInterval", "clearInterval", "otpForm", "group", "digit1", "required", "pattern", "digit2", "digit3", "digit4", "digit5", "digit6", "setInterval", "onDigitInput", "event", "nextInput", "input", "target", "value", "test", "nextElement", "document", "getElementById", "focus", "onDigitKeydown", "prevInput", "key", "prevElement", "onPaste", "preventDefault", "pastedData", "clipboardData", "getData", "digits", "split", "patchValue", "lastInput", "getOtpValue", "formValue", "verifyOtp", "valid", "otp", "_id", "subscribe", "next", "response", "success", "setItem", "stringify", "confirmButtonText", "result", "isConfirmed", "error", "console", "errorMessage", "message", "reset", "firstInput", "resendOtp", "resendData", "userId", "goBack", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "_2", "selectors", "decls", "vars", "consts", "template", "VerifyOtpComponent_Template", "rf", "ctx", "ɵɵtemplate", "VerifyOtpComponent_p_12_Template", "ɵɵlistener", "VerifyOtpComponent_Template_form_ngSubmit_15_listener", "VerifyOtpComponent_Template_input_input_17_listener", "$event", "VerifyOtpComponent_Template_input_keydown_17_listener", "VerifyOtpComponent_Template_input_paste_17_listener", "VerifyOtpComponent_Template_input_input_18_listener", "VerifyOtpComponent_Template_input_keydown_18_listener", "VerifyOtpComponent_Template_input_input_19_listener", "VerifyOtpComponent_Template_input_keydown_19_listener", "VerifyOtpComponent_Template_input_input_20_listener", "VerifyOtpComponent_Template_input_keydown_20_listener", "VerifyOtpComponent_Template_input_input_21_listener", "VerifyOtpComponent_Template_input_keydown_21_listener", "VerifyOtpComponent_Template_input_input_22_listener", "VerifyOtpComponent_Template_input_keydown_22_listener", "VerifyOtpComponent_span_25_Template", "VerifyOtpComponent_Template_button_click_30_listener", "VerifyOtpComponent_span_31_Template", "VerifyOtpComponent_span_32_Template", "VerifyOtpComponent_span_33_Template", "VerifyOtpComponent_span_34_Template", "VerifyOtpComponent_Template_a_click_38_listener", "ɵɵproperty", "ɵɵclassProp", "tmp_2_0", "get", "invalid", "touched", "tmp_3_0", "tmp_4_0", "tmp_5_0", "tmp_6_0", "tmp_7_0"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\auth\\verify-otp\\verify-otp.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\auth\\verify-otp\\verify-otp.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { AuthService } from 'src/app/services/auth.service';\nimport Swal from 'sweetalert2';\n\n@Component({\n  selector: 'app-verify-otp',\n  templateUrl: './verify-otp.component.html',\n  styleUrls: ['./verify-otp.component.css']\n})\nexport class VerifyOtpComponent implements OnInit {\n  otpForm!: FormGroup;\n  loading = false;\n  resendLoading = false;\n  user: any = null;\n  otpId: string | null = null;\n  countdown = 0;\n  countdownInterval: any;\n\n  constructor(\n    private fb: FormBuilder,\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    // Get user data from localStorage (set during forgot password)\n    const resetUser = localStorage.getItem('resetUser');\n    \n    if (resetUser) {\n      this.user = JSON.parse(resetUser);\n    } else {\n      // If no user data, redirect to forgot password\n      Swal.fire({\n        title: 'Session Expired',\n        text: 'Please start the password reset process again.',\n        icon: 'warning',\n        confirmButtonColor: '#29578c'\n      }).then(() => {\n        this.router.navigate(['/auth/forgot-password']);\n      });\n      return;\n    }\n\n    this.initializeForm();\n    this.startCountdown();\n  }\n\n  ngOnDestroy(): void {\n    if (this.countdownInterval) {\n      clearInterval(this.countdownInterval);\n    }\n  }\n\n  initializeForm(): void {\n    this.otpForm = this.fb.group({\n      digit1: ['', [Validators.required, Validators.pattern(/^\\d$/)]],\n      digit2: ['', [Validators.required, Validators.pattern(/^\\d$/)]],\n      digit3: ['', [Validators.required, Validators.pattern(/^\\d$/)]],\n      digit4: ['', [Validators.required, Validators.pattern(/^\\d$/)]],\n      digit5: ['', [Validators.required, Validators.pattern(/^\\d$/)]],\n      digit6: ['', [Validators.required, Validators.pattern(/^\\d$/)]],\n    });\n  }\n\n  startCountdown(): void {\n    this.countdown = 60; // 60 seconds countdown\n    this.countdownInterval = setInterval(() => {\n      this.countdown--;\n      if (this.countdown <= 0) {\n        clearInterval(this.countdownInterval);\n      }\n    }, 1000);\n  }\n\n  onDigitInput(event: any, nextInput: string): void {\n    const input = event.target;\n    const value = input.value;\n\n    if (value && /^\\d$/.test(value)) {\n      // Move to next input\n      const nextElement = document.getElementById(nextInput) as HTMLInputElement;\n      if (nextElement) {\n        nextElement.focus();\n      }\n    }\n  }\n\n  onDigitKeydown(event: any, prevInput: string): void {\n    const input = event.target;\n    \n    if (event.key === 'Backspace' && !input.value && prevInput) {\n      // Move to previous input on backspace\n      const prevElement = document.getElementById(prevInput) as HTMLInputElement;\n      if (prevElement) {\n        prevElement.focus();\n      }\n    }\n  }\n\n  onPaste(event: ClipboardEvent): void {\n    event.preventDefault();\n    const pastedData = event.clipboardData?.getData('text') || '';\n    \n    if (/^\\d{6}$/.test(pastedData)) {\n      // Valid 6-digit OTP pasted\n      const digits = pastedData.split('');\n      this.otpForm.patchValue({\n        digit1: digits[0],\n        digit2: digits[1],\n        digit3: digits[2],\n        digit4: digits[3],\n        digit5: digits[4],\n        digit6: digits[5]\n      });\n      \n      // Focus on last input\n      const lastInput = document.getElementById('digit6') as HTMLInputElement;\n      if (lastInput) {\n        lastInput.focus();\n      }\n    }\n  }\n\n  getOtpValue(): string {\n    const formValue = this.otpForm.value;\n    return `${formValue.digit1}${formValue.digit2}${formValue.digit3}${formValue.digit4}${formValue.digit5}${formValue.digit6}`;\n  }\n\n  verifyOtp(): void {\n    if (this.otpForm.valid && this.user) {\n      this.loading = true;\n      const otp = this.getOtpValue();\n\n      this.authService.verifyOtp(this.user._id, otp).subscribe({\n        next: (response) => {\n          this.loading = false;\n          if (response.success) {\n            // Store verified user data for password reset\n            localStorage.setItem('verifiedUser', JSON.stringify(this.user));\n            \n            Swal.fire({\n              title: 'OTP Verified!',\n              text: 'Your OTP has been successfully verified. You can now reset your password.',\n              icon: 'success',\n              confirmButtonText: 'Reset Password',\n              confirmButtonColor: '#29578c'\n            }).then((result) => {\n              if (result.isConfirmed) {\n                this.router.navigate(['/auth/reset-password']);\n              }\n            });\n          }\n        },\n        error: (error) => {\n          this.loading = false;\n          console.error('OTP verification error:', error);\n          \n          const errorMessage = error.error?.error || error.error?.message || 'Invalid OTP. Please try again.';\n          \n          Swal.fire({\n            title: 'Verification Failed',\n            text: errorMessage,\n            icon: 'error',\n            confirmButtonColor: '#29578c'\n          });\n\n          // Clear the form on error\n          this.otpForm.reset();\n          const firstInput = document.getElementById('digit1') as HTMLInputElement;\n          if (firstInput) {\n            firstInput.focus();\n          }\n        }\n      });\n    } else {\n      Swal.fire({\n        title: 'Invalid OTP',\n        text: 'Please enter all 6 digits of the OTP.',\n        icon: 'warning',\n        confirmButtonColor: '#29578c'\n      });\n    }\n  }\n\n  resendOtp(): void {\n    if (!this.user || this.countdown > 0) return;\n\n    this.resendLoading = true;\n    const resendData = {\n      userId: this.user._id,\n      ...(this.otpId && { otpId: this.otpId })\n    };\n\n    this.authService.resendOtp(resendData).subscribe({\n      next: (response) => {\n        this.resendLoading = false;\n        if (response.success) {\n          if (response.otpId) {\n            this.otpId = response.otpId;\n          }\n          \n          Swal.fire({\n            title: 'OTP Resent!',\n            text: 'A new OTP has been sent to your email.',\n            icon: 'success',\n            confirmButtonColor: '#29578c'\n          });\n\n          // Restart countdown\n          this.startCountdown();\n          \n          // Clear the form\n          this.otpForm.reset();\n          const firstInput = document.getElementById('digit1') as HTMLInputElement;\n          if (firstInput) {\n            firstInput.focus();\n          }\n        }\n      },\n      error: (error) => {\n        this.resendLoading = false;\n        console.error('Resend OTP error:', error);\n        \n        const errorMessage = error.error?.message || 'Failed to resend OTP. Please try again.';\n        \n        Swal.fire({\n          title: 'Resend Failed',\n          text: errorMessage,\n          icon: 'error',\n          confirmButtonColor: '#29578c'\n        });\n      }\n    });\n  }\n\n  goBack(): void {\n    this.router.navigate(['/auth/forgot-password']);\n  }\n}\n", "<div class=\"container-fluid\">\n    <div class=\"row vh-100\">\n        <div class=\"col-lg-5 d-flex flex-column justify-content-center align-items-center p-3 bg-white\">\n            <div class=\"w-100\" style=\"max-width: 400px;\">\n                <div class=\"text-center\">\n                    <h2 class=\"mb-4\"><img src=\"../../../assets/images/logo.jpeg\"/> GPGC (Swabi)</h2>\n                    <div class=\"d-flex justify-content-center mt-5 mb-2\">\n                        <i class=\"fa fa-envelope key\" aria-hidden=\"true\"></i>\n                    </div>\n                    \n                    <h1 class=\"mb-4\">Enter Verification Code</h1>\n                    <p *ngIf=\"user\">We sent a 6-digit verification code to <strong>{{ user.email }}</strong></p>\n                    <p class=\"text-muted\">Please enter the code below to verify your identity.</p>\n                </div>\n                \n                <form [formGroup]=\"otpForm\" (ngSubmit)=\"verifyOtp()\">\n                    <!-- OTP Input Fields -->\n                    <div class=\"otp-container mb-4\">\n                        <input \n                            type=\"text\" \n                            id=\"digit1\"\n                            class=\"otp-input\" \n                            formControlName=\"digit1\"\n                            maxlength=\"1\"\n                            (input)=\"onDigitInput($event, 'digit2')\"\n                            (keydown)=\"onDigitKeydown($event, '')\"\n                            (paste)=\"onPaste($event)\"\n                            [class.is-invalid]=\"otpForm.get('digit1')?.invalid && otpForm.get('digit1')?.touched\">\n                        \n                        <input \n                            type=\"text\" \n                            id=\"digit2\"\n                            class=\"otp-input\" \n                            formControlName=\"digit2\"\n                            maxlength=\"1\"\n                            (input)=\"onDigitInput($event, 'digit3')\"\n                            (keydown)=\"onDigitKeydown($event, 'digit1')\"\n                            [class.is-invalid]=\"otpForm.get('digit2')?.invalid && otpForm.get('digit2')?.touched\">\n                        \n                        <input \n                            type=\"text\" \n                            id=\"digit3\"\n                            class=\"otp-input\" \n                            formControlName=\"digit3\"\n                            maxlength=\"1\"\n                            (input)=\"onDigitInput($event, 'digit4')\"\n                            (keydown)=\"onDigitKeydown($event, 'digit2')\"\n                            [class.is-invalid]=\"otpForm.get('digit3')?.invalid && otpForm.get('digit3')?.touched\">\n                        \n                        <input \n                            type=\"text\" \n                            id=\"digit4\"\n                            class=\"otp-input\" \n                            formControlName=\"digit4\"\n                            maxlength=\"1\"\n                            (input)=\"onDigitInput($event, 'digit5')\"\n                            (keydown)=\"onDigitKeydown($event, 'digit3')\"\n                            [class.is-invalid]=\"otpForm.get('digit4')?.invalid && otpForm.get('digit4')?.touched\">\n                        \n                        <input \n                            type=\"text\" \n                            id=\"digit5\"\n                            class=\"otp-input\" \n                            formControlName=\"digit5\"\n                            maxlength=\"1\"\n                            (input)=\"onDigitInput($event, 'digit6')\"\n                            (keydown)=\"onDigitKeydown($event, 'digit4')\"\n                            [class.is-invalid]=\"otpForm.get('digit5')?.invalid && otpForm.get('digit5')?.touched\">\n                        \n                        <input \n                            type=\"text\" \n                            id=\"digit6\"\n                            class=\"otp-input\" \n                            formControlName=\"digit6\"\n                            maxlength=\"1\"\n                            (input)=\"onDigitInput($event, '')\"\n                            (keydown)=\"onDigitKeydown($event, 'digit5')\"\n                            [class.is-invalid]=\"otpForm.get('digit6')?.invalid && otpForm.get('digit6')?.touched\">\n                    </div>\n                  \n                    <div class=\"d-grid gap-2\">\n                        <button \n                            type=\"submit\" \n                            class=\"btn submit\" \n                            [disabled]=\"loading || otpForm.invalid\">\n                            <span *ngIf=\"loading\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\n                            {{ loading ? 'Verifying...' : 'Verify OTP' }}\n                        </button>\n                    </div>\n                    \n                    <!-- Resend OTP Section -->\n                    <div class=\"text-center mt-4\">\n                        <p class=\"mb-2\">Didn't receive the code?</p>\n                        <button \n                            type=\"button\" \n                            class=\"btn resend\" \n                            (click)=\"resendOtp()\"\n                            [disabled]=\"resendLoading || countdown > 0\">\n                            <span *ngIf=\"resendLoading\" class=\"spinner-border spinner-border-sm me-1\" role=\"status\" aria-hidden=\"true\"></span>\n                            <span *ngIf=\"countdown > 0\">Resend in {{ countdown }}s</span>\n                            <span *ngIf=\"countdown <= 0 && !resendLoading\">Resend OTP</span>\n                            <span *ngIf=\"resendLoading\">Sending...</span>\n                        </button>\n                    </div>\n                    \n                    <p class=\"mt-4 text-center\"> \n                        <i class=\"fa fa-arrow-left\" aria-hidden=\"true\"></i> &nbsp;\n                        <a (click)=\"goBack()\" style=\"cursor: pointer;\">Back to forgot password</a>\n                    </p>\n                </form>\n            </div>\n        </div>\n        <div class=\"col-lg-7 d-none d-lg-flex flex-column align-items-start bg-light position-relative\">\n            <div class=\"text-start p-5 w-100\">\n                <blockquote class=\"blockquote\">\n                    <h2 class=\"mb-4\">College management system</h2>\n                    <p class=\"mb-4\">Secure password reset process</p>\n                    <footer class=\"blockquote-footer\">GPGC SWABI <cite title=\"Source Title\">Government Post Graduate College</cite></footer>\n                </blockquote>\n            </div>\n            <div class=\"image-container\">\n                <img src=\"../../../assets/images/background.jpeg\" class=\"img-fluid position-absolute\" alt=\"College background\">\n            </div>\n        </div>\n    </div>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAGnE,OAAOC,IAAI,MAAM,aAAa;;;;;;;;ICOVC,EAAA,CAAAC,cAAA,QAAgB;IAAAD,EAAA,CAAAE,MAAA,8CAAuC;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAzBH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAC,KAAA,CAAgB;;;;;IA0EvER,EAAA,CAAAS,SAAA,eAA4G;;;;;IAa5GT,EAAA,CAAAS,SAAA,eAAkH;;;;;IAClHT,EAAA,CAAAC,cAAA,WAA4B;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAjCH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAU,kBAAA,eAAAC,MAAA,CAAAC,SAAA,MAA0B;;;;;IACtDZ,EAAA,CAAAC,cAAA,WAA+C;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAChEH,EAAA,CAAAC,cAAA,WAA4B;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;AD1FzE,OAAM,MAAOU,kBAAkB;EAS7BC,YACUC,EAAe,EACfC,WAAwB,EACxBC,MAAc;IAFd,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAVhB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAZ,IAAI,GAAQ,IAAI;IAChB,KAAAa,KAAK,GAAkB,IAAI;IAC3B,KAAAR,SAAS,GAAG,CAAC;EAOV;EAEHS,QAAQA,CAAA;IACN;IACA,MAAMC,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAEnD,IAAIF,SAAS,EAAE;MACb,IAAI,CAACf,IAAI,GAAGkB,IAAI,CAACC,KAAK,CAACJ,SAAS,CAAC;KAClC,MAAM;MACL;MACAvB,IAAI,CAAC4B,IAAI,CAAC;QACRC,KAAK,EAAE,iBAAiB;QACxBC,IAAI,EAAE,gDAAgD;QACtDC,IAAI,EAAE,SAAS;QACfC,kBAAkB,EAAE;OACrB,CAAC,CAACC,IAAI,CAAC,MAAK;QACX,IAAI,CAACf,MAAM,CAACgB,QAAQ,CAAC,CAAC,uBAAuB,CAAC,CAAC;MACjD,CAAC,CAAC;MACF;;IAGF,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACC,iBAAiB,EAAE;MAC1BC,aAAa,CAAC,IAAI,CAACD,iBAAiB,CAAC;;EAEzC;EAEAH,cAAcA,CAAA;IACZ,IAAI,CAACK,OAAO,GAAG,IAAI,CAACxB,EAAE,CAACyB,KAAK,CAAC;MAC3BC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC3C,UAAU,CAAC4C,QAAQ,EAAE5C,UAAU,CAAC6C,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;MAC/DC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC9C,UAAU,CAAC4C,QAAQ,EAAE5C,UAAU,CAAC6C,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;MAC/DE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC/C,UAAU,CAAC4C,QAAQ,EAAE5C,UAAU,CAAC6C,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;MAC/DG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAChD,UAAU,CAAC4C,QAAQ,EAAE5C,UAAU,CAAC6C,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;MAC/DI,MAAM,EAAE,CAAC,EAAE,EAAE,CAACjD,UAAU,CAAC4C,QAAQ,EAAE5C,UAAU,CAAC6C,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;MAC/DK,MAAM,EAAE,CAAC,EAAE,EAAE,CAAClD,UAAU,CAAC4C,QAAQ,EAAE5C,UAAU,CAAC6C,OAAO,CAAC,MAAM,CAAC,CAAC;KAC/D,CAAC;EACJ;EAEAR,cAAcA,CAAA;IACZ,IAAI,CAACvB,SAAS,GAAG,EAAE,CAAC,CAAC;IACrB,IAAI,CAACyB,iBAAiB,GAAGY,WAAW,CAAC,MAAK;MACxC,IAAI,CAACrC,SAAS,EAAE;MAChB,IAAI,IAAI,CAACA,SAAS,IAAI,CAAC,EAAE;QACvB0B,aAAa,CAAC,IAAI,CAACD,iBAAiB,CAAC;;IAEzC,CAAC,EAAE,IAAI,CAAC;EACV;EAEAa,YAAYA,CAACC,KAAU,EAAEC,SAAiB;IACxC,MAAMC,KAAK,GAAGF,KAAK,CAACG,MAAM;IAC1B,MAAMC,KAAK,GAAGF,KAAK,CAACE,KAAK;IAEzB,IAAIA,KAAK,IAAI,MAAM,CAACC,IAAI,CAACD,KAAK,CAAC,EAAE;MAC/B;MACA,MAAME,WAAW,GAAGC,QAAQ,CAACC,cAAc,CAACP,SAAS,CAAqB;MAC1E,IAAIK,WAAW,EAAE;QACfA,WAAW,CAACG,KAAK,EAAE;;;EAGzB;EAEAC,cAAcA,CAACV,KAAU,EAAEW,SAAiB;IAC1C,MAAMT,KAAK,GAAGF,KAAK,CAACG,MAAM;IAE1B,IAAIH,KAAK,CAACY,GAAG,KAAK,WAAW,IAAI,CAACV,KAAK,CAACE,KAAK,IAAIO,SAAS,EAAE;MAC1D;MACA,MAAME,WAAW,GAAGN,QAAQ,CAACC,cAAc,CAACG,SAAS,CAAqB;MAC1E,IAAIE,WAAW,EAAE;QACfA,WAAW,CAACJ,KAAK,EAAE;;;EAGzB;EAEAK,OAAOA,CAACd,KAAqB;IAC3BA,KAAK,CAACe,cAAc,EAAE;IACtB,MAAMC,UAAU,GAAGhB,KAAK,CAACiB,aAAa,EAAEC,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE;IAE7D,IAAI,SAAS,CAACb,IAAI,CAACW,UAAU,CAAC,EAAE;MAC9B;MACA,MAAMG,MAAM,GAAGH,UAAU,CAACI,KAAK,CAAC,EAAE,CAAC;MACnC,IAAI,CAAChC,OAAO,CAACiC,UAAU,CAAC;QACtB/B,MAAM,EAAE6B,MAAM,CAAC,CAAC,CAAC;QACjB1B,MAAM,EAAE0B,MAAM,CAAC,CAAC,CAAC;QACjBzB,MAAM,EAAEyB,MAAM,CAAC,CAAC,CAAC;QACjBxB,MAAM,EAAEwB,MAAM,CAAC,CAAC,CAAC;QACjBvB,MAAM,EAAEuB,MAAM,CAAC,CAAC,CAAC;QACjBtB,MAAM,EAAEsB,MAAM,CAAC,CAAC;OACjB,CAAC;MAEF;MACA,MAAMG,SAAS,GAAGf,QAAQ,CAACC,cAAc,CAAC,QAAQ,CAAqB;MACvE,IAAIc,SAAS,EAAE;QACbA,SAAS,CAACb,KAAK,EAAE;;;EAGvB;EAEAc,WAAWA,CAAA;IACT,MAAMC,SAAS,GAAG,IAAI,CAACpC,OAAO,CAACgB,KAAK;IACpC,OAAO,GAAGoB,SAAS,CAAClC,MAAM,GAAGkC,SAAS,CAAC/B,MAAM,GAAG+B,SAAS,CAAC9B,MAAM,GAAG8B,SAAS,CAAC7B,MAAM,GAAG6B,SAAS,CAAC5B,MAAM,GAAG4B,SAAS,CAAC3B,MAAM,EAAE;EAC7H;EAEA4B,SAASA,CAAA;IACP,IAAI,IAAI,CAACrC,OAAO,CAACsC,KAAK,IAAI,IAAI,CAACtE,IAAI,EAAE;MACnC,IAAI,CAACW,OAAO,GAAG,IAAI;MACnB,MAAM4D,GAAG,GAAG,IAAI,CAACJ,WAAW,EAAE;MAE9B,IAAI,CAAC1D,WAAW,CAAC4D,SAAS,CAAC,IAAI,CAACrE,IAAI,CAACwE,GAAG,EAAED,GAAG,CAAC,CAACE,SAAS,CAAC;QACvDC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAChE,OAAO,GAAG,KAAK;UACpB,IAAIgE,QAAQ,CAACC,OAAO,EAAE;YACpB;YACA5D,YAAY,CAAC6D,OAAO,CAAC,cAAc,EAAE3D,IAAI,CAAC4D,SAAS,CAAC,IAAI,CAAC9E,IAAI,CAAC,CAAC;YAE/DR,IAAI,CAAC4B,IAAI,CAAC;cACRC,KAAK,EAAE,eAAe;cACtBC,IAAI,EAAE,2EAA2E;cACjFC,IAAI,EAAE,SAAS;cACfwD,iBAAiB,EAAE,gBAAgB;cACnCvD,kBAAkB,EAAE;aACrB,CAAC,CAACC,IAAI,CAAEuD,MAAM,IAAI;cACjB,IAAIA,MAAM,CAACC,WAAW,EAAE;gBACtB,IAAI,CAACvE,MAAM,CAACgB,QAAQ,CAAC,CAAC,sBAAsB,CAAC,CAAC;;YAElD,CAAC,CAAC;;QAEN,CAAC;QACDwD,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACvE,OAAO,GAAG,KAAK;UACpBwE,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAE/C,MAAME,YAAY,GAAGF,KAAK,CAACA,KAAK,EAAEA,KAAK,IAAIA,KAAK,CAACA,KAAK,EAAEG,OAAO,IAAI,gCAAgC;UAEnG7F,IAAI,CAAC4B,IAAI,CAAC;YACRC,KAAK,EAAE,qBAAqB;YAC5BC,IAAI,EAAE8D,YAAY;YAClB7D,IAAI,EAAE,OAAO;YACbC,kBAAkB,EAAE;WACrB,CAAC;UAEF;UACA,IAAI,CAACQ,OAAO,CAACsD,KAAK,EAAE;UACpB,MAAMC,UAAU,GAAGpC,QAAQ,CAACC,cAAc,CAAC,QAAQ,CAAqB;UACxE,IAAImC,UAAU,EAAE;YACdA,UAAU,CAAClC,KAAK,EAAE;;QAEtB;OACD,CAAC;KACH,MAAM;MACL7D,IAAI,CAAC4B,IAAI,CAAC;QACRC,KAAK,EAAE,aAAa;QACpBC,IAAI,EAAE,uCAAuC;QAC7CC,IAAI,EAAE,SAAS;QACfC,kBAAkB,EAAE;OACrB,CAAC;;EAEN;EAEAgE,SAASA,CAAA;IACP,IAAI,CAAC,IAAI,CAACxF,IAAI,IAAI,IAAI,CAACK,SAAS,GAAG,CAAC,EAAE;IAEtC,IAAI,CAACO,aAAa,GAAG,IAAI;IACzB,MAAM6E,UAAU,GAAG;MACjBC,MAAM,EAAE,IAAI,CAAC1F,IAAI,CAACwE,GAAG;MACrB,IAAI,IAAI,CAAC3D,KAAK,IAAI;QAAEA,KAAK,EAAE,IAAI,CAACA;MAAK,CAAE;KACxC;IAED,IAAI,CAACJ,WAAW,CAAC+E,SAAS,CAACC,UAAU,CAAC,CAAChB,SAAS,CAAC;MAC/CC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAC/D,aAAa,GAAG,KAAK;QAC1B,IAAI+D,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAID,QAAQ,CAAC9D,KAAK,EAAE;YAClB,IAAI,CAACA,KAAK,GAAG8D,QAAQ,CAAC9D,KAAK;;UAG7BrB,IAAI,CAAC4B,IAAI,CAAC;YACRC,KAAK,EAAE,aAAa;YACpBC,IAAI,EAAE,wCAAwC;YAC9CC,IAAI,EAAE,SAAS;YACfC,kBAAkB,EAAE;WACrB,CAAC;UAEF;UACA,IAAI,CAACI,cAAc,EAAE;UAErB;UACA,IAAI,CAACI,OAAO,CAACsD,KAAK,EAAE;UACpB,MAAMC,UAAU,GAAGpC,QAAQ,CAACC,cAAc,CAAC,QAAQ,CAAqB;UACxE,IAAImC,UAAU,EAAE;YACdA,UAAU,CAAClC,KAAK,EAAE;;;MAGxB,CAAC;MACD6B,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACtE,aAAa,GAAG,KAAK;QAC1BuE,OAAO,CAACD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;QAEzC,MAAME,YAAY,GAAGF,KAAK,CAACA,KAAK,EAAEG,OAAO,IAAI,yCAAyC;QAEtF7F,IAAI,CAAC4B,IAAI,CAAC;UACRC,KAAK,EAAE,eAAe;UACtBC,IAAI,EAAE8D,YAAY;UAClB7D,IAAI,EAAE,OAAO;UACbC,kBAAkB,EAAE;SACrB,CAAC;MACJ;KACD,CAAC;EACJ;EAEAmE,MAAMA,CAAA;IACJ,IAAI,CAACjF,MAAM,CAACgB,QAAQ,CAAC,CAAC,uBAAuB,CAAC,CAAC;EACjD;EAAC,QAAAkE,CAAA,G;qBApOUtF,kBAAkB,EAAAb,EAAA,CAAAoG,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtG,EAAA,CAAAoG,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAxG,EAAA,CAAAoG,iBAAA,CAAAK,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAlB9F,kBAAkB;IAAA+F,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCX/BlH,EAAA,CAAAC,cAAA,aAA6B;QAKQD,EAAA,CAAAS,SAAA,aAA6C;QAACT,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAChFH,EAAA,CAAAC,cAAA,aAAqD;QACjDD,EAAA,CAAAS,SAAA,WAAqD;QACzDT,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,aAAiB;QAAAD,EAAA,CAAAE,MAAA,+BAAuB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC7CH,EAAA,CAAAoH,UAAA,KAAAC,gCAAA,eAA4F;QAC5FrH,EAAA,CAAAC,cAAA,aAAsB;QAAAD,EAAA,CAAAE,MAAA,4DAAoD;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAGlFH,EAAA,CAAAC,cAAA,gBAAqD;QAAzBD,EAAA,CAAAsH,UAAA,sBAAAC,sDAAA;UAAA,OAAYJ,GAAA,CAAAvC,SAAA,EAAW;QAAA,EAAC;QAEhD5E,EAAA,CAAAC,cAAA,eAAgC;QAOxBD,EAAA,CAAAsH,UAAA,mBAAAE,oDAAAC,MAAA;UAAA,OAASN,GAAA,CAAAjE,YAAA,CAAAuE,MAAA,EAAqB,QAAQ,CAAC;QAAA,EAAC,qBAAAC,sDAAAD,MAAA;UAAA,OAC7BN,GAAA,CAAAtD,cAAA,CAAA4D,MAAA,EAAuB,EAAE,CAAC;QAAA,EADG,mBAAAE,oDAAAF,MAAA;UAAA,OAE/BN,GAAA,CAAAlD,OAAA,CAAAwD,MAAA,CAAe;QAAA,EAFgB;QAN5CzH,EAAA,CAAAG,YAAA,EAS0F;QAE1FH,EAAA,CAAAC,cAAA,iBAQ0F;QAFtFD,EAAA,CAAAsH,UAAA,mBAAAM,oDAAAH,MAAA;UAAA,OAASN,GAAA,CAAAjE,YAAA,CAAAuE,MAAA,EAAqB,QAAQ,CAAC;QAAA,EAAC,qBAAAI,sDAAAJ,MAAA;UAAA,OAC7BN,GAAA,CAAAtD,cAAA,CAAA4D,MAAA,EAAuB,QAAQ,CAAC;QAAA,EADH;QAN5CzH,EAAA,CAAAG,YAAA,EAQ0F;QAE1FH,EAAA,CAAAC,cAAA,iBAQ0F;QAFtFD,EAAA,CAAAsH,UAAA,mBAAAQ,oDAAAL,MAAA;UAAA,OAASN,GAAA,CAAAjE,YAAA,CAAAuE,MAAA,EAAqB,QAAQ,CAAC;QAAA,EAAC,qBAAAM,sDAAAN,MAAA;UAAA,OAC7BN,GAAA,CAAAtD,cAAA,CAAA4D,MAAA,EAAuB,QAAQ,CAAC;QAAA,EADH;QAN5CzH,EAAA,CAAAG,YAAA,EAQ0F;QAE1FH,EAAA,CAAAC,cAAA,iBAQ0F;QAFtFD,EAAA,CAAAsH,UAAA,mBAAAU,oDAAAP,MAAA;UAAA,OAASN,GAAA,CAAAjE,YAAA,CAAAuE,MAAA,EAAqB,QAAQ,CAAC;QAAA,EAAC,qBAAAQ,sDAAAR,MAAA;UAAA,OAC7BN,GAAA,CAAAtD,cAAA,CAAA4D,MAAA,EAAuB,QAAQ,CAAC;QAAA,EADH;QAN5CzH,EAAA,CAAAG,YAAA,EAQ0F;QAE1FH,EAAA,CAAAC,cAAA,iBAQ0F;QAFtFD,EAAA,CAAAsH,UAAA,mBAAAY,oDAAAT,MAAA;UAAA,OAASN,GAAA,CAAAjE,YAAA,CAAAuE,MAAA,EAAqB,QAAQ,CAAC;QAAA,EAAC,qBAAAU,sDAAAV,MAAA;UAAA,OAC7BN,GAAA,CAAAtD,cAAA,CAAA4D,MAAA,EAAuB,QAAQ,CAAC;QAAA,EADH;QAN5CzH,EAAA,CAAAG,YAAA,EAQ0F;QAE1FH,EAAA,CAAAC,cAAA,iBAQ0F;QAFtFD,EAAA,CAAAsH,UAAA,mBAAAc,oDAAAX,MAAA;UAAA,OAASN,GAAA,CAAAjE,YAAA,CAAAuE,MAAA,EAAqB,EAAE,CAAC;QAAA,EAAC,qBAAAY,sDAAAZ,MAAA;UAAA,OACvBN,GAAA,CAAAtD,cAAA,CAAA4D,MAAA,EAAuB,QAAQ,CAAC;QAAA,EADT;QANtCzH,EAAA,CAAAG,YAAA,EAQ0F;QAG9FH,EAAA,CAAAC,cAAA,eAA0B;QAKlBD,EAAA,CAAAoH,UAAA,KAAAkB,mCAAA,mBAA4G;QAC5GtI,EAAA,CAAAE,MAAA,IACJ;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAIbH,EAAA,CAAAC,cAAA,eAA8B;QACVD,EAAA,CAAAE,MAAA,gCAAwB;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAC5CH,EAAA,CAAAC,cAAA,kBAIgD;QAD5CD,EAAA,CAAAsH,UAAA,mBAAAiB,qDAAA;UAAA,OAASpB,GAAA,CAAApB,SAAA,EAAW;QAAA,EAAC;QAErB/F,EAAA,CAAAoH,UAAA,KAAAoB,mCAAA,mBAAkH;QAClHxI,EAAA,CAAAoH,UAAA,KAAAqB,mCAAA,kBAA6D;QAC7DzI,EAAA,CAAAoH,UAAA,KAAAsB,mCAAA,kBAAgE;QAChE1I,EAAA,CAAAoH,UAAA,KAAAuB,mCAAA,kBAA6C;QACjD3I,EAAA,CAAAG,YAAA,EAAS;QAGbH,EAAA,CAAAC,cAAA,aAA4B;QACxBD,EAAA,CAAAS,SAAA,aAAmD;QAACT,EAAA,CAAAE,MAAA,gBACpD;QAAAF,EAAA,CAAAC,cAAA,aAA+C;QAA5CD,EAAA,CAAAsH,UAAA,mBAAAsB,gDAAA;UAAA,OAASzB,GAAA,CAAAjB,MAAA,EAAQ;QAAA,EAAC;QAA0BlG,EAAA,CAAAE,MAAA,+BAAuB;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAK1FH,EAAA,CAAAC,cAAA,eAAgG;QAGnED,EAAA,CAAAE,MAAA,iCAAyB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC/CH,EAAA,CAAAC,cAAA,YAAgB;QAAAD,EAAA,CAAAE,MAAA,qCAA6B;QAAAF,EAAA,CAAAG,YAAA,EAAI;QACjDH,EAAA,CAAAC,cAAA,kBAAkC;QAAAD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAC,cAAA,gBAA2B;QAAAD,EAAA,CAAAE,MAAA,wCAAgC;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAGvHH,EAAA,CAAAC,cAAA,eAA6B;QACzBD,EAAA,CAAAS,SAAA,eAA+G;QACnHT,EAAA,CAAAG,YAAA,EAAM;;;;;;;;;QA/GMH,EAAA,CAAAI,SAAA,IAAU;QAAVJ,EAAA,CAAA6I,UAAA,SAAA1B,GAAA,CAAA5G,IAAA,CAAU;QAIZP,EAAA,CAAAI,SAAA,GAAqB;QAArBJ,EAAA,CAAA6I,UAAA,cAAA1B,GAAA,CAAA5E,OAAA,CAAqB;QAYfvC,EAAA,CAAAI,SAAA,GAAqF;QAArFJ,EAAA,CAAA8I,WAAA,iBAAAC,OAAA,GAAA5B,GAAA,CAAA5E,OAAA,CAAAyG,GAAA,6BAAAD,OAAA,CAAAE,OAAA,OAAAF,OAAA,GAAA5B,GAAA,CAAA5E,OAAA,CAAAyG,GAAA,6BAAAD,OAAA,CAAAG,OAAA,EAAqF;QAUrFlJ,EAAA,CAAAI,SAAA,GAAqF;QAArFJ,EAAA,CAAA8I,WAAA,iBAAAK,OAAA,GAAAhC,GAAA,CAAA5E,OAAA,CAAAyG,GAAA,6BAAAG,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAAhC,GAAA,CAAA5E,OAAA,CAAAyG,GAAA,6BAAAG,OAAA,CAAAD,OAAA,EAAqF;QAUrFlJ,EAAA,CAAAI,SAAA,GAAqF;QAArFJ,EAAA,CAAA8I,WAAA,iBAAAM,OAAA,GAAAjC,GAAA,CAAA5E,OAAA,CAAAyG,GAAA,6BAAAI,OAAA,CAAAH,OAAA,OAAAG,OAAA,GAAAjC,GAAA,CAAA5E,OAAA,CAAAyG,GAAA,6BAAAI,OAAA,CAAAF,OAAA,EAAqF;QAUrFlJ,EAAA,CAAAI,SAAA,GAAqF;QAArFJ,EAAA,CAAA8I,WAAA,iBAAAO,OAAA,GAAAlC,GAAA,CAAA5E,OAAA,CAAAyG,GAAA,6BAAAK,OAAA,CAAAJ,OAAA,OAAAI,OAAA,GAAAlC,GAAA,CAAA5E,OAAA,CAAAyG,GAAA,6BAAAK,OAAA,CAAAH,OAAA,EAAqF;QAUrFlJ,EAAA,CAAAI,SAAA,GAAqF;QAArFJ,EAAA,CAAA8I,WAAA,iBAAAQ,OAAA,GAAAnC,GAAA,CAAA5E,OAAA,CAAAyG,GAAA,6BAAAM,OAAA,CAAAL,OAAA,OAAAK,OAAA,GAAAnC,GAAA,CAAA5E,OAAA,CAAAyG,GAAA,6BAAAM,OAAA,CAAAJ,OAAA,EAAqF;QAUrFlJ,EAAA,CAAAI,SAAA,GAAqF;QAArFJ,EAAA,CAAA8I,WAAA,iBAAAS,OAAA,GAAApC,GAAA,CAAA5E,OAAA,CAAAyG,GAAA,6BAAAO,OAAA,CAAAN,OAAA,OAAAM,OAAA,GAAApC,GAAA,CAAA5E,OAAA,CAAAyG,GAAA,6BAAAO,OAAA,CAAAL,OAAA,EAAqF;QAOrFlJ,EAAA,CAAAI,SAAA,GAAuC;QAAvCJ,EAAA,CAAA6I,UAAA,aAAA1B,GAAA,CAAAjG,OAAA,IAAAiG,GAAA,CAAA5E,OAAA,CAAA0G,OAAA,CAAuC;QAChCjJ,EAAA,CAAAI,SAAA,GAAa;QAAbJ,EAAA,CAAA6I,UAAA,SAAA1B,GAAA,CAAAjG,OAAA,CAAa;QACpBlB,EAAA,CAAAI,SAAA,GACJ;QADIJ,EAAA,CAAAU,kBAAA,MAAAyG,GAAA,CAAAjG,OAAA,sCACJ;QAUIlB,EAAA,CAAAI,SAAA,GAA2C;QAA3CJ,EAAA,CAAA6I,UAAA,aAAA1B,GAAA,CAAAhG,aAAA,IAAAgG,GAAA,CAAAvG,SAAA,KAA2C;QACpCZ,EAAA,CAAAI,SAAA,GAAmB;QAAnBJ,EAAA,CAAA6I,UAAA,SAAA1B,GAAA,CAAAhG,aAAA,CAAmB;QACnBnB,EAAA,CAAAI,SAAA,GAAmB;QAAnBJ,EAAA,CAAA6I,UAAA,SAAA1B,GAAA,CAAAvG,SAAA,KAAmB;QACnBZ,EAAA,CAAAI,SAAA,GAAsC;QAAtCJ,EAAA,CAAA6I,UAAA,SAAA1B,GAAA,CAAAvG,SAAA,UAAAuG,GAAA,CAAAhG,aAAA,CAAsC;QACtCnB,EAAA,CAAAI,SAAA,GAAmB;QAAnBJ,EAAA,CAAA6I,UAAA,SAAA1B,GAAA,CAAAhG,aAAA,CAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}