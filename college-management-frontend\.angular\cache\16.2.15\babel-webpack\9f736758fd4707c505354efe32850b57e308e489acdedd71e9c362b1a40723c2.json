{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/checkbox\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/forms\";\nfunction TeacherStudentsComponent_tr_62_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 28);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 29);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 30);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 31);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 31);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\")(14, \"mat-checkbox\", 32);\n    i0.ɵɵlistener(\"change\", function TeacherStudentsComponent_tr_62_Template_mat_checkbox_change_14_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const student_r1 = restoredCtx.$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.toggleStatus(student_r1, $event.checked));\n    });\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const student_r1 = ctx.$implicit;\n    const i_r2 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i_r2 + 1, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(student_r1.rollNumber);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(student_r1.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(student_r1.department);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", student_r1.class, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", student_r1.section, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", student_r1.status === \"present\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", student_r1.status === \"present\" ? \"Present\" : \"Absent\", \" \");\n  }\n}\nexport let TeacherStudentsComponent = /*#__PURE__*/(() => {\n  class TeacherStudentsComponent {\n    constructor(route) {\n      this.route = route;\n      this.searchQuery = '';\n      // Dropdown options\n      this.departments = ['Pre-Medical', 'Pre-Engineering', 'Computer Science'];\n      this.classes = ['1st Year', '2nd Year'];\n      this.sections = ['A', 'B', 'C'];\n      // Selected filters\n      this.selectedClass = '';\n      this.selectedDepartment = '';\n      this.selectedSection = '';\n      this.students = [];\n      this.generateStudentList();\n    }\n    ngOnInit() {\n      // Retrieve query params from the route\n      this.route.queryParams.subscribe(params => {\n        this.selectedClass = params['class'];\n        this.selectedDepartment = params['department'];\n        this.selectedSection = params['section'];\n      });\n    }\n    // Generate a list of 100 students\n    generateStudentList() {\n      const firstNames = ['John', 'Jane', 'Alice', 'Bob', 'Mary', 'Steve', 'Sarah', 'Michael', 'David', 'Linda'];\n      const lastNames = ['Doe', 'Smith', 'Johnson', 'Lee', 'Williams', 'Brown', 'Davis', 'Miller', 'Wilson', 'Moore'];\n      let counter = 1;\n      for (let department of this.departments) {\n        for (let className of this.classes) {\n          for (let section of this.sections) {\n            for (let i = 0; i < 10; i++) {\n              this.students.push({\n                rollNumber: `R${counter++}`,\n                name: `${firstNames[i % 10]} ${lastNames[i % 10]}`,\n                department: department,\n                class: className,\n                section: section,\n                status: 'present'\n              });\n            }\n          }\n        }\n      }\n    }\n    // Filter students based on search query, selected class, department, and section\n    get filteredStudents() {\n      return this.students.filter(student => {\n        const searchLower = this.searchQuery.toLowerCase();\n        const matchesName = student.name.toLowerCase().includes(searchLower);\n        const matchesRollNumber = student.rollNumber.toLowerCase().includes(searchLower);\n        const matchesStatus = student.status.toLowerCase().includes(searchLower);\n        // Filter based on selected class, department, and section\n        const matchesClass = student.class === this.selectedClass;\n        const matchesDepartment = student.department === this.selectedDepartment;\n        const matchesSection = student.section === this.selectedSection;\n        // Search should match either name, roll number, or status\n        return (matchesName || matchesRollNumber || matchesStatus) && matchesClass && matchesDepartment && matchesSection;\n      });\n    }\n    // Toggle status based on checkbox\n    toggleStatus(student, isChecked) {\n      student.status = isChecked ? 'present' : 'absent';\n    }\n    static #_ = this.ɵfac = function TeacherStudentsComponent_Factory(t) {\n      return new (t || TeacherStudentsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TeacherStudentsComponent,\n      selectors: [[\"app-teacher-students\"]],\n      decls: 77,\n      vars: 5,\n      consts: [[1, \"container\"], [1, \"form-group\", \"row\", \"mb-3\"], [1, \"col-lg-4\", \"col-md-4\"], [\"for\", \"classSelect\", 1, \"mb-2\"], [\"for\", \"departmentSelect\", 1, \"mb-2\"], [\"for\", \"sectionSelect\", 1, \"mb-2\"], [1, \"col-lg-6\", \"col-md-12\"], [\"for\", \"date\", 1, \"mb-2\"], [\"type\", \"date\", \"name\", \"\", \"id\", \"\", 1, \"form-control\"], [\"for\", \"subjects\", 1, \"mb-2\"], [\"name\", \"\", \"id\", \"\", 1, \"form-select\"], [\"value\", \"maths\"], [\"value\", \"urdu\"], [\"value\", \"english\"], [\"value\", \"chemistry\"], [\"for\", \"search\", 1, \"mb-2\"], [\"type\", \"text\", \"placeholder\", \"Search by roll number, name or status...\", 1, \"form-control\", \"mb-3\", 3, \"ngModel\", \"ngModelChange\"], [1, \"table-container\", \"mb-4\"], [1, \"table-responsive\"], [1, \"tablehead\"], [1, \"one\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"p-3\", \"align-items-center\", \"justify-content-between\"], [1, \"btn\", \"btn-top\", \"border\"], [1, \"page\"], [1, \"d-flex\", \"justify-content-end\", \"my-4\"], [1, \"btn\", \"save\"], [\"data-label\", \"No\"], [\"data-label\", \"Section\"], [\"data-label\", \"Department\"], [\"data-label\", \"Subjects\"], [\"data-label\", \"Students\"], [3, \"checked\", \"change\"]],\n      template: function TeacherStudentsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\")(2, \"div\")(3, \"h2\");\n          i0.ɵɵtext(4, \"Attendance Form\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(5, \"div\", 1)(6, \"div\", 2)(7, \"label\", 3);\n          i0.ɵɵtext(8, \"Class\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"h1\");\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 2)(12, \"label\", 4);\n          i0.ɵɵtext(13, \"Department\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"h1\");\n          i0.ɵɵtext(15);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 2)(17, \"label\", 5);\n          i0.ɵɵtext(18, \"Section\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"h1\");\n          i0.ɵɵtext(20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"div\", 6)(22, \"label\", 7);\n          i0.ɵɵtext(23, \"Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(24, \"input\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"div\", 6)(26, \"label\", 9);\n          i0.ɵɵtext(27, \"Subjects\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"select\", 10)(29, \"option\", 11);\n          i0.ɵɵtext(30, \"Mathematics\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"option\", 12);\n          i0.ɵɵtext(32, \"Urdu\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"option\", 13);\n          i0.ɵɵtext(34, \"English\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"option\", 14);\n          i0.ɵɵtext(36, \"Chemistry\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(37, \"label\", 15);\n          i0.ɵɵtext(38, \"Search\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"input\", 16);\n          i0.ɵɵlistener(\"ngModelChange\", function TeacherStudentsComponent_Template_input_ngModelChange_39_listener($event) {\n            return ctx.searchQuery = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"div\", 17)(41, \"div\", 18)(42, \"table\")(43, \"thead\")(44, \"tr\", 19)(45, \"th\", 20);\n          i0.ɵɵtext(46, \" S.No \");\n          i0.ɵɵelementStart(47, \"mat-icon\");\n          i0.ɵɵtext(48, \"arrow_downward\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(49, \"th\");\n          i0.ɵɵtext(50, \"Roll Number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"th\");\n          i0.ɵɵtext(52, \"Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"th\");\n          i0.ɵɵtext(54, \"Department\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"th\");\n          i0.ɵɵtext(56, \"Class\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"th\");\n          i0.ɵɵtext(58, \"Section\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"th\");\n          i0.ɵɵtext(60, \"Status\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(61, \"tbody\");\n          i0.ɵɵtemplate(62, TeacherStudentsComponent_tr_62_Template, 16, 8, \"tr\", 21);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(63, \"div\", 22)(64, \"div\")(65, \"button\", 23);\n          i0.ɵɵtext(66, \"Previous\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(67, \"div\", 24);\n          i0.ɵɵtext(68, \"Page 1 of 10\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"div\")(70, \"button\", 23);\n          i0.ɵɵtext(71, \"Next\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(72, \"div\", 25)(73, \"button\", 26)(74, \"mat-icon\");\n          i0.ɵɵtext(75, \"save\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(76, \" save\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate(ctx.selectedClass);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.selectedDepartment);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.selectedSection);\n          i0.ɵɵadvance(19);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance(23);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredStudents);\n        }\n      },\n      dependencies: [i2.NgForOf, i3.MatCheckbox, i4.MatIcon, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel]\n    });\n  }\n  return TeacherStudentsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}