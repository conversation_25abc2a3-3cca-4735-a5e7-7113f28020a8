{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { StudentOverviewComponent } from './student-overview/student-overview.component';\nimport { AttendanceComponent } from './attendance/attendance.component';\nimport { StudentTeachersComponent } from './student-teachers/student-teachers.component';\nimport { StudentNoticeComponent } from './student-notice/student-notice.component';\nimport { StudentComplaintComponent } from './student-complaint/student-complaint.component';\nimport { StudentClassesComponent } from './student-classes/student-classes.component';\nimport { StudentSubjectsComponent } from './student-subjects/student-subjects.component';\nimport { StudentTimetableComponent } from './student-timetable/student-timetable.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: StudentOverviewComponent\n}, {\n  path: 'overview',\n  component: StudentOverviewComponent\n}, {\n  path: 'classes',\n  component: StudentClassesComponent\n}, {\n  path: 'subjects',\n  component: StudentSubjectsComponent\n}, {\n  path: 'timetable',\n  component: StudentTimetableComponent\n}, {\n  path: 'attendance',\n  component: AttendanceComponent\n}, {\n  path: 'teachers',\n  component: StudentTeachersComponent\n}, {\n  path: 'notice',\n  component: StudentNoticeComponent\n}, {\n  path: 'complaint',\n  component: StudentComplaintComponent\n}];\nexport class StudentDashboardRoutingModule {\n  static #_ = this.ɵfac = function StudentDashboardRoutingModule_Factory(t) {\n    return new (t || StudentDashboardRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: StudentDashboardRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild(routes), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(StudentDashboardRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "StudentOverviewComponent", "AttendanceComponent", "StudentTeachersComponent", "StudentNoticeComponent", "StudentComplaintComponent", "StudentClassesComponent", "StudentSubjectsComponent", "StudentTimetableComponent", "routes", "path", "component", "StudentDashboardRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\student-dashboard\\student-dashboard-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { StudentOverviewComponent } from './student-overview/student-overview.component';\r\nimport { AttendanceComponent } from './attendance/attendance.component';\r\nimport { StudentTeachersComponent } from './student-teachers/student-teachers.component';\r\nimport { StudentNoticeComponent } from './student-notice/student-notice.component';\r\nimport { StudentComplaintComponent } from './student-complaint/student-complaint.component';\r\nimport { StudentClassesComponent } from './student-classes/student-classes.component';\r\nimport { StudentSubjectsComponent } from './student-subjects/student-subjects.component';\r\nimport { StudentTimetableComponent } from './student-timetable/student-timetable.component';\r\n\r\nconst routes: Routes = [\r\n  { path: '', component: StudentOverviewComponent },\r\n  { path: 'overview', component: StudentOverviewComponent },\r\n  { path: 'classes', component: StudentClassesComponent },\r\n  { path: 'subjects', component: StudentSubjectsComponent },\r\n  { path: 'timetable', component: StudentTimetableComponent },\r\n  { path: 'attendance', component: AttendanceComponent },\r\n  { path: 'teachers', component: StudentTeachersComponent },\r\n  { path: 'notice', component: StudentNoticeComponent },\r\n  { path: 'complaint', component: StudentComplaintComponent }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class StudentDashboardRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,yBAAyB,QAAQ,iDAAiD;AAC3F,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,yBAAyB,QAAQ,iDAAiD;;;AAE3F,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEV;AAAwB,CAAE,EACjD;EAAES,IAAI,EAAE,UAAU;EAAEC,SAAS,EAAEV;AAAwB,CAAE,EACzD;EAAES,IAAI,EAAE,SAAS;EAAEC,SAAS,EAAEL;AAAuB,CAAE,EACvD;EAAEI,IAAI,EAAE,UAAU;EAAEC,SAAS,EAAEJ;AAAwB,CAAE,EACzD;EAAEG,IAAI,EAAE,WAAW;EAAEC,SAAS,EAAEH;AAAyB,CAAE,EAC3D;EAAEE,IAAI,EAAE,YAAY;EAAEC,SAAS,EAAET;AAAmB,CAAE,EACtD;EAAEQ,IAAI,EAAE,UAAU;EAAEC,SAAS,EAAER;AAAwB,CAAE,EACzD;EAAEO,IAAI,EAAE,QAAQ;EAAEC,SAAS,EAAEP;AAAsB,CAAE,EACrD;EAAEM,IAAI,EAAE,WAAW;EAAEC,SAAS,EAAEN;AAAyB,CAAE,CAC5D;AAMD,OAAM,MAAOO,6BAA6B;EAAA,QAAAC,CAAA,G;qBAA7BD,6BAA6B;EAAA;EAAA,QAAAE,EAAA,G;UAA7BF;EAA6B;EAAA,QAAAG,EAAA,G;cAH9Bf,YAAY,CAACgB,QAAQ,CAACP,MAAM,CAAC,EAC7BT,YAAY;EAAA;;;2EAEXY,6BAA6B;IAAAK,OAAA,GAAAC,EAAA,CAAAlB,YAAA;IAAAmB,OAAA,GAF9BnB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}