{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class DepartmentService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = environment.apiUrl;\n  }\n  // Get all departments with filters\n  getAllDepartments(filters) {\n    let params = new HttpParams();\n    if (filters) {\n      Object.keys(filters).forEach(key => {\n        const value = filters[key];\n        if (value !== undefined && value !== null) {\n          params = params.set(key, value.toString());\n        }\n      });\n    }\n    return this.http.get(`${this.apiUrl}/departments`, {\n      params\n    });\n  }\n  // Get departments by program\n  getDepartmentsByProgram(programId, isActive) {\n    let params = new HttpParams();\n    if (isActive !== undefined) params = params.set('isActive', isActive.toString());\n    return this.http.get(`${this.apiUrl}/departments/program/${programId}`, {\n      params\n    });\n  }\n  // Get department by ID\n  getDepartmentById(id) {\n    return this.http.get(`${this.apiUrl}/department/${id}`);\n  }\n  // Add a new department\n  createDepartment(departmentData) {\n    return this.http.post(`${this.apiUrl}/department`, departmentData);\n  }\n  // Update an existing department\n  updateDepartment(id, departmentData) {\n    return this.http.put(`${this.apiUrl}/department/${id}`, departmentData);\n  }\n  // Delete a department (soft delete)\n  deleteDepartment(id) {\n    return this.http.delete(`${this.apiUrl}/department/${id}`);\n  }\n  // Legacy methods for backward compatibility\n  getdepartment() {\n    return this.getAllDepartments();\n  }\n  getdepartmentById(id) {\n    return this.getDepartmentById(id);\n  }\n  adddepartment(departmentData) {\n    return this.createDepartment(departmentData);\n  }\n  updatedepartment(id, departmentData) {\n    return this.updateDepartment(id, departmentData);\n  }\n  deletedepartment(id) {\n    return this.deleteDepartment(id);\n  }\n  static #_ = this.ɵfac = function DepartmentService_Factory(t) {\n    return new (t || DepartmentService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: DepartmentService,\n    factory: DepartmentService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpParams", "environment", "DepartmentService", "constructor", "http", "apiUrl", "getAllDepartments", "filters", "params", "Object", "keys", "for<PERSON>ach", "key", "value", "undefined", "set", "toString", "get", "getDepartmentsByProgram", "programId", "isActive", "getDepartmentById", "id", "createDepartment", "departmentData", "post", "updateDepartment", "put", "deleteDepartment", "delete", "getdepartment", "getdepartmentById", "adddepartment", "updatedepartment", "deletedepartment", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\services\\department.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { environment } from 'src/environments/environment';\r\nimport { Department } from '../models/user';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class DepartmentService {\r\n  private apiUrl = environment.apiUrl;\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  // Get all departments with filters\r\n  getAllDepartments(filters?: {\r\n    program?: string;\r\n    isActive?: boolean;\r\n  }): Observable<any> {\r\n    let params = new HttpParams();\r\n\r\n    if (filters) {\r\n      Object.keys(filters).forEach(key => {\r\n        const value = (filters as any)[key];\r\n        if (value !== undefined && value !== null) {\r\n          params = params.set(key, value.toString());\r\n        }\r\n      });\r\n    }\r\n\r\n    return this.http.get<any>(`${this.apiUrl}/departments`, { params });\r\n  }\r\n\r\n  // Get departments by program\r\n  getDepartmentsByProgram(programId: string, isActive?: boolean): Observable<any> {\r\n    let params = new HttpParams();\r\n    if (isActive !== undefined) params = params.set('isActive', isActive.toString());\r\n\r\n    return this.http.get<any>(`${this.apiUrl}/departments/program/${programId}`, { params });\r\n  }\r\n\r\n  // Get department by ID\r\n  getDepartmentById(id: string): Observable<any> {\r\n    return this.http.get<any>(`${this.apiUrl}/department/${id}`);\r\n  }\r\n\r\n  // Add a new department\r\n  createDepartment(departmentData: Partial<Department>): Observable<any> {\r\n    return this.http.post<any>(`${this.apiUrl}/department`, departmentData);\r\n  }\r\n\r\n  // Update an existing department\r\n  updateDepartment(id: string, departmentData: Partial<Department>): Observable<any> {\r\n    return this.http.put<any>(`${this.apiUrl}/department/${id}`, departmentData);\r\n  }\r\n\r\n  // Delete a department (soft delete)\r\n  deleteDepartment(id: string): Observable<any> {\r\n    return this.http.delete<any>(`${this.apiUrl}/department/${id}`);\r\n  }\r\n\r\n  // Legacy methods for backward compatibility\r\n  getdepartment(): Observable<any> {\r\n    return this.getAllDepartments();\r\n  }\r\n\r\n  getdepartmentById(id: string): Observable<any> {\r\n    return this.getDepartmentById(id);\r\n  }\r\n\r\n  adddepartment(departmentData: any): Observable<any> {\r\n    return this.createDepartment(departmentData);\r\n  }\r\n\r\n  updatedepartment(id: string, departmentData: any): Observable<any> {\r\n    return this.updateDepartment(id, departmentData);\r\n  }\r\n\r\n  deletedepartment(id: string): Observable<any> {\r\n    return this.deleteDepartment(id);\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAG7D,SAASC,WAAW,QAAQ,8BAA8B;;;AAM1D,OAAM,MAAOC,iBAAiB;EAG5BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAGJ,WAAW,CAACI,MAAM;EAEI;EAEvC;EACAC,iBAAiBA,CAACC,OAGjB;IACC,IAAIC,MAAM,GAAG,IAAIR,UAAU,EAAE;IAE7B,IAAIO,OAAO,EAAE;MACXE,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC,CAACI,OAAO,CAACC,GAAG,IAAG;QACjC,MAAMC,KAAK,GAAIN,OAAe,CAACK,GAAG,CAAC;QACnC,IAAIC,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,EAAE;UACzCL,MAAM,GAAGA,MAAM,CAACO,GAAG,CAACH,GAAG,EAAEC,KAAK,CAACG,QAAQ,EAAE,CAAC;;MAE9C,CAAC,CAAC;;IAGJ,OAAO,IAAI,CAACZ,IAAI,CAACa,GAAG,CAAM,GAAG,IAAI,CAACZ,MAAM,cAAc,EAAE;MAAEG;IAAM,CAAE,CAAC;EACrE;EAEA;EACAU,uBAAuBA,CAACC,SAAiB,EAAEC,QAAkB;IAC3D,IAAIZ,MAAM,GAAG,IAAIR,UAAU,EAAE;IAC7B,IAAIoB,QAAQ,KAAKN,SAAS,EAAEN,MAAM,GAAGA,MAAM,CAACO,GAAG,CAAC,UAAU,EAAEK,QAAQ,CAACJ,QAAQ,EAAE,CAAC;IAEhF,OAAO,IAAI,CAACZ,IAAI,CAACa,GAAG,CAAM,GAAG,IAAI,CAACZ,MAAM,wBAAwBc,SAAS,EAAE,EAAE;MAAEX;IAAM,CAAE,CAAC;EAC1F;EAEA;EACAa,iBAAiBA,CAACC,EAAU;IAC1B,OAAO,IAAI,CAAClB,IAAI,CAACa,GAAG,CAAM,GAAG,IAAI,CAACZ,MAAM,eAAeiB,EAAE,EAAE,CAAC;EAC9D;EAEA;EACAC,gBAAgBA,CAACC,cAAmC;IAClD,OAAO,IAAI,CAACpB,IAAI,CAACqB,IAAI,CAAM,GAAG,IAAI,CAACpB,MAAM,aAAa,EAAEmB,cAAc,CAAC;EACzE;EAEA;EACAE,gBAAgBA,CAACJ,EAAU,EAAEE,cAAmC;IAC9D,OAAO,IAAI,CAACpB,IAAI,CAACuB,GAAG,CAAM,GAAG,IAAI,CAACtB,MAAM,eAAeiB,EAAE,EAAE,EAAEE,cAAc,CAAC;EAC9E;EAEA;EACAI,gBAAgBA,CAACN,EAAU;IACzB,OAAO,IAAI,CAAClB,IAAI,CAACyB,MAAM,CAAM,GAAG,IAAI,CAACxB,MAAM,eAAeiB,EAAE,EAAE,CAAC;EACjE;EAEA;EACAQ,aAAaA,CAAA;IACX,OAAO,IAAI,CAACxB,iBAAiB,EAAE;EACjC;EAEAyB,iBAAiBA,CAACT,EAAU;IAC1B,OAAO,IAAI,CAACD,iBAAiB,CAACC,EAAE,CAAC;EACnC;EAEAU,aAAaA,CAACR,cAAmB;IAC/B,OAAO,IAAI,CAACD,gBAAgB,CAACC,cAAc,CAAC;EAC9C;EAEAS,gBAAgBA,CAACX,EAAU,EAAEE,cAAmB;IAC9C,OAAO,IAAI,CAACE,gBAAgB,CAACJ,EAAE,EAAEE,cAAc,CAAC;EAClD;EAEAU,gBAAgBA,CAACZ,EAAU;IACzB,OAAO,IAAI,CAACM,gBAAgB,CAACN,EAAE,CAAC;EAClC;EAAC,QAAAa,CAAA,G;qBAvEUjC,iBAAiB,EAAAkC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAjBtC,iBAAiB;IAAAuC,OAAA,EAAjBvC,iBAAiB,CAAAwC,IAAA;IAAAC,UAAA,EAFhB;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}