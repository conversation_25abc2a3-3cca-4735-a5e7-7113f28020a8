{"ast": null, "code": "import Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../../services/user.service\";\nimport * as i3 from \"../../../../services/attendance.service\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/progress-spinner\";\nfunction StudentViewComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function StudentViewComponent_div_13_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.editStudent());\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Edit Student \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StudentViewComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading student details...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StudentViewComponent_div_15_mat_card_content_90_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelement(1, \"mat-spinner\", 28);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading attendance data...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StudentViewComponent_div_15_mat_card_content_90_div_2_div_24_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const record_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", record_r12.lateClasses, \" Late\");\n  }\n}\nfunction StudentViewComponent_div_15_mat_card_content_90_div_2_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"div\", 40)(2, \"div\", 41)(3, \"h5\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 42);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 43);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 44)(10, \"div\", 45)(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 46)(16, \"mat-icon\");\n    i0.ɵɵtext(17, \"cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\");\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(20, StudentViewComponent_div_15_mat_card_content_90_div_2_div_24_div_20_Template, 5, 1, \"div\", 47);\n    i0.ɵɵelementStart(21, \"div\", 48)(22, \"mat-icon\");\n    i0.ɵɵtext(23, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"span\");\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const record_r12 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r10.getAttendanceClass(record_r12.attendancePercentage));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(record_r12.subject.subjectName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(record_r12.subject.code);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r10.getAttendanceClass(record_r12.attendancePercentage));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", record_r12.attendancePercentage, \"% \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", record_r12.presentClasses, \" Present\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", record_r12.absentClasses, \" Absent\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", record_r12.lateClasses > 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", record_r12.totalClasses, \" Total\");\n  }\n}\nfunction StudentViewComponent_div_15_mat_card_content_90_div_2_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function StudentViewComponent_div_15_mat_card_content_90_div_2_div_25_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r15.changePage(ctx_r15.currentPage - 1));\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"chevron_left\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"span\", 52);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function StudentViewComponent_div_15_mat_card_content_90_div_2_div_25_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r17 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r17.changePage(ctx_r17.currentPage + 1));\n    });\n    i0.ɵɵelementStart(7, \"mat-icon\");\n    i0.ɵɵtext(8, \"chevron_right\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r11.currentPage === 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"Page \", ctx_r11.currentPage, \" of \", ctx_r11.totalPages, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r11.currentPage === ctx_r11.totalPages);\n  }\n}\nfunction StudentViewComponent_div_15_mat_card_content_90_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 30)(2, \"div\", 31)(3, \"div\", 32)(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"assessment\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 33)(7, \"h3\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\");\n    i0.ɵɵtext(10, \"Overall Attendance\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 34)(12, \"div\", 32)(13, \"mat-icon\");\n    i0.ɵɵtext(14, \"subject\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 33)(16, \"h3\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"p\");\n    i0.ɵɵtext(19, \"Total Subjects\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(20, \"div\", 35)(21, \"h4\");\n    i0.ɵɵtext(22, \"Subject-wise Attendance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 36);\n    i0.ɵɵtemplate(24, StudentViewComponent_div_15_mat_card_content_90_div_2_div_24_Template, 26, 9, \"div\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, StudentViewComponent_div_15_mat_card_content_90_div_2_div_25_Template, 9, 4, \"div\", 38);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r8.overallAttendance, \"%\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r8.totalSubjects);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.paginatedRecords);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.totalPages > 1);\n  }\n}\nfunction StudentViewComponent_div_15_mat_card_content_90_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"mat-icon\", 54);\n    i0.ɵɵtext(2, \"event_busy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"No Attendance Records\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"No attendance data found for this student.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StudentViewComponent_div_15_mat_card_content_90_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card-content\");\n    i0.ɵɵtemplate(1, StudentViewComponent_div_15_mat_card_content_90_div_1_Template, 4, 0, \"div\", 24);\n    i0.ɵɵtemplate(2, StudentViewComponent_div_15_mat_card_content_90_div_2_Template, 26, 4, \"div\", 25);\n    i0.ɵɵtemplate(3, StudentViewComponent_div_15_mat_card_content_90_div_3_Template, 7, 0, \"div\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.attendanceLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.attendanceLoading && ctx_r6.attendanceRecords.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.attendanceLoading && ctx_r6.attendanceRecords.length === 0);\n  }\n}\nfunction StudentViewComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"mat-card\", 16)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"account_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" Basic Information \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"div\", 17)(9, \"div\", 18)(10, \"label\");\n    i0.ɵɵtext(11, \"Full Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"h3\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 18)(15, \"label\");\n    i0.ɵɵtext(16, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 18)(20, \"label\");\n    i0.ɵɵtext(21, \"Roll Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"h3\");\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 18)(25, \"label\");\n    i0.ɵɵtext(26, \"Registration Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"p\");\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 18)(30, \"label\");\n    i0.ɵɵtext(31, \"Contact\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"p\");\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 18)(35, \"label\");\n    i0.ɵɵtext(36, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"span\", 19);\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(39, \"mat-card\", 16)(40, \"mat-card-header\")(41, \"mat-card-title\")(42, \"mat-icon\");\n    i0.ɵɵtext(43, \"school\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(44, \" Academic Information \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"mat-card-content\")(46, \"div\", 17)(47, \"div\", 18)(48, \"label\");\n    i0.ɵɵtext(49, \"Program\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"h3\");\n    i0.ɵɵtext(51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"small\");\n    i0.ɵɵtext(53);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(54, \"div\", 18)(55, \"label\");\n    i0.ɵɵtext(56, \"Department\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"h3\");\n    i0.ɵɵtext(58);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(59, \"div\", 18)(60, \"label\");\n    i0.ɵɵtext(61, \"Class\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(62, \"h3\");\n    i0.ɵɵtext(63);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(64, \"div\", 18)(65, \"label\");\n    i0.ɵɵtext(66, \"Section\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(67, \"h3\");\n    i0.ɵɵtext(68);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(69, \"div\", 18)(70, \"label\");\n    i0.ɵɵtext(71, \"Semester\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(72, \"h3\");\n    i0.ɵɵtext(73);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(74, \"div\", 18)(75, \"label\");\n    i0.ɵɵtext(76, \"Academic Year\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(77, \"p\");\n    i0.ɵɵtext(78);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(79, \"mat-card\", 20)(80, \"mat-card-header\")(81, \"mat-card-title\")(82, \"mat-icon\");\n    i0.ɵɵtext(83, \"access_time\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(84, \" Attendance Records \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(85, \"div\", 21)(86, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function StudentViewComponent_div_15_Template_button_click_86_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.toggleAttendanceDetails());\n    });\n    i0.ɵɵelementStart(87, \"mat-icon\");\n    i0.ɵɵtext(88);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(89);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(90, StudentViewComponent_div_15_mat_card_content_90_Template, 4, 3, \"mat-card-content\", 23);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate(ctx_r2.student.name);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.student.email);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.student.rollNo || \"N/A\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.student.regNo || \"N/A\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.student.contact || \"N/A\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.student.isActive ? \"active\" : \"inactive\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.student.isActive ? \"Active\" : \"Inactive\", \" \");\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate((ctx_r2.student.program == null ? null : ctx_r2.student.program.name) || \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((ctx_r2.student.program == null ? null : ctx_r2.student.program.fullName) || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r2.student.department == null ? null : ctx_r2.student.department.name) || \"N/A\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getClassName(ctx_r2.student));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getClassSection(ctx_r2.student) || \"N/A\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.student.semester || \"N/A\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.student.academicYear || \"N/A\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r2.showAttendanceDetails ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.showAttendanceDetails ? \"Hide\" : \"View\", \" Attendance \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showAttendanceDetails);\n  }\n}\nfunction StudentViewComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"mat-icon\", 56);\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Student Not Found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"The requested student could not be found.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function StudentViewComponent_div_16_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.goBack());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"arrow_back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Back to Students \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class StudentViewComponent {\n  constructor(route, router, userService, attendanceService, snackBar) {\n    this.route = route;\n    this.router = router;\n    this.userService = userService;\n    this.attendanceService = attendanceService;\n    this.snackBar = snackBar;\n    this.student = null;\n    this.studentId = null;\n    this.loading = false;\n    this.attendanceLoading = false;\n    // Attendance data\n    this.attendanceRecords = [];\n    this.overallAttendance = 0;\n    this.totalSubjects = 0;\n    // View toggles\n    this.showAttendanceDetails = false;\n    this.selectedAttendanceView = 'daily'; // daily, monthly, yearly\n    // Pagination for attendance\n    this.currentPage = 1;\n    this.itemsPerPage = 10;\n  }\n  ngOnInit() {\n    this.studentId = this.route.snapshot.paramMap.get('id');\n    if (this.studentId) {\n      this.loadStudentDetails();\n    } else {\n      Swal.fire('Error', 'Student ID not found', 'error');\n      this.router.navigate(['/dashboard/admin/students']);\n    }\n  }\n  loadStudentDetails() {\n    if (!this.studentId) return;\n    this.loading = true;\n    this.userService.getUserProfile(this.studentId).subscribe({\n      next: response => {\n        this.loading = false;\n        if (response.success) {\n          this.student = response.user;\n        } else {\n          Swal.fire('Error', 'Failed to load student details', 'error');\n          this.router.navigate(['/dashboard/admin/students']);\n        }\n      },\n      error: error => {\n        this.loading = false;\n        console.error('Error loading student details:', error);\n        Swal.fire('Error', 'Failed to load student details', 'error');\n        this.router.navigate(['/dashboard/admin/students']);\n      }\n    });\n  }\n  loadStudentAttendance() {\n    if (!this.studentId) return;\n    this.attendanceLoading = true;\n    this.attendanceService.getStudentAttendance(this.studentId).subscribe({\n      next: response => {\n        this.attendanceLoading = false;\n        if (response.success) {\n          this.processAttendanceData(response.attendance);\n          this.calculateOverallAttendance();\n        } else {\n          this.snackBar.open('Failed to load attendance data', 'Close', {\n            duration: 3000\n          });\n        }\n      },\n      error: error => {\n        this.attendanceLoading = false;\n        console.error('Error loading attendance:', error);\n        this.snackBar.open('Error loading attendance data', 'Close', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  processAttendanceData(attendanceData) {\n    // Group attendance by subject\n    const subjectMap = new Map();\n    attendanceData.forEach(record => {\n      const subjectId = record.subject._id;\n      if (!subjectMap.has(subjectId)) {\n        subjectMap.set(subjectId, {\n          subject: record.subject,\n          class: record.class,\n          totalClasses: 0,\n          presentClasses: 0,\n          absentClasses: 0,\n          lateClasses: 0,\n          attendancePercentage: 0,\n          records: []\n        });\n      }\n      const subjectData = subjectMap.get(subjectId);\n      subjectData.totalClasses++;\n      subjectData.records.push(record);\n      if (record.status === 'present') {\n        subjectData.presentClasses++;\n      } else if (record.status === 'absent') {\n        subjectData.absentClasses++;\n      } else if (record.status === 'late') {\n        subjectData.lateClasses++;\n        subjectData.presentClasses++; // Late is considered present\n      }\n      // Calculate percentage\n      subjectData.attendancePercentage = Math.round(subjectData.presentClasses / subjectData.totalClasses * 100);\n    });\n    this.attendanceRecords = Array.from(subjectMap.values());\n    this.totalSubjects = this.attendanceRecords.length;\n  }\n  calculateOverallAttendance() {\n    if (this.attendanceRecords.length === 0) {\n      this.overallAttendance = 0;\n      return;\n    }\n    const totalPercentage = this.attendanceRecords.reduce((sum, record) => sum + record.attendancePercentage, 0);\n    this.overallAttendance = Math.round(totalPercentage / this.attendanceRecords.length);\n  }\n  toggleAttendanceDetails() {\n    this.showAttendanceDetails = !this.showAttendanceDetails;\n    if (this.showAttendanceDetails && this.attendanceRecords.length === 0) {\n      this.loadStudentAttendance();\n    }\n  }\n  editStudent() {\n    if (this.studentId) {\n      this.router.navigate(['/dashboard/admin/students/edit', this.studentId]);\n    }\n  }\n  goBack() {\n    this.router.navigate(['/dashboard/admin/students']);\n  }\n  getAttendanceClass(percentage) {\n    if (percentage >= 85) return 'excellent';\n    if (percentage >= 75) return 'good';\n    if (percentage >= 65) return 'average';\n    return 'poor';\n  }\n  getClassSection(student) {\n    if (student?.classId && typeof student.classId === 'object') {\n      return student.classId.section || '';\n    }\n    return '';\n  }\n  getClassName(student) {\n    if (student?.classId && typeof student.classId === 'object') {\n      return student.classId.className || 'N/A';\n    }\n    return 'N/A';\n  }\n  get paginatedRecords() {\n    const startIndex = (this.currentPage - 1) * this.itemsPerPage;\n    return this.attendanceRecords.slice(startIndex, startIndex + this.itemsPerPage);\n  }\n  get totalPages() {\n    return Math.ceil(this.attendanceRecords.length / this.itemsPerPage);\n  }\n  changePage(page) {\n    if (page >= 1 && page <= this.totalPages) {\n      this.currentPage = page;\n    }\n  }\n  static #_ = this.ɵfac = function StudentViewComponent_Factory(t) {\n    return new (t || StudentViewComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.AttendanceService), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: StudentViewComponent,\n    selectors: [[\"app-student-view\"]],\n    decls: 17,\n    vars: 4,\n    consts: [[1, \"student-view-container\"], [1, \"page-header\"], [1, \"header-content\"], [\"mat-icon-button\", \"\", 1, \"back-btn\", 3, \"click\"], [1, \"title-section\"], [1, \"page-title\"], [1, \"title-icon\"], [1, \"page-subtitle\"], [\"class\", \"header-actions\", 4, \"ngIf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"student-details\", 4, \"ngIf\"], [\"class\", \"error-state\", 4, \"ngIf\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"loading-container\"], [1, \"student-details\"], [1, \"info-card\"], [1, \"info-grid\"], [1, \"info-item\"], [1, \"status-badge\", 3, \"ngClass\"], [1, \"attendance-card\"], [1, \"card-actions\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", 3, \"click\"], [4, \"ngIf\"], [\"class\", \"loading-state\", 4, \"ngIf\"], [\"class\", \"attendance-content\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"loading-state\"], [\"diameter\", \"40\"], [1, \"attendance-content\"], [1, \"attendance-summary\"], [1, \"summary-item\", \"overall\"], [1, \"summary-icon\"], [1, \"summary-info\"], [1, \"summary-item\", \"subjects\"], [1, \"attendance-records\"], [1, \"records-grid\"], [\"class\", \"attendance-record\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"pagination-container\", 4, \"ngIf\"], [1, \"attendance-record\", 3, \"ngClass\"], [1, \"record-header\"], [1, \"subject-info\"], [1, \"subject-code\"], [1, \"attendance-percentage\", 3, \"ngClass\"], [1, \"record-stats\"], [1, \"stat-item\", \"present\"], [1, \"stat-item\", \"absent\"], [\"class\", \"stat-item late\", 4, \"ngIf\"], [1, \"stat-item\", \"total\"], [1, \"stat-item\", \"late\"], [1, \"pagination-container\"], [\"mat-icon-button\", \"\", 3, \"disabled\", \"click\"], [1, \"page-info\"], [1, \"empty-state\"], [1, \"empty-icon\"], [1, \"error-state\"], [1, \"error-icon\"]],\n    template: function StudentViewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"button\", 3);\n        i0.ɵɵlistener(\"click\", function StudentViewComponent_Template_button_click_3_listener() {\n          return ctx.goBack();\n        });\n        i0.ɵɵelementStart(4, \"mat-icon\");\n        i0.ɵɵtext(5, \"arrow_back\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(6, \"div\", 4)(7, \"h1\", 5)(8, \"mat-icon\", 6);\n        i0.ɵɵtext(9, \"person\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(10, \" Student Details \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"p\", 7);\n        i0.ɵɵtext(12, \"View comprehensive student information and attendance records\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(13, StudentViewComponent_div_13_Template, 5, 0, \"div\", 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(14, StudentViewComponent_div_14_Template, 4, 0, \"div\", 9);\n        i0.ɵɵtemplate(15, StudentViewComponent_div_15_Template, 91, 17, \"div\", 10);\n        i0.ɵɵtemplate(16, StudentViewComponent_div_16_Template, 11, 0, \"div\", 11);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(13);\n        i0.ɵɵproperty(\"ngIf\", ctx.student);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.student);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.student);\n      }\n    },\n    dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i6.MatButton, i6.MatIconButton, i7.MatCard, i7.MatCardContent, i7.MatCardHeader, i7.MatCardTitle, i8.MatIcon, i9.MatProgressSpinner],\n    styles: [\".student-view-container[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  min-height: 100vh;\\n  padding: 20px;\\n}\\n\\n\\n\\n.page-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 25px;\\n  padding: 15px 0;\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n}\\n\\n.back-btn[_ngcontent-%COMP%] {\\n  background-color: #e9ecef;\\n  color: #495057;\\n}\\n\\n.title-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.page-title[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-size: 1.8rem;\\n  font-weight: 600;\\n  margin: 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n\\n.title-icon[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  color: #3498db;\\n}\\n\\n.page-subtitle[_ngcontent-%COMP%] {\\n  color: #7f8c8d;\\n  margin: 5px 0 0 0;\\n  font-size: 0.9rem;\\n}\\n\\n.header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n}\\n\\n\\n\\n.loading-container[_ngcontent-%COMP%], .error-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 60px 20px;\\n  text-align: center;\\n}\\n\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .error-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n  color: #7f8c8d;\\n}\\n\\n.error-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  color: #e74c3c;\\n  margin-bottom: 20px;\\n}\\n\\n\\n\\n.info-card[_ngcontent-%COMP%], .attendance-card[_ngcontent-%COMP%] {\\n  margin-bottom: 25px;\\n  border-radius: 12px;\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n  border: 1px solid #e9ecef;\\n}\\n\\n.info-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%], .attendance-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  border-radius: 12px 12px 0 0;\\n  padding: 20px;\\n}\\n\\n.info-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%], .attendance-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  margin: 0;\\n}\\n\\n.card-actions[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n}\\n\\n\\n\\n.info-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 20px;\\n  padding: 20px;\\n}\\n\\n.info-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 5px;\\n}\\n\\n.info-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  font-weight: 600;\\n  color: #7f8c8d;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n\\n.info-item[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #2c3e50;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n}\\n\\n.info-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #495057;\\n  font-size: 0.95rem;\\n}\\n\\n.info-item[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 0.8rem;\\n}\\n\\n\\n\\n.status-badge[_ngcontent-%COMP%] {\\n  padding: 4px 12px;\\n  border-radius: 20px;\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\n\\n.status-badge.active[_ngcontent-%COMP%] {\\n  background-color: #d4edda;\\n  color: #155724;\\n}\\n\\n.status-badge.inactive[_ngcontent-%COMP%] {\\n  background-color: #f8d7da;\\n  color: #721c24;\\n}\\n\\n\\n\\n.attendance-content[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n\\n.loading-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  padding: 40px;\\n  gap: 15px;\\n}\\n\\n.attendance-summary[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 30px;\\n}\\n\\n.summary-item[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  padding: 20px;\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  border-left: 4px solid #3498db;\\n}\\n\\n.summary-item.overall[_ngcontent-%COMP%] {\\n  border-left-color: #27ae60;\\n}\\n\\n.summary-item.subjects[_ngcontent-%COMP%] {\\n  border-left-color: #f39c12;\\n}\\n\\n.summary-icon[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n}\\n\\n.summary-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.8rem;\\n  font-weight: 700;\\n  color: #2c3e50;\\n}\\n\\n.summary-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #7f8c8d;\\n  font-size: 0.9rem;\\n}\\n\\n\\n\\n.attendance-records[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  margin-bottom: 20px;\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n}\\n\\n.records-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 20px;\\n}\\n\\n.attendance-record[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  padding: 20px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  border-left: 4px solid #bdc3c7;\\n  transition: transform 0.2s ease;\\n}\\n\\n.attendance-record[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\\n}\\n\\n.attendance-record.excellent[_ngcontent-%COMP%] {\\n  border-left-color: #27ae60;\\n}\\n\\n.attendance-record.good[_ngcontent-%COMP%] {\\n  border-left-color: #f39c12;\\n}\\n\\n.attendance-record.average[_ngcontent-%COMP%] {\\n  border-left-color: #e67e22;\\n}\\n\\n.attendance-record.poor[_ngcontent-%COMP%] {\\n  border-left-color: #e74c3c;\\n}\\n\\n.record-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 15px;\\n}\\n\\n.subject-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #2c3e50;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n}\\n\\n.subject-code[_ngcontent-%COMP%] {\\n  background: #e9ecef;\\n  color: #495057;\\n  padding: 2px 8px;\\n  border-radius: 12px;\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n}\\n\\n.attendance-percentage[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  border-radius: 20px;\\n  font-weight: 700;\\n  font-size: 0.9rem;\\n}\\n\\n.attendance-percentage.excellent[_ngcontent-%COMP%] {\\n  background: #d4edda;\\n  color: #155724;\\n}\\n\\n.attendance-percentage.good[_ngcontent-%COMP%] {\\n  background: #fff3cd;\\n  color: #856404;\\n}\\n\\n.attendance-percentage.average[_ngcontent-%COMP%] {\\n  background: #ffeaa7;\\n  color: #b8860b;\\n}\\n\\n.attendance-percentage.poor[_ngcontent-%COMP%] {\\n  background: #f8d7da;\\n  color: #721c24;\\n}\\n\\n.record-stats[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(2, 1fr);\\n  gap: 10px;\\n}\\n\\n.stat-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 0.85rem;\\n  color: #495057;\\n}\\n\\n.stat-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.stat-item.present[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #27ae60;\\n}\\n\\n.stat-item.absent[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #e74c3c;\\n}\\n\\n.stat-item.late[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #f39c12;\\n}\\n\\n.stat-item.total[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #3498db;\\n}\\n\\n\\n\\n.pagination-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 15px;\\n  margin-top: 20px;\\n}\\n\\n.page-info[_ngcontent-%COMP%] {\\n  color: #7f8c8d;\\n  font-size: 0.9rem;\\n}\\n\\n\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px 20px;\\n}\\n\\n.empty-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  color: #bdc3c7;\\n  margin-bottom: 20px;\\n}\\n\\n.empty-state[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  margin-bottom: 10px;\\n}\\n\\n.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #7f8c8d;\\n  margin-bottom: 20px;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .student-view-container[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n\\n  .page-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 15px;\\n  }\\n\\n  .header-actions[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: flex-end;\\n  }\\n\\n  .info-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 15px;\\n    padding: 15px;\\n  }\\n\\n  .attendance-summary[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 15px;\\n  }\\n\\n  .records-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 15px;\\n  }\\n\\n  .record-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 10px;\\n  }\\n\\n  .record-stats[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 8px;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵlistener", "StudentViewComponent_div_13_Template_button_click_1_listener", "ɵɵrestoreView", "_r5", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "editStudent", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate1", "record_r12", "lateClasses", "ɵɵtemplate", "StudentViewComponent_div_15_mat_card_content_90_div_2_div_24_div_20_Template", "ɵɵproperty", "ctx_r10", "getAttendanceClass", "attendancePercentage", "ɵɵtextInterpolate", "subject", "subjectName", "code", "presentClasses", "absentClasses", "totalClasses", "StudentViewComponent_div_15_mat_card_content_90_div_2_div_25_Template_button_click_1_listener", "_r16", "ctx_r15", "changePage", "currentPage", "StudentViewComponent_div_15_mat_card_content_90_div_2_div_25_Template_button_click_6_listener", "ctx_r17", "ctx_r11", "ɵɵtextInterpolate2", "totalPages", "StudentViewComponent_div_15_mat_card_content_90_div_2_div_24_Template", "StudentViewComponent_div_15_mat_card_content_90_div_2_div_25_Template", "ctx_r8", "overallAttendance", "totalSubjects", "paginatedRecords", "StudentViewComponent_div_15_mat_card_content_90_div_1_Template", "StudentViewComponent_div_15_mat_card_content_90_div_2_Template", "StudentViewComponent_div_15_mat_card_content_90_div_3_Template", "ctx_r6", "attendanceLoading", "attendanceRecords", "length", "StudentViewComponent_div_15_Template_button_click_86_listener", "_r19", "ctx_r18", "toggleAttendanceDetails", "StudentViewComponent_div_15_mat_card_content_90_Template", "ctx_r2", "student", "name", "email", "rollNo", "regNo", "contact", "isActive", "program", "fullName", "department", "getClassName", "getClassSection", "semester", "academicYear", "showAttendanceDetails", "StudentViewComponent_div_16_Template_button_click_7_listener", "_r21", "ctx_r20", "goBack", "StudentViewComponent", "constructor", "route", "router", "userService", "attendanceService", "snackBar", "studentId", "loading", "selectedAttendanceView", "itemsPerPage", "ngOnInit", "snapshot", "paramMap", "get", "loadStudentDetails", "fire", "navigate", "getUserProfile", "subscribe", "next", "response", "success", "user", "error", "console", "loadStudentAttendance", "getStudentAttendance", "processAttendanceData", "attendance", "calculateOverallAttendance", "open", "duration", "attendanceData", "subjectMap", "Map", "for<PERSON>ach", "record", "subjectId", "_id", "has", "set", "class", "records", "subjectData", "push", "status", "Math", "round", "Array", "from", "values", "totalPercentage", "reduce", "sum", "percentage", "classId", "section", "className", "startIndex", "slice", "ceil", "page", "_", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "UserService", "i3", "AttendanceService", "i4", "MatSnackBar", "_2", "selectors", "decls", "vars", "consts", "template", "StudentViewComponent_Template", "rf", "ctx", "StudentViewComponent_Template_button_click_3_listener", "StudentViewComponent_div_13_Template", "StudentViewComponent_div_14_Template", "StudentViewComponent_div_15_Template", "StudentViewComponent_div_16_Template"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\students\\student-view\\student-view.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\students\\student-view\\student-view.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { UserService } from '../../../../services/user.service';\r\nimport { AttendanceService } from '../../../../services/attendance.service';\r\nimport { User } from '../../../../models/user';\r\nimport Swal from 'sweetalert2';\r\n\r\ninterface AttendanceRecord {\r\n  subject: {\r\n    _id: string;\r\n    subjectName: string;\r\n    code: string;\r\n  };\r\n  class: {\r\n    _id: string;\r\n    className: string;\r\n    section: string;\r\n  };\r\n  totalClasses: number;\r\n  presentClasses: number;\r\n  absentClasses: number;\r\n  lateClasses: number;\r\n  attendancePercentage: number;\r\n  monthlyRecords?: any[];\r\n  yearlyRecords?: any[];\r\n}\r\n\r\n@Component({\r\n  selector: 'app-student-view',\r\n  templateUrl: './student-view.component.html',\r\n  styleUrls: ['./student-view.component.css']\r\n})\r\nexport class StudentViewComponent implements OnInit {\r\n  student: User | null = null;\r\n  studentId: string | null = null;\r\n  loading = false;\r\n  attendanceLoading = false;\r\n  \r\n  // Attendance data\r\n  attendanceRecords: AttendanceRecord[] = [];\r\n  overallAttendance = 0;\r\n  totalSubjects = 0;\r\n  \r\n  // View toggles\r\n  showAttendanceDetails = false;\r\n  selectedAttendanceView = 'daily'; // daily, monthly, yearly\r\n  \r\n  // Pagination for attendance\r\n  currentPage = 1;\r\n  itemsPerPage = 10;\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private userService: UserService,\r\n    private attendanceService: AttendanceService,\r\n    private snackBar: MatSnackBar\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.studentId = this.route.snapshot.paramMap.get('id');\r\n    if (this.studentId) {\r\n      this.loadStudentDetails();\r\n    } else {\r\n      Swal.fire('Error', 'Student ID not found', 'error');\r\n      this.router.navigate(['/dashboard/admin/students']);\r\n    }\r\n  }\r\n\r\n  loadStudentDetails(): void {\r\n    if (!this.studentId) return;\r\n\r\n    this.loading = true;\r\n    this.userService.getUserProfile(this.studentId).subscribe({\r\n      next: (response) => {\r\n        this.loading = false;\r\n        if (response.success) {\r\n          this.student = response.user;\r\n        } else {\r\n          Swal.fire('Error', 'Failed to load student details', 'error');\r\n          this.router.navigate(['/dashboard/admin/students']);\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.loading = false;\r\n        console.error('Error loading student details:', error);\r\n        Swal.fire('Error', 'Failed to load student details', 'error');\r\n        this.router.navigate(['/dashboard/admin/students']);\r\n      }\r\n    });\r\n  }\r\n\r\n  loadStudentAttendance(): void {\r\n    if (!this.studentId) return;\r\n\r\n    this.attendanceLoading = true;\r\n    this.attendanceService.getStudentAttendance(this.studentId).subscribe({\r\n      next: (response) => {\r\n        this.attendanceLoading = false;\r\n        if (response.success) {\r\n          this.processAttendanceData(response.attendance);\r\n          this.calculateOverallAttendance();\r\n        } else {\r\n          this.snackBar.open('Failed to load attendance data', 'Close', { duration: 3000 });\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.attendanceLoading = false;\r\n        console.error('Error loading attendance:', error);\r\n        this.snackBar.open('Error loading attendance data', 'Close', { duration: 3000 });\r\n      }\r\n    });\r\n  }\r\n\r\n  processAttendanceData(attendanceData: any[]): void {\r\n    // Group attendance by subject\r\n    const subjectMap = new Map<string, any>();\r\n\r\n    attendanceData.forEach(record => {\r\n      const subjectId = record.subject._id;\r\n      if (!subjectMap.has(subjectId)) {\r\n        subjectMap.set(subjectId, {\r\n          subject: record.subject,\r\n          class: record.class,\r\n          totalClasses: 0,\r\n          presentClasses: 0,\r\n          absentClasses: 0,\r\n          lateClasses: 0,\r\n          attendancePercentage: 0,\r\n          records: []\r\n        });\r\n      }\r\n\r\n      const subjectData = subjectMap.get(subjectId);\r\n      subjectData.totalClasses++;\r\n      subjectData.records.push(record);\r\n\r\n      if (record.status === 'present') {\r\n        subjectData.presentClasses++;\r\n      } else if (record.status === 'absent') {\r\n        subjectData.absentClasses++;\r\n      } else if (record.status === 'late') {\r\n        subjectData.lateClasses++;\r\n        subjectData.presentClasses++; // Late is considered present\r\n      }\r\n\r\n      // Calculate percentage\r\n      subjectData.attendancePercentage = Math.round(\r\n        (subjectData.presentClasses / subjectData.totalClasses) * 100\r\n      );\r\n    });\r\n\r\n    this.attendanceRecords = Array.from(subjectMap.values());\r\n    this.totalSubjects = this.attendanceRecords.length;\r\n  }\r\n\r\n  calculateOverallAttendance(): void {\r\n    if (this.attendanceRecords.length === 0) {\r\n      this.overallAttendance = 0;\r\n      return;\r\n    }\r\n\r\n    const totalPercentage = this.attendanceRecords.reduce(\r\n      (sum, record) => sum + record.attendancePercentage, 0\r\n    );\r\n    this.overallAttendance = Math.round(totalPercentage / this.attendanceRecords.length);\r\n  }\r\n\r\n  toggleAttendanceDetails(): void {\r\n    this.showAttendanceDetails = !this.showAttendanceDetails;\r\n    if (this.showAttendanceDetails && this.attendanceRecords.length === 0) {\r\n      this.loadStudentAttendance();\r\n    }\r\n  }\r\n\r\n  editStudent(): void {\r\n    if (this.studentId) {\r\n      this.router.navigate(['/dashboard/admin/students/edit', this.studentId]);\r\n    }\r\n  }\r\n\r\n  goBack(): void {\r\n    this.router.navigate(['/dashboard/admin/students']);\r\n  }\r\n\r\n  getAttendanceClass(percentage: number): string {\r\n    if (percentage >= 85) return 'excellent';\r\n    if (percentage >= 75) return 'good';\r\n    if (percentage >= 65) return 'average';\r\n    return 'poor';\r\n  }\r\n\r\n  getClassSection(student: User): string {\r\n    if (student?.classId && typeof student.classId === 'object') {\r\n      return student.classId.section || '';\r\n    }\r\n    return '';\r\n  }\r\n\r\n  getClassName(student: User): string {\r\n    if (student?.classId && typeof student.classId === 'object') {\r\n      return student.classId.className || 'N/A';\r\n    }\r\n    return 'N/A';\r\n  }\r\n\r\n  get paginatedRecords(): AttendanceRecord[] {\r\n    const startIndex = (this.currentPage - 1) * this.itemsPerPage;\r\n    return this.attendanceRecords.slice(startIndex, startIndex + this.itemsPerPage);\r\n  }\r\n\r\n  get totalPages(): number {\r\n    return Math.ceil(this.attendanceRecords.length / this.itemsPerPage);\r\n  }\r\n\r\n  changePage(page: number): void {\r\n    if (page >= 1 && page <= this.totalPages) {\r\n      this.currentPage = page;\r\n    }\r\n  }\r\n}\r\n", "<div class=\"student-view-container\">\r\n  <!-- Header -->\r\n  <div class=\"page-header\">\r\n    <div class=\"header-content\">\r\n      <button mat-icon-button (click)=\"goBack()\" class=\"back-btn\">\r\n        <mat-icon>arrow_back</mat-icon>\r\n      </button>\r\n      <div class=\"title-section\">\r\n        <h1 class=\"page-title\">\r\n          <mat-icon class=\"title-icon\">person</mat-icon>\r\n          Student Details\r\n        </h1>\r\n        <p class=\"page-subtitle\">View comprehensive student information and attendance records</p>\r\n      </div>\r\n    </div>\r\n    <div class=\"header-actions\" *ngIf=\"student\">\r\n      <button mat-raised-button color=\"primary\" (click)=\"editStudent()\">\r\n        <mat-icon>edit</mat-icon>\r\n        Edit Student\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Loading State -->\r\n  <div *ngIf=\"loading\" class=\"loading-container\">\r\n    <mat-spinner></mat-spinner>\r\n    <p>Loading student details...</p>\r\n  </div>\r\n\r\n  <!-- Student Details -->\r\n  <div *ngIf=\"!loading && student\" class=\"student-details\">\r\n    <!-- Basic Information Card -->\r\n    <mat-card class=\"info-card\">\r\n      <mat-card-header>\r\n        <mat-card-title>\r\n          <mat-icon>account_circle</mat-icon>\r\n          Basic Information\r\n        </mat-card-title>\r\n      </mat-card-header>\r\n      <mat-card-content>\r\n        <div class=\"info-grid\">\r\n          <div class=\"info-item\">\r\n            <label>Full Name</label>\r\n            <h3>{{ student.name }}</h3>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <label>Email</label>\r\n            <p>{{ student.email }}</p>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <label>Roll Number</label>\r\n            <h3>{{ student.rollNo || 'N/A' }}</h3>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <label>Registration Number</label>\r\n            <p>{{ student.regNo || 'N/A' }}</p>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <label>Contact</label>\r\n            <p>{{ student.contact || 'N/A' }}</p>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <label>Status</label>\r\n            <span class=\"status-badge\" [ngClass]=\"student.isActive ? 'active' : 'inactive'\">\r\n              {{ student.isActive ? 'Active' : 'Inactive' }}\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n\r\n    <!-- Academic Information Card -->\r\n    <mat-card class=\"info-card\">\r\n      <mat-card-header>\r\n        <mat-card-title>\r\n          <mat-icon>school</mat-icon>\r\n          Academic Information\r\n        </mat-card-title>\r\n      </mat-card-header>\r\n      <mat-card-content>\r\n        <div class=\"info-grid\">\r\n          <div class=\"info-item\">\r\n            <label>Program</label>\r\n            <h3>{{ student.program?.name || 'N/A' }}</h3>\r\n            <small>{{ student.program?.fullName || '' }}</small>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <label>Department</label>\r\n            <h3>{{ student.department?.name || 'N/A' }}</h3>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <label>Class</label>\r\n            <h3>{{ getClassName(student) }}</h3>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <label>Section</label>\r\n            <h3>{{ getClassSection(student) || 'N/A' }}</h3>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <label>Semester</label>\r\n            <h3>{{ student.semester || 'N/A' }}</h3>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <label>Academic Year</label>\r\n            <p>{{ student.academicYear || 'N/A' }}</p>\r\n          </div>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n\r\n    <!-- Attendance Section -->\r\n    <mat-card class=\"attendance-card\">\r\n      <mat-card-header>\r\n        <mat-card-title>\r\n          <mat-icon>access_time</mat-icon>\r\n          Attendance Records\r\n        </mat-card-title>\r\n        <div class=\"card-actions\">\r\n          <button mat-raised-button color=\"accent\" (click)=\"toggleAttendanceDetails()\">\r\n            <mat-icon>{{ showAttendanceDetails ? 'visibility_off' : 'visibility' }}</mat-icon>\r\n            {{ showAttendanceDetails ? 'Hide' : 'View' }} Attendance\r\n          </button>\r\n        </div>\r\n      </mat-card-header>\r\n      \r\n      <!-- Attendance Summary -->\r\n      <mat-card-content *ngIf=\"showAttendanceDetails\">\r\n        <div *ngIf=\"attendanceLoading\" class=\"loading-state\">\r\n          <mat-spinner diameter=\"40\"></mat-spinner>\r\n          <p>Loading attendance data...</p>\r\n        </div>\r\n\r\n        <div *ngIf=\"!attendanceLoading && attendanceRecords.length > 0\" class=\"attendance-content\">\r\n          <!-- Summary Stats -->\r\n          <div class=\"attendance-summary\">\r\n            <div class=\"summary-item overall\">\r\n              <div class=\"summary-icon\">\r\n                <mat-icon>assessment</mat-icon>\r\n              </div>\r\n              <div class=\"summary-info\">\r\n                <h3>{{ overallAttendance }}%</h3>\r\n                <p>Overall Attendance</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"summary-item subjects\">\r\n              <div class=\"summary-icon\">\r\n                <mat-icon>subject</mat-icon>\r\n              </div>\r\n              <div class=\"summary-info\">\r\n                <h3>{{ totalSubjects }}</h3>\r\n                <p>Total Subjects</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Attendance Records -->\r\n          <div class=\"attendance-records\">\r\n            <h4>Subject-wise Attendance</h4>\r\n            <div class=\"records-grid\">\r\n              <div *ngFor=\"let record of paginatedRecords\" class=\"attendance-record\"\r\n                   [ngClass]=\"getAttendanceClass(record.attendancePercentage)\">\r\n                <div class=\"record-header\">\r\n                  <div class=\"subject-info\">\r\n                    <h5>{{ record.subject.subjectName }}</h5>\r\n                    <span class=\"subject-code\">{{ record.subject.code }}</span>\r\n                  </div>\r\n                  <div class=\"attendance-percentage\" [ngClass]=\"getAttendanceClass(record.attendancePercentage)\">\r\n                    {{ record.attendancePercentage }}%\r\n                  </div>\r\n                </div>\r\n                <div class=\"record-stats\">\r\n                  <div class=\"stat-item present\">\r\n                    <mat-icon>check_circle</mat-icon>\r\n                    <span>{{ record.presentClasses }} Present</span>\r\n                  </div>\r\n                  <div class=\"stat-item absent\">\r\n                    <mat-icon>cancel</mat-icon>\r\n                    <span>{{ record.absentClasses }} Absent</span>\r\n                  </div>\r\n                  <div class=\"stat-item late\" *ngIf=\"record.lateClasses > 0\">\r\n                    <mat-icon>schedule</mat-icon>\r\n                    <span>{{ record.lateClasses }} Late</span>\r\n                  </div>\r\n                  <div class=\"stat-item total\">\r\n                    <mat-icon>event</mat-icon>\r\n                    <span>{{ record.totalClasses }} Total</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Pagination -->\r\n            <div class=\"pagination-container\" *ngIf=\"totalPages > 1\">\r\n              <button mat-icon-button [disabled]=\"currentPage === 1\" (click)=\"changePage(currentPage - 1)\">\r\n                <mat-icon>chevron_left</mat-icon>\r\n              </button>\r\n              <span class=\"page-info\">Page {{ currentPage }} of {{ totalPages }}</span>\r\n              <button mat-icon-button [disabled]=\"currentPage === totalPages\" (click)=\"changePage(currentPage + 1)\">\r\n                <mat-icon>chevron_right</mat-icon>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div *ngIf=\"!attendanceLoading && attendanceRecords.length === 0\" class=\"empty-state\">\r\n          <mat-icon class=\"empty-icon\">event_busy</mat-icon>\r\n          <h4>No Attendance Records</h4>\r\n          <p>No attendance data found for this student.</p>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n  </div>\r\n\r\n  <!-- Error State -->\r\n  <div *ngIf=\"!loading && !student\" class=\"error-state\">\r\n    <mat-icon class=\"error-icon\">error</mat-icon>\r\n    <h3>Student Not Found</h3>\r\n    <p>The requested student could not be found.</p>\r\n    <button mat-raised-button color=\"primary\" (click)=\"goBack()\">\r\n      <mat-icon>arrow_back</mat-icon>\r\n      Back to Students\r\n    </button>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAMA,OAAOA,IAAI,MAAM,aAAa;;;;;;;;;;;;;;ICS1BC,EAAA,CAAAC,cAAA,cAA4C;IACAD,EAAA,CAAAE,UAAA,mBAAAC,6DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAC/DT,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAU,MAAA,WAAI;IAAAV,EAAA,CAAAW,YAAA,EAAW;IACzBX,EAAA,CAAAU,MAAA,qBACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;;IAKbX,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAY,SAAA,kBAA2B;IAC3BZ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAU,MAAA,iCAA0B;IAAAV,EAAA,CAAAW,YAAA,EAAI;;;;;IAqG7BX,EAAA,CAAAC,cAAA,cAAqD;IACnDD,EAAA,CAAAY,SAAA,sBAAyC;IACzCZ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAU,MAAA,iCAA0B;IAAAV,EAAA,CAAAW,YAAA,EAAI;;;;;IAkDzBX,EAAA,CAAAC,cAAA,cAA2D;IAC/CD,EAAA,CAAAU,MAAA,eAAQ;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAC7BX,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,GAA6B;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IAApCX,EAAA,CAAAa,SAAA,GAA6B;IAA7Bb,EAAA,CAAAc,kBAAA,KAAAC,UAAA,CAAAC,WAAA,UAA6B;;;;;IAtBzChB,EAAA,CAAAC,cAAA,cACiE;IAGvDD,EAAA,CAAAU,MAAA,GAAgC;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACzCX,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAU,MAAA,GAAyB;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAE7DX,EAAA,CAAAC,cAAA,cAA+F;IAC7FD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAERX,EAAA,CAAAC,cAAA,cAA0B;IAEZD,EAAA,CAAAU,MAAA,oBAAY;IAAAV,EAAA,CAAAW,YAAA,EAAW;IACjCX,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,IAAmC;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAElDX,EAAA,CAAAC,cAAA,eAA8B;IAClBD,EAAA,CAAAU,MAAA,cAAM;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAC3BX,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,IAAiC;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAEhDX,EAAA,CAAAiB,UAAA,KAAAC,4EAAA,kBAGM;IACNlB,EAAA,CAAAC,cAAA,eAA6B;IACjBD,EAAA,CAAAU,MAAA,aAAK;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAC1BX,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,IAA+B;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;IAzB7CX,EAAA,CAAAmB,UAAA,YAAAC,OAAA,CAAAC,kBAAA,CAAAN,UAAA,CAAAO,oBAAA,EAA2D;IAGtDtB,EAAA,CAAAa,SAAA,GAAgC;IAAhCb,EAAA,CAAAuB,iBAAA,CAAAR,UAAA,CAAAS,OAAA,CAAAC,WAAA,CAAgC;IACTzB,EAAA,CAAAa,SAAA,GAAyB;IAAzBb,EAAA,CAAAuB,iBAAA,CAAAR,UAAA,CAAAS,OAAA,CAAAE,IAAA,CAAyB;IAEnB1B,EAAA,CAAAa,SAAA,GAA2D;IAA3Db,EAAA,CAAAmB,UAAA,YAAAC,OAAA,CAAAC,kBAAA,CAAAN,UAAA,CAAAO,oBAAA,EAA2D;IAC5FtB,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAC,UAAA,CAAAO,oBAAA,OACF;IAKQtB,EAAA,CAAAa,SAAA,GAAmC;IAAnCb,EAAA,CAAAc,kBAAA,KAAAC,UAAA,CAAAY,cAAA,aAAmC;IAInC3B,EAAA,CAAAa,SAAA,GAAiC;IAAjCb,EAAA,CAAAc,kBAAA,KAAAC,UAAA,CAAAa,aAAA,YAAiC;IAEZ5B,EAAA,CAAAa,SAAA,GAA4B;IAA5Bb,EAAA,CAAAmB,UAAA,SAAAJ,UAAA,CAAAC,WAAA,KAA4B;IAMjDhB,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAAc,kBAAA,KAAAC,UAAA,CAAAc,YAAA,WAA+B;;;;;;IAO7C7B,EAAA,CAAAC,cAAA,cAAyD;IACAD,EAAA,CAAAE,UAAA,mBAAA4B,8FAAA;MAAA9B,EAAA,CAAAI,aAAA,CAAA2B,IAAA;MAAA,MAAAC,OAAA,GAAAhC,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAwB,OAAA,CAAAC,UAAA,CAAAD,OAAA,CAAAE,WAAA,GAAyB,CAAC,CAAC;IAAA,EAAC;IAC1FlC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAU,MAAA,mBAAY;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAEnCX,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAU,MAAA,GAA0C;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACzEX,EAAA,CAAAC,cAAA,iBAAsG;IAAtCD,EAAA,CAAAE,UAAA,mBAAAiC,8FAAA;MAAAnC,EAAA,CAAAI,aAAA,CAAA2B,IAAA;MAAA,MAAAK,OAAA,GAAApC,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA4B,OAAA,CAAAH,UAAA,CAAAG,OAAA,CAAAF,WAAA,GAAyB,CAAC,CAAC;IAAA,EAAC;IACnGlC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAU,MAAA,oBAAa;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;IALZX,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAAmB,UAAA,aAAAkB,OAAA,CAAAH,WAAA,OAA8B;IAG9BlC,EAAA,CAAAa,SAAA,GAA0C;IAA1Cb,EAAA,CAAAsC,kBAAA,UAAAD,OAAA,CAAAH,WAAA,UAAAG,OAAA,CAAAE,UAAA,KAA0C;IAC1CvC,EAAA,CAAAa,SAAA,GAAuC;IAAvCb,EAAA,CAAAmB,UAAA,aAAAkB,OAAA,CAAAH,WAAA,KAAAG,OAAA,CAAAE,UAAA,CAAuC;;;;;IAjErEvC,EAAA,CAAAC,cAAA,cAA2F;IAKzED,EAAA,CAAAU,MAAA,iBAAU;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAEjCX,EAAA,CAAAC,cAAA,cAA0B;IACpBD,EAAA,CAAAU,MAAA,GAAwB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACjCX,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAU,MAAA,0BAAkB;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAG7BX,EAAA,CAAAC,cAAA,eAAmC;IAErBD,EAAA,CAAAU,MAAA,eAAO;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAE9BX,EAAA,CAAAC,cAAA,eAA0B;IACpBD,EAAA,CAAAU,MAAA,IAAmB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAC5BX,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAU,MAAA,sBAAc;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAM3BX,EAAA,CAAAC,cAAA,eAAgC;IAC1BD,EAAA,CAAAU,MAAA,+BAAuB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAChCX,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAiB,UAAA,KAAAuB,qEAAA,mBA6BM;IACRxC,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAiB,UAAA,KAAAwB,qEAAA,kBAQM;IACRzC,EAAA,CAAAW,YAAA,EAAM;;;;IA7DIX,EAAA,CAAAa,SAAA,GAAwB;IAAxBb,EAAA,CAAAc,kBAAA,KAAA4B,MAAA,CAAAC,iBAAA,MAAwB;IASxB3C,EAAA,CAAAa,SAAA,GAAmB;IAAnBb,EAAA,CAAAuB,iBAAA,CAAAmB,MAAA,CAAAE,aAAA,CAAmB;IAUD5C,EAAA,CAAAa,SAAA,GAAmB;IAAnBb,EAAA,CAAAmB,UAAA,YAAAuB,MAAA,CAAAG,gBAAA,CAAmB;IAiCV7C,EAAA,CAAAa,SAAA,GAAoB;IAApBb,EAAA,CAAAmB,UAAA,SAAAuB,MAAA,CAAAH,UAAA,KAAoB;;;;;IAY3DvC,EAAA,CAAAC,cAAA,cAAsF;IACvDD,EAAA,CAAAU,MAAA,iBAAU;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAClDX,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAU,MAAA,4BAAqB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAC9BX,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAU,MAAA,iDAA0C;IAAAV,EAAA,CAAAW,YAAA,EAAI;;;;;IAjFrDX,EAAA,CAAAC,cAAA,uBAAgD;IAC9CD,EAAA,CAAAiB,UAAA,IAAA6B,8DAAA,kBAGM;IAEN9C,EAAA,CAAAiB,UAAA,IAAA8B,8DAAA,mBAsEM;IAEN/C,EAAA,CAAAiB,UAAA,IAAA+B,8DAAA,kBAIM;IACRhD,EAAA,CAAAW,YAAA,EAAmB;;;;IAlFXX,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAmB,UAAA,SAAA8B,MAAA,CAAAC,iBAAA,CAAuB;IAKvBlD,EAAA,CAAAa,SAAA,GAAwD;IAAxDb,EAAA,CAAAmB,UAAA,UAAA8B,MAAA,CAAAC,iBAAA,IAAAD,MAAA,CAAAE,iBAAA,CAAAC,MAAA,KAAwD;IAwExDpD,EAAA,CAAAa,SAAA,GAA0D;IAA1Db,EAAA,CAAAmB,UAAA,UAAA8B,MAAA,CAAAC,iBAAA,IAAAD,MAAA,CAAAE,iBAAA,CAAAC,MAAA,OAA0D;;;;;;IA9KtEpD,EAAA,CAAAC,cAAA,cAAyD;IAKvCD,EAAA,CAAAU,MAAA,qBAAc;IAAAV,EAAA,CAAAW,YAAA,EAAW;IACnCX,EAAA,CAAAU,MAAA,0BACF;IAAAV,EAAA,CAAAW,YAAA,EAAiB;IAEnBX,EAAA,CAAAC,cAAA,uBAAkB;IAGLD,EAAA,CAAAU,MAAA,iBAAS;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IACxBX,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAU,MAAA,IAAkB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAE7BX,EAAA,CAAAC,cAAA,eAAuB;IACdD,EAAA,CAAAU,MAAA,aAAK;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IACpBX,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAU,MAAA,IAAmB;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAE5BX,EAAA,CAAAC,cAAA,eAAuB;IACdD,EAAA,CAAAU,MAAA,mBAAW;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IAC1BX,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAU,MAAA,IAA6B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAExCX,EAAA,CAAAC,cAAA,eAAuB;IACdD,EAAA,CAAAU,MAAA,2BAAmB;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IAClCX,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAU,MAAA,IAA4B;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAErCX,EAAA,CAAAC,cAAA,eAAuB;IACdD,EAAA,CAAAU,MAAA,eAAO;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IACtBX,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAU,MAAA,IAA8B;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAEvCX,EAAA,CAAAC,cAAA,eAAuB;IACdD,EAAA,CAAAU,MAAA,cAAM;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IACrBX,EAAA,CAAAC,cAAA,gBAAgF;IAC9ED,EAAA,CAAAU,MAAA,IACF;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAOfX,EAAA,CAAAC,cAAA,oBAA4B;IAGZD,EAAA,CAAAU,MAAA,cAAM;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAC3BX,EAAA,CAAAU,MAAA,8BACF;IAAAV,EAAA,CAAAW,YAAA,EAAiB;IAEnBX,EAAA,CAAAC,cAAA,wBAAkB;IAGLD,EAAA,CAAAU,MAAA,eAAO;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IACtBX,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAU,MAAA,IAAoC;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAC7CX,EAAA,CAAAC,cAAA,aAAO;IAAAD,EAAA,CAAAU,MAAA,IAAqC;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IAEtDX,EAAA,CAAAC,cAAA,eAAuB;IACdD,EAAA,CAAAU,MAAA,kBAAU;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IACzBX,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAU,MAAA,IAAuC;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAElDX,EAAA,CAAAC,cAAA,eAAuB;IACdD,EAAA,CAAAU,MAAA,aAAK;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IACpBX,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAU,MAAA,IAA2B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAEtCX,EAAA,CAAAC,cAAA,eAAuB;IACdD,EAAA,CAAAU,MAAA,eAAO;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IACtBX,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAU,MAAA,IAAuC;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAElDX,EAAA,CAAAC,cAAA,eAAuB;IACdD,EAAA,CAAAU,MAAA,gBAAQ;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IACvBX,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAU,MAAA,IAA+B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAE1CX,EAAA,CAAAC,cAAA,eAAuB;IACdD,EAAA,CAAAU,MAAA,qBAAa;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IAC5BX,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAU,MAAA,IAAmC;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAOlDX,EAAA,CAAAC,cAAA,oBAAkC;IAGlBD,EAAA,CAAAU,MAAA,mBAAW;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAChCX,EAAA,CAAAU,MAAA,4BACF;IAAAV,EAAA,CAAAW,YAAA,EAAiB;IACjBX,EAAA,CAAAC,cAAA,eAA0B;IACiBD,EAAA,CAAAE,UAAA,mBAAAmD,8DAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAkD,IAAA;MAAA,MAAAC,OAAA,GAAAvD,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA+C,OAAA,CAAAC,uBAAA,EAAyB;IAAA,EAAC;IAC1ExD,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAU,MAAA,IAA6D;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAClFX,EAAA,CAAAU,MAAA,IACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;IAKbX,EAAA,CAAAiB,UAAA,KAAAwC,wDAAA,+BAmFmB;IACrBzD,EAAA,CAAAW,YAAA,EAAW;;;;IAvKCX,EAAA,CAAAa,SAAA,IAAkB;IAAlBb,EAAA,CAAAuB,iBAAA,CAAAmC,MAAA,CAAAC,OAAA,CAAAC,IAAA,CAAkB;IAInB5D,EAAA,CAAAa,SAAA,GAAmB;IAAnBb,EAAA,CAAAuB,iBAAA,CAAAmC,MAAA,CAAAC,OAAA,CAAAE,KAAA,CAAmB;IAIlB7D,EAAA,CAAAa,SAAA,GAA6B;IAA7Bb,EAAA,CAAAuB,iBAAA,CAAAmC,MAAA,CAAAC,OAAA,CAAAG,MAAA,UAA6B;IAI9B9D,EAAA,CAAAa,SAAA,GAA4B;IAA5Bb,EAAA,CAAAuB,iBAAA,CAAAmC,MAAA,CAAAC,OAAA,CAAAI,KAAA,UAA4B;IAI5B/D,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAAuB,iBAAA,CAAAmC,MAAA,CAAAC,OAAA,CAAAK,OAAA,UAA8B;IAINhE,EAAA,CAAAa,SAAA,GAAoD;IAApDb,EAAA,CAAAmB,UAAA,YAAAuC,MAAA,CAAAC,OAAA,CAAAM,QAAA,yBAAoD;IAC7EjE,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAA4C,MAAA,CAAAC,OAAA,CAAAM,QAAA,8BACF;IAkBIjE,EAAA,CAAAa,SAAA,IAAoC;IAApCb,EAAA,CAAAuB,iBAAA,EAAAmC,MAAA,CAAAC,OAAA,CAAAO,OAAA,kBAAAR,MAAA,CAAAC,OAAA,CAAAO,OAAA,CAAAN,IAAA,WAAoC;IACjC5D,EAAA,CAAAa,SAAA,GAAqC;IAArCb,EAAA,CAAAuB,iBAAA,EAAAmC,MAAA,CAAAC,OAAA,CAAAO,OAAA,kBAAAR,MAAA,CAAAC,OAAA,CAAAO,OAAA,CAAAC,QAAA,QAAqC;IAIxCnE,EAAA,CAAAa,SAAA,GAAuC;IAAvCb,EAAA,CAAAuB,iBAAA,EAAAmC,MAAA,CAAAC,OAAA,CAAAS,UAAA,kBAAAV,MAAA,CAAAC,OAAA,CAAAS,UAAA,CAAAR,IAAA,WAAuC;IAIvC5D,EAAA,CAAAa,SAAA,GAA2B;IAA3Bb,EAAA,CAAAuB,iBAAA,CAAAmC,MAAA,CAAAW,YAAA,CAAAX,MAAA,CAAAC,OAAA,EAA2B;IAI3B3D,EAAA,CAAAa,SAAA,GAAuC;IAAvCb,EAAA,CAAAuB,iBAAA,CAAAmC,MAAA,CAAAY,eAAA,CAAAZ,MAAA,CAAAC,OAAA,WAAuC;IAIvC3D,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAAuB,iBAAA,CAAAmC,MAAA,CAAAC,OAAA,CAAAY,QAAA,UAA+B;IAIhCvE,EAAA,CAAAa,SAAA,GAAmC;IAAnCb,EAAA,CAAAuB,iBAAA,CAAAmC,MAAA,CAAAC,OAAA,CAAAa,YAAA,UAAmC;IAe5BxE,EAAA,CAAAa,SAAA,IAA6D;IAA7Db,EAAA,CAAAuB,iBAAA,CAAAmC,MAAA,CAAAe,qBAAA,mCAA6D;IACvEzE,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAA4C,MAAA,CAAAe,qBAAA,mCACF;IAKezE,EAAA,CAAAa,SAAA,GAA2B;IAA3Bb,EAAA,CAAAmB,UAAA,SAAAuC,MAAA,CAAAe,qBAAA,CAA2B;;;;;;IAwFlDzE,EAAA,CAAAC,cAAA,cAAsD;IACvBD,EAAA,CAAAU,MAAA,YAAK;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAC7CX,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAU,MAAA,wBAAiB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAC1BX,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAU,MAAA,gDAAyC;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAChDX,EAAA,CAAAC,cAAA,iBAA6D;IAAnBD,EAAA,CAAAE,UAAA,mBAAAwE,6DAAA;MAAA1E,EAAA,CAAAI,aAAA,CAAAuE,IAAA;MAAA,MAAAC,OAAA,GAAA5E,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAoE,OAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IAC1D7E,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAU,MAAA,iBAAU;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAC/BX,EAAA,CAAAU,MAAA,0BACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;AD5Lb,OAAM,MAAOmE,oBAAoB;EAmB/BC,YACUC,KAAqB,EACrBC,MAAc,EACdC,WAAwB,EACxBC,iBAAoC,EACpCC,QAAqB;IAJrB,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,QAAQ,GAARA,QAAQ;IAvBlB,KAAAzB,OAAO,GAAgB,IAAI;IAC3B,KAAA0B,SAAS,GAAkB,IAAI;IAC/B,KAAAC,OAAO,GAAG,KAAK;IACf,KAAApC,iBAAiB,GAAG,KAAK;IAEzB;IACA,KAAAC,iBAAiB,GAAuB,EAAE;IAC1C,KAAAR,iBAAiB,GAAG,CAAC;IACrB,KAAAC,aAAa,GAAG,CAAC;IAEjB;IACA,KAAA6B,qBAAqB,GAAG,KAAK;IAC7B,KAAAc,sBAAsB,GAAG,OAAO,CAAC,CAAC;IAElC;IACA,KAAArD,WAAW,GAAG,CAAC;IACf,KAAAsD,YAAY,GAAG,EAAE;EAQd;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACJ,SAAS,GAAG,IAAI,CAACL,KAAK,CAACU,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACvD,IAAI,IAAI,CAACP,SAAS,EAAE;MAClB,IAAI,CAACQ,kBAAkB,EAAE;KAC1B,MAAM;MACL9F,IAAI,CAAC+F,IAAI,CAAC,OAAO,EAAE,sBAAsB,EAAE,OAAO,CAAC;MACnD,IAAI,CAACb,MAAM,CAACc,QAAQ,CAAC,CAAC,2BAA2B,CAAC,CAAC;;EAEvD;EAEAF,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACR,SAAS,EAAE;IAErB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACJ,WAAW,CAACc,cAAc,CAAC,IAAI,CAACX,SAAS,CAAC,CAACY,SAAS,CAAC;MACxDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACb,OAAO,GAAG,KAAK;QACpB,IAAIa,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACzC,OAAO,GAAGwC,QAAQ,CAACE,IAAI;SAC7B,MAAM;UACLtG,IAAI,CAAC+F,IAAI,CAAC,OAAO,EAAE,gCAAgC,EAAE,OAAO,CAAC;UAC7D,IAAI,CAACb,MAAM,CAACc,QAAQ,CAAC,CAAC,2BAA2B,CAAC,CAAC;;MAEvD,CAAC;MACDO,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAChB,OAAO,GAAG,KAAK;QACpBiB,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtDvG,IAAI,CAAC+F,IAAI,CAAC,OAAO,EAAE,gCAAgC,EAAE,OAAO,CAAC;QAC7D,IAAI,CAACb,MAAM,CAACc,QAAQ,CAAC,CAAC,2BAA2B,CAAC,CAAC;MACrD;KACD,CAAC;EACJ;EAEAS,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACnB,SAAS,EAAE;IAErB,IAAI,CAACnC,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACiC,iBAAiB,CAACsB,oBAAoB,CAAC,IAAI,CAACpB,SAAS,CAAC,CAACY,SAAS,CAAC;MACpEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACjD,iBAAiB,GAAG,KAAK;QAC9B,IAAIiD,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACM,qBAAqB,CAACP,QAAQ,CAACQ,UAAU,CAAC;UAC/C,IAAI,CAACC,0BAA0B,EAAE;SAClC,MAAM;UACL,IAAI,CAACxB,QAAQ,CAACyB,IAAI,CAAC,gCAAgC,EAAE,OAAO,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;;MAErF,CAAC;MACDR,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACpD,iBAAiB,GAAG,KAAK;QAC9BqD,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,IAAI,CAAClB,QAAQ,CAACyB,IAAI,CAAC,+BAA+B,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MAClF;KACD,CAAC;EACJ;EAEAJ,qBAAqBA,CAACK,cAAqB;IACzC;IACA,MAAMC,UAAU,GAAG,IAAIC,GAAG,EAAe;IAEzCF,cAAc,CAACG,OAAO,CAACC,MAAM,IAAG;MAC9B,MAAMC,SAAS,GAAGD,MAAM,CAAC3F,OAAO,CAAC6F,GAAG;MACpC,IAAI,CAACL,UAAU,CAACM,GAAG,CAACF,SAAS,CAAC,EAAE;QAC9BJ,UAAU,CAACO,GAAG,CAACH,SAAS,EAAE;UACxB5F,OAAO,EAAE2F,MAAM,CAAC3F,OAAO;UACvBgG,KAAK,EAAEL,MAAM,CAACK,KAAK;UACnB3F,YAAY,EAAE,CAAC;UACfF,cAAc,EAAE,CAAC;UACjBC,aAAa,EAAE,CAAC;UAChBZ,WAAW,EAAE,CAAC;UACdM,oBAAoB,EAAE,CAAC;UACvBmG,OAAO,EAAE;SACV,CAAC;;MAGJ,MAAMC,WAAW,GAAGV,UAAU,CAACpB,GAAG,CAACwB,SAAS,CAAC;MAC7CM,WAAW,CAAC7F,YAAY,EAAE;MAC1B6F,WAAW,CAACD,OAAO,CAACE,IAAI,CAACR,MAAM,CAAC;MAEhC,IAAIA,MAAM,CAACS,MAAM,KAAK,SAAS,EAAE;QAC/BF,WAAW,CAAC/F,cAAc,EAAE;OAC7B,MAAM,IAAIwF,MAAM,CAACS,MAAM,KAAK,QAAQ,EAAE;QACrCF,WAAW,CAAC9F,aAAa,EAAE;OAC5B,MAAM,IAAIuF,MAAM,CAACS,MAAM,KAAK,MAAM,EAAE;QACnCF,WAAW,CAAC1G,WAAW,EAAE;QACzB0G,WAAW,CAAC/F,cAAc,EAAE,CAAC,CAAC;;MAGhC;MACA+F,WAAW,CAACpG,oBAAoB,GAAGuG,IAAI,CAACC,KAAK,CAC1CJ,WAAW,CAAC/F,cAAc,GAAG+F,WAAW,CAAC7F,YAAY,GAAI,GAAG,CAC9D;IACH,CAAC,CAAC;IAEF,IAAI,CAACsB,iBAAiB,GAAG4E,KAAK,CAACC,IAAI,CAAChB,UAAU,CAACiB,MAAM,EAAE,CAAC;IACxD,IAAI,CAACrF,aAAa,GAAG,IAAI,CAACO,iBAAiB,CAACC,MAAM;EACpD;EAEAwD,0BAA0BA,CAAA;IACxB,IAAI,IAAI,CAACzD,iBAAiB,CAACC,MAAM,KAAK,CAAC,EAAE;MACvC,IAAI,CAACT,iBAAiB,GAAG,CAAC;MAC1B;;IAGF,MAAMuF,eAAe,GAAG,IAAI,CAAC/E,iBAAiB,CAACgF,MAAM,CACnD,CAACC,GAAG,EAAEjB,MAAM,KAAKiB,GAAG,GAAGjB,MAAM,CAAC7F,oBAAoB,EAAE,CAAC,CACtD;IACD,IAAI,CAACqB,iBAAiB,GAAGkF,IAAI,CAACC,KAAK,CAACI,eAAe,GAAG,IAAI,CAAC/E,iBAAiB,CAACC,MAAM,CAAC;EACtF;EAEAI,uBAAuBA,CAAA;IACrB,IAAI,CAACiB,qBAAqB,GAAG,CAAC,IAAI,CAACA,qBAAqB;IACxD,IAAI,IAAI,CAACA,qBAAqB,IAAI,IAAI,CAACtB,iBAAiB,CAACC,MAAM,KAAK,CAAC,EAAE;MACrE,IAAI,CAACoD,qBAAqB,EAAE;;EAEhC;EAEA/F,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC4E,SAAS,EAAE;MAClB,IAAI,CAACJ,MAAM,CAACc,QAAQ,CAAC,CAAC,gCAAgC,EAAE,IAAI,CAACV,SAAS,CAAC,CAAC;;EAE5E;EAEAR,MAAMA,CAAA;IACJ,IAAI,CAACI,MAAM,CAACc,QAAQ,CAAC,CAAC,2BAA2B,CAAC,CAAC;EACrD;EAEA1E,kBAAkBA,CAACgH,UAAkB;IACnC,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO,WAAW;IACxC,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO,MAAM;IACnC,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO,SAAS;IACtC,OAAO,MAAM;EACf;EAEA/D,eAAeA,CAACX,OAAa;IAC3B,IAAIA,OAAO,EAAE2E,OAAO,IAAI,OAAO3E,OAAO,CAAC2E,OAAO,KAAK,QAAQ,EAAE;MAC3D,OAAO3E,OAAO,CAAC2E,OAAO,CAACC,OAAO,IAAI,EAAE;;IAEtC,OAAO,EAAE;EACX;EAEAlE,YAAYA,CAACV,OAAa;IACxB,IAAIA,OAAO,EAAE2E,OAAO,IAAI,OAAO3E,OAAO,CAAC2E,OAAO,KAAK,QAAQ,EAAE;MAC3D,OAAO3E,OAAO,CAAC2E,OAAO,CAACE,SAAS,IAAI,KAAK;;IAE3C,OAAO,KAAK;EACd;EAEA,IAAI3F,gBAAgBA,CAAA;IAClB,MAAM4F,UAAU,GAAG,CAAC,IAAI,CAACvG,WAAW,GAAG,CAAC,IAAI,IAAI,CAACsD,YAAY;IAC7D,OAAO,IAAI,CAACrC,iBAAiB,CAACuF,KAAK,CAACD,UAAU,EAAEA,UAAU,GAAG,IAAI,CAACjD,YAAY,CAAC;EACjF;EAEA,IAAIjD,UAAUA,CAAA;IACZ,OAAOsF,IAAI,CAACc,IAAI,CAAC,IAAI,CAACxF,iBAAiB,CAACC,MAAM,GAAG,IAAI,CAACoC,YAAY,CAAC;EACrE;EAEAvD,UAAUA,CAAC2G,IAAY;IACrB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAACrG,UAAU,EAAE;MACxC,IAAI,CAACL,WAAW,GAAG0G,IAAI;;EAE3B;EAAC,QAAAC,CAAA,G;qBA3LU/D,oBAAoB,EAAA9E,EAAA,CAAA8I,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAhJ,EAAA,CAAA8I,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAjJ,EAAA,CAAA8I,iBAAA,CAAAI,EAAA,CAAAC,WAAA,GAAAnJ,EAAA,CAAA8I,iBAAA,CAAAM,EAAA,CAAAC,iBAAA,GAAArJ,EAAA,CAAA8I,iBAAA,CAAAQ,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAApB1E,oBAAoB;IAAA2E,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCjCjC/J,EAAA,CAAAC,cAAA,aAAoC;QAIND,EAAA,CAAAE,UAAA,mBAAA+J,sDAAA;UAAA,OAASD,GAAA,CAAAnF,MAAA,EAAQ;QAAA,EAAC;QACxC7E,EAAA,CAAAC,cAAA,eAAU;QAAAD,EAAA,CAAAU,MAAA,iBAAU;QAAAV,EAAA,CAAAW,YAAA,EAAW;QAEjCX,EAAA,CAAAC,cAAA,aAA2B;QAEMD,EAAA,CAAAU,MAAA,aAAM;QAAAV,EAAA,CAAAW,YAAA,EAAW;QAC9CX,EAAA,CAAAU,MAAA,yBACF;QAAAV,EAAA,CAAAW,YAAA,EAAK;QACLX,EAAA,CAAAC,cAAA,YAAyB;QAAAD,EAAA,CAAAU,MAAA,qEAA6D;QAAAV,EAAA,CAAAW,YAAA,EAAI;QAG9FX,EAAA,CAAAiB,UAAA,KAAAiJ,oCAAA,iBAKM;QACRlK,EAAA,CAAAW,YAAA,EAAM;QAGNX,EAAA,CAAAiB,UAAA,KAAAkJ,oCAAA,iBAGM;QAGNnK,EAAA,CAAAiB,UAAA,KAAAmJ,oCAAA,oBAqLM;QAGNpK,EAAA,CAAAiB,UAAA,KAAAoJ,oCAAA,mBAQM;QACRrK,EAAA,CAAAW,YAAA,EAAM;;;QAhN2BX,EAAA,CAAAa,SAAA,IAAa;QAAbb,EAAA,CAAAmB,UAAA,SAAA6I,GAAA,CAAArG,OAAA,CAAa;QAStC3D,EAAA,CAAAa,SAAA,GAAa;QAAbb,EAAA,CAAAmB,UAAA,SAAA6I,GAAA,CAAA1E,OAAA,CAAa;QAMbtF,EAAA,CAAAa,SAAA,GAAyB;QAAzBb,EAAA,CAAAmB,UAAA,UAAA6I,GAAA,CAAA1E,OAAA,IAAA0E,GAAA,CAAArG,OAAA,CAAyB;QAwLzB3D,EAAA,CAAAa,SAAA,GAA0B;QAA1Bb,EAAA,CAAAmB,UAAA,UAAA6I,GAAA,CAAA1E,OAAA,KAAA0E,GAAA,CAAArG,OAAA,CAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}