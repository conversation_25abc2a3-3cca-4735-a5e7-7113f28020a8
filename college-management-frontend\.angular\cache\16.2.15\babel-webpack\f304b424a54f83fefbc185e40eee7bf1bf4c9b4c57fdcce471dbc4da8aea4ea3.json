{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction EnterCodeManuallyComponent_p_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"We sent a verification code to \");\n    i0.ɵɵelementStart(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.user.email);\n  }\n}\nfunction EnterCodeManuallyComponent_div_18_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"OTP is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EnterCodeManuallyComponent_div_18_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Please enter a valid 6-digit OTP\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EnterCodeManuallyComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtemplate(1, EnterCodeManuallyComponent_div_18_small_1_Template, 2, 0, \"small\", 9);\n    i0.ɵɵtemplate(2, EnterCodeManuallyComponent_div_18_small_2_Template, 2, 0, \"small\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r1.otpForm.get(\"otp\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r1.otpForm.get(\"otp\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"pattern\"]);\n  }\n}\nfunction EnterCodeManuallyComponent_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 31);\n  }\n}\nfunction EnterCodeManuallyComponent_span_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 32);\n  }\n}\nexport let EnterCodeManuallyComponent = /*#__PURE__*/(() => {\n  class EnterCodeManuallyComponent {\n    constructor(fb, authService, router) {\n      this.fb = fb;\n      this.authService = authService;\n      this.router = router;\n      this.loading = false;\n      this.resendLoading = false;\n      this.user = null;\n      this.otpId = null;\n    }\n    ngOnInit() {\n      // Get user data from localStorage (set during forgot password or signup)\n      const resetUser = localStorage.getItem('resetUser');\n      const signupUser = localStorage.getItem('signupUser');\n      if (resetUser) {\n        this.user = JSON.parse(resetUser);\n      } else if (signupUser) {\n        this.user = JSON.parse(signupUser);\n      }\n      if (!this.user) {\n        // If no user data, redirect to login\n        this.router.navigate(['/auth']);\n        return;\n      }\n      this.otpForm = this.fb.group({\n        otp: ['', [Validators.required, Validators.pattern(/^\\d{6}$/)]]\n      });\n    }\n    verifyOtp() {\n      if (this.otpForm.valid && this.user) {\n        this.loading = true;\n        const otp = this.otpForm.value.otp;\n        this.authService.verifyOtp(this.user._id, otp).subscribe({\n          next: response => {\n            this.loading = false;\n            if (response.success) {\n              Swal.fire({\n                title: '<h1 class=\"mb-4\">OTP Verified!</h1>',\n                html: '<p>Your OTP has been successfully verified. <br> You can now reset your password.</p>',\n                icon: 'success',\n                confirmButtonText: 'Reset Password',\n                confirmButtonColor: '#29578c'\n              }).then(result => {\n                if (result.isConfirmed) {\n                  // Store user data for password reset\n                  localStorage.setItem('verifiedUser', JSON.stringify(this.user));\n                  this.router.navigate(['/auth/reset-password']);\n                }\n              });\n            }\n          },\n          error: error => {\n            this.loading = false;\n            console.error('OTP verification error:', error);\n            const errorMessage = error.error?.error || error.error?.message || 'Invalid OTP. Please try again.';\n            Swal.fire({\n              title: 'Verification Failed',\n              text: errorMessage,\n              icon: 'error',\n              confirmButtonColor: '#29578c'\n            });\n          }\n        });\n      } else {\n        Swal.fire({\n          title: 'Invalid OTP',\n          text: 'Please enter a valid 6-digit OTP.',\n          icon: 'warning',\n          confirmButtonColor: '#29578c'\n        });\n      }\n    }\n    resendOtp() {\n      if (!this.user) return;\n      this.resendLoading = true;\n      const resendData = {\n        userId: this.user._id,\n        ...(this.otpId && {\n          otpId: this.otpId\n        })\n      };\n      this.authService.resendOtp(resendData).subscribe({\n        next: response => {\n          this.resendLoading = false;\n          if (response.success) {\n            if (response.otpId) {\n              this.otpId = response.otpId;\n            }\n            Swal.fire({\n              title: 'OTP Resent!',\n              text: 'A new OTP has been sent to your email.',\n              icon: 'success',\n              confirmButtonColor: '#29578c'\n            });\n          }\n        },\n        error: error => {\n          this.resendLoading = false;\n          console.error('Resend OTP error:', error);\n          const errorMessage = error.error?.message || 'Failed to resend OTP. Please try again.';\n          Swal.fire({\n            title: 'Resend Failed',\n            text: errorMessage,\n            icon: 'error',\n            confirmButtonColor: '#29578c'\n          });\n        }\n      });\n    }\n    // Legacy method for backward compatibility\n    verifyemail() {\n      this.verifyOtp();\n    }\n    static #_ = this.ɵfac = function EnterCodeManuallyComponent_Factory(t) {\n      return new (t || EnterCodeManuallyComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EnterCodeManuallyComponent,\n      selectors: [[\"app-enter-code-manually\"]],\n      decls: 44,\n      vars: 11,\n      consts: [[1, \"container-fluid\"], [1, \"row\", \"vh-100\"], [1, \"col-lg-5\", \"d-flex\", \"flex-column\", \"justify-content-center\", \"align-items-center\", \"p-3\", \"bg-white\"], [1, \"w-100\", 2, \"max-width\", \"400px\"], [1, \"text-center\"], [1, \"mb-4\"], [\"src\", \"../../../assets/images/logo.jpeg\"], [1, \"d-flex\", \"justify-content-center\", \"mt-5\", \"mb-2\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"fa-envelope\", \"key\"], [4, \"ngIf\"], [3, \"formGroup\", \"ngSubmit\"], [\"for\", \"otp\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"otp\", \"formControlName\", \"otp\", \"placeholder\", \"000000\", \"maxlength\", \"6\", 1, \"form-control\", \"text-center\", 2, \"font-size\", \"1.5rem\", \"letter-spacing\", \"0.5rem\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [1, \"d-grid\", \"gap-2\"], [\"type\", \"submit\", 1, \"btn\", \"submit\", 3, \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", \"role\", \"status\", \"aria-hidden\", \"true\", 4, \"ngIf\"], [1, \"mb-3\", \"mt-3\", \"text-center\"], [\"type\", \"button\", 1, \"btn\", \"resend\", 3, \"disabled\", \"click\"], [\"class\", \"spinner-border spinner-border-sm me-1\", \"role\", \"status\", \"aria-hidden\", \"true\", 4, \"ngIf\"], [1, \"mt-3\", \"text-center\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"fa-arrow-left\"], [\"routerLink\", \"/auth\"], [1, \"col-lg-7\", \"d-none\", \"d-lg-flex\", \"flex-column\", \"align-items-start\", \"bg-light\", \"position-relative\"], [1, \"text-start\", \"p-5\", \"w-100\"], [1, \"blockquote\"], [1, \"blockquote-footer\"], [\"title\", \"Source Title\"], [1, \"image-container\"], [\"src\", \"../../../assets/images/background.jpeg\", \"alt\", \"Stats graph\", 1, \"img-fluid\", \"position-absolute\"], [1, \"invalid-feedback\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-1\"]],\n      template: function EnterCodeManuallyComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"h2\", 5);\n          i0.ɵɵelement(6, \"img\", 6);\n          i0.ɵɵtext(7, \" GPGC (Swabi)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 7);\n          i0.ɵɵelement(9, \"i\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"h1\", 5);\n          i0.ɵɵtext(11, \"Enter Verification Code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, EnterCodeManuallyComponent_p_12_Template, 4, 1, \"p\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"form\", 10);\n          i0.ɵɵlistener(\"ngSubmit\", function EnterCodeManuallyComponent_Template_form_ngSubmit_13_listener() {\n            return ctx.verifyOtp();\n          });\n          i0.ɵɵelementStart(14, \"div\", 5)(15, \"label\", 11);\n          i0.ɵɵtext(16, \"Enter 6-digit OTP\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(17, \"input\", 12);\n          i0.ɵɵtemplate(18, EnterCodeManuallyComponent_div_18_Template, 3, 2, \"div\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 14)(20, \"button\", 15);\n          i0.ɵɵtemplate(21, EnterCodeManuallyComponent_span_21_Template, 1, 0, \"span\", 16);\n          i0.ɵɵtext(22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"p\", 17);\n          i0.ɵɵtext(24, \" Don't receive the email? \");\n          i0.ɵɵelementStart(25, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function EnterCodeManuallyComponent_Template_button_click_25_listener() {\n            return ctx.resendOtp();\n          });\n          i0.ɵɵtemplate(26, EnterCodeManuallyComponent_span_26_Template, 1, 0, \"span\", 19);\n          i0.ɵɵtext(27);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"p\", 20);\n          i0.ɵɵelement(29, \"i\", 21);\n          i0.ɵɵtext(30, \" \\u00A0 \");\n          i0.ɵɵelementStart(31, \"a\", 22);\n          i0.ɵɵtext(32, \"Back to log in\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(33, \"div\", 23)(34, \"div\", 24)(35, \"blockquote\", 25)(36, \"h2\", 5);\n          i0.ɵɵtext(37, \"College management system Login page\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"footer\", 26);\n          i0.ɵɵtext(39, \"Name\");\n          i0.ɵɵelementStart(40, \"cite\", 27);\n          i0.ɵɵtext(41, \"Owner ~ GPGC SWABI\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(42, \"div\", 28);\n          i0.ɵɵelement(43, \"img\", 29);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          let tmp_2_0;\n          let tmp_3_0;\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngIf\", ctx.user);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.otpForm);\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_2_0 = ctx.otpForm.get(\"otp\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.otpForm.get(\"otp\")) == null ? null : tmp_2_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx.otpForm.get(\"otp\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.otpForm.get(\"otp\")) == null ? null : tmp_3_0.touched));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.loading || ctx.otpForm.invalid);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.loading ? \"Verifying...\" : \"Verify OTP\", \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.resendLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.resendLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.resendLoading ? \"Sending...\" : \"Click to resend\", \" \");\n        }\n      },\n      dependencies: [i4.NgIf, i3.RouterLink, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MaxLengthValidator, i1.FormGroupDirective, i1.FormControlName],\n      styles: [\"body[_ngcontent-%COMP%], html[_ngcontent-%COMP%]{margin:0;padding:0;overflow-x:hidden;height:100%}.image-container[_ngcontent-%COMP%]{width:100%;height:100%;position:relative}.image-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:80%;height:auto;position:absolute;right:0;bottom:0;object-fit:contain}.submit[_ngcontent-%COMP%]{background-color:#29578c;color:#fff}.forgot[_ngcontent-%COMP%]{color:#29578c;text-decoration:none;font-weight:700}a[_ngcontent-%COMP%]{cursor:pointer;text-decoration:none;color:#000;font-weight:600}.key[_ngcontent-%COMP%]{background-color:#f4ebff!important;border-radius:50%;height:50px;width:50px;display:flex;justify-content:center;align-items:center;color:#29578c}.resend[_ngcontent-%COMP%]{background-color:none;color:#29578c;font-weight:700;font-family:serif}.number-box[_ngcontent-%COMP%]{border:2px solid #29578c;border-radius:8px;text-align:center;font-size:2rem;color:#29578c;padding:10px}.number-box[_ngcontent-%COMP%]:hover{box-shadow:0 0 10px 3px #8a2bb280}\"]\n    });\n  }\n  return EnterCodeManuallyComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}