{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class RoleService {\n  constructor() {\n    this.userRoleSubject = new BehaviorSubject('');\n    this.userRole$ = this.userRoleSubject.asObservable();\n    // Initialize user role from local storage\n    const storedRole = localStorage.getItem('userRole');\n    if (storedRole) {\n      this.userRoleSubject.next(storedRole);\n    }\n  }\n  setRole(role) {\n    this.userRoleSubject.next(role);\n    // Store user role in local storage\n    localStorage.setItem('userRole', role);\n  }\n  getRole() {\n    return this.userRoleSubject.getValue();\n  }\n  logout() {\n    // Remove user role and token from local storage\n    localStorage.removeItem('userRole');\n    localStorage.removeItem('user');\n    localStorage.removeItem('token');\n    this.userRoleSubject.next('');\n  }\n  // Check if user has specific role\n  hasRole(role) {\n    return this.getRole() === role;\n  }\n  // Check if user has any of the specified roles\n  hasAnyRole(roles) {\n    const currentRole = this.getRole();\n    return roles.includes(currentRole);\n  }\n  // Check if user is authenticated\n  isAuthenticated() {\n    const token = localStorage.getItem('token');\n    const role = this.getRole();\n    return !!(token && role);\n  }\n  static #_ = this.ɵfac = function RoleService_Factory(t) {\n    return new (t || RoleService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: RoleService,\n    factory: RoleService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["BehaviorSubject", "RoleService", "constructor", "userRoleSubject", "userRole$", "asObservable", "storedRole", "localStorage", "getItem", "next", "setRole", "role", "setItem", "getRole", "getValue", "logout", "removeItem", "hasRole", "hasAnyRole", "roles", "currentRole", "includes", "isAuthenticated", "token", "_", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\services\\role.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { BehaviorSubject } from 'rxjs';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class RoleService {\r\n  private userRoleSubject = new BehaviorSubject<string>('');\r\n\r\n  userRole$ = this.userRoleSubject.asObservable();\r\n\r\n  constructor() {\r\n    // Initialize user role from local storage\r\n    const storedRole = localStorage.getItem('userRole');\r\n    if (storedRole) {\r\n      this.userRoleSubject.next(storedRole);\r\n    }\r\n  }\r\n\r\n  setRole(role: string): void {\r\n    this.userRoleSubject.next(role);\r\n    // Store user role in local storage\r\n    localStorage.setItem('userRole', role);\r\n  }\r\n\r\n  getRole(): string {\r\n    return this.userRoleSubject.getValue();\r\n  }\r\n\r\n  logout(): void {\r\n    // Remove user role and token from local storage\r\n    localStorage.removeItem('userRole');\r\n    localStorage.removeItem('user');\r\n    localStorage.removeItem('token');\r\n    this.userRoleSubject.next('');\r\n  }\r\n\r\n  // Check if user has specific role\r\n  hasRole(role: string): boolean {\r\n    return this.getRole() === role;\r\n  }\r\n\r\n  // Check if user has any of the specified roles\r\n  hasAnyRole(roles: string[]): boolean {\r\n    const currentRole = this.getRole();\r\n    return roles.includes(currentRole);\r\n  }\r\n\r\n  // Check if user is authenticated\r\n  isAuthenticated(): boolean {\r\n    const token = localStorage.getItem('token');\r\n    const role = this.getRole();\r\n    return !!(token && role);\r\n  }\r\n}"], "mappings": "AACA,SAASA,eAAe,QAAQ,MAAM;;AAKtC,OAAM,MAAOC,WAAW;EAKtBC,YAAA;IAJQ,KAAAC,eAAe,GAAG,IAAIH,eAAe,CAAS,EAAE,CAAC;IAEzD,KAAAI,SAAS,GAAG,IAAI,CAACD,eAAe,CAACE,YAAY,EAAE;IAG7C;IACA,MAAMC,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IACnD,IAAIF,UAAU,EAAE;MACd,IAAI,CAACH,eAAe,CAACM,IAAI,CAACH,UAAU,CAAC;;EAEzC;EAEAI,OAAOA,CAACC,IAAY;IAClB,IAAI,CAACR,eAAe,CAACM,IAAI,CAACE,IAAI,CAAC;IAC/B;IACAJ,YAAY,CAACK,OAAO,CAAC,UAAU,EAAED,IAAI,CAAC;EACxC;EAEAE,OAAOA,CAAA;IACL,OAAO,IAAI,CAACV,eAAe,CAACW,QAAQ,EAAE;EACxC;EAEAC,MAAMA,CAAA;IACJ;IACAR,YAAY,CAACS,UAAU,CAAC,UAAU,CAAC;IACnCT,YAAY,CAACS,UAAU,CAAC,MAAM,CAAC;IAC/BT,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;IAChC,IAAI,CAACb,eAAe,CAACM,IAAI,CAAC,EAAE,CAAC;EAC/B;EAEA;EACAQ,OAAOA,CAACN,IAAY;IAClB,OAAO,IAAI,CAACE,OAAO,EAAE,KAAKF,IAAI;EAChC;EAEA;EACAO,UAAUA,CAACC,KAAe;IACxB,MAAMC,WAAW,GAAG,IAAI,CAACP,OAAO,EAAE;IAClC,OAAOM,KAAK,CAACE,QAAQ,CAACD,WAAW,CAAC;EACpC;EAEA;EACAE,eAAeA,CAAA;IACb,MAAMC,KAAK,GAAGhB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMG,IAAI,GAAG,IAAI,CAACE,OAAO,EAAE;IAC3B,OAAO,CAAC,EAAEU,KAAK,IAAIZ,IAAI,CAAC;EAC1B;EAAC,QAAAa,CAAA,G;qBA/CUvB,WAAW;EAAA;EAAA,QAAAwB,EAAA,G;WAAXxB,WAAW;IAAAyB,OAAA,EAAXzB,WAAW,CAAA0B,IAAA;IAAAC,UAAA,EAFV;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}