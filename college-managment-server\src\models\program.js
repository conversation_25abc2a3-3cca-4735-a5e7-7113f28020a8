const mongoose = require('mongoose');

const programSchema = new mongoose.Schema({
  name: { 
    type: String, 
    required: [true, 'Program name is required'],
    enum: ['BS', 'Intermediate', 'MS', 'PhD'],
    unique: true
  },
  fullName: {
    type: String,
    required: [true, 'Full program name is required']
  },
  description: { 
    type: String 
  },
  duration: {
    type: Number,
    required: [true, 'Program duration is required'],
    min: 1
  },
  durationUnit: {
    type: String,
    enum: ['years', 'semesters'],
    default: 'years'
  },
  totalSemesters: {
    type: Number,
    required: [true, 'Total semesters is required'],
    min: 1
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, { timestamps: true });

const Program = mongoose.model('Program', programSchema);

module.exports = Program;
