{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class DashboardService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = environment.apiUrl;\n  }\n  // Principal Dashboard\n  getPrincipalDashboardStats() {\n    return this.http.get(`${this.apiUrl}/dashboard/principal/stats`);\n  }\n  getAllUsersForPrincipal(filters) {\n    let params = new HttpParams();\n    if (filters) {\n      Object.keys(filters).forEach(key => {\n        const value = filters[key];\n        if (value !== undefined && value !== null) {\n          params = params.set(key, value.toString());\n        }\n      });\n    }\n    return this.http.get(`${this.apiUrl}/dashboard/principal/users`, {\n      params\n    });\n  }\n  getAttendanceSummary(filters) {\n    let params = new HttpParams();\n    if (filters) {\n      Object.keys(filters).forEach(key => {\n        const value = filters[key];\n        if (value !== undefined && value !== null) {\n          params = params.set(key, value.toString());\n        }\n      });\n    }\n    return this.http.get(`${this.apiUrl}/dashboard/principal/attendance`, {\n      params\n    });\n  }\n  // Teacher Dashboard\n  getTeacherDashboard(teacherId, academicYear) {\n    let params = new HttpParams();\n    if (academicYear) params = params.set('academicYear', academicYear);\n    return this.http.get(`${this.apiUrl}/dashboard/teacher/${teacherId}`, {\n      params\n    });\n  }\n  // Student Dashboard\n  getStudentDashboard(studentId, academicYear) {\n    let params = new HttpParams();\n    if (academicYear) params = params.set('academicYear', academicYear);\n    return this.http.get(`${this.apiUrl}/dashboard/student/${studentId}`, {\n      params\n    });\n  }\n  static #_ = this.ɵfac = function DashboardService_Factory(t) {\n    return new (t || DashboardService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: DashboardService,\n    factory: DashboardService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpParams", "environment", "DashboardService", "constructor", "http", "apiUrl", "getPrincipalDashboardStats", "get", "getAllUsersForPrincipal", "filters", "params", "Object", "keys", "for<PERSON>ach", "key", "value", "undefined", "set", "toString", "getAttendanceSummary", "getTeacherDashboard", "teacherId", "academicYear", "getStudentDashboard", "studentId", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\services\\dashboard.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { environment } from 'src/environments/environment';\r\nimport { PrincipalDashboard, TeacherDashboard, StudentDashboard } from '../models/dashboard';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class DashboardService {\r\n  private apiUrl = environment.apiUrl;\r\n\r\n  constructor(private http: HttpClient) { }\r\n\r\n  // Principal Dashboard\r\n  getPrincipalDashboardStats(): Observable<any> {\r\n    return this.http.get<any>(`${this.apiUrl}/dashboard/principal/stats`);\r\n  }\r\n\r\n  getAllUsersForPrincipal(filters?: {\r\n    role?: string;\r\n    program?: string;\r\n    department?: string;\r\n    semester?: number;\r\n    isActive?: boolean;\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n  }): Observable<any> {\r\n    let params = new HttpParams();\r\n    \r\n    if (filters) {\r\n      Object.keys(filters).forEach(key => {\r\n        const value = (filters as any)[key];\r\n        if (value !== undefined && value !== null) {\r\n          params = params.set(key, value.toString());\r\n        }\r\n      });\r\n    }\r\n\r\n    return this.http.get<any>(`${this.apiUrl}/dashboard/principal/users`, { params });\r\n  }\r\n\r\n  getAttendanceSummary(filters?: {\r\n    program?: string;\r\n    department?: string;\r\n    class?: string;\r\n    subject?: string;\r\n    startDate?: string;\r\n    endDate?: string;\r\n    page?: number;\r\n    limit?: number;\r\n  }): Observable<any> {\r\n    let params = new HttpParams();\r\n    \r\n    if (filters) {\r\n      Object.keys(filters).forEach(key => {\r\n        const value = (filters as any)[key];\r\n        if (value !== undefined && value !== null) {\r\n          params = params.set(key, value.toString());\r\n        }\r\n      });\r\n    }\r\n\r\n    return this.http.get<any>(`${this.apiUrl}/dashboard/principal/attendance`, { params });\r\n  }\r\n\r\n  // Teacher Dashboard\r\n  getTeacherDashboard(teacherId: string, academicYear?: string): Observable<any> {\r\n    let params = new HttpParams();\r\n    if (academicYear) params = params.set('academicYear', academicYear);\r\n\r\n    return this.http.get<any>(`${this.apiUrl}/dashboard/teacher/${teacherId}`, { params });\r\n  }\r\n\r\n  // Student Dashboard\r\n  getStudentDashboard(studentId: string, academicYear?: string): Observable<any> {\r\n    let params = new HttpParams();\r\n    if (academicYear) params = params.set('academicYear', academicYear);\r\n\r\n    return this.http.get<any>(`${this.apiUrl}/dashboard/student/${studentId}`, { params });\r\n  }\r\n}\r\n"], "mappings": "AACA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,WAAW,QAAQ,8BAA8B;;;AAM1D,OAAM,MAAOC,gBAAgB;EAG3BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAGJ,WAAW,CAACI,MAAM;EAEK;EAExC;EACAC,0BAA0BA,CAAA;IACxB,OAAO,IAAI,CAACF,IAAI,CAACG,GAAG,CAAM,GAAG,IAAI,CAACF,MAAM,4BAA4B,CAAC;EACvE;EAEAG,uBAAuBA,CAACC,OASvB;IACC,IAAIC,MAAM,GAAG,IAAIV,UAAU,EAAE;IAE7B,IAAIS,OAAO,EAAE;MACXE,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC,CAACI,OAAO,CAACC,GAAG,IAAG;QACjC,MAAMC,KAAK,GAAIN,OAAe,CAACK,GAAG,CAAC;QACnC,IAAIC,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,EAAE;UACzCL,MAAM,GAAGA,MAAM,CAACO,GAAG,CAACH,GAAG,EAAEC,KAAK,CAACG,QAAQ,EAAE,CAAC;;MAE9C,CAAC,CAAC;;IAGJ,OAAO,IAAI,CAACd,IAAI,CAACG,GAAG,CAAM,GAAG,IAAI,CAACF,MAAM,4BAA4B,EAAE;MAAEK;IAAM,CAAE,CAAC;EACnF;EAEAS,oBAAoBA,CAACV,OASpB;IACC,IAAIC,MAAM,GAAG,IAAIV,UAAU,EAAE;IAE7B,IAAIS,OAAO,EAAE;MACXE,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC,CAACI,OAAO,CAACC,GAAG,IAAG;QACjC,MAAMC,KAAK,GAAIN,OAAe,CAACK,GAAG,CAAC;QACnC,IAAIC,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,EAAE;UACzCL,MAAM,GAAGA,MAAM,CAACO,GAAG,CAACH,GAAG,EAAEC,KAAK,CAACG,QAAQ,EAAE,CAAC;;MAE9C,CAAC,CAAC;;IAGJ,OAAO,IAAI,CAACd,IAAI,CAACG,GAAG,CAAM,GAAG,IAAI,CAACF,MAAM,iCAAiC,EAAE;MAAEK;IAAM,CAAE,CAAC;EACxF;EAEA;EACAU,mBAAmBA,CAACC,SAAiB,EAAEC,YAAqB;IAC1D,IAAIZ,MAAM,GAAG,IAAIV,UAAU,EAAE;IAC7B,IAAIsB,YAAY,EAAEZ,MAAM,GAAGA,MAAM,CAACO,GAAG,CAAC,cAAc,EAAEK,YAAY,CAAC;IAEnE,OAAO,IAAI,CAAClB,IAAI,CAACG,GAAG,CAAM,GAAG,IAAI,CAACF,MAAM,sBAAsBgB,SAAS,EAAE,EAAE;MAAEX;IAAM,CAAE,CAAC;EACxF;EAEA;EACAa,mBAAmBA,CAACC,SAAiB,EAAEF,YAAqB;IAC1D,IAAIZ,MAAM,GAAG,IAAIV,UAAU,EAAE;IAC7B,IAAIsB,YAAY,EAAEZ,MAAM,GAAGA,MAAM,CAACO,GAAG,CAAC,cAAc,EAAEK,YAAY,CAAC;IAEnE,OAAO,IAAI,CAAClB,IAAI,CAACG,GAAG,CAAM,GAAG,IAAI,CAACF,MAAM,sBAAsBmB,SAAS,EAAE,EAAE;MAAEd;IAAM,CAAE,CAAC;EACxF;EAAC,QAAAe,CAAA,G;qBAxEUvB,gBAAgB,EAAAwB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAhB5B,gBAAgB;IAAA6B,OAAA,EAAhB7B,gBAAgB,CAAA8B,IAAA;IAAAC,UAAA,EAFf;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}