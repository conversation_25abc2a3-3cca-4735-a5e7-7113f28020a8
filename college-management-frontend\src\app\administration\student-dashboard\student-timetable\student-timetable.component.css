.student-timetable-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.header-content h1 {
  margin: 0;
  color: #333;
  font-size: 2rem;
  font-weight: 500;
}

.header-content p {
  margin: 5px 0 0 0;
  color: #666;
  font-size: 1rem;
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-container p, .error-container p {
  margin-top: 20px;
  color: #666;
}

.error-container mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  color: #f44336;
  margin-bottom: 20px;
}

.timetable-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.current-day-info {
  margin-bottom: 20px;
}

.current-day-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.current-day-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.current-day-content mat-icon {
  font-size: 32px;
  width: 32px;
  height: 32px;
}

.day-info h3 {
  margin: 0 0 5px 0;
  font-size: 1.3rem;
  font-weight: 500;
}

.day-info p {
  margin: 0;
  opacity: 0.9;
  font-size: 0.9rem;
}

.timetable-wrapper {
  overflow-x: auto;
}

.timetable-card {
  min-width: 800px;
}

.timetable-grid {
  display: grid;
  grid-template-columns: 150px repeat(5, 1fr);
  gap: 1px;
  background: #e0e0e0;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.timetable-header {
  display: contents;
}

.time-header, .day-header {
  background: #2196f3;
  color: white;
  padding: 15px 10px;
  font-weight: 600;
  text-align: center;
  font-size: 0.9rem;
}

.day-header.current-day {
  background: #ff9800;
}

.timetable-row {
  display: contents;
}

.timetable-row.current-time .time-slot {
  background: #fff3e0;
  border-left: 4px solid #ff9800;
}

.time-slot {
  background: #f5f5f5;
  padding: 15px 10px;
  font-weight: 500;
  font-size: 0.8rem;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1px solid #e0e0e0;
}

.schedule-cell {
  background: white;
  padding: 8px;
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: background-color 0.2s ease;
}

.schedule-cell.current-day {
  background: #f8f9ff;
}

.schedule-cell.has-class {
  background: #f8f9fa;
}

.schedule-cell.break-time {
  background: #fff8e1;
}

.schedule-cell:hover {
  background: #f0f0f0;
}

.class-info {
  width: 100%;
  text-align: center;
}

.subject-name {
  font-weight: 600;
  font-size: 0.9rem;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.2;
}

.subject-name.break {
  color: #ff9800;
  font-size: 0.8rem;
}

.class-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.teacher, .room {
  font-size: 0.7rem;
  color: #666;
  line-height: 1.1;
}

.type {
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
  margin-top: 4px;
}

.type.lecture {
  background: #e3f2fd;
  color: #1976d2;
}

.type.lab {
  background: #e8f5e8;
  color: #2e7d32;
}

.type.tutorial {
  background: #fff3e0;
  color: #f57c00;
}

.break-details {
  font-size: 0.7rem;
  color: #ff9800;
}

.free-period {
  color: #999;
  font-size: 0.8rem;
  font-style: italic;
}

.legend-section {
  margin-top: 20px;
}

.legend-items {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 15px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
}

.legend-color {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.legend-color.lecture {
  background: #e3f2fd;
}

.legend-color.lab {
  background: #e8f5e8;
}

.legend-color.tutorial {
  background: #fff3e0;
}

.legend-color.break {
  background: #fff8e1;
}

.legend-color.free {
  background: #f5f5f5;
}

.quick-actions {
  margin-top: 30px;
}

.quick-actions h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.3rem;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.action-buttons button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
}

.action-buttons button mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
}

@media (max-width: 768px) {
  .student-timetable-container {
    padding: 15px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .timetable-grid {
    grid-template-columns: 100px repeat(5, 1fr);
    font-size: 0.8rem;
  }
  
  .time-header, .day-header {
    padding: 10px 5px;
    font-size: 0.8rem;
  }
  
  .schedule-cell {
    min-height: 60px;
    padding: 4px;
  }
  
  .subject-name {
    font-size: 0.8rem;
  }
  
  .teacher, .room, .type {
    font-size: 0.6rem;
  }
  
  .legend-items {
    flex-direction: column;
    gap: 10px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons button {
    width: 100%;
    justify-content: center;
  }
}
