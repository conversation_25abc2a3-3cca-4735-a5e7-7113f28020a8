{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/user.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common/http\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/icon\";\nfunction AdminStudentListComponent_tr_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const student_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(student_r1.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(student_r1 == null ? null : student_r1.rollNo);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(student_r1 == null ? null : student_r1.regNo);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(student_r1.class);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(student_r1.section);\n  }\n}\nexport let AdminStudentListComponent = /*#__PURE__*/(() => {\n  class AdminStudentListComponent {\n    constructor(userService, router, http) {\n      this.userService = userService;\n      this.router = router;\n      this.http = http;\n      this.searchQuery = '';\n      this.currentPage = 1;\n      this.itemsPerPage = 7;\n      // Dropdown options\n      this.departments = ['Pre-Medical', 'Pre-Engineering', 'Computer Science'];\n      this.classes = ['1st Year', '2nd Year'];\n      this.sections = ['A', 'B', 'C'];\n      // Selected filters\n      this.selectedClass = '';\n      this.selectedDepartment = '';\n      this.selectedSection = '';\n      this.selectedFile = null;\n      this.responseMessage = '';\n      this.students = [];\n    }\n    ngOnInit() {\n      this.fetchStudents();\n    }\n    onFileSelected(event) {\n      this.selectedFile = event.target.files[0];\n    }\n    // Add student\n    onUpload() {\n      if (!this.selectedFile) {\n        alert('Please select a file before uploading');\n        return;\n      }\n      if (this.selectedFile) {\n        const formData = new FormData();\n        formData.append('file', this.selectedFile, this.selectedFile.name);\n        this.http.post('http://localhost:5007/api/v1/bulk-signup', formData).subscribe({\n          next: response => {\n            this.responseMessage = response.success ? `Success: ${response.message}` : `Error: ${response.message}`;\n            this.fetchStudents();\n          },\n          error: error => {\n            console.error('Error uploading the file', error);\n            this.responseMessage = 'Error uploading the file. Please try again.';\n          }\n        });\n      }\n    }\n    fetchStudents() {\n      this.userService.getUsersByRole('Student').subscribe({\n        next: response => {\n          console.log(\"response\", response);\n          // Assuming response contains an array of students\n          this.students = response.users; // Adjust according to your API response\n        },\n\n        error: error => {\n          console.error('Error fetching students:', error);\n        }\n      });\n    }\n    // Filter students based on search query, selected class, department, and section\n    get filteredStudents() {\n      return this.students.filter(student => {\n        const searchLower = this.searchQuery.toLowerCase();\n        const matchesName = student.name?.toLowerCase().includes(searchLower) ?? false;\n        const matchesRollNumber = student.rollNo?.toLowerCase().includes(searchLower) ?? false;\n        const matchesDepartment = student.regNo?.toLowerCase().includes(searchLower) ?? false;\n        const matchesClass = student.class?.toLowerCase().includes(searchLower) ?? false;\n        const matchesSection = student.section?.toLowerCase().includes(searchLower) ?? false;\n        const matchesFilters = (this.selectedClass ? student.class === this.selectedClass : true) && (this.selectedDepartment ? student.regNo === this.selectedDepartment : true) && (this.selectedSection ? student.section === this.selectedSection : true);\n        return (matchesName || matchesRollNumber || matchesDepartment || matchesClass || matchesSection) && matchesFilters;\n      });\n    }\n    // Reset filters\n    resetFilters() {\n      this.selectedClass = '';\n      this.selectedDepartment = '';\n      this.selectedSection = '';\n      this.searchQuery = '';\n    }\n    // Toggle status based on checkbox\n    toggleStatus(student, isChecked) {\n      student.status = isChecked ? 'present' : 'absent';\n    }\n    addnewstudent() {\n      this.router.navigate(['/dashboard/admin/students/add-student']);\n    }\n    get paginatedStudents() {\n      const startIndex = (this.currentPage - 1) * this.itemsPerPage;\n      const endIndex = startIndex + this.itemsPerPage;\n      return this.filteredStudents.slice(startIndex, endIndex);\n    }\n    get totalPages() {\n      return Math.ceil(this.filteredStudents.length / this.itemsPerPage);\n    }\n    nextPage() {\n      if (this.currentPage < this.totalPages) {\n        this.currentPage++;\n      }\n    }\n    previousPage() {\n      if (this.currentPage > 1) {\n        this.currentPage--;\n      }\n    }\n    static #_ = this.ɵfac = function AdminStudentListComponent_Factory(t) {\n      return new (t || AdminStudentListComponent)(i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.HttpClient));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AdminStudentListComponent,\n      selectors: [[\"app-admin-student-list\"]],\n      decls: 48,\n      vars: 6,\n      consts: [[1, \"maindiv\"], [1, \"container\"], [1, \"d-flex\", \"justify-content-between\"], [\"mat-raised-button\", \"\", \"routerLink\", \"/dashboard/admin/students/add-student\"], [1, \"container\", \"row\", \"d-flex\", \"align-items-center\", \"justify-content-center\"], [1, \"col-lg-6\", \"col-md-12\"], [\"for\", \"fileInput\", 1, \"mb-2\"], [1, \"input-group\", \"mb-3\"], [\"type\", \"file\", \"name\", \"file\", \"aria-label\", \"Recipient's username\", \"aria-describedby\", \"button-addon2\", \"accept\", \".xlsx, .xls\", \"required\", \"\", 1, \"form-control\", 3, \"change\"], [\"type\", \"button\", \"id\", \"button-addon2\", 1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"col-lg-6\", \"col-md-12\", \"mb-3\"], [\"for\", \"search\", 1, \"mb-2\"], [\"type\", \"text\", \"placeholder\", \"Search by roll number, name, department, class ...\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"table-container\", \"mb-4\"], [1, \"table-responsive\"], [1, \"tablehead\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"p-3\", \"align-items-center\", \"justify-content-between\"], [1, \"btn\", \"btn-top\", \"border\", 3, \"disabled\", \"click\"], [1, \"page\"]],\n      template: function AdminStudentListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\");\n          i0.ɵɵtext(4, \"Student List\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"button\", 3)(6, \"mat-icon\");\n          i0.ɵɵtext(7, \"add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(8, \" Add New Student \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 4)(10, \"div\", 5)(11, \"label\", 6);\n          i0.ɵɵtext(12, \"Upload Excel File of students\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(13, \"br\");\n          i0.ɵɵelementStart(14, \"div\", 7)(15, \"input\", 8);\n          i0.ɵɵlistener(\"change\", function AdminStudentListComponent_Template_input_change_15_listener($event) {\n            return ctx.onFileSelected($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function AdminStudentListComponent_Template_button_click_16_listener() {\n            return ctx.onUpload();\n          });\n          i0.ɵɵtext(17, \"Submit\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"div\", 10)(19, \"label\", 11);\n          i0.ɵɵtext(20, \"Search\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"input\", 12);\n          i0.ɵɵlistener(\"ngModelChange\", function AdminStudentListComponent_Template_input_ngModelChange_21_listener($event) {\n            return ctx.searchQuery = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(22, \"div\", 13)(23, \"div\", 14)(24, \"table\")(25, \"thead\")(26, \"tr\", 15)(27, \"th\");\n          i0.ɵɵtext(28, \"Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"th\");\n          i0.ɵɵtext(30, \"Roll Number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"th\");\n          i0.ɵɵtext(32, \"Reg-NO\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"th\");\n          i0.ɵɵtext(34, \"Class\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"th\");\n          i0.ɵɵtext(36, \"Section\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(37, \"tbody\");\n          i0.ɵɵtemplate(38, AdminStudentListComponent_tr_38_Template, 11, 5, \"tr\", 16);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(39, \"div\", 17)(40, \"div\")(41, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function AdminStudentListComponent_Template_button_click_41_listener() {\n            return ctx.previousPage();\n          });\n          i0.ɵɵtext(42, \"Previous\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(43, \"div\", 19);\n          i0.ɵɵtext(44);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"div\")(46, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function AdminStudentListComponent_Template_button_click_46_listener() {\n            return ctx.nextPage();\n          });\n          i0.ɵɵtext(47, \"Next\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(21);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"ngForOf\", ctx.paginatedStudents);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.currentPage === 1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate2(\"Page \", ctx.currentPage, \" of \", ctx.totalPages, \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.currentPage === ctx.totalPages);\n        }\n      },\n      dependencies: [i4.NgForOf, i2.RouterLink, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel, i6.MatButton, i7.MatIcon]\n    });\n  }\n  return AdminStudentListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}