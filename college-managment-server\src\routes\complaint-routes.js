const express = require('express');
const router = express.Router();

const {
    createComplaint,
    getAllComplaints,
    getComplaintById,
    updateComplaintStatus,
    addComment,
    getComplaintsByUser,
    deleteComplaint
} = require('../controllers/complaint-controller');

// Routes

// Create a new complaint
router.post('/complaints', createComplaint);

// Get all complaints with filtering and pagination
router.get('/complaints', getAllComplaints);

// Get complaint by ID
router.get('/complaints/:id', getComplaintById);

// Update complaint status
router.put('/complaints/:id/status', updateComplaintStatus);

// Add comment to complaint
router.post('/complaints/:id/comments', addComment);

// Get complaints by user
router.get('/complaints/user/:userId', getComplaintsByUser);

// Delete complaint (soft delete)
router.delete('/complaints/:id', deleteComplaint);

module.exports = router;
