<div class="profile-container">
  <!-- Header -->
  <div class="page-header">
    <h1>Profile Management</h1>
    <p class="subtitle">Manage your personal information and account settings</p>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="loading-container">
    <mat-spinner></mat-spinner>
    <p>Loading profile...</p>
  </div>

  <!-- Profile Content -->
  <div *ngIf="!loading && currentUser" class="profile-content">
    <!-- Profile Summary Card -->
    <mat-card class="profile-summary">
      <mat-card-content>
        <div class="profile-header">
          <div class="profile-avatar">
            <mat-icon>account_circle</mat-icon>
          </div>
          <div class="profile-info">
            <h2>{{ currentUser.name }}</h2>
            <p class="role-badge">{{ currentUser.role }}</p>
            <p class="email">{{ currentUser.email }}</p>
          </div>
          <div class="profile-status">
            <span class="status-badge" [class.active]="currentUser.isActive">
              {{ currentUser.isActive ? 'Active' : 'Inactive' }}
            </span>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Tabs -->
    <mat-tab-group [(selectedIndex)]="selectedTabIndex" class="profile-tabs">
      <!-- Personal Information Tab -->
      <mat-tab label="Personal Information">
        <div class="tab-content">
          <form [formGroup]="profileForm" (ngSubmit)="updateProfile()">
            <mat-card>
              <mat-card-header>
                <mat-card-title>Personal Details</mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <div class="form-grid">
                  <!-- Basic Information -->
                  <mat-form-field appearance="outline">
                    <mat-label>Full Name</mat-label>
                    <input matInput formControlName="name" placeholder="Enter your full name">
                    <mat-error>{{ getErrorMessage('name') }}</mat-error>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Email</mat-label>
                    <input matInput formControlName="email" type="email" placeholder="Enter your email">
                    <mat-error>{{ getErrorMessage('email') }}</mat-error>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Contact Number</mat-label>
                    <input matInput formControlName="contact" placeholder="Enter your contact number">
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Address</mat-label>
                    <textarea matInput formControlName="address" rows="3" placeholder="Enter your address"></textarea>
                  </mat-form-field>

                  <!-- Student-specific fields -->
                  <div *ngIf="isStudent()" class="student-fields">
                    <mat-form-field appearance="outline">
                      <mat-label>Father's Name</mat-label>
                      <input matInput formControlName="father_name" placeholder="Enter father's name">
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                      <mat-label>Registration Number</mat-label>
                      <input matInput formControlName="regNo" placeholder="Enter registration number">
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                      <mat-label>Roll Number</mat-label>
                      <input matInput formControlName="rollNo" placeholder="Enter roll number">
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                      <mat-label>Semester</mat-label>
                      <mat-select formControlName="semester">
                        <mat-option *ngFor="let sem of [1,2,3,4,5,6,7,8]" [value]="sem">
                          Semester {{ sem }}
                        </mat-option>
                      </mat-select>
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                      <mat-label>Academic Year</mat-label>
                      <input matInput formControlName="academicYear" placeholder="e.g., 2023-2024">
                    </mat-form-field>
                  </div>

                  <!-- Teacher-specific fields -->
                  <div *ngIf="isTeacher()" class="teacher-fields">
                    <mat-form-field appearance="outline">
                      <mat-label>Designation</mat-label>
                      <input matInput formControlName="designation" placeholder="Enter your designation">
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                      <mat-label>Position</mat-label>
                      <input matInput formControlName="position" placeholder="Enter your position">
                    </mat-form-field>

                    <mat-checkbox formControlName="isVisiting">Visiting Faculty</mat-checkbox>
                  </div>

                  <!-- Academic Information -->
                  <div class="academic-section">
                    <h3>Academic Information</h3>
                    
                    <mat-form-field appearance="outline">
                      <mat-label>Program</mat-label>
                      <mat-select formControlName="program" (selectionChange)="onProgramChange()">
                        <mat-option *ngFor="let program of programs" [value]="program._id">
                          {{ program.name }} - {{ program.fullName }}
                        </mat-option>
                      </mat-select>
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                      <mat-label>Department</mat-label>
                      <mat-select formControlName="department" (selectionChange)="onDepartmentChange()">
                        <mat-option *ngFor="let dept of filteredDepartments" [value]="dept._id">
                          {{ dept.name }} ({{ dept.code }})
                        </mat-option>
                      </mat-select>
                    </mat-form-field>

                    <mat-form-field appearance="outline" *ngIf="isStudent()">
                      <mat-label>Class</mat-label>
                      <mat-select formControlName="classId">
                        <mat-option *ngFor="let cls of filteredClasses" [value]="cls._id">
                          {{ cls.className }} - {{ cls.section }}
                        </mat-option>
                      </mat-select>
                    </mat-form-field>
                  </div>
                </div>
              </mat-card-content>
              <mat-card-actions>
                <button mat-raised-button color="primary" type="submit" [disabled]="updating || profileForm.invalid">
                  <mat-spinner *ngIf="updating" diameter="20"></mat-spinner>
                  {{ updating ? 'Updating...' : 'Update Profile' }}
                </button>
                <button mat-button type="button" (click)="populateForm()">Reset</button>
              </mat-card-actions>
            </mat-card>
          </form>
        </div>
      </mat-tab>

      <!-- Security Tab -->
      <mat-tab label="Security">
        <div class="tab-content">
          <form [formGroup]="passwordForm" (ngSubmit)="changePassword()">
            <mat-card>
              <mat-card-header>
                <mat-card-title>Change Password</mat-card-title>
                <mat-card-subtitle>Update your account password</mat-card-subtitle>
              </mat-card-header>
              <mat-card-content>
                <div class="password-form">
                  <mat-form-field appearance="outline">
                    <mat-label>Current Password</mat-label>
                    <input matInput formControlName="currentPassword" type="password" placeholder="Enter current password">
                    <mat-error>{{ getErrorMessage('currentPassword', passwordForm) }}</mat-error>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>New Password</mat-label>
                    <input matInput formControlName="newPassword" type="password" placeholder="Enter new password">
                    <mat-error>{{ getErrorMessage('newPassword', passwordForm) }}</mat-error>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Confirm New Password</mat-label>
                    <input matInput formControlName="confirmPassword" type="password" placeholder="Confirm new password">
                    <mat-error>{{ getErrorMessage('confirmPassword', passwordForm) }}</mat-error>
                  </mat-form-field>
                </div>
              </mat-card-content>
              <mat-card-actions>
                <button mat-raised-button color="primary" type="submit" [disabled]="changingPassword || passwordForm.invalid">
                  <mat-spinner *ngIf="changingPassword" diameter="20"></mat-spinner>
                  {{ changingPassword ? 'Changing...' : 'Change Password' }}
                </button>
                <button mat-button type="button" (click)="passwordForm.reset()">Clear</button>
              </mat-card-actions>
            </mat-card>
          </form>
        </div>
      </mat-tab>

      <!-- Account Information Tab -->
      <mat-tab label="Account Info">
        <div class="tab-content">
          <mat-card>
            <mat-card-header>
              <mat-card-title>Account Information</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="account-info">
                <div class="info-item">
                  <span class="label">User ID:</span>
                  <span class="value">{{ currentUser._id }}</span>
                </div>
                <div class="info-item">
                  <span class="label">Role:</span>
                  <span class="value">{{ currentUser.role }}</span>
                </div>
                <div class="info-item">
                  <span class="label">Account Status:</span>
                  <span class="value" [class.active]="currentUser.isActive">
                    {{ currentUser.isActive ? 'Active' : 'Inactive' }}
                  </span>
                </div>
                <div class="info-item" *ngIf="currentUser.createdAt">
                  <span class="label">Member Since:</span>
                  <span class="value">{{ currentUser.createdAt | date:'mediumDate' }}</span>
                </div>
                <div class="info-item" *ngIf="currentUser.updatedAt">
                  <span class="label">Last Updated:</span>
                  <span class="value">{{ currentUser.updatedAt | date:'medium' }}</span>
                </div>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </mat-tab>
    </mat-tab-group>
  </div>
</div>
