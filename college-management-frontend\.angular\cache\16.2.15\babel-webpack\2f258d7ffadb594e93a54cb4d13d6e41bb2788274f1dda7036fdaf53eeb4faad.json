{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ClassTableComponent } from './class-table/class-table.component';\nimport { ClassFormComponent } from './class-form/class-form.component';\nimport { ViewClassComponent } from './view-class/view-class.component';\nimport { StudentbyclassComponent } from './studentbyclass/studentbyclass.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ClassTableComponent\n}, {\n  path: 'add-class',\n  component: ClassFormComponent\n}, {\n  path: 'add-class/:id',\n  component: ClassFormComponent\n}, {\n  path: 'view-class/:id',\n  component: ViewClassComponent\n}, {\n  path: 'student-by-class/:id',\n  component: StudentbyclassComponent\n}];\nexport class ClassesRoutingModule {\n  static #_ = this.ɵfac = function ClassesRoutingModule_Factory(t) {\n    return new (t || ClassesRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: ClassesRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild(routes), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ClassesRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "ClassTableComponent", "ClassFormComponent", "ViewClassComponent", "StudentbyclassComponent", "routes", "path", "component", "ClassesRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\classes\\classes-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { ClassTableComponent } from './class-table/class-table.component';\r\nimport { ClassFormComponent } from './class-form/class-form.component';\r\nimport { ViewClassComponent } from './view-class/view-class.component';\r\nimport { StudentbyclassComponent } from './studentbyclass/studentbyclass.component';\r\n\r\nconst routes: Routes = [\r\n  {path:'', component:ClassTableComponent},\r\n  {path:'add-class', component:ClassFormComponent},\r\n  {path:'add-class/:id', component:ClassFormComponent}, // Route for editing classes\r\n  {path:'view-class/:id', component:ViewClassComponent}, // Route for viewing class details\r\n  {path:'student-by-class/:id', component:StudentbyclassComponent},\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class ClassesRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,uBAAuB,QAAQ,2CAA2C;;;AAEnF,MAAMC,MAAM,GAAW,CACrB;EAACC,IAAI,EAAC,EAAE;EAAEC,SAAS,EAACN;AAAmB,CAAC,EACxC;EAACK,IAAI,EAAC,WAAW;EAAEC,SAAS,EAACL;AAAkB,CAAC,EAChD;EAACI,IAAI,EAAC,eAAe;EAAEC,SAAS,EAACL;AAAkB,CAAC,EACpD;EAACI,IAAI,EAAC,gBAAgB;EAAEC,SAAS,EAACJ;AAAkB,CAAC,EACrD;EAACG,IAAI,EAAC,sBAAsB;EAAEC,SAAS,EAACH;AAAuB,CAAC,CACjE;AAMD,OAAM,MAAOI,oBAAoB;EAAA,QAAAC,CAAA,G;qBAApBD,oBAAoB;EAAA;EAAA,QAAAE,EAAA,G;UAApBF;EAAoB;EAAA,QAAAG,EAAA,G;cAHrBX,YAAY,CAACY,QAAQ,CAACP,MAAM,CAAC,EAC7BL,YAAY;EAAA;;;2EAEXQ,oBAAoB;IAAAK,OAAA,GAAAC,EAAA,CAAAd,YAAA;IAAAe,OAAA,GAFrBf,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}