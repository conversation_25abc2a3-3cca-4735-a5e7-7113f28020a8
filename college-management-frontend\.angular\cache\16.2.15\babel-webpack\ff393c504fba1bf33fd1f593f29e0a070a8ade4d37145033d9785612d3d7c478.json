{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../services/timetable.service\";\nimport * as i3 from \"../../../services/user.service\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/chips\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/input\";\nimport * as i11 from \"@angular/material/form-field\";\nimport * as i12 from \"@angular/forms\";\nimport * as i13 from \"@angular/material/paginator\";\nimport * as i14 from \"@angular/material/progress-spinner\";\nimport * as i15 from \"@angular/material/tooltip\";\nfunction TeacherSubjectsComponent_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r0.subjects.length, \")\");\n  }\n}\nfunction TeacherSubjectsComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵelement(1, \"mat-spinner\", 14);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading your subjects...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TeacherSubjectsComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"mat-icon\", 16);\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"Error Loading Subjects\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function TeacherSubjectsComponent_div_15_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.refreshData());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.error);\n  }\n}\nfunction TeacherSubjectsComponent_div_16_div_15_mat_card_1_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"p\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const subject_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(subject_r10.description);\n  }\n}\nfunction TeacherSubjectsComponent_div_16_div_15_mat_card_1_div_26_mat_chip_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"small\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const class_r15 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", class_r15.className, \"-\", class_r15.section, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", class_r15.studentCount, \" students)\");\n  }\n}\nfunction TeacherSubjectsComponent_div_16_div_15_mat_card_1_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"h4\");\n    i0.ɵɵtext(2, \"Teaching in:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 46)(4, \"mat-chip-listbox\");\n    i0.ɵɵtemplate(5, TeacherSubjectsComponent_div_16_div_15_mat_card_1_div_26_mat_chip_5_Template, 4, 3, \"mat-chip\", 47);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const subject_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", subject_r10.classes);\n  }\n}\nfunction TeacherSubjectsComponent_div_16_div_15_mat_card_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 31)(1, \"mat-card-header\")(2, \"div\", 32)(3, \"div\", 33)(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"subject\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 34)(7, \"h3\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 35);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 36);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"mat-card-content\");\n    i0.ɵɵtemplate(14, TeacherSubjectsComponent_div_16_div_15_mat_card_1_div_14_Template, 3, 1, \"div\", 37);\n    i0.ɵɵelementStart(15, \"div\", 38)(16, \"div\", 39)(17, \"mat-icon\");\n    i0.ɵɵtext(18, \"class\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 39)(22, \"mat-icon\");\n    i0.ɵɵtext(23, \"groups\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"span\");\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(26, TeacherSubjectsComponent_div_16_div_15_mat_card_1_div_26_Template, 6, 1, \"div\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"mat-card-actions\")(28, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function TeacherSubjectsComponent_div_16_div_15_mat_card_1_Template_button_click_28_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r18);\n      const subject_r10 = restoredCtx.$implicit;\n      const ctx_r17 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r17.viewSubjectClasses(subject_r10));\n    });\n    i0.ɵɵelementStart(29, \"mat-icon\");\n    i0.ɵɵtext(30, \"class\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(31, \" View Classes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function TeacherSubjectsComponent_div_16_div_15_mat_card_1_Template_button_click_32_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r18);\n      const subject_r10 = restoredCtx.$implicit;\n      const ctx_r19 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r19.markAttendanceForSubject(subject_r10));\n    });\n    i0.ɵɵelementStart(33, \"mat-icon\");\n    i0.ɵɵtext(34, \"fact_check\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(35, \" Attendance \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function TeacherSubjectsComponent_div_16_div_15_mat_card_1_Template_button_click_36_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r18);\n      const subject_r10 = restoredCtx.$implicit;\n      const ctx_r20 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r20.viewSubjectDetails(subject_r10));\n    });\n    i0.ɵɵelementStart(37, \"mat-icon\");\n    i0.ɵɵtext(38, \"info\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const subject_r10 = ctx.$implicit;\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(subject_r10.subjectName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(subject_r10.code);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", subject_r10.credits, \" Credits \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", subject_r10.description);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", subject_r10.totalClasses, \" Classes\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", subject_r10.totalStudents, \" Students\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", subject_r10.classes.length > 0);\n  }\n}\nfunction TeacherSubjectsComponent_div_16_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtemplate(1, TeacherSubjectsComponent_div_16_div_15_mat_card_1_Template, 39, 7, \"mat-card\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.paginatedSubjects);\n  }\n}\nfunction TeacherSubjectsComponent_div_16_div_16_p_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"No subjects match your search criteria.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TeacherSubjectsComponent_div_16_div_16_p_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"You don't have any assigned subjects yet.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TeacherSubjectsComponent_div_16_div_16_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function TeacherSubjectsComponent_div_16_div_16_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r24 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r24.clearFilters());\n    });\n    i0.ɵɵtext(1, \" Clear Filters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TeacherSubjectsComponent_div_16_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"mat-icon\", 50);\n    i0.ɵɵtext(2, \"subject\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"No Subjects Found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, TeacherSubjectsComponent_div_16_div_16_p_5_Template, 2, 0, \"p\", 11);\n    i0.ɵɵtemplate(6, TeacherSubjectsComponent_div_16_div_16_p_6_Template, 2, 0, \"p\", 11);\n    i0.ɵɵtemplate(7, TeacherSubjectsComponent_div_16_div_16_button_7_Template, 2, 0, \"button\", 51);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.searchQuery);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r7.searchQuery);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.searchQuery);\n  }\n}\nconst _c0 = function () {\n  return [6, 12, 24];\n};\nfunction TeacherSubjectsComponent_div_16_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"mat-paginator\", 54);\n    i0.ɵɵlistener(\"page\", function TeacherSubjectsComponent_div_16_div_17_Template_mat_paginator_page_1_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r26.goToPage($event.pageIndex + 1));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"length\", ctx_r8.filteredSubjects.length)(\"pageSize\", ctx_r8.itemsPerPage)(\"pageSizeOptions\", i0.ɵɵpureFunction0(4, _c0))(\"pageIndex\", ctx_r8.currentPage - 1);\n  }\n}\nfunction TeacherSubjectsComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"mat-card\", 18)(2, \"mat-card-content\")(3, \"div\", 19)(4, \"div\", 20)(5, \"mat-form-field\", 21)(6, \"mat-label\");\n    i0.ɵɵtext(7, \"Search Subjects\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"input\", 22);\n    i0.ɵɵlistener(\"ngModelChange\", function TeacherSubjectsComponent_div_16_Template_input_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.searchQuery = $event);\n    })(\"input\", function TeacherSubjectsComponent_div_16_Template_input_input_8_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.onSearch());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-icon\", 23);\n    i0.ɵɵtext(10, \"search\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 24)(12, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function TeacherSubjectsComponent_div_16_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.clearFilters());\n    });\n    i0.ɵɵelementStart(13, \"mat-icon\");\n    i0.ɵɵtext(14, \"clear\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵtemplate(15, TeacherSubjectsComponent_div_16_div_15_Template, 2, 1, \"div\", 26);\n    i0.ɵɵtemplate(16, TeacherSubjectsComponent_div_16_div_16_Template, 8, 3, \"div\", 27);\n    i0.ɵɵtemplate(17, TeacherSubjectsComponent_div_16_div_17_Template, 2, 5, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.searchQuery);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.paginatedSubjects.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.loading && ctx_r3.paginatedSubjects.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.totalPages > 1);\n  }\n}\nexport class TeacherSubjectsComponent {\n  constructor(router, timetableService, userService, snackBar) {\n    this.router = router;\n    this.timetableService = timetableService;\n    this.userService = userService;\n    this.snackBar = snackBar;\n    // UI State\n    this.loading = true;\n    this.error = null;\n    this.subjects = [];\n    // Filters\n    this.searchQuery = '';\n    this.selectedDepartment = '';\n    this.selectedProgram = '';\n    // Pagination\n    this.currentPage = 1;\n    this.itemsPerPage = 12;\n    this.totalItems = 0;\n  }\n  ngOnInit() {\n    this.currentUser = this.userService.getUserFromLocalStorage()?.user;\n    if (this.currentUser) {\n      this.loadTeacherSubjects();\n    } else {\n      this.error = 'User not found';\n      this.loading = false;\n    }\n  }\n  loadTeacherSubjects() {\n    this.loading = true;\n    this.error = null;\n    this.timetableService.getTimetableByTeacher(this.currentUser._id).subscribe({\n      next: response => {\n        if (response.success) {\n          this.processTeacherSubjects(response.timetable);\n        } else {\n          this.error = 'Failed to load subjects';\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading teacher subjects:', error);\n        this.error = 'Error loading subjects data';\n        this.loading = false;\n        this.snackBar.open('Failed to load subjects', 'Close', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  processTeacherSubjects(timetableData) {\n    // Group timetable entries by subject to get unique subjects with their classes\n    const subjectMap = new Map();\n    timetableData.forEach(entry => {\n      const subjectId = entry.subject._id;\n      if (!subjectMap.has(subjectId)) {\n        subjectMap.set(subjectId, {\n          _id: entry.subject._id,\n          subjectName: entry.subject.subjectName,\n          code: entry.subject.code,\n          credits: entry.subject.credits || 3,\n          description: entry.subject.description,\n          classes: [],\n          totalClasses: 0,\n          totalStudents: 0\n        });\n      }\n      const teacherSubject = subjectMap.get(subjectId);\n      // Add class if not already added\n      const classExists = teacherSubject.classes.find(c => c._id === entry.class._id);\n      if (!classExists) {\n        teacherSubject.classes.push({\n          _id: entry.class._id,\n          className: entry.class.className,\n          section: entry.class.section,\n          department: entry.department,\n          program: entry.program,\n          studentCount: Math.floor(Math.random() * 30) + 15 // Random for now\n        });\n      }\n    });\n    // Calculate totals\n    subjectMap.forEach(subject => {\n      subject.totalClasses = subject.classes.length;\n      subject.totalStudents = subject.classes.reduce((sum, cls) => sum + (cls.studentCount || 0), 0);\n    });\n    this.subjects = Array.from(subjectMap.values());\n    this.totalItems = this.subjects.length;\n  }\n  // Filter methods\n  get filteredSubjects() {\n    let filtered = this.subjects;\n    if (this.searchQuery) {\n      const query = this.searchQuery.toLowerCase();\n      filtered = filtered.filter(subject => subject.subjectName.toLowerCase().includes(query) || subject.code.toLowerCase().includes(query) || subject.description && subject.description.toLowerCase().includes(query));\n    }\n    if (this.selectedDepartment) {\n      filtered = filtered.filter(subject => subject.classes.some(cls => cls.department._id === this.selectedDepartment));\n    }\n    if (this.selectedProgram) {\n      filtered = filtered.filter(subject => subject.classes.some(cls => cls.program._id === this.selectedProgram));\n    }\n    return filtered;\n  }\n  get paginatedSubjects() {\n    const startIndex = (this.currentPage - 1) * this.itemsPerPage;\n    return this.filteredSubjects.slice(startIndex, startIndex + this.itemsPerPage);\n  }\n  get totalPages() {\n    return Math.ceil(this.filteredSubjects.length / this.itemsPerPage);\n  }\n  // Navigation methods\n  viewSubjectDetails(subject) {\n    this.router.navigate(['/dashboard/teacher/subject-detail'], {\n      queryParams: {\n        subjectId: subject._id,\n        subjectName: subject.subjectName\n      }\n    });\n  }\n  viewSubjectClasses(subject) {\n    this.router.navigate(['/dashboard/teacher/classes'], {\n      queryParams: {\n        subjectId: subject._id\n      }\n    });\n  }\n  markAttendanceForSubject(subject) {\n    this.router.navigate(['/dashboard/teacher/attendance'], {\n      queryParams: {\n        subjectId: subject._id\n      }\n    });\n  }\n  // Pagination methods\n  nextPage() {\n    if (this.currentPage < this.totalPages) {\n      this.currentPage++;\n    }\n  }\n  previousPage() {\n    if (this.currentPage > 1) {\n      this.currentPage--;\n    }\n  }\n  goToPage(page) {\n    if (page >= 1 && page <= this.totalPages) {\n      this.currentPage = page;\n    }\n  }\n  // Utility methods\n  refreshData() {\n    this.loadTeacherSubjects();\n  }\n  clearFilters() {\n    this.searchQuery = '';\n    this.selectedDepartment = '';\n    this.selectedProgram = '';\n    this.currentPage = 1;\n  }\n  onSearch() {\n    this.currentPage = 1;\n  }\n  getClassesList(classes) {\n    return classes.map(c => `${c.className}-${c.section}`).join(', ');\n  }\n  static #_ = this.ɵfac = function TeacherSubjectsComponent_Factory(t) {\n    return new (t || TeacherSubjectsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.TimetableService), i0.ɵɵdirectiveInject(i3.UserService), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: TeacherSubjectsComponent,\n    selectors: [[\"app-teacher-subjects\"]],\n    decls: 17,\n    vars: 4,\n    consts: [[1, \"teacher-subjects-container\"], [1, \"page-header\"], [1, \"header-content\"], [1, \"page-title\"], [1, \"title-icon\"], [\"class\", \"subject-count\", 4, \"ngIf\"], [1, \"page-subtitle\"], [1, \"header-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Refresh\", 1, \"refresh-btn\", 3, \"click\"], [\"class\", \"loading-state\", 4, \"ngIf\"], [\"class\", \"error-state\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"subject-count\"], [1, \"loading-state\"], [\"diameter\", \"40\"], [1, \"error-state\"], [1, \"error-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"filters-card\"], [1, \"filters-row\"], [1, \"search-field\"], [\"appearance\", \"outline\", 1, \"compact-field\"], [\"matInput\", \"\", \"placeholder\", \"Search by name, code...\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [\"matSuffix\", \"\"], [1, \"filter-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Clear Filters\", 1, \"clear-btn\", 3, \"click\"], [\"class\", \"subjects-grid\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"pagination-container\", 4, \"ngIf\"], [1, \"subjects-grid\"], [\"class\", \"subject-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"subject-card\"], [1, \"subject-header\"], [1, \"subject-icon\"], [1, \"subject-title\"], [1, \"subject-code\"], [1, \"credits-badge\"], [\"class\", \"subject-description\", 4, \"ngIf\"], [1, \"subject-stats\"], [1, \"stat-item\"], [\"class\", \"classes-list\", 4, \"ngIf\"], [\"mat-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-button\", \"\", \"color\", \"accent\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Subject Details\", 3, \"click\"], [1, \"subject-description\"], [1, \"classes-list\"], [1, \"classes-chips\"], [\"class\", \"class-chip\", 4, \"ngFor\", \"ngForOf\"], [1, \"class-chip\"], [1, \"empty-state\"], [1, \"empty-icon\"], [\"mat-button\", \"\", 3, \"click\", 4, \"ngIf\"], [\"mat-button\", \"\", 3, \"click\"], [1, \"pagination-container\"], [\"showFirstLastButtons\", \"\", 3, \"length\", \"pageSize\", \"pageSizeOptions\", \"pageIndex\", \"page\"]],\n    template: function TeacherSubjectsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3)(4, \"mat-icon\", 4);\n        i0.ɵɵtext(5, \"subject\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(6, \" My Subjects \");\n        i0.ɵɵtemplate(7, TeacherSubjectsComponent_span_7_Template, 2, 1, \"span\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"p\", 6);\n        i0.ɵɵtext(9, \"Subjects you are teaching this semester\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 7)(11, \"button\", 8);\n        i0.ɵɵlistener(\"click\", function TeacherSubjectsComponent_Template_button_click_11_listener() {\n          return ctx.refreshData();\n        });\n        i0.ɵɵelementStart(12, \"mat-icon\");\n        i0.ɵɵtext(13, \"refresh\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵtemplate(14, TeacherSubjectsComponent_div_14_Template, 4, 0, \"div\", 9);\n        i0.ɵɵtemplate(15, TeacherSubjectsComponent_div_15_Template, 11, 1, \"div\", 10);\n        i0.ɵɵtemplate(16, TeacherSubjectsComponent_div_16_Template, 18, 4, \"div\", 11);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error);\n      }\n    },\n    dependencies: [i5.NgForOf, i5.NgIf, i6.MatButton, i6.MatIconButton, i7.MatCard, i7.MatCardActions, i7.MatCardContent, i7.MatCardHeader, i8.MatChip, i8.MatChipListbox, i9.MatIcon, i10.MatInput, i11.MatFormField, i11.MatLabel, i11.MatSuffix, i12.DefaultValueAccessor, i12.NgControlStatus, i12.NgModel, i13.MatPaginator, i14.MatProgressSpinner, i15.MatTooltip],\n    styles: [\".teacher-subjects-container[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  min-height: 100vh;\\n  padding: 15px;\\n}\\n\\n\\n\\n.page-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n  padding: 15px 0;\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.page-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #2c3e50;\\n  font-size: 1.8rem;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n\\n.title-icon[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  color: #3498db;\\n}\\n\\n.subject-count[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);\\n  color: white;\\n  padding: 2px 8px;\\n  border-radius: 10px;\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n  margin-left: 10px;\\n}\\n\\n.page-subtitle[_ngcontent-%COMP%] {\\n  margin: 5px 0 0 0;\\n  color: #7f8c8d;\\n  font-size: 0.9rem;\\n}\\n\\n.header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.refresh-btn[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n\\n.refresh-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n\\n\\n\\n.filters-card[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.filters-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  padding: 15px !important;\\n}\\n\\n.filters-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n}\\n\\n.search-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.compact-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.compact-field[_ngcontent-%COMP%]   .mat-form-field-wrapper[_ngcontent-%COMP%] {\\n  padding-bottom: 0;\\n}\\n\\n.compact-field[_ngcontent-%COMP%]   .mat-form-field-infix[_ngcontent-%COMP%] {\\n  padding: 8px 0;\\n  border-top: none;\\n}\\n\\n.compact-field[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n\\n.clear-btn[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  transition: all 0.3s ease;\\n}\\n\\n.clear-btn[_ngcontent-%COMP%]:hover {\\n  color: #495057;\\n  background-color: #f8f9fa;\\n}\\n\\n\\n\\n.subjects-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 20px;\\n}\\n\\n.subject-card[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease;\\n  border: 2px solid transparent;\\n}\\n\\n.subject-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\\n  border-color: #3498db;\\n}\\n\\n.subject-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  width: 100%;\\n}\\n\\n.subject-icon[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n}\\n\\n.subject-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n}\\n\\n.subject-title[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.subject-title[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #2c3e50;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n}\\n\\n.subject-code[_ngcontent-%COMP%] {\\n  background: #34495e;\\n  color: white;\\n  padding: 2px 8px;\\n  border-radius: 10px;\\n  font-size: 0.7rem;\\n  font-weight: 600;\\n}\\n\\n.credits-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n}\\n\\n.subject-description[_ngcontent-%COMP%] {\\n  margin: 15px 0;\\n  padding: 10px;\\n  background: #f8f9fa;\\n  border-radius: 6px;\\n  border-left: 3px solid #3498db;\\n}\\n\\n.subject-description[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #34495e;\\n  font-size: 0.9rem;\\n  line-height: 1.5;\\n}\\n\\n.subject-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n  margin: 15px 0;\\n}\\n\\n.stat-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  color: #34495e;\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n}\\n\\n.stat-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  width: 20px;\\n  height: 20px;\\n  color: #7f8c8d;\\n}\\n\\n.classes-list[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n  padding-top: 15px;\\n  border-top: 1px solid #ecf0f1;\\n}\\n\\n.classes-list[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 10px 0;\\n  color: #2c3e50;\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n}\\n\\n.classes-chips[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 5px;\\n}\\n\\n.class-chip[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);\\n  color: white;\\n  font-size: 0.75rem;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n}\\n\\n.class-chip[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  opacity: 0.8;\\n  margin-left: 4px;\\n}\\n\\n\\n\\n.subject-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  border-top: 1px solid #ecf0f1;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.subject-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  padding: 6px 12px;\\n}\\n\\n\\n\\n.loading-state[_ngcontent-%COMP%], .error-state[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n  text-align: center;\\n}\\n\\n.loading-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .error-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n  color: #7f8c8d;\\n}\\n\\n.error-state[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  margin-bottom: 10px;\\n}\\n\\n.error-icon[_ngcontent-%COMP%], .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  color: #bdc3c7;\\n  margin-bottom: 15px;\\n}\\n\\n.error-icon[_ngcontent-%COMP%] {\\n  color: #e74c3c;\\n}\\n\\n\\n\\n.pagination-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-top: 20px;\\n}\\n\\n\\n\\n@media (max-width: 1200px) {\\n  .teacher-subjects-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n\\n  .subjects-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\\n    gap: 15px;\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .page-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 15px;\\n  }\\n\\n  .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  .header-actions[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: space-between;\\n  }\\n\\n  .filters-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 10px;\\n  }\\n\\n  .filter-actions[_ngcontent-%COMP%] {\\n    width: 100%;\\n    display: flex;\\n    justify-content: flex-end;\\n  }\\n\\n  .subjects-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 15px;\\n  }\\n\\n  .subject-card[_ngcontent-%COMP%] {\\n    margin-bottom: 15px;\\n  }\\n\\n  .subject-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 8px;\\n  }\\n\\n  .subject-stats[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 8px;\\n  }\\n\\n  .subject-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 8px;\\n  }\\n\\n  .subject-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n}\\n\\n@media (max-width: 576px) {\\n  .teacher-subjects-container[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n\\n  .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.3rem;\\n  }\\n\\n  .title-icon[_ngcontent-%COMP%] {\\n    font-size: 1.3rem;\\n  }\\n\\n  .subject-count[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n    padding: 1px 6px;\\n  }\\n\\n  .subject-card[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n\\n  .subject-icon[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n\\n  .subject-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n    font-size: 1.2rem;\\n  }\\n\\n  .subject-title[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n\\n  .subject-code[_ngcontent-%COMP%] {\\n    font-size: 0.65rem;\\n    padding: 1px 6px;\\n  }\\n\\n  .credits-badge[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n    padding: 3px 6px;\\n  }\\n\\n  .stat-item[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n\\n  .classes-list[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n\\n  .class-chip[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n    padding: 3px 6px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "subjects", "length", "ɵɵelement", "ɵɵlistener", "TeacherSubjectsComponent_div_15_Template_button_click_7_listener", "ɵɵrestoreView", "_r5", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "refreshData", "ɵɵtextInterpolate", "ctx_r2", "error", "subject_r10", "description", "ɵɵtextInterpolate2", "class_r15", "className", "section", "studentCount", "ɵɵtemplate", "TeacherSubjectsComponent_div_16_div_15_mat_card_1_div_26_mat_chip_5_Template", "ɵɵproperty", "classes", "TeacherSubjectsComponent_div_16_div_15_mat_card_1_div_14_Template", "TeacherSubjectsComponent_div_16_div_15_mat_card_1_div_26_Template", "TeacherSubjectsComponent_div_16_div_15_mat_card_1_Template_button_click_28_listener", "restoredCtx", "_r18", "$implicit", "ctx_r17", "viewSubjectClasses", "TeacherSubjectsComponent_div_16_div_15_mat_card_1_Template_button_click_32_listener", "ctx_r19", "markAttendanceForSubject", "TeacherSubjectsComponent_div_16_div_15_mat_card_1_Template_button_click_36_listener", "ctx_r20", "viewSubjectDetails", "subjectName", "code", "credits", "totalClasses", "totalStudents", "TeacherSubjectsComponent_div_16_div_15_mat_card_1_Template", "ctx_r6", "paginatedSubjects", "TeacherSubjectsComponent_div_16_div_16_button_7_Template_button_click_0_listener", "_r25", "ctx_r24", "clearFilters", "TeacherSubjectsComponent_div_16_div_16_p_5_Template", "TeacherSubjectsComponent_div_16_div_16_p_6_Template", "TeacherSubjectsComponent_div_16_div_16_button_7_Template", "ctx_r7", "searchQuery", "TeacherSubjectsComponent_div_16_div_17_Template_mat_paginator_page_1_listener", "$event", "_r27", "ctx_r26", "goToPage", "pageIndex", "ctx_r8", "filteredSubjects", "itemsPerPage", "ɵɵpureFunction0", "_c0", "currentPage", "TeacherSubjectsComponent_div_16_Template_input_ngModelChange_8_listener", "_r29", "ctx_r28", "TeacherSubjectsComponent_div_16_Template_input_input_8_listener", "ctx_r30", "onSearch", "TeacherSubjectsComponent_div_16_Template_button_click_12_listener", "ctx_r31", "TeacherSubjectsComponent_div_16_div_15_Template", "TeacherSubjectsComponent_div_16_div_16_Template", "TeacherSubjectsComponent_div_16_div_17_Template", "ctx_r3", "loading", "totalPages", "TeacherSubjectsComponent", "constructor", "router", "timetableService", "userService", "snackBar", "selectedDepartment", "selectedProgram", "totalItems", "ngOnInit", "currentUser", "getUserFromLocalStorage", "user", "loadTeacherSubjects", "getTimetableByTeacher", "_id", "subscribe", "next", "response", "success", "processTeacherSubjects", "timetable", "console", "open", "duration", "timetableData", "subjectMap", "Map", "for<PERSON>ach", "entry", "subjectId", "subject", "has", "set", "teacherSubject", "get", "classExists", "find", "c", "class", "push", "department", "program", "Math", "floor", "random", "reduce", "sum", "cls", "Array", "from", "values", "filtered", "query", "toLowerCase", "filter", "includes", "some", "startIndex", "slice", "ceil", "navigate", "queryParams", "nextPage", "previousPage", "page", "getClassesList", "map", "join", "_", "ɵɵdirectiveInject", "i1", "Router", "i2", "TimetableService", "i3", "UserService", "i4", "MatSnackBar", "_2", "selectors", "decls", "vars", "consts", "template", "TeacherSubjectsComponent_Template", "rf", "ctx", "TeacherSubjectsComponent_span_7_Template", "TeacherSubjectsComponent_Template_button_click_11_listener", "TeacherSubjectsComponent_div_14_Template", "TeacherSubjectsComponent_div_15_Template", "TeacherSubjectsComponent_div_16_Template"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\teacher-dashboard\\teacher-subjects\\teacher-subjects.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\teacher-dashboard\\teacher-subjects\\teacher-subjects.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { TimetableService } from '../../../services/timetable.service';\r\nimport { UserService } from '../../../services/user.service';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\n\r\ninterface TeacherSubject {\r\n  _id: string;\r\n  subjectName: string;\r\n  code: string;\r\n  credits: number;\r\n  description?: string;\r\n  classes: Array<{\r\n    _id: string;\r\n    className: string;\r\n    section: string;\r\n    department: {\r\n      _id: string;\r\n      name: string;\r\n    };\r\n    program: {\r\n      _id: string;\r\n      name: string;\r\n    };\r\n    studentCount?: number;\r\n  }>;\r\n  totalClasses: number;\r\n  totalStudents: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-teacher-subjects',\r\n  templateUrl: './teacher-subjects.component.html',\r\n  styleUrls: ['./teacher-subjects.component.css']\r\n})\r\nexport class TeacherSubjectsComponent implements OnInit {\r\n  // UI State\r\n  loading = true;\r\n  error: string | null = null;\r\n\r\n  // Data\r\n  currentUser: any;\r\n  subjects: TeacherSubject[] = [];\r\n\r\n  // Filters\r\n  searchQuery = '';\r\n  selectedDepartment = '';\r\n  selectedProgram = '';\r\n\r\n  // Pagination\r\n  currentPage = 1;\r\n  itemsPerPage = 12;\r\n  totalItems = 0;\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private timetableService: TimetableService,\r\n    private userService: UserService,\r\n    private snackBar: MatSnackBar\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.currentUser = this.userService.getUserFromLocalStorage()?.user;\r\n    if (this.currentUser) {\r\n      this.loadTeacherSubjects();\r\n    } else {\r\n      this.error = 'User not found';\r\n      this.loading = false;\r\n    }\r\n  }\r\n\r\n  loadTeacherSubjects(): void {\r\n    this.loading = true;\r\n    this.error = null;\r\n\r\n    this.timetableService.getTimetableByTeacher(this.currentUser._id).subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.processTeacherSubjects(response.timetable);\r\n        } else {\r\n          this.error = 'Failed to load subjects';\r\n        }\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading teacher subjects:', error);\r\n        this.error = 'Error loading subjects data';\r\n        this.loading = false;\r\n        this.snackBar.open('Failed to load subjects', 'Close', { duration: 3000 });\r\n      }\r\n    });\r\n  }\r\n\r\n  processTeacherSubjects(timetableData: any[]): void {\r\n    // Group timetable entries by subject to get unique subjects with their classes\r\n    const subjectMap = new Map<string, TeacherSubject>();\r\n\r\n    timetableData.forEach(entry => {\r\n      const subjectId = entry.subject._id;\r\n\r\n      if (!subjectMap.has(subjectId)) {\r\n        subjectMap.set(subjectId, {\r\n          _id: entry.subject._id,\r\n          subjectName: entry.subject.subjectName,\r\n          code: entry.subject.code,\r\n          credits: entry.subject.credits || 3,\r\n          description: entry.subject.description,\r\n          classes: [],\r\n          totalClasses: 0,\r\n          totalStudents: 0\r\n        });\r\n      }\r\n\r\n      const teacherSubject = subjectMap.get(subjectId)!;\r\n\r\n      // Add class if not already added\r\n      const classExists = teacherSubject.classes.find(c => c._id === entry.class._id);\r\n      if (!classExists) {\r\n        teacherSubject.classes.push({\r\n          _id: entry.class._id,\r\n          className: entry.class.className,\r\n          section: entry.class.section,\r\n          department: entry.department,\r\n          program: entry.program,\r\n          studentCount: Math.floor(Math.random() * 30) + 15 // Random for now\r\n        });\r\n      }\r\n    });\r\n\r\n    // Calculate totals\r\n    subjectMap.forEach(subject => {\r\n      subject.totalClasses = subject.classes.length;\r\n      subject.totalStudents = subject.classes.reduce((sum, cls) => sum + (cls.studentCount || 0), 0);\r\n    });\r\n\r\n    this.subjects = Array.from(subjectMap.values());\r\n    this.totalItems = this.subjects.length;\r\n  }\r\n\r\n  // Filter methods\r\n  get filteredSubjects(): TeacherSubject[] {\r\n    let filtered = this.subjects;\r\n\r\n    if (this.searchQuery) {\r\n      const query = this.searchQuery.toLowerCase();\r\n      filtered = filtered.filter(subject =>\r\n        subject.subjectName.toLowerCase().includes(query) ||\r\n        subject.code.toLowerCase().includes(query) ||\r\n        (subject.description && subject.description.toLowerCase().includes(query))\r\n      );\r\n    }\r\n\r\n    if (this.selectedDepartment) {\r\n      filtered = filtered.filter(subject =>\r\n        subject.classes.some(cls => cls.department._id === this.selectedDepartment)\r\n      );\r\n    }\r\n\r\n    if (this.selectedProgram) {\r\n      filtered = filtered.filter(subject =>\r\n        subject.classes.some(cls => cls.program._id === this.selectedProgram)\r\n      );\r\n    }\r\n\r\n    return filtered;\r\n  }\r\n\r\n  get paginatedSubjects(): TeacherSubject[] {\r\n    const startIndex = (this.currentPage - 1) * this.itemsPerPage;\r\n    return this.filteredSubjects.slice(startIndex, startIndex + this.itemsPerPage);\r\n  }\r\n\r\n  get totalPages(): number {\r\n    return Math.ceil(this.filteredSubjects.length / this.itemsPerPage);\r\n  }\r\n\r\n  // Navigation methods\r\n  viewSubjectDetails(subject: TeacherSubject): void {\r\n    this.router.navigate(['/dashboard/teacher/subject-detail'], {\r\n      queryParams: {\r\n        subjectId: subject._id,\r\n        subjectName: subject.subjectName\r\n      }\r\n    });\r\n  }\r\n\r\n  viewSubjectClasses(subject: TeacherSubject): void {\r\n    this.router.navigate(['/dashboard/teacher/classes'], {\r\n      queryParams: {\r\n        subjectId: subject._id\r\n      }\r\n    });\r\n  }\r\n\r\n  markAttendanceForSubject(subject: TeacherSubject): void {\r\n    this.router.navigate(['/dashboard/teacher/attendance'], {\r\n      queryParams: {\r\n        subjectId: subject._id\r\n      }\r\n    });\r\n  }\r\n\r\n  // Pagination methods\r\n  nextPage(): void {\r\n    if (this.currentPage < this.totalPages) {\r\n      this.currentPage++;\r\n    }\r\n  }\r\n\r\n  previousPage(): void {\r\n    if (this.currentPage > 1) {\r\n      this.currentPage--;\r\n    }\r\n  }\r\n\r\n  goToPage(page: number): void {\r\n    if (page >= 1 && page <= this.totalPages) {\r\n      this.currentPage = page;\r\n    }\r\n  }\r\n\r\n  // Utility methods\r\n  refreshData(): void {\r\n    this.loadTeacherSubjects();\r\n  }\r\n\r\n  clearFilters(): void {\r\n    this.searchQuery = '';\r\n    this.selectedDepartment = '';\r\n    this.selectedProgram = '';\r\n    this.currentPage = 1;\r\n  }\r\n\r\n  onSearch(): void {\r\n    this.currentPage = 1;\r\n  }\r\n\r\n  getClassesList(classes: any[]): string {\r\n    return classes.map(c => `${c.className}-${c.section}`).join(', ');\r\n  }\r\n}\r\n", "<div class=\"teacher-subjects-container\">\r\n  <!-- Header -->\r\n  <div class=\"page-header\">\r\n    <div class=\"header-content\">\r\n      <h1 class=\"page-title\">\r\n        <mat-icon class=\"title-icon\">subject</mat-icon>\r\n        My Subjects\r\n        <span class=\"subject-count\" *ngIf=\"!loading\">({{ subjects.length }})</span>\r\n      </h1>\r\n      <p class=\"page-subtitle\">Subjects you are teaching this semester</p>\r\n    </div>\r\n    <div class=\"header-actions\">\r\n      <button mat-icon-button (click)=\"refreshData()\" matTooltip=\"Refresh\" class=\"refresh-btn\">\r\n        <mat-icon>refresh</mat-icon>\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Loading State -->\r\n  <div *ngIf=\"loading\" class=\"loading-state\">\r\n    <mat-spinner diameter=\"40\"></mat-spinner>\r\n    <p>Loading your subjects...</p>\r\n  </div>\r\n\r\n  <!-- Error State -->\r\n  <div *ngIf=\"error && !loading\" class=\"error-state\">\r\n    <mat-icon class=\"error-icon\">error</mat-icon>\r\n    <h4>Error Loading Subjects</h4>\r\n    <p>{{ error }}</p>\r\n    <button mat-raised-button color=\"primary\" (click)=\"refreshData()\">\r\n      <mat-icon>refresh</mat-icon>\r\n      Try Again\r\n    </button>\r\n  </div>\r\n\r\n  <!-- Main Content -->\r\n  <div *ngIf=\"!loading && !error\">\r\n    <!-- Filters -->\r\n    <mat-card class=\"filters-card\">\r\n      <mat-card-content>\r\n        <div class=\"filters-row\">\r\n          <div class=\"search-field\">\r\n            <mat-form-field appearance=\"outline\" class=\"compact-field\">\r\n              <mat-label>Search Subjects</mat-label>\r\n              <input matInput [(ngModel)]=\"searchQuery\" (input)=\"onSearch()\"\r\n                     placeholder=\"Search by name, code...\">\r\n              <mat-icon matSuffix>search</mat-icon>\r\n            </mat-form-field>\r\n          </div>\r\n          <div class=\"filter-actions\">\r\n            <button mat-icon-button (click)=\"clearFilters()\" matTooltip=\"Clear Filters\" class=\"clear-btn\">\r\n              <mat-icon>clear</mat-icon>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n\r\n    <!-- Subjects Grid -->\r\n    <div class=\"subjects-grid\" *ngIf=\"paginatedSubjects.length > 0\">\r\n      <mat-card *ngFor=\"let subject of paginatedSubjects\" class=\"subject-card\">\r\n        <mat-card-header>\r\n          <div class=\"subject-header\">\r\n            <div class=\"subject-icon\">\r\n              <mat-icon>subject</mat-icon>\r\n            </div>\r\n            <div class=\"subject-title\">\r\n              <h3>{{ subject.subjectName }}</h3>\r\n              <span class=\"subject-code\">{{ subject.code }}</span>\r\n            </div>\r\n            <div class=\"credits-badge\">\r\n              {{ subject.credits }} Credits\r\n            </div>\r\n          </div>\r\n        </mat-card-header>\r\n\r\n        <mat-card-content>\r\n          <div class=\"subject-description\" *ngIf=\"subject.description\">\r\n            <p>{{ subject.description }}</p>\r\n          </div>\r\n\r\n          <div class=\"subject-stats\">\r\n            <div class=\"stat-item\">\r\n              <mat-icon>class</mat-icon>\r\n              <span>{{ subject.totalClasses }} Classes</span>\r\n            </div>\r\n            <div class=\"stat-item\">\r\n              <mat-icon>groups</mat-icon>\r\n              <span>{{ subject.totalStudents }} Students</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"classes-list\" *ngIf=\"subject.classes.length > 0\">\r\n            <h4>Teaching in:</h4>\r\n            <div class=\"classes-chips\">\r\n              <mat-chip-listbox>\r\n                <mat-chip *ngFor=\"let class of subject.classes\" class=\"class-chip\">\r\n                  {{ class.className }}-{{ class.section }}\r\n                  <small>({{ class.studentCount }} students)</small>\r\n                </mat-chip>\r\n              </mat-chip-listbox>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n\r\n        <mat-card-actions>\r\n          <button mat-button color=\"primary\" (click)=\"viewSubjectClasses(subject)\">\r\n            <mat-icon>class</mat-icon>\r\n            View Classes\r\n          </button>\r\n          <button mat-button color=\"accent\" (click)=\"markAttendanceForSubject(subject)\">\r\n            <mat-icon>fact_check</mat-icon>\r\n            Attendance\r\n          </button>\r\n          <button mat-icon-button (click)=\"viewSubjectDetails(subject)\" matTooltip=\"Subject Details\">\r\n            <mat-icon>info</mat-icon>\r\n          </button>\r\n        </mat-card-actions>\r\n      </mat-card>\r\n    </div>\r\n\r\n    <!-- Empty State -->\r\n    <div *ngIf=\"!loading && paginatedSubjects.length === 0\" class=\"empty-state\">\r\n      <mat-icon class=\"empty-icon\">subject</mat-icon>\r\n      <h4>No Subjects Found</h4>\r\n      <p *ngIf=\"searchQuery\">No subjects match your search criteria.</p>\r\n      <p *ngIf=\"!searchQuery\">You don't have any assigned subjects yet.</p>\r\n      <button mat-button (click)=\"clearFilters()\" *ngIf=\"searchQuery\">\r\n        Clear Filters\r\n      </button>\r\n    </div>\r\n\r\n    <!-- Pagination -->\r\n    <div class=\"pagination-container\" *ngIf=\"totalPages > 1\">\r\n      <mat-paginator\r\n        [length]=\"filteredSubjects.length\"\r\n        [pageSize]=\"itemsPerPage\"\r\n        [pageSizeOptions]=\"[6, 12, 24]\"\r\n        [pageIndex]=\"currentPage - 1\"\r\n        (page)=\"goToPage($event.pageIndex + 1)\"\r\n        showFirstLastButtons>\r\n      </mat-paginator>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;ICOQA,EAAA,CAAAC,cAAA,eAA6C;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA9BH,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,QAAA,CAAAC,MAAA,MAAuB;;;;;IAY1ER,EAAA,CAAAC,cAAA,cAA2C;IACzCD,EAAA,CAAAS,SAAA,sBAAyC;IACzCT,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;IAIjCH,EAAA,CAAAC,cAAA,cAAmD;IACpBD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7CH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAClBH,EAAA,CAAAC,cAAA,iBAAkE;IAAxBD,EAAA,CAAAU,UAAA,mBAAAC,iEAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAC/DjB,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAE,MAAA,mBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAJNH,EAAA,CAAAI,SAAA,GAAW;IAAXJ,EAAA,CAAAkB,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IAiDRpB,EAAA,CAAAC,cAAA,cAA6D;IACxDD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAA7BH,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAkB,iBAAA,CAAAG,WAAA,CAAAC,WAAA,CAAyB;;;;;IAkBxBtB,EAAA,CAAAC,cAAA,mBAAmE;IACjED,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAC,cAAA,YAAO;IAAAD,EAAA,CAAAE,MAAA,GAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IADlDH,EAAA,CAAAI,SAAA,GACA;IADAJ,EAAA,CAAAuB,kBAAA,MAAAC,SAAA,CAAAC,SAAA,OAAAD,SAAA,CAAAE,OAAA,MACA;IAAO1B,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAK,kBAAA,MAAAmB,SAAA,CAAAG,YAAA,eAAmC;;;;;IANlD3B,EAAA,CAAAC,cAAA,cAA6D;IACvDD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrBH,EAAA,CAAAC,cAAA,cAA2B;IAEvBD,EAAA,CAAA4B,UAAA,IAAAC,4EAAA,uBAGW;IACb7B,EAAA,CAAAG,YAAA,EAAmB;;;;IAJWH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAA8B,UAAA,YAAAT,WAAA,CAAAU,OAAA,CAAkB;;;;;;IApCxD/B,EAAA,CAAAC,cAAA,mBAAyE;IAIvDD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE9BH,EAAA,CAAAC,cAAA,cAA2B;IACrBD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,IAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEtDH,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAIVH,EAAA,CAAAC,cAAA,wBAAkB;IAChBD,EAAA,CAAA4B,UAAA,KAAAI,iEAAA,kBAEM;IAENhC,EAAA,CAAAC,cAAA,eAA2B;IAEbD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEjDH,EAAA,CAAAC,cAAA,eAAuB;IACXD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAIrDH,EAAA,CAAA4B,UAAA,KAAAK,iEAAA,kBAUM;IACRjC,EAAA,CAAAG,YAAA,EAAmB;IAEnBH,EAAA,CAAAC,cAAA,wBAAkB;IACmBD,EAAA,CAAAU,UAAA,mBAAAwB,oFAAA;MAAA,MAAAC,WAAA,GAAAnC,EAAA,CAAAY,aAAA,CAAAwB,IAAA;MAAA,MAAAf,WAAA,GAAAc,WAAA,CAAAE,SAAA;MAAA,MAAAC,OAAA,GAAAtC,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAsB,OAAA,CAAAC,kBAAA,CAAAlB,WAAA,CAA2B;IAAA,EAAC;IACtErB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA8E;IAA5CD,EAAA,CAAAU,UAAA,mBAAA8B,oFAAA;MAAA,MAAAL,WAAA,GAAAnC,EAAA,CAAAY,aAAA,CAAAwB,IAAA;MAAA,MAAAf,WAAA,GAAAc,WAAA,CAAAE,SAAA;MAAA,MAAAI,OAAA,GAAAzC,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAyB,OAAA,CAAAC,wBAAA,CAAArB,WAAA,CAAiC;IAAA,EAAC;IAC3ErB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAE,MAAA,oBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA2F;IAAnED,EAAA,CAAAU,UAAA,mBAAAiC,oFAAA;MAAA,MAAAR,WAAA,GAAAnC,EAAA,CAAAY,aAAA,CAAAwB,IAAA;MAAA,MAAAf,WAAA,GAAAc,WAAA,CAAAE,SAAA;MAAA,MAAAO,OAAA,GAAA5C,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAA4B,OAAA,CAAAC,kBAAA,CAAAxB,WAAA,CAA2B;IAAA,EAAC;IAC3DrB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IAhDnBH,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAkB,iBAAA,CAAAG,WAAA,CAAAyB,WAAA,CAAyB;IACF9C,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAkB,iBAAA,CAAAG,WAAA,CAAA0B,IAAA,CAAkB;IAG7C/C,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAgB,WAAA,CAAA2B,OAAA,cACF;IAKgChD,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAA8B,UAAA,SAAAT,WAAA,CAAAC,WAAA,CAAyB;IAOjDtB,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAK,kBAAA,KAAAgB,WAAA,CAAA4B,YAAA,aAAkC;IAIlCjD,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAK,kBAAA,KAAAgB,WAAA,CAAA6B,aAAA,cAAoC;IAInBlD,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAA8B,UAAA,SAAAT,WAAA,CAAAU,OAAA,CAAAvB,MAAA,KAAgC;;;;;IAjCjER,EAAA,CAAAC,cAAA,cAAgE;IAC9DD,EAAA,CAAA4B,UAAA,IAAAuB,0DAAA,wBA0DW;IACbnD,EAAA,CAAAG,YAAA,EAAM;;;;IA3D0BH,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAA8B,UAAA,YAAAsB,MAAA,CAAAC,iBAAA,CAAoB;;;;;IAiElDrD,EAAA,CAAAC,cAAA,QAAuB;IAAAD,EAAA,CAAAE,MAAA,8CAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAClEH,EAAA,CAAAC,cAAA,QAAwB;IAAAD,EAAA,CAAAE,MAAA,gDAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;IACrEH,EAAA,CAAAC,cAAA,iBAAgE;IAA7CD,EAAA,CAAAU,UAAA,mBAAA4C,iFAAA;MAAAtD,EAAA,CAAAY,aAAA,CAAA2C,IAAA;MAAA,MAAAC,OAAA,GAAAxD,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAwC,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IACzCzD,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAPXH,EAAA,CAAAC,cAAA,cAA4E;IAC7CD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/CH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAA4B,UAAA,IAAA8B,mDAAA,gBAAkE;IAClE1D,EAAA,CAAA4B,UAAA,IAAA+B,mDAAA,gBAAqE;IACrE3D,EAAA,CAAA4B,UAAA,IAAAgC,wDAAA,qBAES;IACX5D,EAAA,CAAAG,YAAA,EAAM;;;;IALAH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAA8B,UAAA,SAAA+B,MAAA,CAAAC,WAAA,CAAiB;IACjB9D,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAA8B,UAAA,UAAA+B,MAAA,CAAAC,WAAA,CAAkB;IACuB9D,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAA8B,UAAA,SAAA+B,MAAA,CAAAC,WAAA,CAAiB;;;;;;;;;IAMhE9D,EAAA,CAAAC,cAAA,cAAyD;IAMrDD,EAAA,CAAAU,UAAA,kBAAAqD,8EAAAC,MAAA;MAAAhE,EAAA,CAAAY,aAAA,CAAAqD,IAAA;MAAA,MAAAC,OAAA,GAAAlE,EAAA,CAAAe,aAAA;MAAA,OAAQf,EAAA,CAAAgB,WAAA,CAAAkD,OAAA,CAAAC,QAAA,CAAAH,MAAA,CAAAI,SAAA,GAA4B,CAAC,CAAC;IAAA,EAAC;IAEzCpE,EAAA,CAAAG,YAAA,EAAgB;;;;IANdH,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAA8B,UAAA,WAAAuC,MAAA,CAAAC,gBAAA,CAAA9D,MAAA,CAAkC,aAAA6D,MAAA,CAAAE,YAAA,qBAAAvE,EAAA,CAAAwE,eAAA,IAAAC,GAAA,gBAAAJ,MAAA,CAAAK,WAAA;;;;;;IAnGxC1E,EAAA,CAAAC,cAAA,UAAgC;IAOTD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACtCH,EAAA,CAAAC,cAAA,gBAC6C;IAD7BD,EAAA,CAAAU,UAAA,2BAAAiE,wEAAAX,MAAA;MAAAhE,EAAA,CAAAY,aAAA,CAAAgE,IAAA;MAAA,MAAAC,OAAA,GAAA7E,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAA6D,OAAA,CAAAf,WAAA,GAAAE,MAAA;IAAA,EAAyB,mBAAAc,gEAAA;MAAA9E,EAAA,CAAAY,aAAA,CAAAgE,IAAA;MAAA,MAAAG,OAAA,GAAA/E,EAAA,CAAAe,aAAA;MAAA,OAAUf,EAAA,CAAAgB,WAAA,CAAA+D,OAAA,CAAAC,QAAA,EAAU;IAAA,EAApB;IAAzChF,EAAA,CAAAG,YAAA,EAC6C;IAC7CH,EAAA,CAAAC,cAAA,mBAAoB;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAGzCH,EAAA,CAAAC,cAAA,eAA4B;IACFD,EAAA,CAAAU,UAAA,mBAAAuE,kEAAA;MAAAjF,EAAA,CAAAY,aAAA,CAAAgE,IAAA;MAAA,MAAAM,OAAA,GAAAlF,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAkE,OAAA,CAAAzB,YAAA,EAAc;IAAA,EAAC;IAC9CzD,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAQpCH,EAAA,CAAA4B,UAAA,KAAAuD,+CAAA,kBA4DM;IAGNnF,EAAA,CAAA4B,UAAA,KAAAwD,+CAAA,kBAQM;IAGNpF,EAAA,CAAA4B,UAAA,KAAAyD,+CAAA,kBASM;IACRrF,EAAA,CAAAG,YAAA,EAAM;;;;IAnGsBH,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAA8B,UAAA,YAAAwD,MAAA,CAAAxB,WAAA,CAAyB;IAevB9D,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAA8B,UAAA,SAAAwD,MAAA,CAAAjC,iBAAA,CAAA7C,MAAA,KAAkC;IA+DxDR,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAA8B,UAAA,UAAAwD,MAAA,CAAAC,OAAA,IAAAD,MAAA,CAAAjC,iBAAA,CAAA7C,MAAA,OAAgD;IAWnBR,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAA8B,UAAA,SAAAwD,MAAA,CAAAE,UAAA,KAAoB;;;ADlG3D,OAAM,MAAOC,wBAAwB;EAmBnCC,YACUC,MAAc,EACdC,gBAAkC,EAClCC,WAAwB,EACxBC,QAAqB;IAHrB,KAAAH,MAAM,GAANA,MAAM;IACN,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAtBlB;IACA,KAAAP,OAAO,GAAG,IAAI;IACd,KAAAnE,KAAK,GAAkB,IAAI;IAI3B,KAAAb,QAAQ,GAAqB,EAAE;IAE/B;IACA,KAAAuD,WAAW,GAAG,EAAE;IAChB,KAAAiC,kBAAkB,GAAG,EAAE;IACvB,KAAAC,eAAe,GAAG,EAAE;IAEpB;IACA,KAAAtB,WAAW,GAAG,CAAC;IACf,KAAAH,YAAY,GAAG,EAAE;IACjB,KAAA0B,UAAU,GAAG,CAAC;EAOX;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,GAAG,IAAI,CAACN,WAAW,CAACO,uBAAuB,EAAE,EAAEC,IAAI;IACnE,IAAI,IAAI,CAACF,WAAW,EAAE;MACpB,IAAI,CAACG,mBAAmB,EAAE;KAC3B,MAAM;MACL,IAAI,CAAClF,KAAK,GAAG,gBAAgB;MAC7B,IAAI,CAACmE,OAAO,GAAG,KAAK;;EAExB;EAEAe,mBAAmBA,CAAA;IACjB,IAAI,CAACf,OAAO,GAAG,IAAI;IACnB,IAAI,CAACnE,KAAK,GAAG,IAAI;IAEjB,IAAI,CAACwE,gBAAgB,CAACW,qBAAqB,CAAC,IAAI,CAACJ,WAAW,CAACK,GAAG,CAAC,CAACC,SAAS,CAAC;MAC1EC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACC,sBAAsB,CAACF,QAAQ,CAACG,SAAS,CAAC;SAChD,MAAM;UACL,IAAI,CAAC1F,KAAK,GAAG,yBAAyB;;QAExC,IAAI,CAACmE,OAAO,GAAG,KAAK;MACtB,CAAC;MACDnE,KAAK,EAAGA,KAAK,IAAI;QACf2F,OAAO,CAAC3F,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD,IAAI,CAACA,KAAK,GAAG,6BAA6B;QAC1C,IAAI,CAACmE,OAAO,GAAG,KAAK;QACpB,IAAI,CAACO,QAAQ,CAACkB,IAAI,CAAC,yBAAyB,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MAC5E;KACD,CAAC;EACJ;EAEAJ,sBAAsBA,CAACK,aAAoB;IACzC;IACA,MAAMC,UAAU,GAAG,IAAIC,GAAG,EAA0B;IAEpDF,aAAa,CAACG,OAAO,CAACC,KAAK,IAAG;MAC5B,MAAMC,SAAS,GAAGD,KAAK,CAACE,OAAO,CAAChB,GAAG;MAEnC,IAAI,CAACW,UAAU,CAACM,GAAG,CAACF,SAAS,CAAC,EAAE;QAC9BJ,UAAU,CAACO,GAAG,CAACH,SAAS,EAAE;UACxBf,GAAG,EAAEc,KAAK,CAACE,OAAO,CAAChB,GAAG;UACtB1D,WAAW,EAAEwE,KAAK,CAACE,OAAO,CAAC1E,WAAW;UACtCC,IAAI,EAAEuE,KAAK,CAACE,OAAO,CAACzE,IAAI;UACxBC,OAAO,EAAEsE,KAAK,CAACE,OAAO,CAACxE,OAAO,IAAI,CAAC;UACnC1B,WAAW,EAAEgG,KAAK,CAACE,OAAO,CAAClG,WAAW;UACtCS,OAAO,EAAE,EAAE;UACXkB,YAAY,EAAE,CAAC;UACfC,aAAa,EAAE;SAChB,CAAC;;MAGJ,MAAMyE,cAAc,GAAGR,UAAU,CAACS,GAAG,CAACL,SAAS,CAAE;MAEjD;MACA,MAAMM,WAAW,GAAGF,cAAc,CAAC5F,OAAO,CAAC+F,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACvB,GAAG,KAAKc,KAAK,CAACU,KAAK,CAACxB,GAAG,CAAC;MAC/E,IAAI,CAACqB,WAAW,EAAE;QAChBF,cAAc,CAAC5F,OAAO,CAACkG,IAAI,CAAC;UAC1BzB,GAAG,EAAEc,KAAK,CAACU,KAAK,CAACxB,GAAG;UACpB/E,SAAS,EAAE6F,KAAK,CAACU,KAAK,CAACvG,SAAS;UAChCC,OAAO,EAAE4F,KAAK,CAACU,KAAK,CAACtG,OAAO;UAC5BwG,UAAU,EAAEZ,KAAK,CAACY,UAAU;UAC5BC,OAAO,EAAEb,KAAK,CAACa,OAAO;UACtBxG,YAAY,EAAEyG,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;SACnD,CAAC;;IAEN,CAAC,CAAC;IAEF;IACAnB,UAAU,CAACE,OAAO,CAACG,OAAO,IAAG;MAC3BA,OAAO,CAACvE,YAAY,GAAGuE,OAAO,CAACzF,OAAO,CAACvB,MAAM;MAC7CgH,OAAO,CAACtE,aAAa,GAAGsE,OAAO,CAACzF,OAAO,CAACwG,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,IAAIC,GAAG,CAAC9G,YAAY,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAChG,CAAC,CAAC;IAEF,IAAI,CAACpB,QAAQ,GAAGmI,KAAK,CAACC,IAAI,CAACxB,UAAU,CAACyB,MAAM,EAAE,CAAC;IAC/C,IAAI,CAAC3C,UAAU,GAAG,IAAI,CAAC1F,QAAQ,CAACC,MAAM;EACxC;EAEA;EACA,IAAI8D,gBAAgBA,CAAA;IAClB,IAAIuE,QAAQ,GAAG,IAAI,CAACtI,QAAQ;IAE5B,IAAI,IAAI,CAACuD,WAAW,EAAE;MACpB,MAAMgF,KAAK,GAAG,IAAI,CAAChF,WAAW,CAACiF,WAAW,EAAE;MAC5CF,QAAQ,GAAGA,QAAQ,CAACG,MAAM,CAACxB,OAAO,IAChCA,OAAO,CAAC1E,WAAW,CAACiG,WAAW,EAAE,CAACE,QAAQ,CAACH,KAAK,CAAC,IACjDtB,OAAO,CAACzE,IAAI,CAACgG,WAAW,EAAE,CAACE,QAAQ,CAACH,KAAK,CAAC,IACzCtB,OAAO,CAAClG,WAAW,IAAIkG,OAAO,CAAClG,WAAW,CAACyH,WAAW,EAAE,CAACE,QAAQ,CAACH,KAAK,CAAE,CAC3E;;IAGH,IAAI,IAAI,CAAC/C,kBAAkB,EAAE;MAC3B8C,QAAQ,GAAGA,QAAQ,CAACG,MAAM,CAACxB,OAAO,IAChCA,OAAO,CAACzF,OAAO,CAACmH,IAAI,CAACT,GAAG,IAAIA,GAAG,CAACP,UAAU,CAAC1B,GAAG,KAAK,IAAI,CAACT,kBAAkB,CAAC,CAC5E;;IAGH,IAAI,IAAI,CAACC,eAAe,EAAE;MACxB6C,QAAQ,GAAGA,QAAQ,CAACG,MAAM,CAACxB,OAAO,IAChCA,OAAO,CAACzF,OAAO,CAACmH,IAAI,CAACT,GAAG,IAAIA,GAAG,CAACN,OAAO,CAAC3B,GAAG,KAAK,IAAI,CAACR,eAAe,CAAC,CACtE;;IAGH,OAAO6C,QAAQ;EACjB;EAEA,IAAIxF,iBAAiBA,CAAA;IACnB,MAAM8F,UAAU,GAAG,CAAC,IAAI,CAACzE,WAAW,GAAG,CAAC,IAAI,IAAI,CAACH,YAAY;IAC7D,OAAO,IAAI,CAACD,gBAAgB,CAAC8E,KAAK,CAACD,UAAU,EAAEA,UAAU,GAAG,IAAI,CAAC5E,YAAY,CAAC;EAChF;EAEA,IAAIiB,UAAUA,CAAA;IACZ,OAAO4C,IAAI,CAACiB,IAAI,CAAC,IAAI,CAAC/E,gBAAgB,CAAC9D,MAAM,GAAG,IAAI,CAAC+D,YAAY,CAAC;EACpE;EAEA;EACA1B,kBAAkBA,CAAC2E,OAAuB;IACxC,IAAI,CAAC7B,MAAM,CAAC2D,QAAQ,CAAC,CAAC,mCAAmC,CAAC,EAAE;MAC1DC,WAAW,EAAE;QACXhC,SAAS,EAAEC,OAAO,CAAChB,GAAG;QACtB1D,WAAW,EAAE0E,OAAO,CAAC1E;;KAExB,CAAC;EACJ;EAEAP,kBAAkBA,CAACiF,OAAuB;IACxC,IAAI,CAAC7B,MAAM,CAAC2D,QAAQ,CAAC,CAAC,4BAA4B,CAAC,EAAE;MACnDC,WAAW,EAAE;QACXhC,SAAS,EAAEC,OAAO,CAAChB;;KAEtB,CAAC;EACJ;EAEA9D,wBAAwBA,CAAC8E,OAAuB;IAC9C,IAAI,CAAC7B,MAAM,CAAC2D,QAAQ,CAAC,CAAC,+BAA+B,CAAC,EAAE;MACtDC,WAAW,EAAE;QACXhC,SAAS,EAAEC,OAAO,CAAChB;;KAEtB,CAAC;EACJ;EAEA;EACAgD,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC9E,WAAW,GAAG,IAAI,CAACc,UAAU,EAAE;MACtC,IAAI,CAACd,WAAW,EAAE;;EAEtB;EAEA+E,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC/E,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;;EAEtB;EAEAP,QAAQA,CAACuF,IAAY;IACnB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAAClE,UAAU,EAAE;MACxC,IAAI,CAACd,WAAW,GAAGgF,IAAI;;EAE3B;EAEA;EACAzI,WAAWA,CAAA;IACT,IAAI,CAACqF,mBAAmB,EAAE;EAC5B;EAEA7C,YAAYA,CAAA;IACV,IAAI,CAACK,WAAW,GAAG,EAAE;IACrB,IAAI,CAACiC,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACtB,WAAW,GAAG,CAAC;EACtB;EAEAM,QAAQA,CAAA;IACN,IAAI,CAACN,WAAW,GAAG,CAAC;EACtB;EAEAiF,cAAcA,CAAC5H,OAAc;IAC3B,OAAOA,OAAO,CAAC6H,GAAG,CAAC7B,CAAC,IAAI,GAAGA,CAAC,CAACtG,SAAS,IAAIsG,CAAC,CAACrG,OAAO,EAAE,CAAC,CAACmI,IAAI,CAAC,IAAI,CAAC;EACnE;EAAC,QAAAC,CAAA,G;qBA5MUrE,wBAAwB,EAAAzF,EAAA,CAAA+J,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAjK,EAAA,CAAA+J,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAnK,EAAA,CAAA+J,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAArK,EAAA,CAAA+J,iBAAA,CAAAO,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAxB/E,wBAAwB;IAAAgF,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCnCrC/K,EAAA,CAAAC,cAAA,aAAwC;QAKHD,EAAA,CAAAE,MAAA,cAAO;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC/CH,EAAA,CAAAE,MAAA,oBACA;QAAAF,EAAA,CAAA4B,UAAA,IAAAqJ,wCAAA,kBAA2E;QAC7EjL,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAC,cAAA,WAAyB;QAAAD,EAAA,CAAAE,MAAA,8CAAuC;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAEtEH,EAAA,CAAAC,cAAA,cAA4B;QACFD,EAAA,CAAAU,UAAA,mBAAAwK,2DAAA;UAAA,OAASF,GAAA,CAAA/J,WAAA,EAAa;QAAA,EAAC;QAC7CjB,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAMlCH,EAAA,CAAA4B,UAAA,KAAAuJ,wCAAA,iBAGM;QAGNnL,EAAA,CAAA4B,UAAA,KAAAwJ,wCAAA,mBAQM;QAGNpL,EAAA,CAAA4B,UAAA,KAAAyJ,wCAAA,mBA2GM;QACRrL,EAAA,CAAAG,YAAA,EAAM;;;QAzI+BH,EAAA,CAAAI,SAAA,GAAc;QAAdJ,EAAA,CAAA8B,UAAA,UAAAkJ,GAAA,CAAAzF,OAAA,CAAc;QAY3CvF,EAAA,CAAAI,SAAA,GAAa;QAAbJ,EAAA,CAAA8B,UAAA,SAAAkJ,GAAA,CAAAzF,OAAA,CAAa;QAMbvF,EAAA,CAAAI,SAAA,GAAuB;QAAvBJ,EAAA,CAAA8B,UAAA,SAAAkJ,GAAA,CAAA5J,KAAA,KAAA4J,GAAA,CAAAzF,OAAA,CAAuB;QAWvBvF,EAAA,CAAAI,SAAA,GAAwB;QAAxBJ,EAAA,CAAA8B,UAAA,UAAAkJ,GAAA,CAAAzF,OAAA,KAAAyF,GAAA,CAAA5J,KAAA,CAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}