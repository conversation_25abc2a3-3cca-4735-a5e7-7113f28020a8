{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let SubjectService = /*#__PURE__*/(() => {\n  class SubjectService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = environment.apiUrl; // Replace with your backend URL\n      this.subjectUrl = this.apiUrl + '/subjects';\n    }\n    // Get all subjects\n    getSubjects() {\n      return this.http.get(this.subjectUrl);\n    }\n    getSubjectById(id) {\n      return this.http.get(`${this.apiUrl}/subjects/${id}`);\n    }\n    // Add a new subject\n    addSubject(subjectData) {\n      return this.http.post(this.subjectUrl, subjectData);\n    }\n    // Update an existing subject\n    updateSubject(id, subjectData) {\n      return this.http.put(`${this.subjectUrl}/${id}`, subjectData);\n    }\n    // Delete a subject\n    deleteSubject(id) {\n      return this.http.delete(`${this.subjectUrl}/${id}`);\n    }\n    static #_ = this.ɵfac = function SubjectService_Factory(t) {\n      return new (t || SubjectService)(i0.ɵɵinject(i1.HttpClient));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SubjectService,\n      factory: SubjectService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return SubjectService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}