{"ast": null, "code": "import { Chart, registerables } from 'chart.js';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/dashboard.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/card\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/progress-spinner\";\nimport * as i8 from \"@angular/material/tooltip\";\nconst _c0 = [\"programChart\"];\nconst _c1 = [\"departmentChart\"];\nconst _c2 = [\"attendanceChart\"];\nconst _c3 = [\"attendanceTrendChart\"];\nconst _c4 = [\"attendanceComparisonChart\"];\nconst _c5 = [\"semesterChart\"];\nfunction DashboardOverviewComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading dashboard statistics...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardOverviewComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"mat-icon\", 6);\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function DashboardOverviewComponent_div_2_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.refreshData());\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" Retry \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction DashboardOverviewComponent_div_3_div_85_div_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵelement(1, \"div\", 49);\n    i0.ɵɵelementStart(2, \"div\", 50);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 51);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const program_r19 = ctx.$implicit;\n    const ctx_r18 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"background\", ctx_r18.getProgramColor(program_r19.programName));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(program_r19.programName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", program_r19.studentCount, \" (\", ctx_r18.getPercentage(program_r19.studentCount, ctx_r18.dashboardStats.totals.students), \"%)\");\n  }\n}\nfunction DashboardOverviewComponent_div_3_div_85_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵtemplate(1, DashboardOverviewComponent_div_3_div_85_div_4_div_1_Template, 6, 5, \"div\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r17.dashboardStats.programStats);\n  }\n}\nfunction DashboardOverviewComponent_div_3_div_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 43);\n    i0.ɵɵelement(2, \"canvas\", null, 44);\n    i0.ɵɵtemplate(4, DashboardOverviewComponent_div_3_div_85_div_4_Template, 2, 1, \"div\", 45);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.dashboardStats.programStats);\n  }\n}\nfunction DashboardOverviewComponent_div_3_ng_template_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No program data available\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardOverviewComponent_div_3_div_107_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵelement(1, \"div\", 54);\n    i0.ɵɵelementStart(2, \"span\", 55);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 56);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const stat_r20 = ctx.$implicit;\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", \"status-\" + stat_r20._id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r8.capitalizeFirst(stat_r20._id));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r20.count);\n  }\n}\nfunction DashboardOverviewComponent_div_3_div_122_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵelement(1, \"div\", 61);\n    i0.ɵɵelementStart(2, \"div\", 62);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const dept_r23 = ctx.$implicit;\n    const i_r24 = ctx.index;\n    const ctx_r22 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"background\", ctx_r22.getDepartmentColor(i_r24));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(dept_r23.departmentName);\n  }\n}\nfunction DashboardOverviewComponent_div_3_div_122_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 43);\n    i0.ɵɵelement(2, \"canvas\", null, 57);\n    i0.ɵɵelementStart(4, \"div\", 58);\n    i0.ɵɵtemplate(5, DashboardOverviewComponent_div_3_div_122_div_5_Template, 4, 3, \"div\", 59);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.dashboardStats.departmentStats);\n  }\n}\nfunction DashboardOverviewComponent_div_3_ng_template_123_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No department data available\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardOverviewComponent_div_3_div_134_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"canvas\", null, 63);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardOverviewComponent_div_3_ng_template_135_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No attendance comparison data available\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardOverviewComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"h1\");\n    i0.ɵɵtext(3, \"Principal Dashboard\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function DashboardOverviewComponent_div_3_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.refreshData());\n    });\n    i0.ɵɵelementStart(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"refresh\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 11)(8, \"mat-card\", 12)(9, \"mat-card-content\")(10, \"div\", 13)(11, \"div\", 14)(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"school\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 15)(15, \"h3\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p\");\n    i0.ɵɵtext(18, \"Total Students\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(19, \"mat-card\", 16)(20, \"mat-card-content\")(21, \"div\", 13)(22, \"div\", 14)(23, \"mat-icon\");\n    i0.ɵɵtext(24, \"person\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 15)(26, \"h3\");\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"p\");\n    i0.ɵɵtext(29, \"Total Teachers\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(30, \"mat-card\", 17)(31, \"mat-card-content\")(32, \"div\", 13)(33, \"div\", 14)(34, \"mat-icon\");\n    i0.ɵɵtext(35, \"library_books\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"div\", 15)(37, \"h3\");\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"p\");\n    i0.ɵɵtext(40, \"Active Programs\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(41, \"mat-card\", 18)(42, \"mat-card-content\")(43, \"div\", 13)(44, \"div\", 14)(45, \"mat-icon\");\n    i0.ɵɵtext(46, \"business\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"div\", 15)(48, \"h3\");\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"p\");\n    i0.ɵɵtext(51, \"Departments\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(52, \"mat-card\", 19)(53, \"mat-card-content\")(54, \"div\", 13)(55, \"div\", 14)(56, \"mat-icon\");\n    i0.ɵɵtext(57, \"class\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(58, \"div\", 15)(59, \"h3\");\n    i0.ɵɵtext(60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(61, \"p\");\n    i0.ɵɵtext(62, \"Active Classes\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(63, \"mat-card\", 20)(64, \"mat-card-content\")(65, \"div\", 13)(66, \"div\", 14)(67, \"mat-icon\");\n    i0.ɵɵtext(68, \"subject\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(69, \"div\", 15)(70, \"h3\");\n    i0.ɵɵtext(71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(72, \"p\");\n    i0.ɵɵtext(73, \"Total Subjects\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(74, \"div\", 21)(75, \"div\", 22)(76, \"mat-card\", 23)(77, \"mat-card-header\")(78, \"mat-card-title\")(79, \"mat-icon\");\n    i0.ɵɵtext(80, \"pie_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(81, \" Students by Program \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(82, \"mat-card-subtitle\");\n    i0.ɵɵtext(83, \"Distribution of students across different programs\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(84, \"mat-card-content\");\n    i0.ɵɵtemplate(85, DashboardOverviewComponent_div_3_div_85_Template, 5, 1, \"div\", 24);\n    i0.ɵɵtemplate(86, DashboardOverviewComponent_div_3_ng_template_86_Template, 5, 0, \"ng-template\", null, 25, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(88, \"mat-card\", 26)(89, \"mat-card-header\")(90, \"mat-card-title\")(91, \"mat-icon\");\n    i0.ɵɵtext(92, \"access_time\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(93, \" Attendance Overview (Last 30 Days) \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(94, \"mat-card-subtitle\");\n    i0.ɵɵtext(95, \"Daily attendance trends and statistics\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(96, \"mat-card-content\")(97, \"div\", 27)(98, \"div\", 28)(99, \"div\", 29)(100, \"div\", 30)(101, \"div\", 31)(102, \"h2\");\n    i0.ɵɵtext(103);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(104, \"p\");\n    i0.ɵɵtext(105, \"Overall Attendance\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(106, \"div\", 32);\n    i0.ɵɵtemplate(107, DashboardOverviewComponent_div_3_div_107_Template, 6, 3, \"div\", 33);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(108, \"div\", 34)(109, \"div\", 35);\n    i0.ɵɵelement(110, \"canvas\", null, 36);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(112, \"div\", 22)(113, \"mat-card\", 23)(114, \"mat-card-header\")(115, \"mat-card-title\")(116, \"mat-icon\");\n    i0.ɵɵtext(117, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(118, \" Students by Department \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(119, \"mat-card-subtitle\");\n    i0.ɵɵtext(120, \"Student enrollment across departments\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(121, \"mat-card-content\");\n    i0.ɵɵtemplate(122, DashboardOverviewComponent_div_3_div_122_Template, 6, 1, \"div\", 24);\n    i0.ɵɵtemplate(123, DashboardOverviewComponent_div_3_ng_template_123_Template, 5, 0, \"ng-template\", null, 25, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(125, \"mat-card\", 23)(126, \"mat-card-header\")(127, \"mat-card-title\")(128, \"mat-icon\");\n    i0.ɵɵtext(129, \"compare\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(130, \" Attendance by Department \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(131, \"mat-card-subtitle\");\n    i0.ɵɵtext(132, \"Attendance rates across different departments\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(133, \"mat-card-content\");\n    i0.ɵɵtemplate(134, DashboardOverviewComponent_div_3_div_134_Template, 3, 0, \"div\", 24);\n    i0.ɵɵtemplate(135, DashboardOverviewComponent_div_3_ng_template_135_Template, 5, 0, \"ng-template\", null, 25, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(137, \"div\", 37)(138, \"h2\");\n    i0.ɵɵtext(139, \"\\uD83D\\uDE80 Quick Administrative Actions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(140, \"p\");\n    i0.ɵɵtext(141, \"Common tasks you can perform right away\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(142, \"div\", 38)(143, \"button\", 39)(144, \"mat-icon\");\n    i0.ɵɵtext(145, \"person_add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(146, \" Enroll New Student \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(147, \"button\", 40)(148, \"mat-icon\");\n    i0.ɵɵtext(149, \"person_add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(150, \" Add New Teacher \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(151, \"button\", 41)(152, \"mat-icon\");\n    i0.ɵɵtext(153, \"class\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(154, \" Create New Class \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(155, \"button\", 42)(156, \"mat-icon\");\n    i0.ɵɵtext(157, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(158, \" Add New Department \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const _r6 = i0.ɵɵreference(87);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(16);\n    i0.ɵɵtextInterpolate(ctx_r2.dashboardStats.totals.students);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r2.dashboardStats.totals.teachers);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r2.dashboardStats.totals.programs);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r2.dashboardStats.totals.departments);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r2.dashboardStats.totals.classes);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r2.dashboardStats.totals.subjects);\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r2.dashboardStats == null ? null : ctx_r2.dashboardStats.programStats) && ctx_r2.dashboardStats.programStats.length > 0)(\"ngIfElse\", _r6);\n    i0.ɵɵadvance(15);\n    i0.ɵɵstyleProp(\"background\", ctx_r2.getAttendancePercentageColor());\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.getAttendancePercentage(), \"%\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.dashboardStats == null ? null : ctx_r2.dashboardStats.attendanceStats);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r2.dashboardStats == null ? null : ctx_r2.dashboardStats.departmentStats) && ctx_r2.dashboardStats.departmentStats.length > 0)(\"ngIfElse\", _r6);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r2.dashboardStats == null ? null : ctx_r2.dashboardStats.departmentAttendance) && ctx_r2.dashboardStats.departmentAttendance.length > 0)(\"ngIfElse\", _r6);\n  }\n}\nexport class DashboardOverviewComponent {\n  constructor(dashboardService) {\n    this.dashboardService = dashboardService;\n    this.dashboardStats = null;\n    this.loading = true;\n    this.error = null;\n    // Chart instances\n    // Update your chart instance declarations\n    this.programChart = null;\n    this.departmentChart = null;\n    this.attendanceChart = null;\n    this.attendanceTrendChart = null;\n    this.attendanceComparisonChart = null;\n    this.semesterChart = null;\n    // Color palettes\n    this.programColors = ['#4E79A7', '#F28E2B', '#E15759', '#76B7B2', '#59A14F', '#EDC948', '#B07AA1', '#FF9DA7'];\n    this.departmentColors = ['#4285F4', '#34A853', '#EA4335', '#FBBC05', '#673AB7', '#FF5722', '#009688', '#795548'];\n    this.attendanceColors = {\n      'present': '#4CAF50',\n      'absent': '#F44336',\n      'late': '#FFC107',\n      'excused': '#9C27B0',\n      'sick': '#2196F3'\n    };\n    Chart.register(...registerables);\n  }\n  ngOnInit() {\n    this.loadDashboardStats();\n  }\n  ngAfterViewInit() {\n    // Charts will be initialized after data is loaded\n  }\n  ngOnDestroy() {\n    this.destroyCharts();\n  }\n  loadDashboardStats() {\n    this.loading = true;\n    this.error = null;\n    this.dashboardService.getPrincipalDashboardStats().subscribe({\n      next: response => {\n        if (response.success) {\n          this.dashboardStats = response.stats;\n          this.prepareChartData();\n        } else {\n          this.error = 'Failed to load dashboard statistics';\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading dashboard stats:', error);\n        this.error = 'Error loading dashboard statistics';\n        this.loading = false;\n      }\n    });\n  }\n  prepareChartData() {\n    if (!this.dashboardStats) return;\n    this.destroyCharts();\n    setTimeout(() => {\n      this.createProgramChart();\n      this.createDepartmentChart();\n      this.createAttendanceChart();\n      this.createAttendanceTrendChart();\n      this.createAttendanceComparisonChart();\n      this.createSemesterChart();\n    }, 100);\n  }\n  destroyCharts() {\n    // Destroy all chart instances\n    [this.programChart, this.departmentChart, this.attendanceChart, this.attendanceTrendChart, this.attendanceComparisonChart, this.semesterChart].forEach(chart => {\n      if (chart) {\n        chart.destroy();\n      }\n    });\n  }\n  createProgramChart() {\n    if (!this.dashboardStats?.programStats || !this.programChartRef) return;\n    const labels = this.dashboardStats.programStats.map(p => p.programName);\n    const data = this.dashboardStats.programStats.map(p => p.studentCount);\n    const backgroundColors = labels.map((_, i) => this.programColors[i % this.programColors.length]);\n    this.programChart = new Chart(this.programChartRef.nativeElement, {\n      type: 'doughnut',\n      data: {\n        labels,\n        datasets: [{\n          data,\n          backgroundColor: backgroundColors,\n          borderWidth: 1,\n          borderColor: '#fff'\n        }]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        cutout: '70%',\n        plugins: {\n          legend: {\n            display: false\n          },\n          tooltip: {\n            callbacks: {\n              label: context => {\n                const label = context.label || '';\n                const value = context.parsed;\n                const total = context.dataset.data.reduce((a, b) => a + b, 0);\n                const percentage = Math.round(value / total * 100);\n                return `${label}: ${value} (${percentage}%)`;\n              }\n            }\n          }\n        }\n      }\n    });\n  }\n  createDepartmentChart() {\n    if (!this.dashboardStats?.departmentStats || !this.departmentChartRef) return;\n    const labels = this.dashboardStats.departmentStats.map(d => d.departmentName);\n    const data = this.dashboardStats.departmentStats.map(d => d.studentCount);\n    const backgroundColors = labels.map((_, i) => this.departmentColors[i % this.departmentColors.length]);\n    this.departmentChart = new Chart(this.departmentChartRef.nativeElement, {\n      type: 'bar',\n      data: {\n        labels,\n        datasets: [{\n          label: 'Students',\n          data,\n          backgroundColor: backgroundColors,\n          borderColor: backgroundColors.map(c => this.darkenColor(c, 20)),\n          borderWidth: 1\n        }]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        plugins: {\n          legend: {\n            display: false\n          },\n          tooltip: {\n            callbacks: {\n              label: context => `${context.parsed.y} students`\n            }\n          }\n        },\n        scales: {\n          y: {\n            beginAtZero: true,\n            title: {\n              display: true,\n              text: 'Number of Students'\n            }\n          },\n          x: {\n            grid: {\n              display: false\n            }\n          }\n        }\n      }\n    });\n  }\n  createAttendanceChart() {\n    if (!this.dashboardStats?.attendanceStats || !this.attendanceChartRef) return;\n    const labels = this.dashboardStats.attendanceStats.map(a => this.capitalizeFirst(a._id));\n    const data = this.dashboardStats.attendanceStats.map(a => a.count);\n    const backgroundColors = this.dashboardStats.attendanceStats.map(a => this.attendanceColors[a._id]);\n    this.attendanceChart = new Chart(this.attendanceChartRef.nativeElement, {\n      type: 'doughnut',\n      data: {\n        labels,\n        datasets: [{\n          data,\n          backgroundColor: backgroundColors,\n          borderColor: backgroundColors.map(c => this.darkenColor(c, 20)),\n          borderWidth: 2\n        }]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        plugins: {\n          legend: {\n            display: false\n          },\n          tooltip: {\n            callbacks: {\n              label: context => {\n                const label = context.label || '';\n                const value = context.parsed;\n                const total = context.dataset.data.reduce((a, b) => a + b, 0);\n                const percentage = Math.round(value / total * 100);\n                return `${label}: ${value} (${percentage}%)`;\n              }\n            }\n          }\n        }\n      }\n    });\n  }\n  createAttendanceTrendChart() {\n    if (!this.dashboardStats?.attendanceTrend || !this.attendanceTrendChartRef) return;\n    const labels = this.dashboardStats.attendanceTrend.map(t => {\n      const date = new Date(t.date);\n      return `${date.getDate()}/${date.getMonth() + 1}`;\n    });\n    const presentData = this.dashboardStats.attendanceTrend.map(t => t.present);\n    const absentData = this.dashboardStats.attendanceTrend.map(t => t.absent);\n    this.attendanceTrendChart = new Chart(this.attendanceTrendChartRef.nativeElement, {\n      type: 'line',\n      data: {\n        labels,\n        datasets: [{\n          label: 'Present',\n          data: presentData,\n          borderColor: '#4CAF50',\n          backgroundColor: 'rgba(76, 175, 80, 0.1)',\n          borderWidth: 2,\n          tension: 0.3,\n          fill: true\n        }, {\n          label: 'Absent',\n          data: absentData,\n          borderColor: '#F44336',\n          backgroundColor: 'rgba(244, 67, 54, 0.1)',\n          borderWidth: 2,\n          tension: 0.3,\n          fill: true\n        }]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        plugins: {\n          tooltip: {\n            mode: 'index',\n            intersect: false\n          },\n          legend: {\n            position: 'top'\n          }\n        },\n        scales: {\n          y: {\n            beginAtZero: true,\n            title: {\n              display: true,\n              text: 'Number of Students'\n            }\n          },\n          x: {\n            title: {\n              display: true,\n              text: 'Date'\n            }\n          }\n        }\n      }\n    });\n  }\n  createAttendanceComparisonChart() {\n    if (!this.dashboardStats?.departmentAttendance || !this.attendanceComparisonChartRef) return;\n    const labels = this.dashboardStats.departmentAttendance.map(d => d.departmentName);\n    const attendanceRates = this.dashboardStats.departmentAttendance.map(d => d.attendanceRate);\n    this.attendanceComparisonChart = new Chart(this.attendanceComparisonChartRef.nativeElement, {\n      type: 'bar',\n      data: {\n        labels,\n        datasets: [{\n          label: 'Attendance Rate (%)',\n          data: attendanceRates,\n          backgroundColor: this.departmentColors.slice(0, labels.length),\n          borderColor: this.departmentColors.slice(0, labels.length).map(c => this.darkenColor(c, 20)),\n          borderWidth: 1\n        }]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        plugins: {\n          tooltip: {\n            callbacks: {\n              label: context => `${context.parsed.y}% attendance`\n            }\n          },\n          legend: {\n            display: false\n          }\n        },\n        scales: {\n          y: {\n            beginAtZero: true,\n            max: 100,\n            title: {\n              display: true,\n              text: 'Attendance Rate (%)'\n            }\n          },\n          x: {\n            grid: {\n              display: false\n            }\n          }\n        }\n      }\n    });\n  }\n  createSemesterChart() {\n    if (!this.dashboardStats?.semesterStats || !this.semesterChartRef) return;\n    const labels = this.dashboardStats.semesterStats.map(s => `Semester ${s._id}`);\n    const data = this.dashboardStats.semesterStats.map(s => s.count);\n    this.semesterChart = new Chart(this.semesterChartRef.nativeElement, {\n      type: 'bar',\n      data: {\n        labels,\n        datasets: [{\n          label: 'Students',\n          data,\n          backgroundColor: '#FF6384',\n          borderColor: '#FF6384',\n          borderWidth: 1\n        }]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        plugins: {\n          legend: {\n            display: false\n          },\n          tooltip: {\n            callbacks: {\n              label: context => `${context.parsed.y} students`\n            }\n          }\n        },\n        scales: {\n          y: {\n            beginAtZero: true,\n            title: {\n              display: true,\n              text: 'Number of Students'\n            }\n          },\n          x: {\n            grid: {\n              display: false\n            }\n          }\n        }\n      }\n    });\n  }\n  // Helper methods\n  getProgramColor(programName) {\n    const index = this.dashboardStats?.programStats.findIndex(p => p.programName === programName) || 0;\n    return this.programColors[index % this.programColors.length];\n  }\n  getDepartmentColor(index) {\n    return this.departmentColors[index % this.departmentColors.length];\n  }\n  getPercentage(value, total) {\n    return total > 0 ? Math.round(value / total * 100) : 0;\n  }\n  getAttendancePercentage() {\n    if (!this.dashboardStats?.attendanceStats?.length) return 0;\n    const totalAttendance = this.dashboardStats.attendanceStats.reduce((sum, stat) => sum + stat.count, 0);\n    const presentCount = this.dashboardStats.attendanceStats.find(stat => stat._id === 'present')?.count || 0;\n    return totalAttendance > 0 ? Math.round(presentCount / totalAttendance * 100) : 0;\n  }\n  getAttendancePercentageColor() {\n    const percentage = this.getAttendancePercentage();\n    if (percentage >= 90) return '#4CAF50';\n    if (percentage >= 75) return '#FFC107';\n    return '';\n  }\n  capitalizeFirst(str) {\n    return str.charAt(0).toUpperCase() + str.slice(1);\n  }\n  darkenColor(color, percent) {\n    // Helper function to darken colors for borders\n    const num = parseInt(color.replace('#', ''), 16);\n    const amt = Math.round(2.55 * percent);\n    const R = (num >> 16) - amt;\n    const G = (num >> 8 & 0x00FF) - amt;\n    const B = (num & 0x0000FF) - amt;\n    return `#${(0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 + (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 + (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1)}`;\n  }\n  refreshData() {\n    this.loadDashboardStats();\n  }\n  static #_ = this.ɵfac = function DashboardOverviewComponent_Factory(t) {\n    return new (t || DashboardOverviewComponent)(i0.ɵɵdirectiveInject(i1.DashboardService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: DashboardOverviewComponent,\n    selectors: [[\"app-dashboard-overview\"]],\n    viewQuery: function DashboardOverviewComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n        i0.ɵɵviewQuery(_c2, 5);\n        i0.ɵɵviewQuery(_c3, 5);\n        i0.ɵɵviewQuery(_c4, 5);\n        i0.ɵɵviewQuery(_c5, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.programChartRef = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.departmentChartRef = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.attendanceChartRef = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.attendanceTrendChartRef = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.attendanceComparisonChartRef = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.semesterChartRef = _t.first);\n      }\n    },\n    decls: 4,\n    vars: 3,\n    consts: [[1, \"dashboard-overview\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"dashboard-content\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"error-container\"], [\"color\", \"warn\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"dashboard-content\"], [1, \"dashboard-header\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Refresh Data\", 3, \"click\"], [1, \"stats-grid\"], [1, \"stat-card\", \"students\"], [1, \"stat-content\"], [1, \"stat-icon\"], [1, \"stat-info\"], [1, \"stat-card\", \"teachers\"], [1, \"stat-card\", \"programs\"], [1, \"stat-card\", \"departments\"], [1, \"stat-card\", \"classes\"], [1, \"stat-card\", \"subjects\"], [1, \"charts-section\"], [1, \"chart-row\"], [1, \"chart-card\"], [\"class\", \"chart-container\", 4, \"ngIf\", \"ngIfElse\"], [\"noData\", \"\"], [1, \"chart-card\", \"attendance-card\"], [1, \"attendance-container\"], [1, \"attendance-summary\"], [1, \"attendance-percentage\"], [1, \"percentage-circle\"], [1, \"percentage-value\"], [1, \"attendance-breakdown\"], [\"class\", \"breakdown-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"attendance-trend\"], [1, \"chart-container\"], [\"attendanceTrendChart\", \"\"], [1, \"quick-actions\"], [1, \"action-buttons\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/dashboard/admin/students/add-student\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/dashboard/admin/teacher/teacher-form\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"routerLink\", \"/dashboard/admin/classes/class-form\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"routerLink\", \"/dashboard/admin/department/department-form\"], [1, \"chart-wrapper\"], [\"programChart\", \"\"], [\"class\", \"chart-summary\", 4, \"ngIf\"], [1, \"chart-summary\"], [\"class\", \"summary-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"summary-item\"], [1, \"color-indicator\"], [1, \"program-name\"], [1, \"program-count\"], [1, \"no-data\"], [1, \"breakdown-item\"], [1, \"status-indicator\", 3, \"ngClass\"], [1, \"status-label\"], [1, \"status-count\"], [\"departmentChart\", \"\"], [1, \"chart-legend\"], [\"class\", \"legend-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"legend-item\"], [1, \"legend-color\"], [1, \"legend-label\"], [\"attendanceComparisonChart\", \"\"]],\n    template: function DashboardOverviewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵtemplate(1, DashboardOverviewComponent_div_1_Template, 4, 0, \"div\", 1);\n        i0.ɵɵtemplate(2, DashboardOverviewComponent_div_2_Template, 9, 1, \"div\", 2);\n        i0.ɵɵtemplate(3, DashboardOverviewComponent_div_3_Template, 159, 16, \"div\", 3);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.dashboardStats && !ctx.loading && !ctx.error);\n      }\n    },\n    dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i3.RouterLink, i4.MatButton, i4.MatIconButton, i5.MatCard, i5.MatCardContent, i5.MatCardHeader, i5.MatCardSubtitle, i5.MatCardTitle, i6.MatIcon, i7.MatProgressSpinner, i8.MatTooltip],\n    styles: [\".dashboard-overview[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  \\n\\n  min-height: 100vh;\\n  animation: _ngcontent-%COMP%_fadeIn 0.6s ease-in-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n.loading-container[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 400px;\\n  text-align: center;\\n}\\n\\n.error-container[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  margin-bottom: 16px;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 30px;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #333;\\n  font-weight: 500;\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 30px;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease-in-out;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  position: relative;\\n  background: white;\\n  box-shadow: 0 2px 8px rgba(0,0,0,0.08);\\n}\\n\\n.stat-card[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 4px;\\n  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);\\n  transform: scaleX(0);\\n  transition: transform 0.3s ease-in-out;\\n}\\n\\n.stat-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(0,0,0,0.15);\\n}\\n\\n.stat-card[_ngcontent-%COMP%]:hover::before {\\n  transform: scaleX(1);\\n}\\n\\n.stat-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n\\n.stat-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  color: white;\\n}\\n\\n.stat-card.students[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n}\\n\\n.stat-card.teachers[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\\n}\\n\\n.stat-card.programs[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\\n}\\n\\n.stat-card.departments[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\\n}\\n\\n.stat-card.classes[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);\\n}\\n\\n.stat-card.subjects[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);\\n}\\n\\n.stat-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  height: 28px;\\n  width: 28px;\\n}\\n\\n.stat-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 2rem;\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.stat-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 4px 0 0 0;\\n  color: #666;\\n  font-size: 0.9rem;\\n}\\n\\n.charts-section[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.chart-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 20px;\\n  margin-bottom: 20px;\\n}\\n\\n.chart-card[_ngcontent-%COMP%] {\\n  min-height: 400px;\\n  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;\\n}\\n\\n.chart-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 16px rgba(0,0,0,0.15);\\n}\\n\\n.chart-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.chart-card[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.9rem;\\n  margin-top: 4px;\\n}\\n\\n.chart-container[_ngcontent-%COMP%] {\\n  height: 280px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n}\\n\\n.simple-chart[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.chart-item[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 2fr auto;\\n  gap: 12px;\\n  align-items: center;\\n  margin-bottom: 12px;\\n}\\n\\n.chart-label[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #666;\\n}\\n\\n.chart-bar[_ngcontent-%COMP%] {\\n  height: 20px;\\n  background-color: #e0e0e0;\\n  border-radius: 10px;\\n  overflow: hidden;\\n}\\n\\n.chart-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 10px;\\n  transition: width 0.3s ease;\\n}\\n\\n.chart-value[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #333;\\n  font-size: 0.9rem;\\n}\\n\\n\\n\\n.attendance-card[_ngcontent-%COMP%] {\\n  \\n\\n  color: black;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  position: relative;\\n}\\n\\n.attendance-card[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(255, 255, 255, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  z-index: 0;\\n}\\n\\n.attendance-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%], .attendance-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n\\n\\n.attendance-summary[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 24px;\\n  align-items: center;\\n  height: 100%;\\n}\\n\\n.attendance-percentage[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 20px;\\n}\\n\\n.percentage-circle[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 16px;\\n}\\n\\n.percentage-value[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.15);\\n  border-radius: 50%;\\n  width: 120px;\\n  height: 120px;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.percentage-value[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: -50%;\\n  left: -50%;\\n  width: 200%;\\n  height: 200%;\\n  background: conic-gradient(\\n    from 0deg,\\n    transparent 0deg,\\n    rgba(255, 255, 255, 0.1) 90deg,\\n    transparent 180deg\\n  );\\n  animation: _ngcontent-%COMP%_rotate 3s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_rotate {\\n  from { transform: rotate(0deg); }\\n  to { transform: rotate(360deg); }\\n}\\n\\n.percentage-value[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 2.2rem;\\n  font-weight: 700;\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.percentage-value[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 4px 0 0 0;\\n  font-size: 0.8rem;\\n  opacity: 0.9;\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.attendance-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n  width: 100%;\\n}\\n\\n.stat-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 8px 12px;\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 8px;\\n  -webkit-backdrop-filter: blur(5px);\\n          backdrop-filter: blur(5px);\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n  transition: all 0.3s ease;\\n}\\n\\n.stat-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.15);\\n  transform: translateX(4px);\\n}\\n\\n.stat-indicator[_ngcontent-%COMP%] {\\n  width: 12px;\\n  height: 12px;\\n  border-radius: 50%;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\\n}\\n\\n.stat-indicator.status-present[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4CAF50, #45a049);\\n}\\n\\n.stat-indicator.status-absent[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #F44336, #d32f2f);\\n}\\n\\n.stat-indicator.status-late[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #FF9800, #f57c00);\\n}\\n\\n.stat-indicator.status-excused[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #9C27B0, #7b1fa2);\\n}\\n\\n.stat-label[_ngcontent-%COMP%] {\\n  flex: 1;\\n  font-weight: 500;\\n  color: white;\\n}\\n\\n.stat-count[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: white;\\n  background: rgba(255, 255, 255, 0.2);\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 0.9rem;\\n}\\n\\n.attendance-breakdown[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n  width: 100%;\\n}\\n\\n.attendance-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 8px;\\n  background-color: #f9f9f9;\\n  border-radius: 4px;\\n}\\n\\n.status-indicator[_ngcontent-%COMP%] {\\n  width: 12px;\\n  height: 12px;\\n  border-radius: 50%;\\n}\\n\\n.status-indicator.present[_ngcontent-%COMP%] {\\n  background-color: #4caf50;\\n}\\n\\n.status-indicator.absent[_ngcontent-%COMP%] {\\n  background-color: #f44336;\\n}\\n\\n.status-indicator.late[_ngcontent-%COMP%] {\\n  background-color: #ff9800;\\n}\\n\\n.no-data[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: #999;\\n  font-style: italic;\\n  padding: 40px;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.no-data[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  opacity: 0.5;\\n}\\n\\n.no-data[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1rem;\\n}\\n\\n.quick-actions[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 24px;\\n  border-radius: 12px;\\n  box-shadow: 0 4px 12px rgba(0,0,0,0.1);\\n  border: 1px solid rgba(0,0,0,0.05);\\n  transition: all 0.3s ease-in-out;\\n}\\n\\n.quick-actions[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 6px 20px rgba(0,0,0,0.15);\\n}\\n\\n.quick-actions[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0 0 20px 0;\\n  color: #333;\\n  font-weight: 500;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 12px;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n\\n\\n@media (max-width: 1200px) {\\n  .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .dashboard-overview[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n\\n  .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 16px;\\n  }\\n\\n  .chart-row[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 16px;\\n  }\\n\\n  .chart-card[_ngcontent-%COMP%] {\\n    min-height: 350px;\\n  }\\n\\n  .chart-container[_ngcontent-%COMP%] {\\n    height: 250px;\\n  }\\n\\n  .attendance-summary[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 20px;\\n  }\\n\\n  .percentage-value[_ngcontent-%COMP%] {\\n    width: 100px;\\n    height: 100px;\\n  }\\n\\n  .percentage-value[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n    font-size: 1.8rem;\\n  }\\n\\n  .attendance-stats[_ngcontent-%COMP%] {\\n    margin-top: 16px;\\n  }\\n\\n  .action-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n\\n  .action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n\\n  .dashboard-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n}\\n\\n@media (max-width: 480px) {\\n  .dashboard-overview[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n\\n  .chart-card[_ngcontent-%COMP%] {\\n    min-height: 300px;\\n  }\\n\\n  .chart-container[_ngcontent-%COMP%] {\\n    height: 200px;\\n  }\\n\\n  .stat-content[_ngcontent-%COMP%] {\\n    gap: 12px;\\n  }\\n\\n  .stat-icon[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n  }\\n\\n  .stat-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n    font-size: 24px;\\n    height: 24px;\\n    width: 24px;\\n  }\\n\\n  .stat-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Chart", "registerables", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "DashboardOverviewComponent_div_2_Template_button_click_5_listener", "ɵɵrestoreView", "_r4", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "refreshData", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "error", "ɵɵstyleProp", "ctx_r18", "getProgramColor", "program_r19", "programName", "ɵɵtextInterpolate2", "studentCount", "getPercentage", "dashboardStats", "totals", "students", "ɵɵtemplate", "DashboardOverviewComponent_div_3_div_85_div_4_div_1_Template", "ɵɵproperty", "ctx_r17", "programStats", "DashboardOverviewComponent_div_3_div_85_div_4_Template", "ctx_r5", "stat_r20", "_id", "ctx_r8", "capitalizeFirst", "count", "ctx_r22", "getDepartmentColor", "i_r24", "dept_r23", "departmentName", "DashboardOverviewComponent_div_3_div_122_div_5_Template", "ctx_r10", "departmentStats", "DashboardOverviewComponent_div_3_Template_button_click_4_listener", "_r27", "ctx_r26", "DashboardOverviewComponent_div_3_div_85_Template", "DashboardOverviewComponent_div_3_ng_template_86_Template", "ɵɵtemplateRefExtractor", "DashboardOverviewComponent_div_3_div_107_Template", "DashboardOverviewComponent_div_3_div_122_Template", "DashboardOverviewComponent_div_3_ng_template_123_Template", "DashboardOverviewComponent_div_3_div_134_Template", "DashboardOverviewComponent_div_3_ng_template_135_Template", "ctx_r2", "teachers", "programs", "departments", "classes", "subjects", "length", "_r6", "getAttendancePercentageColor", "ɵɵtextInterpolate1", "getAttendancePercentage", "attendanceStats", "departmentAttendance", "DashboardOverviewComponent", "constructor", "dashboardService", "loading", "programChart", "departmentChart", "attendanceChart", "attendanceTrendChart", "attendanceComparisonChart", "semesterChart", "programColors", "departmentColors", "attendanceColors", "register", "ngOnInit", "loadDashboardStats", "ngAfterViewInit", "ngOnDestroy", "<PERSON><PERSON><PERSON><PERSON>", "getPrincipalDashboardStats", "subscribe", "next", "response", "success", "stats", "prepareChartData", "console", "setTimeout", "createProgramChart", "createDepartmentChart", "createAttendanceChart", "createAttendanceTrendChart", "createAttendanceComparisonChart", "createSemesterChart", "for<PERSON>ach", "chart", "destroy", "programChartRef", "labels", "map", "p", "data", "backgroundColors", "_", "i", "nativeElement", "type", "datasets", "backgroundColor", "borderWidth", "borderColor", "options", "responsive", "maintainAspectRatio", "cutout", "plugins", "legend", "display", "tooltip", "callbacks", "label", "context", "value", "parsed", "total", "dataset", "reduce", "a", "b", "percentage", "Math", "round", "departmentChartRef", "d", "c", "darkenColor", "y", "scales", "beginAtZero", "title", "text", "x", "grid", "attendanceChartRef", "attendanceTrend", "attendanceTrendChartRef", "t", "date", "Date", "getDate", "getMonth", "presentData", "present", "absentData", "absent", "tension", "fill", "mode", "intersect", "position", "attendanceComparisonChartRef", "attendanceRates", "attendanceRate", "slice", "max", "semesterStats", "semesterChartRef", "s", "index", "findIndex", "totalAttendance", "sum", "stat", "presentCount", "find", "str", "char<PERSON>t", "toUpperCase", "color", "percent", "num", "parseInt", "replace", "amt", "R", "G", "B", "toString", "ɵɵdirectiveInject", "i1", "DashboardService", "_2", "selectors", "viewQuery", "DashboardOverviewComponent_Query", "rf", "ctx", "DashboardOverviewComponent_div_1_Template", "DashboardOverviewComponent_div_2_Template", "DashboardOverviewComponent_div_3_Template"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\dashboard-overview\\dashboard-overview.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\dashboard-overview\\dashboard-overview.component.html"], "sourcesContent": ["import { Component, OnInit, AfterViewInit, OnDestroy, ViewChild, ElementRef } from '@angular/core';\r\nimport { DashboardService } from '../../../services/dashboard.service';\r\nimport { Chart, registerables } from 'chart.js';\r\nimport { DashboardStats } from '../../../models/dashboard';\r\n\r\n@Component({\r\n  selector: 'app-dashboard-overview',\r\n  templateUrl: './dashboard-overview.component.html',\r\n  styleUrls: ['./dashboard-overview.component.css']\r\n})\r\nexport class DashboardOverviewComponent implements OnInit, AfterViewInit, OnDestroy {\r\n  dashboardStats: DashboardStats | null = null;\r\n  loading = true;\r\n  error: string | null = null;\r\n\r\n  // Chart references\r\n  @ViewChild('programChart') programChartRef!: ElementRef<HTMLCanvasElement>;\r\n  @ViewChild('departmentChart') departmentChartRef!: ElementRef<HTMLCanvasElement>;\r\n  @ViewChild('attendanceChart') attendanceChartRef!: ElementRef<HTMLCanvasElement>;\r\n  @ViewChild('attendanceTrendChart') attendanceTrendChartRef!: ElementRef<HTMLCanvasElement>;\r\n  @ViewChild('attendanceComparisonChart') attendanceComparisonChartRef!: ElementRef<HTMLCanvasElement>;\r\n  @ViewChild('semesterChart') semesterChartRef!: ElementRef<HTMLCanvasElement>;\r\n\r\n  // Chart instances\r\n  // Update your chart instance declarations\r\n  programChart: Chart<'doughnut', number[], string> | null = null;\r\n  departmentChart: Chart<'bar', number[], string> | null = null;\r\n  attendanceChart: Chart<'doughnut', number[], string> | null = null;\r\n  attendanceTrendChart: Chart<'line', number[], string> | null = null;\r\n  attendanceComparisonChart: Chart<'bar', number[], string> | null = null;\r\n  semesterChart: Chart<'bar', number[], string> | null = null;\r\n  // Color palettes\r\n  private programColors = [\r\n    '#4E79A7', '#F28E2B', '#E15759', '#76B7B2',\r\n    '#59A14F', '#EDC948', '#B07AA1', '#FF9DA7'\r\n  ];\r\n\r\n  private departmentColors = [\r\n    '#4285F4', '#34A853', '#EA4335', '#FBBC05',\r\n    '#673AB7', '#FF5722', '#009688', '#795548'\r\n  ];\r\n\r\n  private attendanceColors: { [key: string]: string } = {\r\n    'present': '#4CAF50',\r\n    'absent': '#F44336',\r\n    'late': '#FFC107',\r\n    'excused': '#9C27B0',\r\n    'sick': '#2196F3'\r\n  };\r\n\r\n  constructor(private dashboardService: DashboardService) {\r\n    Chart.register(...registerables);\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.loadDashboardStats();\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    // Charts will be initialized after data is loaded\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.destroyCharts();\r\n  }\r\n\r\n  loadDashboardStats(): void {\r\n    this.loading = true;\r\n    this.error = null;\r\n\r\n    this.dashboardService.getPrincipalDashboardStats().subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.dashboardStats = response.stats;\r\n          this.prepareChartData();\r\n        } else {\r\n          this.error = 'Failed to load dashboard statistics';\r\n        }\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading dashboard stats:', error);\r\n        this.error = 'Error loading dashboard statistics';\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  private prepareChartData(): void {\r\n    if (!this.dashboardStats) return;\r\n\r\n    this.destroyCharts();\r\n\r\n    setTimeout(() => {\r\n      this.createProgramChart();\r\n      this.createDepartmentChart();\r\n      this.createAttendanceChart();\r\n      this.createAttendanceTrendChart();\r\n      this.createAttendanceComparisonChart();\r\n      this.createSemesterChart();\r\n    }, 100);\r\n  }\r\n\r\n  private destroyCharts(): void {\r\n    // Destroy all chart instances\r\n    [\r\n      this.programChart,\r\n      this.departmentChart,\r\n      this.attendanceChart,\r\n      this.attendanceTrendChart,\r\n      this.attendanceComparisonChart,\r\n      this.semesterChart\r\n    ].forEach(chart => {\r\n      if (chart) {\r\n        chart.destroy();\r\n      }\r\n    });\r\n  }\r\n\r\n  private createProgramChart(): void {\r\n    if (!this.dashboardStats?.programStats || !this.programChartRef) return;\r\n\r\n    const labels = this.dashboardStats.programStats.map(p => p.programName);\r\n    const data = this.dashboardStats.programStats.map(p => p.studentCount);\r\n    const backgroundColors = labels.map((_, i) => this.programColors[i % this.programColors.length]);\r\n\r\n    this.programChart = new Chart(this.programChartRef.nativeElement, {\r\n      type: 'doughnut',\r\n      data: {\r\n        labels,\r\n        datasets: [{\r\n          data,\r\n          backgroundColor: backgroundColors,\r\n          borderWidth: 1,\r\n          borderColor: '#fff'\r\n        }]\r\n      },\r\n      options: {\r\n        responsive: true,\r\n        maintainAspectRatio: false,\r\n        cutout: '70%',\r\n        plugins: {\r\n          legend: {\r\n            display: false\r\n          },\r\n          tooltip: {\r\n            callbacks: {\r\n              label: (context) => {\r\n                const label = context.label || '';\r\n                const value = context.parsed;\r\n                const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);\r\n                const percentage = Math.round((value / total) * 100);\r\n                return `${label}: ${value} (${percentage}%)`;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  private createDepartmentChart(): void {\r\n    if (!this.dashboardStats?.departmentStats || !this.departmentChartRef) return;\r\n\r\n    const labels = this.dashboardStats.departmentStats.map(d => d.departmentName);\r\n    const data = this.dashboardStats.departmentStats.map(d => d.studentCount);\r\n    const backgroundColors = labels.map((_, i) => this.departmentColors[i % this.departmentColors.length]);\r\n\r\n    this.departmentChart = new Chart(this.departmentChartRef.nativeElement, {\r\n      type: 'bar',\r\n      data: {\r\n        labels,\r\n        datasets: [{\r\n          label: 'Students',\r\n          data,\r\n          backgroundColor: backgroundColors,\r\n          borderColor: backgroundColors.map(c => this.darkenColor(c, 20)),\r\n          borderWidth: 1\r\n        }]\r\n      },\r\n      options: {\r\n        responsive: true,\r\n        maintainAspectRatio: false,\r\n        plugins: {\r\n          legend: {\r\n            display: false\r\n          },\r\n          tooltip: {\r\n            callbacks: {\r\n              label: (context) => `${context.parsed.y} students`\r\n            }\r\n          }\r\n        },\r\n        scales: {\r\n          y: {\r\n            beginAtZero: true,\r\n            title: {\r\n              display: true,\r\n              text: 'Number of Students'\r\n            }\r\n          },\r\n          x: {\r\n            grid: {\r\n              display: false\r\n            }\r\n          }\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  private createAttendanceChart(): void {\r\n    if (!this.dashboardStats?.attendanceStats || !this.attendanceChartRef) return;\r\n\r\n    const labels = this.dashboardStats.attendanceStats.map(a => this.capitalizeFirst(a._id));\r\n    const data = this.dashboardStats.attendanceStats.map(a => a.count);\r\n    const backgroundColors = this.dashboardStats.attendanceStats.map(a => this.attendanceColors[a._id]);\r\n\r\n    this.attendanceChart = new Chart(this.attendanceChartRef.nativeElement, {\r\n      type: 'doughnut',\r\n      data: {\r\n        labels,\r\n        datasets: [{\r\n          data,\r\n          backgroundColor: backgroundColors,\r\n          borderColor: backgroundColors.map(c => this.darkenColor(c, 20)),\r\n          borderWidth: 2\r\n        }]\r\n      },\r\n      options: {\r\n        responsive: true,\r\n        maintainAspectRatio: false,\r\n        plugins: {\r\n          legend: {\r\n            display: false\r\n          },\r\n          tooltip: {\r\n            callbacks: {\r\n              label: (context) => {\r\n                const label = context.label || '';\r\n                const value = context.parsed;\r\n                const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);\r\n                const percentage = Math.round((value / total) * 100);\r\n                return `${label}: ${value} (${percentage}%)`;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  private createAttendanceTrendChart(): void {\r\n    if (!this.dashboardStats?.attendanceTrend || !this.attendanceTrendChartRef) return;\r\n\r\n    const labels = this.dashboardStats.attendanceTrend.map(t => {\r\n      const date = new Date(t.date);\r\n      return `${date.getDate()}/${date.getMonth() + 1}`;\r\n    });\r\n    const presentData = this.dashboardStats.attendanceTrend.map(t => t.present);\r\n    const absentData = this.dashboardStats.attendanceTrend.map(t => t.absent);\r\n\r\n    this.attendanceTrendChart = new Chart(this.attendanceTrendChartRef.nativeElement, {\r\n      type: 'line',\r\n      data: {\r\n        labels,\r\n        datasets: [\r\n          {\r\n            label: 'Present',\r\n            data: presentData,\r\n            borderColor: '#4CAF50',\r\n            backgroundColor: 'rgba(76, 175, 80, 0.1)',\r\n            borderWidth: 2,\r\n            tension: 0.3,\r\n            fill: true\r\n          },\r\n          {\r\n            label: 'Absent',\r\n            data: absentData,\r\n            borderColor: '#F44336',\r\n            backgroundColor: 'rgba(244, 67, 54, 0.1)',\r\n            borderWidth: 2,\r\n            tension: 0.3,\r\n            fill: true\r\n          }\r\n        ]\r\n      },\r\n      options: {\r\n        responsive: true,\r\n        maintainAspectRatio: false,\r\n        plugins: {\r\n          tooltip: {\r\n            mode: 'index',\r\n            intersect: false\r\n          },\r\n          legend: {\r\n            position: 'top',\r\n          }\r\n        },\r\n        scales: {\r\n          y: {\r\n            beginAtZero: true,\r\n            title: {\r\n              display: true,\r\n              text: 'Number of Students'\r\n            }\r\n          },\r\n          x: {\r\n            title: {\r\n              display: true,\r\n              text: 'Date'\r\n            }\r\n          }\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  private createAttendanceComparisonChart(): void {\r\n    if (!this.dashboardStats?.departmentAttendance || !this.attendanceComparisonChartRef) return;\r\n\r\n    const labels = this.dashboardStats.departmentAttendance.map(d => d.departmentName);\r\n    const attendanceRates = this.dashboardStats.departmentAttendance.map(d => d.attendanceRate);\r\n\r\n    this.attendanceComparisonChart = new Chart(this.attendanceComparisonChartRef.nativeElement, {\r\n      type: 'bar',\r\n      data: {\r\n        labels,\r\n        datasets: [{\r\n          label: 'Attendance Rate (%)',\r\n          data: attendanceRates,\r\n          backgroundColor: this.departmentColors.slice(0, labels.length),\r\n          borderColor: this.departmentColors.slice(0, labels.length).map(c => this.darkenColor(c, 20)),\r\n          borderWidth: 1\r\n        }]\r\n      },\r\n      options: {\r\n        responsive: true,\r\n        maintainAspectRatio: false,\r\n        plugins: {\r\n          tooltip: {\r\n            callbacks: {\r\n              label: (context) => `${context.parsed.y}% attendance`\r\n            }\r\n          },\r\n          legend: {\r\n            display: false\r\n          }\r\n        },\r\n        scales: {\r\n          y: {\r\n            beginAtZero: true,\r\n            max: 100,\r\n            title: {\r\n              display: true,\r\n              text: 'Attendance Rate (%)'\r\n            }\r\n          },\r\n          x: {\r\n            grid: {\r\n              display: false\r\n            }\r\n          }\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  private createSemesterChart(): void {\r\n    if (!this.dashboardStats?.semesterStats || !this.semesterChartRef) return;\r\n\r\n    const labels = this.dashboardStats.semesterStats.map(s => `Semester ${s._id}`);\r\n    const data = this.dashboardStats.semesterStats.map(s => s.count);\r\n\r\n    this.semesterChart = new Chart(this.semesterChartRef.nativeElement, {\r\n      type: 'bar',\r\n      data: {\r\n        labels,\r\n        datasets: [{\r\n          label: 'Students',\r\n          data,\r\n          backgroundColor: '#FF6384',\r\n          borderColor: '#FF6384',\r\n          borderWidth: 1\r\n        }]\r\n      },\r\n      options: {\r\n        responsive: true,\r\n        maintainAspectRatio: false,\r\n        plugins: {\r\n          legend: {\r\n            display: false\r\n          },\r\n          tooltip: {\r\n            callbacks: {\r\n              label: (context) => `${context.parsed.y} students`\r\n            }\r\n          }\r\n        },\r\n        scales: {\r\n          y: {\r\n            beginAtZero: true,\r\n            title: {\r\n              display: true,\r\n              text: 'Number of Students'\r\n            }\r\n          },\r\n          x: {\r\n            grid: {\r\n              display: false\r\n            }\r\n          }\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  // Helper methods\r\n  getProgramColor(programName: string): string {\r\n    const index = this.dashboardStats?.programStats.findIndex(p => p.programName === programName) || 0;\r\n    return this.programColors[index % this.programColors.length];\r\n  }\r\n\r\n  getDepartmentColor(index: number): string {\r\n    return this.departmentColors[index % this.departmentColors.length];\r\n  }\r\n\r\n  getPercentage(value: number, total: number): number {\r\n    return total > 0 ? Math.round((value / total) * 100) : 0;\r\n  }\r\n\r\n  getAttendancePercentage(): number {\r\n    if (!this.dashboardStats?.attendanceStats?.length) return 0;\r\n\r\n    const totalAttendance = this.dashboardStats.attendanceStats.reduce((sum, stat) => sum + stat.count, 0);\r\n    const presentCount = this.dashboardStats.attendanceStats.find(stat => stat._id === 'present')?.count || 0;\r\n\r\n    return totalAttendance > 0 ? Math.round((presentCount / totalAttendance) * 100) : 0;\r\n  }\r\n\r\n  getAttendancePercentageColor(): string {\r\n    const percentage = this.getAttendancePercentage();\r\n    if (percentage >= 90) return '#4CAF50';\r\n    if (percentage >= 75) return '#FFC107';\r\n    return '';\r\n  }\r\n\r\n  capitalizeFirst(str: string): string {\r\n    return str.charAt(0).toUpperCase() + str.slice(1);\r\n  }\r\n\r\n  darkenColor(color: string, percent: number): string {\r\n    // Helper function to darken colors for borders\r\n    const num = parseInt(color.replace('#', ''), 16);\r\n    const amt = Math.round(2.55 * percent);\r\n    const R = (num >> 16) - amt;\r\n    const G = ((num >> 8) & 0x00FF) - amt;\r\n    const B = (num & 0x0000FF) - amt;\r\n    return `#${(\r\n      0x1000000 +\r\n      (R < 255 ? (R < 1 ? 0 : R) : 255) * 0x10000 +\r\n      (G < 255 ? (G < 1 ? 0 : G) : 255) * 0x100 +\r\n      (B < 255 ? (B < 1 ? 0 : B) : 255)\r\n    ).toString(16).slice(1)}`;\r\n  }\r\n\r\n  refreshData(): void {\r\n    this.loadDashboardStats();\r\n  }\r\n}", "<div class=\"dashboard-overview\">\r\n  <!-- Loading State -->\r\n  <div *ngIf=\"loading\" class=\"loading-container\">\r\n    <mat-spinner></mat-spinner>\r\n    <p>Loading dashboard statistics...</p>\r\n  </div>\r\n\r\n  <!-- Error State -->\r\n  <div *ngIf=\"error && !loading\" class=\"error-container\">\r\n    <mat-icon color=\"warn\">error</mat-icon>\r\n    <p>{{ error }}</p>\r\n    <button mat-raised-button color=\"primary\" (click)=\"refreshData()\">\r\n      <mat-icon>refresh</mat-icon>\r\n      Retry\r\n    </button>\r\n  </div>\r\n\r\n  <!-- Dashboard Content -->\r\n  <div *ngIf=\"dashboardStats && !loading && !error\" class=\"dashboard-content\">\r\n    <!-- Header -->\r\n    <div class=\"dashboard-header\">\r\n      <h1>Principal Dashboard</h1>\r\n      <button mat-icon-button (click)=\"refreshData()\" matTooltip=\"Refresh Data\">\r\n        <mat-icon>refresh</mat-icon>\r\n      </button>\r\n    </div>\r\n\r\n    <!-- Statistics Cards -->\r\n    <div class=\"stats-grid\">\r\n      <mat-card class=\"stat-card students\">\r\n        <mat-card-content>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon\">\r\n              <mat-icon>school</mat-icon>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ dashboardStats.totals.students }}</h3>\r\n              <p>Total Students</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n\r\n      <mat-card class=\"stat-card teachers\">\r\n        <mat-card-content>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon\">\r\n              <mat-icon>person</mat-icon>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ dashboardStats.totals.teachers }}</h3>\r\n              <p>Total Teachers</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n\r\n      <mat-card class=\"stat-card programs\">\r\n        <mat-card-content>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon\">\r\n              <mat-icon>library_books</mat-icon>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ dashboardStats.totals.programs }}</h3>\r\n              <p>Active Programs</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n\r\n      <mat-card class=\"stat-card departments\">\r\n        <mat-card-content>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon\">\r\n              <mat-icon>business</mat-icon>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ dashboardStats.totals.departments }}</h3>\r\n              <p>Departments</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n\r\n      <mat-card class=\"stat-card classes\">\r\n        <mat-card-content>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon\">\r\n              <mat-icon>class</mat-icon>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ dashboardStats.totals.classes }}</h3>\r\n              <p>Active Classes</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n\r\n      <mat-card class=\"stat-card subjects\">\r\n        <mat-card-content>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon\">\r\n              <mat-icon>subject</mat-icon>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ dashboardStats.totals.subjects }}</h3>\r\n              <p>Total Subjects</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n    </div>\r\n\r\n     <!-- Enhanced Charts Section -->\r\n     <div class=\"charts-section\">\r\n      <div class=\"chart-row\">\r\n        <!-- Program Distribution - Enhanced -->\r\n        <mat-card class=\"chart-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>\r\n              <mat-icon>pie_chart</mat-icon>\r\n              Students by Program\r\n            </mat-card-title>\r\n            <mat-card-subtitle>Distribution of students across different programs</mat-card-subtitle>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div class=\"chart-container\" *ngIf=\"dashboardStats?.programStats && dashboardStats.programStats.length > 0; else noData\">\r\n              <div class=\"chart-wrapper\">\r\n                <canvas #programChart></canvas>\r\n                <div class=\"chart-summary\" *ngIf=\"dashboardStats.programStats\">\r\n                  <div class=\"summary-item\" *ngFor=\"let program of dashboardStats.programStats\">\r\n                    <div class=\"color-indicator\" [style.background]=\"getProgramColor(program.programName)\"></div>\r\n                    <div class=\"program-name\">{{ program.programName }}</div>\r\n                    <div class=\"program-count\">{{ program.studentCount }} ({{ getPercentage(program.studentCount, dashboardStats.totals.students) }}%)</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <ng-template #noData>\r\n              <div class=\"no-data\">\r\n                <mat-icon>info</mat-icon>\r\n                <p>No program data available</p>\r\n              </div>\r\n            </ng-template>\r\n          </mat-card-content>\r\n        </mat-card>\r\n\r\n        <!-- Attendance Overview - Enhanced -->\r\n        <mat-card class=\"chart-card attendance-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>\r\n              <mat-icon>access_time</mat-icon>\r\n              Attendance Overview (Last 30 Days)\r\n            </mat-card-title>\r\n            <mat-card-subtitle>Daily attendance trends and statistics</mat-card-subtitle>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div class=\"attendance-container\">\r\n              <div class=\"attendance-summary\">\r\n                <div class=\"attendance-percentage\">\r\n                  <div class=\"percentage-circle\" [style.background]=\"getAttendancePercentageColor()\">\r\n                    <div class=\"percentage-value\">\r\n                      <h2>{{ getAttendancePercentage() }}%</h2>\r\n                      <p>Overall Attendance</p>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"attendance-breakdown\">\r\n                    <div class=\"breakdown-item\" *ngFor=\"let stat of dashboardStats?.attendanceStats\">\r\n                      <div class=\"status-indicator\" [ngClass]=\"'status-' + stat._id\"></div>\r\n                      <span class=\"status-label\">{{ capitalizeFirst(stat._id) }}</span>\r\n                      <span class=\"status-count\">{{ stat.count }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"attendance-trend\">\r\n                <div class=\"chart-container\">\r\n                  <canvas #attendanceTrendChart></canvas>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n      </div>\r\n\r\n      <div class=\"chart-row\">\r\n        <!-- Department Distribution - Enhanced -->\r\n        <mat-card class=\"chart-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>\r\n              <mat-icon>business</mat-icon>\r\n              Students by Department\r\n            </mat-card-title>\r\n            <mat-card-subtitle>Student enrollment across departments</mat-card-subtitle>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div class=\"chart-container\" *ngIf=\"dashboardStats?.departmentStats && dashboardStats.departmentStats.length > 0; else noData\">\r\n              <div class=\"chart-wrapper\">\r\n                <canvas #departmentChart></canvas>\r\n                <div class=\"chart-legend\">\r\n                  <div class=\"legend-item\" *ngFor=\"let dept of dashboardStats.departmentStats; let i = index\">\r\n                    <div class=\"legend-color\" [style.background]=\"getDepartmentColor(i)\"></div>\r\n                    <div class=\"legend-label\">{{ dept.departmentName }}</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <ng-template #noData>\r\n              <div class=\"no-data\">\r\n                <mat-icon>info</mat-icon>\r\n                <p>No department data available</p>\r\n              </div>\r\n            </ng-template>\r\n          </mat-card-content>\r\n        </mat-card>\r\n\r\n        <!-- Attendance Comparison - New Chart -->\r\n        <mat-card class=\"chart-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>\r\n              <mat-icon>compare</mat-icon>\r\n              Attendance by Department\r\n            </mat-card-title>\r\n            <mat-card-subtitle>Attendance rates across different departments</mat-card-subtitle>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div class=\"chart-container\" *ngIf=\"dashboardStats?.departmentAttendance && dashboardStats.departmentAttendance.length > 0; else noData\">\r\n              <canvas #attendanceComparisonChart></canvas>\r\n            </div>\r\n            <ng-template #noData>\r\n              <div class=\"no-data\">\r\n                <mat-icon>info</mat-icon>\r\n                <p>No attendance comparison data available</p>\r\n              </div>\r\n            </ng-template>\r\n          </mat-card-content>\r\n        </mat-card>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Quick Actions -->\r\n    <div class=\"quick-actions\">\r\n      <h2>🚀 Quick Administrative Actions</h2>\r\n      <p>Common tasks you can perform right away</p>\r\n      <div class=\"action-buttons\">\r\n        <button mat-raised-button color=\"primary\" routerLink=\"/dashboard/admin/students/add-student\">\r\n          <mat-icon>person_add</mat-icon>\r\n          Enroll New Student\r\n        </button>\r\n        <button mat-raised-button color=\"primary\" routerLink=\"/dashboard/admin/teacher/teacher-form\">\r\n          <mat-icon>person_add</mat-icon>\r\n          Add New Teacher\r\n        </button>\r\n        <button mat-raised-button color=\"accent\" routerLink=\"/dashboard/admin/classes/class-form\">\r\n          <mat-icon>class</mat-icon>\r\n          Create New Class\r\n        </button>\r\n        <button mat-raised-button color=\"accent\" routerLink=\"/dashboard/admin/department/department-form\">\r\n          <mat-icon>business</mat-icon>\r\n          Add New Department\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n"], "mappings": "AAEA,SAASA,KAAK,EAAEC,aAAa,QAAQ,UAAU;;;;;;;;;;;;;;;;;;ICA7CC,EAAA,CAAAC,cAAA,aAA+C;IAC7CD,EAAA,CAAAE,SAAA,kBAA2B;IAC3BF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,sCAA+B;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;;IAIxCJ,EAAA,CAAAC,cAAA,aAAuD;IAC9BD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACvCJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAW;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAClBJ,EAAA,CAAAC,cAAA,gBAAkE;IAAxBD,EAAA,CAAAK,UAAA,mBAAAC,kEAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAC/DZ,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC5BJ,EAAA,CAAAG,MAAA,cACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAJNJ,EAAA,CAAAa,SAAA,GAAW;IAAXb,EAAA,CAAAc,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IAyHAhB,EAAA,CAAAC,cAAA,cAA8E;IAC5ED,EAAA,CAAAE,SAAA,cAA6F;IAC7FF,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAG,MAAA,GAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACzDJ,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAG,MAAA,GAAuG;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAF3GJ,EAAA,CAAAa,SAAA,GAAyD;IAAzDb,EAAA,CAAAiB,WAAA,eAAAC,OAAA,CAAAC,eAAA,CAAAC,WAAA,CAAAC,WAAA,EAAyD;IAC5DrB,EAAA,CAAAa,SAAA,GAAyB;IAAzBb,EAAA,CAAAc,iBAAA,CAAAM,WAAA,CAAAC,WAAA,CAAyB;IACxBrB,EAAA,CAAAa,SAAA,GAAuG;IAAvGb,EAAA,CAAAsB,kBAAA,KAAAF,WAAA,CAAAG,YAAA,QAAAL,OAAA,CAAAM,aAAA,CAAAJ,WAAA,CAAAG,YAAA,EAAAL,OAAA,CAAAO,cAAA,CAAAC,MAAA,CAAAC,QAAA,QAAuG;;;;;IAJtI3B,EAAA,CAAAC,cAAA,cAA+D;IAC7DD,EAAA,CAAA4B,UAAA,IAAAC,4DAAA,kBAIM;IACR7B,EAAA,CAAAI,YAAA,EAAM;;;;IAL0CJ,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAA8B,UAAA,YAAAC,OAAA,CAAAN,cAAA,CAAAO,YAAA,CAA8B;;;;;IAJlFhC,EAAA,CAAAC,cAAA,cAAyH;IAErHD,EAAA,CAAAE,SAAA,uBAA+B;IAC/BF,EAAA,CAAA4B,UAAA,IAAAK,sDAAA,kBAMM;IACRjC,EAAA,CAAAI,YAAA,EAAM;;;;IAPwBJ,EAAA,CAAAa,SAAA,GAAiC;IAAjCb,EAAA,CAAA8B,UAAA,SAAAI,MAAA,CAAAT,cAAA,CAAAO,YAAA,CAAiC;;;;;IAU/DhC,EAAA,CAAAC,cAAA,cAAqB;IACTD,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACzBJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,gCAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IA0B5BJ,EAAA,CAAAC,cAAA,cAAiF;IAC/ED,EAAA,CAAAE,SAAA,cAAqE;IACrEF,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAG,MAAA,GAA+B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACjEJ,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAG,MAAA,GAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAFpBJ,EAAA,CAAAa,SAAA,GAAgC;IAAhCb,EAAA,CAAA8B,UAAA,wBAAAK,QAAA,CAAAC,GAAA,CAAgC;IACnCpC,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAAc,iBAAA,CAAAuB,MAAA,CAAAC,eAAA,CAAAH,QAAA,CAAAC,GAAA,EAA+B;IAC/BpC,EAAA,CAAAa,SAAA,GAAgB;IAAhBb,EAAA,CAAAc,iBAAA,CAAAqB,QAAA,CAAAI,KAAA,CAAgB;;;;;IA8B/CvC,EAAA,CAAAC,cAAA,cAA4F;IAC1FD,EAAA,CAAAE,SAAA,cAA2E;IAC3EF,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAG,MAAA,GAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;;IAD/BJ,EAAA,CAAAa,SAAA,GAA0C;IAA1Cb,EAAA,CAAAiB,WAAA,eAAAuB,OAAA,CAAAC,kBAAA,CAAAC,KAAA,EAA0C;IAC1C1C,EAAA,CAAAa,SAAA,GAAyB;IAAzBb,EAAA,CAAAc,iBAAA,CAAA6B,QAAA,CAAAC,cAAA,CAAyB;;;;;IAN3D5C,EAAA,CAAAC,cAAA,cAA+H;IAE3HD,EAAA,CAAAE,SAAA,uBAAkC;IAClCF,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAA4B,UAAA,IAAAiB,uDAAA,kBAGM;IACR7C,EAAA,CAAAI,YAAA,EAAM;;;;IAJsCJ,EAAA,CAAAa,SAAA,GAAmC;IAAnCb,EAAA,CAAA8B,UAAA,YAAAgB,OAAA,CAAArB,cAAA,CAAAsB,eAAA,CAAmC;;;;;IAQjF/C,EAAA,CAAAC,cAAA,cAAqB;IACTD,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACzBJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,mCAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAgBvCJ,EAAA,CAAAC,cAAA,cAAyI;IACvID,EAAA,CAAAE,SAAA,uBAA4C;IAC9CF,EAAA,CAAAI,YAAA,EAAM;;;;;IAEJJ,EAAA,CAAAC,cAAA,cAAqB;IACTD,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACzBJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,8CAAuC;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;;IAvN5DJ,EAAA,CAAAC,cAAA,aAA4E;IAGpED,EAAA,CAAAG,MAAA,0BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC5BJ,EAAA,CAAAC,cAAA,iBAA0E;IAAlDD,EAAA,CAAAK,UAAA,mBAAA2C,kEAAA;MAAAhD,EAAA,CAAAO,aAAA,CAAA0C,IAAA;MAAA,MAAAC,OAAA,GAAAlD,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAuC,OAAA,CAAAtC,WAAA,EAAa;IAAA,EAAC;IAC7CZ,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAKhCJ,EAAA,CAAAC,cAAA,cAAwB;IAKJD,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAE7BJ,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAG,MAAA,IAAoC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC7CJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,sBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAM7BJ,EAAA,CAAAC,cAAA,oBAAqC;IAInBD,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAE7BJ,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAG,MAAA,IAAoC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC7CJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,sBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAM7BJ,EAAA,CAAAC,cAAA,oBAAqC;IAInBD,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAEpCJ,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAG,MAAA,IAAoC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC7CJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,uBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAM9BJ,EAAA,CAAAC,cAAA,oBAAwC;IAItBD,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAE/BJ,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAG,MAAA,IAAuC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAChDJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,mBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAM1BJ,EAAA,CAAAC,cAAA,oBAAoC;IAIlBD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAE5BJ,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAG,MAAA,IAAmC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC5CJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,sBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAM7BJ,EAAA,CAAAC,cAAA,oBAAqC;IAInBD,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAE9BJ,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAG,MAAA,IAAoC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC7CJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,sBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAQ9BJ,EAAA,CAAAC,cAAA,eAA4B;IAMTD,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC9BJ,EAAA,CAAAG,MAAA,6BACF;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IACjBJ,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAG,MAAA,0DAAkD;IAAAH,EAAA,CAAAI,YAAA,EAAoB;IAE3FJ,EAAA,CAAAC,cAAA,wBAAkB;IAChBD,EAAA,CAAA4B,UAAA,KAAAuB,gDAAA,kBAWM;IACNnD,EAAA,CAAA4B,UAAA,KAAAwB,wDAAA,iCAAApD,EAAA,CAAAqD,sBAAA,CAKc;IAChBrD,EAAA,CAAAI,YAAA,EAAmB;IAIrBJ,EAAA,CAAAC,cAAA,oBAA6C;IAG7BD,EAAA,CAAAG,MAAA,mBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAChCJ,EAAA,CAAAG,MAAA,4CACF;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IACjBJ,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAG,MAAA,8CAAsC;IAAAH,EAAA,CAAAI,YAAA,EAAoB;IAE/EJ,EAAA,CAAAC,cAAA,wBAAkB;IAMFD,EAAA,CAAAG,MAAA,KAAgC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACzCJ,EAAA,CAAAC,cAAA,UAAG;IAAAD,EAAA,CAAAG,MAAA,2BAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAG7BJ,EAAA,CAAAC,cAAA,gBAAkC;IAChCD,EAAA,CAAA4B,UAAA,MAAA0B,iDAAA,kBAIM;IACRtD,EAAA,CAAAI,YAAA,EAAM;IAGVJ,EAAA,CAAAC,cAAA,gBAA8B;IAE1BD,EAAA,CAAAE,SAAA,yBAAuC;IACzCF,EAAA,CAAAI,YAAA,EAAM;IAOhBJ,EAAA,CAAAC,cAAA,gBAAuB;IAKLD,EAAA,CAAAG,MAAA,iBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC7BJ,EAAA,CAAAG,MAAA,iCACF;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IACjBJ,EAAA,CAAAC,cAAA,0BAAmB;IAAAD,EAAA,CAAAG,MAAA,8CAAqC;IAAAH,EAAA,CAAAI,YAAA,EAAoB;IAE9EJ,EAAA,CAAAC,cAAA,yBAAkB;IAChBD,EAAA,CAAA4B,UAAA,MAAA2B,iDAAA,kBAUM;IACNvD,EAAA,CAAA4B,UAAA,MAAA4B,yDAAA,iCAAAxD,EAAA,CAAAqD,sBAAA,CAKc;IAChBrD,EAAA,CAAAI,YAAA,EAAmB;IAIrBJ,EAAA,CAAAC,cAAA,qBAA6B;IAGbD,EAAA,CAAAG,MAAA,gBAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC5BJ,EAAA,CAAAG,MAAA,mCACF;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IACjBJ,EAAA,CAAAC,cAAA,0BAAmB;IAAAD,EAAA,CAAAG,MAAA,sDAA6C;IAAAH,EAAA,CAAAI,YAAA,EAAoB;IAEtFJ,EAAA,CAAAC,cAAA,yBAAkB;IAChBD,EAAA,CAAA4B,UAAA,MAAA6B,iDAAA,kBAEM;IACNzD,EAAA,CAAA4B,UAAA,MAAA8B,yDAAA,iCAAA1D,EAAA,CAAAqD,sBAAA,CAKc;IAChBrD,EAAA,CAAAI,YAAA,EAAmB;IAMzBJ,EAAA,CAAAC,cAAA,gBAA2B;IACrBD,EAAA,CAAAG,MAAA,kDAA+B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACxCJ,EAAA,CAAAC,cAAA,UAAG;IAAAD,EAAA,CAAAG,MAAA,gDAAuC;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAC9CJ,EAAA,CAAAC,cAAA,gBAA4B;IAEdD,EAAA,CAAAG,MAAA,mBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC/BJ,EAAA,CAAAG,MAAA,6BACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,mBAA6F;IACjFD,EAAA,CAAAG,MAAA,mBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC/BJ,EAAA,CAAAG,MAAA,0BACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,mBAA0F;IAC9ED,EAAA,CAAAG,MAAA,cAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC1BJ,EAAA,CAAAG,MAAA,2BACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,mBAAkG;IACtFD,EAAA,CAAAG,MAAA,iBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC7BJ,EAAA,CAAAG,MAAA,6BACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;IAjOCJ,EAAA,CAAAa,SAAA,IAAoC;IAApCb,EAAA,CAAAc,iBAAA,CAAA6C,MAAA,CAAAlC,cAAA,CAAAC,MAAA,CAAAC,QAAA,CAAoC;IAcpC3B,EAAA,CAAAa,SAAA,IAAoC;IAApCb,EAAA,CAAAc,iBAAA,CAAA6C,MAAA,CAAAlC,cAAA,CAAAC,MAAA,CAAAkC,QAAA,CAAoC;IAcpC5D,EAAA,CAAAa,SAAA,IAAoC;IAApCb,EAAA,CAAAc,iBAAA,CAAA6C,MAAA,CAAAlC,cAAA,CAAAC,MAAA,CAAAmC,QAAA,CAAoC;IAcpC7D,EAAA,CAAAa,SAAA,IAAuC;IAAvCb,EAAA,CAAAc,iBAAA,CAAA6C,MAAA,CAAAlC,cAAA,CAAAC,MAAA,CAAAoC,WAAA,CAAuC;IAcvC9D,EAAA,CAAAa,SAAA,IAAmC;IAAnCb,EAAA,CAAAc,iBAAA,CAAA6C,MAAA,CAAAlC,cAAA,CAAAC,MAAA,CAAAqC,OAAA,CAAmC;IAcnC/D,EAAA,CAAAa,SAAA,IAAoC;IAApCb,EAAA,CAAAc,iBAAA,CAAA6C,MAAA,CAAAlC,cAAA,CAAAC,MAAA,CAAAsC,QAAA,CAAoC;IAqBZhE,EAAA,CAAAa,SAAA,IAA8E;IAA9Eb,EAAA,CAAA8B,UAAA,UAAA6B,MAAA,CAAAlC,cAAA,kBAAAkC,MAAA,CAAAlC,cAAA,CAAAO,YAAA,KAAA2B,MAAA,CAAAlC,cAAA,CAAAO,YAAA,CAAAiC,MAAA,KAA8E,aAAAC,GAAA;IAkCvElE,EAAA,CAAAa,SAAA,IAAmD;IAAnDb,EAAA,CAAAiB,WAAA,eAAA0C,MAAA,CAAAQ,4BAAA,GAAmD;IAE1EnE,EAAA,CAAAa,SAAA,GAAgC;IAAhCb,EAAA,CAAAoE,kBAAA,KAAAT,MAAA,CAAAU,uBAAA,QAAgC;IAKOrE,EAAA,CAAAa,SAAA,GAAkC;IAAlCb,EAAA,CAAA8B,UAAA,YAAA6B,MAAA,CAAAlC,cAAA,kBAAAkC,MAAA,CAAAlC,cAAA,CAAA6C,eAAA,CAAkC;IA6BzDtE,EAAA,CAAAa,SAAA,IAAoF;IAApFb,EAAA,CAAA8B,UAAA,UAAA6B,MAAA,CAAAlC,cAAA,kBAAAkC,MAAA,CAAAlC,cAAA,CAAAsB,eAAA,KAAAY,MAAA,CAAAlC,cAAA,CAAAsB,eAAA,CAAAkB,MAAA,KAAoF,aAAAC,GAAA;IA8BpFlE,EAAA,CAAAa,SAAA,IAA8F;IAA9Fb,EAAA,CAAA8B,UAAA,UAAA6B,MAAA,CAAAlC,cAAA,kBAAAkC,MAAA,CAAAlC,cAAA,CAAA8C,oBAAA,KAAAZ,MAAA,CAAAlC,cAAA,CAAA8C,oBAAA,CAAAN,MAAA,KAA8F,aAAAC,GAAA;;;ADzNxI,OAAM,MAAOM,0BAA0B;EAwCrCC,YAAoBC,gBAAkC;IAAlC,KAAAA,gBAAgB,GAAhBA,gBAAgB;IAvCpC,KAAAjD,cAAc,GAA0B,IAAI;IAC5C,KAAAkD,OAAO,GAAG,IAAI;IACd,KAAA3D,KAAK,GAAkB,IAAI;IAU3B;IACA;IACA,KAAA4D,YAAY,GAA+C,IAAI;IAC/D,KAAAC,eAAe,GAA0C,IAAI;IAC7D,KAAAC,eAAe,GAA+C,IAAI;IAClE,KAAAC,oBAAoB,GAA2C,IAAI;IACnE,KAAAC,yBAAyB,GAA0C,IAAI;IACvE,KAAAC,aAAa,GAA0C,IAAI;IAC3D;IACQ,KAAAC,aAAa,GAAG,CACtB,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAC1C,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAC3C;IAEO,KAAAC,gBAAgB,GAAG,CACzB,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAC1C,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAC3C;IAEO,KAAAC,gBAAgB,GAA8B;MACpD,SAAS,EAAE,SAAS;MACpB,QAAQ,EAAE,SAAS;MACnB,MAAM,EAAE,SAAS;MACjB,SAAS,EAAE,SAAS;MACpB,MAAM,EAAE;KACT;IAGCtF,KAAK,CAACuF,QAAQ,CAAC,GAAGtF,aAAa,CAAC;EAClC;EAEAuF,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEAC,eAAeA,CAAA;IACb;EAAA;EAGFC,WAAWA,CAAA;IACT,IAAI,CAACC,aAAa,EAAE;EACtB;EAEAH,kBAAkBA,CAAA;IAChB,IAAI,CAACZ,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC3D,KAAK,GAAG,IAAI;IAEjB,IAAI,CAAC0D,gBAAgB,CAACiB,0BAA0B,EAAE,CAACC,SAAS,CAAC;MAC3DC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACtE,cAAc,GAAGqE,QAAQ,CAACE,KAAK;UACpC,IAAI,CAACC,gBAAgB,EAAE;SACxB,MAAM;UACL,IAAI,CAACjF,KAAK,GAAG,qCAAqC;;QAEpD,IAAI,CAAC2D,OAAO,GAAG,KAAK;MACtB,CAAC;MACD3D,KAAK,EAAGA,KAAK,IAAI;QACfkF,OAAO,CAAClF,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAACA,KAAK,GAAG,oCAAoC;QACjD,IAAI,CAAC2D,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEQsB,gBAAgBA,CAAA;IACtB,IAAI,CAAC,IAAI,CAACxE,cAAc,EAAE;IAE1B,IAAI,CAACiE,aAAa,EAAE;IAEpBS,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,kBAAkB,EAAE;MACzB,IAAI,CAACC,qBAAqB,EAAE;MAC5B,IAAI,CAACC,qBAAqB,EAAE;MAC5B,IAAI,CAACC,0BAA0B,EAAE;MACjC,IAAI,CAACC,+BAA+B,EAAE;MACtC,IAAI,CAACC,mBAAmB,EAAE;IAC5B,CAAC,EAAE,GAAG,CAAC;EACT;EAEQf,aAAaA,CAAA;IACnB;IACA,CACE,IAAI,CAACd,YAAY,EACjB,IAAI,CAACC,eAAe,EACpB,IAAI,CAACC,eAAe,EACpB,IAAI,CAACC,oBAAoB,EACzB,IAAI,CAACC,yBAAyB,EAC9B,IAAI,CAACC,aAAa,CACnB,CAACyB,OAAO,CAACC,KAAK,IAAG;MAChB,IAAIA,KAAK,EAAE;QACTA,KAAK,CAACC,OAAO,EAAE;;IAEnB,CAAC,CAAC;EACJ;EAEQR,kBAAkBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAAC3E,cAAc,EAAEO,YAAY,IAAI,CAAC,IAAI,CAAC6E,eAAe,EAAE;IAEjE,MAAMC,MAAM,GAAG,IAAI,CAACrF,cAAc,CAACO,YAAY,CAAC+E,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC3F,WAAW,CAAC;IACvE,MAAM4F,IAAI,GAAG,IAAI,CAACxF,cAAc,CAACO,YAAY,CAAC+E,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACzF,YAAY,CAAC;IACtE,MAAM2F,gBAAgB,GAAGJ,MAAM,CAACC,GAAG,CAAC,CAACI,CAAC,EAAEC,CAAC,KAAK,IAAI,CAAClC,aAAa,CAACkC,CAAC,GAAG,IAAI,CAAClC,aAAa,CAACjB,MAAM,CAAC,CAAC;IAEhG,IAAI,CAACW,YAAY,GAAG,IAAI9E,KAAK,CAAC,IAAI,CAAC+G,eAAe,CAACQ,aAAa,EAAE;MAChEC,IAAI,EAAE,UAAU;MAChBL,IAAI,EAAE;QACJH,MAAM;QACNS,QAAQ,EAAE,CAAC;UACTN,IAAI;UACJO,eAAe,EAAEN,gBAAgB;UACjCO,WAAW,EAAE,CAAC;UACdC,WAAW,EAAE;SACd;OACF;MACDC,OAAO,EAAE;QACPC,UAAU,EAAE,IAAI;QAChBC,mBAAmB,EAAE,KAAK;QAC1BC,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACPC,MAAM,EAAE;YACNC,OAAO,EAAE;WACV;UACDC,OAAO,EAAE;YACPC,SAAS,EAAE;cACTC,KAAK,EAAGC,OAAO,IAAI;gBACjB,MAAMD,KAAK,GAAGC,OAAO,CAACD,KAAK,IAAI,EAAE;gBACjC,MAAME,KAAK,GAAGD,OAAO,CAACE,MAAM;gBAC5B,MAAMC,KAAK,GAAGH,OAAO,CAACI,OAAO,CAACxB,IAAI,CAACyB,MAAM,CAAC,CAACC,CAAS,EAAEC,CAAS,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC;gBAC7E,MAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAAET,KAAK,GAAGE,KAAK,GAAI,GAAG,CAAC;gBACpD,OAAO,GAAGJ,KAAK,KAAKE,KAAK,KAAKO,UAAU,IAAI;cAC9C;;;;;KAKT,CAAC;EACJ;EAEQxC,qBAAqBA,CAAA;IAC3B,IAAI,CAAC,IAAI,CAAC5E,cAAc,EAAEsB,eAAe,IAAI,CAAC,IAAI,CAACiG,kBAAkB,EAAE;IAEvE,MAAMlC,MAAM,GAAG,IAAI,CAACrF,cAAc,CAACsB,eAAe,CAACgE,GAAG,CAACkC,CAAC,IAAIA,CAAC,CAACrG,cAAc,CAAC;IAC7E,MAAMqE,IAAI,GAAG,IAAI,CAACxF,cAAc,CAACsB,eAAe,CAACgE,GAAG,CAACkC,CAAC,IAAIA,CAAC,CAAC1H,YAAY,CAAC;IACzE,MAAM2F,gBAAgB,GAAGJ,MAAM,CAACC,GAAG,CAAC,CAACI,CAAC,EAAEC,CAAC,KAAK,IAAI,CAACjC,gBAAgB,CAACiC,CAAC,GAAG,IAAI,CAACjC,gBAAgB,CAAClB,MAAM,CAAC,CAAC;IAEtG,IAAI,CAACY,eAAe,GAAG,IAAI/E,KAAK,CAAC,IAAI,CAACkJ,kBAAkB,CAAC3B,aAAa,EAAE;MACtEC,IAAI,EAAE,KAAK;MACXL,IAAI,EAAE;QACJH,MAAM;QACNS,QAAQ,EAAE,CAAC;UACTa,KAAK,EAAE,UAAU;UACjBnB,IAAI;UACJO,eAAe,EAAEN,gBAAgB;UACjCQ,WAAW,EAAER,gBAAgB,CAACH,GAAG,CAACmC,CAAC,IAAI,IAAI,CAACC,WAAW,CAACD,CAAC,EAAE,EAAE,CAAC,CAAC;UAC/DzB,WAAW,EAAE;SACd;OACF;MACDE,OAAO,EAAE;QACPC,UAAU,EAAE,IAAI;QAChBC,mBAAmB,EAAE,KAAK;QAC1BE,OAAO,EAAE;UACPC,MAAM,EAAE;YACNC,OAAO,EAAE;WACV;UACDC,OAAO,EAAE;YACPC,SAAS,EAAE;cACTC,KAAK,EAAGC,OAAO,IAAK,GAAGA,OAAO,CAACE,MAAM,CAACa,CAAC;;;SAG5C;QACDC,MAAM,EAAE;UACND,CAAC,EAAE;YACDE,WAAW,EAAE,IAAI;YACjBC,KAAK,EAAE;cACLtB,OAAO,EAAE,IAAI;cACbuB,IAAI,EAAE;;WAET;UACDC,CAAC,EAAE;YACDC,IAAI,EAAE;cACJzB,OAAO,EAAE;;;;;KAKlB,CAAC;EACJ;EAEQ3B,qBAAqBA,CAAA;IAC3B,IAAI,CAAC,IAAI,CAAC7E,cAAc,EAAE6C,eAAe,IAAI,CAAC,IAAI,CAACqF,kBAAkB,EAAE;IAEvE,MAAM7C,MAAM,GAAG,IAAI,CAACrF,cAAc,CAAC6C,eAAe,CAACyC,GAAG,CAAC4B,CAAC,IAAI,IAAI,CAACrG,eAAe,CAACqG,CAAC,CAACvG,GAAG,CAAC,CAAC;IACxF,MAAM6E,IAAI,GAAG,IAAI,CAACxF,cAAc,CAAC6C,eAAe,CAACyC,GAAG,CAAC4B,CAAC,IAAIA,CAAC,CAACpG,KAAK,CAAC;IAClE,MAAM2E,gBAAgB,GAAG,IAAI,CAACzF,cAAc,CAAC6C,eAAe,CAACyC,GAAG,CAAC4B,CAAC,IAAI,IAAI,CAACvD,gBAAgB,CAACuD,CAAC,CAACvG,GAAG,CAAC,CAAC;IAEnG,IAAI,CAAC0C,eAAe,GAAG,IAAIhF,KAAK,CAAC,IAAI,CAAC6J,kBAAkB,CAACtC,aAAa,EAAE;MACtEC,IAAI,EAAE,UAAU;MAChBL,IAAI,EAAE;QACJH,MAAM;QACNS,QAAQ,EAAE,CAAC;UACTN,IAAI;UACJO,eAAe,EAAEN,gBAAgB;UACjCQ,WAAW,EAAER,gBAAgB,CAACH,GAAG,CAACmC,CAAC,IAAI,IAAI,CAACC,WAAW,CAACD,CAAC,EAAE,EAAE,CAAC,CAAC;UAC/DzB,WAAW,EAAE;SACd;OACF;MACDE,OAAO,EAAE;QACPC,UAAU,EAAE,IAAI;QAChBC,mBAAmB,EAAE,KAAK;QAC1BE,OAAO,EAAE;UACPC,MAAM,EAAE;YACNC,OAAO,EAAE;WACV;UACDC,OAAO,EAAE;YACPC,SAAS,EAAE;cACTC,KAAK,EAAGC,OAAO,IAAI;gBACjB,MAAMD,KAAK,GAAGC,OAAO,CAACD,KAAK,IAAI,EAAE;gBACjC,MAAME,KAAK,GAAGD,OAAO,CAACE,MAAM;gBAC5B,MAAMC,KAAK,GAAGH,OAAO,CAACI,OAAO,CAACxB,IAAI,CAACyB,MAAM,CAAC,CAACC,CAAS,EAAEC,CAAS,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC;gBAC7E,MAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAAET,KAAK,GAAGE,KAAK,GAAI,GAAG,CAAC;gBACpD,OAAO,GAAGJ,KAAK,KAAKE,KAAK,KAAKO,UAAU,IAAI;cAC9C;;;;;KAKT,CAAC;EACJ;EAEQtC,0BAA0BA,CAAA;IAChC,IAAI,CAAC,IAAI,CAAC9E,cAAc,EAAEmI,eAAe,IAAI,CAAC,IAAI,CAACC,uBAAuB,EAAE;IAE5E,MAAM/C,MAAM,GAAG,IAAI,CAACrF,cAAc,CAACmI,eAAe,CAAC7C,GAAG,CAAC+C,CAAC,IAAG;MACzD,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,CAAC,CAACC,IAAI,CAAC;MAC7B,OAAO,GAAGA,IAAI,CAACE,OAAO,EAAE,IAAIF,IAAI,CAACG,QAAQ,EAAE,GAAG,CAAC,EAAE;IACnD,CAAC,CAAC;IACF,MAAMC,WAAW,GAAG,IAAI,CAAC1I,cAAc,CAACmI,eAAe,CAAC7C,GAAG,CAAC+C,CAAC,IAAIA,CAAC,CAACM,OAAO,CAAC;IAC3E,MAAMC,UAAU,GAAG,IAAI,CAAC5I,cAAc,CAACmI,eAAe,CAAC7C,GAAG,CAAC+C,CAAC,IAAIA,CAAC,CAACQ,MAAM,CAAC;IAEzE,IAAI,CAACvF,oBAAoB,GAAG,IAAIjF,KAAK,CAAC,IAAI,CAAC+J,uBAAuB,CAACxC,aAAa,EAAE;MAChFC,IAAI,EAAE,MAAM;MACZL,IAAI,EAAE;QACJH,MAAM;QACNS,QAAQ,EAAE,CACR;UACEa,KAAK,EAAE,SAAS;UAChBnB,IAAI,EAAEkD,WAAW;UACjBzC,WAAW,EAAE,SAAS;UACtBF,eAAe,EAAE,wBAAwB;UACzCC,WAAW,EAAE,CAAC;UACd8C,OAAO,EAAE,GAAG;UACZC,IAAI,EAAE;SACP,EACD;UACEpC,KAAK,EAAE,QAAQ;UACfnB,IAAI,EAAEoD,UAAU;UAChB3C,WAAW,EAAE,SAAS;UACtBF,eAAe,EAAE,wBAAwB;UACzCC,WAAW,EAAE,CAAC;UACd8C,OAAO,EAAE,GAAG;UACZC,IAAI,EAAE;SACP;OAEJ;MACD7C,OAAO,EAAE;QACPC,UAAU,EAAE,IAAI;QAChBC,mBAAmB,EAAE,KAAK;QAC1BE,OAAO,EAAE;UACPG,OAAO,EAAE;YACPuC,IAAI,EAAE,OAAO;YACbC,SAAS,EAAE;WACZ;UACD1C,MAAM,EAAE;YACN2C,QAAQ,EAAE;;SAEb;QACDtB,MAAM,EAAE;UACND,CAAC,EAAE;YACDE,WAAW,EAAE,IAAI;YACjBC,KAAK,EAAE;cACLtB,OAAO,EAAE,IAAI;cACbuB,IAAI,EAAE;;WAET;UACDC,CAAC,EAAE;YACDF,KAAK,EAAE;cACLtB,OAAO,EAAE,IAAI;cACbuB,IAAI,EAAE;;;;;KAKf,CAAC;EACJ;EAEQhD,+BAA+BA,CAAA;IACrC,IAAI,CAAC,IAAI,CAAC/E,cAAc,EAAE8C,oBAAoB,IAAI,CAAC,IAAI,CAACqG,4BAA4B,EAAE;IAEtF,MAAM9D,MAAM,GAAG,IAAI,CAACrF,cAAc,CAAC8C,oBAAoB,CAACwC,GAAG,CAACkC,CAAC,IAAIA,CAAC,CAACrG,cAAc,CAAC;IAClF,MAAMiI,eAAe,GAAG,IAAI,CAACpJ,cAAc,CAAC8C,oBAAoB,CAACwC,GAAG,CAACkC,CAAC,IAAIA,CAAC,CAAC6B,cAAc,CAAC;IAE3F,IAAI,CAAC9F,yBAAyB,GAAG,IAAIlF,KAAK,CAAC,IAAI,CAAC8K,4BAA4B,CAACvD,aAAa,EAAE;MAC1FC,IAAI,EAAE,KAAK;MACXL,IAAI,EAAE;QACJH,MAAM;QACNS,QAAQ,EAAE,CAAC;UACTa,KAAK,EAAE,qBAAqB;UAC5BnB,IAAI,EAAE4D,eAAe;UACrBrD,eAAe,EAAE,IAAI,CAACrC,gBAAgB,CAAC4F,KAAK,CAAC,CAAC,EAAEjE,MAAM,CAAC7C,MAAM,CAAC;UAC9DyD,WAAW,EAAE,IAAI,CAACvC,gBAAgB,CAAC4F,KAAK,CAAC,CAAC,EAAEjE,MAAM,CAAC7C,MAAM,CAAC,CAAC8C,GAAG,CAACmC,CAAC,IAAI,IAAI,CAACC,WAAW,CAACD,CAAC,EAAE,EAAE,CAAC,CAAC;UAC5FzB,WAAW,EAAE;SACd;OACF;MACDE,OAAO,EAAE;QACPC,UAAU,EAAE,IAAI;QAChBC,mBAAmB,EAAE,KAAK;QAC1BE,OAAO,EAAE;UACPG,OAAO,EAAE;YACPC,SAAS,EAAE;cACTC,KAAK,EAAGC,OAAO,IAAK,GAAGA,OAAO,CAACE,MAAM,CAACa,CAAC;;WAE1C;UACDpB,MAAM,EAAE;YACNC,OAAO,EAAE;;SAEZ;QACDoB,MAAM,EAAE;UACND,CAAC,EAAE;YACDE,WAAW,EAAE,IAAI;YACjB0B,GAAG,EAAE,GAAG;YACRzB,KAAK,EAAE;cACLtB,OAAO,EAAE,IAAI;cACbuB,IAAI,EAAE;;WAET;UACDC,CAAC,EAAE;YACDC,IAAI,EAAE;cACJzB,OAAO,EAAE;;;;;KAKlB,CAAC;EACJ;EAEQxB,mBAAmBA,CAAA;IACzB,IAAI,CAAC,IAAI,CAAChF,cAAc,EAAEwJ,aAAa,IAAI,CAAC,IAAI,CAACC,gBAAgB,EAAE;IAEnE,MAAMpE,MAAM,GAAG,IAAI,CAACrF,cAAc,CAACwJ,aAAa,CAAClE,GAAG,CAACoE,CAAC,IAAI,YAAYA,CAAC,CAAC/I,GAAG,EAAE,CAAC;IAC9E,MAAM6E,IAAI,GAAG,IAAI,CAACxF,cAAc,CAACwJ,aAAa,CAAClE,GAAG,CAACoE,CAAC,IAAIA,CAAC,CAAC5I,KAAK,CAAC;IAEhE,IAAI,CAAC0C,aAAa,GAAG,IAAInF,KAAK,CAAC,IAAI,CAACoL,gBAAgB,CAAC7D,aAAa,EAAE;MAClEC,IAAI,EAAE,KAAK;MACXL,IAAI,EAAE;QACJH,MAAM;QACNS,QAAQ,EAAE,CAAC;UACTa,KAAK,EAAE,UAAU;UACjBnB,IAAI;UACJO,eAAe,EAAE,SAAS;UAC1BE,WAAW,EAAE,SAAS;UACtBD,WAAW,EAAE;SACd;OACF;MACDE,OAAO,EAAE;QACPC,UAAU,EAAE,IAAI;QAChBC,mBAAmB,EAAE,KAAK;QAC1BE,OAAO,EAAE;UACPC,MAAM,EAAE;YACNC,OAAO,EAAE;WACV;UACDC,OAAO,EAAE;YACPC,SAAS,EAAE;cACTC,KAAK,EAAGC,OAAO,IAAK,GAAGA,OAAO,CAACE,MAAM,CAACa,CAAC;;;SAG5C;QACDC,MAAM,EAAE;UACND,CAAC,EAAE;YACDE,WAAW,EAAE,IAAI;YACjBC,KAAK,EAAE;cACLtB,OAAO,EAAE,IAAI;cACbuB,IAAI,EAAE;;WAET;UACDC,CAAC,EAAE;YACDC,IAAI,EAAE;cACJzB,OAAO,EAAE;;;;;KAKlB,CAAC;EACJ;EAEA;EACA9G,eAAeA,CAACE,WAAmB;IACjC,MAAM+J,KAAK,GAAG,IAAI,CAAC3J,cAAc,EAAEO,YAAY,CAACqJ,SAAS,CAACrE,CAAC,IAAIA,CAAC,CAAC3F,WAAW,KAAKA,WAAW,CAAC,IAAI,CAAC;IAClG,OAAO,IAAI,CAAC6D,aAAa,CAACkG,KAAK,GAAG,IAAI,CAAClG,aAAa,CAACjB,MAAM,CAAC;EAC9D;EAEAxB,kBAAkBA,CAAC2I,KAAa;IAC9B,OAAO,IAAI,CAACjG,gBAAgB,CAACiG,KAAK,GAAG,IAAI,CAACjG,gBAAgB,CAAClB,MAAM,CAAC;EACpE;EAEAzC,aAAaA,CAAC8G,KAAa,EAAEE,KAAa;IACxC,OAAOA,KAAK,GAAG,CAAC,GAAGM,IAAI,CAACC,KAAK,CAAET,KAAK,GAAGE,KAAK,GAAI,GAAG,CAAC,GAAG,CAAC;EAC1D;EAEAnE,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAAC5C,cAAc,EAAE6C,eAAe,EAAEL,MAAM,EAAE,OAAO,CAAC;IAE3D,MAAMqH,eAAe,GAAG,IAAI,CAAC7J,cAAc,CAAC6C,eAAe,CAACoE,MAAM,CAAC,CAAC6C,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAACjJ,KAAK,EAAE,CAAC,CAAC;IACtG,MAAMkJ,YAAY,GAAG,IAAI,CAAChK,cAAc,CAAC6C,eAAe,CAACoH,IAAI,CAACF,IAAI,IAAIA,IAAI,CAACpJ,GAAG,KAAK,SAAS,CAAC,EAAEG,KAAK,IAAI,CAAC;IAEzG,OAAO+I,eAAe,GAAG,CAAC,GAAGxC,IAAI,CAACC,KAAK,CAAE0C,YAAY,GAAGH,eAAe,GAAI,GAAG,CAAC,GAAG,CAAC;EACrF;EAEAnH,4BAA4BA,CAAA;IAC1B,MAAM0E,UAAU,GAAG,IAAI,CAACxE,uBAAuB,EAAE;IACjD,IAAIwE,UAAU,IAAI,EAAE,EAAE,OAAO,SAAS;IACtC,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO,SAAS;IACtC,OAAO,EAAE;EACX;EAEAvG,eAAeA,CAACqJ,GAAW;IACzB,OAAOA,GAAG,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGF,GAAG,CAACZ,KAAK,CAAC,CAAC,CAAC;EACnD;EAEA5B,WAAWA,CAAC2C,KAAa,EAAEC,OAAe;IACxC;IACA,MAAMC,GAAG,GAAGC,QAAQ,CAACH,KAAK,CAACI,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;IAChD,MAAMC,GAAG,GAAGrD,IAAI,CAACC,KAAK,CAAC,IAAI,GAAGgD,OAAO,CAAC;IACtC,MAAMK,CAAC,GAAG,CAACJ,GAAG,IAAI,EAAE,IAAIG,GAAG;IAC3B,MAAME,CAAC,GAAG,CAAEL,GAAG,IAAI,CAAC,GAAI,MAAM,IAAIG,GAAG;IACrC,MAAMG,CAAC,GAAG,CAACN,GAAG,GAAG,QAAQ,IAAIG,GAAG;IAChC,OAAO,IAAI,CACT,SAAS,GACT,CAACC,CAAC,GAAG,GAAG,GAAIA,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAI,GAAG,IAAI,OAAO,GAC3C,CAACC,CAAC,GAAG,GAAG,GAAIA,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAI,GAAG,IAAI,KAAK,IACxCC,CAAC,GAAG,GAAG,GAAIA,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAI,GAAG,CAAC,EACjCC,QAAQ,CAAC,EAAE,CAAC,CAACxB,KAAK,CAAC,CAAC,CAAC,EAAE;EAC3B;EAEAnK,WAAWA,CAAA;IACT,IAAI,CAAC2E,kBAAkB,EAAE;EAC3B;EAAC,QAAA4B,CAAA,G;qBA1cU3C,0BAA0B,EAAAxE,EAAA,CAAAwM,iBAAA,CAAAC,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA1BnI,0BAA0B;IAAAoI,SAAA;IAAAC,SAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;QCVvC/M,EAAA,CAAAC,cAAA,aAAgC;QAE9BD,EAAA,CAAA4B,UAAA,IAAAqL,yCAAA,iBAGM;QAGNjN,EAAA,CAAA4B,UAAA,IAAAsL,yCAAA,iBAOM;QAGNlN,EAAA,CAAA4B,UAAA,IAAAuL,yCAAA,oBAsPM;QACRnN,EAAA,CAAAI,YAAA,EAAM;;;QAvQEJ,EAAA,CAAAa,SAAA,GAAa;QAAbb,EAAA,CAAA8B,UAAA,SAAAkL,GAAA,CAAArI,OAAA,CAAa;QAMb3E,EAAA,CAAAa,SAAA,GAAuB;QAAvBb,EAAA,CAAA8B,UAAA,SAAAkL,GAAA,CAAAhM,KAAA,KAAAgM,GAAA,CAAArI,OAAA,CAAuB;QAUvB3E,EAAA,CAAAa,SAAA,GAA0C;QAA1Cb,EAAA,CAAA8B,UAAA,SAAAkL,GAAA,CAAAvL,cAAA,KAAAuL,GAAA,CAAArI,OAAA,KAAAqI,GAAA,CAAAhM,KAAA,CAA0C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}