<div class="maindiv">
  <div class="secondarydiv">
    <div class="header-section">
      <h2>{{ isEditMode ? 'Edit User' : 'Add New User' }}</h2>
      <button class="btn btn-outline-secondary" (click)="cancel()">
        <mat-icon>arrow_back</mat-icon> Back to Users
      </button>
    </div>

    <form [formGroup]="userForm" (ngSubmit)="onSubmit()" *ngIf="!loading">
      <div class="row">
        <!-- Basic Information -->
        <div class="col-md-6">
          <div class="form-group">
            <label for="name" class="form-label">
              {{ getLabel('FULL_NAME') }} *
              <mat-icon
                class="help-icon"
                [matTooltip]="getTooltip('FULL_NAME')"
                matTooltipPosition="right">
                help_outline
              </mat-icon>
            </label>
            <input
              type="text"
              id="name"
              class="form-control"
              formControlName="name"
              [placeholder]="getPlaceholder('ENTER_NAME')"
              [class.is-invalid]="userForm.get('name')?.invalid && userForm.get('name')?.touched">
            <div class="invalid-feedback" *ngIf="userForm.get('name')?.invalid && userForm.get('name')?.touched">
              <small>{{ getFieldErrorMessage('name') }}</small>
            </div>
          </div>
        </div>

        <div class="col-md-6">
          <div class="form-group">
            <label for="email" class="form-label">
              {{ getLabel('EMAIL_ADDRESS') }} *
              <mat-icon
                class="help-icon"
                [matTooltip]="getTooltip('EMAIL_ADDRESS')"
                matTooltipPosition="right">
                help_outline
              </mat-icon>
            </label>
            <input
              type="email"
              id="email"
              class="form-control"
              formControlName="email"
              [placeholder]="getPlaceholder('ENTER_EMAIL')"
              [class.is-invalid]="userForm.get('email')?.invalid && userForm.get('email')?.touched">
            <div class="invalid-feedback" *ngIf="userForm.get('email')?.invalid && userForm.get('email')?.touched">
              <small>{{ getFieldErrorMessage('email') }}</small>
            </div>
          </div>
        </div>

        <div class="col-md-6" *ngIf="!isEditMode">
          <div class="form-group">
            <label for="password" class="form-label">
              {{ getLabel('PASSWORD') }} *
              <mat-icon
                class="help-icon"
                [matTooltip]="getTooltip('PASSWORD')"
                matTooltipPosition="right">
                help_outline
              </mat-icon>
            </label>
            <div class="input-group">
              <input
                [type]="showPassword ? 'text' : 'password'"
                id="password"
                class="form-control"
                formControlName="password"
                placeholder="Enter a secure password"
                [class.is-invalid]="userForm.get('password')?.invalid && userForm.get('password')?.touched">
              <button
                class="btn btn-outline-secondary"
                type="button"
                [matTooltip]="showPassword ? 'Hide password' : 'Show password'"
                (click)="togglePasswordVisibility()">
                <mat-icon>{{ showPassword ? 'visibility_off' : 'visibility' }}</mat-icon>
              </button>
            </div>
            <div class="invalid-feedback" *ngIf="userForm.get('password')?.invalid && userForm.get('password')?.touched">
              <small>{{ getFieldErrorMessage('password') }}</small>
            </div>
          </div>
        </div>

        <div class="col-md-6">
          <div class="form-group">
            <label for="role" class="form-label">Role *</label>
            <select 
              id="role" 
              class="form-control" 
              formControlName="role"
              [class.is-invalid]="userForm.get('role')?.invalid && userForm.get('role')?.touched">
              <option value="">Select Role</option>
              <option *ngFor="let role of roles" [value]="role">{{ role }}</option>
            </select>
            <div class="invalid-feedback" *ngIf="userForm.get('role')?.invalid && userForm.get('role')?.touched">
              <small>Role is required</small>
            </div>
          </div>
        </div>

        <div class="col-md-6">
          <div class="form-group">
            <label for="contact" class="form-label">Contact Number</label>
            <input 
              type="tel" 
              id="contact" 
              class="form-control" 
              formControlName="contact"
              placeholder="e.g., 03001234567"
              [class.is-invalid]="userForm.get('contact')?.invalid && userForm.get('contact')?.touched">
            <div class="invalid-feedback" *ngIf="userForm.get('contact')?.invalid && userForm.get('contact')?.touched">
              <small>Please enter a valid contact number (10-15 digits)</small>
            </div>
          </div>
        </div>

        <div class="col-md-6">
          <div class="form-group">
            <label for="address" class="form-label">Address</label>
            <textarea 
              id="address" 
              class="form-control" 
              formControlName="address" 
              rows="2"
              placeholder="Enter full address"></textarea>
          </div>
        </div>

        <!-- Student-specific fields -->
        <div class="col-md-6" *ngIf="isStudent">
          <div class="form-group">
            <label for="regNo" class="form-label">Registration Number *</label>
            <input 
              type="text" 
              id="regNo" 
              class="form-control" 
              formControlName="regNo"
              [class.is-invalid]="userForm.get('regNo')?.invalid && userForm.get('regNo')?.touched">
            <div class="invalid-feedback" *ngIf="userForm.get('regNo')?.invalid && userForm.get('regNo')?.touched">
              <small>Registration number is required for students</small>
            </div>
          </div>
        </div>

        <div class="col-md-6" *ngIf="isStudent">
          <div class="form-group">
            <label for="rollNo" class="form-label">Roll Number *</label>
            <input 
              type="text" 
              id="rollNo" 
              class="form-control" 
              formControlName="rollNo"
              [class.is-invalid]="userForm.get('rollNo')?.invalid && userForm.get('rollNo')?.touched">
            <div class="invalid-feedback" *ngIf="userForm.get('rollNo')?.invalid && userForm.get('rollNo')?.touched">
              <small>Roll number is required for students</small>
            </div>
          </div>
        </div>

        <div class="col-md-6" *ngIf="isStudent">
          <div class="form-group">
            <label for="father_name" class="form-label">Father's Name *</label>
            <input 
              type="text" 
              id="father_name" 
              class="form-control" 
              formControlName="father_name"
              [class.is-invalid]="userForm.get('father_name')?.invalid && userForm.get('father_name')?.touched">
            <div class="invalid-feedback" *ngIf="userForm.get('father_name')?.invalid && userForm.get('father_name')?.touched">
              <small>Father's name is required for students</small>
            </div>
          </div>
        </div>

        <!-- Teacher/Principal-specific fields -->
        <div class="col-md-6" *ngIf="isTeacher || isPrincipal">
          <div class="form-group">
            <label for="designation" class="form-label">Designation *</label>
            <input 
              type="text" 
              id="designation" 
              class="form-control" 
              formControlName="designation"
              placeholder="e.g., Assistant Professor, Lecturer"
              [class.is-invalid]="userForm.get('designation')?.invalid && userForm.get('designation')?.touched">
            <div class="invalid-feedback" *ngIf="userForm.get('designation')?.invalid && userForm.get('designation')?.touched">
              <small>Designation is required</small>
            </div>
          </div>
        </div>

        <div class="col-md-6" *ngIf="isTeacher || isPrincipal">
          <div class="form-group">
            <label for="position" class="form-label">Position</label>
            <input 
              type="text" 
              id="position" 
              class="form-control" 
              formControlName="position"
              placeholder="e.g., Head of Department">
          </div>
        </div>

        <!-- Common fields for academic roles -->
        <div class="col-md-6" *ngIf="isStudent || isTeacher || isPrincipal">
          <div class="form-group">
            <label for="department" class="form-label">Department *</label>
            <select 
              id="department" 
              class="form-control" 
              formControlName="department"
              [class.is-invalid]="userForm.get('department')?.invalid && userForm.get('department')?.touched">
              <option value="">Select Department</option>
              <option *ngFor="let dept of departments" [value]="dept">{{ dept }}</option>
            </select>
            <div class="invalid-feedback" *ngIf="userForm.get('department')?.invalid && userForm.get('department')?.touched">
              <small>Department is required</small>
            </div>
          </div>
        </div>

        <div class="col-md-6" *ngIf="isStudent">
          <div class="form-group">
            <label for="classId" class="form-label">Class</label>
            <select id="classId" class="form-control" formControlName="classId">
              <option value="">Select Class</option>
              <option *ngFor="let cls of classes" [value]="cls">{{ cls }}</option>
            </select>
          </div>
        </div>

        <!-- Status -->
        <div class="col-md-6">
          <div class="form-group">
            <div class="form-check">
              <input 
                class="form-check-input" 
                type="checkbox" 
                id="isActive" 
                formControlName="isActive">
              <label class="form-check-label" for="isActive">
                Active User
              </label>
            </div>
          </div>
        </div>

        <div class="col-md-6" *ngIf="isTeacher">
          <div class="form-group">
            <div class="form-check">
              <input 
                class="form-check-input" 
                type="checkbox" 
                id="isVisiting" 
                formControlName="isVisiting">
              <label class="form-check-label" for="isVisiting">
                Visiting Faculty
              </label>
            </div>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="form-actions">
        <button 
          type="submit" 
          class="btn btn-primary" 
          [disabled]="loading || userForm.invalid">
          <span *ngIf="loading" class="spinner-border spinner-border-sm me-2" role="status"></span>
          {{ loading ? 'Saving...' : (isEditMode ? 'Update User' : 'Create User') }}
        </button>
        <button type="button" class="btn btn-outline-secondary ms-2" (click)="cancel()">
          Cancel
        </button>
      </div>
    </form>

    <!-- Loading State -->
    <div *ngIf="loading" class="text-center py-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      <p class="mt-2">{{ isEditMode ? 'Loading user data...' : 'Creating user...' }}</p>
    </div>
  </div>
</div>
