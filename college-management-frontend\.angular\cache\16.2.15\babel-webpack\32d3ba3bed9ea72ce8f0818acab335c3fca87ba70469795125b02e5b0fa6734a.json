{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { DepartmentRoutingModule } from './department-routing.module';\nimport { DepartmentFormComponent } from './department-form/department-form.component';\nimport { DepartmentTableComponent } from './department-table/department-table.component';\nimport { MaterialModule } from 'src/app/material';\nimport * as i0 from \"@angular/core\";\nexport class DepartmentModule {\n  static #_ = this.ɵfac = function DepartmentModule_Factory(t) {\n    return new (t || DepartmentModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: DepartmentModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, FormsModule, ReactiveFormsModule, DepartmentRoutingModule, MaterialModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(DepartmentModule, {\n    declarations: [DepartmentFormComponent, DepartmentTableComponent],\n    imports: [CommonModule, FormsModule, ReactiveFormsModule, DepartmentRoutingModule, MaterialModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "DepartmentRoutingModule", "DepartmentFormComponent", "DepartmentTableComponent", "MaterialModule", "DepartmentModule", "_", "_2", "_3", "declarations", "imports"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\department\\department.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\n\r\nimport { DepartmentRoutingModule } from './department-routing.module';\r\nimport { DepartmentFormComponent } from './department-form/department-form.component';\r\nimport { DepartmentTableComponent } from './department-table/department-table.component';\r\nimport { MaterialModule } from 'src/app/material';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    DepartmentFormComponent,\r\n    DepartmentTableComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    DepartmentRoutingModule,\r\n    MaterialModule\r\n  ]\r\n})\r\nexport class DepartmentModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAEjE,SAASC,uBAAuB,QAAQ,6BAA6B;AACrE,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,cAAc,QAAQ,kBAAkB;;AAgBjD,OAAM,MAAOC,gBAAgB;EAAA,QAAAC,CAAA,G;qBAAhBD,gBAAgB;EAAA;EAAA,QAAAE,EAAA,G;UAAhBF;EAAgB;EAAA,QAAAG,EAAA,G;cAPzBV,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,uBAAuB,EACvBG,cAAc;EAAA;;;2EAGLC,gBAAgB;IAAAI,YAAA,GAXzBP,uBAAuB,EACvBC,wBAAwB;IAAAO,OAAA,GAGxBZ,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,uBAAuB,EACvBG,cAAc;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}