{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction ResetPasswordComponent_div_24_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Password is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_24_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Password must be at least 6 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtemplate(1, ResetPasswordComponent_div_24_small_1_Template, 2, 0, \"small\", 32);\n    i0.ɵɵtemplate(2, ResetPasswordComponent_div_24_small_2_Template, 2, 0, \"small\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r0.resetPasswordForm.get(\"password\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r0.resetPasswordForm.get(\"password\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"minlength\"]);\n  }\n}\nfunction ResetPasswordComponent_div_32_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Confirm password is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_32_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Passwords do not match\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtemplate(1, ResetPasswordComponent_div_32_small_1_Template, 2, 0, \"small\", 32);\n    i0.ɵɵtemplate(2, ResetPasswordComponent_div_32_small_2_Template, 2, 0, \"small\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r1.resetPasswordForm.get(\"confirmPassword\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r1.resetPasswordForm.get(\"confirmPassword\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"passwordMismatch\"]);\n  }\n}\nfunction ResetPasswordComponent_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 33);\n  }\n}\nexport class ResetPasswordComponent {\n  constructor(fb, authService, router) {\n    this.fb = fb;\n    this.authService = authService;\n    this.router = router;\n    this.loading = false;\n    this.showPassword = false;\n    this.showConfirmPassword = false;\n    this.user = null;\n  }\n  ngOnInit() {\n    // Get verified user data from localStorage\n    const verifiedUser = localStorage.getItem('verifiedUser');\n    if (verifiedUser) {\n      this.user = JSON.parse(verifiedUser);\n    } else {\n      // If no verified user data, redirect to forgot password\n      Swal.fire({\n        title: 'Access Denied',\n        text: 'Please complete the OTP verification process first.',\n        icon: 'warning',\n        confirmButtonColor: '#29578c'\n      }).then(() => {\n        this.router.navigate(['/auth/forgot-password']);\n      });\n      return;\n    }\n    this.resetPasswordForm = this.fb.group({\n      password: ['', [Validators.required, Validators.minLength(6)]],\n      confirmPassword: ['', [Validators.required]]\n    }, {\n      validators: this.passwordMatchValidator\n    });\n  }\n  passwordMatchValidator(form) {\n    const password = form.get('password');\n    const confirmPassword = form.get('confirmPassword');\n    if (password && confirmPassword && password.value !== confirmPassword.value) {\n      confirmPassword.setErrors({\n        passwordMismatch: true\n      });\n      return {\n        passwordMismatch: true\n      };\n    }\n    return null;\n  }\n  onSubmit() {\n    if (this.resetPasswordForm.valid && this.user) {\n      this.loading = true;\n      const password = this.resetPasswordForm.value.password;\n      this.authService.resetPassword(this.user._id, password).subscribe({\n        next: response => {\n          this.loading = false;\n          if (response.success) {\n            // Clear stored user data\n            localStorage.removeItem('resetUser');\n            localStorage.removeItem('signupUser');\n            localStorage.removeItem('verifiedUser');\n            Swal.fire({\n              title: '<h1 class=\"mb-4\">Password Reset Successfully!</h1>',\n              html: '<p>Your password has been successfully reset. <br> Click below to log in with your new password.</p>',\n              icon: 'success',\n              confirmButtonText: 'Log in',\n              confirmButtonColor: '#29578c'\n            }).then(result => {\n              if (result.isConfirmed) {\n                this.router.navigate(['/auth']);\n              }\n            });\n          }\n        },\n        error: error => {\n          this.loading = false;\n          console.error('Reset password error:', error);\n          const errorMessage = error.error?.message || 'Failed to reset password. Please try again.';\n          Swal.fire({\n            title: 'Reset Failed',\n            text: errorMessage,\n            icon: 'error',\n            confirmButtonColor: '#29578c'\n          });\n        }\n      });\n    } else {\n      // Mark all fields as touched to show validation errors\n      Object.keys(this.resetPasswordForm.controls).forEach(key => {\n        this.resetPasswordForm.get(key)?.markAsTouched();\n      });\n    }\n  }\n  togglePasswordVisibility() {\n    this.showPassword = !this.showPassword;\n  }\n  toggleConfirmPasswordVisibility() {\n    this.showConfirmPassword = !this.showConfirmPassword;\n  }\n  // Legacy method for backward compatibility\n  resetpassword() {\n    this.onSubmit();\n  }\n  static #_ = this.ɵfac = function ResetPasswordComponent_Factory(t) {\n    return new (t || ResetPasswordComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ResetPasswordComponent,\n    selectors: [[\"app-reset-password\"]],\n    decls: 53,\n    vars: 16,\n    consts: [[1, \"container-fluid\"], [1, \"row\", \"vh-100\"], [1, \"col-lg-5\", \"d-flex\", \"flex-column\", \"justify-content-center\", \"align-items-center\", \"p-3\", \"bg-white\"], [1, \"w-100\", 2, \"max-width\", \"400px\"], [1, \"text-center\"], [1, \"mb-4\"], [\"src\", \"../../../assets/images/logo.jpeg\"], [1, \"d-flex\", \"justify-content-center\", \"mt-5\", \"mb-2\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"fa-check\", \"text-success\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"mb-3\"], [\"for\", \"password\", 1, \"form-label\"], [1, \"input-group\"], [\"id\", \"password\", \"formControlName\", \"password\", \"placeholder\", \"Enter your password\", 1, \"form-control\", 3, \"type\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"for\", \"confirmPassword\", 1, \"form-label\"], [\"id\", \"confirmPassword\", \"formControlName\", \"confirmPassword\", \"placeholder\", \"Enter confirm password\", 1, \"form-control\", 3, \"type\"], [1, \"d-grid\", \"gap-2\"], [\"type\", \"submit\", 1, \"btn\", \"submit\", 3, \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", \"role\", \"status\", \"aria-hidden\", \"true\", 4, \"ngIf\"], [1, \"mt-3\", \"text-center\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"fa-arrow-left\"], [\"routerLink\", \"/auth\"], [1, \"col-lg-7\", \"d-none\", \"d-lg-flex\", \"flex-column\", \"align-items-start\", \"bg-light\", \"position-relative\"], [1, \"text-start\", \"p-5\", \"w-100\"], [1, \"blockquote\"], [1, \"blockquote-footer\"], [\"title\", \"Source Title\"], [1, \"image-container\"], [\"src\", \"../../../assets/images/background.jpeg\", \"alt\", \"Stats graph\", 1, \"img-fluid\", \"position-absolute\"], [1, \"invalid-feedback\"], [4, \"ngIf\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"]],\n    template: function ResetPasswordComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"h2\", 5);\n        i0.ɵɵelement(6, \"img\", 6);\n        i0.ɵɵtext(7, \" GPGC (Swabi)\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"div\", 7);\n        i0.ɵɵelement(9, \"i\", 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"h1\", 5);\n        i0.ɵɵtext(11, \"Set new password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"p\");\n        i0.ɵɵtext(13, \"Your new password must be different\");\n        i0.ɵɵelement(14, \"br\");\n        i0.ɵɵtext(15, \" to previously used passwords.\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(16, \"form\", 9);\n        i0.ɵɵlistener(\"ngSubmit\", function ResetPasswordComponent_Template_form_ngSubmit_16_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵelementStart(17, \"div\", 10)(18, \"label\", 11);\n        i0.ɵɵtext(19, \"Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"div\", 12);\n        i0.ɵɵelement(21, \"input\", 13);\n        i0.ɵɵelementStart(22, \"button\", 14);\n        i0.ɵɵlistener(\"click\", function ResetPasswordComponent_Template_button_click_22_listener() {\n          return ctx.togglePasswordVisibility();\n        });\n        i0.ɵɵelement(23, \"i\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(24, ResetPasswordComponent_div_24_Template, 3, 2, \"div\", 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"div\", 10)(26, \"label\", 16);\n        i0.ɵɵtext(27, \"Confirm Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(28, \"div\", 12);\n        i0.ɵɵelement(29, \"input\", 17);\n        i0.ɵɵelementStart(30, \"button\", 14);\n        i0.ɵɵlistener(\"click\", function ResetPasswordComponent_Template_button_click_30_listener() {\n          return ctx.toggleConfirmPasswordVisibility();\n        });\n        i0.ɵɵelement(31, \"i\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(32, ResetPasswordComponent_div_32_Template, 3, 2, \"div\", 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(33, \"div\", 18)(34, \"button\", 19);\n        i0.ɵɵtemplate(35, ResetPasswordComponent_span_35_Template, 1, 0, \"span\", 20);\n        i0.ɵɵtext(36);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(37, \"p\", 21);\n        i0.ɵɵelement(38, \"i\", 22);\n        i0.ɵɵtext(39, \" \\u00A0\");\n        i0.ɵɵelementStart(40, \"a\", 23);\n        i0.ɵɵtext(41, \"Back to log in \");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(42, \"div\", 24)(43, \"div\", 25)(44, \"blockquote\", 26)(45, \"h2\", 5);\n        i0.ɵɵtext(46, \"College management system Login page\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(47, \"footer\", 27);\n        i0.ɵɵtext(48, \"Name\");\n        i0.ɵɵelementStart(49, \"cite\", 28);\n        i0.ɵɵtext(50, \"Owner ~ GPGC SWABI\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(51, \"div\", 29);\n        i0.ɵɵelement(52, \"img\", 30);\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        let tmp_1_0;\n        let tmp_4_0;\n        let tmp_5_0;\n        let tmp_8_0;\n        i0.ɵɵadvance(16);\n        i0.ɵɵproperty(\"formGroup\", ctx.resetPasswordForm);\n        i0.ɵɵadvance(5);\n        i0.ɵɵclassProp(\"is-invalid\", ((tmp_1_0 = ctx.resetPasswordForm.get(\"password\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.resetPasswordForm.get(\"password\")) == null ? null : tmp_1_0.touched));\n        i0.ɵɵproperty(\"type\", ctx.showPassword ? \"text\" : \"password\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵclassMap(ctx.showPassword ? \"fa fa-eye-slash\" : \"fa fa-eye\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.resetPasswordForm.get(\"password\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.resetPasswordForm.get(\"password\")) == null ? null : tmp_4_0.touched));\n        i0.ɵɵadvance(5);\n        i0.ɵɵclassProp(\"is-invalid\", ((tmp_5_0 = ctx.resetPasswordForm.get(\"confirmPassword\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.resetPasswordForm.get(\"confirmPassword\")) == null ? null : tmp_5_0.touched));\n        i0.ɵɵproperty(\"type\", ctx.showConfirmPassword ? \"text\" : \"password\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵclassMap(ctx.showConfirmPassword ? \"fa fa-eye-slash\" : \"fa fa-eye\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx.resetPasswordForm.get(\"confirmPassword\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx.resetPasswordForm.get(\"confirmPassword\")) == null ? null : tmp_8_0.touched));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"disabled\", ctx.loading || ctx.resetPasswordForm.invalid);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate1(\" \", ctx.loading ? \"Resetting...\" : \"Reset password\", \" \");\n      }\n    },\n    dependencies: [i4.NgIf, i3.RouterLink, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n    styles: [\"body[_ngcontent-%COMP%], html[_ngcontent-%COMP%] {\\n    margin: 0;\\n    padding: 0;\\n    overflow-x: hidden; \\n\\n    height: 100%;\\n}\\n\\n\\n\\n.image-container[_ngcontent-%COMP%] {\\n    width: 100%;\\n    height: 100%;\\n    position: relative;\\n}\\n\\n.image-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    max-width: 80%;\\n    height: auto;\\n    position: absolute;\\n    right: 0;\\n    bottom: 0;\\n    object-fit: contain; \\n\\n}\\n.submit[_ngcontent-%COMP%]{\\n    background-color: #29578c;\\n    color: white;\\n}\\n.forgot[_ngcontent-%COMP%]{\\n    color: #29578c;\\n    text-decoration: none;\\n    font-weight: 700;\\n}\\na[_ngcontent-%COMP%]{\\n    cursor: pointer;\\n    text-decoration: none;\\n    color: black;\\n    font-weight: 600;\\n}\\n.key[_ngcontent-%COMP%]{\\n    background-color: #F4EBFF !important;\\n    border-radius: 50%;\\n    height: 50px;\\n    width: 50px;\\n    display: flex;\\n    justify-content: center;\\n    align-items: center;\\n    color: #29578c;\\n}\\n.resend[_ngcontent-%COMP%]{\\n    background-color: none;\\n    color:#29578c ;\\n    font-weight: 700;\\n    font-family: cursive ;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "ResetPasswordComponent_div_24_small_1_Template", "ResetPasswordComponent_div_24_small_2_Template", "ɵɵadvance", "ɵɵproperty", "tmp_0_0", "ctx_r0", "resetPasswordForm", "get", "errors", "tmp_1_0", "ResetPasswordComponent_div_32_small_1_Template", "ResetPasswordComponent_div_32_small_2_Template", "ctx_r1", "ɵɵelement", "ResetPasswordComponent", "constructor", "fb", "authService", "router", "loading", "showPassword", "showConfirmPassword", "user", "ngOnInit", "verifiedUser", "localStorage", "getItem", "JSON", "parse", "fire", "title", "text", "icon", "confirmButtonColor", "then", "navigate", "group", "password", "required", "<PERSON><PERSON><PERSON><PERSON>", "confirmPassword", "validators", "passwordMatchValidator", "form", "value", "setErrors", "passwordMismatch", "onSubmit", "valid", "resetPassword", "_id", "subscribe", "next", "response", "success", "removeItem", "html", "confirmButtonText", "result", "isConfirmed", "error", "console", "errorMessage", "message", "Object", "keys", "controls", "for<PERSON>ach", "key", "<PERSON><PERSON><PERSON><PERSON>ched", "togglePasswordVisibility", "toggleConfirmPasswordVisibility", "resetpassword", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "_2", "selectors", "decls", "vars", "consts", "template", "ResetPasswordComponent_Template", "rf", "ctx", "ɵɵlistener", "ResetPasswordComponent_Template_form_ngSubmit_16_listener", "ResetPasswordComponent_Template_button_click_22_listener", "ResetPasswordComponent_div_24_Template", "ResetPasswordComponent_Template_button_click_30_listener", "ResetPasswordComponent_div_32_Template", "ResetPasswordComponent_span_35_Template", "ɵɵclassProp", "invalid", "touched", "ɵɵclassMap", "tmp_4_0", "tmp_5_0", "tmp_8_0", "ɵɵtextInterpolate1"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\auth\\reset-password\\reset-password.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\auth\\reset-password\\reset-password.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { AuthService } from 'src/app/services/auth.service';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-reset-password',\r\n  templateUrl: './reset-password.component.html',\r\n  styleUrls: ['./reset-password.component.css']\r\n})\r\nexport class ResetPasswordComponent implements OnInit {\r\n  resetPasswordForm!: FormGroup;\r\n  loading = false;\r\n  showPassword = false;\r\n  showConfirmPassword = false;\r\n  user: any = null;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private authService: AuthService,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    // Get verified user data from localStorage\r\n    const verifiedUser = localStorage.getItem('verifiedUser');\r\n\r\n    if (verifiedUser) {\r\n      this.user = JSON.parse(verifiedUser);\r\n    } else {\r\n      // If no verified user data, redirect to forgot password\r\n      Swal.fire({\r\n        title: 'Access Denied',\r\n        text: 'Please complete the OTP verification process first.',\r\n        icon: 'warning',\r\n        confirmButtonColor: '#29578c'\r\n      }).then(() => {\r\n        this.router.navigate(['/auth/forgot-password']);\r\n      });\r\n      return;\r\n    }\r\n\r\n    this.resetPasswordForm = this.fb.group({\r\n      password: ['', [Validators.required, Validators.minLength(6)]],\r\n      confirmPassword: ['', [Validators.required]]\r\n    }, { validators: this.passwordMatchValidator });\r\n  }\r\n\r\n  passwordMatchValidator(form: FormGroup) {\r\n    const password = form.get('password');\r\n    const confirmPassword = form.get('confirmPassword');\r\n\r\n    if (password && confirmPassword && password.value !== confirmPassword.value) {\r\n      confirmPassword.setErrors({ passwordMismatch: true });\r\n      return { passwordMismatch: true };\r\n    }\r\n\r\n    return null;\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (this.resetPasswordForm.valid && this.user) {\r\n      this.loading = true;\r\n      const password = this.resetPasswordForm.value.password;\r\n\r\n      this.authService.resetPassword(this.user._id, password).subscribe({\r\n        next: (response) => {\r\n          this.loading = false;\r\n          if (response.success) {\r\n            // Clear stored user data\r\n            localStorage.removeItem('resetUser');\r\n            localStorage.removeItem('signupUser');\r\n            localStorage.removeItem('verifiedUser');\r\n\r\n            Swal.fire({\r\n              title: '<h1 class=\"mb-4\">Password Reset Successfully!</h1>',\r\n              html: '<p>Your password has been successfully reset. <br> Click below to log in with your new password.</p>',\r\n              icon: 'success',\r\n              confirmButtonText: 'Log in',\r\n              confirmButtonColor: '#29578c'\r\n            }).then((result) => {\r\n              if (result.isConfirmed) {\r\n                this.router.navigate(['/auth']);\r\n              }\r\n            });\r\n          }\r\n        },\r\n        error: (error) => {\r\n          this.loading = false;\r\n          console.error('Reset password error:', error);\r\n\r\n          const errorMessage = error.error?.message || 'Failed to reset password. Please try again.';\r\n\r\n          Swal.fire({\r\n            title: 'Reset Failed',\r\n            text: errorMessage,\r\n            icon: 'error',\r\n            confirmButtonColor: '#29578c'\r\n          });\r\n        }\r\n      });\r\n    } else {\r\n      // Mark all fields as touched to show validation errors\r\n      Object.keys(this.resetPasswordForm.controls).forEach(key => {\r\n        this.resetPasswordForm.get(key)?.markAsTouched();\r\n      });\r\n    }\r\n  }\r\n\r\n  togglePasswordVisibility(): void {\r\n    this.showPassword = !this.showPassword;\r\n  }\r\n\r\n  toggleConfirmPasswordVisibility(): void {\r\n    this.showConfirmPassword = !this.showConfirmPassword;\r\n  }\r\n\r\n  // Legacy method for backward compatibility\r\n  resetpassword(): void {\r\n    this.onSubmit();\r\n  }\r\n}\r\n", "<div class=\"container-fluid\">\r\n    <div class=\"row vh-100\">\r\n        <div class=\"col-lg-5 d-flex flex-column justify-content-center align-items-center p-3 bg-white\">\r\n            <div class=\"w-100\" style=\"max-width: 400px;\">\r\n                <div class=\"text-center\">\r\n                <h2 class=\"mb-4\"> <img src=\"../../../assets/images/logo.jpeg\"/> GPGC (Swabi)</h2>\r\n                <div class=\"d-flex justify-content-center mt-5 mb-2\">\r\n                    <i class=\"fa fa-check text-success\" aria-hidden=\"true\"></i>\r\n\r\n                </div>\r\n                \r\n                <h1 class=\"mb-4\">Set new password</h1>\r\n                <p>Your new password must be different<br> to previously used passwords.</p>\r\n            </div>\r\n                <form [formGroup]=\"resetPasswordForm\" (ngSubmit)=\"onSubmit()\">\r\n                    <div class=\"mb-3\">\r\n                        <label for=\"password\" class=\"form-label\">Password</label>\r\n                        <div class=\"input-group\">\r\n                            <input\r\n                                [type]=\"showPassword ? 'text' : 'password'\"\r\n                                class=\"form-control\"\r\n                                id=\"password\"\r\n                                formControlName=\"password\"\r\n                                placeholder=\"Enter your password\"\r\n                                [class.is-invalid]=\"resetPasswordForm.get('password')?.invalid && resetPasswordForm.get('password')?.touched\">\r\n                            <button\r\n                                class=\"btn btn-outline-secondary\"\r\n                                type=\"button\"\r\n                                (click)=\"togglePasswordVisibility()\">\r\n                                <i [class]=\"showPassword ? 'fa fa-eye-slash' : 'fa fa-eye'\"></i>\r\n                            </button>\r\n                        </div>\r\n                        <div class=\"invalid-feedback\" *ngIf=\"resetPasswordForm.get('password')?.invalid && resetPasswordForm.get('password')?.touched\">\r\n                            <small *ngIf=\"resetPasswordForm.get('password')?.errors?.['required']\">Password is required</small>\r\n                            <small *ngIf=\"resetPasswordForm.get('password')?.errors?.['minlength']\">Password must be at least 6 characters</small>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div class=\"mb-3\">\r\n                        <label for=\"confirmPassword\" class=\"form-label\">Confirm Password</label>\r\n                        <div class=\"input-group\">\r\n                            <input\r\n                                [type]=\"showConfirmPassword ? 'text' : 'password'\"\r\n                                class=\"form-control\"\r\n                                id=\"confirmPassword\"\r\n                                formControlName=\"confirmPassword\"\r\n                                placeholder=\"Enter confirm password\"\r\n                                [class.is-invalid]=\"resetPasswordForm.get('confirmPassword')?.invalid && resetPasswordForm.get('confirmPassword')?.touched\">\r\n                            <button\r\n                                class=\"btn btn-outline-secondary\"\r\n                                type=\"button\"\r\n                                (click)=\"toggleConfirmPasswordVisibility()\">\r\n                                <i [class]=\"showConfirmPassword ? 'fa fa-eye-slash' : 'fa fa-eye'\"></i>\r\n                            </button>\r\n                        </div>\r\n                        <div class=\"invalid-feedback\" *ngIf=\"resetPasswordForm.get('confirmPassword')?.invalid && resetPasswordForm.get('confirmPassword')?.touched\">\r\n                            <small *ngIf=\"resetPasswordForm.get('confirmPassword')?.errors?.['required']\">Confirm password is required</small>\r\n                            <small *ngIf=\"resetPasswordForm.get('confirmPassword')?.errors?.['passwordMismatch']\">Passwords do not match</small>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div class=\"d-grid gap-2\">\r\n                        <button\r\n                            type=\"submit\"\r\n                            class=\"btn submit\"\r\n                            [disabled]=\"loading || resetPasswordForm.invalid\">\r\n                            <span *ngIf=\"loading\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\r\n                            {{ loading ? 'Resetting...' : 'Reset password' }}\r\n                        </button>\r\n                    </div>\r\n                    <p class=\"mt-3 text-center\"> <i class=\"fa fa-arrow-left\" aria-hidden=\"true\"></i> &nbsp;<a routerLink=\"/auth\">Back to log in </a></p>\r\n                </form>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-lg-7 d-none d-lg-flex flex-column align-items-start bg-light position-relative\">\r\n            <div class=\"text-start p-5 w-100\">\r\n                <blockquote class=\"blockquote\">\r\n                    <h2 class=\"mb-4\">College management system Login page</h2>\r\n                    <footer class=\"blockquote-footer\">Name<cite title=\"Source Title\">Owner ~ GPGC SWABI</cite></footer>\r\n                </blockquote>\r\n            </div>\r\n            <div class=\"image-container\">\r\n                <img src=\"../../../assets/images/background.jpeg\" class=\"img-fluid position-absolute\" alt=\"Stats graph\">\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAGnE,OAAOC,IAAI,MAAM,aAAa;;;;;;;;IC6BFC,EAAA,CAAAC,cAAA,YAAuE;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IACnGH,EAAA,CAAAC,cAAA,YAAwE;IAAAD,EAAA,CAAAE,MAAA,6CAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAF1HH,EAAA,CAAAC,cAAA,cAA+H;IAC3HD,EAAA,CAAAI,UAAA,IAAAC,8CAAA,oBAAmG;IACnGL,EAAA,CAAAI,UAAA,IAAAE,8CAAA,oBAAsH;IAC1HN,EAAA,CAAAG,YAAA,EAAM;;;;;;IAFMH,EAAA,CAAAO,SAAA,GAA6D;IAA7DP,EAAA,CAAAQ,UAAA,UAAAC,OAAA,GAAAC,MAAA,CAAAC,iBAAA,CAAAC,GAAA,+BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAA6D;IAC7Db,EAAA,CAAAO,SAAA,GAA8D;IAA9DP,EAAA,CAAAQ,UAAA,UAAAM,OAAA,GAAAJ,MAAA,CAAAC,iBAAA,CAAAC,GAAA,+BAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,cAA8D;;;;;IAsBtEb,EAAA,CAAAC,cAAA,YAA8E;IAAAD,EAAA,CAAAE,MAAA,mCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAClHH,EAAA,CAAAC,cAAA,YAAsF;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAFxHH,EAAA,CAAAC,cAAA,cAA6I;IACzID,EAAA,CAAAI,UAAA,IAAAW,8CAAA,oBAAkH;IAClHf,EAAA,CAAAI,UAAA,IAAAY,8CAAA,oBAAoH;IACxHhB,EAAA,CAAAG,YAAA,EAAM;;;;;;IAFMH,EAAA,CAAAO,SAAA,GAAoE;IAApEP,EAAA,CAAAQ,UAAA,UAAAC,OAAA,GAAAQ,MAAA,CAAAN,iBAAA,CAAAC,GAAA,sCAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAoE;IACpEb,EAAA,CAAAO,SAAA,GAA4E;IAA5EP,EAAA,CAAAQ,UAAA,UAAAM,OAAA,GAAAG,MAAA,CAAAN,iBAAA,CAAAC,GAAA,sCAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,qBAA4E;;;;;IASpFb,EAAA,CAAAkB,SAAA,eAA4G;;;ADvDxI,OAAM,MAAOC,sBAAsB;EAOjCC,YACUC,EAAe,EACfC,WAAwB,EACxBC,MAAc;IAFd,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IARhB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,IAAI,GAAQ,IAAI;EAMb;EAEHC,QAAQA,CAAA;IACN;IACA,MAAMC,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IAEzD,IAAIF,YAAY,EAAE;MAChB,IAAI,CAACF,IAAI,GAAGK,IAAI,CAACC,KAAK,CAACJ,YAAY,CAAC;KACrC,MAAM;MACL;MACA9B,IAAI,CAACmC,IAAI,CAAC;QACRC,KAAK,EAAE,eAAe;QACtBC,IAAI,EAAE,qDAAqD;QAC3DC,IAAI,EAAE,SAAS;QACfC,kBAAkB,EAAE;OACrB,CAAC,CAACC,IAAI,CAAC,MAAK;QACX,IAAI,CAAChB,MAAM,CAACiB,QAAQ,CAAC,CAAC,uBAAuB,CAAC,CAAC;MACjD,CAAC,CAAC;MACF;;IAGF,IAAI,CAAC7B,iBAAiB,GAAG,IAAI,CAACU,EAAE,CAACoB,KAAK,CAAC;MACrCC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC5C,UAAU,CAAC6C,QAAQ,EAAE7C,UAAU,CAAC8C,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DC,eAAe,EAAE,CAAC,EAAE,EAAE,CAAC/C,UAAU,CAAC6C,QAAQ,CAAC;KAC5C,EAAE;MAAEG,UAAU,EAAE,IAAI,CAACC;IAAsB,CAAE,CAAC;EACjD;EAEAA,sBAAsBA,CAACC,IAAe;IACpC,MAAMN,QAAQ,GAAGM,IAAI,CAACpC,GAAG,CAAC,UAAU,CAAC;IACrC,MAAMiC,eAAe,GAAGG,IAAI,CAACpC,GAAG,CAAC,iBAAiB,CAAC;IAEnD,IAAI8B,QAAQ,IAAIG,eAAe,IAAIH,QAAQ,CAACO,KAAK,KAAKJ,eAAe,CAACI,KAAK,EAAE;MAC3EJ,eAAe,CAACK,SAAS,CAAC;QAAEC,gBAAgB,EAAE;MAAI,CAAE,CAAC;MACrD,OAAO;QAAEA,gBAAgB,EAAE;MAAI,CAAE;;IAGnC,OAAO,IAAI;EACb;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACzC,iBAAiB,CAAC0C,KAAK,IAAI,IAAI,CAAC1B,IAAI,EAAE;MAC7C,IAAI,CAACH,OAAO,GAAG,IAAI;MACnB,MAAMkB,QAAQ,GAAG,IAAI,CAAC/B,iBAAiB,CAACsC,KAAK,CAACP,QAAQ;MAEtD,IAAI,CAACpB,WAAW,CAACgC,aAAa,CAAC,IAAI,CAAC3B,IAAI,CAAC4B,GAAG,EAAEb,QAAQ,CAAC,CAACc,SAAS,CAAC;QAChEC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAClC,OAAO,GAAG,KAAK;UACpB,IAAIkC,QAAQ,CAACC,OAAO,EAAE;YACpB;YACA7B,YAAY,CAAC8B,UAAU,CAAC,WAAW,CAAC;YACpC9B,YAAY,CAAC8B,UAAU,CAAC,YAAY,CAAC;YACrC9B,YAAY,CAAC8B,UAAU,CAAC,cAAc,CAAC;YAEvC7D,IAAI,CAACmC,IAAI,CAAC;cACRC,KAAK,EAAE,oDAAoD;cAC3D0B,IAAI,EAAE,sGAAsG;cAC5GxB,IAAI,EAAE,SAAS;cACfyB,iBAAiB,EAAE,QAAQ;cAC3BxB,kBAAkB,EAAE;aACrB,CAAC,CAACC,IAAI,CAAEwB,MAAM,IAAI;cACjB,IAAIA,MAAM,CAACC,WAAW,EAAE;gBACtB,IAAI,CAACzC,MAAM,CAACiB,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;;YAEnC,CAAC,CAAC;;QAEN,CAAC;QACDyB,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACzC,OAAO,GAAG,KAAK;UACpB0C,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;UAE7C,MAAME,YAAY,GAAGF,KAAK,CAACA,KAAK,EAAEG,OAAO,IAAI,6CAA6C;UAE1FrE,IAAI,CAACmC,IAAI,CAAC;YACRC,KAAK,EAAE,cAAc;YACrBC,IAAI,EAAE+B,YAAY;YAClB9B,IAAI,EAAE,OAAO;YACbC,kBAAkB,EAAE;WACrB,CAAC;QACJ;OACD,CAAC;KACH,MAAM;MACL;MACA+B,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC3D,iBAAiB,CAAC4D,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;QACzD,IAAI,CAAC9D,iBAAiB,CAACC,GAAG,CAAC6D,GAAG,CAAC,EAAEC,aAAa,EAAE;MAClD,CAAC,CAAC;;EAEN;EAEAC,wBAAwBA,CAAA;IACtB,IAAI,CAAClD,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEAmD,+BAA+BA,CAAA;IAC7B,IAAI,CAAClD,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;EACtD;EAEA;EACAmD,aAAaA,CAAA;IACX,IAAI,CAACzB,QAAQ,EAAE;EACjB;EAAC,QAAA0B,CAAA,G;qBA9GU3D,sBAAsB,EAAAnB,EAAA,CAAA+E,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjF,EAAA,CAAA+E,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAnF,EAAA,CAAA+E,iBAAA,CAAAK,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAtBnE,sBAAsB;IAAAoE,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCXnC7F,EAAA,CAAAC,cAAA,aAA6B;QAKKD,EAAA,CAAAkB,SAAA,aAA6C;QAAClB,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACjFH,EAAA,CAAAC,cAAA,aAAqD;QACjDD,EAAA,CAAAkB,SAAA,WAA2D;QAE/DlB,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,aAAiB;QAAAD,EAAA,CAAAE,MAAA,wBAAgB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACtCH,EAAA,CAAAC,cAAA,SAAG;QAAAD,EAAA,CAAAE,MAAA,2CAAmC;QAAAF,EAAA,CAAAkB,SAAA,UAAI;QAAClB,EAAA,CAAAE,MAAA,sCAA6B;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAE5EH,EAAA,CAAAC,cAAA,eAA8D;QAAxBD,EAAA,CAAA+F,UAAA,sBAAAC,0DAAA;UAAA,OAAYF,GAAA,CAAA1C,QAAA,EAAU;QAAA,EAAC;QACzDpD,EAAA,CAAAC,cAAA,eAAkB;QAC2BD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACzDH,EAAA,CAAAC,cAAA,eAAyB;QACrBD,EAAA,CAAAkB,SAAA,iBAMkH;QAClHlB,EAAA,CAAAC,cAAA,kBAGyC;QAArCD,EAAA,CAAA+F,UAAA,mBAAAE,yDAAA;UAAA,OAASH,GAAA,CAAAnB,wBAAA,EAA0B;QAAA,EAAC;QACpC3E,EAAA,CAAAkB,SAAA,SAAgE;QACpElB,EAAA,CAAAG,YAAA,EAAS;QAEbH,EAAA,CAAAI,UAAA,KAAA8F,sCAAA,kBAGM;QACVlG,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,eAAkB;QACkCD,EAAA,CAAAE,MAAA,wBAAgB;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACxEH,EAAA,CAAAC,cAAA,eAAyB;QACrBD,EAAA,CAAAkB,SAAA,iBAMgI;QAChIlB,EAAA,CAAAC,cAAA,kBAGgD;QAA5CD,EAAA,CAAA+F,UAAA,mBAAAI,yDAAA;UAAA,OAASL,GAAA,CAAAlB,+BAAA,EAAiC;QAAA,EAAC;QAC3C5E,EAAA,CAAAkB,SAAA,SAAuE;QAC3ElB,EAAA,CAAAG,YAAA,EAAS;QAEbH,EAAA,CAAAI,UAAA,KAAAgG,sCAAA,kBAGM;QACVpG,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,eAA0B;QAKlBD,EAAA,CAAAI,UAAA,KAAAiG,uCAAA,mBAA4G;QAC5GrG,EAAA,CAAAE,MAAA,IACJ;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAEbH,EAAA,CAAAC,cAAA,aAA4B;QAACD,EAAA,CAAAkB,SAAA,aAAmD;QAAClB,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAC,cAAA,aAAsB;QAAAD,EAAA,CAAAE,MAAA,uBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAI5IH,EAAA,CAAAC,cAAA,eAAgG;QAGnED,EAAA,CAAAE,MAAA,4CAAoC;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC1DH,EAAA,CAAAC,cAAA,kBAAkC;QAAAD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAC,cAAA,gBAA2B;QAAAD,EAAA,CAAAE,MAAA,0BAAkB;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAGlGH,EAAA,CAAAC,cAAA,eAA6B;QACzBD,EAAA,CAAAkB,SAAA,eAAwG;QAC5GlB,EAAA,CAAAG,YAAA,EAAM;;;;;;;QArEIH,EAAA,CAAAO,SAAA,IAA+B;QAA/BP,EAAA,CAAAQ,UAAA,cAAAsF,GAAA,CAAAnF,iBAAA,CAA+B;QAUrBX,EAAA,CAAAO,SAAA,GAA6G;QAA7GP,EAAA,CAAAsG,WAAA,iBAAAxF,OAAA,GAAAgF,GAAA,CAAAnF,iBAAA,CAAAC,GAAA,+BAAAE,OAAA,CAAAyF,OAAA,OAAAzF,OAAA,GAAAgF,GAAA,CAAAnF,iBAAA,CAAAC,GAAA,+BAAAE,OAAA,CAAA0F,OAAA,EAA6G;QAL7GxG,EAAA,CAAAQ,UAAA,SAAAsF,GAAA,CAAArE,YAAA,uBAA2C;QAUxCzB,EAAA,CAAAO,SAAA,GAAwD;QAAxDP,EAAA,CAAAyG,UAAA,CAAAX,GAAA,CAAArE,YAAA,mCAAwD;QAGpCzB,EAAA,CAAAO,SAAA,GAA8F;QAA9FP,EAAA,CAAAQ,UAAA,WAAAkG,OAAA,GAAAZ,GAAA,CAAAnF,iBAAA,CAAAC,GAAA,+BAAA8F,OAAA,CAAAH,OAAA,OAAAG,OAAA,GAAAZ,GAAA,CAAAnF,iBAAA,CAAAC,GAAA,+BAAA8F,OAAA,CAAAF,OAAA,EAA8F;QAerHxG,EAAA,CAAAO,SAAA,GAA2H;QAA3HP,EAAA,CAAAsG,WAAA,iBAAAK,OAAA,GAAAb,GAAA,CAAAnF,iBAAA,CAAAC,GAAA,sCAAA+F,OAAA,CAAAJ,OAAA,OAAAI,OAAA,GAAAb,GAAA,CAAAnF,iBAAA,CAAAC,GAAA,sCAAA+F,OAAA,CAAAH,OAAA,EAA2H;QAL3HxG,EAAA,CAAAQ,UAAA,SAAAsF,GAAA,CAAApE,mBAAA,uBAAkD;QAU/C1B,EAAA,CAAAO,SAAA,GAA+D;QAA/DP,EAAA,CAAAyG,UAAA,CAAAX,GAAA,CAAApE,mBAAA,mCAA+D;QAG3C1B,EAAA,CAAAO,SAAA,GAA4G;QAA5GP,EAAA,CAAAQ,UAAA,WAAAoG,OAAA,GAAAd,GAAA,CAAAnF,iBAAA,CAAAC,GAAA,sCAAAgG,OAAA,CAAAL,OAAA,OAAAK,OAAA,GAAAd,GAAA,CAAAnF,iBAAA,CAAAC,GAAA,sCAAAgG,OAAA,CAAAJ,OAAA,EAA4G;QAUvIxG,EAAA,CAAAO,SAAA,GAAiD;QAAjDP,EAAA,CAAAQ,UAAA,aAAAsF,GAAA,CAAAtE,OAAA,IAAAsE,GAAA,CAAAnF,iBAAA,CAAA4F,OAAA,CAAiD;QAC1CvG,EAAA,CAAAO,SAAA,GAAa;QAAbP,EAAA,CAAAQ,UAAA,SAAAsF,GAAA,CAAAtE,OAAA,CAAa;QACpBxB,EAAA,CAAAO,SAAA,GACJ;QADIP,EAAA,CAAA6G,kBAAA,MAAAf,GAAA,CAAAtE,OAAA,0CACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}