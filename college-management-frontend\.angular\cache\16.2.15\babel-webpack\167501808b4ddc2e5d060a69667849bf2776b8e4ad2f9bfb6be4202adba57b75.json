{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { AuthRoutingModule } from './auth-routing.module';\nimport { LoginComponent } from './login/login.component';\nimport { SignupComponent } from './signup/signup.component';\nimport { EnterCodeManuallyComponent } from './enter-code-manually/enter-code-manually.component';\nimport { ForgotPasswordComponent } from './forgot-password/forgot-password.component';\nimport { VerifyOtpComponent } from './verify-otp/verify-otp.component';\nimport { ResetPasswordComponent } from './reset-password/reset-password.component';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nexport class AuthModule {\n  static #_ = this.ɵfac = function AuthModule_Factory(t) {\n    return new (t || AuthModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AuthModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, AuthRoutingModule, ReactiveFormsModule, FormsModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AuthModule, {\n    declarations: [LoginComponent, SignupComponent, EnterCodeManuallyComponent, ForgotPasswordComponent, VerifyOtpComponent, ResetPasswordComponent],\n    imports: [CommonModule, AuthRoutingModule, ReactiveFormsModule, FormsModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "AuthRoutingModule", "LoginComponent", "SignupComponent", "EnterCodeManuallyComponent", "ForgotPasswordComponent", "VerifyOtpComponent", "ResetPasswordComponent", "FormsModule", "ReactiveFormsModule", "AuthModule", "_", "_2", "_3", "declarations", "imports"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\auth\\auth.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { AuthRoutingModule } from './auth-routing.module';\r\nimport { LoginComponent } from './login/login.component';\r\nimport { SignupComponent } from './signup/signup.component';\r\nimport { EnterCodeManuallyComponent } from './enter-code-manually/enter-code-manually.component';\r\nimport { ForgotPasswordComponent } from './forgot-password/forgot-password.component';\r\nimport { VerifyOtpComponent } from './verify-otp/verify-otp.component';\r\nimport { ResetPasswordComponent } from './reset-password/reset-password.component';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    LoginComponent,\r\n    SignupComponent,\r\n    EnterCodeManuallyComponent,\r\n    ForgotPasswordComponent,\r\n    VerifyOtpComponent,\r\n    ResetPasswordComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    AuthRoutingModule\r\n    ,ReactiveFormsModule,\r\n    FormsModule,\r\n  ]\r\n})\r\nexport class AuthModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,0BAA0B,QAAQ,qDAAqD;AAChG,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;;AAmBjE,OAAM,MAAOC,UAAU;EAAA,QAAAC,CAAA,G;qBAAVD,UAAU;EAAA;EAAA,QAAAE,EAAA,G;UAAVF;EAAU;EAAA,QAAAG,EAAA,G;cANnBb,YAAY,EACZC,iBAAiB,EAChBQ,mBAAmB,EACpBD,WAAW;EAAA;;;2EAGFE,UAAU;IAAAI,YAAA,GAdnBZ,cAAc,EACdC,eAAe,EACfC,0BAA0B,EAC1BC,uBAAuB,EACvBC,kBAAkB,EAClBC,sBAAsB;IAAAQ,OAAA,GAGtBf,YAAY,EACZC,iBAAiB,EAChBQ,mBAAmB,EACpBD,WAAW;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}