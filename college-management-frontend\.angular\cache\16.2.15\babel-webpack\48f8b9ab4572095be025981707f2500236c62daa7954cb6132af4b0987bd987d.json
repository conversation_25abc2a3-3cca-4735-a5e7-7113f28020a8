{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nexport class EnterCodeManuallyComponent {\n  constructor(fb, authService, router) {\n    this.fb = fb;\n    this.authService = authService;\n    this.router = router;\n    this.loading = false;\n    this.resendLoading = false;\n    this.user = null;\n    this.otpId = null;\n  }\n  ngOnInit() {\n    // Get user data from localStorage (set during forgot password or signup)\n    const resetUser = localStorage.getItem('resetUser');\n    const signupUser = localStorage.getItem('signupUser');\n    if (resetUser) {\n      this.user = JSON.parse(resetUser);\n    } else if (signupUser) {\n      this.user = JSON.parse(signupUser);\n    }\n    if (!this.user) {\n      // If no user data, redirect to login\n      this.router.navigate(['/auth']);\n      return;\n    }\n    this.otpForm = this.fb.group({\n      otp: ['', [Validators.required, Validators.pattern(/^\\d{6}$/)]]\n    });\n  }\n  verifyOtp() {\n    if (this.otpForm.valid && this.user) {\n      this.loading = true;\n      const otp = this.otpForm.value.otp;\n      this.authService.verifyOtp(this.user._id, otp).subscribe({\n        next: response => {\n          this.loading = false;\n          if (response.success) {\n            Swal.fire({\n              title: '<h1 class=\"mb-4\">OTP Verified!</h1>',\n              html: '<p>Your OTP has been successfully verified. <br> You can now reset your password.</p>',\n              icon: 'success',\n              confirmButtonText: 'Reset Password',\n              confirmButtonColor: '#29578c'\n            }).then(result => {\n              if (result.isConfirmed) {\n                // Store user data for password reset\n                localStorage.setItem('verifiedUser', JSON.stringify(this.user));\n                this.router.navigate(['/auth/reset-password']);\n              }\n            });\n          }\n        },\n        error: error => {\n          this.loading = false;\n          console.error('OTP verification error:', error);\n          const errorMessage = error.error?.error || error.error?.message || 'Invalid OTP. Please try again.';\n          Swal.fire({\n            title: 'Verification Failed',\n            text: errorMessage,\n            icon: 'error',\n            confirmButtonColor: '#29578c'\n          });\n        }\n      });\n    } else {\n      Swal.fire({\n        title: 'Invalid OTP',\n        text: 'Please enter a valid 6-digit OTP.',\n        icon: 'warning',\n        confirmButtonColor: '#29578c'\n      });\n    }\n  }\n  resendOtp() {\n    if (!this.user) return;\n    this.resendLoading = true;\n    const resendData = {\n      userId: this.user._id,\n      ...(this.otpId && {\n        otpId: this.otpId\n      })\n    };\n    this.authService.resendOtp(resendData).subscribe({\n      next: response => {\n        this.resendLoading = false;\n        if (response.success) {\n          if (response.otpId) {\n            this.otpId = response.otpId;\n          }\n          Swal.fire({\n            title: 'OTP Resent!',\n            text: 'A new OTP has been sent to your email.',\n            icon: 'success',\n            confirmButtonColor: '#29578c'\n          });\n        }\n      },\n      error: error => {\n        this.resendLoading = false;\n        console.error('Resend OTP error:', error);\n        const errorMessage = error.error?.message || 'Failed to resend OTP. Please try again.';\n        Swal.fire({\n          title: 'Resend Failed',\n          text: errorMessage,\n          icon: 'error',\n          confirmButtonColor: '#29578c'\n        });\n      }\n    });\n  }\n  // Legacy method for backward compatibility\n  verifyemail() {\n    this.verifyOtp();\n  }\n  static #_ = this.ɵfac = function EnterCodeManuallyComponent_Factory(t) {\n    return new (t || EnterCodeManuallyComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: EnterCodeManuallyComponent,\n    selectors: [[\"app-enter-code-manually\"]],\n    decls: 47,\n    vars: 0,\n    consts: [[1, \"container-fluid\"], [1, \"row\", \"vh-100\"], [1, \"col-lg-5\", \"d-flex\", \"flex-column\", \"justify-content-center\", \"align-items-center\", \"p-3\", \"bg-white\"], [1, \"w-100\", 2, \"max-width\", \"400px\"], [1, \"text-center\"], [1, \"mb-4\"], [\"src\", \"../../../assets/images/logo.jpeg\"], [1, \"d-flex\", \"justify-content-center\", \"mt-5\", \"mb-2\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"fa-envelope\", \"key\"], [1, \"row\", \"justify-content-center\", \"mb-4\", \"mt-4\", \"d-flex\", \"align-items-center\"], [1, \"col-3\"], [1, \"number-box\"], [1, \"d-grid\", \"gap-2\"], [\"type\", \"button\", 1, \"btn\", \"submit\", 3, \"click\"], [1, \"mb-3\", \"mt-3\", \"text-center\"], [\"routerLink\", \"/auth\", 1, \"btn\", \"resend\"], [1, \"mt-3\", \"text-center\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"fa-arrow-left\"], [\"routerLink\", \"/auth\"], [1, \"col-lg-7\", \"d-none\", \"d-lg-flex\", \"flex-column\", \"align-items-start\", \"bg-light\", \"position-relative\"], [1, \"text-start\", \"p-5\", \"w-100\"], [1, \"blockquote\"], [1, \"blockquote-footer\"], [\"title\", \"Source Title\"], [1, \"image-container\"], [\"src\", \"../../../assets/images/background.jpeg\", \"alt\", \"Stats graph\", 1, \"img-fluid\", \"position-absolute\"]],\n    template: function EnterCodeManuallyComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"h2\", 5);\n        i0.ɵɵelement(6, \"img\", 6);\n        i0.ɵɵtext(7, \" GPGC (Swabi)\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"div\", 7);\n        i0.ɵɵelement(9, \"i\", 8);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 9)(11, \"div\", 10)(12, \"div\", 11);\n        i0.ɵɵtext(13, \"3\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(14, \"div\", 10)(15, \"div\", 11);\n        i0.ɵɵtext(16, \"0\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(17, \"div\", 10)(18, \"div\", 11);\n        i0.ɵɵtext(19, \"6\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(20, \"div\", 10)(21, \"div\", 11);\n        i0.ɵɵtext(22, \"6\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(23, \"form\")(24, \"div\", 12)(25, \"button\", 13);\n        i0.ɵɵlistener(\"click\", function EnterCodeManuallyComponent_Template_button_click_25_listener() {\n          return ctx.verifyemail();\n        });\n        i0.ɵɵtext(26, \"Verify Email\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(27, \"p\", 14);\n        i0.ɵɵtext(28, \" Don,t recive the email? \");\n        i0.ɵɵelementStart(29, \"button\", 15);\n        i0.ɵɵtext(30, \"Click to resend\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(31, \"p\", 16);\n        i0.ɵɵelement(32, \"i\", 17);\n        i0.ɵɵtext(33, \" \\u00A0\");\n        i0.ɵɵelementStart(34, \"a\", 18);\n        i0.ɵɵtext(35, \"Back to log in \");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(36, \"div\", 19)(37, \"div\", 20)(38, \"blockquote\", 21)(39, \"h2\", 5);\n        i0.ɵɵtext(40, \"College management system Login page\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(41, \"footer\", 22);\n        i0.ɵɵtext(42, \"Name\");\n        i0.ɵɵelementStart(43, \"cite\", 23);\n        i0.ɵɵtext(44, \"Owner ~ GPGC SWABI\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(45, \"div\", 24);\n        i0.ɵɵelement(46, \"img\", 25);\n        i0.ɵɵelementEnd()()()();\n      }\n    },\n    dependencies: [i3.RouterLink, i1.ɵNgNoValidate, i1.NgControlStatusGroup, i1.NgForm],\n    styles: [\"body[_ngcontent-%COMP%], html[_ngcontent-%COMP%] {\\n    margin: 0;\\n    padding: 0;\\n    overflow-x: hidden; \\n\\n    height: 100%;\\n}\\n\\n\\n\\n.image-container[_ngcontent-%COMP%] {\\n    width: 100%;\\n    height: 100%;\\n    position: relative;\\n}\\n\\n.image-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    max-width: 80%;\\n    height: auto;\\n    position: absolute;\\n    right: 0;\\n    bottom: 0;\\n    object-fit: contain; \\n\\n}\\n.submit[_ngcontent-%COMP%]{\\n    background-color: #29578c;\\n    color: white;\\n}\\n.forgot[_ngcontent-%COMP%]{\\n    color: #29578c;\\n    text-decoration: none;\\n    font-weight: 700;\\n}\\na[_ngcontent-%COMP%]{\\n    cursor: pointer;\\n    text-decoration: none;\\n    color: black;\\n    font-weight: 600;\\n}\\n.key[_ngcontent-%COMP%]{\\n    background-color: #F4EBFF !important;\\n    border-radius: 50%;\\n    height: 50px;\\n    width: 50px;\\n    display: flex;\\n    justify-content: center;\\n    align-items: center;\\n    color: #29578c;\\n}\\n.resend[_ngcontent-%COMP%]{\\n    background-color: none;\\n    color:#29578c ;\\n    font-weight: 700;\\n    font-family: serif ;\\n}\\n.number-box[_ngcontent-%COMP%] {\\n    border: 2px solid #29578c;\\n    border-radius: 8px;\\n    text-align: center;\\n    font-size: 2rem;\\n    color: #29578c;\\n    padding: 10px;\\n  }\\n  \\n  .number-box[_ngcontent-%COMP%]:hover {\\n    box-shadow: 0 0 10px 3px rgba(138, 43, 178, 0.5);\\n  }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "<PERSON><PERSON>", "EnterCodeManuallyComponent", "constructor", "fb", "authService", "router", "loading", "resendLoading", "user", "otpId", "ngOnInit", "resetUser", "localStorage", "getItem", "signupUser", "JSON", "parse", "navigate", "otpForm", "group", "otp", "required", "pattern", "verifyOtp", "valid", "value", "_id", "subscribe", "next", "response", "success", "fire", "title", "html", "icon", "confirmButtonText", "confirmButtonColor", "then", "result", "isConfirmed", "setItem", "stringify", "error", "console", "errorMessage", "message", "text", "resendOtp", "resendData", "userId", "verifyemail", "_", "i0", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "_2", "selectors", "decls", "vars", "consts", "template", "EnterCodeManuallyComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "EnterCodeManuallyComponent_Template_button_click_25_listener"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\auth\\enter-code-manually\\enter-code-manually.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\auth\\enter-code-manually\\enter-code-manually.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { AuthService } from 'src/app/services/auth.service';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-enter-code-manually',\r\n  templateUrl: './enter-code-manually.component.html',\r\n  styleUrls: ['./enter-code-manually.component.css']\r\n})\r\nexport class EnterCodeManuallyComponent implements OnInit {\r\n  otpForm!: FormGroup;\r\n  loading = false;\r\n  resendLoading = false;\r\n  user: any = null;\r\n  otpId: string | null = null;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private authService: AuthService,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    // Get user data from localStorage (set during forgot password or signup)\r\n    const resetUser = localStorage.getItem('resetUser');\r\n    const signupUser = localStorage.getItem('signupUser');\r\n\r\n    if (resetUser) {\r\n      this.user = JSON.parse(resetUser);\r\n    } else if (signupUser) {\r\n      this.user = JSON.parse(signupUser);\r\n    }\r\n\r\n    if (!this.user) {\r\n      // If no user data, redirect to login\r\n      this.router.navigate(['/auth']);\r\n      return;\r\n    }\r\n\r\n    this.otpForm = this.fb.group({\r\n      otp: ['', [Validators.required, Validators.pattern(/^\\d{6}$/)]]\r\n    });\r\n  }\r\n\r\n  verifyOtp(): void {\r\n    if (this.otpForm.valid && this.user) {\r\n      this.loading = true;\r\n      const otp = this.otpForm.value.otp;\r\n\r\n      this.authService.verifyOtp(this.user._id, otp).subscribe({\r\n        next: (response) => {\r\n          this.loading = false;\r\n          if (response.success) {\r\n            Swal.fire({\r\n              title: '<h1 class=\"mb-4\">OTP Verified!</h1>',\r\n              html: '<p>Your OTP has been successfully verified. <br> You can now reset your password.</p>',\r\n              icon: 'success',\r\n              confirmButtonText: 'Reset Password',\r\n              confirmButtonColor: '#29578c'\r\n            }).then((result) => {\r\n              if (result.isConfirmed) {\r\n                // Store user data for password reset\r\n                localStorage.setItem('verifiedUser', JSON.stringify(this.user));\r\n                this.router.navigate(['/auth/reset-password']);\r\n              }\r\n            });\r\n          }\r\n        },\r\n        error: (error) => {\r\n          this.loading = false;\r\n          console.error('OTP verification error:', error);\r\n\r\n          const errorMessage = error.error?.error || error.error?.message || 'Invalid OTP. Please try again.';\r\n\r\n          Swal.fire({\r\n            title: 'Verification Failed',\r\n            text: errorMessage,\r\n            icon: 'error',\r\n            confirmButtonColor: '#29578c'\r\n          });\r\n        }\r\n      });\r\n    } else {\r\n      Swal.fire({\r\n        title: 'Invalid OTP',\r\n        text: 'Please enter a valid 6-digit OTP.',\r\n        icon: 'warning',\r\n        confirmButtonColor: '#29578c'\r\n      });\r\n    }\r\n  }\r\n\r\n  resendOtp(): void {\r\n    if (!this.user) return;\r\n\r\n    this.resendLoading = true;\r\n    const resendData = {\r\n      userId: this.user._id,\r\n      ...(this.otpId && { otpId: this.otpId })\r\n    };\r\n\r\n    this.authService.resendOtp(resendData).subscribe({\r\n      next: (response) => {\r\n        this.resendLoading = false;\r\n        if (response.success) {\r\n          if (response.otpId) {\r\n            this.otpId = response.otpId;\r\n          }\r\n\r\n          Swal.fire({\r\n            title: 'OTP Resent!',\r\n            text: 'A new OTP has been sent to your email.',\r\n            icon: 'success',\r\n            confirmButtonColor: '#29578c'\r\n          });\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.resendLoading = false;\r\n        console.error('Resend OTP error:', error);\r\n\r\n        const errorMessage = error.error?.message || 'Failed to resend OTP. Please try again.';\r\n\r\n        Swal.fire({\r\n          title: 'Resend Failed',\r\n          text: errorMessage,\r\n          icon: 'error',\r\n          confirmButtonColor: '#29578c'\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  // Legacy method for backward compatibility\r\n  verifyemail(): void {\r\n    this.verifyOtp();\r\n  }\r\n}\r\n", "<div class=\"container-fluid\">\r\n    <div class=\"row vh-100\">\r\n        <div class=\"col-lg-5 d-flex flex-column justify-content-center align-items-center p-3 bg-white\">\r\n            <div class=\"w-100\" style=\"max-width: 400px;\">\r\n                <div class=\"text-center\">\r\n                <h2 class=\"mb-4\"><img src=\"../../../assets/images/logo.jpeg\"/> GPGC (Swabi)</h2>\r\n                <div class=\"d-flex justify-content-center mt-5 mb-2\">\r\n                    <!-- <i class=\"fa fa-key key\" aria-hidden=\"true\"></i> -->\r\n                    <i class=\"fa fa-envelope key\" aria-hidden=\"true\" ></i>\r\n\r\n                </div>\r\n                \r\n                <!-- <h1 class=\"mb-4\">Check your Email</h1>\r\n                <p>We sent a verification link to <strong>luqman.dagai&#64;gmail.com</strong></p> -->\r\n            </div>\r\n                <div class=\"row justify-content-center mb-4 mt-4 d-flex align-items-center\">\r\n                  <div class=\"col-3\">\r\n                    <div class=\"number-box\">3</div>\r\n                  </div>\r\n                  <div class=\"col-3\">\r\n                    <div class=\"number-box\">0</div>\r\n                  </div>\r\n                  <div class=\"col-3\">\r\n                    <div class=\"number-box\">6</div>\r\n                  </div>\r\n                  <div class=\"col-3\">\r\n                    <div class=\"number-box\">6</div>\r\n                  </div>\r\n                </div>\r\n              \r\n                <form>\r\n                   \r\n                      <div class=\"d-grid gap-2\">\r\n                        <button type=\"button\" class=\"btn submit\" (click)=\"verifyemail()\">Verify Email</button>\r\n                    </div>\r\n                    <p class=\"mb-3 mt-3 text-center\"> Don,t recive the email? <button class=\"btn resend\" routerLink=\"/auth\">Click to resend</button></p>\r\n                    <p class=\"mt-3 text-center\"> <i class=\"fa fa-arrow-left\" aria-hidden=\"true\"></i> &nbsp;<a routerLink=\"/auth\">Back to log in </a></p>\r\n                \r\n                \r\n                </form>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-lg-7 d-none d-lg-flex flex-column align-items-start bg-light position-relative\">\r\n            <div class=\"text-start p-5 w-100\">\r\n                <blockquote class=\"blockquote\">\r\n                    <h2 class=\"mb-4\">College management system Login page</h2>\r\n                    <footer class=\"blockquote-footer\">Name<cite title=\"Source Title\">Owner ~ GPGC SWABI</cite></footer>\r\n                </blockquote>\r\n            </div>\r\n            <div class=\"image-container\">\r\n                <img src=\"../../../assets/images/background.jpeg\" class=\"img-fluid position-absolute\" alt=\"Stats graph\">\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAGnE,OAAOC,IAAI,MAAM,aAAa;;;;;AAO9B,OAAM,MAAOC,0BAA0B;EAOrCC,YACUC,EAAe,EACfC,WAAwB,EACxBC,MAAc;IAFd,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IARhB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,IAAI,GAAQ,IAAI;IAChB,KAAAC,KAAK,GAAkB,IAAI;EAMxB;EAEHC,QAAQA,CAAA;IACN;IACA,MAAMC,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACnD,MAAMC,UAAU,GAAGF,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAErD,IAAIF,SAAS,EAAE;MACb,IAAI,CAACH,IAAI,GAAGO,IAAI,CAACC,KAAK,CAACL,SAAS,CAAC;KAClC,MAAM,IAAIG,UAAU,EAAE;MACrB,IAAI,CAACN,IAAI,GAAGO,IAAI,CAACC,KAAK,CAACF,UAAU,CAAC;;IAGpC,IAAI,CAAC,IAAI,CAACN,IAAI,EAAE;MACd;MACA,IAAI,CAACH,MAAM,CAACY,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;MAC/B;;IAGF,IAAI,CAACC,OAAO,GAAG,IAAI,CAACf,EAAE,CAACgB,KAAK,CAAC;MAC3BC,GAAG,EAAE,CAAC,EAAE,EAAE,CAACrB,UAAU,CAACsB,QAAQ,EAAEtB,UAAU,CAACuB,OAAO,CAAC,SAAS,CAAC,CAAC;KAC/D,CAAC;EACJ;EAEAC,SAASA,CAAA;IACP,IAAI,IAAI,CAACL,OAAO,CAACM,KAAK,IAAI,IAAI,CAAChB,IAAI,EAAE;MACnC,IAAI,CAACF,OAAO,GAAG,IAAI;MACnB,MAAMc,GAAG,GAAG,IAAI,CAACF,OAAO,CAACO,KAAK,CAACL,GAAG;MAElC,IAAI,CAAChB,WAAW,CAACmB,SAAS,CAAC,IAAI,CAACf,IAAI,CAACkB,GAAG,EAAEN,GAAG,CAAC,CAACO,SAAS,CAAC;QACvDC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAACvB,OAAO,GAAG,KAAK;UACpB,IAAIuB,QAAQ,CAACC,OAAO,EAAE;YACpB9B,IAAI,CAAC+B,IAAI,CAAC;cACRC,KAAK,EAAE,qCAAqC;cAC5CC,IAAI,EAAE,uFAAuF;cAC7FC,IAAI,EAAE,SAAS;cACfC,iBAAiB,EAAE,gBAAgB;cACnCC,kBAAkB,EAAE;aACrB,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAI;cACjB,IAAIA,MAAM,CAACC,WAAW,EAAE;gBACtB;gBACA3B,YAAY,CAAC4B,OAAO,CAAC,cAAc,EAAEzB,IAAI,CAAC0B,SAAS,CAAC,IAAI,CAACjC,IAAI,CAAC,CAAC;gBAC/D,IAAI,CAACH,MAAM,CAACY,QAAQ,CAAC,CAAC,sBAAsB,CAAC,CAAC;;YAElD,CAAC,CAAC;;QAEN,CAAC;QACDyB,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACpC,OAAO,GAAG,KAAK;UACpBqC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAE/C,MAAME,YAAY,GAAGF,KAAK,CAACA,KAAK,EAAEA,KAAK,IAAIA,KAAK,CAACA,KAAK,EAAEG,OAAO,IAAI,gCAAgC;UAEnG7C,IAAI,CAAC+B,IAAI,CAAC;YACRC,KAAK,EAAE,qBAAqB;YAC5Bc,IAAI,EAAEF,YAAY;YAClBV,IAAI,EAAE,OAAO;YACbE,kBAAkB,EAAE;WACrB,CAAC;QACJ;OACD,CAAC;KACH,MAAM;MACLpC,IAAI,CAAC+B,IAAI,CAAC;QACRC,KAAK,EAAE,aAAa;QACpBc,IAAI,EAAE,mCAAmC;QACzCZ,IAAI,EAAE,SAAS;QACfE,kBAAkB,EAAE;OACrB,CAAC;;EAEN;EAEAW,SAASA,CAAA;IACP,IAAI,CAAC,IAAI,CAACvC,IAAI,EAAE;IAEhB,IAAI,CAACD,aAAa,GAAG,IAAI;IACzB,MAAMyC,UAAU,GAAG;MACjBC,MAAM,EAAE,IAAI,CAACzC,IAAI,CAACkB,GAAG;MACrB,IAAI,IAAI,CAACjB,KAAK,IAAI;QAAEA,KAAK,EAAE,IAAI,CAACA;MAAK,CAAE;KACxC;IAED,IAAI,CAACL,WAAW,CAAC2C,SAAS,CAACC,UAAU,CAAC,CAACrB,SAAS,CAAC;MAC/CC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACtB,aAAa,GAAG,KAAK;QAC1B,IAAIsB,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAID,QAAQ,CAACpB,KAAK,EAAE;YAClB,IAAI,CAACA,KAAK,GAAGoB,QAAQ,CAACpB,KAAK;;UAG7BT,IAAI,CAAC+B,IAAI,CAAC;YACRC,KAAK,EAAE,aAAa;YACpBc,IAAI,EAAE,wCAAwC;YAC9CZ,IAAI,EAAE,SAAS;YACfE,kBAAkB,EAAE;WACrB,CAAC;;MAEN,CAAC;MACDM,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACnC,aAAa,GAAG,KAAK;QAC1BoC,OAAO,CAACD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;QAEzC,MAAME,YAAY,GAAGF,KAAK,CAACA,KAAK,EAAEG,OAAO,IAAI,yCAAyC;QAEtF7C,IAAI,CAAC+B,IAAI,CAAC;UACRC,KAAK,EAAE,eAAe;UACtBc,IAAI,EAAEF,YAAY;UAClBV,IAAI,EAAE,OAAO;UACbE,kBAAkB,EAAE;SACrB,CAAC;MACJ;KACD,CAAC;EACJ;EAEA;EACAc,WAAWA,CAAA;IACT,IAAI,CAAC3B,SAAS,EAAE;EAClB;EAAC,QAAA4B,CAAA,G;qBA/HUlD,0BAA0B,EAAAmD,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA1B3D,0BAA0B;IAAA4D,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCXvCf,EAAA,CAAAiB,cAAA,aAA6B;QAKIjB,EAAA,CAAAkB,SAAA,aAA6C;QAAClB,EAAA,CAAAmB,MAAA,oBAAY;QAAAnB,EAAA,CAAAoB,YAAA,EAAK;QAChFpB,EAAA,CAAAiB,cAAA,aAAqD;QAEjDjB,EAAA,CAAAkB,SAAA,WAAsD;QAE1DlB,EAAA,CAAAoB,YAAA,EAAM;QAKNpB,EAAA,CAAAiB,cAAA,cAA4E;QAEhDjB,EAAA,CAAAmB,MAAA,SAAC;QAAAnB,EAAA,CAAAoB,YAAA,EAAM;QAEjCpB,EAAA,CAAAiB,cAAA,eAAmB;QACOjB,EAAA,CAAAmB,MAAA,SAAC;QAAAnB,EAAA,CAAAoB,YAAA,EAAM;QAEjCpB,EAAA,CAAAiB,cAAA,eAAmB;QACOjB,EAAA,CAAAmB,MAAA,SAAC;QAAAnB,EAAA,CAAAoB,YAAA,EAAM;QAEjCpB,EAAA,CAAAiB,cAAA,eAAmB;QACOjB,EAAA,CAAAmB,MAAA,SAAC;QAAAnB,EAAA,CAAAoB,YAAA,EAAM;QAInCpB,EAAA,CAAAiB,cAAA,YAAM;QAG2CjB,EAAA,CAAAqB,UAAA,mBAAAC,6DAAA;UAAA,OAASN,GAAA,CAAAlB,WAAA,EAAa;QAAA,EAAC;QAACE,EAAA,CAAAmB,MAAA,oBAAY;QAAAnB,EAAA,CAAAoB,YAAA,EAAS;QAE1FpB,EAAA,CAAAiB,cAAA,aAAiC;QAACjB,EAAA,CAAAmB,MAAA,iCAAwB;QAAAnB,EAAA,CAAAiB,cAAA,kBAA8C;QAAAjB,EAAA,CAAAmB,MAAA,uBAAe;QAAAnB,EAAA,CAAAoB,YAAA,EAAS;QAChIpB,EAAA,CAAAiB,cAAA,aAA4B;QAACjB,EAAA,CAAAkB,SAAA,aAAmD;QAAClB,EAAA,CAAAmB,MAAA,eAAM;QAAAnB,EAAA,CAAAiB,cAAA,aAAsB;QAAAjB,EAAA,CAAAmB,MAAA,uBAAe;QAAAnB,EAAA,CAAAoB,YAAA,EAAI;QAM5IpB,EAAA,CAAAiB,cAAA,eAAgG;QAGnEjB,EAAA,CAAAmB,MAAA,4CAAoC;QAAAnB,EAAA,CAAAoB,YAAA,EAAK;QAC1DpB,EAAA,CAAAiB,cAAA,kBAAkC;QAAAjB,EAAA,CAAAmB,MAAA,YAAI;QAAAnB,EAAA,CAAAiB,cAAA,gBAA2B;QAAAjB,EAAA,CAAAmB,MAAA,0BAAkB;QAAAnB,EAAA,CAAAoB,YAAA,EAAO;QAGlGpB,EAAA,CAAAiB,cAAA,eAA6B;QACzBjB,EAAA,CAAAkB,SAAA,eAAwG;QAC5GlB,EAAA,CAAAoB,YAAA,EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}