import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class BulkUploadService {
  private apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) { }

  // Upload Excel file for bulk student registration
  uploadStudentsBulk(file: File): Observable<any> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post(`${this.apiUrl}/bulk-upload-students`, formData, {
      reportProgress: true,
      observe: 'events'
    });
  }

  // Download Excel template for bulk upload
  downloadStudentTemplate(): Observable<Blob> {
    return this.http.get(`${this.apiUrl}/download-student-template`, {
      responseType: 'blob'
    });
  }

  // Get upload progress
  uploadStudentsBulkWithProgress(file: File): Observable<any> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post(`${this.apiUrl}/bulk-upload-students`, formData, {
      reportProgress: true,
      observe: 'events'
    });
  }
}
