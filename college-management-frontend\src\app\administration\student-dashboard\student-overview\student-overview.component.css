.student-dashboard {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  text-align: center;
}

.error-container mat-icon {
  font-size: 48px;
  height: 48px;
  width: 48px;
  margin-bottom: 16px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.welcome-section h1 {
  margin: 0;
  color: #333;
  font-weight: 500;
}

.welcome-section p {
  margin: 8px 0 0 0;
  color: #666;
}

.student-info {
  font-weight: 500;
  color: #1976d2;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  transition: transform 0.2s ease-in-out;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.12);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  color: white;
}

.stat-card.attendance .stat-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card.present .stat-icon {
  background: linear-gradient(135deg, #4caf50 0%, #8bc34a 100%);
}

.stat-card.absent .stat-icon {
  background: linear-gradient(135deg, #f44336 0%, #ff9800 100%);
}

.stat-card.total .stat-icon {
  background: linear-gradient(135deg, #2196f3 0%, #03a9f4 100%);
}

.stat-icon mat-icon {
  font-size: 28px;
  height: 28px;
  width: 28px;
}

.stat-info h3 {
  margin: 0;
  font-size: 2rem;
  font-weight: 600;
  color: #333;
}

.stat-info p {
  margin: 4px 0 0 0;
  color: #666;
  font-size: 0.9rem;
}

.content-section {
  margin-bottom: 30px;
}

.content-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.schedule-card, .attendance-card {
  min-height: 300px;
}

.classes-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.class-item {
  display: flex;
  gap: 16px;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 8px;
  border-left: 4px solid #1976d2;
}

.time-slot {
  min-width: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.time {
  font-weight: 600;
  color: #1976d2;
  font-size: 0.9rem;
}

.class-details h4 {
  margin: 0 0 4px 0;
  color: #333;
  font-size: 1.1rem;
}

.class-details p {
  margin: 2px 0;
  color: #666;
  font-size: 0.9rem;
}

.room {
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  display: inline-block;
  margin-top: 4px;
}

.no-classes, .no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
  color: #666;
}

.no-classes mat-icon, .no-data mat-icon {
  font-size: 48px;
  height: 48px;
  width: 48px;
  margin-bottom: 16px;
  color: #ccc;
}

.subject-attendance {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.subject-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.subject-info h4 {
  margin: 0 0 4px 0;
  color: #333;
  font-size: 1rem;
}

.subject-info p {
  margin: 0;
  color: #666;
  font-size: 0.8rem;
}

.attendance-bar {
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 120px;
}

.progress-bar {
  width: 80px;
  height: 8px;
  background-color: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4caf50 0%, #8bc34a 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.percentage {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
  min-width: 35px;
}

.recent-section {
  margin-bottom: 30px;
}

.attendance-history {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.history-item {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 16px;
  align-items: center;
  padding: 12px;
  background-color: #f9f9f9;
  border-radius: 6px;
}

.date-info {
  display: flex;
  flex-direction: column;
}

.date {
  font-weight: 500;
  color: #333;
  font-size: 0.9rem;
}

.day {
  font-size: 0.8rem;
  color: #666;
}

.subject-info {
  display: flex;
  flex-direction: column;
}

.subject {
  font-weight: 500;
  color: #333;
  font-size: 0.9rem;
}

.teacher {
  font-size: 0.8rem;
  color: #666;
}

.status-info {
  display: flex;
  justify-content: center;
}

.quick-actions {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.quick-actions h2 {
  margin: 0 0 20px 0;
  color: #333;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.action-buttons button {
  display: flex;
  align-items: center;
  gap: 8px;
}

@media (max-width: 768px) {
  .student-dashboard {
    padding: 10px;
  }
  
  .dashboard-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .content-row {
    grid-template-columns: 1fr;
  }
  
  .history-item {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons button {
    width: 100%;
    justify-content: center;
  }
}
