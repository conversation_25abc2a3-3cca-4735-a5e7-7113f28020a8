<div class="student-subjects-container">
  <!-- Header -->
  <div class="page-header">
    <div class="header-content">
      <h1>📚 My Subjects</h1>
      <p>View your enrolled subjects, teachers, and attendance progress</p>
    </div>
    <button mat-icon-button (click)="refreshData()" matTooltip="Refresh your subject information">
      <mat-icon>refresh</mat-icon>
    </button>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="loading-container">
    <mat-spinner></mat-spinner>
    <p>📖 Loading your subjects and attendance information...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !loading" class="error-container">
    <mat-icon>error</mat-icon>
    <h3>Unable to Load Your Subjects</h3>
    <p>{{ error }}</p>
    <p>Don't worry! This is usually a temporary issue.</p>
    <button mat-raised-button color="primary" (click)="refreshData()">
      <mat-icon>refresh</mat-icon>
      Try Loading Again
    </button>
  </div>

  <!-- Content -->
  <div *ngIf="studentDashboard && !loading && !error" class="subjects-content">
    <!-- Summary Stats -->
    <div class="summary-stats">
      <mat-card class="stat-card total">
        <mat-card-content>
          <div class="stat-content">
            <div class="stat-icon">
              <mat-icon>subject</mat-icon>
            </div>
            <div class="stat-info">
              <h3>{{ studentDashboard.subjectAttendance?.length || 0 }}</h3>
              <p>Subjects Enrolled</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card average">
        <mat-card-content>
          <div class="stat-content">
            <div class="stat-icon">
              <mat-icon>trending_up</mat-icon>
            </div>
            <div class="stat-info">
              <h3>{{ calculateOverallPercentage() }}%</h3>
              <p>Your Attendance Rate</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Subjects List -->
    <div *ngIf="studentDashboard.subjectAttendance?.length > 0; else noSubjects" class="subjects-grid">
      <mat-card *ngFor="let subject of studentDashboard.subjectAttendance" class="subject-card">
        <mat-card-header>
          <div mat-card-avatar class="subject-avatar">
            <mat-icon>subject</mat-icon>
          </div>
          <mat-card-title>{{ subject.subjectName }}</mat-card-title>
          <mat-card-subtitle>Subject Code: {{ subject._id }}</mat-card-subtitle>
        </mat-card-header>
        
        <mat-card-content>
          <div class="subject-details">
            <!-- Attendance Summary -->
            <div class="attendance-summary">
              <div class="attendance-circle" [class]="getAttendanceStatus(subject.percentage)">
                <span class="percentage">{{ subject.percentage | number:'1.0-0' }}%</span>
              </div>
              <div class="attendance-info">
                <h4>{{ getAttendanceMessage(subject.percentage) }}</h4>
                <p>{{ subject.present }} out of {{ subject.total }} classes attended</p>
              </div>
            </div>

            <!-- Progress Bar -->
            <div class="progress-container">
              <div class="progress-bar">
                <div class="progress-fill" 
                     [style.width.%]="subject.percentage"
                     [class]="getAttendanceStatus(subject.percentage)">
                </div>
              </div>
              <div class="progress-labels">
                <span>Present: {{ subject.present }}</span>
                <span>Absent: {{ subject.absent }}</span>
                <span>Total: {{ subject.total }}</span>
              </div>
            </div>

            <!-- Subject Stats -->
            <div class="subject-stats">
              <div class="stat-item">
                <mat-icon>check_circle</mat-icon>
                <div class="stat-details">
                  <span class="stat-value">{{ subject.present }}</span>
                  <span class="stat-label">Present</span>
                </div>
              </div>
              <div class="stat-item">
                <mat-icon>cancel</mat-icon>
                <div class="stat-details">
                  <span class="stat-value">{{ subject.absent }}</span>
                  <span class="stat-label">Absent</span>
                </div>
              </div>
              <div class="stat-item">
                <mat-icon>event</mat-icon>
                <div class="stat-details">
                  <span class="stat-value">{{ subject.total }}</span>
                  <span class="stat-label">Total Classes</span>
                </div>
              </div>
            </div>
          </div>
        </mat-card-content>

        <mat-card-actions>
          <button mat-button color="primary" routerLink="/dashboard/student/attendance">
            <mat-icon>fact_check</mat-icon>
            View Attendance
          </button>
        </mat-card-actions>
      </mat-card>
    </div>

    <!-- No Subjects State -->
    <ng-template #noSubjects>
      <mat-card class="no-data-card">
        <mat-card-content>
          <div class="no-data-content">
            <mat-icon>subject</mat-icon>
            <h3>No Subjects Found</h3>
            <p>You don't have any subjects assigned yet. Please contact your administrator.</p>
          </div>
        </mat-card-content>
      </mat-card>
    </ng-template>

    <!-- Quick Actions -->
    <div class="quick-actions">
      <h3>Quick Actions</h3>
      <div class="action-buttons">
        <button mat-raised-button color="primary" routerLink="/dashboard/student/attendance">
          <mat-icon>fact_check</mat-icon>
          View Full Attendance
        </button>
        <button mat-raised-button color="accent" routerLink="/dashboard/student/teachers">
          <mat-icon>person</mat-icon>
          View Teachers
        </button>
        <button mat-raised-button color="primary" routerLink="/dashboard/student/classes">
          <mat-icon>class</mat-icon>
          View Classes
        </button>
      </div>
    </div>
  </div>
</div>
