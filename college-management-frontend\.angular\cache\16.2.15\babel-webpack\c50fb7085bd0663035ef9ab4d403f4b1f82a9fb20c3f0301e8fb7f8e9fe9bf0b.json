{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ProgramService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = environment.apiUrl;\n  }\n  // Get all programs\n  getAllPrograms(isActive) {\n    let params = new HttpParams();\n    if (isActive !== undefined) {\n      params = params.set('isActive', isActive.toString());\n    }\n    return this.http.get(`${this.apiUrl}/programs`, {\n      params\n    });\n  }\n  // Get program by ID\n  getProgramById(id) {\n    return this.http.get(`${this.apiUrl}/program/${id}`);\n  }\n  // Create new program\n  createProgram(program) {\n    return this.http.post(`${this.apiUrl}/program`, program);\n  }\n  // Update program\n  updateProgram(id, program) {\n    return this.http.put(`${this.apiUrl}/program/${id}`, program);\n  }\n  // Delete program (soft delete)\n  deleteProgram(id) {\n    return this.http.delete(`${this.apiUrl}/program/${id}`);\n  }\n  // Permanently delete program\n  permanentDeleteProgram(id) {\n    return this.http.delete(`${this.apiUrl}/program/${id}/permanent`);\n  }\n  static #_ = this.ɵfac = function ProgramService_Factory(t) {\n    return new (t || ProgramService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ProgramService,\n    factory: ProgramService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpParams", "environment", "ProgramService", "constructor", "http", "apiUrl", "getAllPrograms", "isActive", "params", "undefined", "set", "toString", "get", "getProgramById", "id", "createProgram", "program", "post", "updateProgram", "put", "deleteProgram", "delete", "permanentDeleteProgram", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\services\\program.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { environment } from 'src/environments/environment';\r\nimport { Program } from '../models/user';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ProgramService {\r\n  private apiUrl = environment.apiUrl;\r\n\r\n  constructor(private http: HttpClient) { }\r\n\r\n  // Get all programs\r\n  getAllPrograms(isActive?: boolean): Observable<any> {\r\n    let params = new HttpParams();\r\n    if (isActive !== undefined) {\r\n      params = params.set('isActive', isActive.toString());\r\n    }\r\n    return this.http.get<any>(`${this.apiUrl}/programs`, { params });\r\n  }\r\n\r\n  // Get program by ID\r\n  getProgramById(id: string): Observable<any> {\r\n    return this.http.get<any>(`${this.apiUrl}/program/${id}`);\r\n  }\r\n\r\n  // Create new program\r\n  createProgram(program: Partial<Program>): Observable<any> {\r\n    return this.http.post<any>(`${this.apiUrl}/program`, program);\r\n  }\r\n\r\n  // Update program\r\n  updateProgram(id: string, program: Partial<Program>): Observable<any> {\r\n    return this.http.put<any>(`${this.apiUrl}/program/${id}`, program);\r\n  }\r\n\r\n  // Delete program (soft delete)\r\n  deleteProgram(id: string): Observable<any> {\r\n    return this.http.delete<any>(`${this.apiUrl}/program/${id}`);\r\n  }\r\n\r\n  // Permanently delete program\r\n  permanentDeleteProgram(id: string): Observable<any> {\r\n    return this.http.delete<any>(`${this.apiUrl}/program/${id}/permanent`);\r\n  }\r\n}\r\n"], "mappings": "AACA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,WAAW,QAAQ,8BAA8B;;;AAM1D,OAAM,MAAOC,cAAc;EAGzBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAGJ,WAAW,CAACI,MAAM;EAEK;EAExC;EACAC,cAAcA,CAACC,QAAkB;IAC/B,IAAIC,MAAM,GAAG,IAAIR,UAAU,EAAE;IAC7B,IAAIO,QAAQ,KAAKE,SAAS,EAAE;MAC1BD,MAAM,GAAGA,MAAM,CAACE,GAAG,CAAC,UAAU,EAAEH,QAAQ,CAACI,QAAQ,EAAE,CAAC;;IAEtD,OAAO,IAAI,CAACP,IAAI,CAACQ,GAAG,CAAM,GAAG,IAAI,CAACP,MAAM,WAAW,EAAE;MAAEG;IAAM,CAAE,CAAC;EAClE;EAEA;EACAK,cAAcA,CAACC,EAAU;IACvB,OAAO,IAAI,CAACV,IAAI,CAACQ,GAAG,CAAM,GAAG,IAAI,CAACP,MAAM,YAAYS,EAAE,EAAE,CAAC;EAC3D;EAEA;EACAC,aAAaA,CAACC,OAAyB;IACrC,OAAO,IAAI,CAACZ,IAAI,CAACa,IAAI,CAAM,GAAG,IAAI,CAACZ,MAAM,UAAU,EAAEW,OAAO,CAAC;EAC/D;EAEA;EACAE,aAAaA,CAACJ,EAAU,EAAEE,OAAyB;IACjD,OAAO,IAAI,CAACZ,IAAI,CAACe,GAAG,CAAM,GAAG,IAAI,CAACd,MAAM,YAAYS,EAAE,EAAE,EAAEE,OAAO,CAAC;EACpE;EAEA;EACAI,aAAaA,CAACN,EAAU;IACtB,OAAO,IAAI,CAACV,IAAI,CAACiB,MAAM,CAAM,GAAG,IAAI,CAAChB,MAAM,YAAYS,EAAE,EAAE,CAAC;EAC9D;EAEA;EACAQ,sBAAsBA,CAACR,EAAU;IAC/B,OAAO,IAAI,CAACV,IAAI,CAACiB,MAAM,CAAM,GAAG,IAAI,CAAChB,MAAM,YAAYS,EAAE,YAAY,CAAC;EACxE;EAAC,QAAAS,CAAA,G;qBArCUrB,cAAc,EAAAsB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAd1B,cAAc;IAAA2B,OAAA,EAAd3B,cAAc,CAAA4B,IAAA;IAAAC,UAAA,EAFb;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}