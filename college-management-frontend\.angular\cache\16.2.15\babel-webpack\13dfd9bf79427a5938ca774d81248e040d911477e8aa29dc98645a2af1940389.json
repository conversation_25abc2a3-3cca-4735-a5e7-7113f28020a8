{"ast": null, "code": "import Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/department.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/checkbox\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/forms\";\nfunction DepartmentTableComponent_tr_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 18)(2, \"mat-checkbox\", 19);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\", 20);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\", 21)(7, \"div\", 22)(8, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function DepartmentTableComponent_tr_29_Template_button_click_8_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r3);\n      const department_r1 = restoredCtx.$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.deletedepartment(department_r1._id));\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"delete\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function DepartmentTableComponent_tr_29_Template_button_click_11_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r3);\n      const department_r1 = restoredCtx.$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.editdepartment(department_r1));\n    });\n    i0.ɵɵelementStart(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"edit\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const department_r1 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", department_r1 == null ? null : department_r1.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", department_r1 == null ? null : department_r1.note, \" \");\n  }\n}\nexport let DepartmentTableComponent = /*#__PURE__*/(() => {\n  class DepartmentTableComponent {\n    constructor(departmentservice, router) {\n      this.departmentservice = departmentservice;\n      this.router = router;\n      this.currentPage = 1;\n      this.itemsPerPage = 5;\n      this.searchQuery = '';\n      this.departments = [];\n      this.isChecked = false;\n      this.isIndeterminate = true;\n    }\n    ngOnInit() {\n      this.getdepartments();\n    }\n    searchdepartment() {\n      if (this.searchQuery) {\n        this.departments = this.departments.filter(department => {\n          return department.departmentName.toLowerCase().includes(this.searchQuery.toLowerCase()) || department.code.toLowerCase().includes(this.searchQuery);\n        });\n      } else {\n        this.departments = this.painateddepartment;\n      }\n      this.currentPage = 1;\n    }\n    get painateddepartment() {\n      const startIndex = (this.currentPage - 1) * this.itemsPerPage;\n      const endIndex = startIndex + this.itemsPerPage;\n      return this.departments.slice(startIndex, endIndex);\n    }\n    get totalPages() {\n      return Math.ceil(this.departments.length / this.itemsPerPage);\n    }\n    nextPage() {\n      if (this.currentPage < this.totalPages) {\n        this.currentPage++;\n      }\n    }\n    previousPage() {\n      if (this.currentPage > 1) {\n        this.currentPage--;\n      }\n    }\n    // Edit department\n    editdepartment(department) {\n      this.router.navigate(['/dashboard/admin/department/add-department' + '/' + department._id]);\n      // this.departmentForm.patchValue({\n      //   departmentName: department.departmentName,\n      //   code: department.code\n      // });\n      // this.editingdepartmentId = department._id;\n    }\n    // Fetch all departments\n    getdepartments() {\n      this.departmentservice.getdepartment().subscribe(data => {\n        this.departments = data.departments;\n      }, error => {\n        console.error('Error fetching departments:', error);\n      });\n    }\n    deletedepartment(id) {\n      Swal.fire({\n        title: 'Are you sure?',\n        text: 'This action will permanently delete the department.',\n        icon: 'warning',\n        showCancelButton: true,\n        confirmButtonColor: '#d33',\n        cancelButtonColor: '#3085d6',\n        confirmButtonText: 'Yes, delete it!',\n        cancelButtonText: 'Cancel'\n      }).then(result => {\n        if (result.isConfirmed) {\n          // Proceed with deletion\n          this.departmentservice.deletedepartment(id).subscribe(res => {\n            Swal.fire({\n              title: res.message,\n              icon: 'success',\n              confirmButtonText: 'OK',\n              confirmButtonColor: '#3085d6',\n              timer: 2000\n            }).then(() => {\n              // Update the UI without reloading\n              this.departments = this.departments.filter(department => department._id !== id);\n            });\n          }, error => {\n            console.error('Error deleting department:', error);\n            Swal.fire({\n              title: 'Error',\n              text: 'Failed to delete the department. Please try again.',\n              icon: 'error',\n              confirmButtonColor: '#3085d6'\n            });\n          });\n        }\n      });\n    }\n    toggleCheckbox() {\n      if (this.isIndeterminate) {\n        this.isIndeterminate = false;\n        this.isChecked = true;\n      } else if (this.isChecked) {\n        this.isChecked = false;\n      } else {\n        this.isIndeterminate = true;\n      }\n    }\n    static #_ = this.ɵfac = function DepartmentTableComponent_Factory(t) {\n      return new (t || DepartmentTableComponent)(i0.ɵɵdirectiveInject(i1.DepartmentService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DepartmentTableComponent,\n      selectors: [[\"app-department-table\"]],\n      decls: 39,\n      vars: 8,\n      consts: [[1, \"maindiv\"], [1, \"secondarydiv\"], [1, \"my-3\", \"d-flex\", \"justify-content-between\", \"searchAndtab\"], [1, \"d-flex\", \"flex-wrap\", \"gap-3\"], [\"routerLink\", \"/dashboard/admin/department/add-department\", 1, \"btn\", \"btn-top\", \"border\", \"d-flex\", \"align-items-center\"], [1, \"search-container\"], [1, \"search-input-wrapper\"], [\"type\", \"text\", \"placeholder\", \"Search\", 1, \"search-input\", 3, \"ngModel\", \"input\", \"ngModelChange\"], [1, \"search-icon\"], [1, \"table-container\"], [1, \"table-responsive\"], [1, \"tablehead\"], [\"color\", \"primary\", 3, \"ngModel\", \"indeterminate\", \"ngModelChange\", \"change\"], [1, \"two\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"p-3\", \"align-items-center\", \"justify-content-between\"], [1, \"btn\", \"btn-top\", \"border\", 3, \"disabled\", \"click\"], [1, \"page\"], [\"data-label\", \"Teacher Name\", 1, \"name\"], [\"color\", \"primary\"], [\"data-label\", \"Class\", 1, \"para\"], [\"data-label\", \"Action\", 1, \"para\"], [1, \"d-flex\", \"gap-2\"], [1, \"btn\", \"btndelete\", \"d-flex\", \"justify-content-center\", \"align-items-center\", 3, \"click\"], [1, \"btn\", \"btnedit\", \"d-flex\", \"justify-content-center\", \"align-items-center\", 3, \"click\"]],\n      template: function DepartmentTableComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"button\", 4)(5, \"mat-icon\");\n          i0.ɵɵtext(6, \"add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(7, \"\\u00A0 Add New Department\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\")(9, \"div\", 5)(10, \"div\", 6)(11, \"input\", 7);\n          i0.ɵɵlistener(\"input\", function DepartmentTableComponent_Template_input_input_11_listener() {\n            return ctx.searchdepartment();\n          })(\"ngModelChange\", function DepartmentTableComponent_Template_input_ngModelChange_11_listener($event) {\n            return ctx.searchQuery = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"mat-icon\", 8);\n          i0.ɵɵtext(13, \"search\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(14, \"div\", 9)(15, \"div\", 10)(16, \"table\")(17, \"thead\")(18, \"tr\", 11)(19, \"th\")(20, \"mat-checkbox\", 12);\n          i0.ɵɵlistener(\"ngModelChange\", function DepartmentTableComponent_Template_mat_checkbox_ngModelChange_20_listener($event) {\n            return ctx.isChecked = $event;\n          })(\"change\", function DepartmentTableComponent_Template_mat_checkbox_change_20_listener() {\n            return ctx.toggleCheckbox();\n          });\n          i0.ɵɵtext(21, \" Department name \");\n          i0.ɵɵelementStart(22, \"mat-icon\");\n          i0.ɵɵtext(23, \"arrow_downward\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(24, \"th\");\n          i0.ɵɵtext(25, \"code \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"th\", 13);\n          i0.ɵɵtext(27, \"Actions\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(28, \"tbody\");\n          i0.ɵɵtemplate(29, DepartmentTableComponent_tr_29_Template, 14, 2, \"tr\", 14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(30, \"div\", 15)(31, \"div\")(32, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function DepartmentTableComponent_Template_button_click_32_listener() {\n            return ctx.previousPage();\n          });\n          i0.ɵɵtext(33, \"Previous\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(34, \"div\", 17);\n          i0.ɵɵtext(35);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"div\")(37, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function DepartmentTableComponent_Template_button_click_37_listener() {\n            return ctx.nextPage();\n          });\n          i0.ɵɵtext(38, \"Next\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngModel\", ctx.isChecked)(\"indeterminate\", ctx.isIndeterminate);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngForOf\", ctx.painateddepartment);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.currentPage === 1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate2(\"Page \", ctx.currentPage, \" of \", ctx.totalPages, \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.currentPage === ctx.totalPages);\n        }\n      },\n      dependencies: [i3.NgForOf, i2.RouterLink, i4.MatCheckbox, i5.MatIcon, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgModel]\n    });\n  }\n  return DepartmentTableComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}