{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { UsersRoutingModule } from './users-routing.module';\nimport { UserListComponent } from './user-list/user-list.component';\nimport { UserFormComponent } from './user-form/user-form.component';\nimport { MaterialModule } from 'src/app/material';\nimport * as i0 from \"@angular/core\";\nexport class UsersModule {\n  static #_ = this.ɵfac = function UsersModule_Factory(t) {\n    return new (t || UsersModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: UsersModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, UsersRoutingModule, ReactiveFormsModule, FormsModule, MaterialModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(UsersModule, {\n    declarations: [UserListComponent, UserFormComponent],\n    imports: [CommonModule, UsersRoutingModule, ReactiveFormsModule, FormsModule, MaterialModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "FormsModule", "UsersRoutingModule", "UserListComponent", "UserFormComponent", "MaterialModule", "UsersModule", "_", "_2", "_3", "declarations", "imports"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\users\\users.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\n\nimport { UsersRoutingModule } from './users-routing.module';\nimport { UserListComponent } from './user-list/user-list.component';\nimport { UserFormComponent } from './user-form/user-form.component';\nimport { MaterialModule } from 'src/app/material';\n\n@NgModule({\n  declarations: [\n    UserListComponent,\n    UserFormComponent\n  ],\n  imports: [\n    CommonModule,\n    UsersRoutingModule,\n    ReactiveFormsModule,\n    FormsModule,\n    MaterialModule\n  ]\n})\nexport class UsersModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AAEjE,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,cAAc,QAAQ,kBAAkB;;AAejD,OAAM,MAAOC,WAAW;EAAA,QAAAC,CAAA,G;qBAAXD,WAAW;EAAA;EAAA,QAAAE,EAAA,G;UAAXF;EAAW;EAAA,QAAAG,EAAA,G;cAPpBV,YAAY,EACZG,kBAAkB,EAClBF,mBAAmB,EACnBC,WAAW,EACXI,cAAc;EAAA;;;2EAGLC,WAAW;IAAAI,YAAA,GAXpBP,iBAAiB,EACjBC,iBAAiB;IAAAO,OAAA,GAGjBZ,YAAY,EACZG,kBAAkB,EAClBF,mBAAmB,EACnBC,WAAW,EACXI,cAAc;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}