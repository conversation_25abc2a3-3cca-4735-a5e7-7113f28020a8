import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { UxHelperService } from '../../services/ux-helper.service';
import { UX_TEXT_STANDARDS } from '../../constants/ux-text-standards';

@Component({
  selector: 'app-ux-demo',
  template: `
    <div class="ux-demo-container">
      <h2>🎨 UX Improvements Demo</h2>
      <p>This component demonstrates all the user experience improvements implemented in the college management system.</p>

      <!-- Form Example -->
      <mat-card class="demo-section">
        <mat-card-header>
          <mat-card-title>Enhanced Form with User-Friendly Labels & Tooltips</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <form [formGroup]="demoForm" (ngSubmit)="onSubmit()">
            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>
                  {{ getLabel('FULL_NAME') }}
                  <mat-icon matSuffix [matTooltip]="getTooltip('FULL_NAME')">help_outline</mat-icon>
                </mat-label>
                <input matInput formControlName="name" [placeholder]="getPlaceholder('ENTER_NAME')">
                <mat-error *ngIf="demoForm.get('name')?.invalid">
                  {{ getFieldErrorMessage('name') }}
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>
                  {{ getLabel('EMAIL_ADDRESS') }}
                  <mat-icon matSuffix [matTooltip]="getTooltip('EMAIL_ADDRESS')">help_outline</mat-icon>
                </mat-label>
                <input matInput type="email" formControlName="email" [placeholder]="getPlaceholder('ENTER_EMAIL')">
                <mat-error *ngIf="demoForm.get('email')?.invalid">
                  {{ getFieldErrorMessage('email') }}
                </mat-error>
              </mat-form-field>
            </div>

            <div class="form-actions">
              <button mat-raised-button color="primary" type="submit" [disabled]="demoForm.invalid">
                {{ getButtonText('SAVE') }}
              </button>
              <button mat-button type="button" (click)="resetForm()">
                {{ getButtonText('RESET') }}
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>

      <!-- Message Examples -->
      <mat-card class="demo-section">
        <mat-card-header>
          <mat-card-title>Enhanced Success & Error Messages</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="button-group">
            <button mat-raised-button color="primary" (click)="showSuccessExample()">
              Show Success Message
            </button>
            <button mat-raised-button color="warn" (click)="showErrorExample()">
              Show Error Message
            </button>
            <button mat-raised-button color="accent" (click)="showConfirmationExample()">
              Show Confirmation Dialog
            </button>
            <button mat-raised-button (click)="showLoadingExample()">
              Show Loading Message
            </button>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Attendance Warning Example -->
      <mat-card class="demo-section">
        <mat-card-header>
          <mat-card-title>Student-Specific Features</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="button-group">
            <button mat-raised-button color="warn" (click)="showAttendanceWarning()">
              Show Attendance Warning (Below 75%)
            </button>
            <button mat-raised-button color="warn" (click)="showStruckOffWarning()">
              Show Struck-Off Warning (BS Students)
            </button>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Role-Specific Text Example -->
      <mat-card class="demo-section">
        <mat-card-header>
          <mat-card-title>Role-Specific Welcome Messages</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="button-group">
            <button mat-raised-button color="primary" (click)="showWelcomeMessage('Principal')">
              Principal Welcome
            </button>
            <button mat-raised-button color="primary" (click)="showWelcomeMessage('Teacher')">
              Teacher Welcome
            </button>
            <button mat-raised-button color="primary" (click)="showWelcomeMessage('Student')">
              Student Welcome
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .ux-demo-container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .demo-section {
      margin-bottom: 20px;
    }

    .form-row {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;
    }

    .form-row mat-form-field {
      flex: 1;
    }

    .form-actions {
      display: flex;
      gap: 10px;
      justify-content: flex-end;
    }

    .button-group {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
    }

    .button-group button {
      margin-bottom: 10px;
    }
  `]
})
export class UxDemoComponent {
  demoForm: FormGroup;

  constructor(
    private fb: FormBuilder,
    private uxHelper: UxHelperService
  ) {
    this.demoForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]]
    });
  }

  // Helper methods for template
  getFieldErrorMessage(fieldName: string): string {
    const field = this.demoForm.get(fieldName);
    if (field && field.invalid && field.touched) {
      const errors = field.errors;
      if (errors) {
        const errorType = Object.keys(errors)[0];
        return this.uxHelper.getValidationErrorMessage(fieldName, errorType, errors[errorType]);
      }
    }
    return '';
  }

  getTooltip(fieldKey: keyof typeof UX_TEXT_STANDARDS.TOOLTIPS): string {
    return this.uxHelper.getTooltipText(fieldKey);
  }

  getLabel(labelKey: keyof typeof UX_TEXT_STANDARDS.FORM_LABELS): string {
    return this.uxHelper.getLabelText(labelKey);
  }

  getPlaceholder(placeholderKey: keyof typeof UX_TEXT_STANDARDS.PLACEHOLDERS): string {
    return this.uxHelper.getPlaceholderText(placeholderKey);
  }

  getButtonText(buttonKey: keyof typeof UX_TEXT_STANDARDS.BUTTONS): string {
    return this.uxHelper.getButtonText(buttonKey);
  }

  // Demo methods
  onSubmit(): void {
    if (this.demoForm.valid) {
      this.uxHelper.showSuccessMessage('DATA_SAVED', 'Your demo form has been submitted successfully!');
    } else {
      this.uxHelper.showErrorMessage('VALIDATION_ERROR', 'Please fill in all required fields correctly.');
    }
  }

  resetForm(): void {
    this.demoForm.reset();
  }

  showSuccessExample(): void {
    this.uxHelper.showSuccessMessage('USER_CREATED');
  }

  showErrorExample(): void {
    this.uxHelper.showErrorMessage('SERVER_ERROR');
  }

  showConfirmationExample(): void {
    this.uxHelper.showConfirmationDialog('DELETE_USER').then((result) => {
      if (result.isConfirmed) {
        this.uxHelper.showSuccessMessage('DATA_SAVED', 'Action confirmed!');
      }
    });
  }

  showLoadingExample(): void {
    this.uxHelper.showLoadingMessage('PROCESSING_REQUEST');
    setTimeout(() => {
      this.uxHelper.closeLoadingMessage();
      this.uxHelper.showSuccessMessage('DATA_SAVED', 'Loading completed!');
    }, 3000);
  }

  showAttendanceWarning(): void {
    this.uxHelper.showAttendanceWarning(65, 'Mathematics');
  }

  showStruckOffWarning(): void {
    this.uxHelper.showStruckOffWarning('Computer Science', 3);
  }

  showWelcomeMessage(role: string): void {
    this.uxHelper.showWelcomeMessage('John Doe', role);
  }
}
