{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { UserListComponent } from './user-list/user-list.component';\nimport { UserFormComponent } from './user-form/user-form.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: 'user-list',\n  pathMatch: 'full'\n}, {\n  path: 'user-list',\n  component: UserListComponent\n}, {\n  path: 'add-user',\n  component: UserFormComponent\n}, {\n  path: 'edit-user/:id',\n  component: UserFormComponent\n}];\nexport let UsersRoutingModule = /*#__PURE__*/(() => {\n  class UsersRoutingModule {\n    static #_ = this.ɵfac = function UsersRoutingModule_Factory(t) {\n      return new (t || UsersRoutingModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: UsersRoutingModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n  return UsersRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}