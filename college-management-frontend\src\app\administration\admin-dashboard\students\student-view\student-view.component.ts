import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { UserService } from '../../../../services/user.service';
import { AttendanceService } from '../../../../services/attendance.service';
import { User } from '../../../../models/user';
import Swal from 'sweetalert2';

interface AttendanceRecord {
  subject: {
    _id: string;
    subjectName: string;
    code: string;
  };
  class: {
    _id: string;
    className: string;
    section: string;
  };
  totalClasses: number;
  presentClasses: number;
  absentClasses: number;
  lateClasses: number;
  attendancePercentage: number;
  monthlyRecords?: any[];
  yearlyRecords?: any[];
}

@Component({
  selector: 'app-student-view',
  templateUrl: './student-view.component.html',
  styleUrls: ['./student-view.component.css']
})
export class StudentViewComponent implements OnInit {
  student: User | null = null;
  studentId: string | null = null;
  loading = false;
  attendanceLoading = false;
  
  // Attendance data
  attendanceRecords: AttendanceRecord[] = [];
  overallAttendance = 0;
  totalSubjects = 0;
  
  // View toggles
  showAttendanceDetails = false;
  selectedAttendanceView = 'daily'; // daily, monthly, yearly
  
  // Pagination for attendance
  currentPage = 1;
  itemsPerPage = 10;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private userService: UserService,
    private attendanceService: AttendanceService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.studentId = this.route.snapshot.paramMap.get('id');
    if (this.studentId) {
      this.loadStudentDetails();
    } else {
      Swal.fire('Error', 'Student ID not found', 'error');
      this.router.navigate(['/dashboard/admin/students']);
    }
  }

  loadStudentDetails(): void {
    if (!this.studentId) return;

    this.loading = true;
    this.userService.getUserProfile(this.studentId).subscribe({
      next: (response) => {
        this.loading = false;
        if (response.success) {
          this.student = response.user;
        } else {
          Swal.fire('Error', 'Failed to load student details', 'error');
          this.router.navigate(['/dashboard/admin/students']);
        }
      },
      error: (error) => {
        this.loading = false;
        console.error('Error loading student details:', error);
        Swal.fire('Error', 'Failed to load student details', 'error');
        this.router.navigate(['/dashboard/admin/students']);
      }
    });
  }

  loadStudentAttendance(): void {
    if (!this.studentId) return;

    this.attendanceLoading = true;
    this.attendanceService.getStudentAttendance(this.studentId).subscribe({
      next: (response) => {
        this.attendanceLoading = false;
        if (response.success) {
          this.processAttendanceData(response.attendance);
          this.calculateOverallAttendance();
        } else {
          this.snackBar.open('Failed to load attendance data', 'Close', { duration: 3000 });
        }
      },
      error: (error) => {
        this.attendanceLoading = false;
        console.error('Error loading attendance:', error);
        this.snackBar.open('Error loading attendance data', 'Close', { duration: 3000 });
      }
    });
  }

  processAttendanceData(attendanceData: any[]): void {
    // Group attendance by subject
    const subjectMap = new Map<string, any>();

    attendanceData.forEach(record => {
      const subjectId = record.subject._id;
      if (!subjectMap.has(subjectId)) {
        subjectMap.set(subjectId, {
          subject: record.subject,
          class: record.class,
          totalClasses: 0,
          presentClasses: 0,
          absentClasses: 0,
          lateClasses: 0,
          attendancePercentage: 0,
          records: []
        });
      }

      const subjectData = subjectMap.get(subjectId);
      subjectData.totalClasses++;
      subjectData.records.push(record);

      if (record.status === 'present') {
        subjectData.presentClasses++;
      } else if (record.status === 'absent') {
        subjectData.absentClasses++;
      } else if (record.status === 'late') {
        subjectData.lateClasses++;
        subjectData.presentClasses++; // Late is considered present
      }

      // Calculate percentage
      subjectData.attendancePercentage = Math.round(
        (subjectData.presentClasses / subjectData.totalClasses) * 100
      );
    });

    this.attendanceRecords = Array.from(subjectMap.values());
    this.totalSubjects = this.attendanceRecords.length;
  }

  calculateOverallAttendance(): void {
    if (this.attendanceRecords.length === 0) {
      this.overallAttendance = 0;
      return;
    }

    const totalPercentage = this.attendanceRecords.reduce(
      (sum, record) => sum + record.attendancePercentage, 0
    );
    this.overallAttendance = Math.round(totalPercentage / this.attendanceRecords.length);
  }

  toggleAttendanceDetails(): void {
    this.showAttendanceDetails = !this.showAttendanceDetails;
    if (this.showAttendanceDetails && this.attendanceRecords.length === 0) {
      this.loadStudentAttendance();
    }
  }

  editStudent(): void {
    if (this.studentId) {
      this.router.navigate(['/dashboard/admin/students/edit', this.studentId]);
    }
  }

  goBack(): void {
    this.router.navigate(['/dashboard/admin/students']);
  }

  getAttendanceClass(percentage: number): string {
    if (percentage >= 85) return 'excellent';
    if (percentage >= 75) return 'good';
    if (percentage >= 65) return 'average';
    return 'poor';
  }

  getClassSection(student: User): string {
    if (student?.classId && typeof student.classId === 'object') {
      return student.classId.section || '';
    }
    return '';
  }

  getClassName(student: User): string {
    if (student?.classId && typeof student.classId === 'object') {
      return student.classId.className || 'N/A';
    }
    return 'N/A';
  }

  get paginatedRecords(): AttendanceRecord[] {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    return this.attendanceRecords.slice(startIndex, startIndex + this.itemsPerPage);
  }

  get totalPages(): number {
    return Math.ceil(this.attendanceRecords.length / this.itemsPerPage);
  }

  changePage(page: number): void {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
    }
  }
}
