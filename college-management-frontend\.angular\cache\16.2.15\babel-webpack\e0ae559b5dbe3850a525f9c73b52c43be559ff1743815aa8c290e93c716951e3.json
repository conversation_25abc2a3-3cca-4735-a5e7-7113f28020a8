{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { TeacherAttendnaceComponent } from './teacher-attendnace/teacher-attendnace.component';\nimport { TeacherStudentsComponent } from './teacher-students/teacher-students.component';\nimport { TeacherAllStudentsComponent } from './teacher-all-students/teacher-all-students.component';\nimport { TeacherClassesComponent } from './teacher-classes/teacher-classes.component';\nimport { StudentDetailComponent } from './student-detail/student-detail.component';\nimport { TeacherNoticeComponent } from './teacher-notice/teacher-notice.component';\nimport { TeacherComplaintComponent } from './teacher-complaint/teacher-complaint.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'attendnace',\n  component: TeacherAttendnaceComponent\n}, {\n  path: 'class-students',\n  component: TeacherStudentsComponent\n}, {\n  path: 'all-students',\n  component: TeacherAllStudentsComponent\n}, {\n  path: 'classes',\n  component: TeacherClassesComponent\n}, {\n  path: 'student-detail',\n  component: StudentDetailComponent\n}, {\n  path: 'notice',\n  component: TeacherNoticeComponent\n}, {\n  path: 'complaint',\n  component: TeacherComplaintComponent\n}];\nexport let TeacherDashboardRoutingModule = /*#__PURE__*/(() => {\n  class TeacherDashboardRoutingModule {\n    static #_ = this.ɵfac = function TeacherDashboardRoutingModule_Factory(t) {\n      return new (t || TeacherDashboardRoutingModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: TeacherDashboardRoutingModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n  return TeacherDashboardRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}