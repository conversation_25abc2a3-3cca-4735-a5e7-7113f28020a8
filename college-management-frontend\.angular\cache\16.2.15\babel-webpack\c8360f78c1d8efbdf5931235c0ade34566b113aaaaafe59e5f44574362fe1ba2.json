{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/services/user.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"src/app/services/classes.service\";\nimport * as i5 from \"src/app/services/program.service\";\nimport * as i6 from \"src/app/services/department.service\";\nimport * as i7 from \"@angular/material/snack-bar\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/material/core\";\nimport * as i10 from \"@angular/material/button\";\nimport * as i11 from \"@angular/material/card\";\nimport * as i12 from \"@angular/material/icon\";\nimport * as i13 from \"@angular/material/input\";\nimport * as i14 from \"@angular/material/form-field\";\nimport * as i15 from \"@angular/material/progress-spinner\";\nimport * as i16 from \"@angular/material/select\";\nfunction AddStudentComponent_mat_error_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddStudentComponent_mat_error_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Name must be at least 2 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddStudentComponent_mat_error_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Father's name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddStudentComponent_mat_error_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Date of birth is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddStudentComponent_mat_error_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Address is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddStudentComponent_mat_error_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Contact number is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddStudentComponent_mat_error_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Please enter a valid phone number (10-15 digits)\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddStudentComponent_mat_error_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Father's contact is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddStudentComponent_mat_error_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Please enter a valid phone number (10-15 digits)\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddStudentComponent_mat_error_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Email is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddStudentComponent_mat_error_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Please enter a valid email address\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddStudentComponent_mat_form_field_78_mat_error_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Password is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddStudentComponent_mat_form_field_78_mat_error_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Password must be at least 6 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddStudentComponent_mat_form_field_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-form-field\", 5)(1, \"mat-label\");\n    i0.ɵɵtext(2, \"Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 33);\n    i0.ɵɵelementStart(4, \"mat-icon\", 7);\n    i0.ɵɵtext(5, \"lock\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, AddStudentComponent_mat_form_field_78_mat_error_6_Template, 2, 0, \"mat-error\", 8);\n    i0.ɵɵtemplate(7, AddStudentComponent_mat_form_field_78_mat_error_7_Template, 2, 0, \"mat-error\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.password == null ? null : ctx_r11.password.hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.password == null ? null : ctx_r11.password.hasError(\"minlength\"));\n  }\n}\nfunction AddStudentComponent_mat_error_93_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Roll number is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddStudentComponent_mat_error_100_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Registration number is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddStudentComponent_mat_option_108_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const program_r27 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", program_r27._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", program_r27.name, \" \");\n  }\n}\nfunction AddStudentComponent_mat_error_109_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Program is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddStudentComponent_mat_option_116_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dept_r28 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", dept_r28._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", dept_r28.name, \" \");\n  }\n}\nfunction AddStudentComponent_mat_error_117_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Department is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddStudentComponent_mat_option_125_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const cls_r29 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", cls_r29._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", cls_r29.className, \" - \", cls_r29.section, \" \");\n  }\n}\nfunction AddStudentComponent_mat_error_126_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Class is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddStudentComponent_mat_option_133_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const sem_r30 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", sem_r30.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", sem_r30.label, \" \");\n  }\n}\nfunction AddStudentComponent_mat_error_134_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r21.isIntermediateProgram() ? \"Year\" : \"Semester\", \" is required\");\n  }\n}\nfunction AddStudentComponent_mat_error_142_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Academic year is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddStudentComponent_div_156_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const control_r32 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", control_r32, \" \");\n  }\n}\nfunction AddStudentComponent_div_156_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"strong\");\n    i0.ɵɵtext(2, \"Form Status:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementStart(4, \"strong\");\n    i0.ɵɵtext(5, \"Loading:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementStart(7, \"strong\");\n    i0.ɵɵtext(8, \"Edit Mode:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9);\n    i0.ɵɵelement(10, \"br\");\n    i0.ɵɵelementStart(11, \"strong\");\n    i0.ɵɵtext(12, \"Invalid Fields:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, AddStudentComponent_div_156_span_13_Template, 2, 1, \"span\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r23.studentForm.valid ? \"Valid\" : \"Invalid\", \" | \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r23.loading, \" | \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r23.isEditMode, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r23.getInvalidControls());\n  }\n}\nfunction AddStudentComponent_mat_spinner_166_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 37);\n  }\n}\nexport class AddStudentComponent {\n  constructor(fb, userService, router, route, classService, programService, departmentService, snackBar) {\n    this.fb = fb;\n    this.userService = userService;\n    this.router = router;\n    this.route = route;\n    this.classService = classService;\n    this.programService = programService;\n    this.departmentService = departmentService;\n    this.snackBar = snackBar;\n    this.isEditMode = false;\n    this.studentId = null;\n    this.loading = false;\n    // Data arrays\n    this.programs = [];\n    this.departments = [];\n    this.classes = [];\n    this.filteredDepartments = [];\n    this.filteredClasses = [];\n    this.availableSemesters = [];\n    this.studentForm = this.createForm();\n  }\n  ngOnInit() {\n    this.loadInitialData();\n    this.checkEditMode();\n  }\n  createForm() {\n    return this.fb.group({\n      name: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(6)]],\n      rollNo: ['', [Validators.required]],\n      regNo: ['', [Validators.required]],\n      father_name: ['', [Validators.required]],\n      father_contact: ['', [Validators.required, Validators.pattern(/^[0-9]{10,15}$/)]],\n      contact: ['', [Validators.required, Validators.pattern(/^[0-9]{10,15}$/)]],\n      address: ['', [Validators.required]],\n      date_of_birth: ['', [Validators.required]],\n      school_last_attendance: [''],\n      year_of_passing: [''],\n      program: ['', [Validators.required]],\n      department: ['', [Validators.required]],\n      classId: ['', [Validators.required]],\n      semester: ['', [Validators.required, Validators.min(1)]],\n      academicYear: ['', [Validators.required]]\n    });\n  }\n  updatePasswordValidation() {\n    const passwordControl = this.studentForm.get('password');\n    if (this.isEditMode) {\n      // In edit mode, password is optional\n      passwordControl?.setValidators([Validators.minLength(6)]);\n    } else {\n      // In add mode, password is required\n      passwordControl?.setValidators([Validators.required, Validators.minLength(6)]);\n    }\n    passwordControl?.updateValueAndValidity();\n  }\n  loadInitialData() {\n    // Load programs\n    this.programService.getAllPrograms(true).subscribe({\n      next: response => {\n        if (response.success) {\n          this.programs = response.programs;\n          console.log('Programs loaded:', this.programs);\n        }\n      },\n      error: error => {\n        console.error('Error loading programs:', error);\n        this.showError('Failed to load programs');\n      }\n    });\n    // Load all departments (will be filtered by program selection)\n    this.departmentService.getAllDepartments().subscribe({\n      next: response => {\n        if (response.success) {\n          this.departments = response.departments;\n          console.log('Departments loaded:', this.departments);\n        }\n      },\n      error: error => {\n        console.error('Error loading departments:', error);\n        this.showError('Failed to load departments');\n      }\n    });\n    // Load all classes (will be filtered by program and department selection)\n    this.classService.getAllClasses().subscribe({\n      next: response => {\n        if (response.success) {\n          this.classes = response.classes;\n        }\n      },\n      error: () => {\n        this.showError('Failed to load classes');\n      }\n    });\n  }\n  checkEditMode() {\n    this.studentId = this.route.snapshot.paramMap.get('id');\n    if (this.studentId) {\n      this.isEditMode = true;\n      this.updatePasswordValidation();\n      this.loadStudentData();\n    }\n  }\n  loadStudentData() {\n    if (!this.studentId) return;\n    this.loading = true;\n    this.userService.getUserProfile(this.studentId).subscribe({\n      next: response => {\n        if (response.success) {\n          const student = response.user;\n          this.studentForm.patchValue({\n            name: student.name,\n            email: student.email,\n            rollNo: student.rollNo,\n            regNo: student.regNo,\n            father_name: student.father_name,\n            father_contact: student.father_contact,\n            contact: student.contact,\n            address: student.address,\n            date_of_birth: student.date_of_birth ? new Date(student.date_of_birth).toISOString().split('T')[0] : '',\n            school_last_attendance: student.school_last_attendance,\n            year_of_passing: student.year_of_passing,\n            program: student.program?._id || student.program,\n            department: student.department?._id || student.department,\n            classId: student.classId?._id || student.classId,\n            semester: student.semester,\n            academicYear: student.academicYear\n          });\n          // Trigger cascading updates\n          if (student.program) {\n            this.onProgramChange();\n          }\n          if (student.department) {\n            this.onDepartmentChange();\n          }\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading student:', error);\n        this.showError('Failed to load student data');\n        this.loading = false;\n      }\n    });\n  }\n  // Cascading dropdown handlers\n  onProgramChange() {\n    const programId = this.studentForm.get('program')?.value;\n    console.log('Program changed:', programId);\n    if (programId) {\n      // Load departments for the selected program\n      this.loadDepartmentsByProgram(programId);\n      // Reset dependent fields\n      this.studentForm.patchValue({\n        department: '',\n        classId: '',\n        semester: ''\n      });\n      this.filteredClasses = [];\n      this.availableSemesters = [];\n      // Handle program type logic for semester/year\n      const selectedProgram = this.programs.find(p => p._id === programId);\n      this.handleProgramTypeChange(selectedProgram);\n    } else {\n      this.filteredDepartments = [];\n      this.filteredClasses = [];\n      this.availableSemesters = [];\n    }\n  }\n  onDepartmentChange() {\n    const programId = this.studentForm.get('program')?.value;\n    const departmentId = this.studentForm.get('department')?.value;\n    console.log('Department changed:', departmentId, 'Program:', programId);\n    if (programId && departmentId) {\n      // Load classes for the selected program and department\n      this.loadClassesByProgramAndDepartment(programId, departmentId);\n      // Reset class selection\n      this.studentForm.patchValue({\n        classId: ''\n      });\n    } else {\n      this.filteredClasses = [];\n    }\n  }\n  // Load departments by program\n  loadDepartmentsByProgram(programId) {\n    console.log('Loading departments for program:', programId);\n    this.departmentService.getDepartmentsByProgram(programId, true).subscribe({\n      next: response => {\n        console.log('Departments response:', response);\n        if (response.success) {\n          this.filteredDepartments = response.departments;\n          console.log('Filtered departments:', this.filteredDepartments);\n        }\n      },\n      error: error => {\n        console.error('Error loading departments by program:', error);\n        this.showError('Failed to load departments');\n      }\n    });\n  }\n  // Load classes by program and department\n  loadClassesByProgramAndDepartment(programId, departmentId) {\n    this.classService.getClassesByProgramAndDepartment(programId, departmentId, true).subscribe({\n      next: response => {\n        if (response.success) {\n          this.filteredClasses = response.classes;\n        }\n      },\n      error: error => {\n        console.error('Error loading classes:', error);\n        this.showError('Failed to load classes');\n      }\n    });\n  }\n  // Handle program type change for semester/year logic\n  handleProgramTypeChange(program) {\n    if (!program) return;\n    if (program.name === 'Intermediate') {\n      // For Intermediate programs: Year-based (1st Year, 2nd Year)\n      this.availableSemesters = [{\n        value: 1,\n        label: '1st Year'\n      }, {\n        value: 2,\n        label: '2nd Year'\n      }];\n    } else {\n      // For BS/MS/PhD programs: Semester-based\n      const totalSemesters = program.totalSemesters || 8;\n      this.availableSemesters = [];\n      for (let i = 1; i <= totalSemesters; i++) {\n        this.availableSemesters.push({\n          value: i,\n          label: this.getOrdinalNumber(i) + ' Semester'\n        });\n      }\n    }\n  }\n  // Helper method to get ordinal numbers (1st, 2nd, 3rd, etc.)\n  getOrdinalNumber(num) {\n    const suffixes = ['th', 'st', 'nd', 'rd'];\n    const v = num % 100;\n    return num + (suffixes[(v - 20) % 10] || suffixes[v] || suffixes[0]);\n  }\n  // Check if current program is Intermediate\n  isIntermediateProgram() {\n    const programId = this.studentForm.get('program')?.value;\n    const selectedProgram = this.programs.find(p => p._id === programId);\n    return selectedProgram?.name === 'Intermediate';\n  }\n  onSave() {\n    if (this.studentForm.invalid) {\n      this.markFormGroupTouched();\n      this.logFormErrors();\n      this.showError('Please fill in all required fields correctly');\n      return;\n    }\n    this.loading = true;\n    const formData = this.studentForm.value;\n    const studentData = {\n      ...formData,\n      role: 'Student'\n    };\n    // Remove password field for edit mode\n    if (this.isEditMode) {\n      delete studentData.password;\n    }\n    const operation = this.isEditMode ? this.userService.updateUserProfile(this.studentId, studentData) : this.userService.register(studentData);\n    operation.subscribe({\n      next: () => {\n        this.loading = false;\n        const message = this.isEditMode ? 'Student updated successfully!' : 'Student registered successfully!';\n        this.showSuccess(message);\n        this.router.navigate(['/dashboard/admin/students/student-list']);\n      },\n      error: error => {\n        this.loading = false;\n        console.error('Error saving student:', error);\n        this.showError(error.error?.message || 'Failed to save student');\n      }\n    });\n  }\n  onCancel() {\n    this.router.navigate(['/dashboard/admin/students/student-list']);\n  }\n  // Helper methods\n  markFormGroupTouched() {\n    Object.keys(this.studentForm.controls).forEach(key => {\n      this.studentForm.get(key)?.markAsTouched();\n    });\n  }\n  logFormErrors() {\n    // Form validation errors - only log in development\n    if (!environment.production) {\n      console.log('Form is invalid. Checking individual field errors:');\n      Object.keys(this.studentForm.controls).forEach(key => {\n        const control = this.studentForm.get(key);\n        if (control && control.invalid) {\n          console.log(`${key}:`, control.errors);\n        }\n      });\n    }\n  }\n  showSuccess(message) {\n    this.snackBar.open(message, 'Close', {\n      duration: 3000,\n      panelClass: ['success-snackbar']\n    });\n  }\n  showError(message) {\n    this.snackBar.open(message, 'Close', {\n      duration: 5000,\n      panelClass: ['error-snackbar']\n    });\n  }\n  // Getters for form validation\n  get name() {\n    return this.studentForm.get('name');\n  }\n  get email() {\n    return this.studentForm.get('email');\n  }\n  get password() {\n    return this.studentForm.get('password');\n  }\n  get rollNo() {\n    return this.studentForm.get('rollNo');\n  }\n  get regNo() {\n    return this.studentForm.get('regNo');\n  }\n  get father_name() {\n    return this.studentForm.get('father_name');\n  }\n  get father_contact() {\n    return this.studentForm.get('father_contact');\n  }\n  get contact() {\n    return this.studentForm.get('contact');\n  }\n  get address() {\n    return this.studentForm.get('address');\n  }\n  get date_of_birth() {\n    return this.studentForm.get('date_of_birth');\n  }\n  get program() {\n    return this.studentForm.get('program');\n  }\n  get department() {\n    return this.studentForm.get('department');\n  }\n  get classId() {\n    return this.studentForm.get('classId');\n  }\n  get semester() {\n    return this.studentForm.get('semester');\n  }\n  get academicYear() {\n    return this.studentForm.get('academicYear');\n  }\n  // Check if form is valid for submission\n  get isFormValid() {\n    return this.studentForm.valid;\n  }\n  // Get list of invalid controls for debugging\n  getInvalidControls() {\n    const invalidControls = [];\n    Object.keys(this.studentForm.controls).forEach(key => {\n      const control = this.studentForm.get(key);\n      if (control && control.invalid) {\n        invalidControls.push(key);\n      }\n    });\n    return invalidControls;\n  }\n  static #_ = this.ɵfac = function AddStudentComponent_Factory(t) {\n    return new (t || AddStudentComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.ClassesService), i0.ɵɵdirectiveInject(i5.ProgramService), i0.ɵɵdirectiveInject(i6.DepartmentService), i0.ɵɵdirectiveInject(i7.MatSnackBar));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AddStudentComponent,\n    selectors: [[\"app-add-student\"]],\n    decls: 167,\n    vars: 36,\n    consts: [[1, \"form-container\"], [1, \"form-header\"], [1, \"student-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"form-card\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"name\", \"placeholder\", \"Enter full name\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"father_name\", \"placeholder\", \"Enter father's full name\"], [\"matInput\", \"\", \"type\", \"date\", \"formControlName\", \"date_of_birth\"], [\"matInput\", \"\", \"formControlName\", \"address\", \"rows\", \"3\", \"placeholder\", \"Enter complete address\"], [\"matInput\", \"\", \"formControlName\", \"contact\", \"placeholder\", \"Enter student's mobile number\"], [\"matInput\", \"\", \"formControlName\", \"father_contact\", \"placeholder\", \"Enter father's mobile number\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter email address\"], [\"appearance\", \"outline\", \"class\", \"full-width\", 4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"rollNo\", \"placeholder\", \"Enter roll number\"], [\"matInput\", \"\", \"formControlName\", \"regNo\", \"placeholder\", \"Enter registration number\"], [\"formControlName\", \"program\", 3, \"selectionChange\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"formControlName\", \"department\", 3, \"disabled\", \"selectionChange\"], [\"formControlName\", \"classId\", 3, \"disabled\"], [\"formControlName\", \"semester\", 3, \"disabled\"], [\"matInput\", \"\", \"formControlName\", \"academicYear\", \"placeholder\", \"e.g., 2024-2025\"], [\"matInput\", \"\", \"formControlName\", \"school_last_attendance\", \"placeholder\", \"Enter previous school name\"], [\"appearance\", \"outline\", 1, \"half-width\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"year_of_passing\", \"placeholder\", \"Enter year of passing\", \"min\", \"1990\", \"max\", \"2030\"], [\"class\", \"debug-info\", \"style\", \"background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px; font-size: 12px;\", 4, \"ngIf\"], [1, \"form-actions\"], [\"mat-raised-button\", \"\", \"type\", \"button\", \"color\", \"warn\", 1, \"action-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"type\", \"submit\", \"color\", \"primary\", 1, \"action-btn\", 3, \"disabled\"], [\"diameter\", \"20\", \"class\", \"spinner\", 4, \"ngIf\"], [\"matInput\", \"\", \"type\", \"password\", \"formControlName\", \"password\", \"placeholder\", \"Enter password\"], [3, \"value\"], [1, \"debug-info\", 2, \"background\", \"#f5f5f5\", \"padding\", \"10px\", \"margin\", \"10px 0\", \"border-radius\", \"4px\", \"font-size\", \"12px\"], [4, \"ngFor\", \"ngForOf\"], [\"diameter\", \"20\", 1, \"spinner\"]],\n    template: function AddStudentComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\")(3, \"mat-icon\");\n        i0.ɵɵtext(4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(5);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(6, \"form\", 2);\n        i0.ɵɵlistener(\"ngSubmit\", function AddStudentComponent_Template_form_ngSubmit_6_listener() {\n          return ctx.onSave();\n        });\n        i0.ɵɵelementStart(7, \"mat-card\", 3)(8, \"mat-card-header\")(9, \"mat-card-title\")(10, \"mat-icon\");\n        i0.ɵɵtext(11, \"person\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(12, \" Personal Information \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(13, \"mat-card-content\")(14, \"div\", 4)(15, \"mat-form-field\", 5)(16, \"mat-label\");\n        i0.ɵɵtext(17, \"Student Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(18, \"input\", 6);\n        i0.ɵɵelementStart(19, \"mat-icon\", 7);\n        i0.ɵɵtext(20, \"person\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(21, AddStudentComponent_mat_error_21_Template, 2, 0, \"mat-error\", 8);\n        i0.ɵɵtemplate(22, AddStudentComponent_mat_error_22_Template, 2, 0, \"mat-error\", 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(23, \"mat-form-field\", 5)(24, \"mat-label\");\n        i0.ɵɵtext(25, \"Father's Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(26, \"input\", 9);\n        i0.ɵɵelementStart(27, \"mat-icon\", 7);\n        i0.ɵɵtext(28, \"family_restroom\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(29, AddStudentComponent_mat_error_29_Template, 2, 0, \"mat-error\", 8);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(30, \"div\", 4)(31, \"mat-form-field\", 5)(32, \"mat-label\");\n        i0.ɵɵtext(33, \"Date of Birth\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(34, \"input\", 10);\n        i0.ɵɵelementStart(35, \"mat-icon\", 7);\n        i0.ɵɵtext(36, \"calendar_today\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(37, AddStudentComponent_mat_error_37_Template, 2, 0, \"mat-error\", 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(38, \"mat-form-field\", 5)(39, \"mat-label\");\n        i0.ɵɵtext(40, \"Address\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(41, \"textarea\", 11);\n        i0.ɵɵelementStart(42, \"mat-icon\", 7);\n        i0.ɵɵtext(43, \"location_on\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(44, AddStudentComponent_mat_error_44_Template, 2, 0, \"mat-error\", 8);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(45, \"mat-card\", 3)(46, \"mat-card-header\")(47, \"mat-card-title\")(48, \"mat-icon\");\n        i0.ɵɵtext(49, \"contact_phone\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(50, \" Contact Information \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(51, \"mat-card-content\")(52, \"div\", 4)(53, \"mat-form-field\", 5)(54, \"mat-label\");\n        i0.ɵɵtext(55, \"Student Contact Number\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(56, \"input\", 12);\n        i0.ɵɵelementStart(57, \"mat-icon\", 7);\n        i0.ɵɵtext(58, \"phone\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(59, AddStudentComponent_mat_error_59_Template, 2, 0, \"mat-error\", 8);\n        i0.ɵɵtemplate(60, AddStudentComponent_mat_error_60_Template, 2, 0, \"mat-error\", 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(61, \"mat-form-field\", 5)(62, \"mat-label\");\n        i0.ɵɵtext(63, \"Father's Contact Number\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(64, \"input\", 13);\n        i0.ɵɵelementStart(65, \"mat-icon\", 7);\n        i0.ɵɵtext(66, \"phone\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(67, AddStudentComponent_mat_error_67_Template, 2, 0, \"mat-error\", 8);\n        i0.ɵɵtemplate(68, AddStudentComponent_mat_error_68_Template, 2, 0, \"mat-error\", 8);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(69, \"div\", 4)(70, \"mat-form-field\", 5)(71, \"mat-label\");\n        i0.ɵɵtext(72, \"Email Address\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(73, \"input\", 14);\n        i0.ɵɵelementStart(74, \"mat-icon\", 7);\n        i0.ɵɵtext(75, \"email\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(76, AddStudentComponent_mat_error_76_Template, 2, 0, \"mat-error\", 8);\n        i0.ɵɵtemplate(77, AddStudentComponent_mat_error_77_Template, 2, 0, \"mat-error\", 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(78, AddStudentComponent_mat_form_field_78_Template, 8, 2, \"mat-form-field\", 15);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(79, \"mat-card\", 3)(80, \"mat-card-header\")(81, \"mat-card-title\")(82, \"mat-icon\");\n        i0.ɵɵtext(83, \"school\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(84, \" Academic Information \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(85, \"mat-card-content\")(86, \"div\", 4)(87, \"mat-form-field\", 5)(88, \"mat-label\");\n        i0.ɵɵtext(89, \"Roll Number\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(90, \"input\", 16);\n        i0.ɵɵelementStart(91, \"mat-icon\", 7);\n        i0.ɵɵtext(92, \"badge\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(93, AddStudentComponent_mat_error_93_Template, 2, 0, \"mat-error\", 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(94, \"mat-form-field\", 5)(95, \"mat-label\");\n        i0.ɵɵtext(96, \"Registration Number\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(97, \"input\", 17);\n        i0.ɵɵelementStart(98, \"mat-icon\", 7);\n        i0.ɵɵtext(99, \"assignment\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(100, AddStudentComponent_mat_error_100_Template, 2, 0, \"mat-error\", 8);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(101, \"div\", 4)(102, \"mat-form-field\", 5)(103, \"mat-label\");\n        i0.ɵɵtext(104, \"Program\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(105, \"mat-select\", 18);\n        i0.ɵɵlistener(\"selectionChange\", function AddStudentComponent_Template_mat_select_selectionChange_105_listener() {\n          return ctx.onProgramChange();\n        });\n        i0.ɵɵelementStart(106, \"mat-option\", 19);\n        i0.ɵɵtext(107, \"Select Program\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(108, AddStudentComponent_mat_option_108_Template, 2, 2, \"mat-option\", 20);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(109, AddStudentComponent_mat_error_109_Template, 2, 0, \"mat-error\", 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(110, \"mat-form-field\", 5)(111, \"mat-label\");\n        i0.ɵɵtext(112, \"Department\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(113, \"mat-select\", 21);\n        i0.ɵɵlistener(\"selectionChange\", function AddStudentComponent_Template_mat_select_selectionChange_113_listener() {\n          return ctx.onDepartmentChange();\n        });\n        i0.ɵɵelementStart(114, \"mat-option\", 19);\n        i0.ɵɵtext(115, \"Select Department\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(116, AddStudentComponent_mat_option_116_Template, 2, 2, \"mat-option\", 20);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(117, AddStudentComponent_mat_error_117_Template, 2, 0, \"mat-error\", 8);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(118, \"div\", 4)(119, \"mat-form-field\", 5)(120, \"mat-label\");\n        i0.ɵɵtext(121, \"Class\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(122, \"mat-select\", 22)(123, \"mat-option\", 19);\n        i0.ɵɵtext(124, \"Select Class\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(125, AddStudentComponent_mat_option_125_Template, 2, 3, \"mat-option\", 20);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(126, AddStudentComponent_mat_error_126_Template, 2, 0, \"mat-error\", 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(127, \"mat-form-field\", 5)(128, \"mat-label\");\n        i0.ɵɵtext(129);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(130, \"mat-select\", 23)(131, \"mat-option\", 19);\n        i0.ɵɵtext(132);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(133, AddStudentComponent_mat_option_133_Template, 2, 2, \"mat-option\", 20);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(134, AddStudentComponent_mat_error_134_Template, 2, 1, \"mat-error\", 8);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(135, \"div\", 4)(136, \"mat-form-field\", 5)(137, \"mat-label\");\n        i0.ɵɵtext(138, \"Academic Year\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(139, \"input\", 24);\n        i0.ɵɵelementStart(140, \"mat-icon\", 7);\n        i0.ɵɵtext(141, \"calendar_today\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(142, AddStudentComponent_mat_error_142_Template, 2, 0, \"mat-error\", 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(143, \"mat-form-field\", 5)(144, \"mat-label\");\n        i0.ɵɵtext(145, \"School Last Attended\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(146, \"input\", 25);\n        i0.ɵɵelementStart(147, \"mat-icon\", 7);\n        i0.ɵɵtext(148, \"school\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(149, \"div\", 4)(150, \"mat-form-field\", 26)(151, \"mat-label\");\n        i0.ɵɵtext(152, \"Year of Passing\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(153, \"input\", 27);\n        i0.ɵɵelementStart(154, \"mat-icon\", 7);\n        i0.ɵɵtext(155, \"event\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵtemplate(156, AddStudentComponent_div_156_Template, 14, 4, \"div\", 28);\n        i0.ɵɵelementStart(157, \"div\", 29)(158, \"button\", 30);\n        i0.ɵɵlistener(\"click\", function AddStudentComponent_Template_button_click_158_listener() {\n          return ctx.onCancel();\n        });\n        i0.ɵɵelementStart(159, \"mat-icon\");\n        i0.ɵɵtext(160, \"cancel\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(161, \" Cancel \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(162, \"button\", 31)(163, \"mat-icon\");\n        i0.ɵɵtext(164);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(165);\n        i0.ɵɵtemplate(166, AddStudentComponent_mat_spinner_166_Template, 1, 0, \"mat-spinner\", 32);\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(ctx.isEditMode ? \"edit\" : \"person_add\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Edit Student\" : \"Add New Student\", \" \");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"formGroup\", ctx.studentForm);\n        i0.ɵɵadvance(15);\n        i0.ɵɵproperty(\"ngIf\", ctx.name == null ? null : ctx.name.hasError(\"required\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.name == null ? null : ctx.name.hasError(\"minlength\"));\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngIf\", ctx.father_name == null ? null : ctx.father_name.hasError(\"required\"));\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"ngIf\", ctx.date_of_birth == null ? null : ctx.date_of_birth.hasError(\"required\"));\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngIf\", ctx.address == null ? null : ctx.address.hasError(\"required\"));\n        i0.ɵɵadvance(15);\n        i0.ɵɵproperty(\"ngIf\", ctx.contact == null ? null : ctx.contact.hasError(\"required\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.contact == null ? null : ctx.contact.hasError(\"pattern\"));\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngIf\", ctx.father_contact == null ? null : ctx.father_contact.hasError(\"required\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.father_contact == null ? null : ctx.father_contact.hasError(\"pattern\"));\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"ngIf\", ctx.email == null ? null : ctx.email.hasError(\"required\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.email == null ? null : ctx.email.hasError(\"email\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isEditMode);\n        i0.ɵɵadvance(15);\n        i0.ɵɵproperty(\"ngIf\", ctx.rollNo == null ? null : ctx.rollNo.hasError(\"required\"));\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngIf\", ctx.regNo == null ? null : ctx.regNo.hasError(\"required\"));\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"ngForOf\", ctx.programs);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.program == null ? null : ctx.program.hasError(\"required\"));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"disabled\", !(ctx.program == null ? null : ctx.program.value));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.filteredDepartments);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.department == null ? null : ctx.department.hasError(\"required\"));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"disabled\", !(ctx.department == null ? null : ctx.department.value));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.filteredClasses);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.classId == null ? null : ctx.classId.hasError(\"required\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.isIntermediateProgram() ? \"Academic Year\" : \"Semester\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"disabled\", !(ctx.program == null ? null : ctx.program.value) || ctx.availableSemesters.length === 0);\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.isIntermediateProgram() ? \"Select Year\" : \"Select Semester\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngForOf\", ctx.availableSemesters);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.semester == null ? null : ctx.semester.hasError(\"required\"));\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"ngIf\", ctx.academicYear == null ? null : ctx.academicYear.hasError(\"required\"));\n        i0.ɵɵadvance(14);\n        i0.ɵɵproperty(\"ngIf\", false);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"disabled\", !ctx.isFormValid || ctx.loading);\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.isEditMode ? \"update\" : \"save\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Update Student\" : \"Save Student\", \" \");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n      }\n    },\n    dependencies: [i8.NgForOf, i8.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName, i9.MatOption, i10.MatButton, i11.MatCard, i11.MatCardContent, i11.MatCardHeader, i11.MatCardTitle, i12.MatIcon, i13.MatInput, i14.MatFormField, i14.MatLabel, i14.MatError, i14.MatSuffix, i15.MatProgressSpinner, i16.MatSelect],\n    styles: [\"\\n\\n.form-container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 32px;\\n  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);\\n  border-radius: 16px;\\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);\\n  border: 1px solid #e2e8f0;\\n}\\n\\n.form-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 40px;\\n  padding-bottom: 20px;\\n  border-bottom: 2px solid #e2e8f0;\\n}\\n\\n.form-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  color: #11418e;\\n  margin: 0;\\n  font-size: 28px;\\n  font-weight: 700;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 10px;\\n  width: 100%;\\n  text-align: center;\\n}\\n\\n\\n\\n.student-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n}\\n\\n.form-card[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  border-radius: 12px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\\n}\\n\\n.form-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  padding-bottom: 15px;\\n  border-bottom: 1px solid #e2e8f0;\\n}\\n\\n.form-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  color: #2c3e50;\\n  font-weight: 600;\\n  font-size: 18px;\\n}\\n\\n.form-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n  margin-bottom: 10px;\\n}\\n\\n.form-row[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.half-width[_ngcontent-%COMP%] {\\n  width: 48%;\\n}\\n\\n.form-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n  gap: 32px;\\n  margin-bottom: 32px;\\n}\\n\\n.form-section[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  padding: 24px;\\n  border-radius: 12px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\\n  border: 1px solid #f1f5f9;\\n  transition: all 0.3s ease;\\n}\\n\\n.form-section[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);\\n  transform: translateY(-2px);\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  color: #1e293b;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin-bottom: 20px;\\n  padding-bottom: 12px;\\n  border-bottom: 2px solid #f1f5f9;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.section-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #11418e;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 8px;\\n  color: #374151;\\n  font-weight: 600;\\n  font-size: 14px;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #6b7280;\\n  margin-right: 6px;\\n}\\n\\n.required[_ngcontent-%COMP%] {\\n  color: #ef4444;\\n  font-weight: 500;\\n  margin-left: 4px;\\n}\\n\\n.optional[_ngcontent-%COMP%] {\\n  color: #6b7280;\\n  font-weight: 400;\\n  font-size: 12px;\\n  margin-left: 4px;\\n}\\n\\n.form-control[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 12px 16px;\\n  border: 2px solid #e5e7eb;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  transition: all 0.3s ease;\\n  background-color: #ffffff;\\n}\\n\\n.form-control[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #11418e;\\n  box-shadow: 0 0 0 3px rgba(17, 65, 142, 0.1);\\n  background-color: #fafbfc;\\n}\\n\\n.form-control[_ngcontent-%COMP%]:hover {\\n  border-color: #9ca3af;\\n}\\n\\n.form-control[_ngcontent-%COMP%]::placeholder {\\n  color: #9ca3af;\\n  font-style: italic;\\n}\\n\\ntextarea.form-control[_ngcontent-%COMP%] {\\n  resize: vertical;\\n  min-height: 80px;\\n}\\n\\nselect.form-control[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n\\n.form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 16px;\\n  padding-top: 24px;\\n  border-top: 2px solid #f1f5f9;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 12px 24px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  font-size: 14px;\\n  border: none;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  text-decoration: none;\\n}\\n\\n.btn-save[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #11418e 0%, #1e40af 100%);\\n  color: white;\\n  box-shadow: 0 4px 12px rgba(17, 65, 142, 0.3);\\n}\\n\\n.btn-save[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #0f3a7a 0%, #1d4ed8 100%);\\n  box-shadow: 0 6px 16px rgba(17, 65, 142, 0.4);\\n  transform: translateY(-2px);\\n}\\n\\n.btn-back[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  color: #11418e;\\n  border: 2px solid #11418e;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.btn-back[_ngcontent-%COMP%]:hover {\\n  background: #11418e;\\n  color: white;\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(17, 65, 142, 0.3);\\n}\\n\\n\\n\\n@media screen and (max-width: 768px) {\\n  .form-container[_ngcontent-%COMP%] {\\n    padding: 20px;\\n    margin: 10px;\\n  }\\n\\n  .form-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 20px;\\n  }\\n\\n  .form-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n    font-size: 24px;\\n  }\\n\\n  .form-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n\\n  .btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n}\\n\\n@media screen and (max-width: 480px) {\\n  .form-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n    margin: 8px;\\n  }\\n\\n  .form-section[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n}\\n\\n\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  min-width: 150px;\\n  height: 45px;\\n  font-size: 16px;\\n  font-weight: 500;\\n  border-radius: 8px;\\n}\\n\\n.spinner[_ngcontent-%COMP%] {\\n  margin-left: 10px;\\n}\\n\\n\\n\\n.mat-form-field-appearance-outline[_ngcontent-%COMP%]   .mat-form-field-outline[_ngcontent-%COMP%] {\\n  color: #ddd;\\n}\\n\\n.mat-form-field-appearance-outline.mat-focused[_ngcontent-%COMP%]   .mat-form-field-outline-thick[_ngcontent-%COMP%] {\\n  color: #11418e;\\n}\\n\\n.mat-form-field-appearance-outline[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n\\n.mat-form-field-appearance-outline.mat-focused[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%] {\\n  color: #11418e;\\n}\\n\\n.mat-form-field-appearance-outline[_ngcontent-%COMP%]   .mat-form-field-suffix[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n\\n.mat-form-field-appearance-outline.mat-focused[_ngcontent-%COMP%]   .mat-form-field-suffix[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #11418e;\\n}\\n\\n\\n\\n.mat-form-field.mat-form-field-invalid[_ngcontent-%COMP%]   .mat-form-field-outline-thick[_ngcontent-%COMP%] {\\n  color: #f44336;\\n}\\n\\n.mat-form-field.mat-form-field-invalid[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%] {\\n  color: #f44336;\\n}\\n\\n\\n\\n.mat-form-field.mat-form-field-disabled[_ngcontent-%COMP%]   .mat-form-field-outline[_ngcontent-%COMP%] {\\n  color: #ccc;\\n}\\n\\n.mat-form-field.mat-form-field-disabled[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%] {\\n  color: #999;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "environment", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵtemplate", "AddStudentComponent_mat_form_field_78_mat_error_6_Template", "AddStudentComponent_mat_form_field_78_mat_error_7_Template", "ɵɵadvance", "ɵɵproperty", "ctx_r11", "password", "<PERSON><PERSON><PERSON><PERSON>", "program_r27", "_id", "ɵɵtextInterpolate1", "name", "dept_r28", "cls_r29", "ɵɵtextInterpolate2", "className", "section", "sem_r30", "value", "label", "ctx_r21", "isIntermediateProgram", "control_r32", "AddStudentComponent_div_156_span_13_Template", "ctx_r23", "studentForm", "valid", "loading", "isEditMode", "getInvalidControls", "AddStudentComponent", "constructor", "fb", "userService", "router", "route", "classService", "programService", "departmentService", "snackBar", "studentId", "programs", "departments", "classes", "filteredDepartments", "filteredClasses", "availableSemesters", "createForm", "ngOnInit", "loadInitialData", "checkEditMode", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "email", "rollNo", "regNo", "father_name", "father_contact", "pattern", "contact", "address", "date_of_birth", "school_last_attendance", "year_of_passing", "program", "department", "classId", "semester", "min", "academicYear", "updatePasswordValidation", "passwordControl", "get", "setValidators", "updateValueAndValidity", "getAllPrograms", "subscribe", "next", "response", "success", "console", "log", "error", "showError", "getAllDepartments", "getAllClasses", "snapshot", "paramMap", "loadStudentData", "getUserProfile", "student", "user", "patchValue", "Date", "toISOString", "split", "onProgramChange", "onDepartmentChange", "programId", "loadDepartmentsByProgram", "selectedProgram", "find", "p", "handleProgramTypeChange", "departmentId", "loadClassesByProgramAndDepartment", "getDepartmentsByProgram", "getClassesByProgramAndDepartment", "totalSemesters", "i", "push", "getOrdinalNumber", "num", "suffixes", "v", "onSave", "invalid", "markFormGroupTouched", "logFormErrors", "formData", "studentData", "role", "operation", "updateUserProfile", "register", "message", "showSuccess", "navigate", "onCancel", "Object", "keys", "controls", "for<PERSON>ach", "key", "<PERSON><PERSON><PERSON><PERSON>ched", "production", "control", "errors", "open", "duration", "panelClass", "isFormValid", "invalidControls", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "UserService", "i3", "Router", "ActivatedRoute", "i4", "ClassesService", "i5", "ProgramService", "i6", "DepartmentService", "i7", "MatSnackBar", "_2", "selectors", "decls", "vars", "consts", "template", "AddStudentComponent_Template", "rf", "ctx", "ɵɵlistener", "AddStudentComponent_Template_form_ngSubmit_6_listener", "AddStudentComponent_mat_error_21_Template", "AddStudentComponent_mat_error_22_Template", "AddStudentComponent_mat_error_29_Template", "AddStudentComponent_mat_error_37_Template", "AddStudentComponent_mat_error_44_Template", "AddStudentComponent_mat_error_59_Template", "AddStudentComponent_mat_error_60_Template", "AddStudentComponent_mat_error_67_Template", "AddStudentComponent_mat_error_68_Template", "AddStudentComponent_mat_error_76_Template", "AddStudentComponent_mat_error_77_Template", "AddStudentComponent_mat_form_field_78_Template", "AddStudentComponent_mat_error_93_Template", "AddStudentComponent_mat_error_100_Template", "AddStudentComponent_Template_mat_select_selectionChange_105_listener", "AddStudentComponent_mat_option_108_Template", "AddStudentComponent_mat_error_109_Template", "AddStudentComponent_Template_mat_select_selectionChange_113_listener", "AddStudentComponent_mat_option_116_Template", "AddStudentComponent_mat_error_117_Template", "AddStudentComponent_mat_option_125_Template", "AddStudentComponent_mat_error_126_Template", "AddStudentComponent_mat_option_133_Template", "AddStudentComponent_mat_error_134_Template", "AddStudentComponent_mat_error_142_Template", "AddStudentComponent_div_156_Template", "AddStudentComponent_Template_button_click_158_listener", "AddStudentComponent_mat_spinner_166_Template", "ɵɵtextInterpolate", "length"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\students\\add-student\\add-student.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\students\\add-student\\add-student.component.html"], "sourcesContent": ["import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';\r\nimport { Component, ElementRef, ViewChild, OnInit } from '@angular/core';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { ClassesService } from 'src/app/services/classes.service';\r\nimport { UserService } from 'src/app/services/user.service';\r\nimport { ProgramService } from 'src/app/services/program.service';\r\nimport { DepartmentService } from 'src/app/services/department.service';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { Program, Department, Class } from 'src/app/models/user';\r\nimport { environment } from 'src/environments/environment';\r\n\r\n@Component({\r\n  selector: 'app-add-student',\r\n  templateUrl: './add-student.component.html',\r\n  styleUrls: ['./add-student.component.css']\r\n})\r\nexport class AddStudentComponent implements OnInit {\r\n  studentForm: FormGroup;\r\n  isEditMode = false;\r\n  studentId: string | null = null;\r\n  loading = false;\r\n\r\n  // Data arrays\r\n  programs: Program[] = [];\r\n  departments: Department[] = [];\r\n  classes: Class[] = [];\r\n  filteredDepartments: Department[] = [];\r\n  filteredClasses: Class[] = [];\r\n  availableSemesters: { value: number; label: string }[] = [];\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private userService: UserService,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private classService: ClassesService,\r\n    private programService: ProgramService,\r\n    private departmentService: DepartmentService,\r\n    private snackBar: MatSnackBar\r\n  ) {\r\n    this.studentForm = this.createForm();\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.loadInitialData();\r\n    this.checkEditMode();\r\n  }\r\n\r\n  createForm(): FormGroup {\r\n    return this.fb.group({\r\n      name: ['', [Validators.required, Validators.minLength(2)]],\r\n      email: ['', [Validators.required, Validators.email]],\r\n      password: ['', [Validators.required, Validators.minLength(6)]],\r\n      rollNo: ['', [Validators.required]],\r\n      regNo: ['', [Validators.required]],\r\n      father_name: ['', [Validators.required]],\r\n      father_contact: ['', [Validators.required, Validators.pattern(/^[0-9]{10,15}$/)]],\r\n      contact: ['', [Validators.required, Validators.pattern(/^[0-9]{10,15}$/)]],\r\n      address: ['', [Validators.required]],\r\n      date_of_birth: ['', [Validators.required]],\r\n      school_last_attendance: [''],\r\n      year_of_passing: [''],\r\n      program: ['', [Validators.required]],\r\n      department: ['', [Validators.required]],\r\n      classId: ['', [Validators.required]],\r\n      semester: ['', [Validators.required, Validators.min(1)]],\r\n      academicYear: ['', [Validators.required]]\r\n    });\r\n  }\r\n\r\n  updatePasswordValidation(): void {\r\n    const passwordControl = this.studentForm.get('password');\r\n    if (this.isEditMode) {\r\n      // In edit mode, password is optional\r\n      passwordControl?.setValidators([Validators.minLength(6)]);\r\n    } else {\r\n      // In add mode, password is required\r\n      passwordControl?.setValidators([Validators.required, Validators.minLength(6)]);\r\n    }\r\n    passwordControl?.updateValueAndValidity();\r\n  }\r\n  loadInitialData(): void {\r\n    // Load programs\r\n    this.programService.getAllPrograms(true).subscribe({\r\n      next: (response: any) => {\r\n        if (response.success) {\r\n          this.programs = response.programs;\r\n          console.log('Programs loaded:', this.programs);\r\n        }\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Error loading programs:', error);\r\n        this.showError('Failed to load programs');\r\n      }\r\n    });\r\n\r\n    // Load all departments (will be filtered by program selection)\r\n    this.departmentService.getAllDepartments().subscribe({\r\n      next: (response: any) => {\r\n        if (response.success) {\r\n          this.departments = response.departments;\r\n          console.log('Departments loaded:', this.departments);\r\n        }\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Error loading departments:', error);\r\n        this.showError('Failed to load departments');\r\n      }\r\n    });\r\n\r\n    // Load all classes (will be filtered by program and department selection)\r\n    this.classService.getAllClasses().subscribe({\r\n      next: (response: any) => {\r\n        if (response.success) {\r\n          this.classes = response.classes;\r\n        }\r\n      },\r\n      error: () => {\r\n        this.showError('Failed to load classes');\r\n      }\r\n    });\r\n  }\r\n\r\n  checkEditMode(): void {\r\n    this.studentId = this.route.snapshot.paramMap.get('id');\r\n    if (this.studentId) {\r\n      this.isEditMode = true;\r\n      this.updatePasswordValidation();\r\n      this.loadStudentData();\r\n    }\r\n  }\r\n\r\n  loadStudentData(): void {\r\n    if (!this.studentId) return;\r\n\r\n    this.loading = true;\r\n    this.userService.getUserProfile(this.studentId).subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          const student = response.user;\r\n          this.studentForm.patchValue({\r\n            name: student.name,\r\n            email: student.email,\r\n            rollNo: student.rollNo,\r\n            regNo: student.regNo,\r\n            father_name: student.father_name,\r\n            father_contact: student.father_contact,\r\n            contact: student.contact,\r\n            address: student.address,\r\n            date_of_birth: student.date_of_birth ? new Date(student.date_of_birth).toISOString().split('T')[0] : '',\r\n            school_last_attendance: student.school_last_attendance,\r\n            year_of_passing: student.year_of_passing,\r\n            program: student.program?._id || student.program,\r\n            department: student.department?._id || student.department,\r\n            classId: student.classId?._id || student.classId,\r\n            semester: student.semester,\r\n            academicYear: student.academicYear\r\n          });\r\n\r\n          // Trigger cascading updates\r\n          if (student.program) {\r\n            this.onProgramChange();\r\n          }\r\n          if (student.department) {\r\n            this.onDepartmentChange();\r\n          }\r\n        }\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading student:', error);\r\n        this.showError('Failed to load student data');\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n  // Cascading dropdown handlers\r\n  onProgramChange(): void {\r\n    const programId = this.studentForm.get('program')?.value;\r\n    console.log('Program changed:', programId);\r\n    if (programId) {\r\n      // Load departments for the selected program\r\n      this.loadDepartmentsByProgram(programId);\r\n\r\n      // Reset dependent fields\r\n      this.studentForm.patchValue({\r\n        department: '',\r\n        classId: '',\r\n        semester: ''\r\n      });\r\n      this.filteredClasses = [];\r\n      this.availableSemesters = [];\r\n\r\n      // Handle program type logic for semester/year\r\n      const selectedProgram = this.programs.find(p => p._id === programId);\r\n      this.handleProgramTypeChange(selectedProgram);\r\n    } else {\r\n      this.filteredDepartments = [];\r\n      this.filteredClasses = [];\r\n      this.availableSemesters = [];\r\n    }\r\n  }\r\n\r\n  onDepartmentChange(): void {\r\n    const programId = this.studentForm.get('program')?.value;\r\n    const departmentId = this.studentForm.get('department')?.value;\r\n    console.log('Department changed:', departmentId, 'Program:', programId);\r\n\r\n    if (programId && departmentId) {\r\n      // Load classes for the selected program and department\r\n      this.loadClassesByProgramAndDepartment(programId, departmentId);\r\n\r\n      // Reset class selection\r\n      this.studentForm.patchValue({\r\n        classId: ''\r\n      });\r\n    } else {\r\n      this.filteredClasses = [];\r\n    }\r\n  }\r\n\r\n  // Load departments by program\r\n  loadDepartmentsByProgram(programId: string): void {\r\n    console.log('Loading departments for program:', programId);\r\n    this.departmentService.getDepartmentsByProgram(programId, true).subscribe({\r\n      next: (response: any) => {\r\n        console.log('Departments response:', response);\r\n        if (response.success) {\r\n          this.filteredDepartments = response.departments;\r\n          console.log('Filtered departments:', this.filteredDepartments);\r\n        }\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Error loading departments by program:', error);\r\n        this.showError('Failed to load departments');\r\n      }\r\n    });\r\n  }\r\n\r\n  // Load classes by program and department\r\n  loadClassesByProgramAndDepartment(programId: string, departmentId: string): void {\r\n    this.classService.getClassesByProgramAndDepartment(programId, departmentId, true).subscribe({\r\n      next: (response: any) => {\r\n        if (response.success) {\r\n          this.filteredClasses = response.classes;\r\n        }\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Error loading classes:', error);\r\n        this.showError('Failed to load classes');\r\n      }\r\n    });\r\n  }\r\n\r\n  // Handle program type change for semester/year logic\r\n  handleProgramTypeChange(program: any): void {\r\n    if (!program) return;\r\n\r\n    if (program.name === 'Intermediate') {\r\n      // For Intermediate programs: Year-based (1st Year, 2nd Year)\r\n      this.availableSemesters = [\r\n        { value: 1, label: '1st Year' },\r\n        { value: 2, label: '2nd Year' }\r\n      ];\r\n    } else {\r\n      // For BS/MS/PhD programs: Semester-based\r\n      const totalSemesters = program.totalSemesters || 8;\r\n      this.availableSemesters = [];\r\n      for (let i = 1; i <= totalSemesters; i++) {\r\n        this.availableSemesters.push({\r\n          value: i,\r\n          label: this.getOrdinalNumber(i) + ' Semester'\r\n        });\r\n      }\r\n    }\r\n  }\r\n\r\n  // Helper method to get ordinal numbers (1st, 2nd, 3rd, etc.)\r\n  getOrdinalNumber(num: number): string {\r\n    const suffixes = ['th', 'st', 'nd', 'rd'];\r\n    const v = num % 100;\r\n    return num + (suffixes[(v - 20) % 10] || suffixes[v] || suffixes[0]);\r\n  }\r\n\r\n  // Check if current program is Intermediate\r\n  isIntermediateProgram(): boolean {\r\n    const programId = this.studentForm.get('program')?.value;\r\n    const selectedProgram = this.programs.find(p => p._id === programId);\r\n    return selectedProgram?.name === 'Intermediate';\r\n  }\r\n\r\n  onSave(): void {\r\n    if (this.studentForm.invalid) {\r\n      this.markFormGroupTouched();\r\n      this.logFormErrors();\r\n      this.showError('Please fill in all required fields correctly');\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n    const formData = this.studentForm.value;\r\n\r\n    const studentData = {\r\n      ...formData,\r\n      role: 'Student'\r\n    };\r\n\r\n    // Remove password field for edit mode\r\n    if (this.isEditMode) {\r\n      delete studentData.password;\r\n    }\r\n\r\n    const operation = this.isEditMode\r\n      ? this.userService.updateUserProfile(this.studentId!, studentData)\r\n      : this.userService.register(studentData);\r\n\r\n    operation.subscribe({\r\n      next: () => {\r\n        this.loading = false;\r\n        const message = this.isEditMode ? 'Student updated successfully!' : 'Student registered successfully!';\r\n        this.showSuccess(message);\r\n        this.router.navigate(['/dashboard/admin/students/student-list']);\r\n      },\r\n      error: (error: HttpErrorResponse) => {\r\n        this.loading = false;\r\n        console.error('Error saving student:', error);\r\n        this.showError(error.error?.message || 'Failed to save student');\r\n      }\r\n    });\r\n  }\r\n\r\n  onCancel(): void {\r\n    this.router.navigate(['/dashboard/admin/students/student-list']);\r\n  }\r\n\r\n  // Helper methods\r\n  private markFormGroupTouched(): void {\r\n    Object.keys(this.studentForm.controls).forEach(key => {\r\n      this.studentForm.get(key)?.markAsTouched();\r\n    });\r\n  }\r\n\r\n  private logFormErrors(): void {\r\n    // Form validation errors - only log in development\r\n    if (!environment.production) {\r\n      console.log('Form is invalid. Checking individual field errors:');\r\n      Object.keys(this.studentForm.controls).forEach(key => {\r\n        const control = this.studentForm.get(key);\r\n        if (control && control.invalid) {\r\n          console.log(`${key}:`, control.errors);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  private showSuccess(message: string): void {\r\n    this.snackBar.open(message, 'Close', {\r\n      duration: 3000,\r\n      panelClass: ['success-snackbar']\r\n    });\r\n  }\r\n\r\n  private showError(message: string): void {\r\n    this.snackBar.open(message, 'Close', {\r\n      duration: 5000,\r\n      panelClass: ['error-snackbar']\r\n    });\r\n  }\r\n\r\n  // Getters for form validation\r\n  get name() { return this.studentForm.get('name'); }\r\n  get email() { return this.studentForm.get('email'); }\r\n  get password() { return this.studentForm.get('password'); }\r\n  get rollNo() { return this.studentForm.get('rollNo'); }\r\n  get regNo() { return this.studentForm.get('regNo'); }\r\n  get father_name() { return this.studentForm.get('father_name'); }\r\n  get father_contact() { return this.studentForm.get('father_contact'); }\r\n  get contact() { return this.studentForm.get('contact'); }\r\n  get address() { return this.studentForm.get('address'); }\r\n  get date_of_birth() { return this.studentForm.get('date_of_birth'); }\r\n  get program() { return this.studentForm.get('program'); }\r\n  get department() { return this.studentForm.get('department'); }\r\n  get classId() { return this.studentForm.get('classId'); }\r\n  get semester() { return this.studentForm.get('semester'); }\r\n  get academicYear() { return this.studentForm.get('academicYear'); }\r\n\r\n  // Check if form is valid for submission\r\n  get isFormValid(): boolean {\r\n    return this.studentForm.valid;\r\n  }\r\n\r\n  // Get list of invalid controls for debugging\r\n  getInvalidControls(): string[] {\r\n    const invalidControls: string[] = [];\r\n    Object.keys(this.studentForm.controls).forEach(key => {\r\n      const control = this.studentForm.get(key);\r\n      if (control && control.invalid) {\r\n        invalidControls.push(key);\r\n      }\r\n    });\r\n    return invalidControls;\r\n  }\r\n}\r\n", "<div class=\"form-container\">\r\n  <div class=\"form-header\">\r\n    <h2>\r\n      <mat-icon>{{ isEditMode ? 'edit' : 'person_add' }}</mat-icon>\r\n      {{ isEditMode ? 'Edit Student' : 'Add New Student' }}\r\n    </h2>\r\n  </div>\r\n\r\n  <form [formGroup]=\"studentForm\" (ngSubmit)=\"onSave()\" class=\"student-form\">\r\n    <mat-card class=\"form-card\">\r\n      <mat-card-header>\r\n        <mat-card-title>\r\n          <mat-icon>person</mat-icon>\r\n          Personal Information\r\n        </mat-card-title>\r\n      </mat-card-header>\r\n      <mat-card-content>\r\n        <div class=\"form-row\">\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Student Name</mat-label>\r\n            <input matInput formControlName=\"name\" placeholder=\"Enter full name\">\r\n            <mat-icon matSuffix>person</mat-icon>\r\n            <mat-error *ngIf=\"name?.hasError('required')\">Name is required</mat-error>\r\n            <mat-error *ngIf=\"name?.hasError('minlength')\">Name must be at least 2 characters</mat-error>\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Father's Name</mat-label>\r\n            <input matInput formControlName=\"father_name\" placeholder=\"Enter father's full name\">\r\n            <mat-icon matSuffix>family_restroom</mat-icon>\r\n            <mat-error *ngIf=\"father_name?.hasError('required')\">Father's name is required</mat-error>\r\n          </mat-form-field>\r\n        </div>\r\n\r\n        <div class=\"form-row\">\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Date of Birth</mat-label>\r\n            <input matInput type=\"date\" formControlName=\"date_of_birth\">\r\n            <mat-icon matSuffix>calendar_today</mat-icon>\r\n            <mat-error *ngIf=\"date_of_birth?.hasError('required')\">Date of birth is required</mat-error>\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Address</mat-label>\r\n            <textarea matInput formControlName=\"address\" rows=\"3\" placeholder=\"Enter complete address\"></textarea>\r\n            <mat-icon matSuffix>location_on</mat-icon>\r\n            <mat-error *ngIf=\"address?.hasError('required')\">Address is required</mat-error>\r\n          </mat-form-field>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n\r\n    <mat-card class=\"form-card\">\r\n      <mat-card-header>\r\n        <mat-card-title>\r\n          <mat-icon>contact_phone</mat-icon>\r\n          Contact Information\r\n        </mat-card-title>\r\n      </mat-card-header>\r\n      <mat-card-content>\r\n        <div class=\"form-row\">\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Student Contact Number</mat-label>\r\n            <input matInput formControlName=\"contact\" placeholder=\"Enter student's mobile number\">\r\n            <mat-icon matSuffix>phone</mat-icon>\r\n            <mat-error *ngIf=\"contact?.hasError('required')\">Contact number is required</mat-error>\r\n            <mat-error *ngIf=\"contact?.hasError('pattern')\">Please enter a valid phone number (10-15 digits)</mat-error>\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Father's Contact Number</mat-label>\r\n            <input matInput formControlName=\"father_contact\" placeholder=\"Enter father's mobile number\">\r\n            <mat-icon matSuffix>phone</mat-icon>\r\n            <mat-error *ngIf=\"father_contact?.hasError('required')\">Father's contact is required</mat-error>\r\n            <mat-error *ngIf=\"father_contact?.hasError('pattern')\">Please enter a valid phone number (10-15 digits)</mat-error>\r\n          </mat-form-field>\r\n        </div>\r\n\r\n        <div class=\"form-row\">\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Email Address</mat-label>\r\n            <input matInput type=\"email\" formControlName=\"email\" placeholder=\"Enter email address\">\r\n            <mat-icon matSuffix>email</mat-icon>\r\n            <mat-error *ngIf=\"email?.hasError('required')\">Email is required</mat-error>\r\n            <mat-error *ngIf=\"email?.hasError('email')\">Please enter a valid email address</mat-error>\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\" *ngIf=\"!isEditMode\">\r\n            <mat-label>Password</mat-label>\r\n            <input matInput type=\"password\" formControlName=\"password\" placeholder=\"Enter password\">\r\n            <mat-icon matSuffix>lock</mat-icon>\r\n            <mat-error *ngIf=\"password?.hasError('required')\">Password is required</mat-error>\r\n            <mat-error *ngIf=\"password?.hasError('minlength')\">Password must be at least 6 characters</mat-error>\r\n          </mat-form-field>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n\r\n    <mat-card class=\"form-card\">\r\n      <mat-card-header>\r\n        <mat-card-title>\r\n          <mat-icon>school</mat-icon>\r\n          Academic Information\r\n        </mat-card-title>\r\n      </mat-card-header>\r\n      <mat-card-content>\r\n        <div class=\"form-row\">\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Roll Number</mat-label>\r\n            <input matInput formControlName=\"rollNo\" placeholder=\"Enter roll number\">\r\n            <mat-icon matSuffix>badge</mat-icon>\r\n            <mat-error *ngIf=\"rollNo?.hasError('required')\">Roll number is required</mat-error>\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Registration Number</mat-label>\r\n            <input matInput formControlName=\"regNo\" placeholder=\"Enter registration number\">\r\n            <mat-icon matSuffix>assignment</mat-icon>\r\n            <mat-error *ngIf=\"regNo?.hasError('required')\">Registration number is required</mat-error>\r\n          </mat-form-field>\r\n        </div>\r\n\r\n        <!-- Cascading Dropdowns -->\r\n        <div class=\"form-row\">\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Program</mat-label>\r\n            <mat-select formControlName=\"program\" (selectionChange)=\"onProgramChange()\">\r\n              <mat-option value=\"\">Select Program</mat-option>\r\n              <mat-option *ngFor=\"let program of programs\" [value]=\"program._id\">\r\n                {{ program.name }}\r\n              </mat-option>\r\n            </mat-select>\r\n            <mat-error *ngIf=\"program?.hasError('required')\">Program is required</mat-error>\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Department</mat-label>\r\n            <mat-select formControlName=\"department\" (selectionChange)=\"onDepartmentChange()\" [disabled]=\"!program?.value\">\r\n              <mat-option value=\"\">Select Department</mat-option>\r\n              <mat-option *ngFor=\"let dept of filteredDepartments\" [value]=\"dept._id\">\r\n                {{ dept.name }}\r\n              </mat-option>\r\n            </mat-select>\r\n            <mat-error *ngIf=\"department?.hasError('required')\">Department is required</mat-error>\r\n          </mat-form-field>\r\n        </div>\r\n\r\n        <div class=\"form-row\">\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Class</mat-label>\r\n            <mat-select formControlName=\"classId\" [disabled]=\"!department?.value\">\r\n              <mat-option value=\"\">Select Class</mat-option>\r\n              <mat-option *ngFor=\"let cls of filteredClasses\" [value]=\"cls._id\">\r\n                {{ cls.className }} - {{ cls.section }}\r\n              </mat-option>\r\n            </mat-select>\r\n            <mat-error *ngIf=\"classId?.hasError('required')\">Class is required</mat-error>\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>{{ isIntermediateProgram() ? 'Academic Year' : 'Semester' }}</mat-label>\r\n            <mat-select formControlName=\"semester\" [disabled]=\"!program?.value || availableSemesters.length === 0\">\r\n              <mat-option value=\"\">{{ isIntermediateProgram() ? 'Select Year' : 'Select Semester' }}</mat-option>\r\n              <mat-option *ngFor=\"let sem of availableSemesters\" [value]=\"sem.value\">\r\n                {{ sem.label }}\r\n              </mat-option>\r\n            </mat-select>\r\n            <mat-error *ngIf=\"semester?.hasError('required')\">{{ isIntermediateProgram() ? 'Year' : 'Semester' }} is required</mat-error>\r\n          </mat-form-field>\r\n        </div>\r\n\r\n        <div class=\"form-row\">\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Academic Year</mat-label>\r\n            <input matInput formControlName=\"academicYear\" placeholder=\"e.g., 2024-2025\">\r\n            <mat-icon matSuffix>calendar_today</mat-icon>\r\n            <mat-error *ngIf=\"academicYear?.hasError('required')\">Academic year is required</mat-error>\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>School Last Attended</mat-label>\r\n            <input matInput formControlName=\"school_last_attendance\" placeholder=\"Enter previous school name\">\r\n            <mat-icon matSuffix>school</mat-icon>\r\n          </mat-form-field>\r\n        </div>\r\n\r\n        <div class=\"form-row\">\r\n          <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n            <mat-label>Year of Passing</mat-label>\r\n            <input matInput type=\"number\" formControlName=\"year_of_passing\" placeholder=\"Enter year of passing\" min=\"1990\" max=\"2030\">\r\n            <mat-icon matSuffix>event</mat-icon>\r\n          </mat-form-field>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n\r\n    <!-- Form Status (for debugging - can be removed in production) -->\r\n    <div class=\"debug-info\" style=\"background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px; font-size: 12px;\" *ngIf=\"false\">\r\n      <strong>Form Status:</strong> {{ studentForm.valid ? 'Valid' : 'Invalid' }} |\r\n      <strong>Loading:</strong> {{ loading }} |\r\n      <strong>Edit Mode:</strong> {{ isEditMode }}\r\n      <br>\r\n      <strong>Invalid Fields:</strong>\r\n      <span *ngFor=\"let control of getInvalidControls()\">{{ control }} </span>\r\n    </div>\r\n\r\n    <!-- Action Buttons -->\r\n    <div class=\"form-actions\">\r\n      <button mat-raised-button type=\"button\" color=\"warn\" (click)=\"onCancel()\" class=\"action-btn\">\r\n        <mat-icon>cancel</mat-icon>\r\n        Cancel\r\n      </button>\r\n\r\n      <button mat-raised-button type=\"submit\" color=\"primary\" [disabled]=\"!isFormValid || loading\" class=\"action-btn\">\r\n        <mat-icon>{{ isEditMode ? 'update' : 'save' }}</mat-icon>\r\n        {{ isEditMode ? 'Update Student' : 'Save Student' }}\r\n        <mat-spinner *ngIf=\"loading\" diameter=\"20\" class=\"spinner\"></mat-spinner>\r\n      </button>\r\n    </div>\r\n  </form>\r\n</div>\r\n"], "mappings": "AAGA,SAAiCA,UAAU,QAAQ,gBAAgB;AAOnE,SAASC,WAAW,QAAQ,8BAA8B;;;;;;;;;;;;;;;;;;;;ICY9CC,EAAA,CAAAC,cAAA,gBAA8C;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAC1EH,EAAA,CAAAC,cAAA,gBAA+C;IAAAD,EAAA,CAAAE,MAAA,yCAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAO7FH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,gCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAS1FH,EAAA,CAAAC,cAAA,gBAAuD;IAAAD,EAAA,CAAAE,MAAA,gCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAO5FH,EAAA,CAAAC,cAAA,gBAAiD;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAmBhFH,EAAA,CAAAC,cAAA,gBAAiD;IAAAD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACvFH,EAAA,CAAAC,cAAA,gBAAgD;IAAAD,EAAA,CAAAE,MAAA,uDAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAO5GH,EAAA,CAAAC,cAAA,gBAAwD;IAAAD,EAAA,CAAAE,MAAA,mCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAChGH,EAAA,CAAAC,cAAA,gBAAuD;IAAAD,EAAA,CAAAE,MAAA,uDAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IASnHH,EAAA,CAAAC,cAAA,gBAA+C;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAC5EH,EAAA,CAAAC,cAAA,gBAA4C;IAAAD,EAAA,CAAAE,MAAA,yCAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAO1FH,EAAA,CAAAC,cAAA,gBAAkD;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAClFH,EAAA,CAAAC,cAAA,gBAAmD;IAAAD,EAAA,CAAAE,MAAA,6CAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IALvGH,EAAA,CAAAC,cAAA,wBAA4E;IAC/DD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC/BH,EAAA,CAAAI,SAAA,gBAAwF;IACxFJ,EAAA,CAAAC,cAAA,kBAAoB;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACnCH,EAAA,CAAAK,UAAA,IAAAC,0DAAA,uBAAkF;IAClFN,EAAA,CAAAK,UAAA,IAAAE,0DAAA,uBAAqG;IACvGP,EAAA,CAAAG,YAAA,EAAiB;;;;IAFHH,EAAA,CAAAQ,SAAA,GAAoC;IAApCR,EAAA,CAAAS,UAAA,SAAAC,OAAA,CAAAC,QAAA,kBAAAD,OAAA,CAAAC,QAAA,CAAAC,QAAA,aAAoC;IACpCZ,EAAA,CAAAQ,SAAA,GAAqC;IAArCR,EAAA,CAAAS,UAAA,SAAAC,OAAA,CAAAC,QAAA,kBAAAD,OAAA,CAAAC,QAAA,CAAAC,QAAA,cAAqC;;;;;IAmBjDZ,EAAA,CAAAC,cAAA,gBAAgD;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAOnFH,EAAA,CAAAC,cAAA,gBAA+C;IAAAD,EAAA,CAAAE,MAAA,sCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAUxFH,EAAA,CAAAC,cAAA,qBAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFgCH,EAAA,CAAAS,UAAA,UAAAI,WAAA,CAAAC,GAAA,CAAqB;IAChEd,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAe,kBAAA,MAAAF,WAAA,CAAAG,IAAA,MACF;;;;;IAEFhB,EAAA,CAAAC,cAAA,gBAAiD;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAO9EH,EAAA,CAAAC,cAAA,qBAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFwCH,EAAA,CAAAS,UAAA,UAAAQ,QAAA,CAAAH,GAAA,CAAkB;IACrEd,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAe,kBAAA,MAAAE,QAAA,CAAAD,IAAA,MACF;;;;;IAEFhB,EAAA,CAAAC,cAAA,gBAAoD;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IASpFH,EAAA,CAAAC,cAAA,qBAAkE;IAChED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFmCH,EAAA,CAAAS,UAAA,UAAAS,OAAA,CAAAJ,GAAA,CAAiB;IAC/Dd,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAmB,kBAAA,MAAAD,OAAA,CAAAE,SAAA,SAAAF,OAAA,CAAAG,OAAA,MACF;;;;;IAEFrB,EAAA,CAAAC,cAAA,gBAAiD;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAO5EH,EAAA,CAAAC,cAAA,qBAAuE;IACrED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFsCH,EAAA,CAAAS,UAAA,UAAAa,OAAA,CAAAC,KAAA,CAAmB;IACpEvB,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAe,kBAAA,MAAAO,OAAA,CAAAE,KAAA,MACF;;;;;IAEFxB,EAAA,CAAAC,cAAA,gBAAkD;IAAAD,EAAA,CAAAE,MAAA,GAA+D;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAA3EH,EAAA,CAAAQ,SAAA,GAA+D;IAA/DR,EAAA,CAAAe,kBAAA,KAAAU,OAAA,CAAAC,qBAAA,yCAA+D;;;;;IASjH1B,EAAA,CAAAC,cAAA,gBAAsD;IAAAD,EAAA,CAAAE,MAAA,gCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IA2BjGH,EAAA,CAAAC,cAAA,WAAmD;IAAAD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAArBH,EAAA,CAAAQ,SAAA,GAAc;IAAdR,EAAA,CAAAe,kBAAA,KAAAY,WAAA,MAAc;;;;;IANnE3B,EAAA,CAAAC,cAAA,cAAuI;IAC7HD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GAC9B;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GAC1B;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GAC5B;IAAAF,EAAA,CAAAI,SAAA,UAAI;IACJJ,EAAA,CAAAC,cAAA,cAAQ;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAChCH,EAAA,CAAAK,UAAA,KAAAuB,4CAAA,mBAAwE;IAC1E5B,EAAA,CAAAG,YAAA,EAAM;;;;IAN0BH,EAAA,CAAAQ,SAAA,GAC9B;IAD8BR,EAAA,CAAAe,kBAAA,MAAAc,OAAA,CAAAC,WAAA,CAAAC,KAAA,8BAC9B;IAA0B/B,EAAA,CAAAQ,SAAA,GAC1B;IAD0BR,EAAA,CAAAe,kBAAA,MAAAc,OAAA,CAAAG,OAAA,QAC1B;IAA4BhC,EAAA,CAAAQ,SAAA,GAC5B;IAD4BR,EAAA,CAAAe,kBAAA,MAAAc,OAAA,CAAAI,UAAA,MAC5B;IAE0BjC,EAAA,CAAAQ,SAAA,GAAuB;IAAvBR,EAAA,CAAAS,UAAA,YAAAoB,OAAA,CAAAK,kBAAA,GAAuB;;;;;IAa/ClC,EAAA,CAAAI,SAAA,sBAAyE;;;ADvMjF,OAAM,MAAO+B,mBAAmB;EAc9BC,YACUC,EAAe,EACfC,WAAwB,EACxBC,MAAc,EACdC,KAAqB,EACrBC,YAA4B,EAC5BC,cAA8B,EAC9BC,iBAAoC,EACpCC,QAAqB;IAPrB,KAAAP,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,QAAQ,GAARA,QAAQ;IApBlB,KAAAX,UAAU,GAAG,KAAK;IAClB,KAAAY,SAAS,GAAkB,IAAI;IAC/B,KAAAb,OAAO,GAAG,KAAK;IAEf;IACA,KAAAc,QAAQ,GAAc,EAAE;IACxB,KAAAC,WAAW,GAAiB,EAAE;IAC9B,KAAAC,OAAO,GAAY,EAAE;IACrB,KAAAC,mBAAmB,GAAiB,EAAE;IACtC,KAAAC,eAAe,GAAY,EAAE;IAC7B,KAAAC,kBAAkB,GAAuC,EAAE;IAYzD,IAAI,CAACrB,WAAW,GAAG,IAAI,CAACsB,UAAU,EAAE;EACtC;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,aAAa,EAAE;EACtB;EAEAH,UAAUA,CAAA;IACR,OAAO,IAAI,CAACf,EAAE,CAACmB,KAAK,CAAC;MACnBxC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAClB,UAAU,CAAC2D,QAAQ,EAAE3D,UAAU,CAAC4D,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1DC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC7D,UAAU,CAAC2D,QAAQ,EAAE3D,UAAU,CAAC6D,KAAK,CAAC,CAAC;MACpDhD,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACb,UAAU,CAAC2D,QAAQ,EAAE3D,UAAU,CAAC4D,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC9D,UAAU,CAAC2D,QAAQ,CAAC,CAAC;MACnCI,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC/D,UAAU,CAAC2D,QAAQ,CAAC,CAAC;MAClCK,WAAW,EAAE,CAAC,EAAE,EAAE,CAAChE,UAAU,CAAC2D,QAAQ,CAAC,CAAC;MACxCM,cAAc,EAAE,CAAC,EAAE,EAAE,CAACjE,UAAU,CAAC2D,QAAQ,EAAE3D,UAAU,CAACkE,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC;MACjFC,OAAO,EAAE,CAAC,EAAE,EAAE,CAACnE,UAAU,CAAC2D,QAAQ,EAAE3D,UAAU,CAACkE,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC;MAC1EE,OAAO,EAAE,CAAC,EAAE,EAAE,CAACpE,UAAU,CAAC2D,QAAQ,CAAC,CAAC;MACpCU,aAAa,EAAE,CAAC,EAAE,EAAE,CAACrE,UAAU,CAAC2D,QAAQ,CAAC,CAAC;MAC1CW,sBAAsB,EAAE,CAAC,EAAE,CAAC;MAC5BC,eAAe,EAAE,CAAC,EAAE,CAAC;MACrBC,OAAO,EAAE,CAAC,EAAE,EAAE,CAACxE,UAAU,CAAC2D,QAAQ,CAAC,CAAC;MACpCc,UAAU,EAAE,CAAC,EAAE,EAAE,CAACzE,UAAU,CAAC2D,QAAQ,CAAC,CAAC;MACvCe,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC1E,UAAU,CAAC2D,QAAQ,CAAC,CAAC;MACpCgB,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC3E,UAAU,CAAC2D,QAAQ,EAAE3D,UAAU,CAAC4E,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACxDC,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC7E,UAAU,CAAC2D,QAAQ,CAAC;KACzC,CAAC;EACJ;EAEAmB,wBAAwBA,CAAA;IACtB,MAAMC,eAAe,GAAG,IAAI,CAAC/C,WAAW,CAACgD,GAAG,CAAC,UAAU,CAAC;IACxD,IAAI,IAAI,CAAC7C,UAAU,EAAE;MACnB;MACA4C,eAAe,EAAEE,aAAa,CAAC,CAACjF,UAAU,CAAC4D,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;KAC1D,MAAM;MACL;MACAmB,eAAe,EAAEE,aAAa,CAAC,CAACjF,UAAU,CAAC2D,QAAQ,EAAE3D,UAAU,CAAC4D,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEhFmB,eAAe,EAAEG,sBAAsB,EAAE;EAC3C;EACA1B,eAAeA,CAAA;IACb;IACA,IAAI,CAACZ,cAAc,CAACuC,cAAc,CAAC,IAAI,CAAC,CAACC,SAAS,CAAC;MACjDC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACvC,QAAQ,GAAGsC,QAAQ,CAACtC,QAAQ;UACjCwC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAACzC,QAAQ,CAAC;;MAElD,CAAC;MACD0C,KAAK,EAAGA,KAAU,IAAI;QACpBF,OAAO,CAACE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACC,SAAS,CAAC,yBAAyB,CAAC;MAC3C;KACD,CAAC;IAEF;IACA,IAAI,CAAC9C,iBAAiB,CAAC+C,iBAAiB,EAAE,CAACR,SAAS,CAAC;MACnDC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACtC,WAAW,GAAGqC,QAAQ,CAACrC,WAAW;UACvCuC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACxC,WAAW,CAAC;;MAExD,CAAC;MACDyC,KAAK,EAAGA,KAAU,IAAI;QACpBF,OAAO,CAACE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,IAAI,CAACC,SAAS,CAAC,4BAA4B,CAAC;MAC9C;KACD,CAAC;IAEF;IACA,IAAI,CAAChD,YAAY,CAACkD,aAAa,EAAE,CAACT,SAAS,CAAC;MAC1CC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACrC,OAAO,GAAGoC,QAAQ,CAACpC,OAAO;;MAEnC,CAAC;MACDwC,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACC,SAAS,CAAC,wBAAwB,CAAC;MAC1C;KACD,CAAC;EACJ;EAEAlC,aAAaA,CAAA;IACX,IAAI,CAACV,SAAS,GAAG,IAAI,CAACL,KAAK,CAACoD,QAAQ,CAACC,QAAQ,CAACf,GAAG,CAAC,IAAI,CAAC;IACvD,IAAI,IAAI,CAACjC,SAAS,EAAE;MAClB,IAAI,CAACZ,UAAU,GAAG,IAAI;MACtB,IAAI,CAAC2C,wBAAwB,EAAE;MAC/B,IAAI,CAACkB,eAAe,EAAE;;EAE1B;EAEAA,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACjD,SAAS,EAAE;IAErB,IAAI,CAACb,OAAO,GAAG,IAAI;IACnB,IAAI,CAACM,WAAW,CAACyD,cAAc,CAAC,IAAI,CAAClD,SAAS,CAAC,CAACqC,SAAS,CAAC;MACxDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,MAAMW,OAAO,GAAGZ,QAAQ,CAACa,IAAI;UAC7B,IAAI,CAACnE,WAAW,CAACoE,UAAU,CAAC;YAC1BlF,IAAI,EAAEgF,OAAO,CAAChF,IAAI;YAClB2C,KAAK,EAAEqC,OAAO,CAACrC,KAAK;YACpBC,MAAM,EAAEoC,OAAO,CAACpC,MAAM;YACtBC,KAAK,EAAEmC,OAAO,CAACnC,KAAK;YACpBC,WAAW,EAAEkC,OAAO,CAAClC,WAAW;YAChCC,cAAc,EAAEiC,OAAO,CAACjC,cAAc;YACtCE,OAAO,EAAE+B,OAAO,CAAC/B,OAAO;YACxBC,OAAO,EAAE8B,OAAO,CAAC9B,OAAO;YACxBC,aAAa,EAAE6B,OAAO,CAAC7B,aAAa,GAAG,IAAIgC,IAAI,CAACH,OAAO,CAAC7B,aAAa,CAAC,CAACiC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;YACvGjC,sBAAsB,EAAE4B,OAAO,CAAC5B,sBAAsB;YACtDC,eAAe,EAAE2B,OAAO,CAAC3B,eAAe;YACxCC,OAAO,EAAE0B,OAAO,CAAC1B,OAAO,EAAExD,GAAG,IAAIkF,OAAO,CAAC1B,OAAO;YAChDC,UAAU,EAAEyB,OAAO,CAACzB,UAAU,EAAEzD,GAAG,IAAIkF,OAAO,CAACzB,UAAU;YACzDC,OAAO,EAAEwB,OAAO,CAACxB,OAAO,EAAE1D,GAAG,IAAIkF,OAAO,CAACxB,OAAO;YAChDC,QAAQ,EAAEuB,OAAO,CAACvB,QAAQ;YAC1BE,YAAY,EAAEqB,OAAO,CAACrB;WACvB,CAAC;UAEF;UACA,IAAIqB,OAAO,CAAC1B,OAAO,EAAE;YACnB,IAAI,CAACgC,eAAe,EAAE;;UAExB,IAAIN,OAAO,CAACzB,UAAU,EAAE;YACtB,IAAI,CAACgC,kBAAkB,EAAE;;;QAG7B,IAAI,CAACvE,OAAO,GAAG,KAAK;MACtB,CAAC;MACDwD,KAAK,EAAGA,KAAK,IAAI;QACfF,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACC,SAAS,CAAC,6BAA6B,CAAC;QAC7C,IAAI,CAACzD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EACA;EACAsE,eAAeA,CAAA;IACb,MAAME,SAAS,GAAG,IAAI,CAAC1E,WAAW,CAACgD,GAAG,CAAC,SAAS,CAAC,EAAEvD,KAAK;IACxD+D,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEiB,SAAS,CAAC;IAC1C,IAAIA,SAAS,EAAE;MACb;MACA,IAAI,CAACC,wBAAwB,CAACD,SAAS,CAAC;MAExC;MACA,IAAI,CAAC1E,WAAW,CAACoE,UAAU,CAAC;QAC1B3B,UAAU,EAAE,EAAE;QACdC,OAAO,EAAE,EAAE;QACXC,QAAQ,EAAE;OACX,CAAC;MACF,IAAI,CAACvB,eAAe,GAAG,EAAE;MACzB,IAAI,CAACC,kBAAkB,GAAG,EAAE;MAE5B;MACA,MAAMuD,eAAe,GAAG,IAAI,CAAC5D,QAAQ,CAAC6D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC9F,GAAG,KAAK0F,SAAS,CAAC;MACpE,IAAI,CAACK,uBAAuB,CAACH,eAAe,CAAC;KAC9C,MAAM;MACL,IAAI,CAACzD,mBAAmB,GAAG,EAAE;MAC7B,IAAI,CAACC,eAAe,GAAG,EAAE;MACzB,IAAI,CAACC,kBAAkB,GAAG,EAAE;;EAEhC;EAEAoD,kBAAkBA,CAAA;IAChB,MAAMC,SAAS,GAAG,IAAI,CAAC1E,WAAW,CAACgD,GAAG,CAAC,SAAS,CAAC,EAAEvD,KAAK;IACxD,MAAMuF,YAAY,GAAG,IAAI,CAAChF,WAAW,CAACgD,GAAG,CAAC,YAAY,CAAC,EAAEvD,KAAK;IAC9D+D,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEuB,YAAY,EAAE,UAAU,EAAEN,SAAS,CAAC;IAEvE,IAAIA,SAAS,IAAIM,YAAY,EAAE;MAC7B;MACA,IAAI,CAACC,iCAAiC,CAACP,SAAS,EAAEM,YAAY,CAAC;MAE/D;MACA,IAAI,CAAChF,WAAW,CAACoE,UAAU,CAAC;QAC1B1B,OAAO,EAAE;OACV,CAAC;KACH,MAAM;MACL,IAAI,CAACtB,eAAe,GAAG,EAAE;;EAE7B;EAEA;EACAuD,wBAAwBA,CAACD,SAAiB;IACxClB,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEiB,SAAS,CAAC;IAC1D,IAAI,CAAC7D,iBAAiB,CAACqE,uBAAuB,CAACR,SAAS,EAAE,IAAI,CAAC,CAACtB,SAAS,CAAC;MACxEC,IAAI,EAAGC,QAAa,IAAI;QACtBE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEH,QAAQ,CAAC;QAC9C,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACpC,mBAAmB,GAAGmC,QAAQ,CAACrC,WAAW;UAC/CuC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACtC,mBAAmB,CAAC;;MAElE,CAAC;MACDuC,KAAK,EAAGA,KAAU,IAAI;QACpBF,OAAO,CAACE,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7D,IAAI,CAACC,SAAS,CAAC,4BAA4B,CAAC;MAC9C;KACD,CAAC;EACJ;EAEA;EACAsB,iCAAiCA,CAACP,SAAiB,EAAEM,YAAoB;IACvE,IAAI,CAACrE,YAAY,CAACwE,gCAAgC,CAACT,SAAS,EAAEM,YAAY,EAAE,IAAI,CAAC,CAAC5B,SAAS,CAAC;MAC1FC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACnC,eAAe,GAAGkC,QAAQ,CAACpC,OAAO;;MAE3C,CAAC;MACDwC,KAAK,EAAGA,KAAU,IAAI;QACpBF,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACC,SAAS,CAAC,wBAAwB,CAAC;MAC1C;KACD,CAAC;EACJ;EAEA;EACAoB,uBAAuBA,CAACvC,OAAY;IAClC,IAAI,CAACA,OAAO,EAAE;IAEd,IAAIA,OAAO,CAACtD,IAAI,KAAK,cAAc,EAAE;MACnC;MACA,IAAI,CAACmC,kBAAkB,GAAG,CACxB;QAAE5B,KAAK,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAU,CAAE,EAC/B;QAAED,KAAK,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAU,CAAE,CAChC;KACF,MAAM;MACL;MACA,MAAM0F,cAAc,GAAG5C,OAAO,CAAC4C,cAAc,IAAI,CAAC;MAClD,IAAI,CAAC/D,kBAAkB,GAAG,EAAE;MAC5B,KAAK,IAAIgE,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAID,cAAc,EAAEC,CAAC,EAAE,EAAE;QACxC,IAAI,CAAChE,kBAAkB,CAACiE,IAAI,CAAC;UAC3B7F,KAAK,EAAE4F,CAAC;UACR3F,KAAK,EAAE,IAAI,CAAC6F,gBAAgB,CAACF,CAAC,CAAC,GAAG;SACnC,CAAC;;;EAGR;EAEA;EACAE,gBAAgBA,CAACC,GAAW;IAC1B,MAAMC,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,MAAMC,CAAC,GAAGF,GAAG,GAAG,GAAG;IACnB,OAAOA,GAAG,IAAIC,QAAQ,CAAC,CAACC,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,IAAID,QAAQ,CAACC,CAAC,CAAC,IAAID,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtE;EAEA;EACA7F,qBAAqBA,CAAA;IACnB,MAAM8E,SAAS,GAAG,IAAI,CAAC1E,WAAW,CAACgD,GAAG,CAAC,SAAS,CAAC,EAAEvD,KAAK;IACxD,MAAMmF,eAAe,GAAG,IAAI,CAAC5D,QAAQ,CAAC6D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC9F,GAAG,KAAK0F,SAAS,CAAC;IACpE,OAAOE,eAAe,EAAE1F,IAAI,KAAK,cAAc;EACjD;EAEAyG,MAAMA,CAAA;IACJ,IAAI,IAAI,CAAC3F,WAAW,CAAC4F,OAAO,EAAE;MAC5B,IAAI,CAACC,oBAAoB,EAAE;MAC3B,IAAI,CAACC,aAAa,EAAE;MACpB,IAAI,CAACnC,SAAS,CAAC,8CAA8C,CAAC;MAC9D;;IAGF,IAAI,CAACzD,OAAO,GAAG,IAAI;IACnB,MAAM6F,QAAQ,GAAG,IAAI,CAAC/F,WAAW,CAACP,KAAK;IAEvC,MAAMuG,WAAW,GAAG;MAClB,GAAGD,QAAQ;MACXE,IAAI,EAAE;KACP;IAED;IACA,IAAI,IAAI,CAAC9F,UAAU,EAAE;MACnB,OAAO6F,WAAW,CAACnH,QAAQ;;IAG7B,MAAMqH,SAAS,GAAG,IAAI,CAAC/F,UAAU,GAC7B,IAAI,CAACK,WAAW,CAAC2F,iBAAiB,CAAC,IAAI,CAACpF,SAAU,EAAEiF,WAAW,CAAC,GAChE,IAAI,CAACxF,WAAW,CAAC4F,QAAQ,CAACJ,WAAW,CAAC;IAE1CE,SAAS,CAAC9C,SAAS,CAAC;MAClBC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACnD,OAAO,GAAG,KAAK;QACpB,MAAMmG,OAAO,GAAG,IAAI,CAAClG,UAAU,GAAG,+BAA+B,GAAG,kCAAkC;QACtG,IAAI,CAACmG,WAAW,CAACD,OAAO,CAAC;QACzB,IAAI,CAAC5F,MAAM,CAAC8F,QAAQ,CAAC,CAAC,wCAAwC,CAAC,CAAC;MAClE,CAAC;MACD7C,KAAK,EAAGA,KAAwB,IAAI;QAClC,IAAI,CAACxD,OAAO,GAAG,KAAK;QACpBsD,OAAO,CAACE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,IAAI,CAACC,SAAS,CAACD,KAAK,CAACA,KAAK,EAAE2C,OAAO,IAAI,wBAAwB,CAAC;MAClE;KACD,CAAC;EACJ;EAEAG,QAAQA,CAAA;IACN,IAAI,CAAC/F,MAAM,CAAC8F,QAAQ,CAAC,CAAC,wCAAwC,CAAC,CAAC;EAClE;EAEA;EACQV,oBAAoBA,CAAA;IAC1BY,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC1G,WAAW,CAAC2G,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MACnD,IAAI,CAAC7G,WAAW,CAACgD,GAAG,CAAC6D,GAAG,CAAC,EAAEC,aAAa,EAAE;IAC5C,CAAC,CAAC;EACJ;EAEQhB,aAAaA,CAAA;IACnB;IACA,IAAI,CAAC7H,WAAW,CAAC8I,UAAU,EAAE;MAC3BvD,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;MACjEgD,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC1G,WAAW,CAAC2G,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;QACnD,MAAMG,OAAO,GAAG,IAAI,CAAChH,WAAW,CAACgD,GAAG,CAAC6D,GAAG,CAAC;QACzC,IAAIG,OAAO,IAAIA,OAAO,CAACpB,OAAO,EAAE;UAC9BpC,OAAO,CAACC,GAAG,CAAC,GAAGoD,GAAG,GAAG,EAAEG,OAAO,CAACC,MAAM,CAAC;;MAE1C,CAAC,CAAC;;EAEN;EAEQX,WAAWA,CAACD,OAAe;IACjC,IAAI,CAACvF,QAAQ,CAACoG,IAAI,CAACb,OAAO,EAAE,OAAO,EAAE;MACnCc,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,CAAC,kBAAkB;KAChC,CAAC;EACJ;EAEQzD,SAASA,CAAC0C,OAAe;IAC/B,IAAI,CAACvF,QAAQ,CAACoG,IAAI,CAACb,OAAO,EAAE,OAAO,EAAE;MACnCc,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,CAAC,gBAAgB;KAC9B,CAAC;EACJ;EAEA;EACA,IAAIlI,IAAIA,CAAA;IAAK,OAAO,IAAI,CAACc,WAAW,CAACgD,GAAG,CAAC,MAAM,CAAC;EAAE;EAClD,IAAInB,KAAKA,CAAA;IAAK,OAAO,IAAI,CAAC7B,WAAW,CAACgD,GAAG,CAAC,OAAO,CAAC;EAAE;EACpD,IAAInE,QAAQA,CAAA;IAAK,OAAO,IAAI,CAACmB,WAAW,CAACgD,GAAG,CAAC,UAAU,CAAC;EAAE;EAC1D,IAAIlB,MAAMA,CAAA;IAAK,OAAO,IAAI,CAAC9B,WAAW,CAACgD,GAAG,CAAC,QAAQ,CAAC;EAAE;EACtD,IAAIjB,KAAKA,CAAA;IAAK,OAAO,IAAI,CAAC/B,WAAW,CAACgD,GAAG,CAAC,OAAO,CAAC;EAAE;EACpD,IAAIhB,WAAWA,CAAA;IAAK,OAAO,IAAI,CAAChC,WAAW,CAACgD,GAAG,CAAC,aAAa,CAAC;EAAE;EAChE,IAAIf,cAAcA,CAAA;IAAK,OAAO,IAAI,CAACjC,WAAW,CAACgD,GAAG,CAAC,gBAAgB,CAAC;EAAE;EACtE,IAAIb,OAAOA,CAAA;IAAK,OAAO,IAAI,CAACnC,WAAW,CAACgD,GAAG,CAAC,SAAS,CAAC;EAAE;EACxD,IAAIZ,OAAOA,CAAA;IAAK,OAAO,IAAI,CAACpC,WAAW,CAACgD,GAAG,CAAC,SAAS,CAAC;EAAE;EACxD,IAAIX,aAAaA,CAAA;IAAK,OAAO,IAAI,CAACrC,WAAW,CAACgD,GAAG,CAAC,eAAe,CAAC;EAAE;EACpE,IAAIR,OAAOA,CAAA;IAAK,OAAO,IAAI,CAACxC,WAAW,CAACgD,GAAG,CAAC,SAAS,CAAC;EAAE;EACxD,IAAIP,UAAUA,CAAA;IAAK,OAAO,IAAI,CAACzC,WAAW,CAACgD,GAAG,CAAC,YAAY,CAAC;EAAE;EAC9D,IAAIN,OAAOA,CAAA;IAAK,OAAO,IAAI,CAAC1C,WAAW,CAACgD,GAAG,CAAC,SAAS,CAAC;EAAE;EACxD,IAAIL,QAAQA,CAAA;IAAK,OAAO,IAAI,CAAC3C,WAAW,CAACgD,GAAG,CAAC,UAAU,CAAC;EAAE;EAC1D,IAAIH,YAAYA,CAAA;IAAK,OAAO,IAAI,CAAC7C,WAAW,CAACgD,GAAG,CAAC,cAAc,CAAC;EAAE;EAElE;EACA,IAAIqE,WAAWA,CAAA;IACb,OAAO,IAAI,CAACrH,WAAW,CAACC,KAAK;EAC/B;EAEA;EACAG,kBAAkBA,CAAA;IAChB,MAAMkH,eAAe,GAAa,EAAE;IACpCb,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC1G,WAAW,CAAC2G,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MACnD,MAAMG,OAAO,GAAG,IAAI,CAAChH,WAAW,CAACgD,GAAG,CAAC6D,GAAG,CAAC;MACzC,IAAIG,OAAO,IAAIA,OAAO,CAACpB,OAAO,EAAE;QAC9B0B,eAAe,CAAChC,IAAI,CAACuB,GAAG,CAAC;;IAE7B,CAAC,CAAC;IACF,OAAOS,eAAe;EACxB;EAAC,QAAAC,CAAA,G;qBAjYUlH,mBAAmB,EAAAnC,EAAA,CAAAsJ,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxJ,EAAA,CAAAsJ,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA1J,EAAA,CAAAsJ,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAA5J,EAAA,CAAAsJ,iBAAA,CAAAK,EAAA,CAAAE,cAAA,GAAA7J,EAAA,CAAAsJ,iBAAA,CAAAQ,EAAA,CAAAC,cAAA,GAAA/J,EAAA,CAAAsJ,iBAAA,CAAAU,EAAA,CAAAC,cAAA,GAAAjK,EAAA,CAAAsJ,iBAAA,CAAAY,EAAA,CAAAC,iBAAA,GAAAnK,EAAA,CAAAsJ,iBAAA,CAAAc,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAnBnI,mBAAmB;IAAAoI,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCjBhC7K,EAAA,CAAAC,cAAA,aAA4B;QAGZD,EAAA,CAAAE,MAAA,GAAwC;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC7DH,EAAA,CAAAE,MAAA,GACF;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAGPH,EAAA,CAAAC,cAAA,cAA2E;QAA3CD,EAAA,CAAA+K,UAAA,sBAAAC,sDAAA;UAAA,OAAYF,GAAA,CAAArD,MAAA,EAAQ;QAAA,EAAC;QACnDzH,EAAA,CAAAC,cAAA,kBAA4B;QAGZD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC3BH,EAAA,CAAAE,MAAA,8BACF;QAAAF,EAAA,CAAAG,YAAA,EAAiB;QAEnBH,EAAA,CAAAC,cAAA,wBAAkB;QAGDD,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACnCH,EAAA,CAAAI,SAAA,gBAAqE;QACrEJ,EAAA,CAAAC,cAAA,mBAAoB;QAAAD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACrCH,EAAA,CAAAK,UAAA,KAAA4K,yCAAA,uBAA0E;QAC1EjL,EAAA,CAAAK,UAAA,KAAA6K,yCAAA,uBAA6F;QAC/FlL,EAAA,CAAAG,YAAA,EAAiB;QAEjBH,EAAA,CAAAC,cAAA,yBAAwD;QAC3CD,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACpCH,EAAA,CAAAI,SAAA,gBAAqF;QACrFJ,EAAA,CAAAC,cAAA,mBAAoB;QAAAD,EAAA,CAAAE,MAAA,uBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC9CH,EAAA,CAAAK,UAAA,KAAA8K,yCAAA,uBAA0F;QAC5FnL,EAAA,CAAAG,YAAA,EAAiB;QAGnBH,EAAA,CAAAC,cAAA,cAAsB;QAEPD,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACpCH,EAAA,CAAAI,SAAA,iBAA4D;QAC5DJ,EAAA,CAAAC,cAAA,mBAAoB;QAAAD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC7CH,EAAA,CAAAK,UAAA,KAAA+K,yCAAA,uBAA4F;QAC9FpL,EAAA,CAAAG,YAAA,EAAiB;QAEjBH,EAAA,CAAAC,cAAA,yBAAwD;QAC3CD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC9BH,EAAA,CAAAI,SAAA,oBAAsG;QACtGJ,EAAA,CAAAC,cAAA,mBAAoB;QAAAD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC1CH,EAAA,CAAAK,UAAA,KAAAgL,yCAAA,uBAAgF;QAClFrL,EAAA,CAAAG,YAAA,EAAiB;QAKvBH,EAAA,CAAAC,cAAA,mBAA4B;QAGZD,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAClCH,EAAA,CAAAE,MAAA,6BACF;QAAAF,EAAA,CAAAG,YAAA,EAAiB;QAEnBH,EAAA,CAAAC,cAAA,wBAAkB;QAGDD,EAAA,CAAAE,MAAA,8BAAsB;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC7CH,EAAA,CAAAI,SAAA,iBAAsF;QACtFJ,EAAA,CAAAC,cAAA,mBAAoB;QAAAD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACpCH,EAAA,CAAAK,UAAA,KAAAiL,yCAAA,uBAAuF;QACvFtL,EAAA,CAAAK,UAAA,KAAAkL,yCAAA,uBAA4G;QAC9GvL,EAAA,CAAAG,YAAA,EAAiB;QAEjBH,EAAA,CAAAC,cAAA,yBAAwD;QAC3CD,EAAA,CAAAE,MAAA,+BAAuB;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC9CH,EAAA,CAAAI,SAAA,iBAA4F;QAC5FJ,EAAA,CAAAC,cAAA,mBAAoB;QAAAD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACpCH,EAAA,CAAAK,UAAA,KAAAmL,yCAAA,uBAAgG;QAChGxL,EAAA,CAAAK,UAAA,KAAAoL,yCAAA,uBAAmH;QACrHzL,EAAA,CAAAG,YAAA,EAAiB;QAGnBH,EAAA,CAAAC,cAAA,cAAsB;QAEPD,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACpCH,EAAA,CAAAI,SAAA,iBAAuF;QACvFJ,EAAA,CAAAC,cAAA,mBAAoB;QAAAD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACpCH,EAAA,CAAAK,UAAA,KAAAqL,yCAAA,uBAA4E;QAC5E1L,EAAA,CAAAK,UAAA,KAAAsL,yCAAA,uBAA0F;QAC5F3L,EAAA,CAAAG,YAAA,EAAiB;QAEjBH,EAAA,CAAAK,UAAA,KAAAuL,8CAAA,6BAMiB;QACnB5L,EAAA,CAAAG,YAAA,EAAM;QAIVH,EAAA,CAAAC,cAAA,mBAA4B;QAGZD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC3BH,EAAA,CAAAE,MAAA,8BACF;QAAAF,EAAA,CAAAG,YAAA,EAAiB;QAEnBH,EAAA,CAAAC,cAAA,wBAAkB;QAGDD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAClCH,EAAA,CAAAI,SAAA,iBAAyE;QACzEJ,EAAA,CAAAC,cAAA,mBAAoB;QAAAD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACpCH,EAAA,CAAAK,UAAA,KAAAwL,yCAAA,uBAAmF;QACrF7L,EAAA,CAAAG,YAAA,EAAiB;QAEjBH,EAAA,CAAAC,cAAA,yBAAwD;QAC3CD,EAAA,CAAAE,MAAA,2BAAmB;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC1CH,EAAA,CAAAI,SAAA,iBAAgF;QAChFJ,EAAA,CAAAC,cAAA,mBAAoB;QAAAD,EAAA,CAAAE,MAAA,kBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACzCH,EAAA,CAAAK,UAAA,MAAAyL,0CAAA,uBAA0F;QAC5F9L,EAAA,CAAAG,YAAA,EAAiB;QAInBH,EAAA,CAAAC,cAAA,eAAsB;QAEPD,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC9BH,EAAA,CAAAC,cAAA,uBAA4E;QAAtCD,EAAA,CAAA+K,UAAA,6BAAAgB,qEAAA;UAAA,OAAmBjB,GAAA,CAAAxE,eAAA,EAAiB;QAAA,EAAC;QACzEtG,EAAA,CAAAC,cAAA,uBAAqB;QAAAD,EAAA,CAAAE,MAAA,uBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAa;QAChDH,EAAA,CAAAK,UAAA,MAAA2L,2CAAA,yBAEa;QACfhM,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAAK,UAAA,MAAA4L,0CAAA,uBAAgF;QAClFjM,EAAA,CAAAG,YAAA,EAAiB;QAEjBH,EAAA,CAAAC,cAAA,0BAAwD;QAC3CD,EAAA,CAAAE,MAAA,mBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACjCH,EAAA,CAAAC,cAAA,uBAA+G;QAAtED,EAAA,CAAA+K,UAAA,6BAAAmB,qEAAA;UAAA,OAAmBpB,GAAA,CAAAvE,kBAAA,EAAoB;QAAA,EAAC;QAC/EvG,EAAA,CAAAC,cAAA,uBAAqB;QAAAD,EAAA,CAAAE,MAAA,0BAAiB;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACnDH,EAAA,CAAAK,UAAA,MAAA8L,2CAAA,yBAEa;QACfnM,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAAK,UAAA,MAAA+L,0CAAA,uBAAsF;QACxFpM,EAAA,CAAAG,YAAA,EAAiB;QAGnBH,EAAA,CAAAC,cAAA,eAAsB;QAEPD,EAAA,CAAAE,MAAA,cAAK;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC5BH,EAAA,CAAAC,cAAA,uBAAsE;QAC/CD,EAAA,CAAAE,MAAA,qBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAa;QAC9CH,EAAA,CAAAK,UAAA,MAAAgM,2CAAA,yBAEa;QACfrM,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAAK,UAAA,MAAAiM,0CAAA,uBAA8E;QAChFtM,EAAA,CAAAG,YAAA,EAAiB;QAEjBH,EAAA,CAAAC,cAAA,0BAAwD;QAC3CD,EAAA,CAAAE,MAAA,KAA4D;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACnFH,EAAA,CAAAC,cAAA,uBAAuG;QAChFD,EAAA,CAAAE,MAAA,KAAiE;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACnGH,EAAA,CAAAK,UAAA,MAAAkM,2CAAA,yBAEa;QACfvM,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAAK,UAAA,MAAAmM,0CAAA,uBAA6H;QAC/HxM,EAAA,CAAAG,YAAA,EAAiB;QAGnBH,EAAA,CAAAC,cAAA,eAAsB;QAEPD,EAAA,CAAAE,MAAA,sBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACpCH,EAAA,CAAAI,SAAA,kBAA6E;QAC7EJ,EAAA,CAAAC,cAAA,oBAAoB;QAAAD,EAAA,CAAAE,MAAA,uBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC7CH,EAAA,CAAAK,UAAA,MAAAoM,0CAAA,uBAA2F;QAC7FzM,EAAA,CAAAG,YAAA,EAAiB;QAEjBH,EAAA,CAAAC,cAAA,0BAAwD;QAC3CD,EAAA,CAAAE,MAAA,6BAAoB;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC3CH,EAAA,CAAAI,SAAA,kBAAkG;QAClGJ,EAAA,CAAAC,cAAA,oBAAoB;QAAAD,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAIzCH,EAAA,CAAAC,cAAA,eAAsB;QAEPD,EAAA,CAAAE,MAAA,wBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACtCH,EAAA,CAAAI,SAAA,kBAA0H;QAC1HJ,EAAA,CAAAC,cAAA,oBAAoB;QAAAD,EAAA,CAAAE,MAAA,cAAK;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAO5CH,EAAA,CAAAK,UAAA,MAAAqM,oCAAA,mBAOM;QAGN1M,EAAA,CAAAC,cAAA,gBAA0B;QAC6BD,EAAA,CAAA+K,UAAA,mBAAA4B,uDAAA;UAAA,OAAS7B,GAAA,CAAAxC,QAAA,EAAU;QAAA,EAAC;QACvEtI,EAAA,CAAAC,cAAA,iBAAU;QAAAD,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC3BH,EAAA,CAAAE,MAAA,iBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAETH,EAAA,CAAAC,cAAA,mBAAgH;QACpGD,EAAA,CAAAE,MAAA,KAAoC;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACzDH,EAAA,CAAAE,MAAA,KACA;QAAAF,EAAA,CAAAK,UAAA,MAAAuM,4CAAA,0BAAyE;QAC3E5M,EAAA,CAAAG,YAAA,EAAS;;;QAtNCH,EAAA,CAAAQ,SAAA,GAAwC;QAAxCR,EAAA,CAAA6M,iBAAA,CAAA/B,GAAA,CAAA7I,UAAA,yBAAwC;QAClDjC,EAAA,CAAAQ,SAAA,GACF;QADER,EAAA,CAAAe,kBAAA,MAAA+J,GAAA,CAAA7I,UAAA,2CACF;QAGIjC,EAAA,CAAAQ,SAAA,GAAyB;QAAzBR,EAAA,CAAAS,UAAA,cAAAqK,GAAA,CAAAhJ,WAAA,CAAyB;QAcT9B,EAAA,CAAAQ,SAAA,IAAgC;QAAhCR,EAAA,CAAAS,UAAA,SAAAqK,GAAA,CAAA9J,IAAA,kBAAA8J,GAAA,CAAA9J,IAAA,CAAAJ,QAAA,aAAgC;QAChCZ,EAAA,CAAAQ,SAAA,GAAiC;QAAjCR,EAAA,CAAAS,UAAA,SAAAqK,GAAA,CAAA9J,IAAA,kBAAA8J,GAAA,CAAA9J,IAAA,CAAAJ,QAAA,cAAiC;QAOjCZ,EAAA,CAAAQ,SAAA,GAAuC;QAAvCR,EAAA,CAAAS,UAAA,SAAAqK,GAAA,CAAAhH,WAAA,kBAAAgH,GAAA,CAAAhH,WAAA,CAAAlD,QAAA,aAAuC;QASvCZ,EAAA,CAAAQ,SAAA,GAAyC;QAAzCR,EAAA,CAAAS,UAAA,SAAAqK,GAAA,CAAA3G,aAAA,kBAAA2G,GAAA,CAAA3G,aAAA,CAAAvD,QAAA,aAAyC;QAOzCZ,EAAA,CAAAQ,SAAA,GAAmC;QAAnCR,EAAA,CAAAS,UAAA,SAAAqK,GAAA,CAAA5G,OAAA,kBAAA4G,GAAA,CAAA5G,OAAA,CAAAtD,QAAA,aAAmC;QAmBnCZ,EAAA,CAAAQ,SAAA,IAAmC;QAAnCR,EAAA,CAAAS,UAAA,SAAAqK,GAAA,CAAA7G,OAAA,kBAAA6G,GAAA,CAAA7G,OAAA,CAAArD,QAAA,aAAmC;QACnCZ,EAAA,CAAAQ,SAAA,GAAkC;QAAlCR,EAAA,CAAAS,UAAA,SAAAqK,GAAA,CAAA7G,OAAA,kBAAA6G,GAAA,CAAA7G,OAAA,CAAArD,QAAA,YAAkC;QAOlCZ,EAAA,CAAAQ,SAAA,GAA0C;QAA1CR,EAAA,CAAAS,UAAA,SAAAqK,GAAA,CAAA/G,cAAA,kBAAA+G,GAAA,CAAA/G,cAAA,CAAAnD,QAAA,aAA0C;QAC1CZ,EAAA,CAAAQ,SAAA,GAAyC;QAAzCR,EAAA,CAAAS,UAAA,SAAAqK,GAAA,CAAA/G,cAAA,kBAAA+G,GAAA,CAAA/G,cAAA,CAAAnD,QAAA,YAAyC;QASzCZ,EAAA,CAAAQ,SAAA,GAAiC;QAAjCR,EAAA,CAAAS,UAAA,SAAAqK,GAAA,CAAAnH,KAAA,kBAAAmH,GAAA,CAAAnH,KAAA,CAAA/C,QAAA,aAAiC;QACjCZ,EAAA,CAAAQ,SAAA,GAA8B;QAA9BR,EAAA,CAAAS,UAAA,SAAAqK,GAAA,CAAAnH,KAAA,kBAAAmH,GAAA,CAAAnH,KAAA,CAAA/C,QAAA,UAA8B;QAGaZ,EAAA,CAAAQ,SAAA,GAAiB;QAAjBR,EAAA,CAAAS,UAAA,UAAAqK,GAAA,CAAA7I,UAAA,CAAiB;QAwB5DjC,EAAA,CAAAQ,SAAA,IAAkC;QAAlCR,EAAA,CAAAS,UAAA,SAAAqK,GAAA,CAAAlH,MAAA,kBAAAkH,GAAA,CAAAlH,MAAA,CAAAhD,QAAA,aAAkC;QAOlCZ,EAAA,CAAAQ,SAAA,GAAiC;QAAjCR,EAAA,CAAAS,UAAA,SAAAqK,GAAA,CAAAjH,KAAA,kBAAAiH,GAAA,CAAAjH,KAAA,CAAAjD,QAAA,aAAiC;QAUXZ,EAAA,CAAAQ,SAAA,GAAW;QAAXR,EAAA,CAAAS,UAAA,YAAAqK,GAAA,CAAAhI,QAAA,CAAW;QAIjC9C,EAAA,CAAAQ,SAAA,GAAmC;QAAnCR,EAAA,CAAAS,UAAA,SAAAqK,GAAA,CAAAxG,OAAA,kBAAAwG,GAAA,CAAAxG,OAAA,CAAA1D,QAAA,aAAmC;QAKmCZ,EAAA,CAAAQ,SAAA,GAA4B;QAA5BR,EAAA,CAAAS,UAAA,eAAAqK,GAAA,CAAAxG,OAAA,kBAAAwG,GAAA,CAAAxG,OAAA,CAAA/C,KAAA,EAA4B;QAE/EvB,EAAA,CAAAQ,SAAA,GAAsB;QAAtBR,EAAA,CAAAS,UAAA,YAAAqK,GAAA,CAAA7H,mBAAA,CAAsB;QAIzCjD,EAAA,CAAAQ,SAAA,GAAsC;QAAtCR,EAAA,CAAAS,UAAA,SAAAqK,GAAA,CAAAvG,UAAA,kBAAAuG,GAAA,CAAAvG,UAAA,CAAA3D,QAAA,aAAsC;QAOZZ,EAAA,CAAAQ,SAAA,GAA+B;QAA/BR,EAAA,CAAAS,UAAA,eAAAqK,GAAA,CAAAvG,UAAA,kBAAAuG,GAAA,CAAAvG,UAAA,CAAAhD,KAAA,EAA+B;QAEvCvB,EAAA,CAAAQ,SAAA,GAAkB;QAAlBR,EAAA,CAAAS,UAAA,YAAAqK,GAAA,CAAA5H,eAAA,CAAkB;QAIpClD,EAAA,CAAAQ,SAAA,GAAmC;QAAnCR,EAAA,CAAAS,UAAA,SAAAqK,GAAA,CAAAtG,OAAA,kBAAAsG,GAAA,CAAAtG,OAAA,CAAA5D,QAAA,aAAmC;QAIpCZ,EAAA,CAAAQ,SAAA,GAA4D;QAA5DR,EAAA,CAAA6M,iBAAA,CAAA/B,GAAA,CAAApJ,qBAAA,kCAA4D;QAChC1B,EAAA,CAAAQ,SAAA,GAA+D;QAA/DR,EAAA,CAAAS,UAAA,eAAAqK,GAAA,CAAAxG,OAAA,kBAAAwG,GAAA,CAAAxG,OAAA,CAAA/C,KAAA,KAAAuJ,GAAA,CAAA3H,kBAAA,CAAA2J,MAAA,OAA+D;QAC/E9M,EAAA,CAAAQ,SAAA,GAAiE;QAAjER,EAAA,CAAA6M,iBAAA,CAAA/B,GAAA,CAAApJ,qBAAA,uCAAiE;QAC1D1B,EAAA,CAAAQ,SAAA,GAAqB;QAArBR,EAAA,CAAAS,UAAA,YAAAqK,GAAA,CAAA3H,kBAAA,CAAqB;QAIvCnD,EAAA,CAAAQ,SAAA,GAAoC;QAApCR,EAAA,CAAAS,UAAA,SAAAqK,GAAA,CAAArG,QAAA,kBAAAqG,GAAA,CAAArG,QAAA,CAAA7D,QAAA,aAAoC;QASpCZ,EAAA,CAAAQ,SAAA,GAAwC;QAAxCR,EAAA,CAAAS,UAAA,SAAAqK,GAAA,CAAAnG,YAAA,kBAAAmG,GAAA,CAAAnG,YAAA,CAAA/D,QAAA,aAAwC;QAqB8DZ,EAAA,CAAAQ,SAAA,IAAW;QAAXR,EAAA,CAAAS,UAAA,eAAW;QAgB3ET,EAAA,CAAAQ,SAAA,GAAoC;QAApCR,EAAA,CAAAS,UAAA,cAAAqK,GAAA,CAAA3B,WAAA,IAAA2B,GAAA,CAAA9I,OAAA,CAAoC;QAChFhC,EAAA,CAAAQ,SAAA,GAAoC;QAApCR,EAAA,CAAA6M,iBAAA,CAAA/B,GAAA,CAAA7I,UAAA,qBAAoC;QAC9CjC,EAAA,CAAAQ,SAAA,GACA;QADAR,EAAA,CAAAe,kBAAA,MAAA+J,GAAA,CAAA7I,UAAA,0CACA;QAAcjC,EAAA,CAAAQ,SAAA,GAAa;QAAbR,EAAA,CAAAS,UAAA,SAAAqK,GAAA,CAAA9I,OAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}