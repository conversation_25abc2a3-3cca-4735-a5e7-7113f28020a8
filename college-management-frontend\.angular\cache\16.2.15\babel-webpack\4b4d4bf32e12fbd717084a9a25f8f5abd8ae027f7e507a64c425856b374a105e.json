{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction ForgotPasswordComponent_div_19_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Email is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ForgotPasswordComponent_div_19_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Please enter a valid email\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ForgotPasswordComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtemplate(1, ForgotPasswordComponent_div_19_small_1_Template, 2, 0, \"small\", 28);\n    i0.ɵɵtemplate(2, ForgotPasswordComponent_div_19_small_2_Template, 2, 0, \"small\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r0.forgotPasswordForm.get(\"email\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r0.forgotPasswordForm.get(\"email\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"email\"]);\n  }\n}\nfunction ForgotPasswordComponent_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 29);\n  }\n}\nexport let ForgotPasswordComponent = /*#__PURE__*/(() => {\n  class ForgotPasswordComponent {\n    constructor(fb, authService, router) {\n      this.fb = fb;\n      this.authService = authService;\n      this.router = router;\n      this.loading = false;\n    }\n    ngOnInit() {\n      this.forgotPasswordForm = this.fb.group({\n        email: ['', [Validators.required, Validators.email]]\n      });\n    }\n    onSubmit() {\n      if (this.forgotPasswordForm.valid) {\n        this.loading = true;\n        const email = this.forgotPasswordForm.value.email;\n        this.authService.forgotPassword(email).subscribe({\n          next: response => {\n            this.loading = false;\n            if (response.success) {\n              // Store user data for OTP verification\n              localStorage.setItem('resetUser', JSON.stringify(response.user));\n              Swal.fire({\n                title: 'OTP Sent!',\n                text: `We've sent a verification code to ${email}. Please check your email.`,\n                icon: 'success',\n                confirmButtonText: 'Enter OTP',\n                confirmButtonColor: '#29578c'\n              }).then(result => {\n                if (result.isConfirmed) {\n                  this.router.navigate(['/auth/Enter-code']);\n                }\n              });\n            }\n          },\n          error: error => {\n            this.loading = false;\n            console.error('Forgot password error:', error);\n            const errorMessage = error.error?.error || error.error?.message || 'Failed to send OTP. Please try again.';\n            Swal.fire({\n              title: 'Error',\n              text: errorMessage,\n              icon: 'error',\n              confirmButtonColor: '#29578c'\n            });\n          }\n        });\n      } else {\n        Swal.fire({\n          title: 'Invalid Email',\n          text: 'Please enter a valid email address.',\n          icon: 'warning',\n          confirmButtonColor: '#29578c'\n        });\n      }\n    }\n    static #_ = this.ɵfac = function ForgotPasswordComponent_Factory(t) {\n      return new (t || ForgotPasswordComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ForgotPasswordComponent,\n      selectors: [[\"app-forgot-password\"]],\n      decls: 40,\n      vars: 7,\n      consts: [[1, \"container-fluid\"], [1, \"row\", \"vh-100\"], [1, \"col-lg-5\", \"d-flex\", \"flex-column\", \"justify-content-center\", \"align-items-center\", \"p-3\", \"bg-white\"], [1, \"w-100\", 2, \"max-width\", \"400px\"], [1, \"text-center\"], [1, \"mb-4\"], [\"src\", \"../../../assets/images/logo.jpeg\"], [1, \"d-flex\", \"justify-content-center\", \"mt-5\", \"mb-2\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"fa-key\", \"key\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"mb-3\"], [\"for\", \"email\", 1, \"form-label\"], [\"type\", \"email\", \"id\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter your email\", 1, \"form-control\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [1, \"d-grid\", \"gap-2\"], [\"type\", \"submit\", 1, \"btn\", \"submit\", 3, \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", \"role\", \"status\", \"aria-hidden\", \"true\", 4, \"ngIf\"], [1, \"mt-3\", \"text-center\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"fa-arrow-left\"], [\"routerLink\", \"/auth\"], [1, \"col-lg-7\", \"d-none\", \"d-lg-flex\", \"flex-column\", \"align-items-start\", \"bg-light\", \"position-relative\"], [1, \"text-start\", \"p-5\", \"w-100\"], [1, \"blockquote\"], [1, \"blockquote-footer\"], [\"title\", \"Source Title\"], [1, \"image-container\"], [\"src\", \"../../../assets/images/background.jpeg\", \"alt\", \"Stats graph\", 1, \"img-fluid\", \"position-absolute\"], [1, \"invalid-feedback\"], [4, \"ngIf\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"]],\n      template: function ForgotPasswordComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"h2\", 5);\n          i0.ɵɵelement(6, \"img\", 6);\n          i0.ɵɵtext(7, \" GPGC (Swabi)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 7);\n          i0.ɵɵelement(9, \"i\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"h1\", 5);\n          i0.ɵɵtext(11, \"Forgot Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"p\");\n          i0.ɵɵtext(13, \"No Worries , we will send you reset instructions.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"form\", 9);\n          i0.ɵɵlistener(\"ngSubmit\", function ForgotPasswordComponent_Template_form_ngSubmit_14_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(15, \"div\", 10)(16, \"label\", 11);\n          i0.ɵɵtext(17, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(18, \"input\", 12);\n          i0.ɵɵtemplate(19, ForgotPasswordComponent_div_19_Template, 3, 2, \"div\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\", 14)(21, \"button\", 15);\n          i0.ɵɵtemplate(22, ForgotPasswordComponent_span_22_Template, 1, 0, \"span\", 16);\n          i0.ɵɵtext(23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"p\", 17);\n          i0.ɵɵelement(25, \"i\", 18);\n          i0.ɵɵtext(26, \" \\u00A0\");\n          i0.ɵɵelementStart(27, \"a\", 19);\n          i0.ɵɵtext(28, \"Back to log in \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(29, \"div\", 20)(30, \"div\", 21)(31, \"blockquote\", 22)(32, \"h2\", 5);\n          i0.ɵɵtext(33, \"College management system Login page\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"footer\", 23);\n          i0.ɵɵtext(35, \"Name\");\n          i0.ɵɵelementStart(36, \"cite\", 24);\n          i0.ɵɵtext(37, \"Owner ~ GPGC SWABI\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(38, \"div\", 25);\n          i0.ɵɵelement(39, \"img\", 26);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_2_0;\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"formGroup\", ctx.forgotPasswordForm);\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_1_0 = ctx.forgotPasswordForm.get(\"email\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.forgotPasswordForm.get(\"email\")) == null ? null : tmp_1_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.forgotPasswordForm.get(\"email\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.forgotPasswordForm.get(\"email\")) == null ? null : tmp_2_0.touched));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.loading || ctx.forgotPasswordForm.invalid);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.loading ? \"Sending...\" : \"Reset Password\", \" \");\n        }\n      },\n      dependencies: [i4.NgIf, i3.RouterLink, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n      styles: [\"body[_ngcontent-%COMP%], html[_ngcontent-%COMP%]{margin:0;padding:0;overflow-x:hidden;height:100%}.image-container[_ngcontent-%COMP%]{width:100%;height:100%;position:relative}.image-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:80%;height:auto;position:absolute;right:0;bottom:0;object-fit:contain}.submit[_ngcontent-%COMP%]{background-color:#29578c;color:#fff}.forgot[_ngcontent-%COMP%]{color:#29578c;text-decoration:none;font-weight:700}a[_ngcontent-%COMP%]{cursor:pointer;text-decoration:none;color:#000;font-weight:600}.key[_ngcontent-%COMP%]{background-color:#b8d1ef!important;border-radius:50%;height:50px;width:50px;display:flex;justify-content:center;align-items:center;color:#29578c}\"]\n    });\n  }\n  return ForgotPasswordComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}