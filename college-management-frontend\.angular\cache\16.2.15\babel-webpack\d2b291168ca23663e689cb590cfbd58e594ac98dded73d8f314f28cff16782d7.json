{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AttendanceComponent } from './attendance/attendance.component';\nimport { AddStudentComponent } from './add-student/add-student.component';\nimport { AdminStudentListComponent } from './admin-student-list/admin-student-list.component';\nimport { StudentViewComponent } from './student-view/student-view.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: AdminStudentListComponent\n}, {\n  path: 'add-student',\n  component: AddStudentComponent\n}, {\n  path: 'student-list',\n  component: AdminStudentListComponent\n}, {\n  path: 'view/:id',\n  component: StudentViewComponent\n}, {\n  path: 'edit/:id',\n  component: AddStudentComponent\n}, {\n  path: 'attendance',\n  component: AttendanceComponent\n}];\nexport class StudentsRoutingModule {\n  static #_ = this.ɵfac = function StudentsRoutingModule_Factory(t) {\n    return new (t || StudentsRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: StudentsRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild(routes), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(StudentsRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "AttendanceComponent", "AddStudentComponent", "AdminStudentListComponent", "StudentViewComponent", "routes", "path", "component", "StudentsRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\students\\students-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { AttendanceComponent } from './attendance/attendance.component';\r\nimport { AddStudentComponent } from './add-student/add-student.component';\r\nimport { AdminStudentListComponent } from './admin-student-list/admin-student-list.component';\r\nimport { StudentViewComponent } from './student-view/student-view.component';\r\n\r\nconst routes: Routes = [\r\n  {path:'', component:AdminStudentListComponent},\r\n  {path:'add-student' , component:AddStudentComponent},\r\n  {path:'student-list', component:AdminStudentListComponent},\r\n  {path:'view/:id', component:StudentViewComponent},\r\n  {path:'edit/:id', component:AddStudentComponent},\r\n  {path:'attendance', component:AttendanceComponent},\r\n\r\n\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class StudentsRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,yBAAyB,QAAQ,mDAAmD;AAC7F,SAASC,oBAAoB,QAAQ,uCAAuC;;;AAE5E,MAAMC,MAAM,GAAW,CACrB;EAACC,IAAI,EAAC,EAAE;EAAEC,SAAS,EAACJ;AAAyB,CAAC,EAC9C;EAACG,IAAI,EAAC,aAAa;EAAGC,SAAS,EAACL;AAAmB,CAAC,EACpD;EAACI,IAAI,EAAC,cAAc;EAAEC,SAAS,EAACJ;AAAyB,CAAC,EAC1D;EAACG,IAAI,EAAC,UAAU;EAAEC,SAAS,EAACH;AAAoB,CAAC,EACjD;EAACE,IAAI,EAAC,UAAU;EAAEC,SAAS,EAACL;AAAmB,CAAC,EAChD;EAACI,IAAI,EAAC,YAAY;EAAEC,SAAS,EAACN;AAAmB,CAAC,CAGnD;AAMD,OAAM,MAAOO,qBAAqB;EAAA,QAAAC,CAAA,G;qBAArBD,qBAAqB;EAAA;EAAA,QAAAE,EAAA,G;UAArBF;EAAqB;EAAA,QAAAG,EAAA,G;cAHtBX,YAAY,CAACY,QAAQ,CAACP,MAAM,CAAC,EAC7BL,YAAY;EAAA;;;2EAEXQ,qBAAqB;IAAAK,OAAA,GAAAC,EAAA,CAAAd,YAAA;IAAAe,OAAA,GAFtBf,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}