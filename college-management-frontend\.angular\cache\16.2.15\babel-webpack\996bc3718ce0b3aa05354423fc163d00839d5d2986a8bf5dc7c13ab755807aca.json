{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/icon\";\nimport * as i4 from \"@angular/forms\";\nfunction TeacherAllStudentsComponent_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const class_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", class_r4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(class_r4);\n  }\n}\nfunction TeacherAllStudentsComponent_option_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const department_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", department_r5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(department_r5);\n  }\n}\nfunction TeacherAllStudentsComponent_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const section_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", section_r6);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(section_r6);\n  }\n}\nfunction TeacherAllStudentsComponent_tr_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\")(14, \"button\", 23)(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"visibility\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const student_r7 = ctx.$implicit;\n    const i_r8 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r8 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(student_r7.rollNumber);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(student_r7.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(student_r7.department);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(student_r7.class);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(student_r7.section);\n  }\n}\nexport let TeacherAllStudentsComponent = /*#__PURE__*/(() => {\n  class TeacherAllStudentsComponent {\n    constructor(router) {\n      this.router = router;\n      this.searchQuery = '';\n      // Dropdown options\n      this.departments = ['Pre-Medical', 'Pre-Engineering', 'Computer Science'];\n      this.classes = ['1st Year', '2nd Year'];\n      this.sections = ['A', 'B', 'C'];\n      // Selected filters\n      this.selectedClass = '';\n      this.selectedDepartment = '';\n      this.selectedSection = '';\n      this.students = [];\n      // Pagination properties\n      this.currentPage = 0;\n      this.pageSize = 10; // Number of students per page\n      this.totalPages = 0;\n      this.generateStudentList();\n    }\n    // Generate a list of 100 students\n    generateStudentList() {\n      const firstNames = ['John', 'Jane', 'Alice', 'Bob', 'Mary', 'Steve', 'Sarah', 'Michael', 'David', 'Linda'];\n      const lastNames = ['Doe', 'Smith', 'Johnson', 'Lee', 'Williams', 'Brown', 'Davis', 'Miller', 'Wilson', 'Moore'];\n      let counter = 1;\n      for (let department of this.departments) {\n        for (let className of this.classes) {\n          for (let section of this.sections) {\n            for (let i = 0; i < 10; i++) {\n              this.students.push({\n                rollNumber: `R${counter++}`,\n                name: `${firstNames[i % 10]} ${lastNames[i % 10]}`,\n                department: department,\n                class: className,\n                section: section,\n                status: 'present'\n              });\n            }\n          }\n        }\n      }\n      // Set total pages based on students length\n      this.totalPages = Math.ceil(this.students.length / this.pageSize);\n    }\n    get filteredStudents() {\n      const filtered = this.students.filter(student => {\n        const searchLower = this.searchQuery.toLowerCase();\n        const matchesName = student.name.toLowerCase().includes(searchLower);\n        const matchesRollNumber = student.rollNumber.toLowerCase().includes(searchLower);\n        const matchesStatus = student.status.toLowerCase().includes(searchLower);\n        const matchesDepartment = student.department.toLowerCase().includes(searchLower);\n        const matchesClass = student.class.toLowerCase().includes(searchLower);\n        const matchesSection = student.section.toLowerCase().includes(searchLower);\n        const matchesFilters = (this.selectedClass ? student.class === this.selectedClass : true) && (this.selectedDepartment ? student.department === this.selectedDepartment : true) && (this.selectedSection ? student.section === this.selectedSection : true);\n        return (matchesName || matchesRollNumber || matchesStatus || matchesDepartment || matchesClass || matchesSection) && matchesFilters;\n      });\n      this.totalPages = Math.ceil(filtered.length / this.pageSize);\n      // Paginate the filtered list\n      const startIndex = this.currentPage * this.pageSize;\n      const endIndex = startIndex + this.pageSize;\n      return filtered.slice(startIndex, endIndex);\n    }\n    // Reset filters\n    resetFilters() {\n      this.selectedClass = '';\n      this.selectedDepartment = '';\n      this.selectedSection = '';\n      this.searchQuery = '';\n    }\n    nextPage() {\n      if (this.currentPage < this.totalPages - 1) {\n        this.currentPage++;\n      }\n    }\n    // Pagination: Go to previous page\n    previousPage() {\n      if (this.currentPage > 0) {\n        this.currentPage--;\n      }\n    }\n    // Toggle status based on checkbox\n    toggleStatus(student, isChecked) {\n      student.status = isChecked ? 'present' : 'absent';\n    }\n    addnewstudent() {\n      this.router.navigate(['/dashboard/admin/students/add-student']);\n    }\n    static #_ = this.ɵfac = function TeacherAllStudentsComponent_Factory(t) {\n      return new (t || TeacherAllStudentsComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TeacherAllStudentsComponent,\n      selectors: [[\"app-teacher-all-students\"]],\n      decls: 60,\n      vars: 10,\n      consts: [[1, \"container\"], [1, \"form-group\", \"row\", \"mb-3\"], [1, \"col-md-4\"], [\"for\", \"classSelect\", 1, \"mb-2\"], [\"id\", \"classSelect\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"departmentSelect\", 1, \"mb-2\"], [\"id\", \"departmentSelect\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"sectionSelect\", 1, \"mb-2\"], [\"id\", \"sectionSelect\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\"], [1, \"btn\", \"btn-warning\", \"my-3\", \"d-flex\", \"align-items-center\", \"gap-2\", 3, \"click\"], [\"for\", \"search\", 1, \"mb-2\"], [\"type\", \"text\", \"placeholder\", \"Search by roll number, name, department, class, section, or status...\", 1, \"form-control\", \"mb-3\", 3, \"ngModel\", \"ngModelChange\"], [1, \"table-container\", \"border-0\", \"shadow-none\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-between\", \"mt-3\"], [1, \"btn\", \"navbtns\", 3, \"disabled\", \"click\"], [3, \"value\"], [\"routerLink\", \"/dashboard/teacher/student-detail\", 1, \"btn\"]],\n      template: function TeacherAllStudentsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\");\n          i0.ɵɵtext(2, \"Your All Students\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 1)(4, \"div\", 2)(5, \"label\", 3);\n          i0.ɵɵtext(6, \"Class\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"select\", 4);\n          i0.ɵɵlistener(\"ngModelChange\", function TeacherAllStudentsComponent_Template_select_ngModelChange_7_listener($event) {\n            return ctx.selectedClass = $event;\n          });\n          i0.ɵɵelementStart(8, \"option\", 5);\n          i0.ɵɵtext(9, \"All Classes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(10, TeacherAllStudentsComponent_option_10_Template, 2, 2, \"option\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 2)(12, \"label\", 7);\n          i0.ɵɵtext(13, \"Department\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"select\", 8);\n          i0.ɵɵlistener(\"ngModelChange\", function TeacherAllStudentsComponent_Template_select_ngModelChange_14_listener($event) {\n            return ctx.selectedDepartment = $event;\n          });\n          i0.ɵɵelementStart(15, \"option\", 5);\n          i0.ɵɵtext(16, \"All Departments\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(17, TeacherAllStudentsComponent_option_17_Template, 2, 2, \"option\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 2)(19, \"label\", 9);\n          i0.ɵɵtext(20, \"Section\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"select\", 10);\n          i0.ɵɵlistener(\"ngModelChange\", function TeacherAllStudentsComponent_Template_select_ngModelChange_21_listener($event) {\n            return ctx.selectedSection = $event;\n          });\n          i0.ɵɵelementStart(22, \"option\", 5);\n          i0.ɵɵtext(23, \"All Sections\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(24, TeacherAllStudentsComponent_option_24_Template, 2, 2, \"option\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"div\", 11)(26, \"div\", 12)(27, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function TeacherAllStudentsComponent_Template_button_click_27_listener() {\n            return ctx.resetFilters();\n          });\n          i0.ɵɵelementStart(28, \"mat-icon\");\n          i0.ɵɵtext(29, \"refresh\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(30, \" Reset Filters\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(31, \"label\", 14);\n          i0.ɵɵtext(32, \"Search\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"input\", 15);\n          i0.ɵɵlistener(\"ngModelChange\", function TeacherAllStudentsComponent_Template_input_ngModelChange_33_listener($event) {\n            return ctx.searchQuery = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"div\", 16)(35, \"div\", 17)(36, \"table\", 18)(37, \"thead\")(38, \"tr\")(39, \"th\");\n          i0.ɵɵtext(40, \"No\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"th\");\n          i0.ɵɵtext(42, \"Roll Number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"th\");\n          i0.ɵɵtext(44, \"Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"th\");\n          i0.ɵɵtext(46, \"Department\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"th\");\n          i0.ɵɵtext(48, \"Class\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"th\");\n          i0.ɵɵtext(50, \"Section\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"th\");\n          i0.ɵɵtext(52, \"Action\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(53, \"tbody\");\n          i0.ɵɵtemplate(54, TeacherAllStudentsComponent_tr_54_Template, 17, 6, \"tr\", 19);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(55, \"div\", 20)(56, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function TeacherAllStudentsComponent_Template_button_click_56_listener() {\n            return ctx.previousPage();\n          });\n          i0.ɵɵtext(57, \"Previous\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function TeacherAllStudentsComponent_Template_button_click_58_listener() {\n            return ctx.nextPage();\n          });\n          i0.ɵɵtext(59, \"Next\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngModel\", ctx.selectedClass);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.classes);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.selectedDepartment);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.departments);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.selectedSection);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.sections);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance(21);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredStudents);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.currentPage === 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.currentPage >= ctx.totalPages - 1);\n        }\n      },\n      dependencies: [i2.NgForOf, i1.RouterLink, i3.MatIcon, i4.NgSelectOption, i4.ɵNgSelectMultipleOption, i4.DefaultValueAccessor, i4.SelectControlValueAccessor, i4.NgControlStatus, i4.NgModel],\n      styles: [\".navbtns[_ngcontent-%COMP%]{border:1px solid black;font-weight:600}\"]\n    });\n  }\n  return TeacherAllStudentsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}