{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/role.service\";\nimport * as i2 from \"@angular/router\";\nexport let HeaderComponent = /*#__PURE__*/(() => {\n  class HeaderComponent {\n    constructor(roleService, router) {\n      this.roleService = roleService;\n      this.router = router;\n      this.toggleSidebar = new EventEmitter();\n      this.role = '';\n    }\n    ngOnInit() {\n      this.role = this.roleService.getRole();\n    }\n    logout() {\n      this.roleService.logout();\n      this.router.navigate(['/auth']);\n    }\n    showComingSoon() {\n      Swal.fire({\n        title: 'Coming Soon!',\n        text: 'This feature is under development.',\n        icon: 'info',\n        confirmButtonText: 'OK',\n        confirmButtonColor: '#3085d6'\n      });\n    }\n    static #_ = this.ɵfac = function HeaderComponent_Factory(t) {\n      return new (t || HeaderComponent)(i0.ɵɵdirectiveInject(i1.RoleService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HeaderComponent,\n      selectors: [[\"app-header\"]],\n      outputs: {\n        toggleSidebar: \"toggleSidebar\"\n      },\n      decls: 25,\n      vars: 1,\n      consts: [[1, \"navbar\"], [1, \"container-fluid\"], [1, \"d-flex\", \"align-items-center\"], [1, \"toggle-sidebar-btn\", \"me-3\", 3, \"click\"], [1, \"fas\", \"fa-bars\"], [1, \"ml-auto\", \"d-flex\", \"align-items-center\"], [1, \"dropdown\"], [\"type\", \"button\", \"id\", \"dropdownMenuButton\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"dropdown-toggle\", \"user-dropdown\"], [1, \"fas\", \"fa-user-circle\", \"me-1\"], [1, \"dropdown-menu\", \"dropdown-menu-end\", \"shadow\"], [1, \"dropdown-item\", 3, \"click\"], [1, \"fas\", \"fa-user\", \"me-2\"], [1, \"fas\", \"fa-cog\", \"me-2\"], [1, \"dropdown-divider\"], [3, \"click\"], [1, \"dropdown-item\"], [1, \"fas\", \"fa-sign-out-alt\", \"me-2\"]],\n      template: function HeaderComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nav\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function HeaderComponent_Template_button_click_3_listener() {\n            return ctx.toggleSidebar.emit();\n          });\n          i0.ɵɵelement(4, \"i\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6)(7, \"button\", 7);\n          i0.ɵɵelement(8, \"i\", 8);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"ul\", 9)(11, \"li\")(12, \"a\", 10);\n          i0.ɵɵlistener(\"click\", function HeaderComponent_Template_a_click_12_listener() {\n            return ctx.showComingSoon();\n          });\n          i0.ɵɵelement(13, \"i\", 11);\n          i0.ɵɵtext(14, \"Profile\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"li\")(16, \"a\", 10);\n          i0.ɵɵlistener(\"click\", function HeaderComponent_Template_a_click_16_listener() {\n            return ctx.showComingSoon();\n          });\n          i0.ɵɵelement(17, \"i\", 12);\n          i0.ɵɵtext(18, \"Settings\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"li\");\n          i0.ɵɵelement(20, \"hr\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"li\", 14);\n          i0.ɵɵlistener(\"click\", function HeaderComponent_Template_li_click_21_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵelementStart(22, \"a\", 15);\n          i0.ɵɵelement(23, \"i\", 16);\n          i0.ɵɵtext(24, \"Logout\");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\" \", ctx.role, \" \");\n        }\n      },\n      styles: [\".navbar[_ngcontent-%COMP%]{background-color:#fff;box-shadow:0 2px 10px #00000014;padding:.8rem 1.5rem;position:sticky;top:0;z-index:1030;height:70px}.navbar[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#11418e!important;font-weight:600;font-family:Segoe UI,Tahoma,Geneva,Verdana,sans-serif}.toggle-sidebar-btn[_ngcontent-%COMP%]{background:none;border:none;color:#515154;font-size:1.2rem;cursor:pointer;padding:.5rem;border-radius:6px;display:none}.toggle-sidebar-btn[_ngcontent-%COMP%]:hover{background-color:#f0f4f9;color:#11418e}.user-dropdown[_ngcontent-%COMP%]{color:#515154;border:1px solid #e0e0e0;border-radius:8px;padding:.4rem 1rem;font-weight:500;transition:all .2s}.user-dropdown[_ngcontent-%COMP%]:hover{background-color:#f8f9fa;border-color:#d0d0d0}.dropdown-menu[_ngcontent-%COMP%]{border:none;border-radius:8px;box-shadow:0 4px 12px #0000001a;margin-top:.5rem;padding:.5rem}.dropdown-item[_ngcontent-%COMP%]{padding:.5rem 1rem;font-size:.9rem;color:#515154;border-radius:4px;margin:.2rem 0;transition:all .2s}.dropdown-item[_ngcontent-%COMP%]:hover{background-color:#f0f4f9;color:#11418e}.dropdown-divider[_ngcontent-%COMP%]{margin:.3rem 0;border-color:#eaeaea}@media (max-width: 991.98px){.toggle-sidebar-btn[_ngcontent-%COMP%]{display:block}.navbar[_ngcontent-%COMP%]{padding:.8rem 1rem}}a[_ngcontent-%COMP%]{cursor:pointer}\"]\n    });\n  }\n  return HeaderComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}