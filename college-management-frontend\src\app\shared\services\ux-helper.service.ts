import { Injectable } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import Swal from 'sweetalert2';
import { UX_TEXT_STANDARDS, ROLE_SPECIFIC_TEXT, CONFIRMATION_MESSAGES } from '../constants/ux-text-standards';

@Injectable({
  providedIn: 'root'
})
export class UxHelperService {

  constructor(private snackBar: MatSnackBar) { }

  /**
   * Show user-friendly success message with SweetAlert
   */
  showSuccessMessage(messageKey: keyof typeof UX_TEXT_STANDARDS.SUCCESS_MESSAGES, customMessage?: string): Promise<any> {
    const message = customMessage || UX_TEXT_STANDARDS.SUCCESS_MESSAGES[messageKey];
    return Swal.fire({
      title: 'Success!',
      text: message,
      icon: 'success',
      confirmButtonColor: '#29578c',
      confirmButtonText: 'Great!',
      timer: 3000,
      timerProgressBar: true
    });
  }

  /**
   * Show user-friendly error message with SweetAlert
   */
  showErrorMessage(messageKey: keyof typeof UX_TEXT_STANDARDS.ERROR_MESSAGES, customMessage?: string): Promise<any> {
    const message = customMessage || UX_TEXT_STANDARDS.ERROR_MESSAGES[messageKey];
    return Swal.fire({
      title: 'Oops!',
      text: message,
      icon: 'error',
      confirmButtonColor: '#29578c',
      confirmButtonText: 'I understand'
    });
  }

  /**
   * Show confirmation dialog with user-friendly text
   */
  showConfirmationDialog(
    messageKey: keyof typeof CONFIRMATION_MESSAGES, 
    customMessage?: string,
    confirmButtonText: string = 'Yes, proceed',
    cancelButtonText: string = 'Cancel'
  ): Promise<any> {
    const message = customMessage || CONFIRMATION_MESSAGES[messageKey];
    return Swal.fire({
      title: 'Please confirm',
      text: message,
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#29578c',
      cancelButtonColor: '#6c757d',
      confirmButtonText: confirmButtonText,
      cancelButtonText: cancelButtonText,
      reverseButtons: true
    });
  }

  /**
   * Show loading message with SweetAlert
   */
  showLoadingMessage(messageKey: keyof typeof UX_TEXT_STANDARDS.LOADING_MESSAGES, customMessage?: string): void {
    const message = customMessage || UX_TEXT_STANDARDS.LOADING_MESSAGES[messageKey];
    Swal.fire({
      title: 'Please wait...',
      text: message,
      allowOutsideClick: false,
      allowEscapeKey: false,
      showConfirmButton: false,
      didOpen: () => {
        Swal.showLoading();
      }
    });
  }

  /**
   * Close any open SweetAlert dialog
   */
  closeLoadingMessage(): void {
    Swal.close();
  }

  /**
   * Show snackbar notification with user-friendly styling
   */
  showSnackbar(message: string, action: string = 'Close', duration: number = 5000, type: 'success' | 'error' | 'info' = 'info'): void {
    this.snackBar.open(message, action, {
      duration: duration,
      panelClass: [`${type}-snackbar`],
      horizontalPosition: 'right',
      verticalPosition: 'top'
    });
  }

  /**
   * Get role-specific welcome message
   */
  getRoleSpecificText(role: string, textKey: keyof typeof ROLE_SPECIFIC_TEXT.PRINCIPAL): string {
    const roleUpper = role.toUpperCase() as keyof typeof ROLE_SPECIFIC_TEXT;
    return ROLE_SPECIFIC_TEXT[roleUpper]?.[textKey] || '';
  }

  /**
   * Get user-friendly form validation error message
   */
  getValidationErrorMessage(fieldName: string, errorType: string, customValue?: any): string {
    switch (errorType) {
      case 'required':
        return UX_TEXT_STANDARDS.ERROR_MESSAGES.REQUIRED_FIELD;
      case 'email':
        return UX_TEXT_STANDARDS.ERROR_MESSAGES.INVALID_EMAIL;
      case 'minlength':
        if (fieldName.toLowerCase().includes('password')) {
          return UX_TEXT_STANDARDS.ERROR_MESSAGES.PASSWORD_TOO_SHORT;
        }
        if (fieldName.toLowerCase().includes('name')) {
          return UX_TEXT_STANDARDS.ERROR_MESSAGES.NAME_TOO_SHORT;
        }
        return `This field must be at least ${customValue?.requiredLength || 2} characters long`;
      case 'pattern':
        if (fieldName.toLowerCase().includes('phone') || fieldName.toLowerCase().includes('contact')) {
          return UX_TEXT_STANDARDS.ERROR_MESSAGES.INVALID_PHONE;
        }
        return 'Please enter a valid format';
      case 'mismatch':
        return UX_TEXT_STANDARDS.ERROR_MESSAGES.PASSWORDS_DONT_MATCH;
      default:
        return 'Please check this field and try again';
    }
  }

  /**
   * Get tooltip text for form fields
   */
  getTooltipText(fieldKey: keyof typeof UX_TEXT_STANDARDS.TOOLTIPS): string {
    return UX_TEXT_STANDARDS.TOOLTIPS[fieldKey] || '';
  }

  /**
   * Get user-friendly label text
   */
  getLabelText(labelKey: keyof typeof UX_TEXT_STANDARDS.FORM_LABELS): string {
    return UX_TEXT_STANDARDS.FORM_LABELS[labelKey] || '';
  }

  /**
   * Get placeholder text
   */
  getPlaceholderText(placeholderKey: keyof typeof UX_TEXT_STANDARDS.PLACEHOLDERS): string {
    return UX_TEXT_STANDARDS.PLACEHOLDERS[placeholderKey] || '';
  }

  /**
   * Get button text
   */
  getButtonText(buttonKey: keyof typeof UX_TEXT_STANDARDS.BUTTONS): string {
    return UX_TEXT_STANDARDS.BUTTONS[buttonKey] || '';
  }

  /**
   * Show welcome message based on user role
   */
  showWelcomeMessage(userName: string, userRole: string): Promise<any> {
    const roleSpecificWelcome = this.getRoleSpecificText(userRole, 'DASHBOARD_WELCOME');
    return Swal.fire({
      title: `Welcome back, ${userName}!`,
      text: roleSpecificWelcome,
      icon: 'success',
      confirmButtonColor: '#29578c',
      confirmButtonText: 'Let\'s get started!',
      timer: 4000,
      timerProgressBar: true
    });
  }

  /**
   * Show attendance warning for students
   */
  showAttendanceWarning(attendancePercentage: number, subjectName?: string): Promise<any> {
    let message = '';
    let title = '';
    
    if (attendancePercentage < 75) {
      title = 'Attendance Alert!';
      message = subjectName 
        ? `Your attendance in ${subjectName} is ${attendancePercentage}%. You need at least 75% to be eligible for exams.`
        : `Your overall attendance is ${attendancePercentage}%. You need at least 75% to be eligible for exams.`;
    } else if (attendancePercentage < 80) {
      title = 'Attendance Notice';
      message = subjectName
        ? `Your attendance in ${subjectName} is ${attendancePercentage}%. Try to maintain above 80% for better academic standing.`
        : `Your overall attendance is ${attendancePercentage}%. Try to maintain above 80% for better academic standing.`;
    }

    return Swal.fire({
      title: title,
      text: message,
      icon: attendancePercentage < 75 ? 'warning' : 'info',
      confirmButtonColor: '#29578c',
      confirmButtonText: 'I understand'
    });
  }

  /**
   * Show struck-off warning for BS students
   */
  showStruckOffWarning(subjectName: string, consecutiveAbsences: number): Promise<any> {
    return Swal.fire({
      title: 'Important Notice!',
      text: `You have been absent for ${consecutiveAbsences} consecutive classes in ${subjectName}. You may be struck off from this subject. Please contact your teacher immediately.`,
      icon: 'warning',
      confirmButtonColor: '#d33',
      confirmButtonText: 'Contact Teacher',
      showCancelButton: true,
      cancelButtonText: 'I understand'
    });
  }
}
