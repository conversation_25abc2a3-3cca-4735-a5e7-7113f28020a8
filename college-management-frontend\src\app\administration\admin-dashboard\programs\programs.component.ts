import { Component, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ProgramService } from '../../../services/program.service';
import { Program } from '../../../models/user';
import { ProgramDialogComponent } from './program-dialog/program-dialog.component';

@Component({
  selector: 'app-programs',
  templateUrl: './programs.component.html',
  styleUrls: ['./programs.component.css']
})
export class ProgramsComponent implements OnInit {
  programs: Program[] = [];
  loading = true;
  error: string | null = null;

  displayedColumns: string[] = ['name', 'fullName', 'duration', 'totalSemesters', 'isActive', 'actions'];

  constructor(
    private programService: ProgramService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadPrograms();
  }

  loadPrograms(): void {
    this.loading = true;
    this.error = null;

    this.programService.getAllPrograms().subscribe({
      next: (response) => {
        if (response.success) {
          this.programs = response.programs;
        } else {
          this.error = 'Failed to load programs';
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading programs:', error);
        this.error = 'Error loading programs';
        this.loading = false;
      }
    });
  }

  createProgram(): void {
    const dialogRef = this.dialog.open(ProgramDialogComponent, {
      width: '600px',
      data: {}
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result?.success) {
        this.snackBar.open('Program created successfully', 'Close', {
          duration: 3000
        });
        this.loadPrograms();
      } else if (result?.error) {
        this.snackBar.open(result.error, 'Close', {
          duration: 3000
        });
      }
    });
  }

  editProgram(program: Program): void {
    const dialogRef = this.dialog.open(ProgramDialogComponent, {
      width: '600px',
      data: { program }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result?.success) {
        this.snackBar.open('Program updated successfully', 'Close', {
          duration: 3000
        });
        this.loadPrograms();
      } else if (result?.error) {
        this.snackBar.open(result.error, 'Close', {
          duration: 3000
        });
      }
    });
  }

  deleteProgram(program: Program): void {
    if (confirm(`Are you sure you want to delete the program "${program.name}"?`)) {
      this.programService.deleteProgram(program._id).subscribe({
        next: (response) => {
          if (response.success) {
            this.snackBar.open('Program deleted successfully', 'Close', {
              duration: 3000
            });
            this.loadPrograms();
          } else {
            this.snackBar.open('Failed to delete program', 'Close', {
              duration: 3000
            });
          }
        },
        error: (error) => {
          console.error('Error deleting program:', error);
          this.snackBar.open('Error deleting program', 'Close', {
            duration: 3000
          });
        }
      });
    }
  }

  toggleProgramStatus(program: Program): void {
    const updatedProgram = { ...program, isActive: !program.isActive };
    
    this.programService.updateProgram(program._id, updatedProgram).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open(
            `Program ${updatedProgram.isActive ? 'activated' : 'deactivated'} successfully`, 
            'Close', 
            { duration: 3000 }
          );
          this.loadPrograms();
        } else {
          this.snackBar.open('Failed to update program status', 'Close', {
            duration: 3000
          });
        }
      },
      error: (error) => {
        console.error('Error updating program status:', error);
        this.snackBar.open('Error updating program status', 'Close', {
          duration: 3000
        });
      }
    });
  }

  refreshData(): void {
    this.loadPrograms();
  }
}
