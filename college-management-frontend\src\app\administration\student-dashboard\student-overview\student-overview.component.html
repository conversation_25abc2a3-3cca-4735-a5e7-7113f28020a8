<div class="student-dashboard">
  <!-- Loading State -->
  <div *ngIf="loading" class="loading-container">
    <mat-spinner></mat-spinner>
    <p>Loading your dashboard...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !loading" class="error-container">
    <mat-icon color="warn">error</mat-icon>
    <p>{{ error }}</p>
    <button mat-raised-button color="primary" (click)="refreshData()">
      <mat-icon>refresh</mat-icon>
      Retry
    </button>
  </div>

  <!-- Dashboard Content -->
  <div *ngIf="studentDashboard && !loading && !error" class="dashboard-content">
    <!-- Header -->
    <div class="dashboard-header">
      <div class="welcome-section">
        <h1>Welcome, {{ currentUser?.name }}!</h1>
        <p>{{ studentDashboard.student.program?.name }} - {{ studentDashboard.student.department?.name }}</p>
        <p class="student-info">{{ studentDashboard.student.regNo }} | Semester {{ studentDashboard.student.semester }}</p>
      </div>
      <button mat-icon-button (click)="refreshData()" matTooltip="Refresh Data">
        <mat-icon>refresh</mat-icon>
      </button>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-grid">
      <mat-card class="stat-card attendance">
        <mat-card-content>
          <div class="stat-content">
            <div class="stat-icon">
              <mat-icon>fact_check</mat-icon>
            </div>
            <div class="stat-info">
              <h3>{{ getOverallAttendancePercentage() }}%</h3>
              <p>Overall Attendance</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card present">
        <mat-card-content>
          <div class="stat-content">
            <div class="stat-icon">
              <mat-icon>check_circle</mat-icon>
            </div>
            <div class="stat-info">
              <h3>{{ getPresentClasses() }}</h3>
              <p>Classes Attended</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card absent">
        <mat-card-content>
          <div class="stat-content">
            <div class="stat-icon">
              <mat-icon>cancel</mat-icon>
            </div>
            <div class="stat-info">
              <h3>{{ getAbsentClasses() }}</h3>
              <p>Classes Missed</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card total">
        <mat-card-content>
          <div class="stat-content">
            <div class="stat-icon">
              <mat-icon>school</mat-icon>
            </div>
            <div class="stat-info">
              <h3>{{ getTotalClasses() }}</h3>
              <p>Total Classes</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Today's Schedule and Subject Attendance -->
    <div class="content-section">
      <div class="content-row">
        <!-- Today's Classes -->
        <mat-card class="schedule-card">
          <mat-card-header>
            <mat-card-title>Today's Schedule</mat-card-title>
            <mat-card-subtitle>{{ currentDate | date:'fullDate' }}</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <div *ngIf="getTodayClasses().length > 0; else noClassesToday" class="classes-list">
              <div *ngFor="let classItem of getTodayClasses()" class="class-item">
                <div class="time-slot">
                  <span class="time">{{ classItem.timeSlot.startTime }}</span>
                  <span class="time">{{ classItem.timeSlot.endTime }}</span>
                </div>
                <div class="class-details">
                  <h4>{{ classItem.subject.subjectName }}</h4>
                  <p>{{ classItem.teacher.name }}</p>
                  <span class="room" *ngIf="classItem.room">{{ classItem.room }}</span>
                </div>
              </div>
            </div>
            <ng-template #noClassesToday>
              <div class="no-classes">
                <mat-icon>event_available</mat-icon>
                <p>No classes scheduled for today</p>
              </div>
            </ng-template>
          </mat-card-content>
        </mat-card>

        <!-- Subject-wise Attendance -->
        <mat-card class="attendance-card">
          <mat-card-header>
            <mat-card-title>Subject-wise Attendance</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div *ngIf="studentDashboard.subjectAttendance.length > 0; else noSubjectData" class="subject-attendance">
              <div *ngFor="let subject of studentDashboard.subjectAttendance" class="subject-item">
                <div class="subject-info">
                  <h4>{{ subject.subjectName }}</h4>
                  <p>{{ subject.present }}/{{ subject.total }} classes</p>
                </div>
                <div class="attendance-bar">
                  <div class="progress-bar">
                    <div class="progress-fill" [style.width.%]="subject.percentage"></div>
                  </div>
                  <span class="percentage">{{ subject.percentage | number:'1.0-0' }}%</span>
                </div>
              </div>
            </div>
            <ng-template #noSubjectData>
              <div class="no-data">
                <mat-icon>subject</mat-icon>
                <p>No attendance data available</p>
              </div>
            </ng-template>
          </mat-card-content>
        </mat-card>
      </div>
    </div>

    <!-- Recent Attendance History -->
    <div class="recent-section">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Recent Attendance</mat-card-title>
          <mat-card-subtitle>Last 10 attendance records</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <div *ngIf="studentDashboard.attendanceHistory.length > 0; else noRecentAttendance" class="attendance-history">
            <div *ngFor="let attendance of studentDashboard.attendanceHistory.slice(0, 10)" class="history-item">
              <div class="date-info">
                <span class="date">{{ attendance.date | date:'shortDate' }}</span>
                <span class="day">{{ attendance.date | date:'EEEE' }}</span>
              </div>
              <div class="subject-info">
                <span class="subject">{{ attendance.subject.subjectName }}</span>
                <span class="teacher">{{ attendance.teacher.name }}</span>
              </div>
              <div class="status-info">
                <mat-chip [color]="attendance.status === 'present' ? 'primary' : 'warn'" selected>
                  {{ attendance.status | titlecase }}
                </mat-chip>
              </div>
            </div>
          </div>
          <ng-template #noRecentAttendance>
            <div class="no-data">
              <mat-icon>history</mat-icon>
              <p>No attendance history available</p>
            </div>
          </ng-template>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
      <h2>Quick Actions</h2>
      <div class="action-buttons">
        <button mat-raised-button color="primary" routerLink="/dashboard/student/attendance">
          <mat-icon>fact_check</mat-icon>
          View Full Attendance
        </button>
        <button mat-raised-button color="accent" routerLink="/dashboard/student/timetable">
          <mat-icon>schedule</mat-icon>
          View Timetable
        </button>
        <button mat-raised-button color="primary" routerLink="/dashboard/student/teachers">
          <mat-icon>person</mat-icon>
          View Teachers
        </button>
      </div>
    </div>
  </div>
</div>
