const express = require('express');
const router = express.Router();

const {
    getPrincipalDashboardStats,
    getAllUsersForPrincipal,
    getAttendanceSummary,
    getTeacherDashboard,
    getStudentDashboard
} = require('../controllers/dashboard-controller.js');

const authToken = require('../middleware/authMiddleware');

// Principal Dashboard Routes (Protected)
router.get('/dashboard/principal/stats', authToken, getPrincipalDashboardStats);
router.get('/dashboard/principal/users', authToken, getAllUsersForPrincipal);
router.get('/dashboard/principal/attendance', authToken, getAttendanceSummary);

// Teacher Dashboard Routes (Protected)
router.get('/dashboard/teacher/:teacherId', authToken, getTeacherDashboard);

// Student Dashboard Routes (Protected)
router.get('/dashboard/student/:studentId', authToken, getStudentDashboard);

module.exports = router;
