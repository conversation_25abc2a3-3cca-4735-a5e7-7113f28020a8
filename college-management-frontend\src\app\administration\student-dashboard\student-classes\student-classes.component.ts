import { Component, OnInit } from '@angular/core';
import { UserService } from '../../../services/user.service';
import { DashboardService } from '../../../services/dashboard.service';

@Component({
  selector: 'app-student-classes',
  templateUrl: './student-classes.component.html',
  styleUrls: ['./student-classes.component.css']
})
export class StudentClassesComponent implements OnInit {
  currentUser: any;
  studentDashboard: any;
  loading = false;
  error: string | null = null;

  constructor(
    private userService: UserService,
    private dashboardService: DashboardService
  ) {}

  ngOnInit(): void {
    this.currentUser = this.userService.getUserFromLocalStorage()?.user;
    this.loadStudentClasses();
  }

  loadStudentClasses(): void {
    if (!this.currentUser) return;

    this.loading = true;
    this.error = null;

    this.dashboardService.getStudentDashboard(this.currentUser._id).subscribe({
      next: (response) => {
        if (response.success) {
          this.studentDashboard = response.dashboard;
        } else {
          this.error = 'Failed to load student classes';
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading student classes:', error);
        this.error = 'Error loading classes data';
        this.loading = false;
      }
    });
  }

  refreshData(): void {
    this.loadStudentClasses();
  }
}
