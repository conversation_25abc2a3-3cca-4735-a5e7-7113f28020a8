{"ast": null, "code": "import { BehaviorSubject, tap, catchError } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nconst USER_KEY = 'user';\nconst TOKEN_KEY = 'token';\nexport let AuthService = /*#__PURE__*/(() => {\n  class AuthService {\n    constructor(http) {\n      this.http = http;\n      this.userSubject = new BehaviorSubject(this.getUserFromLocalStorage());\n      this.user$ = this.userSubject.asObservable();\n      this.apiUrl = environment.apiUrl;\n    }\n    // Signup\n    signup(userData) {\n      return this.http.post(`${this.apiUrl}/signup`, userData).pipe(tap({\n        next: response => {\n          if (response.success) {\n            this.setUserInLocalStorage(response);\n            this.userSubject.next(response);\n          }\n        }\n      }), catchError(error => {\n        throw error;\n      }));\n    }\n    // Login\n    login(credentials) {\n      return this.http.post(`${this.apiUrl}/login`, credentials).pipe(tap({\n        next: response => {\n          if (response.success) {\n            this.setUserInLocalStorage(response);\n            this.userSubject.next(response);\n          }\n        }\n      }), catchError(error => {\n        throw error;\n      }));\n    }\n    // Forgot Password - Send OTP\n    forgotPassword(email) {\n      return this.http.post(`${this.apiUrl}/forgot-password`, {\n        email\n      }).pipe(tap({\n        next: response => {\n          console.log('OTP sent successfully');\n        }\n      }), catchError(error => {\n        throw error;\n      }));\n    }\n    // Verify OTP\n    verifyOtp(userId, otp) {\n      return this.http.post(`${this.apiUrl}/verify-otp`, {\n        userId,\n        otp\n      }).pipe(tap({\n        next: response => {\n          console.log('OTP verified successfully');\n        }\n      }), catchError(error => {\n        throw error;\n      }));\n    }\n    // Resend OTP\n    resendOtp(data) {\n      return this.http.post(`${this.apiUrl}/resend-otp`, data).pipe(tap({\n        next: response => {\n          console.log('OTP resent successfully');\n        }\n      }), catchError(error => {\n        throw error;\n      }));\n    }\n    // Reset Password\n    resetPassword(userId, password) {\n      return this.http.post(`${this.apiUrl}/reset-password`, {\n        userId,\n        password\n      }).pipe(tap({\n        next: response => {\n          console.log('Password reset successfully');\n        }\n      }), catchError(error => {\n        throw error;\n      }));\n    }\n    // Get all users\n    getAllUsers() {\n      return this.http.get(`${this.apiUrl}/allUser`).pipe(catchError(error => {\n        throw error;\n      }));\n    }\n    // Get users by role\n    getUsersByRole(role) {\n      return this.http.get(`${this.apiUrl}/api/users/${role}`).pipe(catchError(error => {\n        throw error;\n      }));\n    }\n    // Delete user\n    deleteUser(userId) {\n      return this.http.delete(`${this.apiUrl}/deleteUser/${userId}`).pipe(catchError(error => {\n        throw error;\n      }));\n    }\n    // Logout\n    logout() {\n      localStorage.removeItem(USER_KEY);\n      localStorage.removeItem(TOKEN_KEY);\n      this.userSubject.next(null);\n    }\n    // Check if user is authenticated\n    isAuthenticated() {\n      return !!this.getToken();\n    }\n    // Get current user\n    getCurrentUser() {\n      return this.userSubject.value;\n    }\n    // Get token\n    getToken() {\n      return localStorage.getItem(TOKEN_KEY);\n    }\n    // Private helper methods\n    setUserInLocalStorage(response) {\n      localStorage.setItem(USER_KEY, JSON.stringify(response.user || response));\n      if (response.token) {\n        localStorage.setItem(TOKEN_KEY, response.token);\n      }\n    }\n    getUserFromLocalStorage() {\n      const userJson = localStorage.getItem(USER_KEY);\n      if (userJson) {\n        return JSON.parse(userJson);\n      }\n      return null;\n    }\n    static #_ = this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return AuthService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}