import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from 'src/app/services/auth.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-verify-otp',
  templateUrl: './verify-otp.component.html',
  styleUrls: ['./verify-otp.component.css']
})
export class VerifyOtpComponent implements OnInit {
  otpForm!: FormGroup;
  loading = false;
  resendLoading = false;
  user: any = null;
  otpId: string | null = null;
  countdown = 0;
  countdownInterval: any;

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Get user data from localStorage (set during forgot password)
    const resetUser = localStorage.getItem('resetUser');
    
    if (resetUser) {
      this.user = JSON.parse(resetUser);
    } else {
      // If no user data, redirect to forgot password
      Swal.fire({
        title: 'Session Expired',
        text: 'Please start the password reset process again.',
        icon: 'warning',
        confirmButtonColor: '#29578c'
      }).then(() => {
        this.router.navigate(['/auth/forgot-password']);
      });
      return;
    }

    this.initializeForm();
    this.startCountdown();
  }

  ngOnDestroy(): void {
    if (this.countdownInterval) {
      clearInterval(this.countdownInterval);
    }
  }

  initializeForm(): void {
    this.otpForm = this.fb.group({
      digit1: ['', [Validators.required, Validators.pattern(/^\d$/)]],
      digit2: ['', [Validators.required, Validators.pattern(/^\d$/)]],
      digit3: ['', [Validators.required, Validators.pattern(/^\d$/)]],
      digit4: ['', [Validators.required, Validators.pattern(/^\d$/)]],
      digit5: ['', [Validators.required, Validators.pattern(/^\d$/)]],
      digit6: ['', [Validators.required, Validators.pattern(/^\d$/)]],
    });
  }

  startCountdown(): void {
    this.countdown = 60; // 60 seconds countdown
    this.countdownInterval = setInterval(() => {
      this.countdown--;
      if (this.countdown <= 0) {
        clearInterval(this.countdownInterval);
      }
    }, 1000);
  }

  onDigitInput(event: any, nextInput: string): void {
    const input = event.target;
    const value = input.value;

    if (value && /^\d$/.test(value)) {
      // Move to next input
      const nextElement = document.getElementById(nextInput) as HTMLInputElement;
      if (nextElement) {
        nextElement.focus();
      }
    }
  }

  onDigitKeydown(event: any, prevInput: string): void {
    const input = event.target;
    
    if (event.key === 'Backspace' && !input.value && prevInput) {
      // Move to previous input on backspace
      const prevElement = document.getElementById(prevInput) as HTMLInputElement;
      if (prevElement) {
        prevElement.focus();
      }
    }
  }

  onPaste(event: ClipboardEvent): void {
    event.preventDefault();
    const pastedData = event.clipboardData?.getData('text') || '';
    
    if (/^\d{6}$/.test(pastedData)) {
      // Valid 6-digit OTP pasted
      const digits = pastedData.split('');
      this.otpForm.patchValue({
        digit1: digits[0],
        digit2: digits[1],
        digit3: digits[2],
        digit4: digits[3],
        digit5: digits[4],
        digit6: digits[5]
      });
      
      // Focus on last input
      const lastInput = document.getElementById('digit6') as HTMLInputElement;
      if (lastInput) {
        lastInput.focus();
      }
    }
  }

  getOtpValue(): string {
    const formValue = this.otpForm.value;
    return `${formValue.digit1}${formValue.digit2}${formValue.digit3}${formValue.digit4}${formValue.digit5}${formValue.digit6}`;
  }

  verifyOtp(): void {
    if (this.otpForm.valid && this.user) {
      this.loading = true;
      const otp = this.getOtpValue();

      this.authService.verifyOtp(this.user._id, otp).subscribe({
        next: (response) => {
          this.loading = false;
          if (response.success) {
            // Store verified user data for password reset
            localStorage.setItem('verifiedUser', JSON.stringify(this.user));
            
            Swal.fire({
              title: 'OTP Verified!',
              text: 'Your OTP has been successfully verified. You can now reset your password.',
              icon: 'success',
              confirmButtonText: 'Reset Password',
              confirmButtonColor: '#29578c'
            }).then((result) => {
              if (result.isConfirmed) {
                this.router.navigate(['/auth/reset-password']);
              }
            });
          }
        },
        error: (error) => {
          this.loading = false;
          console.error('OTP verification error:', error);
          
          const errorMessage = error.error?.error || error.error?.message || 'Invalid OTP. Please try again.';
          
          Swal.fire({
            title: 'Verification Failed',
            text: errorMessage,
            icon: 'error',
            confirmButtonColor: '#29578c'
          });

          // Clear the form on error
          this.otpForm.reset();
          const firstInput = document.getElementById('digit1') as HTMLInputElement;
          if (firstInput) {
            firstInput.focus();
          }
        }
      });
    } else {
      Swal.fire({
        title: 'Invalid OTP',
        text: 'Please enter all 6 digits of the OTP.',
        icon: 'warning',
        confirmButtonColor: '#29578c'
      });
    }
  }

  resendOtp(): void {
    if (!this.user || this.countdown > 0) return;

    this.resendLoading = true;
    const resendData = {
      userId: this.user._id,
      ...(this.otpId && { otpId: this.otpId })
    };

    this.authService.resendOtp(resendData).subscribe({
      next: (response) => {
        this.resendLoading = false;
        if (response.success) {
          if (response.otpId) {
            this.otpId = response.otpId;
          }
          
          Swal.fire({
            title: 'OTP Resent!',
            text: 'A new OTP has been sent to your email.',
            icon: 'success',
            confirmButtonColor: '#29578c'
          });

          // Restart countdown
          this.startCountdown();
          
          // Clear the form
          this.otpForm.reset();
          const firstInput = document.getElementById('digit1') as HTMLInputElement;
          if (firstInput) {
            firstInput.focus();
          }
        }
      },
      error: (error) => {
        this.resendLoading = false;
        console.error('Resend OTP error:', error);
        
        const errorMessage = error.error?.message || 'Failed to resend OTP. Please try again.';
        
        Swal.fire({
          title: 'Resend Failed',
          text: errorMessage,
          icon: 'error',
          confirmButtonColor: '#29578c'
        });
      }
    });
  }

  goBack(): void {
    this.router.navigate(['/auth/forgot-password']);
  }
}
