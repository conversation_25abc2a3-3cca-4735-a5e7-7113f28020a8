import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { UserService } from '../services/user.service';
import { RoleService } from '../services/role.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {

  constructor(
    private userService: UserService,
    private roleService: RoleService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    
    // Check if user is authenticated
    if (!this.userService.isAuthenticated()) {
      this.router.navigate(['/auth/login']);
      return false;
    }

    // Get required roles from route data
    const requiredRoles = route.data['roles'] as Array<string>;
    
    if (requiredRoles) {
      const currentUser = this.userService.getUserFromLocalStorage();
      const userRole = currentUser?.user?.role;

      // Check if user has required role
      if (requiredRoles.includes(userRole)) {
        return true;
      } else {
        // Redirect to appropriate dashboard based on user role
        this.redirectBasedOnRole(userRole);
        return false;
      }
    }

    return true;
  }

  private redirectBasedOnRole(role: string): void {
    switch (role) {
      case 'Principal':
        this.router.navigate(['/dashboard/admin']);
        break;
      case 'Teacher':
        this.router.navigate(['/dashboard/teacher']);
        break;
      case 'Student':
        this.router.navigate(['/dashboard/student']);
        break;
      default:
        this.router.navigate(['/auth/login']);
        break;
    }
  }
}
