const mongoose = require('mongoose');
const User = require('../models/userModel.js');
const Program = require('../models/program.js');
const Department = require('../models/department.js');
const Class = require('../models/classes.js');
const Subject = require('../models/subject.js');
const Attendance = require('../models/attendance.js');
const Timetable = require('../models/timetable.js');

// Principal Dashboard Statistics
const getPrincipalDashboardStats = async (req, res) => {
    try {
        // Get total counts
        const totalStudents = await User.countDocuments({ role: 'Student', isActive: true });
        const totalTeachers = await User.countDocuments({ role: 'Teacher', isActive: true });
        const totalPrograms = await Program.countDocuments({ isActive: true });
        const totalDepartments = await Department.countDocuments({ isActive: true });
        const totalClasses = await Class.countDocuments({ isActive: true });
        const totalSubjects = await Subject.countDocuments({ isActive: true });

        // Get program-wise statistics
        const programStats = await User.aggregate([
            { $match: { role: 'Student', isActive: true } },
            { $group: { _id: '$program', count: { $sum: 1 } } },
            { $lookup: { from: 'programs', localField: '_id', foreignField: '_id', as: 'program' } },
            { $unwind: '$program' },
            { $project: { programName: '$program.name', studentCount: '$count' } }
        ]);

        // Get department-wise statistics
        const departmentStats = await User.aggregate([
            { $match: { role: 'Student', isActive: true } },
            { $group: { _id: '$department', count: { $sum: 1 } } },
            { $lookup: { from: 'departments', localField: '_id', foreignField: '_id', as: 'department' } },
            { $unwind: '$department' },
            { $project: { departmentName: '$department.name', studentCount: '$count' } }
        ]);

        // Get recent attendance summary (last 7 days)
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

        const attendanceStats = await Attendance.aggregate([
            { $match: { date: { $gte: sevenDaysAgo } } },
            { $group: { 
                _id: '$status', 
                count: { $sum: 1 } 
            }}
        ]);

        // Get semester-wise student distribution
        const semesterStats = await User.aggregate([
            { $match: { role: 'Student', isActive: true } },
            { $group: { _id: '$semester', count: { $sum: 1 } } },
            { $sort: { _id: 1 } }
        ]);

        res.status(200).json({
            success: true,
            stats: {
                totals: {
                    students: totalStudents,
                    teachers: totalTeachers,
                    programs: totalPrograms,
                    departments: totalDepartments,
                    classes: totalClasses,
                    subjects: totalSubjects
                },
                programStats,
                departmentStats,
                attendanceStats,
                semesterStats
            }
        });
    } catch (error) {
        console.error('Error fetching principal dashboard stats:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

// Get all users with filters for Principal
const getAllUsersForPrincipal = async (req, res) => {
    try {
        const { 
            role, 
            program, 
            department, 
            semester, 
            isActive, 
            page = 1, 
            limit = 10,
            search 
        } = req.query;

        const filter = {};
        
        if (role) filter.role = role;
        if (program) filter.program = program;
        if (department) filter.department = department;
        if (semester) filter.semester = parseInt(semester);
        if (isActive !== undefined) filter.isActive = isActive === 'true';

        // Add search functionality
        if (search) {
            filter.$or = [
                { name: { $regex: search, $options: 'i' } },
                { email: { $regex: search, $options: 'i' } },
                { regNo: { $regex: search, $options: 'i' } },
                { rollNo: { $regex: search, $options: 'i' } }
            ];
        }

        const skip = (parseInt(page) - 1) * parseInt(limit);

        const users = await User.find(filter)
            .populate('program')
            .populate('department')
            .populate('classId')
            .populate('subjects')
            .select('-password')
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(parseInt(limit));

        const totalUsers = await User.countDocuments(filter);
        const totalPages = Math.ceil(totalUsers / parseInt(limit));

        res.status(200).json({
            success: true,
            users,
            pagination: {
                currentPage: parseInt(page),
                totalPages,
                totalUsers,
                hasNext: parseInt(page) < totalPages,
                hasPrev: parseInt(page) > 1
            }
        });
    } catch (error) {
        console.error('Error fetching users for principal:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

// Get attendance summary with filters
const getAttendanceSummary = async (req, res) => {
    try {
        const {
            program,
            department,
            class: classId,
            subject,
            startDate,
            endDate,
            page = 1,
            limit = 10
        } = req.query;

        const filter = {};
        
        if (program) {
            // Get classes for the program
            const programClasses = await Class.find({ program });
            const classIds = programClasses.map(c => c._id);
            filter.class = { $in: classIds };
        }
        
        if (department) {
            const departmentClasses = await Class.find({ department });
            const classIds = departmentClasses.map(c => c._id);
            filter.class = { $in: classIds };
        }
        
        if (classId) filter.class = classId;
        if (subject) filter.subject = subject;
        
        if (startDate && endDate) {
            filter.date = {
                $gte: new Date(startDate),
                $lte: new Date(endDate)
            };
        }

        const skip = (parseInt(page) - 1) * parseInt(limit);

        const attendanceRecords = await Attendance.find(filter)
            .populate({
                path: 'student',
                select: 'name email regNo rollNo',
                populate: [
                    { path: 'program', select: 'name' },
                    { path: 'department', select: 'name' }
                ]
            })
            .populate({
                path: 'class',
                select: 'className section semester',
                populate: [
                    { path: 'program', select: 'name' },
                    { path: 'department', select: 'name' }
                ]
            })
            .populate('subject', 'subjectName code')
            .populate('teacher', 'name email')
            .sort({ date: -1 })
            .skip(skip)
            .limit(parseInt(limit));

        const totalRecords = await Attendance.countDocuments(filter);
        const totalPages = Math.ceil(totalRecords / parseInt(limit));

        // Get attendance statistics
        const attendanceStats = await Attendance.aggregate([
            { $match: filter },
            { $group: { 
                _id: '$status', 
                count: { $sum: 1 } 
            }}
        ]);

        res.status(200).json({
            success: true,
            attendanceRecords,
            stats: attendanceStats,
            pagination: {
                currentPage: parseInt(page),
                totalPages,
                totalRecords,
                hasNext: parseInt(page) < totalPages,
                hasPrev: parseInt(page) > 1
            }
        });
    } catch (error) {
        console.error('Error fetching attendance summary:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

// Teacher Dashboard - Get assigned classes and subjects
const getTeacherDashboard = async (req, res) => {
    try {
        const { teacherId } = req.params;
        const { academicYear } = req.query;

        // Get teacher's assigned timetable
        const filter = { teacher: teacherId, isActive: true };
        if (academicYear) filter.academicYear = academicYear;

        const timetableEntries = await Timetable.find(filter)
            .populate([
                { path: 'program', select: 'name' },
                { path: 'department', select: 'name' },
                { path: 'class', select: 'className section semester' },
                { path: 'subject', select: 'subjectName code creditHours' }
            ])
            .sort({ dayOfWeek: 1, 'timeSlot.startTime': 1 });

        // Get unique classes and subjects
        const uniqueClasses = [...new Map(
            timetableEntries.map(entry => [entry.class._id.toString(), entry.class])
        ).values()];

        const uniqueSubjects = [...new Map(
            timetableEntries.map(entry => [entry.subject._id.toString(), entry.subject])
        ).values()];

        // Get students in teacher's classes
        const classIds = uniqueClasses.map(c => c._id);
        const students = await User.find({
            classId: { $in: classIds },
            role: 'Student',
            isActive: true
        })
        .populate('program', 'name')
        .populate('department', 'name')
        .populate('classId', 'className section')
        .select('name email regNo rollNo semester');

        // Get recent attendance for teacher's classes
        const recentAttendance = await Attendance.find({
            teacher: teacherId,
            date: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } // Last 7 days
        })
        .populate('student', 'name regNo')
        .populate('subject', 'subjectName')
        .sort({ date: -1 })
        .limit(20);

        res.status(200).json({
            success: true,
            dashboard: {
                timetable: timetableEntries,
                classes: uniqueClasses,
                subjects: uniqueSubjects,
                students,
                recentAttendance,
                stats: {
                    totalClasses: uniqueClasses.length,
                    totalSubjects: uniqueSubjects.length,
                    totalStudents: students.length
                }
            }
        });
    } catch (error) {
        console.error('Error fetching teacher dashboard:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

// Student Dashboard - Get student's information and attendance
const getStudentDashboard = async (req, res) => {
    try {
        const { studentId } = req.params;
        const { academicYear } = req.query;

        // Get student information
        const student = await User.findById(studentId)
            .populate('program')
            .populate('department')
            .populate('classId')
            .select('-password');

        if (!student || student.role !== 'Student') {
            return res.status(404).json({
                success: false,
                message: 'Student not found'
            });
        }

        // Get student's class timetable
        const filter = { class: student.classId, isActive: true };
        if (academicYear) filter.academicYear = academicYear;

        const timetable = await Timetable.find(filter)
            .populate([
                { path: 'subject', select: 'subjectName code creditHours' },
                { path: 'teacher', select: 'name email' }
            ])
            .sort({ dayOfWeek: 1, 'timeSlot.startTime': 1 });

        // Get student's attendance history
        const attendanceHistory = await Attendance.find({
            student: studentId
        })
        .populate('subject', 'subjectName code')
        .populate('teacher', 'name')
        .sort({ date: -1 })
        .limit(50);

        // Calculate attendance statistics
        const attendanceStats = await Attendance.aggregate([
            { $match: { student: new mongoose.Types.ObjectId(studentId) } },
            { $group: {
                _id: '$status',
                count: { $sum: 1 }
            }}
        ]);

        // Subject-wise attendance
        const subjectAttendance = await Attendance.aggregate([
            { $match: { student: new mongoose.Types.ObjectId(studentId) } },
            { $group: {
                _id: { subject: '$subject', status: '$status' },
                count: { $sum: 1 }
            }},
            { $lookup: {
                from: 'subjects',
                localField: '_id.subject',
                foreignField: '_id',
                as: 'subject'
            }},
            { $unwind: '$subject' },
            { $group: {
                _id: '$_id.subject',
                subjectName: { $first: '$subject.subjectName' },
                present: {
                    $sum: { $cond: [{ $eq: ['$_id.status', 'present'] }, '$count', 0] }
                },
                absent: {
                    $sum: { $cond: [{ $eq: ['$_id.status', 'absent'] }, '$count', 0] }
                },
                total: { $sum: '$count' }
            }},
            { $addFields: {
                percentage: {
                    $multiply: [
                        { $divide: ['$present', '$total'] },
                        100
                    ]
                }
            }}
        ]);

        res.status(200).json({
            success: true,
            dashboard: {
                student,
                timetable,
                attendanceHistory,
                attendanceStats,
                subjectAttendance
            }
        });
    } catch (error) {
        console.error('Error fetching student dashboard:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

module.exports = {
    getPrincipalDashboardStats,
    getAllUsersForPrincipal,
    getAttendanceSummary,
    getTeacherDashboard,
    getStudentDashboard
};
