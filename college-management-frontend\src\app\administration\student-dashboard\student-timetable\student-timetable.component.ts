import { Component, OnInit } from '@angular/core';
import { UserService } from '../../../services/user.service';
import { DashboardService } from '../../../services/dashboard.service';
import { TimetableService } from '../../../services/timetable.service';
import { MatSnackBar } from '@angular/material/snack-bar';

@Component({
  selector: 'app-student-timetable',
  templateUrl: './student-timetable.component.html',
  styleUrls: ['./student-timetable.component.css']
})
export class StudentTimetableComponent implements OnInit {
  currentUser: any;
  studentDashboard: any;
  loading = false;
  error: string | null = null;
  
  // Sample timetable data - in a real app, this would come from the backend
  timetableData: any[] = [];
  timeSlots = [
    '9:00 AM - 10:00 AM',
    '10:00 AM - 11:00 AM', 
    '11:00 AM - 12:00 PM',
    '12:00 PM - 1:00 PM',
    '1:00 PM - 2:00 PM',
    '2:00 PM - 3:00 PM',
    '3:00 PM - 4:00 PM',
    '4:00 PM - 5:00 PM'
  ];
  
  weekDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];

  constructor(
    private userService: UserService,
    private dashboardService: DashboardService,
    private timetableService: TimetableService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.currentUser = this.userService.getUserFromLocalStorage()?.user;
    this.loadStudentTimetable();
  }

  loadStudentTimetable(): void {
    if (!this.currentUser) return;

    this.loading = true;
    this.error = null;

    // Load both dashboard data and real timetable
    this.dashboardService.getStudentDashboard(this.currentUser._id).subscribe({
      next: (response) => {
        if (response.success) {
          this.studentDashboard = response.dashboard;
          this.loadRealTimetable();
        } else {
          this.error = 'Failed to load student dashboard';
          this.loading = false;
        }
      },
      error: (error) => {
        console.error('Error loading student dashboard:', error);
        this.error = 'Error loading dashboard data';
        this.loading = false;
      }
    });
  }

  loadRealTimetable(): void {
    this.timetableService.getTimetableByStudent(this.currentUser._id, '2024-2025').subscribe({
      next: (response) => {
        this.loading = false;
        if (response.success) {
          this.generateTimetableFromData(response.timetable);
        } else {
          // Fallback to sample timetable if no real data
          this.generateSampleTimetable();
        }
      },
      error: (error) => {
        console.error('Error loading real timetable:', error);
        this.loading = false;
        // Fallback to sample timetable
        this.generateSampleTimetable();
        this.snackBar.open('Using sample timetable data', 'Close', { duration: 3000 });
      }
    });
  }

  generateTimetableFromData(timetableEntries: any[]): void {
    this.timetableData = this.timeSlots.map((timeSlot) => {
      const slot: any = { time: timeSlot };

      this.weekDays.forEach((day) => {
        // Find matching timetable entry for this day and time
        const entry = timetableEntries.find(entry =>
          entry.dayOfWeek.toLowerCase() === day.toLowerCase() &&
          entry.timeSlot.startTime === timeSlot.split(' - ')[0]
        );

        if (entry) {
          slot[day.toLowerCase()] = {
            subject: entry.subject.subjectName,
            teacher: entry.teacher.name,
            room: entry.room || 'TBA',
            type: 'Lecture',
            code: entry.subject.code,
            program: entry.program.name,
            semester: entry.semester
          };
        } else {
          slot[day.toLowerCase()] = null;
        }
      });

      return slot;
    });
  }

  generateSampleTimetable(): void {
    // Generate sample timetable based on subjects
    if (this.studentDashboard?.subjectAttendance) {
      const subjects = this.studentDashboard.subjectAttendance;
      this.timetableData = this.timeSlots.map((timeSlot, timeIndex) => {
        const slot: any = { time: timeSlot };
        
        this.weekDays.forEach((day, dayIndex) => {
          // Simple logic to assign subjects to time slots
          const subjectIndex = (timeIndex + dayIndex) % subjects.length;
          if (subjects[subjectIndex] && timeIndex < 6) { // Skip lunch time and last slot sometimes
            slot[day.toLowerCase()] = {
              subject: subjects[subjectIndex].subjectName,
              teacher: 'Teacher ' + (subjectIndex + 1),
              room: 'Room ' + (101 + subjectIndex),
              type: timeIndex % 3 === 0 ? 'Lecture' : timeIndex % 3 === 1 ? 'Lab' : 'Tutorial'
            };
          } else if (timeIndex === 4) { // Lunch break
            slot[day.toLowerCase()] = {
              subject: 'Lunch Break',
              teacher: '',
              room: 'Cafeteria',
              type: 'Break',
              isBreak: true
            };
          } else {
            slot[day.toLowerCase()] = null;
          }
        });
        
        return slot;
      });
    }
  }

  refreshData(): void {
    this.loadStudentTimetable();
  }

  getCurrentDay(): string {
    const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    return days[new Date().getDay()];
  }

  getCurrentDate(): Date {
    return new Date();
  }

  isCurrentTimeSlot(timeSlot: string): boolean {
    const now = new Date();
    const currentHour = now.getHours();
    const slotHour = parseInt(timeSlot.split(':')[0]);
    
    // Simple check if current hour matches slot hour
    return currentHour === slotHour || (slotHour === 12 && currentHour === 0);
  }
}
