import { Component, OnInit } from '@angular/core';
import { UserService } from '../../../services/user.service';
import { DashboardService } from '../../../services/dashboard.service';

@Component({
  selector: 'app-student-subjects',
  templateUrl: './student-subjects.component.html',
  styleUrls: ['./student-subjects.component.css']
})
export class StudentSubjectsComponent implements OnInit {
  currentUser: any;
  studentDashboard: any;
  loading = false;
  error: string | null = null;

  constructor(
    private userService: UserService,
    private dashboardService: DashboardService
  ) {}

  ngOnInit(): void {
    this.currentUser = this.userService.getUserFromLocalStorage()?.user;
    this.loadStudentSubjects();
  }

  loadStudentSubjects(): void {
    if (!this.currentUser) return;

    this.loading = true;
    this.error = null;

    this.dashboardService.getStudentDashboard(this.currentUser._id).subscribe({
      next: (response) => {
        if (response.success) {
          this.studentDashboard = response.dashboard;
        } else {
          this.error = 'Failed to load student subjects';
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading student subjects:', error);
        this.error = 'Error loading subjects data';
        this.loading = false;
      }
    });
  }

  refreshData(): void {
    this.loadStudentSubjects();
  }

  getAttendanceStatus(percentage: number): string {
    if (percentage >= 75) return 'good';
    if (percentage >= 60) return 'warning';
    return 'danger';
  }

  getAttendanceMessage(percentage: number): string {
    if (percentage >= 75) return 'Good Attendance';
    if (percentage >= 60) return 'Needs Improvement';
    return 'Poor Attendance';
  }

  calculateOverallPercentage(): number {
    if (!this.studentDashboard?.subjectAttendance?.length) return 0;

    const totalPresent = this.studentDashboard.subjectAttendance.reduce((sum: number, subject: any) => sum + subject.present, 0);
    const totalClasses = this.studentDashboard.subjectAttendance.reduce((sum: number, subject: any) => sum + subject.total, 0);

    return totalClasses > 0 ? Math.round((totalPresent / totalClasses) * 100) : 0;
  }
}
