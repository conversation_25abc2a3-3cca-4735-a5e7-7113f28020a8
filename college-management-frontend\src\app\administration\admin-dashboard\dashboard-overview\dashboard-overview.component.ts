import { Component, OnInit, AfterViewInit, OnDestroy, ViewChild, ElementRef } from '@angular/core';
import { DashboardService } from '../../../services/dashboard.service';
import { Chart, registerables } from 'chart.js';
import { DashboardStats } from '../../../models/dashboard';

@Component({
  selector: 'app-dashboard-overview',
  templateUrl: './dashboard-overview.component.html',
  styleUrls: ['./dashboard-overview.component.css']
})
export class DashboardOverviewComponent implements OnInit, AfterViewInit, OnDestroy {
  dashboardStats: DashboardStats | null = null;
  loading = true;
  error: string | null = null;

  // Chart references
  @ViewChild('programChart') programChartRef!: ElementRef<HTMLCanvasElement>;
  @ViewChild('departmentChart') departmentChartRef!: ElementRef<HTMLCanvasElement>;
  @ViewChild('attendanceChart') attendanceChartRef!: ElementRef<HTMLCanvasElement>;
  @ViewChild('attendanceTrendChart') attendanceTrendChartRef!: ElementRef<HTMLCanvasElement>;
  @ViewChild('attendanceComparisonChart') attendanceComparisonChartRef!: ElementRef<HTMLCanvasElement>;
  @ViewChild('semesterChart') semesterChartRef!: ElementRef<HTMLCanvasElement>;

  // Chart instances
  // Update your chart instance declarations
  programChart: Chart<'doughnut', number[], string> | null = null;
  departmentChart: Chart<'bar', number[], string> | null = null;
  attendanceChart: Chart<'doughnut', number[], string> | null = null;
  attendanceTrendChart: Chart<'line', number[], string> | null = null;
  attendanceComparisonChart: Chart<'bar', number[], string> | null = null;
  semesterChart: Chart<'bar', number[], string> | null = null;
  // Color palettes
  private programColors = [
    '#4E79A7', '#F28E2B', '#E15759', '#76B7B2',
    '#59A14F', '#EDC948', '#B07AA1', '#FF9DA7'
  ];

  private departmentColors = [
    '#4285F4', '#34A853', '#EA4335', '#FBBC05',
    '#673AB7', '#FF5722', '#009688', '#795548'
  ];

  private attendanceColors: { [key: string]: string } = {
    'present': '#4CAF50',
    'absent': '#F44336',
    'late': '#FFC107',
    'excused': '#9C27B0',
    'sick': '#2196F3'
  };

  constructor(private dashboardService: DashboardService) {
    Chart.register(...registerables);
  }

  ngOnInit(): void {
    this.loadDashboardStats();
  }

  ngAfterViewInit(): void {
    // Charts will be initialized after data is loaded
  }

  ngOnDestroy(): void {
    this.destroyCharts();
  }

  loadDashboardStats(): void {
    this.loading = true;
    this.error = null;

    this.dashboardService.getPrincipalDashboardStats().subscribe({
      next: (response) => {
        if (response.success) {
          this.dashboardStats = response.stats;
          this.prepareChartData();
        } else {
          this.error = 'Failed to load dashboard statistics';
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading dashboard stats:', error);
        this.error = 'Error loading dashboard statistics';
        this.loading = false;
      }
    });
  }

  private prepareChartData(): void {
    if (!this.dashboardStats) return;

    this.destroyCharts();

    setTimeout(() => {
      this.createProgramChart();
      this.createDepartmentChart();
      this.createAttendanceChart();
      this.createAttendanceTrendChart();
      this.createAttendanceComparisonChart();
      this.createSemesterChart();
    }, 100);
  }

  private destroyCharts(): void {
    // Destroy all chart instances
    [
      this.programChart,
      this.departmentChart,
      this.attendanceChart,
      this.attendanceTrendChart,
      this.attendanceComparisonChart,
      this.semesterChart
    ].forEach(chart => {
      if (chart) {
        chart.destroy();
      }
    });
  }

  private createProgramChart(): void {
    if (!this.dashboardStats?.programStats || !this.programChartRef) return;

    const labels = this.dashboardStats.programStats.map(p => p.programName);
    const data = this.dashboardStats.programStats.map(p => p.studentCount);
    const backgroundColors = labels.map((_, i) => this.programColors[i % this.programColors.length]);

    this.programChart = new Chart(this.programChartRef.nativeElement, {
      type: 'doughnut',
      data: {
        labels,
        datasets: [{
          data,
          backgroundColor: backgroundColors,
          borderWidth: 1,
          borderColor: '#fff'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        cutout: '70%',
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              label: (context) => {
                const label = context.label || '';
                const value = context.parsed;
                const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
                const percentage = Math.round((value / total) * 100);
                return `${label}: ${value} (${percentage}%)`;
              }
            }
          }
        }
      }
    });
  }

  private createDepartmentChart(): void {
    if (!this.dashboardStats?.departmentStats || !this.departmentChartRef) return;

    const labels = this.dashboardStats.departmentStats.map(d => d.departmentName);
    const data = this.dashboardStats.departmentStats.map(d => d.studentCount);
    const backgroundColors = labels.map((_, i) => this.departmentColors[i % this.departmentColors.length]);

    this.departmentChart = new Chart(this.departmentChartRef.nativeElement, {
      type: 'bar',
      data: {
        labels,
        datasets: [{
          label: 'Students',
          data,
          backgroundColor: backgroundColors,
          borderColor: backgroundColors.map(c => this.darkenColor(c, 20)),
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              label: (context) => `${context.parsed.y} students`
            }
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Number of Students'
            }
          },
          x: {
            grid: {
              display: false
            }
          }
        }
      }
    });
  }

  private createAttendanceChart(): void {
    if (!this.dashboardStats?.attendanceStats || !this.attendanceChartRef) return;

    const labels = this.dashboardStats.attendanceStats.map(a => this.capitalizeFirst(a._id));
    const data = this.dashboardStats.attendanceStats.map(a => a.count);
    const backgroundColors = this.dashboardStats.attendanceStats.map(a => this.attendanceColors[a._id]);

    this.attendanceChart = new Chart(this.attendanceChartRef.nativeElement, {
      type: 'doughnut',
      data: {
        labels,
        datasets: [{
          data,
          backgroundColor: backgroundColors,
          borderColor: backgroundColors.map(c => this.darkenColor(c, 20)),
          borderWidth: 2
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              label: (context) => {
                const label = context.label || '';
                const value = context.parsed;
                const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
                const percentage = Math.round((value / total) * 100);
                return `${label}: ${value} (${percentage}%)`;
              }
            }
          }
        }
      }
    });
  }

  private createAttendanceTrendChart(): void {
    if (!this.dashboardStats?.attendanceTrend || !this.attendanceTrendChartRef) return;

    const labels = this.dashboardStats.attendanceTrend.map(t => {
      const date = new Date(t.date);
      return `${date.getDate()}/${date.getMonth() + 1}`;
    });
    const presentData = this.dashboardStats.attendanceTrend.map(t => t.present);
    const absentData = this.dashboardStats.attendanceTrend.map(t => t.absent);

    this.attendanceTrendChart = new Chart(this.attendanceTrendChartRef.nativeElement, {
      type: 'line',
      data: {
        labels,
        datasets: [
          {
            label: 'Present',
            data: presentData,
            borderColor: '#4CAF50',
            backgroundColor: 'rgba(76, 175, 80, 0.1)',
            borderWidth: 2,
            tension: 0.3,
            fill: true
          },
          {
            label: 'Absent',
            data: absentData,
            borderColor: '#F44336',
            backgroundColor: 'rgba(244, 67, 54, 0.1)',
            borderWidth: 2,
            tension: 0.3,
            fill: true
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          tooltip: {
            mode: 'index',
            intersect: false
          },
          legend: {
            position: 'top',
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Number of Students'
            }
          },
          x: {
            title: {
              display: true,
              text: 'Date'
            }
          }
        }
      }
    });
  }

  private createAttendanceComparisonChart(): void {
    if (!this.dashboardStats?.departmentAttendance || !this.attendanceComparisonChartRef) return;

    const labels = this.dashboardStats.departmentAttendance.map(d => d.departmentName);
    const attendanceRates = this.dashboardStats.departmentAttendance.map(d => d.attendanceRate);

    this.attendanceComparisonChart = new Chart(this.attendanceComparisonChartRef.nativeElement, {
      type: 'bar',
      data: {
        labels,
        datasets: [{
          label: 'Attendance Rate (%)',
          data: attendanceRates,
          backgroundColor: this.departmentColors.slice(0, labels.length),
          borderColor: this.departmentColors.slice(0, labels.length).map(c => this.darkenColor(c, 20)),
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          tooltip: {
            callbacks: {
              label: (context) => `${context.parsed.y}% attendance`
            }
          },
          legend: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            max: 100,
            title: {
              display: true,
              text: 'Attendance Rate (%)'
            }
          },
          x: {
            grid: {
              display: false
            }
          }
        }
      }
    });
  }

  private createSemesterChart(): void {
    if (!this.dashboardStats?.semesterStats || !this.semesterChartRef) return;

    const labels = this.dashboardStats.semesterStats.map(s => `Semester ${s._id}`);
    const data = this.dashboardStats.semesterStats.map(s => s.count);

    this.semesterChart = new Chart(this.semesterChartRef.nativeElement, {
      type: 'bar',
      data: {
        labels,
        datasets: [{
          label: 'Students',
          data,
          backgroundColor: '#FF6384',
          borderColor: '#FF6384',
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              label: (context) => `${context.parsed.y} students`
            }
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Number of Students'
            }
          },
          x: {
            grid: {
              display: false
            }
          }
        }
      }
    });
  }

  // Helper methods
  getProgramColor(programName: string): string {
    const index = this.dashboardStats?.programStats.findIndex(p => p.programName === programName) || 0;
    return this.programColors[index % this.programColors.length];
  }

  getDepartmentColor(index: number): string {
    return this.departmentColors[index % this.departmentColors.length];
  }

  getPercentage(value: number, total: number): number {
    return total > 0 ? Math.round((value / total) * 100) : 0;
  }

  getAttendancePercentage(): number {
    if (!this.dashboardStats?.attendanceStats?.length) return 0;

    const totalAttendance = this.dashboardStats.attendanceStats.reduce((sum, stat) => sum + stat.count, 0);
    const presentCount = this.dashboardStats.attendanceStats.find(stat => stat._id === 'present')?.count || 0;

    return totalAttendance > 0 ? Math.round((presentCount / totalAttendance) * 100) : 0;
  }

  getAttendancePercentageColor(): string {
    const percentage = this.getAttendancePercentage();
    if (percentage >= 90) return '#4CAF50';
    if (percentage >= 75) return '#FFC107';
    return '';
  }

  capitalizeFirst(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  darkenColor(color: string, percent: number): string {
    // Helper function to darken colors for borders
    const num = parseInt(color.replace('#', ''), 16);
    const amt = Math.round(2.55 * percent);
    const R = (num >> 16) - amt;
    const G = ((num >> 8) & 0x00FF) - amt;
    const B = (num & 0x0000FF) - amt;
    return `#${(
      0x1000000 +
      (R < 255 ? (R < 1 ? 0 : R) : 255) * 0x10000 +
      (G < 255 ? (G < 1 ? 0 : G) : 255) * 0x100 +
      (B < 255 ? (B < 1 ? 0 : B) : 255)
    ).toString(16).slice(1)}`;
  }

  refreshData(): void {
    this.loadDashboardStats();
  }
}