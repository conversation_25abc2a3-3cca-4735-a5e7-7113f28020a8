{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../sidebar/sidebar.component\";\nimport * as i3 from \"../header/header.component\";\nexport class MainComponent {\n  constructor() {\n    this.isSidebarOpen = true;\n    this.isMobile = false;\n  }\n  onResize(event) {\n    this.checkScreenSize();\n  }\n  ngOnInit() {\n    this.checkScreenSize();\n  }\n  checkScreenSize() {\n    this.isMobile = window.innerWidth < 992;\n    if (this.isMobile) {\n      this.isSidebarOpen = false;\n    }\n  }\n  toggleSidebar() {\n    if (this.isMobile) {\n      this.isSidebarOpen = !this.isSidebarOpen;\n    } else {\n      this.isSidebarOpen = !this.isSidebarOpen;\n    }\n  }\n  static #_ = this.ɵfac = function MainComponent_Factory(t) {\n    return new (t || MainComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: MainComponent,\n    selectors: [[\"app-main\"]],\n    hostBindings: function MainComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"resize\", function MainComponent_resize_HostBindingHandler($event) {\n          return ctx.onResize($event);\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    decls: 8,\n    vars: 5,\n    consts: [[1, \"app-container\"], [1, \"sidebar-wrapper\"], [3, \"isSidebarOpen\", \"toggle\"], [1, \"main-content\"], [3, \"toggleSidebar\"], [1, \"maindiv\"], [1, \"router-container\"]],\n    template: function MainComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"app-sidebar\", 2);\n        i0.ɵɵlistener(\"toggle\", function MainComponent_Template_app_sidebar_toggle_2_listener() {\n          return ctx.toggleSidebar();\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"app-header\", 4);\n        i0.ɵɵlistener(\"toggleSidebar\", function MainComponent_Template_app_header_toggleSidebar_4_listener() {\n          return ctx.toggleSidebar();\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6);\n        i0.ɵɵelement(7, \"router-outlet\");\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"sidebar-open\", ctx.isSidebarOpen)(\"sidebar-closed\", !ctx.isSidebarOpen);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"isSidebarOpen\", ctx.isSidebarOpen);\n      }\n    },\n    dependencies: [i1.RouterOutlet, i2.SidebarComponent, i3.HeaderComponent],\n    styles: [\"\\n\\n.app-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  min-height: 100vh;\\n  background-color: #f5f7fa;\\n  transition: all 0.3s ease;\\n  width: 100%;\\n  overflow-x: hidden;\\n}\\n\\n\\n.sidebar-wrapper[_ngcontent-%COMP%] {\\n  position: fixed;\\n  height: 100vh;\\n  z-index: 1000;\\n  transition: all 0.3s ease;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-left: 250px;\\n  transition: all 0.3s ease;\\n  min-height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.router-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background-color: #f5f7fa;\\n}\\n\\n\\n\\n.sidebar-closed[_ngcontent-%COMP%]   .sidebar-wrapper[_ngcontent-%COMP%] {\\n  width: 70px;\\n}\\n\\n.sidebar-closed[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%] {\\n  margin-left: 70px;\\n}\\n\\n\\n\\n@media (max-width: 991.98px) {\\n  .sidebar-wrapper[_ngcontent-%COMP%] {\\n    transform: translateX(-100%);\\n    width: 250px;\\n    position: absolute;\\n  }\\n  \\n  .sidebar-open[_ngcontent-%COMP%]   .sidebar-wrapper[_ngcontent-%COMP%] {\\n    transform: translateX(0);\\n  }\\n  \\n  .sidebar-closed[_ngcontent-%COMP%]   .sidebar-wrapper[_ngcontent-%COMP%] {\\n    transform: translateX(-100%);\\n    width: 250px;\\n  }\\n  \\n  .main-content[_ngcontent-%COMP%] {\\n    margin-left: 0 !important;\\n  }\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .router-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n\\n  \\n\\n  .container-fluid[_ngcontent-%COMP%] {\\n    padding: 0 8px;\\n  }\\n\\n  .row[_ngcontent-%COMP%] {\\n    margin: 0 -4px;\\n  }\\n\\n  .row[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%] {\\n    padding: 0 4px;\\n  }\\n\\n  \\n\\n  .form-control[_ngcontent-%COMP%], .form-select[_ngcontent-%COMP%] {\\n    font-size: 16px; \\n\\n    padding: 8px 12px;\\n  }\\n\\n  \\n\\n  .btn[_ngcontent-%COMP%] {\\n    min-height: 44px; \\n\\n    font-size: 14px;\\n  }\\n\\n  \\n\\n  .table-responsive[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n\\n  \\n\\n  .card[_ngcontent-%COMP%] {\\n    margin-bottom: 15px;\\n  }\\n\\n  .card-body[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n}\\n\\n@media (max-width: 576px) {\\n  .router-container[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n\\n  \\n\\n  .container-fluid[_ngcontent-%COMP%] {\\n    padding: 0 4px;\\n  }\\n\\n  .row[_ngcontent-%COMP%] {\\n    margin: 0 -2px;\\n  }\\n\\n  .row[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%] {\\n    padding: 0 2px;\\n  }\\n\\n  \\n\\n  h1[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  h2[_ngcontent-%COMP%] {\\n    font-size: 1.3rem;\\n  }\\n\\n  h3[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n\\n  \\n\\n  .form-control[_ngcontent-%COMP%], .form-select[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n    padding: 6px 10px;\\n  }\\n\\n  \\n\\n  .btn[_ngcontent-%COMP%] {\\n    min-height: 40px;\\n    font-size: 13px;\\n    padding: 6px 12px;\\n  }\\n\\n  .btn-sm[_ngcontent-%COMP%] {\\n    min-height: 36px;\\n    font-size: 12px;\\n    padding: 4px 8px;\\n  }\\n\\n  \\n\\n  .table-responsive[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n\\n  .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    padding: 6px 4px;\\n  }\\n\\n  \\n\\n  .card[_ngcontent-%COMP%] {\\n    margin-bottom: 12px;\\n  }\\n\\n  .card-body[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n\\n  \\n\\n  .modal-dialog[_ngcontent-%COMP%] {\\n    margin: 10px;\\n  }\\n\\n  .modal-content[_ngcontent-%COMP%] {\\n    border-radius: 8px;\\n  }\\n\\n  .modal-body[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n}\\n\\n\\n\\n@media print {\\n  .sidebar-wrapper[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n\\n  .main-content[_ngcontent-%COMP%] {\\n    margin-left: 0 !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["MainComponent", "constructor", "isSidebarOpen", "isMobile", "onResize", "event", "checkScreenSize", "ngOnInit", "window", "innerWidth", "toggleSidebar", "_", "_2", "selectors", "hostBindings", "MainComponent_HostBindings", "rf", "ctx", "$event", "i0", "ɵɵresolveWindow", "ɵɵelementStart", "ɵɵlistener", "MainComponent_Template_app_sidebar_toggle_2_listener", "ɵɵelementEnd", "MainComponent_Template_app_header_toggleSidebar_4_listener", "ɵɵelement", "ɵɵclassProp", "ɵɵadvance", "ɵɵproperty"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\main\\main.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\main\\main.component.html"], "sourcesContent": ["import { Component, HostListener } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-main',\r\n  templateUrl: './main.component.html',\r\n  styleUrls: ['./main.component.css']\r\n})\r\nexport class MainComponent {\r\n  isSidebarOpen = true;\r\n  isMobile = false;\r\n\r\n  @HostListener('window:resize', ['$event'])\r\n  onResize(event: Event) {\r\n    this.checkScreenSize();\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.checkScreenSize();\r\n  }\r\n\r\n  checkScreenSize() {\r\n    this.isMobile = window.innerWidth < 992;\r\n    if (this.isMobile) {\r\n      this.isSidebarOpen = false;\r\n    }\r\n  }\r\n\r\n  toggleSidebar() {\r\n    if (this.isMobile) {\r\n      this.isSidebarOpen = !this.isSidebarOpen;\r\n    } else {\r\n      this.isSidebarOpen = !this.isSidebarOpen;\r\n    }\r\n  }\r\n}", "<div class=\"app-container\" [class.sidebar-open]=\"isSidebarOpen\" [class.sidebar-closed]=\"!isSidebarOpen\">\r\n  <div class=\"sidebar-wrapper\">\r\n    <app-sidebar [isSidebarOpen]=\"isSidebarOpen\" (toggle)=\"toggleSidebar()\"></app-sidebar>\r\n  </div>\r\n  \r\n  <div class=\"main-content\">\r\n    <app-header (toggleSidebar)=\"toggleSidebar()\"></app-header>\r\n<div class=\"maindiv\">\r\n    \r\n    <div class=\"router-container\">\r\n      <router-outlet></router-outlet>\r\n    </div>\r\n    </div>\r\n  </div>\r\n</div>"], "mappings": ";;;;AAOA,OAAM,MAAOA,aAAa;EAL1BC,YAAA;IAME,KAAAC,aAAa,GAAG,IAAI;IACpB,KAAAC,QAAQ,GAAG,KAAK;;EAGhBC,QAAQA,CAACC,KAAY;IACnB,IAAI,CAACC,eAAe,EAAE;EACxB;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACD,eAAe,EAAE;EACxB;EAEAA,eAAeA,CAAA;IACb,IAAI,CAACH,QAAQ,GAAGK,MAAM,CAACC,UAAU,GAAG,GAAG;IACvC,IAAI,IAAI,CAACN,QAAQ,EAAE;MACjB,IAAI,CAACD,aAAa,GAAG,KAAK;;EAE9B;EAEAQ,aAAaA,CAAA;IACX,IAAI,IAAI,CAACP,QAAQ,EAAE;MACjB,IAAI,CAACD,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;KACzC,MAAM;MACL,IAAI,CAACA,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;;EAE5C;EAAC,QAAAS,CAAA,G;qBA1BUX,aAAa;EAAA;EAAA,QAAAY,EAAA,G;UAAbZ,aAAa;IAAAa,SAAA;IAAAC,YAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;iBAAbC,GAAA,CAAAb,QAAA,CAAAc,MAAA,CAAgB;QAAA,UAAAC,EAAA,CAAAC,eAAA;;;;;;;;QCP7BD,EAAA,CAAAE,cAAA,aAAwG;QAEvDF,EAAA,CAAAG,UAAA,oBAAAC,qDAAA;UAAA,OAAUN,GAAA,CAAAP,aAAA,EAAe;QAAA,EAAC;QAACS,EAAA,CAAAK,YAAA,EAAc;QAGxFL,EAAA,CAAAE,cAAA,aAA0B;QACZF,EAAA,CAAAG,UAAA,2BAAAG,2DAAA;UAAA,OAAiBR,GAAA,CAAAP,aAAA,EAAe;QAAA,EAAC;QAACS,EAAA,CAAAK,YAAA,EAAa;QAC/DL,EAAA,CAAAE,cAAA,aAAqB;QAGfF,EAAA,CAAAO,SAAA,oBAA+B;QACjCP,EAAA,CAAAK,YAAA,EAAM;;;QAXiBL,EAAA,CAAAQ,WAAA,iBAAAV,GAAA,CAAAf,aAAA,CAAoC,oBAAAe,GAAA,CAAAf,aAAA;QAE9CiB,EAAA,CAAAS,SAAA,GAA+B;QAA/BT,EAAA,CAAAU,UAAA,kBAAAZ,GAAA,CAAAf,aAAA,CAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}