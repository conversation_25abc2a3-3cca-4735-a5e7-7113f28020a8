{"ast": null, "code": "import Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/department.service\";\nimport * as i2 from \"src/app/services/program.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/tooltip\";\nfunction DepartmentTableComponent_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const program_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", program_r4._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", program_r4.name, \" - \", program_r4.fullName, \" \");\n  }\n}\nfunction DepartmentTableComponent_tr_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 28);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 29);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 30);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 31)(8, \"div\", 32)(9, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function DepartmentTableComponent_tr_46_Template_button_click_9_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r7);\n      const department_r5 = restoredCtx.$implicit;\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.editdepartment(department_r5));\n    });\n    i0.ɵɵelementStart(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function DepartmentTableComponent_tr_46_Template_button_click_12_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r7);\n      const department_r5 = restoredCtx.$implicit;\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.deletedepartment(department_r5._id));\n    });\n    i0.ɵɵelementStart(13, \"mat-icon\");\n    i0.ɵɵtext(14, \"delete\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const department_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"table-loading\", ctx_r1.loading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", department_r5 == null ? null : department_r5.program == null ? null : department_r5.program.name, \" - \", department_r5 == null ? null : department_r5.program == null ? null : department_r5.program.fullName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", department_r5 == null ? null : department_r5.name, \" (\", department_r5 == null ? null : department_r5.code, \") \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (department_r5 == null ? null : department_r5.description) || \"No description\", \" \");\n  }\n}\nfunction DepartmentTableComponent_tr_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 35)(2, \"div\", 36);\n    i0.ɵɵelement(3, \"div\", 37);\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Loading departments...\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction DepartmentTableComponent_tr_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 35)(2, \"div\", 38)(3, \"mat-icon\", 39);\n    i0.ɵɵtext(4, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 40);\n    i0.ɵɵtext(6, \"No departments found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"small\");\n    i0.ɵɵtext(8, \"Try adjusting your filters or add a new department\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nexport class DepartmentTableComponent {\n  constructor(departmentservice, programService, router) {\n    this.departmentservice = departmentservice;\n    this.programService = programService;\n    this.router = router;\n    this.currentPage = 1;\n    this.itemsPerPage = 5;\n    this.searchQuery = '';\n    this.departments = [];\n    this.allDepartments = [];\n    this.programs = [];\n    this.selectedProgram = '';\n    this.loading = false;\n    this.isChecked = false;\n    this.isIndeterminate = true;\n  }\n  ngOnInit() {\n    this.loadPrograms();\n    this.getdepartments();\n  }\n  loadPrograms() {\n    this.programService.getAllPrograms(true).subscribe({\n      next: response => {\n        if (response.success) {\n          this.programs = response.programs;\n        }\n      },\n      error: error => {\n        console.error('Error loading programs:', error);\n      }\n    });\n  }\n  onProgramChange() {\n    this.applyFilters();\n  }\n  searchdepartment() {\n    this.applyFilters();\n  }\n  applyFilters() {\n    let filteredDepartments = [...this.allDepartments];\n    // Filter by program\n    if (this.selectedProgram) {\n      filteredDepartments = filteredDepartments.filter(department => department.program._id === this.selectedProgram);\n    }\n    // Filter by search query\n    if (this.searchQuery) {\n      filteredDepartments = filteredDepartments.filter(department => {\n        return department.name.toLowerCase().includes(this.searchQuery.toLowerCase()) || department.code.toLowerCase().includes(this.searchQuery.toLowerCase()) || department.program.name.toLowerCase().includes(this.searchQuery.toLowerCase());\n      });\n    }\n    this.departments = filteredDepartments;\n    this.currentPage = 1;\n  }\n  resetFilters() {\n    this.selectedProgram = '';\n    this.searchQuery = '';\n    this.departments = [...this.allDepartments];\n    this.currentPage = 1;\n  }\n  get painateddepartment() {\n    const startIndex = (this.currentPage - 1) * this.itemsPerPage;\n    const endIndex = startIndex + this.itemsPerPage;\n    return this.departments.slice(startIndex, endIndex);\n  }\n  get totalPages() {\n    return Math.ceil(this.departments.length / this.itemsPerPage);\n  }\n  nextPage() {\n    if (this.currentPage < this.totalPages) {\n      this.currentPage++;\n    }\n  }\n  previousPage() {\n    if (this.currentPage > 1) {\n      this.currentPage--;\n    }\n  }\n  // Edit department\n  editdepartment(department) {\n    this.router.navigate(['/dashboard/admin/department/add-department' + '/' + department._id]);\n    // this.departmentForm.patchValue({\n    //   departmentName: department.departmentName,\n    //   code: department.code\n    // });\n    // this.editingdepartmentId = department._id;\n  }\n  // Fetch all departments\n  getdepartments() {\n    this.loading = true;\n    this.departmentservice.getAllDepartments().subscribe({\n      next: response => {\n        if (response.success) {\n          this.allDepartments = response.departments;\n          this.departments = [...this.allDepartments];\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error fetching departments:', error);\n        this.loading = false;\n      }\n    });\n  }\n  deletedepartment(id) {\n    Swal.fire({\n      title: 'Are you sure?',\n      text: 'This action will permanently delete the department.',\n      icon: 'warning',\n      showCancelButton: true,\n      confirmButtonColor: '#d33',\n      cancelButtonColor: '#3085d6',\n      confirmButtonText: 'Yes, delete it!',\n      cancelButtonText: 'Cancel'\n    }).then(result => {\n      if (result.isConfirmed) {\n        // Proceed with deletion\n        this.departmentservice.deletedepartment(id).subscribe(res => {\n          Swal.fire({\n            title: res.message,\n            icon: 'success',\n            confirmButtonText: 'OK',\n            confirmButtonColor: '#3085d6',\n            timer: 2000\n          }).then(() => {\n            // Update the UI without reloading\n            this.departments = this.departments.filter(department => department._id !== id);\n          });\n        }, error => {\n          console.error('Error deleting department:', error);\n          Swal.fire({\n            title: 'Error',\n            text: 'Failed to delete the department. Please try again.',\n            icon: 'error',\n            confirmButtonColor: '#3085d6'\n          });\n        });\n      }\n    });\n  }\n  toggleCheckbox() {\n    if (this.isIndeterminate) {\n      this.isIndeterminate = false;\n      this.isChecked = true;\n    } else if (this.isChecked) {\n      this.isChecked = false;\n    } else {\n      this.isIndeterminate = true;\n    }\n  }\n  static #_ = this.ɵfac = function DepartmentTableComponent_Factory(t) {\n    return new (t || DepartmentTableComponent)(i0.ɵɵdirectiveInject(i1.DepartmentService), i0.ɵɵdirectiveInject(i2.ProgramService), i0.ɵɵdirectiveInject(i3.Router));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: DepartmentTableComponent,\n    selectors: [[\"app-department-table\"]],\n    decls: 58,\n    vars: 10,\n    consts: [[1, \"maindiv\"], [1, \"secondarydiv\"], [1, \"header-section\", \"mb-4\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [\"routerLink\", \"/dashboard/admin/department/add-department\", 1, \"btn\", \"btn-primary\", \"d-flex\", \"align-items-center\"], [1, \"filters-section\"], [1, \"row\", \"g-3\"], [1, \"col-md-4\"], [1, \"form-label\"], [1, \"form-select\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-6\"], [1, \"input-group\"], [\"type\", \"text\", \"placeholder\", \"Search by name, code, or program...\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\"], [1, \"col-md-2\", \"d-flex\", \"align-items-end\"], [1, \"btn\", \"btn-outline-secondary\", \"w-100\", 3, \"click\"], [1, \"table-container\"], [1, \"table-responsive\"], [1, \"tablehead\"], [1, \"two\"], [3, \"table-loading\", 4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [1, \"d-flex\", \"p-3\", \"align-items-center\", \"justify-content-between\"], [1, \"btn\", \"btn-top\", \"border\", 3, \"disabled\", \"click\"], [1, \"page\"], [3, \"value\"], [\"data-label\", \"Program\", 1, \"name\"], [\"data-label\", \"Department Name\", 1, \"para\"], [\"data-label\", \"Description\", 1, \"para\"], [\"data-label\", \"Action\", 1, \"para\"], [1, \"d-flex\", \"gap-2\"], [\"matTooltip\", \"Edit Department\", 1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"d-flex\", \"justify-content-center\", \"align-items-center\", 3, \"click\"], [\"matTooltip\", \"Delete Department\", 1, \"btn\", \"btn-outline-danger\", \"btn-sm\", \"d-flex\", \"justify-content-center\", \"align-items-center\", 3, \"click\"], [\"colspan\", \"4\", 1, \"text-center\", \"py-4\"], [1, \"d-flex\", \"justify-content-center\", \"align-items-center\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\", \"me-2\"], [1, \"text-muted\"], [1, \"large-icon\"], [1, \"mt-2\"]],\n    template: function DepartmentTableComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h2\");\n        i0.ɵɵtext(5, \"Department Management\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"button\", 4)(7, \"mat-icon\");\n        i0.ɵɵtext(8, \"add\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(9, \"\\u00A0 Add New Department \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 5)(11, \"div\", 6)(12, \"div\", 7)(13, \"label\", 8);\n        i0.ɵɵtext(14, \"Filter by Program\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(15, \"select\", 9);\n        i0.ɵɵlistener(\"ngModelChange\", function DepartmentTableComponent_Template_select_ngModelChange_15_listener($event) {\n          return ctx.selectedProgram = $event;\n        })(\"change\", function DepartmentTableComponent_Template_select_change_15_listener() {\n          return ctx.onProgramChange();\n        });\n        i0.ɵɵelementStart(16, \"option\", 10);\n        i0.ɵɵtext(17, \"All Programs\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(18, DepartmentTableComponent_option_18_Template, 2, 3, \"option\", 11);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(19, \"div\", 12)(20, \"label\", 8);\n        i0.ɵɵtext(21, \"Search Departments\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(22, \"div\", 13)(23, \"input\", 14);\n        i0.ɵɵlistener(\"ngModelChange\", function DepartmentTableComponent_Template_input_ngModelChange_23_listener($event) {\n          return ctx.searchQuery = $event;\n        })(\"input\", function DepartmentTableComponent_Template_input_input_23_listener() {\n          return ctx.searchdepartment();\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(24, \"button\", 15)(25, \"mat-icon\");\n        i0.ɵɵtext(26, \"search\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(27, \"div\", 16)(28, \"button\", 17);\n        i0.ɵɵlistener(\"click\", function DepartmentTableComponent_Template_button_click_28_listener() {\n          return ctx.resetFilters();\n        });\n        i0.ɵɵelementStart(29, \"mat-icon\");\n        i0.ɵɵtext(30, \"clear\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(31, \" Reset \");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(32, \"div\", 18)(33, \"div\", 19)(34, \"table\")(35, \"thead\")(36, \"tr\", 20)(37, \"th\");\n        i0.ɵɵtext(38, \" Program \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(39, \"th\");\n        i0.ɵɵtext(40, \" Department name \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(41, \"th\");\n        i0.ɵɵtext(42, \"Description \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(43, \"th\", 21);\n        i0.ɵɵtext(44, \"Actions\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(45, \"tbody\");\n        i0.ɵɵtemplate(46, DepartmentTableComponent_tr_46_Template, 15, 7, \"tr\", 22);\n        i0.ɵɵtemplate(47, DepartmentTableComponent_tr_47_Template, 6, 0, \"tr\", 23);\n        i0.ɵɵtemplate(48, DepartmentTableComponent_tr_48_Template, 9, 0, \"tr\", 23);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(49, \"div\", 24)(50, \"div\")(51, \"button\", 25);\n        i0.ɵɵlistener(\"click\", function DepartmentTableComponent_Template_button_click_51_listener() {\n          return ctx.previousPage();\n        });\n        i0.ɵɵtext(52, \"Previous\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(53, \"div\", 26);\n        i0.ɵɵtext(54);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(55, \"div\")(56, \"button\", 25);\n        i0.ɵɵlistener(\"click\", function DepartmentTableComponent_Template_button_click_56_listener() {\n          return ctx.nextPage();\n        });\n        i0.ɵɵtext(57, \"Next\");\n        i0.ɵɵelementEnd()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(15);\n        i0.ɵɵproperty(\"ngModel\", ctx.selectedProgram);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.programs);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n        i0.ɵɵadvance(23);\n        i0.ɵɵproperty(\"ngForOf\", ctx.painateddepartment);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.departments.length === 0);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"disabled\", ctx.currentPage === 1);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate2(\"Page \", ctx.currentPage, \" of \", ctx.totalPages, \"\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"disabled\", ctx.currentPage === ctx.totalPages);\n      }\n    },\n    dependencies: [i4.NgForOf, i4.NgIf, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.DefaultValueAccessor, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgModel, i3.RouterLink, i6.MatIcon, i7.MatTooltip],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "program_r4", "_id", "ɵɵadvance", "ɵɵtextInterpolate2", "name", "fullName", "ɵɵlistener", "DepartmentTableComponent_tr_46_Template_button_click_9_listener", "restoredCtx", "ɵɵrestoreView", "_r7", "department_r5", "$implicit", "ctx_r6", "ɵɵnextContext", "ɵɵresetView", "editdepartment", "DepartmentTableComponent_tr_46_Template_button_click_12_listener", "ctx_r8", "deletedepartment", "ɵɵclassProp", "ctx_r1", "loading", "program", "code", "ɵɵtextInterpolate1", "description", "ɵɵelement", "DepartmentTableComponent", "constructor", "departmentservice", "programService", "router", "currentPage", "itemsPerPage", "searchQuery", "departments", "allDepartments", "programs", "selectedProgram", "isChecked", "isIndeterminate", "ngOnInit", "loadPrograms", "getdepartments", "getAllPrograms", "subscribe", "next", "response", "success", "error", "console", "onProgramChange", "applyFilters", "searchdepartment", "filteredDepartments", "filter", "department", "toLowerCase", "includes", "resetFilters", "painateddepartment", "startIndex", "endIndex", "slice", "totalPages", "Math", "ceil", "length", "nextPage", "previousPage", "navigate", "getAllDepartments", "id", "fire", "title", "text", "icon", "showCancelButton", "confirmButtonColor", "cancelButtonColor", "confirmButtonText", "cancelButtonText", "then", "result", "isConfirmed", "res", "message", "timer", "toggleCheckbox", "_", "ɵɵdirectiveInject", "i1", "DepartmentService", "i2", "ProgramService", "i3", "Router", "_2", "selectors", "decls", "vars", "consts", "template", "DepartmentTableComponent_Template", "rf", "ctx", "DepartmentTableComponent_Template_select_ngModelChange_15_listener", "$event", "DepartmentTableComponent_Template_select_change_15_listener", "ɵɵtemplate", "DepartmentTableComponent_option_18_Template", "DepartmentTableComponent_Template_input_ngModelChange_23_listener", "DepartmentTableComponent_Template_input_input_23_listener", "DepartmentTableComponent_Template_button_click_28_listener", "DepartmentTableComponent_tr_46_Template", "DepartmentTableComponent_tr_47_Template", "DepartmentTableComponent_tr_48_Template", "DepartmentTableComponent_Template_button_click_51_listener", "DepartmentTableComponent_Template_button_click_56_listener"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\department\\department-table\\department-table.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\department\\department-table\\department-table.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { DepartmentService } from 'src/app/services/department.service';\r\nimport { ProgramService } from 'src/app/services/program.service';\r\nimport { Program, Department } from 'src/app/models/user';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-department-table',\r\n  templateUrl: './department-table.component.html',\r\n  styleUrls: ['./department-table.component.css']\r\n})\r\nexport class DepartmentTableComponent implements OnInit {\r\n  currentPage = 1;\r\n  itemsPerPage = 5;\r\n  searchQuery = '';\r\n  departments: Department[] = [];\r\n  allDepartments: Department[] = [];\r\n  programs: Program[] = [];\r\n  selectedProgram = '';\r\n  loading = false;\r\n\r\n  constructor(\r\n    private departmentservice: DepartmentService,\r\n    private programService: ProgramService,\r\n    public router: Router\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loadPrograms();\r\n    this.getdepartments();\r\n  }\r\n\r\n  loadPrograms(): void {\r\n    this.programService.getAllPrograms(true).subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.programs = response.programs;\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading programs:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n\r\n\r\n  onProgramChange(): void {\r\n    this.applyFilters();\r\n  }\r\n\r\n  searchdepartment(): void {\r\n    this.applyFilters();\r\n  }\r\n\r\n  applyFilters(): void {\r\n    let filteredDepartments = [...this.allDepartments];\r\n\r\n    // Filter by program\r\n    if (this.selectedProgram) {\r\n      filteredDepartments = filteredDepartments.filter(department =>\r\n        department.program._id === this.selectedProgram\r\n      );\r\n    }\r\n\r\n    // Filter by search query\r\n    if (this.searchQuery) {\r\n      filteredDepartments = filteredDepartments.filter(department => {\r\n        return (\r\n          department.name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||\r\n          department.code.toLowerCase().includes(this.searchQuery.toLowerCase()) ||\r\n          department.program.name.toLowerCase().includes(this.searchQuery.toLowerCase())\r\n        );\r\n      });\r\n    }\r\n\r\n    this.departments = filteredDepartments;\r\n    this.currentPage = 1;\r\n  }\r\n\r\n  resetFilters(): void {\r\n    this.selectedProgram = '';\r\n    this.searchQuery = '';\r\n    this.departments = [...this.allDepartments];\r\n    this.currentPage = 1;\r\n  }\r\n  get painateddepartment() {\r\n    const startIndex = (this.currentPage - 1) * this.itemsPerPage;\r\n    const endIndex = startIndex + this.itemsPerPage;\r\n    return this.departments.slice(startIndex, endIndex);\r\n  }\r\n\r\n  get totalPages() {\r\n    return Math.ceil(this.departments.length / this.itemsPerPage);\r\n  }\r\n\r\n  nextPage() {\r\n    if (this.currentPage < this.totalPages) {\r\n      this.currentPage++;\r\n    }\r\n  }\r\n  \r\n  previousPage() {\r\n    if (this.currentPage > 1) {\r\n      this.currentPage--;\r\n    }\r\n  }\r\n // Edit department\r\n editdepartment(department: any) {\r\n  this.router.navigate(['/dashboard/admin/department/add-department' + '/' + department._id])\r\n  // this.departmentForm.patchValue({\r\n  //   departmentName: department.departmentName,\r\n  //   code: department.code\r\n  // });\r\n  // this.editingdepartmentId = department._id;\r\n}\r\n\r\n    // Fetch all departments\r\n    getdepartments(): void {\r\n      this.loading = true;\r\n      this.departmentservice.getAllDepartments().subscribe({\r\n        next: (response) => {\r\n          if (response.success) {\r\n            this.allDepartments = response.departments;\r\n            this.departments = [...this.allDepartments];\r\n          }\r\n          this.loading = false;\r\n        },\r\n        error: (error) => {\r\n          console.error('Error fetching departments:', error);\r\n          this.loading = false;\r\n        }\r\n      });\r\n    }\r\n    deletedepartment(id: string) {\r\n      Swal.fire({\r\n        title: 'Are you sure?',\r\n        text: 'This action will permanently delete the department.',\r\n        icon: 'warning',\r\n        showCancelButton: true,\r\n        confirmButtonColor: '#d33',\r\n        cancelButtonColor: '#3085d6',\r\n        confirmButtonText: 'Yes, delete it!',\r\n        cancelButtonText: 'Cancel'\r\n      }).then((result) => {\r\n        if (result.isConfirmed) {\r\n          // Proceed with deletion\r\n          this.departmentservice.deletedepartment(id).subscribe(\r\n            (res) => {\r\n              Swal.fire({\r\n                title: res.message,\r\n                icon: 'success',\r\n                confirmButtonText: 'OK',\r\n                confirmButtonColor: '#3085d6',\r\n                timer: 2000\r\n              }).then(() => {\r\n                // Update the UI without reloading\r\n                this.departments = this.departments.filter((department: any) => department._id !== id);\r\n              });\r\n            },\r\n            (error) => {\r\n              console.error('Error deleting department:', error);\r\n              Swal.fire({\r\n                title: 'Error',\r\n                text: 'Failed to delete the department. Please try again.',\r\n                icon: 'error',\r\n                confirmButtonColor: '#3085d6'\r\n              });\r\n            }\r\n          );\r\n        }\r\n      });\r\n    }\r\n    \r\n\r\n\r\n  isChecked: boolean = false;\r\n  isIndeterminate: boolean = true;\r\n  toggleCheckbox() {\r\n    if (this.isIndeterminate) {\r\n      this.isIndeterminate = false;\r\n      this.isChecked = true;\r\n    } else if (this.isChecked) {\r\n      this.isChecked = false;\r\n    } else {\r\n      this.isIndeterminate = true;\r\n    }\r\n  }\r\n\r\n}\r\n", "\r\n<div class=\"maindiv\">\r\n    <div class=\"secondarydiv\">\r\n        <div class=\"header-section mb-4\">\r\n            <div class=\"d-flex justify-content-between align-items-center mb-3\">\r\n                <h2>Department Management</h2>\r\n                <button class=\"btn btn-primary d-flex align-items-center\" routerLink=\"/dashboard/admin/department/add-department\">\r\n                    <mat-icon>add</mat-icon>&nbsp; Add New Department\r\n                </button>\r\n            </div>\r\n\r\n            <!-- Filters Section -->\r\n            <div class=\"filters-section\">\r\n                <div class=\"row g-3\">\r\n                    <div class=\"col-md-4\">\r\n                        <label class=\"form-label\">Filter by Program</label>\r\n                        <select class=\"form-select\" [(ngModel)]=\"selectedProgram\" (change)=\"onProgramChange()\">\r\n                            <option value=\"\">All Programs</option>\r\n                            <option *ngFor=\"let program of programs\" [value]=\"program._id\">\r\n                                {{ program.name }} - {{ program.fullName }}\r\n                            </option>\r\n                        </select>\r\n                    </div>\r\n                    <div class=\"col-md-6\">\r\n                        <label class=\"form-label\">Search Departments</label>\r\n                        <div class=\"input-group\">\r\n                            <input type=\"text\" class=\"form-control\" placeholder=\"Search by name, code, or program...\"\r\n                                   [(ngModel)]=\"searchQuery\" (input)=\"searchdepartment()\">\r\n                            <button class=\"btn btn-outline-secondary\" type=\"button\">\r\n                                <mat-icon>search</mat-icon>\r\n                            </button>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"col-md-2 d-flex align-items-end\">\r\n                        <button class=\"btn btn-outline-secondary w-100\" (click)=\"resetFilters()\">\r\n                            <mat-icon>clear</mat-icon> Reset\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"table-container\">\r\n            <div class=\"table-responsive\">\r\n                <table>\r\n                    <thead>\r\n                        <tr class=\"tablehead\">\r\n                            <th >\r\n                               Program                             \r\n                            </th>\r\n                            \r\n                            <th >\r\n                                Department name                               \r\n                            </th>\r\n                            <th>Description </th>\r\n                            \r\n                            <th class=\"two\">Actions</th>\r\n                        </tr>\r\n                    </thead>\r\n                    <tbody>\r\n                        <tr *ngFor=\"let department of painateddepartment\" [class.table-loading]=\"loading\">\r\n                            <td class=\"name\" data-label=\"Program\">\r\n                                {{department?.program?.name}} - {{department?.program?.fullName}}\r\n                            </td>\r\n                            <td class=\"para\" data-label=\"Department Name\">\r\n                              {{department?.name}} ({{department?.code}})\r\n                            </td>\r\n                            <td class=\"para\" data-label=\"Description\">\r\n                              {{department?.description || 'No description'}}\r\n                            </td>\r\n                            <td class=\"para\" data-label=\"Action\">\r\n                                <div class=\"d-flex gap-2\">\r\n                                    <button class=\"btn btn-outline-primary btn-sm d-flex justify-content-center align-items-center\"\r\n                                            (click)=\"editdepartment(department)\"\r\n                                            matTooltip=\"Edit Department\">\r\n                                        <mat-icon>edit</mat-icon>\r\n                                    </button>\r\n                                    <button class=\"btn btn-outline-danger btn-sm d-flex justify-content-center align-items-center\"\r\n                                            (click)=\"deletedepartment(department._id)\"\r\n                                            matTooltip=\"Delete Department\">\r\n                                        <mat-icon>delete</mat-icon>\r\n                                    </button>\r\n                                </div>\r\n                            </td>\r\n                        </tr>\r\n                        <tr *ngIf=\"loading\">\r\n                            <td colspan=\"4\" class=\"text-center py-4\">\r\n                                <div class=\"d-flex justify-content-center align-items-center\">\r\n                                    <div class=\"spinner-border text-primary me-2\" role=\"status\"></div>\r\n                                    <span>Loading departments...</span>\r\n                                </div>\r\n                            </td>\r\n                        </tr>\r\n                        <tr *ngIf=\"!loading && departments.length === 0\">\r\n                            <td colspan=\"4\" class=\"text-center py-4\">\r\n                                <div class=\"text-muted\">\r\n                                    <mat-icon class=\"large-icon\">business</mat-icon>\r\n                                    <p class=\"mt-2\">No departments found</p>\r\n                                    <small>Try adjusting your filters or add a new department</small>\r\n                                </div>\r\n                            </td>\r\n                        </tr>\r\n                    </tbody>\r\n                </table>\r\n            </div>\r\n            <div class=\"d-flex p-3 align-items-center justify-content-between\">\r\n                <div>\r\n                  <button class=\"btn btn-top border\" [disabled]=\"currentPage === 1\" (click)=\"previousPage()\">Previous</button>\r\n                </div>\r\n                <div class=\"page\">Page {{ currentPage }} of {{ totalPages }}</div>\r\n                <div>\r\n                  <button class=\"btn btn-top border\" [disabled]=\"currentPage === totalPages\" (click)=\"nextPage()\">Next</button>\r\n                </div>\r\n              </div>\r\n\r\n        \r\n        </div>\r\n    </div>\r\n</div>\r\n"], "mappings": "AAKA,OAAOA,IAAI,MAAM,aAAa;;;;;;;;;;;ICaFC,EAAA,CAAAC,cAAA,iBAA+D;IAC3DD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFgCH,EAAA,CAAAI,UAAA,UAAAC,UAAA,CAAAC,GAAA,CAAqB;IAC1DN,EAAA,CAAAO,SAAA,GACJ;IADIP,EAAA,CAAAQ,kBAAA,MAAAH,UAAA,CAAAI,IAAA,SAAAJ,UAAA,CAAAK,QAAA,MACJ;;;;;;IAuCJV,EAAA,CAAAC,cAAA,SAAkF;IAE1ED,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAA8C;IAC5CD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAA0C;IACxCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAAqC;IAGrBD,EAAA,CAAAW,UAAA,mBAAAC,gEAAA;MAAA,MAAAC,WAAA,GAAAb,EAAA,CAAAc,aAAA,CAAAC,GAAA;MAAA,MAAAC,aAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAASnB,EAAA,CAAAoB,WAAA,CAAAF,MAAA,CAAAG,cAAA,CAAAL,aAAA,CAA0B;IAAA,EAAC;IAExChB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE7BH,EAAA,CAAAC,cAAA,kBAEuC;IAD/BD,EAAA,CAAAW,UAAA,mBAAAW,iEAAA;MAAA,MAAAT,WAAA,GAAAb,EAAA,CAAAc,aAAA,CAAAC,GAAA;MAAA,MAAAC,aAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAM,MAAA,GAAAvB,EAAA,CAAAmB,aAAA;MAAA,OAASnB,EAAA,CAAAoB,WAAA,CAAAG,MAAA,CAAAC,gBAAA,CAAAR,aAAA,CAAAV,GAAA,CAAgC;IAAA,EAAC;IAE9CN,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IApBOH,EAAA,CAAAyB,WAAA,kBAAAC,MAAA,CAAAC,OAAA,CAA+B;IAEzE3B,EAAA,CAAAO,SAAA,GACJ;IADIP,EAAA,CAAAQ,kBAAA,MAAAQ,aAAA,kBAAAA,aAAA,CAAAY,OAAA,kBAAAZ,aAAA,CAAAY,OAAA,CAAAnB,IAAA,SAAAO,aAAA,kBAAAA,aAAA,CAAAY,OAAA,kBAAAZ,aAAA,CAAAY,OAAA,CAAAlB,QAAA,MACJ;IAEEV,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAQ,aAAA,kBAAAA,aAAA,CAAAP,IAAA,QAAAO,aAAA,kBAAAA,aAAA,CAAAa,IAAA,OACF;IAEE7B,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAA8B,kBAAA,OAAAd,aAAA,kBAAAA,aAAA,CAAAe,WAAA,2BACF;;;;;IAgBJ/B,EAAA,CAAAC,cAAA,SAAoB;IAGRD,EAAA,CAAAgC,SAAA,cAAkE;IAClEhC,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAI/CH,EAAA,CAAAC,cAAA,SAAiD;IAGRD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChDH,EAAA,CAAAC,cAAA,YAAgB;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACxCH,EAAA,CAAAC,cAAA,YAAO;IAAAD,EAAA,CAAAE,MAAA,yDAAkD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;ADrFrG,OAAM,MAAO8B,wBAAwB;EAUnCC,YACUC,iBAAoC,EACpCC,cAA8B,EAC/BC,MAAc;IAFb,KAAAF,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IACf,KAAAC,MAAM,GAANA,MAAM;IAZf,KAAAC,WAAW,GAAG,CAAC;IACf,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,WAAW,GAAiB,EAAE;IAC9B,KAAAC,cAAc,GAAiB,EAAE;IACjC,KAAAC,QAAQ,GAAc,EAAE;IACxB,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAAjB,OAAO,GAAG,KAAK;IA6Jf,KAAAkB,SAAS,GAAY,KAAK;IAC1B,KAAAC,eAAe,GAAY,IAAI;EAxJ5B;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAD,YAAYA,CAAA;IACV,IAAI,CAACZ,cAAc,CAACc,cAAc,CAAC,IAAI,CAAC,CAACC,SAAS,CAAC;MACjDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACX,QAAQ,GAAGU,QAAQ,CAACV,QAAQ;;MAErC,CAAC;MACDY,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD;KACD,CAAC;EACJ;EAIAE,eAAeA,CAAA;IACb,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAC,gBAAgBA,CAAA;IACd,IAAI,CAACD,YAAY,EAAE;EACrB;EAEAA,YAAYA,CAAA;IACV,IAAIE,mBAAmB,GAAG,CAAC,GAAG,IAAI,CAAClB,cAAc,CAAC;IAElD;IACA,IAAI,IAAI,CAACE,eAAe,EAAE;MACxBgB,mBAAmB,GAAGA,mBAAmB,CAACC,MAAM,CAACC,UAAU,IACzDA,UAAU,CAAClC,OAAO,CAACtB,GAAG,KAAK,IAAI,CAACsC,eAAe,CAChD;;IAGH;IACA,IAAI,IAAI,CAACJ,WAAW,EAAE;MACpBoB,mBAAmB,GAAGA,mBAAmB,CAACC,MAAM,CAACC,UAAU,IAAG;QAC5D,OACEA,UAAU,CAACrD,IAAI,CAACsD,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACxB,WAAW,CAACuB,WAAW,EAAE,CAAC,IACtED,UAAU,CAACjC,IAAI,CAACkC,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACxB,WAAW,CAACuB,WAAW,EAAE,CAAC,IACtED,UAAU,CAAClC,OAAO,CAACnB,IAAI,CAACsD,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACxB,WAAW,CAACuB,WAAW,EAAE,CAAC;MAElF,CAAC,CAAC;;IAGJ,IAAI,CAACtB,WAAW,GAAGmB,mBAAmB;IACtC,IAAI,CAACtB,WAAW,GAAG,CAAC;EACtB;EAEA2B,YAAYA,CAAA;IACV,IAAI,CAACrB,eAAe,GAAG,EAAE;IACzB,IAAI,CAACJ,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,WAAW,GAAG,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC;IAC3C,IAAI,CAACJ,WAAW,GAAG,CAAC;EACtB;EACA,IAAI4B,kBAAkBA,CAAA;IACpB,MAAMC,UAAU,GAAG,CAAC,IAAI,CAAC7B,WAAW,GAAG,CAAC,IAAI,IAAI,CAACC,YAAY;IAC7D,MAAM6B,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC5B,YAAY;IAC/C,OAAO,IAAI,CAACE,WAAW,CAAC4B,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EACrD;EAEA,IAAIE,UAAUA,CAAA;IACZ,OAAOC,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC/B,WAAW,CAACgC,MAAM,GAAG,IAAI,CAAClC,YAAY,CAAC;EAC/D;EAEAmC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACpC,WAAW,GAAG,IAAI,CAACgC,UAAU,EAAE;MACtC,IAAI,CAAChC,WAAW,EAAE;;EAEtB;EAEAqC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACrC,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;;EAEtB;EACD;EACAjB,cAAcA,CAACyC,UAAe;IAC7B,IAAI,CAACzB,MAAM,CAACuC,QAAQ,CAAC,CAAC,4CAA4C,GAAG,GAAG,GAAGd,UAAU,CAACxD,GAAG,CAAC,CAAC;IAC3F;IACA;IACA;IACA;IACA;EACF;EAEI;EACA2C,cAAcA,CAAA;IACZ,IAAI,CAACtB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACQ,iBAAiB,CAAC0C,iBAAiB,EAAE,CAAC1B,SAAS,CAAC;MACnDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACZ,cAAc,GAAGW,QAAQ,CAACZ,WAAW;UAC1C,IAAI,CAACA,WAAW,GAAG,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC;;QAE7C,IAAI,CAACf,OAAO,GAAG,KAAK;MACtB,CAAC;MACD4B,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,IAAI,CAAC5B,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EACAH,gBAAgBA,CAACsD,EAAU;IACzB/E,IAAI,CAACgF,IAAI,CAAC;MACRC,KAAK,EAAE,eAAe;MACtBC,IAAI,EAAE,qDAAqD;MAC3DC,IAAI,EAAE,SAAS;MACfC,gBAAgB,EAAE,IAAI;MACtBC,kBAAkB,EAAE,MAAM;MAC1BC,iBAAiB,EAAE,SAAS;MAC5BC,iBAAiB,EAAE,iBAAiB;MACpCC,gBAAgB,EAAE;KACnB,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAI;MACjB,IAAIA,MAAM,CAACC,WAAW,EAAE;QACtB;QACA,IAAI,CAACvD,iBAAiB,CAACX,gBAAgB,CAACsD,EAAE,CAAC,CAAC3B,SAAS,CAClDwC,GAAG,IAAI;UACN5F,IAAI,CAACgF,IAAI,CAAC;YACRC,KAAK,EAAEW,GAAG,CAACC,OAAO;YAClBV,IAAI,EAAE,SAAS;YACfI,iBAAiB,EAAE,IAAI;YACvBF,kBAAkB,EAAE,SAAS;YAC7BS,KAAK,EAAE;WACR,CAAC,CAACL,IAAI,CAAC,MAAK;YACX;YACA,IAAI,CAAC/C,WAAW,GAAG,IAAI,CAACA,WAAW,CAACoB,MAAM,CAAEC,UAAe,IAAKA,UAAU,CAACxD,GAAG,KAAKwE,EAAE,CAAC;UACxF,CAAC,CAAC;QACJ,CAAC,EACAvB,KAAK,IAAI;UACRC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;UAClDxD,IAAI,CAACgF,IAAI,CAAC;YACRC,KAAK,EAAE,OAAO;YACdC,IAAI,EAAE,oDAAoD;YAC1DC,IAAI,EAAE,OAAO;YACbE,kBAAkB,EAAE;WACrB,CAAC;QACJ,CAAC,CACF;;IAEL,CAAC,CAAC;EACJ;EAMFU,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAChD,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACD,SAAS,GAAG,IAAI;KACtB,MAAM,IAAI,IAAI,CAACA,SAAS,EAAE;MACzB,IAAI,CAACA,SAAS,GAAG,KAAK;KACvB,MAAM;MACL,IAAI,CAACC,eAAe,GAAG,IAAI;;EAE/B;EAAC,QAAAiD,CAAA,G;qBAhLU9D,wBAAwB,EAAAjC,EAAA,CAAAgG,iBAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAAlG,EAAA,CAAAgG,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAApG,EAAA,CAAAgG,iBAAA,CAAAK,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAxBtE,wBAAwB;IAAAuE,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCXrC9G,EAAA,CAAAC,cAAA,aAAqB;QAIDD,EAAA,CAAAE,MAAA,4BAAqB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC9BH,EAAA,CAAAC,cAAA,gBAAkH;QACpGD,EAAA,CAAAE,MAAA,UAAG;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAAAH,EAAA,CAAAE,MAAA,iCAC5B;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAIbH,EAAA,CAAAC,cAAA,cAA6B;QAGSD,EAAA,CAAAE,MAAA,yBAAiB;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACnDH,EAAA,CAAAC,cAAA,iBAAuF;QAA3DD,EAAA,CAAAW,UAAA,2BAAAqG,mEAAAC,MAAA;UAAA,OAAAF,GAAA,CAAAnE,eAAA,GAAAqE,MAAA;QAAA,EAA6B,oBAAAC,4DAAA;UAAA,OAAWH,GAAA,CAAAtD,eAAA,EAAiB;QAAA,EAA5B;QACrDzD,EAAA,CAAAC,cAAA,kBAAiB;QAAAD,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACtCH,EAAA,CAAAmH,UAAA,KAAAC,2CAAA,qBAES;QACbpH,EAAA,CAAAG,YAAA,EAAS;QAEbH,EAAA,CAAAC,cAAA,eAAsB;QACQD,EAAA,CAAAE,MAAA,0BAAkB;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACpDH,EAAA,CAAAC,cAAA,eAAyB;QAEdD,EAAA,CAAAW,UAAA,2BAAA0G,kEAAAJ,MAAA;UAAA,OAAAF,GAAA,CAAAvE,WAAA,GAAAyE,MAAA;QAAA,EAAyB,mBAAAK,0DAAA;UAAA,OAAUP,GAAA,CAAApD,gBAAA,EAAkB;QAAA,EAA5B;QADhC3D,EAAA,CAAAG,YAAA,EAC8D;QAC9DH,EAAA,CAAAC,cAAA,kBAAwD;QAC1CD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAIvCH,EAAA,CAAAC,cAAA,eAA6C;QACOD,EAAA,CAAAW,UAAA,mBAAA4G,2DAAA;UAAA,OAASR,GAAA,CAAA9C,YAAA,EAAc;QAAA,EAAC;QACpEjE,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAACH,EAAA,CAAAE,MAAA,eAC/B;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAKzBH,EAAA,CAAAC,cAAA,eAA6B;QAMND,EAAA,CAAAE,MAAA,iBACH;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAELH,EAAA,CAAAC,cAAA,UAAK;QACDD,EAAA,CAAAE,MAAA,yBACJ;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAC,cAAA,UAAI;QAAAD,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAErBH,EAAA,CAAAC,cAAA,cAAgB;QAAAD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAGpCH,EAAA,CAAAC,cAAA,aAAO;QACHD,EAAA,CAAAmH,UAAA,KAAAK,uCAAA,kBAwBK;QACLxH,EAAA,CAAAmH,UAAA,KAAAM,uCAAA,iBAOK;QACLzH,EAAA,CAAAmH,UAAA,KAAAO,uCAAA,iBAQK;QACT1H,EAAA,CAAAG,YAAA,EAAQ;QAGhBH,EAAA,CAAAC,cAAA,eAAmE;QAEKD,EAAA,CAAAW,UAAA,mBAAAgH,2DAAA;UAAA,OAASZ,GAAA,CAAApC,YAAA,EAAc;QAAA,EAAC;QAAC3E,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAE9GH,EAAA,CAAAC,cAAA,eAAkB;QAAAD,EAAA,CAAAE,MAAA,IAA0C;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAClEH,EAAA,CAAAC,cAAA,WAAK;QACwED,EAAA,CAAAW,UAAA,mBAAAiH,2DAAA;UAAA,OAASb,GAAA,CAAArC,QAAA,EAAU;QAAA,EAAC;QAAC1E,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAG,YAAA,EAAS;;;QA9F3EH,EAAA,CAAAO,SAAA,IAA6B;QAA7BP,EAAA,CAAAI,UAAA,YAAA2G,GAAA,CAAAnE,eAAA,CAA6B;QAEzB5C,EAAA,CAAAO,SAAA,GAAW;QAAXP,EAAA,CAAAI,UAAA,YAAA2G,GAAA,CAAApE,QAAA,CAAW;QAShC3C,EAAA,CAAAO,SAAA,GAAyB;QAAzBP,EAAA,CAAAI,UAAA,YAAA2G,GAAA,CAAAvE,WAAA,CAAyB;QAgCTxC,EAAA,CAAAO,SAAA,IAAqB;QAArBP,EAAA,CAAAI,UAAA,YAAA2G,GAAA,CAAA7C,kBAAA,CAAqB;QAyB3ClE,EAAA,CAAAO,SAAA,GAAa;QAAbP,EAAA,CAAAI,UAAA,SAAA2G,GAAA,CAAApF,OAAA,CAAa;QAQb3B,EAAA,CAAAO,SAAA,GAA0C;QAA1CP,EAAA,CAAAI,UAAA,UAAA2G,GAAA,CAAApF,OAAA,IAAAoF,GAAA,CAAAtE,WAAA,CAAAgC,MAAA,OAA0C;QAclBzE,EAAA,CAAAO,SAAA,GAA8B;QAA9BP,EAAA,CAAAI,UAAA,aAAA2G,GAAA,CAAAzE,WAAA,OAA8B;QAEjDtC,EAAA,CAAAO,SAAA,GAA0C;QAA1CP,EAAA,CAAAQ,kBAAA,UAAAuG,GAAA,CAAAzE,WAAA,UAAAyE,GAAA,CAAAzC,UAAA,KAA0C;QAEvBtE,EAAA,CAAAO,SAAA,GAAuC;QAAvCP,EAAA,CAAAI,UAAA,aAAA2G,GAAA,CAAAzE,WAAA,KAAAyE,GAAA,CAAAzC,UAAA,CAAuC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}