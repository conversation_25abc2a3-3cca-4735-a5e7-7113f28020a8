{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { AdministrationRoutingModule } from './administration-routing.module';\nimport { MaterialModule } from '../material';\nimport * as i0 from \"@angular/core\";\nexport let AdministrationModule = /*#__PURE__*/(() => {\n  class AdministrationModule {\n    static #_ = this.ɵfac = function AdministrationModule_Factory(t) {\n      return new (t || AdministrationModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AdministrationModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, AdministrationRoutingModule, MaterialModule]\n    });\n  }\n  return AdministrationModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}