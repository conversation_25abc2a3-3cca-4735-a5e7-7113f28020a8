{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../services/attendance.service\";\nimport * as i3 from \"../../../services/user.service\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/core\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/card\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/input\";\nimport * as i11 from \"@angular/material/form-field\";\nimport * as i12 from \"@angular/forms\";\nimport * as i13 from \"@angular/material/paginator\";\nimport * as i14 from \"@angular/material/progress-spinner\";\nimport * as i15 from \"@angular/material/select\";\nimport * as i16 from \"@angular/material/tooltip\";\nfunction AttendanceComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵelement(1, \"mat-spinner\", 12);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading attendance data...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AttendanceComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"mat-icon\", 14);\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"Error Loading Attendance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function AttendanceComponent_div_14_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.refreshData());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction AttendanceComponent_div_15_mat_card_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 40)(1, \"mat-card-content\")(2, \"div\", 18)(3, \"div\", 19)(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"warning\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 20)(7, \"h3\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\");\n    i0.ɵɵtext(10, \"Low Attendance\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r5.lowAttendanceSubjects);\n  }\n}\nfunction AttendanceComponent_div_15_div_52_mat_card_1_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const record_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", record_r10.lateClasses, \" Late\");\n  }\n}\nfunction AttendanceComponent_div_15_div_52_mat_card_1_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"access_time\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const record_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r12 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Last attended: \", ctx_r12.formatDate(record_r10.lastAttended), \"\");\n  }\n}\nfunction AttendanceComponent_div_15_div_52_mat_card_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 43)(1, \"mat-card-header\")(2, \"div\", 44)(3, \"div\", 45)(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"subject\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 46)(7, \"h3\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 47);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 48);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"mat-card-content\")(14, \"div\", 49)(15, \"span\", 50);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 51)(18, \"div\", 52)(19, \"div\", 53)(20, \"mat-icon\");\n    i0.ɵɵtext(21, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\");\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 54)(25, \"mat-icon\");\n    i0.ɵɵtext(26, \"cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\");\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"div\", 52);\n    i0.ɵɵtemplate(30, AttendanceComponent_div_15_div_52_mat_card_1_div_30_Template, 5, 1, \"div\", 55);\n    i0.ɵɵelementStart(31, \"div\", 56)(32, \"mat-icon\");\n    i0.ɵɵtext(33, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"span\");\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(36, AttendanceComponent_div_15_div_52_mat_card_1_div_36_Template, 5, 1, \"div\", 57);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const record_r10 = ctx.$implicit;\n    const ctx_r9 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r9.getAttendanceClass(record_r10.attendancePercentage));\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(record_r10.subject.subjectName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(record_r10.subject.code);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r9.getAttendanceClass(record_r10.attendancePercentage));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", record_r10.attendancePercentage, \"% \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", record_r10.class.className, \" - \", record_r10.class.section, \"\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", record_r10.presentClasses, \" Present\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", record_r10.absentClasses, \" Absent\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", record_r10.lateClasses > 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", record_r10.totalClasses, \" Total\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", record_r10.lastAttended);\n  }\n}\nfunction AttendanceComponent_div_15_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵtemplate(1, AttendanceComponent_div_15_div_52_mat_card_1_Template, 37, 12, \"mat-card\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.paginatedRecords);\n  }\n}\nfunction AttendanceComponent_div_15_div_53_p_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"No records match your current filters.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AttendanceComponent_div_15_div_53_p_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"No attendance records available yet.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AttendanceComponent_div_15_div_53_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function AttendanceComponent_div_15_div_53_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r18.clearFilters());\n    });\n    i0.ɵɵtext(1, \" Clear Filters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AttendanceComponent_div_15_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"mat-icon\", 61);\n    i0.ɵɵtext(2, \"fact_check\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"No Attendance Records Found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, AttendanceComponent_div_15_div_53_p_5_Template, 2, 0, \"p\", 10);\n    i0.ɵɵtemplate(6, AttendanceComponent_div_15_div_53_p_6_Template, 2, 0, \"p\", 10);\n    i0.ɵɵtemplate(7, AttendanceComponent_div_15_div_53_button_7_Template, 2, 0, \"button\", 62);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.searchQuery || ctx_r7.minAttendance > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r7.searchQuery && ctx_r7.minAttendance === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.searchQuery || ctx_r7.minAttendance > 0);\n  }\n}\nconst _c0 = function () {\n  return [5, 10, 20];\n};\nfunction AttendanceComponent_div_15_div_54_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"mat-paginator\", 65);\n    i0.ɵɵlistener(\"page\", function AttendanceComponent_div_15_div_54_Template_mat_paginator_page_1_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r20.goToPage($event.pageIndex + 1));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"length\", ctx_r8.filteredRecords.length)(\"pageSize\", ctx_r8.itemsPerPage)(\"pageSizeOptions\", i0.ɵɵpureFunction0(4, _c0))(\"pageIndex\", ctx_r8.currentPage - 1);\n  }\n}\nfunction AttendanceComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 16)(2, \"mat-card\", 17)(3, \"mat-card-content\")(4, \"div\", 18)(5, \"div\", 19)(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"assessment\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 20)(9, \"h3\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"p\");\n    i0.ɵɵtext(12, \"Overall Attendance\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(13, \"mat-card\", 21)(14, \"mat-card-content\")(15, \"div\", 18)(16, \"div\", 19)(17, \"mat-icon\");\n    i0.ɵɵtext(18, \"subject\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 20)(20, \"h3\");\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"p\");\n    i0.ɵɵtext(23, \"Total Subjects\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵtemplate(24, AttendanceComponent_div_15_mat_card_24_Template, 11, 1, \"mat-card\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"mat-card\", 23)(26, \"mat-card-content\")(27, \"div\", 24)(28, \"div\", 25)(29, \"mat-form-field\", 26)(30, \"mat-label\");\n    i0.ɵɵtext(31, \"Search Subjects\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"input\", 27);\n    i0.ɵɵlistener(\"ngModelChange\", function AttendanceComponent_div_15_Template_input_ngModelChange_32_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.searchQuery = $event);\n    })(\"input\", function AttendanceComponent_div_15_Template_input_input_32_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.onSearch());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"mat-icon\", 28);\n    i0.ɵɵtext(34, \"search\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"div\", 29)(36, \"mat-form-field\", 26)(37, \"mat-label\");\n    i0.ɵɵtext(38, \"Minimum Attendance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"mat-select\", 30);\n    i0.ɵɵlistener(\"ngModelChange\", function AttendanceComponent_div_15_Template_mat_select_ngModelChange_39_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.minAttendance = $event);\n    })(\"selectionChange\", function AttendanceComponent_div_15_Template_mat_select_selectionChange_39_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.onFilterChange());\n    });\n    i0.ɵɵelementStart(40, \"mat-option\", 31);\n    i0.ɵɵtext(41, \"All\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"mat-option\", 32);\n    i0.ɵɵtext(43, \"75% and above\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"mat-option\", 33);\n    i0.ɵɵtext(45, \"80% and above\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"mat-option\", 34);\n    i0.ɵɵtext(47, \"90% and above\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(48, \"div\", 35)(49, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function AttendanceComponent_div_15_Template_button_click_49_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.clearFilters());\n    });\n    i0.ɵɵelementStart(50, \"mat-icon\");\n    i0.ɵɵtext(51, \"clear\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵtemplate(52, AttendanceComponent_div_15_div_52_Template, 2, 1, \"div\", 37);\n    i0.ɵɵtemplate(53, AttendanceComponent_div_15_div_53_Template, 8, 3, \"div\", 38);\n    i0.ɵɵtemplate(54, AttendanceComponent_div_15_div_54_Template, 2, 5, \"div\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.overallAttendance, \"%\");\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r2.totalSubjects);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.lowAttendanceSubjects > 0);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.searchQuery);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.minAttendance);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.paginatedRecords.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.loading && ctx_r2.paginatedRecords.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.totalPages > 1);\n  }\n}\nexport class AttendanceComponent {\n  constructor(router, attendanceService, userService, snackBar) {\n    this.router = router;\n    this.attendanceService = attendanceService;\n    this.userService = userService;\n    this.snackBar = snackBar;\n    // UI State\n    this.loading = true;\n    this.error = null;\n    this.attendanceRecords = [];\n    this.filteredRecords = [];\n    // Filters\n    this.searchQuery = '';\n    this.selectedSubject = '';\n    this.minAttendance = 0;\n    // Pagination\n    this.currentPage = 1;\n    this.itemsPerPage = 10;\n    // Summary\n    this.overallAttendance = 0;\n    this.totalSubjects = 0;\n    this.lowAttendanceSubjects = 0;\n  }\n  ngOnInit() {\n    this.currentUser = this.userService.getUserFromLocalStorage()?.user;\n    if (this.currentUser) {\n      this.loadStudentAttendance();\n    } else {\n      this.error = 'User not found';\n      this.loading = false;\n    }\n  }\n  loadStudentAttendance() {\n    this.loading = true;\n    this.error = null;\n    this.attendanceService.getStudentAttendance(this.currentUser._id).subscribe({\n      next: response => {\n        if (response.success) {\n          this.processAttendanceData(response.attendance);\n          this.calculateSummary();\n        } else {\n          this.error = 'Failed to load attendance data';\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading attendance:', error);\n        this.error = 'Error loading attendance data';\n        this.loading = false;\n        this.snackBar.open('Failed to load attendance data', 'Close', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  processAttendanceData(attendanceData) {\n    // Group attendance by subject\n    const subjectMap = new Map();\n    attendanceData.forEach(record => {\n      const subjectId = record.subject._id;\n      if (!subjectMap.has(subjectId)) {\n        subjectMap.set(subjectId, {\n          _id: subjectId,\n          subject: record.subject,\n          class: record.class,\n          totalClasses: 0,\n          presentClasses: 0,\n          absentClasses: 0,\n          lateClasses: 0,\n          attendancePercentage: 0,\n          lastAttended: null\n        });\n      }\n      const subjectRecord = subjectMap.get(subjectId);\n      subjectRecord.totalClasses++;\n      switch (record.status) {\n        case 'present':\n          subjectRecord.presentClasses++;\n          break;\n        case 'absent':\n          subjectRecord.absentClasses++;\n          break;\n        case 'late':\n          subjectRecord.lateClasses++;\n          subjectRecord.presentClasses++; // Late is considered present\n          break;\n      }\n      // Update last attended date\n      const recordDate = new Date(record.date);\n      if (!subjectRecord.lastAttended || recordDate > subjectRecord.lastAttended) {\n        if (record.status === 'present' || record.status === 'late') {\n          subjectRecord.lastAttended = recordDate;\n        }\n      }\n    });\n    // Calculate attendance percentages\n    subjectMap.forEach(record => {\n      if (record.totalClasses > 0) {\n        record.attendancePercentage = Math.round(record.presentClasses / record.totalClasses * 100);\n      }\n    });\n    this.attendanceRecords = Array.from(subjectMap.values());\n    this.applyFilters();\n  }\n  calculateSummary() {\n    this.totalSubjects = this.attendanceRecords.length;\n    if (this.totalSubjects > 0) {\n      const totalPercentage = this.attendanceRecords.reduce((sum, record) => sum + record.attendancePercentage, 0);\n      this.overallAttendance = Math.round(totalPercentage / this.totalSubjects);\n      this.lowAttendanceSubjects = this.attendanceRecords.filter(record => record.attendancePercentage < 75).length;\n    }\n  }\n  applyFilters() {\n    let filtered = [...this.attendanceRecords];\n    if (this.searchQuery) {\n      const query = this.searchQuery.toLowerCase();\n      filtered = filtered.filter(record => record.subject.subjectName.toLowerCase().includes(query) || record.subject.code.toLowerCase().includes(query));\n    }\n    if (this.selectedSubject) {\n      filtered = filtered.filter(record => record.subject._id === this.selectedSubject);\n    }\n    if (this.minAttendance > 0) {\n      filtered = filtered.filter(record => record.attendancePercentage >= this.minAttendance);\n    }\n    this.filteredRecords = filtered;\n  }\n  onSearch() {\n    this.applyFilters();\n  }\n  onFilterChange() {\n    this.applyFilters();\n  }\n  clearFilters() {\n    this.searchQuery = '';\n    this.selectedSubject = '';\n    this.minAttendance = 0;\n    this.applyFilters();\n  }\n  refreshData() {\n    this.loadStudentAttendance();\n  }\n  // Pagination\n  get paginatedRecords() {\n    const startIndex = (this.currentPage - 1) * this.itemsPerPage;\n    return this.filteredRecords.slice(startIndex, startIndex + this.itemsPerPage);\n  }\n  get totalPages() {\n    return Math.ceil(this.filteredRecords.length / this.itemsPerPage);\n  }\n  nextPage() {\n    if (this.currentPage < this.totalPages) {\n      this.currentPage++;\n    }\n  }\n  previousPage() {\n    if (this.currentPage > 1) {\n      this.currentPage--;\n    }\n  }\n  goToPage(page) {\n    if (page >= 1 && page <= this.totalPages) {\n      this.currentPage = page;\n    }\n  }\n  // Utility methods\n  getAttendanceClass(percentage) {\n    if (percentage >= 90) return 'excellent';\n    if (percentage >= 80) return 'good';\n    if (percentage >= 75) return 'average';\n    return 'poor';\n  }\n  formatDate(date) {\n    return new Date(date).toLocaleDateString();\n  }\n  getUniqueSubjects() {\n    return this.attendanceRecords.map(record => record.subject);\n  }\n  static #_ = this.ɵfac = function AttendanceComponent_Factory(t) {\n    return new (t || AttendanceComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AttendanceService), i0.ɵɵdirectiveInject(i3.UserService), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AttendanceComponent,\n    selectors: [[\"app-attendance\"]],\n    decls: 16,\n    vars: 3,\n    consts: [[1, \"student-attendance-container\"], [1, \"page-header\"], [1, \"header-content\"], [1, \"page-title\"], [1, \"title-icon\"], [1, \"page-subtitle\"], [1, \"header-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Refresh\", 1, \"refresh-btn\", 3, \"click\"], [\"class\", \"loading-state\", 4, \"ngIf\"], [\"class\", \"error-state\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"loading-state\"], [\"diameter\", \"40\"], [1, \"error-state\"], [1, \"error-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"summary-cards\"], [1, \"summary-card\", \"overall\"], [1, \"summary-content\"], [1, \"summary-icon\"], [1, \"summary-info\"], [1, \"summary-card\", \"subjects\"], [\"class\", \"summary-card warning\", 4, \"ngIf\"], [1, \"filters-card\"], [1, \"filters-row\"], [1, \"search-field\"], [\"appearance\", \"outline\", 1, \"compact-field\"], [\"matInput\", \"\", \"placeholder\", \"Search by subject name or code...\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [\"matSuffix\", \"\"], [1, \"filter-field\"], [3, \"ngModel\", \"ngModelChange\", \"selectionChange\"], [\"value\", \"0\"], [\"value\", \"75\"], [\"value\", \"80\"], [\"value\", \"90\"], [1, \"filter-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Clear Filters\", 1, \"clear-btn\", 3, \"click\"], [\"class\", \"attendance-grid\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"pagination-container\", 4, \"ngIf\"], [1, \"summary-card\", \"warning\"], [1, \"attendance-grid\"], [\"class\", \"attendance-card\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"attendance-card\", 3, \"ngClass\"], [1, \"subject-header\"], [1, \"subject-icon\"], [1, \"subject-info\"], [1, \"subject-code\"], [1, \"attendance-percentage\", 3, \"ngClass\"], [1, \"class-info\"], [1, \"class-name\"], [1, \"attendance-stats\"], [1, \"stat-row\"], [1, \"stat-item\", \"present\"], [1, \"stat-item\", \"absent\"], [\"class\", \"stat-item late\", 4, \"ngIf\"], [1, \"stat-item\", \"total\"], [\"class\", \"last-attended\", 4, \"ngIf\"], [1, \"stat-item\", \"late\"], [1, \"last-attended\"], [1, \"empty-state\"], [1, \"empty-icon\"], [\"mat-button\", \"\", 3, \"click\", 4, \"ngIf\"], [\"mat-button\", \"\", 3, \"click\"], [1, \"pagination-container\"], [\"showFirstLastButtons\", \"\", 3, \"length\", \"pageSize\", \"pageSizeOptions\", \"pageIndex\", \"page\"]],\n    template: function AttendanceComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3)(4, \"mat-icon\", 4);\n        i0.ɵɵtext(5, \"fact_check\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(6, \" My Attendance \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"p\", 5);\n        i0.ɵɵtext(8, \"Track your attendance across all subjects\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(9, \"div\", 6)(10, \"button\", 7);\n        i0.ɵɵlistener(\"click\", function AttendanceComponent_Template_button_click_10_listener() {\n          return ctx.refreshData();\n        });\n        i0.ɵɵelementStart(11, \"mat-icon\");\n        i0.ɵɵtext(12, \"refresh\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵtemplate(13, AttendanceComponent_div_13_Template, 4, 0, \"div\", 8);\n        i0.ɵɵtemplate(14, AttendanceComponent_div_14_Template, 11, 1, \"div\", 9);\n        i0.ɵɵtemplate(15, AttendanceComponent_div_15_Template, 55, 8, \"div\", 10);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(13);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error);\n      }\n    },\n    dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i6.MatOption, i7.MatButton, i7.MatIconButton, i8.MatCard, i8.MatCardContent, i8.MatCardHeader, i9.MatIcon, i10.MatInput, i11.MatFormField, i11.MatLabel, i11.MatSuffix, i12.DefaultValueAccessor, i12.NgControlStatus, i12.NgModel, i13.MatPaginator, i14.MatProgressSpinner, i15.MatSelect, i16.MatTooltip],\n    styles: [\".student-attendance-container[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  min-height: 100vh;\\n  padding: 15px;\\n}\\n\\n\\n\\n.page-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n  padding: 15px 0;\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.page-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #2c3e50;\\n  font-size: 1.8rem;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n\\n.title-icon[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  color: #3498db;\\n}\\n\\n.page-subtitle[_ngcontent-%COMP%] {\\n  margin: 5px 0 0 0;\\n  color: #7f8c8d;\\n  font-size: 0.9rem;\\n}\\n\\n.header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.refresh-btn[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n\\n.refresh-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n\\n\\n\\n.summary-cards[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 20px;\\n}\\n\\n.summary-card[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease;\\n}\\n\\n.summary-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\\n}\\n\\n.summary-card.overall[_ngcontent-%COMP%] {\\n  border-left: 4px solid #3498db;\\n}\\n\\n.summary-card.subjects[_ngcontent-%COMP%] {\\n  border-left: 4px solid #9b59b6;\\n}\\n\\n.summary-card.warning[_ngcontent-%COMP%] {\\n  border-left: 4px solid #e74c3c;\\n}\\n\\n.summary-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n  padding: 10px;\\n}\\n\\n.summary-icon[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n}\\n\\n.summary-card.overall[_ngcontent-%COMP%]   .summary-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);\\n}\\n\\n.summary-card.subjects[_ngcontent-%COMP%]   .summary-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);\\n}\\n\\n.summary-card.warning[_ngcontent-%COMP%]   .summary-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);\\n}\\n\\n.summary-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n}\\n\\n.summary-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #2c3e50;\\n  font-size: 1.8rem;\\n  font-weight: 600;\\n}\\n\\n.summary-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #7f8c8d;\\n  font-size: 0.9rem;\\n}\\n\\n\\n\\n.filters-card[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.filters-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  padding: 15px !important;\\n}\\n\\n.filters-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n}\\n\\n.search-field[_ngcontent-%COMP%] {\\n  flex: 2;\\n}\\n\\n.filter-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.compact-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.compact-field[_ngcontent-%COMP%]   .mat-form-field-wrapper[_ngcontent-%COMP%] {\\n  padding-bottom: 0;\\n}\\n\\n.compact-field[_ngcontent-%COMP%]   .mat-form-field-infix[_ngcontent-%COMP%] {\\n  padding: 8px 0;\\n  border-top: none;\\n}\\n\\n.compact-field[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n\\n.clear-btn[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  transition: all 0.3s ease;\\n}\\n\\n.clear-btn[_ngcontent-%COMP%]:hover {\\n  color: #495057;\\n  background-color: #f8f9fa;\\n}\\n\\n\\n\\n.attendance-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 20px;\\n}\\n\\n.attendance-card[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease;\\n  border-left: 4px solid #ecf0f1;\\n}\\n\\n.attendance-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\\n}\\n\\n.attendance-card.excellent[_ngcontent-%COMP%] {\\n  border-left-color: #27ae60;\\n}\\n\\n.attendance-card.good[_ngcontent-%COMP%] {\\n  border-left-color: #3498db;\\n}\\n\\n.attendance-card.average[_ngcontent-%COMP%] {\\n  border-left-color: #f39c12;\\n}\\n\\n.attendance-card.poor[_ngcontent-%COMP%] {\\n  border-left-color: #e74c3c;\\n}\\n\\n.subject-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  width: 100%;\\n}\\n\\n.subject-icon[_ngcontent-%COMP%] {\\n  width: 45px;\\n  height: 45px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n}\\n\\n.subject-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.3rem;\\n}\\n\\n.subject-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.subject-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #2c3e50;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n}\\n\\n.subject-code[_ngcontent-%COMP%] {\\n  background: #34495e;\\n  color: white;\\n  padding: 2px 8px;\\n  border-radius: 10px;\\n  font-size: 0.7rem;\\n  font-weight: 600;\\n}\\n\\n.attendance-percentage[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  padding: 8px 12px;\\n  border-radius: 20px;\\n  color: white;\\n}\\n\\n.attendance-percentage.excellent[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);\\n}\\n\\n.attendance-percentage.good[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);\\n}\\n\\n.attendance-percentage.average[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);\\n}\\n\\n.attendance-percentage.poor[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);\\n}\\n\\n.class-info[_ngcontent-%COMP%] {\\n  margin: 15px 0 10px 0;\\n  padding: 8px 12px;\\n  background: #f8f9fa;\\n  border-radius: 6px;\\n  border-left: 3px solid #3498db;\\n}\\n\\n.class-name[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-weight: 600;\\n  font-size: 0.9rem;\\n}\\n\\n.attendance-stats[_ngcontent-%COMP%] {\\n  margin: 15px 0;\\n}\\n\\n.stat-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n  margin-bottom: 8px;\\n}\\n\\n.stat-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  font-size: 0.85rem;\\n  font-weight: 600;\\n  flex: 1;\\n}\\n\\n.stat-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.stat-item.present[_ngcontent-%COMP%] {\\n  color: #27ae60;\\n}\\n\\n.stat-item.absent[_ngcontent-%COMP%] {\\n  color: #e74c3c;\\n}\\n\\n.stat-item.late[_ngcontent-%COMP%] {\\n  color: #f39c12;\\n}\\n\\n.stat-item.total[_ngcontent-%COMP%] {\\n  color: #34495e;\\n}\\n\\n.last-attended[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  margin-top: 15px;\\n  padding-top: 15px;\\n  border-top: 1px solid #ecf0f1;\\n  color: #7f8c8d;\\n  font-size: 0.8rem;\\n}\\n\\n.last-attended[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n\\n\\n.loading-state[_ngcontent-%COMP%], .error-state[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n  text-align: center;\\n}\\n\\n.loading-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .error-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n  color: #7f8c8d;\\n}\\n\\n.error-state[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  margin-bottom: 10px;\\n}\\n\\n.error-icon[_ngcontent-%COMP%], .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  color: #bdc3c7;\\n  margin-bottom: 15px;\\n}\\n\\n.error-icon[_ngcontent-%COMP%] {\\n  color: #e74c3c;\\n}\\n\\n\\n\\n.pagination-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-top: 20px;\\n}\\n\\n\\n\\n@media (max-width: 1200px) {\\n  .student-attendance-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n\\n  .summary-cards[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n    gap: 15px;\\n  }\\n\\n  .attendance-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\\n    gap: 15px;\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .page-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 15px;\\n  }\\n\\n  .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  .header-actions[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: space-between;\\n  }\\n\\n  .summary-cards[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 15px;\\n  }\\n\\n  .filters-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 10px;\\n  }\\n\\n  .search-field[_ngcontent-%COMP%], .filter-field[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n\\n  .filter-actions[_ngcontent-%COMP%] {\\n    width: 100%;\\n    display: flex;\\n    justify-content: flex-end;\\n  }\\n\\n  .attendance-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 15px;\\n  }\\n\\n  .subject-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 10px;\\n  }\\n\\n  .attendance-percentage[_ngcontent-%COMP%] {\\n    align-self: flex-end;\\n    font-size: 1.2rem;\\n  }\\n\\n  .stat-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 8px;\\n  }\\n}\\n\\n@media (max-width: 576px) {\\n  .student-attendance-container[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n\\n  .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.3rem;\\n  }\\n\\n  .title-icon[_ngcontent-%COMP%] {\\n    font-size: 1.3rem;\\n  }\\n\\n  .summary-content[_ngcontent-%COMP%] {\\n    gap: 10px;\\n    padding: 8px;\\n  }\\n\\n  .summary-icon[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n\\n  .summary-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n    font-size: 1.2rem;\\n  }\\n\\n  .summary-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  .summary-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n\\n  .attendance-card[_ngcontent-%COMP%] {\\n    margin-bottom: 15px;\\n  }\\n\\n  .subject-icon[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n\\n  .subject-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n\\n  .subject-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n\\n  .subject-code[_ngcontent-%COMP%] {\\n    font-size: 0.65rem;\\n    padding: 1px 6px;\\n  }\\n\\n  .attendance-percentage[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n    padding: 6px 10px;\\n  }\\n\\n  .class-name[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n\\n  .stat-item[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n\\n  .last-attended[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "AttendanceComponent_div_14_Template_button_click_7_listener", "ɵɵrestoreView", "_r4", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "refreshData", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "error", "ctx_r5", "lowAttendanceSubjects", "ɵɵtextInterpolate1", "record_r10", "lateClasses", "ctx_r12", "formatDate", "lastAttended", "ɵɵtemplate", "AttendanceComponent_div_15_div_52_mat_card_1_div_30_Template", "AttendanceComponent_div_15_div_52_mat_card_1_div_36_Template", "ɵɵproperty", "ctx_r9", "getAttendanceClass", "attendancePercentage", "subject", "subjectName", "code", "ɵɵtextInterpolate2", "class", "className", "section", "presentClasses", "absentClasses", "totalClasses", "AttendanceComponent_div_15_div_52_mat_card_1_Template", "ctx_r6", "paginatedRecords", "AttendanceComponent_div_15_div_53_button_7_Template_button_click_0_listener", "_r19", "ctx_r18", "clearFilters", "AttendanceComponent_div_15_div_53_p_5_Template", "AttendanceComponent_div_15_div_53_p_6_Template", "AttendanceComponent_div_15_div_53_button_7_Template", "ctx_r7", "searchQuery", "minAttendance", "AttendanceComponent_div_15_div_54_Template_mat_paginator_page_1_listener", "$event", "_r21", "ctx_r20", "goToPage", "pageIndex", "ctx_r8", "filteredRecords", "length", "itemsPerPage", "ɵɵpureFunction0", "_c0", "currentPage", "AttendanceComponent_div_15_mat_card_24_Template", "AttendanceComponent_div_15_Template_input_ngModelChange_32_listener", "_r23", "ctx_r22", "AttendanceComponent_div_15_Template_input_input_32_listener", "ctx_r24", "onSearch", "AttendanceComponent_div_15_Template_mat_select_ngModelChange_39_listener", "ctx_r25", "AttendanceComponent_div_15_Template_mat_select_selectionChange_39_listener", "ctx_r26", "onFilterChange", "AttendanceComponent_div_15_Template_button_click_49_listener", "ctx_r27", "AttendanceComponent_div_15_div_52_Template", "AttendanceComponent_div_15_div_53_Template", "AttendanceComponent_div_15_div_54_Template", "ctx_r2", "overallAttendance", "totalSubjects", "loading", "totalPages", "AttendanceComponent", "constructor", "router", "attendanceService", "userService", "snackBar", "attendanceRecords", "selectedSubject", "ngOnInit", "currentUser", "getUserFromLocalStorage", "user", "loadStudentAttendance", "getStudentAttendance", "_id", "subscribe", "next", "response", "success", "processAttendanceData", "attendance", "calculateSummary", "console", "open", "duration", "attendanceData", "subjectMap", "Map", "for<PERSON>ach", "record", "subjectId", "has", "set", "subjectRecord", "get", "status", "recordDate", "Date", "date", "Math", "round", "Array", "from", "values", "applyFilters", "totalPercentage", "reduce", "sum", "filter", "filtered", "query", "toLowerCase", "includes", "startIndex", "slice", "ceil", "nextPage", "previousPage", "page", "percentage", "toLocaleDateString", "getUniqueSubjects", "map", "_", "ɵɵdirectiveInject", "i1", "Router", "i2", "AttendanceService", "i3", "UserService", "i4", "MatSnackBar", "_2", "selectors", "decls", "vars", "consts", "template", "AttendanceComponent_Template", "rf", "ctx", "AttendanceComponent_Template_button_click_10_listener", "AttendanceComponent_div_13_Template", "AttendanceComponent_div_14_Template", "AttendanceComponent_div_15_Template"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\student-dashboard\\attendance\\attendance.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\student-dashboard\\attendance\\attendance.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { AttendanceService } from '../../../services/attendance.service';\r\nimport { UserService } from '../../../services/user.service';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\n\r\ninterface AttendanceRecord {\r\n  _id: string;\r\n  subject: {\r\n    _id: string;\r\n    subjectName: string;\r\n    code: string;\r\n  };\r\n  class: {\r\n    _id: string;\r\n    className: string;\r\n    section: string;\r\n  };\r\n  totalClasses: number;\r\n  presentClasses: number;\r\n  absentClasses: number;\r\n  lateClasses: number;\r\n  attendancePercentage: number;\r\n  lastAttended?: Date;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-attendance',\r\n  templateUrl: './attendance.component.html',\r\n  styleUrls: ['./attendance.component.css']\r\n})\r\nexport class AttendanceComponent implements OnInit {\r\n  // UI State\r\n  loading = true;\r\n  error: string | null = null;\r\n\r\n  // Data\r\n  currentUser: any;\r\n  attendanceRecords: AttendanceRecord[] = [];\r\n  filteredRecords: AttendanceRecord[] = [];\r\n\r\n  // Filters\r\n  searchQuery = '';\r\n  selectedSubject = '';\r\n  minAttendance = 0;\r\n\r\n  // Pagination\r\n  currentPage = 1;\r\n  itemsPerPage = 10;\r\n\r\n  // Summary\r\n  overallAttendance = 0;\r\n  totalSubjects = 0;\r\n  lowAttendanceSubjects = 0;\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private attendanceService: AttendanceService,\r\n    private userService: UserService,\r\n    private snackBar: MatSnackBar\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.currentUser = this.userService.getUserFromLocalStorage()?.user;\r\n    if (this.currentUser) {\r\n      this.loadStudentAttendance();\r\n    } else {\r\n      this.error = 'User not found';\r\n      this.loading = false;\r\n    }\r\n  }\r\n\r\n  loadStudentAttendance(): void {\r\n    this.loading = true;\r\n    this.error = null;\r\n\r\n    this.attendanceService.getStudentAttendance(this.currentUser._id).subscribe({\r\n      next: (response: any) => {\r\n        if (response.success) {\r\n          this.processAttendanceData(response.attendance);\r\n          this.calculateSummary();\r\n        } else {\r\n          this.error = 'Failed to load attendance data';\r\n        }\r\n        this.loading = false;\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Error loading attendance:', error);\r\n        this.error = 'Error loading attendance data';\r\n        this.loading = false;\r\n        this.snackBar.open('Failed to load attendance data', 'Close', { duration: 3000 });\r\n      }\r\n    });\r\n  }\r\n\r\n  processAttendanceData(attendanceData: any[]): void {\r\n    // Group attendance by subject\r\n    const subjectMap = new Map<string, any>();\r\n\r\n    attendanceData.forEach(record => {\r\n      const subjectId = record.subject._id;\r\n\r\n      if (!subjectMap.has(subjectId)) {\r\n        subjectMap.set(subjectId, {\r\n          _id: subjectId,\r\n          subject: record.subject,\r\n          class: record.class,\r\n          totalClasses: 0,\r\n          presentClasses: 0,\r\n          absentClasses: 0,\r\n          lateClasses: 0,\r\n          attendancePercentage: 0,\r\n          lastAttended: null\r\n        });\r\n      }\r\n\r\n      const subjectRecord = subjectMap.get(subjectId)!;\r\n      subjectRecord.totalClasses++;\r\n\r\n      switch (record.status) {\r\n        case 'present':\r\n          subjectRecord.presentClasses++;\r\n          break;\r\n        case 'absent':\r\n          subjectRecord.absentClasses++;\r\n          break;\r\n        case 'late':\r\n          subjectRecord.lateClasses++;\r\n          subjectRecord.presentClasses++; // Late is considered present\r\n          break;\r\n      }\r\n\r\n      // Update last attended date\r\n      const recordDate = new Date(record.date);\r\n      if (!subjectRecord.lastAttended || recordDate > subjectRecord.lastAttended) {\r\n        if (record.status === 'present' || record.status === 'late') {\r\n          subjectRecord.lastAttended = recordDate;\r\n        }\r\n      }\r\n    });\r\n\r\n    // Calculate attendance percentages\r\n    subjectMap.forEach(record => {\r\n      if (record.totalClasses > 0) {\r\n        record.attendancePercentage = Math.round((record.presentClasses / record.totalClasses) * 100);\r\n      }\r\n    });\r\n\r\n    this.attendanceRecords = Array.from(subjectMap.values());\r\n    this.applyFilters();\r\n  }\r\n\r\n  calculateSummary(): void {\r\n    this.totalSubjects = this.attendanceRecords.length;\r\n\r\n    if (this.totalSubjects > 0) {\r\n      const totalPercentage = this.attendanceRecords.reduce((sum, record) => sum + record.attendancePercentage, 0);\r\n      this.overallAttendance = Math.round(totalPercentage / this.totalSubjects);\r\n\r\n      this.lowAttendanceSubjects = this.attendanceRecords.filter(record => record.attendancePercentage < 75).length;\r\n    }\r\n  }\r\n\r\n  applyFilters(): void {\r\n    let filtered = [...this.attendanceRecords];\r\n\r\n    if (this.searchQuery) {\r\n      const query = this.searchQuery.toLowerCase();\r\n      filtered = filtered.filter(record =>\r\n        record.subject.subjectName.toLowerCase().includes(query) ||\r\n        record.subject.code.toLowerCase().includes(query)\r\n      );\r\n    }\r\n\r\n    if (this.selectedSubject) {\r\n      filtered = filtered.filter(record => record.subject._id === this.selectedSubject);\r\n    }\r\n\r\n    if (this.minAttendance > 0) {\r\n      filtered = filtered.filter(record => record.attendancePercentage >= this.minAttendance);\r\n    }\r\n\r\n    this.filteredRecords = filtered;\r\n  }\r\n\r\n  onSearch(): void {\r\n    this.applyFilters();\r\n  }\r\n\r\n  onFilterChange(): void {\r\n    this.applyFilters();\r\n  }\r\n\r\n  clearFilters(): void {\r\n    this.searchQuery = '';\r\n    this.selectedSubject = '';\r\n    this.minAttendance = 0;\r\n    this.applyFilters();\r\n  }\r\n\r\n  refreshData(): void {\r\n    this.loadStudentAttendance();\r\n  }\r\n\r\n  // Pagination\r\n  get paginatedRecords(): AttendanceRecord[] {\r\n    const startIndex = (this.currentPage - 1) * this.itemsPerPage;\r\n    return this.filteredRecords.slice(startIndex, startIndex + this.itemsPerPage);\r\n  }\r\n\r\n  get totalPages(): number {\r\n    return Math.ceil(this.filteredRecords.length / this.itemsPerPage);\r\n  }\r\n\r\n  nextPage(): void {\r\n    if (this.currentPage < this.totalPages) {\r\n      this.currentPage++;\r\n    }\r\n  }\r\n\r\n  previousPage(): void {\r\n    if (this.currentPage > 1) {\r\n      this.currentPage--;\r\n    }\r\n  }\r\n\r\n  goToPage(page: number): void {\r\n    if (page >= 1 && page <= this.totalPages) {\r\n      this.currentPage = page;\r\n    }\r\n  }\r\n\r\n  // Utility methods\r\n  getAttendanceClass(percentage: number): string {\r\n    if (percentage >= 90) return 'excellent';\r\n    if (percentage >= 80) return 'good';\r\n    if (percentage >= 75) return 'average';\r\n    return 'poor';\r\n  }\r\n\r\n  formatDate(date: Date | string): string {\r\n    return new Date(date).toLocaleDateString();\r\n  }\r\n\r\n  getUniqueSubjects(): any[] {\r\n    return this.attendanceRecords.map(record => record.subject);\r\n  }\r\n}\r\n", "<div class=\"student-attendance-container\">\r\n  <!-- Header -->\r\n  <div class=\"page-header\">\r\n    <div class=\"header-content\">\r\n      <h1 class=\"page-title\">\r\n        <mat-icon class=\"title-icon\">fact_check</mat-icon>\r\n        My Attendance\r\n      </h1>\r\n      <p class=\"page-subtitle\">Track your attendance across all subjects</p>\r\n    </div>\r\n    <div class=\"header-actions\">\r\n      <button mat-icon-button (click)=\"refreshData()\" matTooltip=\"Refresh\" class=\"refresh-btn\">\r\n        <mat-icon>refresh</mat-icon>\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Loading State -->\r\n  <div *ngIf=\"loading\" class=\"loading-state\">\r\n    <mat-spinner diameter=\"40\"></mat-spinner>\r\n    <p>Loading attendance data...</p>\r\n  </div>\r\n\r\n  <!-- Error State -->\r\n  <div *ngIf=\"error && !loading\" class=\"error-state\">\r\n    <mat-icon class=\"error-icon\">error</mat-icon>\r\n    <h4>Error Loading Attendance</h4>\r\n    <p>{{ error }}</p>\r\n    <button mat-raised-button color=\"primary\" (click)=\"refreshData()\">\r\n      <mat-icon>refresh</mat-icon>\r\n      Try Again\r\n    </button>\r\n  </div>\r\n\r\n  <!-- Main Content -->\r\n  <div *ngIf=\"!loading && !error\">\r\n    <!-- Summary Cards -->\r\n    <div class=\"summary-cards\">\r\n      <mat-card class=\"summary-card overall\">\r\n        <mat-card-content>\r\n          <div class=\"summary-content\">\r\n            <div class=\"summary-icon\">\r\n              <mat-icon>assessment</mat-icon>\r\n            </div>\r\n            <div class=\"summary-info\">\r\n              <h3>{{ overallAttendance }}%</h3>\r\n              <p>Overall Attendance</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n\r\n      <mat-card class=\"summary-card subjects\">\r\n        <mat-card-content>\r\n          <div class=\"summary-content\">\r\n            <div class=\"summary-icon\">\r\n              <mat-icon>subject</mat-icon>\r\n            </div>\r\n            <div class=\"summary-info\">\r\n              <h3>{{ totalSubjects }}</h3>\r\n              <p>Total Subjects</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n\r\n      <mat-card class=\"summary-card warning\" *ngIf=\"lowAttendanceSubjects > 0\">\r\n        <mat-card-content>\r\n          <div class=\"summary-content\">\r\n            <div class=\"summary-icon\">\r\n              <mat-icon>warning</mat-icon>\r\n            </div>\r\n            <div class=\"summary-info\">\r\n              <h3>{{ lowAttendanceSubjects }}</h3>\r\n              <p>Low Attendance</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n    </div>\r\n\r\n    <!-- Filters -->\r\n    <mat-card class=\"filters-card\">\r\n      <mat-card-content>\r\n        <div class=\"filters-row\">\r\n          <div class=\"search-field\">\r\n            <mat-form-field appearance=\"outline\" class=\"compact-field\">\r\n              <mat-label>Search Subjects</mat-label>\r\n              <input matInput [(ngModel)]=\"searchQuery\" (input)=\"onSearch()\"\r\n                     placeholder=\"Search by subject name or code...\">\r\n              <mat-icon matSuffix>search</mat-icon>\r\n            </mat-form-field>\r\n          </div>\r\n\r\n          <div class=\"filter-field\">\r\n            <mat-form-field appearance=\"outline\" class=\"compact-field\">\r\n              <mat-label>Minimum Attendance</mat-label>\r\n              <mat-select [(ngModel)]=\"minAttendance\" (selectionChange)=\"onFilterChange()\">\r\n                <mat-option value=\"0\">All</mat-option>\r\n                <mat-option value=\"75\">75% and above</mat-option>\r\n                <mat-option value=\"80\">80% and above</mat-option>\r\n                <mat-option value=\"90\">90% and above</mat-option>\r\n              </mat-select>\r\n            </mat-form-field>\r\n          </div>\r\n\r\n          <div class=\"filter-actions\">\r\n            <button mat-icon-button (click)=\"clearFilters()\" matTooltip=\"Clear Filters\" class=\"clear-btn\">\r\n              <mat-icon>clear</mat-icon>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n\r\n    <!-- Attendance Records -->\r\n    <div class=\"attendance-grid\" *ngIf=\"paginatedRecords.length > 0\">\r\n      <mat-card *ngFor=\"let record of paginatedRecords\" class=\"attendance-card\"\r\n                [ngClass]=\"getAttendanceClass(record.attendancePercentage)\">\r\n        <mat-card-header>\r\n          <div class=\"subject-header\">\r\n            <div class=\"subject-icon\">\r\n              <mat-icon>subject</mat-icon>\r\n            </div>\r\n            <div class=\"subject-info\">\r\n              <h3>{{ record.subject.subjectName }}</h3>\r\n              <span class=\"subject-code\">{{ record.subject.code }}</span>\r\n            </div>\r\n            <div class=\"attendance-percentage\" [ngClass]=\"getAttendanceClass(record.attendancePercentage)\">\r\n              {{ record.attendancePercentage }}%\r\n            </div>\r\n          </div>\r\n        </mat-card-header>\r\n\r\n        <mat-card-content>\r\n          <div class=\"class-info\">\r\n            <span class=\"class-name\">{{ record.class.className }} - {{ record.class.section }}</span>\r\n          </div>\r\n\r\n          <div class=\"attendance-stats\">\r\n            <div class=\"stat-row\">\r\n              <div class=\"stat-item present\">\r\n                <mat-icon>check_circle</mat-icon>\r\n                <span>{{ record.presentClasses }} Present</span>\r\n              </div>\r\n              <div class=\"stat-item absent\">\r\n                <mat-icon>cancel</mat-icon>\r\n                <span>{{ record.absentClasses }} Absent</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"stat-row\">\r\n              <div class=\"stat-item late\" *ngIf=\"record.lateClasses > 0\">\r\n                <mat-icon>schedule</mat-icon>\r\n                <span>{{ record.lateClasses }} Late</span>\r\n              </div>\r\n              <div class=\"stat-item total\">\r\n                <mat-icon>event</mat-icon>\r\n                <span>{{ record.totalClasses }} Total</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"last-attended\" *ngIf=\"record.lastAttended\">\r\n            <mat-icon>access_time</mat-icon>\r\n            <span>Last attended: {{ formatDate(record.lastAttended) }}</span>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n    </div>\r\n    <!-- Empty State -->\r\n    <div *ngIf=\"!loading && paginatedRecords.length === 0\" class=\"empty-state\">\r\n      <mat-icon class=\"empty-icon\">fact_check</mat-icon>\r\n      <h4>No Attendance Records Found</h4>\r\n      <p *ngIf=\"searchQuery || minAttendance > 0\">No records match your current filters.</p>\r\n      <p *ngIf=\"!searchQuery && minAttendance === 0\">No attendance records available yet.</p>\r\n      <button mat-button (click)=\"clearFilters()\" *ngIf=\"searchQuery || minAttendance > 0\">\r\n        Clear Filters\r\n      </button>\r\n    </div>\r\n\r\n    <!-- Pagination -->\r\n    <div class=\"pagination-container\" *ngIf=\"totalPages > 1\">\r\n      <mat-paginator\r\n        [length]=\"filteredRecords.length\"\r\n        [pageSize]=\"itemsPerPage\"\r\n        [pageSizeOptions]=\"[5, 10, 20]\"\r\n        [pageIndex]=\"currentPage - 1\"\r\n        (page)=\"goToPage($event.pageIndex + 1)\"\r\n        showFirstLastButtons>\r\n      </mat-paginator>\r\n    </div>\r\n  </div>\r\n</div>"], "mappings": ";;;;;;;;;;;;;;;;;;;ICkBEA,EAAA,CAAAC,cAAA,cAA2C;IACzCD,EAAA,CAAAE,SAAA,sBAAyC;IACzCF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,iCAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;;IAInCJ,EAAA,CAAAC,cAAA,cAAmD;IACpBD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC7CJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,+BAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACjCJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAW;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAClBJ,EAAA,CAAAC,cAAA,iBAAkE;IAAxBD,EAAA,CAAAK,UAAA,mBAAAC,4DAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAC/DZ,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC5BJ,EAAA,CAAAG,MAAA,mBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAJNJ,EAAA,CAAAa,SAAA,GAAW;IAAXb,EAAA,CAAAc,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IAuCZhB,EAAA,CAAAC,cAAA,mBAAyE;IAIvDD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAE9BJ,EAAA,CAAAC,cAAA,cAA0B;IACpBD,EAAA,CAAAG,MAAA,GAA2B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACpCJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,sBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IADjBJ,EAAA,CAAAa,SAAA,GAA2B;IAA3Bb,EAAA,CAAAc,iBAAA,CAAAG,MAAA,CAAAC,qBAAA,CAA2B;;;;;IA8E/BlB,EAAA,CAAAC,cAAA,cAA2D;IAC/CD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC7BJ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,GAA6B;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAApCJ,EAAA,CAAAa,SAAA,GAA6B;IAA7Bb,EAAA,CAAAmB,kBAAA,KAAAC,UAAA,CAAAC,WAAA,UAA6B;;;;;IASzCrB,EAAA,CAAAC,cAAA,cAAuD;IAC3CD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAChCJ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,GAAoD;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAA3DJ,EAAA,CAAAa,SAAA,GAAoD;IAApDb,EAAA,CAAAmB,kBAAA,oBAAAG,OAAA,CAAAC,UAAA,CAAAH,UAAA,CAAAI,YAAA,MAAoD;;;;;IA/ChExB,EAAA,CAAAC,cAAA,mBACsE;IAIpDD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAE9BJ,EAAA,CAAAC,cAAA,cAA0B;IACpBD,EAAA,CAAAG,MAAA,GAAgC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACzCJ,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAG,MAAA,IAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAE7DJ,EAAA,CAAAC,cAAA,eAA+F;IAC7FD,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAIVJ,EAAA,CAAAC,cAAA,wBAAkB;IAEWD,EAAA,CAAAG,MAAA,IAAyD;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAG3FJ,EAAA,CAAAC,cAAA,eAA8B;IAGdD,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACjCJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,IAAmC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAElDJ,EAAA,CAAAC,cAAA,eAA8B;IAClBD,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC3BJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,IAAiC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAGlDJ,EAAA,CAAAC,cAAA,eAAsB;IACpBD,EAAA,CAAAyB,UAAA,KAAAC,4DAAA,kBAGM;IACN1B,EAAA,CAAAC,cAAA,eAA6B;IACjBD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC1BJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,IAA+B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAKlDJ,EAAA,CAAAyB,UAAA,KAAAE,4DAAA,kBAGM;IACR3B,EAAA,CAAAI,YAAA,EAAmB;;;;;IAhDXJ,EAAA,CAAA4B,UAAA,YAAAC,MAAA,CAAAC,kBAAA,CAAAV,UAAA,CAAAW,oBAAA,EAA2D;IAOzD/B,EAAA,CAAAa,SAAA,GAAgC;IAAhCb,EAAA,CAAAc,iBAAA,CAAAM,UAAA,CAAAY,OAAA,CAAAC,WAAA,CAAgC;IACTjC,EAAA,CAAAa,SAAA,GAAyB;IAAzBb,EAAA,CAAAc,iBAAA,CAAAM,UAAA,CAAAY,OAAA,CAAAE,IAAA,CAAyB;IAEnBlC,EAAA,CAAAa,SAAA,GAA2D;IAA3Db,EAAA,CAAA4B,UAAA,YAAAC,MAAA,CAAAC,kBAAA,CAAAV,UAAA,CAAAW,oBAAA,EAA2D;IAC5F/B,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAmB,kBAAA,MAAAC,UAAA,CAAAW,oBAAA,OACF;IAMyB/B,EAAA,CAAAa,SAAA,GAAyD;IAAzDb,EAAA,CAAAmC,kBAAA,KAAAf,UAAA,CAAAgB,KAAA,CAAAC,SAAA,SAAAjB,UAAA,CAAAgB,KAAA,CAAAE,OAAA,KAAyD;IAOxEtC,EAAA,CAAAa,SAAA,GAAmC;IAAnCb,EAAA,CAAAmB,kBAAA,KAAAC,UAAA,CAAAmB,cAAA,aAAmC;IAInCvC,EAAA,CAAAa,SAAA,GAAiC;IAAjCb,EAAA,CAAAmB,kBAAA,KAAAC,UAAA,CAAAoB,aAAA,YAAiC;IAIZxC,EAAA,CAAAa,SAAA,GAA4B;IAA5Bb,EAAA,CAAA4B,UAAA,SAAAR,UAAA,CAAAC,WAAA,KAA4B;IAMjDrB,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAAmB,kBAAA,KAAAC,UAAA,CAAAqB,YAAA,WAA+B;IAKfzC,EAAA,CAAAa,SAAA,GAAyB;IAAzBb,EAAA,CAAA4B,UAAA,SAAAR,UAAA,CAAAI,YAAA,CAAyB;;;;;IA9C3DxB,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAyB,UAAA,IAAAiB,qDAAA,yBAkDW;IACb1C,EAAA,CAAAI,YAAA,EAAM;;;;IAnDyBJ,EAAA,CAAAa,SAAA,GAAmB;IAAnBb,EAAA,CAAA4B,UAAA,YAAAe,MAAA,CAAAC,gBAAA,CAAmB;;;;;IAwDhD5C,EAAA,CAAAC,cAAA,QAA4C;IAAAD,EAAA,CAAAG,MAAA,6CAAsC;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IACtFJ,EAAA,CAAAC,cAAA,QAA+C;IAAAD,EAAA,CAAAG,MAAA,2CAAoC;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;;IACvFJ,EAAA,CAAAC,cAAA,iBAAqF;IAAlED,EAAA,CAAAK,UAAA,mBAAAwC,4EAAA;MAAA7C,EAAA,CAAAO,aAAA,CAAAuC,IAAA;MAAA,MAAAC,OAAA,GAAA/C,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAoC,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IACzChD,EAAA,CAAAG,MAAA,sBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;IAPXJ,EAAA,CAAAC,cAAA,cAA2E;IAC5CD,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAClDJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,kCAA2B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACpCJ,EAAA,CAAAyB,UAAA,IAAAwB,8CAAA,gBAAsF;IACtFjD,EAAA,CAAAyB,UAAA,IAAAyB,8CAAA,gBAAuF;IACvFlD,EAAA,CAAAyB,UAAA,IAAA0B,mDAAA,qBAES;IACXnD,EAAA,CAAAI,YAAA,EAAM;;;;IALAJ,EAAA,CAAAa,SAAA,GAAsC;IAAtCb,EAAA,CAAA4B,UAAA,SAAAwB,MAAA,CAAAC,WAAA,IAAAD,MAAA,CAAAE,aAAA,KAAsC;IACtCtD,EAAA,CAAAa,SAAA,GAAyC;IAAzCb,EAAA,CAAA4B,UAAA,UAAAwB,MAAA,CAAAC,WAAA,IAAAD,MAAA,CAAAE,aAAA,OAAyC;IACAtD,EAAA,CAAAa,SAAA,GAAsC;IAAtCb,EAAA,CAAA4B,UAAA,SAAAwB,MAAA,CAAAC,WAAA,IAAAD,MAAA,CAAAE,aAAA,KAAsC;;;;;;;;;IAMrFtD,EAAA,CAAAC,cAAA,cAAyD;IAMrDD,EAAA,CAAAK,UAAA,kBAAAkD,yEAAAC,MAAA;MAAAxD,EAAA,CAAAO,aAAA,CAAAkD,IAAA;MAAA,MAAAC,OAAA,GAAA1D,EAAA,CAAAU,aAAA;MAAA,OAAQV,EAAA,CAAAW,WAAA,CAAA+C,OAAA,CAAAC,QAAA,CAAAH,MAAA,CAAAI,SAAA,GAA4B,CAAC,CAAC;IAAA,EAAC;IAEzC5D,EAAA,CAAAI,YAAA,EAAgB;;;;IANdJ,EAAA,CAAAa,SAAA,GAAiC;IAAjCb,EAAA,CAAA4B,UAAA,WAAAiC,MAAA,CAAAC,eAAA,CAAAC,MAAA,CAAiC,aAAAF,MAAA,CAAAG,YAAA,qBAAAhE,EAAA,CAAAiE,eAAA,IAAAC,GAAA,gBAAAL,MAAA,CAAAM,WAAA;;;;;;IApJvCnE,EAAA,CAAAC,cAAA,UAAgC;IAOVD,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAEjCJ,EAAA,CAAAC,cAAA,cAA0B;IACpBD,EAAA,CAAAG,MAAA,IAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACjCJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,0BAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAMjCJ,EAAA,CAAAC,cAAA,oBAAwC;IAItBD,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAE9BJ,EAAA,CAAAC,cAAA,eAA0B;IACpBD,EAAA,CAAAG,MAAA,IAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC5BJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,sBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAM7BJ,EAAA,CAAAyB,UAAA,KAAA2C,+CAAA,wBAYW;IACbpE,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAC,cAAA,oBAA+B;IAKVD,EAAA,CAAAG,MAAA,uBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAY;IACtCJ,EAAA,CAAAC,cAAA,iBACuD;IADvCD,EAAA,CAAAK,UAAA,2BAAAgE,oEAAAb,MAAA;MAAAxD,EAAA,CAAAO,aAAA,CAAA+D,IAAA;MAAA,MAAAC,OAAA,GAAAvE,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAA4D,OAAA,CAAAlB,WAAA,GAAAG,MAAA;IAAA,EAAyB,mBAAAgB,4DAAA;MAAAxE,EAAA,CAAAO,aAAA,CAAA+D,IAAA;MAAA,MAAAG,OAAA,GAAAzE,EAAA,CAAAU,aAAA;MAAA,OAAUV,EAAA,CAAAW,WAAA,CAAA8D,OAAA,CAAAC,QAAA,EAAU;IAAA,EAApB;IAAzC1E,EAAA,CAAAI,YAAA,EACuD;IACvDJ,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAIzCJ,EAAA,CAAAC,cAAA,eAA0B;IAEXD,EAAA,CAAAG,MAAA,0BAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAY;IACzCJ,EAAA,CAAAC,cAAA,sBAA6E;IAAjED,EAAA,CAAAK,UAAA,2BAAAsE,yEAAAnB,MAAA;MAAAxD,EAAA,CAAAO,aAAA,CAAA+D,IAAA;MAAA,MAAAM,OAAA,GAAA5E,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAiE,OAAA,CAAAtB,aAAA,GAAAE,MAAA;IAAA,EAA2B,6BAAAqB,2EAAA;MAAA7E,EAAA,CAAAO,aAAA,CAAA+D,IAAA;MAAA,MAAAQ,OAAA,GAAA9E,EAAA,CAAAU,aAAA;MAAA,OAAoBV,EAAA,CAAAW,WAAA,CAAAmE,OAAA,CAAAC,cAAA,EAAgB;IAAA,EAApC;IACrC/E,EAAA,CAAAC,cAAA,sBAAsB;IAAAD,EAAA,CAAAG,MAAA,WAAG;IAAAH,EAAA,CAAAI,YAAA,EAAa;IACtCJ,EAAA,CAAAC,cAAA,sBAAuB;IAAAD,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAa;IACjDJ,EAAA,CAAAC,cAAA,sBAAuB;IAAAD,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAa;IACjDJ,EAAA,CAAAC,cAAA,sBAAuB;IAAAD,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAa;IAKvDJ,EAAA,CAAAC,cAAA,eAA4B;IACFD,EAAA,CAAAK,UAAA,mBAAA2E,6DAAA;MAAAhF,EAAA,CAAAO,aAAA,CAAA+D,IAAA;MAAA,MAAAW,OAAA,GAAAjF,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAsE,OAAA,CAAAjC,YAAA,EAAc;IAAA,EAAC;IAC9ChD,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAQpCJ,EAAA,CAAAyB,UAAA,KAAAyD,0CAAA,kBAoDM;IAENlF,EAAA,CAAAyB,UAAA,KAAA0D,0CAAA,kBAQM;IAGNnF,EAAA,CAAAyB,UAAA,KAAA2D,0CAAA,kBASM;IACRpF,EAAA,CAAAI,YAAA,EAAM;;;;IAlJUJ,EAAA,CAAAa,SAAA,IAAwB;IAAxBb,EAAA,CAAAmB,kBAAA,KAAAkE,MAAA,CAAAC,iBAAA,MAAwB;IAcxBtF,EAAA,CAAAa,SAAA,IAAmB;IAAnBb,EAAA,CAAAc,iBAAA,CAAAuE,MAAA,CAAAE,aAAA,CAAmB;IAOSvF,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAA4B,UAAA,SAAAyD,MAAA,CAAAnE,qBAAA,KAA+B;IAsB/ClB,EAAA,CAAAa,SAAA,GAAyB;IAAzBb,EAAA,CAAA4B,UAAA,YAAAyD,MAAA,CAAAhC,WAAA,CAAyB;IAS7BrD,EAAA,CAAAa,SAAA,GAA2B;IAA3Bb,EAAA,CAAA4B,UAAA,YAAAyD,MAAA,CAAA/B,aAAA,CAA2B;IAmBnBtD,EAAA,CAAAa,SAAA,IAAiC;IAAjCb,EAAA,CAAA4B,UAAA,SAAAyD,MAAA,CAAAzC,gBAAA,CAAAmB,MAAA,KAAiC;IAsDzD/D,EAAA,CAAAa,SAAA,GAA+C;IAA/Cb,EAAA,CAAA4B,UAAA,UAAAyD,MAAA,CAAAG,OAAA,IAAAH,MAAA,CAAAzC,gBAAA,CAAAmB,MAAA,OAA+C;IAWlB/D,EAAA,CAAAa,SAAA,GAAoB;IAApBb,EAAA,CAAA4B,UAAA,SAAAyD,MAAA,CAAAI,UAAA,KAAoB;;;ADtJ3D,OAAM,MAAOC,mBAAmB;EAwB9BC,YACUC,MAAc,EACdC,iBAAoC,EACpCC,WAAwB,EACxBC,QAAqB;IAHrB,KAAAH,MAAM,GAANA,MAAM;IACN,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IA3BlB;IACA,KAAAP,OAAO,GAAG,IAAI;IACd,KAAAxE,KAAK,GAAkB,IAAI;IAI3B,KAAAgF,iBAAiB,GAAuB,EAAE;IAC1C,KAAAlC,eAAe,GAAuB,EAAE;IAExC;IACA,KAAAT,WAAW,GAAG,EAAE;IAChB,KAAA4C,eAAe,GAAG,EAAE;IACpB,KAAA3C,aAAa,GAAG,CAAC;IAEjB;IACA,KAAAa,WAAW,GAAG,CAAC;IACf,KAAAH,YAAY,GAAG,EAAE;IAEjB;IACA,KAAAsB,iBAAiB,GAAG,CAAC;IACrB,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAArE,qBAAqB,GAAG,CAAC;EAOtB;EAEHgF,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,GAAG,IAAI,CAACL,WAAW,CAACM,uBAAuB,EAAE,EAAEC,IAAI;IACnE,IAAI,IAAI,CAACF,WAAW,EAAE;MACpB,IAAI,CAACG,qBAAqB,EAAE;KAC7B,MAAM;MACL,IAAI,CAACtF,KAAK,GAAG,gBAAgB;MAC7B,IAAI,CAACwE,OAAO,GAAG,KAAK;;EAExB;EAEAc,qBAAqBA,CAAA;IACnB,IAAI,CAACd,OAAO,GAAG,IAAI;IACnB,IAAI,CAACxE,KAAK,GAAG,IAAI;IAEjB,IAAI,CAAC6E,iBAAiB,CAACU,oBAAoB,CAAC,IAAI,CAACJ,WAAW,CAACK,GAAG,CAAC,CAACC,SAAS,CAAC;MAC1EC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACC,qBAAqB,CAACF,QAAQ,CAACG,UAAU,CAAC;UAC/C,IAAI,CAACC,gBAAgB,EAAE;SACxB,MAAM;UACL,IAAI,CAAC/F,KAAK,GAAG,gCAAgC;;QAE/C,IAAI,CAACwE,OAAO,GAAG,KAAK;MACtB,CAAC;MACDxE,KAAK,EAAGA,KAAU,IAAI;QACpBgG,OAAO,CAAChG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,IAAI,CAACA,KAAK,GAAG,+BAA+B;QAC5C,IAAI,CAACwE,OAAO,GAAG,KAAK;QACpB,IAAI,CAACO,QAAQ,CAACkB,IAAI,CAAC,gCAAgC,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MACnF;KACD,CAAC;EACJ;EAEAL,qBAAqBA,CAACM,cAAqB;IACzC;IACA,MAAMC,UAAU,GAAG,IAAIC,GAAG,EAAe;IAEzCF,cAAc,CAACG,OAAO,CAACC,MAAM,IAAG;MAC9B,MAAMC,SAAS,GAAGD,MAAM,CAACvF,OAAO,CAACwE,GAAG;MAEpC,IAAI,CAACY,UAAU,CAACK,GAAG,CAACD,SAAS,CAAC,EAAE;QAC9BJ,UAAU,CAACM,GAAG,CAACF,SAAS,EAAE;UACxBhB,GAAG,EAAEgB,SAAS;UACdxF,OAAO,EAAEuF,MAAM,CAACvF,OAAO;UACvBI,KAAK,EAAEmF,MAAM,CAACnF,KAAK;UACnBK,YAAY,EAAE,CAAC;UACfF,cAAc,EAAE,CAAC;UACjBC,aAAa,EAAE,CAAC;UAChBnB,WAAW,EAAE,CAAC;UACdU,oBAAoB,EAAE,CAAC;UACvBP,YAAY,EAAE;SACf,CAAC;;MAGJ,MAAMmG,aAAa,GAAGP,UAAU,CAACQ,GAAG,CAACJ,SAAS,CAAE;MAChDG,aAAa,CAAClF,YAAY,EAAE;MAE5B,QAAQ8E,MAAM,CAACM,MAAM;QACnB,KAAK,SAAS;UACZF,aAAa,CAACpF,cAAc,EAAE;UAC9B;QACF,KAAK,QAAQ;UACXoF,aAAa,CAACnF,aAAa,EAAE;UAC7B;QACF,KAAK,MAAM;UACTmF,aAAa,CAACtG,WAAW,EAAE;UAC3BsG,aAAa,CAACpF,cAAc,EAAE,CAAC,CAAC;UAChC;;MAGJ;MACA,MAAMuF,UAAU,GAAG,IAAIC,IAAI,CAACR,MAAM,CAACS,IAAI,CAAC;MACxC,IAAI,CAACL,aAAa,CAACnG,YAAY,IAAIsG,UAAU,GAAGH,aAAa,CAACnG,YAAY,EAAE;QAC1E,IAAI+F,MAAM,CAACM,MAAM,KAAK,SAAS,IAAIN,MAAM,CAACM,MAAM,KAAK,MAAM,EAAE;UAC3DF,aAAa,CAACnG,YAAY,GAAGsG,UAAU;;;IAG7C,CAAC,CAAC;IAEF;IACAV,UAAU,CAACE,OAAO,CAACC,MAAM,IAAG;MAC1B,IAAIA,MAAM,CAAC9E,YAAY,GAAG,CAAC,EAAE;QAC3B8E,MAAM,CAACxF,oBAAoB,GAAGkG,IAAI,CAACC,KAAK,CAAEX,MAAM,CAAChF,cAAc,GAAGgF,MAAM,CAAC9E,YAAY,GAAI,GAAG,CAAC;;IAEjG,CAAC,CAAC;IAEF,IAAI,CAACuD,iBAAiB,GAAGmC,KAAK,CAACC,IAAI,CAAChB,UAAU,CAACiB,MAAM,EAAE,CAAC;IACxD,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAvB,gBAAgBA,CAAA;IACd,IAAI,CAACxB,aAAa,GAAG,IAAI,CAACS,iBAAiB,CAACjC,MAAM;IAElD,IAAI,IAAI,CAACwB,aAAa,GAAG,CAAC,EAAE;MAC1B,MAAMgD,eAAe,GAAG,IAAI,CAACvC,iBAAiB,CAACwC,MAAM,CAAC,CAACC,GAAG,EAAElB,MAAM,KAAKkB,GAAG,GAAGlB,MAAM,CAACxF,oBAAoB,EAAE,CAAC,CAAC;MAC5G,IAAI,CAACuD,iBAAiB,GAAG2C,IAAI,CAACC,KAAK,CAACK,eAAe,GAAG,IAAI,CAAChD,aAAa,CAAC;MAEzE,IAAI,CAACrE,qBAAqB,GAAG,IAAI,CAAC8E,iBAAiB,CAAC0C,MAAM,CAACnB,MAAM,IAAIA,MAAM,CAACxF,oBAAoB,GAAG,EAAE,CAAC,CAACgC,MAAM;;EAEjH;EAEAuE,YAAYA,CAAA;IACV,IAAIK,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC3C,iBAAiB,CAAC;IAE1C,IAAI,IAAI,CAAC3C,WAAW,EAAE;MACpB,MAAMuF,KAAK,GAAG,IAAI,CAACvF,WAAW,CAACwF,WAAW,EAAE;MAC5CF,QAAQ,GAAGA,QAAQ,CAACD,MAAM,CAACnB,MAAM,IAC/BA,MAAM,CAACvF,OAAO,CAACC,WAAW,CAAC4G,WAAW,EAAE,CAACC,QAAQ,CAACF,KAAK,CAAC,IACxDrB,MAAM,CAACvF,OAAO,CAACE,IAAI,CAAC2G,WAAW,EAAE,CAACC,QAAQ,CAACF,KAAK,CAAC,CAClD;;IAGH,IAAI,IAAI,CAAC3C,eAAe,EAAE;MACxB0C,QAAQ,GAAGA,QAAQ,CAACD,MAAM,CAACnB,MAAM,IAAIA,MAAM,CAACvF,OAAO,CAACwE,GAAG,KAAK,IAAI,CAACP,eAAe,CAAC;;IAGnF,IAAI,IAAI,CAAC3C,aAAa,GAAG,CAAC,EAAE;MAC1BqF,QAAQ,GAAGA,QAAQ,CAACD,MAAM,CAACnB,MAAM,IAAIA,MAAM,CAACxF,oBAAoB,IAAI,IAAI,CAACuB,aAAa,CAAC;;IAGzF,IAAI,CAACQ,eAAe,GAAG6E,QAAQ;EACjC;EAEAjE,QAAQA,CAAA;IACN,IAAI,CAAC4D,YAAY,EAAE;EACrB;EAEAvD,cAAcA,CAAA;IACZ,IAAI,CAACuD,YAAY,EAAE;EACrB;EAEAtF,YAAYA,CAAA;IACV,IAAI,CAACK,WAAW,GAAG,EAAE;IACrB,IAAI,CAAC4C,eAAe,GAAG,EAAE;IACzB,IAAI,CAAC3C,aAAa,GAAG,CAAC;IACtB,IAAI,CAACgF,YAAY,EAAE;EACrB;EAEA1H,WAAWA,CAAA;IACT,IAAI,CAAC0F,qBAAqB,EAAE;EAC9B;EAEA;EACA,IAAI1D,gBAAgBA,CAAA;IAClB,MAAMmG,UAAU,GAAG,CAAC,IAAI,CAAC5E,WAAW,GAAG,CAAC,IAAI,IAAI,CAACH,YAAY;IAC7D,OAAO,IAAI,CAACF,eAAe,CAACkF,KAAK,CAACD,UAAU,EAAEA,UAAU,GAAG,IAAI,CAAC/E,YAAY,CAAC;EAC/E;EAEA,IAAIyB,UAAUA,CAAA;IACZ,OAAOwC,IAAI,CAACgB,IAAI,CAAC,IAAI,CAACnF,eAAe,CAACC,MAAM,GAAG,IAAI,CAACC,YAAY,CAAC;EACnE;EAEAkF,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC/E,WAAW,GAAG,IAAI,CAACsB,UAAU,EAAE;MACtC,IAAI,CAACtB,WAAW,EAAE;;EAEtB;EAEAgF,YAAYA,CAAA;IACV,IAAI,IAAI,CAAChF,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;;EAEtB;EAEAR,QAAQA,CAACyF,IAAY;IACnB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAAC3D,UAAU,EAAE;MACxC,IAAI,CAACtB,WAAW,GAAGiF,IAAI;;EAE3B;EAEA;EACAtH,kBAAkBA,CAACuH,UAAkB;IACnC,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO,WAAW;IACxC,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO,MAAM;IACnC,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO,SAAS;IACtC,OAAO,MAAM;EACf;EAEA9H,UAAUA,CAACyG,IAAmB;IAC5B,OAAO,IAAID,IAAI,CAACC,IAAI,CAAC,CAACsB,kBAAkB,EAAE;EAC5C;EAEAC,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACvD,iBAAiB,CAACwD,GAAG,CAACjC,MAAM,IAAIA,MAAM,CAACvF,OAAO,CAAC;EAC7D;EAAC,QAAAyH,CAAA,G;qBAvNU/D,mBAAmB,EAAA1F,EAAA,CAAA0J,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAA5J,EAAA,CAAA0J,iBAAA,CAAAG,EAAA,CAAAC,iBAAA,GAAA9J,EAAA,CAAA0J,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAhK,EAAA,CAAA0J,iBAAA,CAAAO,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAnBzE,mBAAmB;IAAA0E,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC/BhC1K,EAAA,CAAAC,cAAA,aAA0C;QAKLD,EAAA,CAAAG,MAAA,iBAAU;QAAAH,EAAA,CAAAI,YAAA,EAAW;QAClDJ,EAAA,CAAAG,MAAA,sBACF;QAAAH,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAC,cAAA,WAAyB;QAAAD,EAAA,CAAAG,MAAA,gDAAyC;QAAAH,EAAA,CAAAI,YAAA,EAAI;QAExEJ,EAAA,CAAAC,cAAA,aAA4B;QACFD,EAAA,CAAAK,UAAA,mBAAAuK,sDAAA;UAAA,OAASD,GAAA,CAAA/J,WAAA,EAAa;QAAA,EAAC;QAC7CZ,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAG,MAAA,eAAO;QAAAH,EAAA,CAAAI,YAAA,EAAW;QAMlCJ,EAAA,CAAAyB,UAAA,KAAAoJ,mCAAA,iBAGM;QAGN7K,EAAA,CAAAyB,UAAA,KAAAqJ,mCAAA,kBAQM;QAGN9K,EAAA,CAAAyB,UAAA,KAAAsJ,mCAAA,mBA4JM;QACR/K,EAAA,CAAAI,YAAA,EAAM;;;QA9KEJ,EAAA,CAAAa,SAAA,IAAa;QAAbb,EAAA,CAAA4B,UAAA,SAAA+I,GAAA,CAAAnF,OAAA,CAAa;QAMbxF,EAAA,CAAAa,SAAA,GAAuB;QAAvBb,EAAA,CAAA4B,UAAA,SAAA+I,GAAA,CAAA3J,KAAA,KAAA2J,GAAA,CAAAnF,OAAA,CAAuB;QAWvBxF,EAAA,CAAAa,SAAA,GAAwB;QAAxBb,EAAA,CAAA4B,UAAA,UAAA+I,GAAA,CAAAnF,OAAA,KAAAmF,GAAA,CAAA3J,KAAA,CAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}