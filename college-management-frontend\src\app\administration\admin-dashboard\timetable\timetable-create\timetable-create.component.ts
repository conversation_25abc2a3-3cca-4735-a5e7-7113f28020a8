import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { TimetableService } from 'src/app/services/timetable.service';
import { ProgramService } from 'src/app/services/program.service';
import { DepartmentService } from 'src/app/services/department.service';
import { ClassesService } from 'src/app/services/classes.service';
import { SubjectService } from 'src/app/services/subject.service';
import { UserService } from 'src/app/services/user.service';
import { MatSnackBar } from '@angular/material/snack-bar';

@Component({
  selector: 'app-timetable-create',
  templateUrl: './timetable-create.component.html',
  styleUrls: ['./timetable-create.component.css']
})
export class TimetableCreateComponent implements OnInit {
  timetableForm: FormGroup;
  isEditMode = false;
  timetableId: string | null = null;
  loading = false;
  submitting = false;

  // Data arrays
  programs: any[] = [];
  departments: any[] = [];
  filteredDepartments: any[] = [];
  classes: any[] = [];
  filteredClasses: any[] = [];
  subjects: any[] = [];
  filteredSubjects: any[] = [];
  teachers: any[] = [];
  filteredTeachers: any[] = [];

  // Options
  daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  timeSlots = [
    '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
    '12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30',
    '16:00', '16:30', '17:00', '17:30', '18:00'
  ];
  durations = [30, 45, 60, 90, 120]; // in minutes

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private timetableService: TimetableService,
    private programService: ProgramService,
    private departmentService: DepartmentService,
    private classesService: ClassesService,
    private subjectService: SubjectService,
    private userService: UserService,
    private snackBar: MatSnackBar
  ) {
    this.timetableForm = this.createForm();
  }

  ngOnInit(): void {
    this.checkEditMode();
    this.loadInitialData();
    this.setupFormSubscriptions();
  }

  createForm(): FormGroup {
    return this.fb.group({
      program: ['', Validators.required],
      department: ['', Validators.required],
      class: ['', Validators.required],
      subject: ['', Validators.required],
      teacher: ['', Validators.required],
      dayOfWeek: ['', Validators.required],
      startTime: ['', Validators.required],
      duration: [60, [Validators.required, Validators.min(15)]],
      endTime: [{ value: '', disabled: true }],
      room: [''],
      semester: ['', [Validators.required, Validators.min(1)]],
      academicYear: ['', Validators.required],
      notes: ['']
    });
  }

  checkEditMode(): void {
    this.timetableId = this.route.snapshot.paramMap.get('id');
    if (this.timetableId) {
      this.isEditMode = true;
      this.loadTimetableData();
    }
  }

  loadTimetableData(): void {
    if (!this.timetableId) return;

    this.loading = true;
    this.timetableService.getTimetableEntryById(this.timetableId).subscribe({
      next: (response) => {
        if (response.success && response.timetable) {
          this.populateForm(response.timetable);
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading timetable:', error);
        this.showError('Failed to load timetable data');
        this.loading = false;
      }
    });
  }

  populateForm(timetable: any): void {
    this.timetableForm.patchValue({
      program: timetable.program?._id,
      department: timetable.department?._id,
      class: timetable.class?._id,
      subject: timetable.subject?._id,
      teacher: timetable.teacher?._id,
      dayOfWeek: timetable.dayOfWeek,
      startTime: timetable.timeSlot?.startTime,
      duration: timetable.timeSlot?.duration,
      room: timetable.room,
      semester: timetable.semester,
      academicYear: timetable.academicYear,
      notes: timetable.notes
    });

    // Trigger cascading updates
    if (timetable.program?._id) {
      this.onProgramChange();
    }
    if (timetable.department?._id) {
      this.onDepartmentChange();
    }
  }

  loadInitialData(): void {
    // Load programs
    this.programService.getAllPrograms(true).subscribe({
      next: (response) => {
        if (response.success) {
          this.programs = response.programs;
        }
      },
      error: (error) => {
        console.error('Error loading programs:', error);
      }
    });

    // Load all departments
    this.departmentService.getAllDepartments().subscribe({
      next: (response) => {
        if (response.success) {
          this.departments = response.departments;
        }
      },
      error: (error) => {
        console.error('Error loading departments:', error);
      }
    });

    // Load all classes
    this.classesService.getAllClasses().subscribe({
      next: (response) => {
        if (response.success) {
          this.classes = response.classes;
        }
      },
      error: (error) => {
        console.error('Error loading classes:', error);
      }
    });

    // Load all subjects
    this.subjectService.getAllSubjects().subscribe({
      next: (response) => {
        if (response.success) {
          this.subjects = response.subjects;
        }
      },
      error: (error) => {
        console.error('Error loading subjects:', error);
      }
    });

    // Load teachers
    this.userService.getUsersByRole('Teacher').subscribe({
      next: (response) => {
        if (response.success) {
          this.teachers = response.users;
        }
      },
      error: (error) => {
        console.error('Error loading teachers:', error);
      }
    });
  }

  setupFormSubscriptions(): void {
    // Auto-calculate end time when start time or duration changes
    this.timetableForm.get('startTime')?.valueChanges.subscribe(() => {
      this.calculateEndTime();
    });

    this.timetableForm.get('duration')?.valueChanges.subscribe(() => {
      this.calculateEndTime();
    });
  }

  calculateEndTime(): void {
    const startTime = this.timetableForm.get('startTime')?.value;
    const duration = this.timetableForm.get('duration')?.value;

    if (startTime && duration) {
      const [hours, minutes] = startTime.split(':').map(Number);
      const startDate = new Date();
      startDate.setHours(hours, minutes, 0, 0);
      
      const endDate = new Date(startDate.getTime() + duration * 60000);
      const endTime = endDate.toTimeString().slice(0, 5);
      
      this.timetableForm.get('endTime')?.setValue(endTime);
    }
  }

  // Cascading dropdown handlers
  onProgramChange(): void {
    const programId = this.timetableForm.get('program')?.value;
    if (programId) {
      // Filter departments by program
      this.filteredDepartments = this.departments.filter(dept => 
        dept.program === programId || dept.program?._id === programId
      );

      // Filter teachers by program
      this.filteredTeachers = this.teachers.filter(teacher => 
        teacher.program === programId || teacher.program?._id === programId
      );

      // Reset dependent fields
      this.timetableForm.patchValue({
        department: '',
        class: '',
        subject: ''
      });
      this.filteredClasses = [];
      this.filteredSubjects = [];
    } else {
      this.filteredDepartments = [];
      this.filteredClasses = [];
      this.filteredSubjects = [];
      this.filteredTeachers = [];
    }
  }

  onDepartmentChange(): void {
    const programId = this.timetableForm.get('program')?.value;
    const departmentId = this.timetableForm.get('department')?.value;

    if (programId && departmentId) {
      // Load classes by program and department
      this.classesService.getClassesByProgramAndDepartment(programId, departmentId, true).subscribe({
        next: (response) => {
          if (response.success) {
            this.filteredClasses = response.classes;
          }
        },
        error: (error) => {
          console.error('Error loading classes:', error);
        }
      });

      // Load subjects by department (which already filters by program)
      this.subjectService.getSubjectsByDepartment(departmentId, true).subscribe({
        next: (response) => {
          if (response.success) {
            // Further filter by program to ensure exact match
            this.filteredSubjects = response.subjects.filter((subject: any) =>
              subject.program === programId || subject.program?._id === programId
            );
          }
        },
        error: (error) => {
          console.error('Error loading subjects:', error);
        }
      });

      // Reset dependent fields
      this.timetableForm.patchValue({
        class: '',
        subject: ''
      });
    } else {
      this.filteredClasses = [];
      this.filteredSubjects = [];
    }
  }

  onSubmit(): void {
    if (this.timetableForm.invalid) {
      this.markFormGroupTouched();
      this.showError('Please fill in all required fields correctly');
      return;
    }

    this.submitting = true;
    const formData = this.timetableForm.value;

    // Prepare timetable data
    const timetableData = {
      program: formData.program,
      department: formData.department,
      class: formData.class,
      subject: formData.subject,
      teacher: formData.teacher,
      dayOfWeek: formData.dayOfWeek,
      timeSlot: {
        startTime: formData.startTime,
        endTime: this.timetableForm.get('endTime')?.value || ''
      },
      room: formData.room,
      semester: formData.semester,
      academicYear: formData.academicYear,
      notes: formData.notes
    };

    const operation = this.isEditMode
      ? this.timetableService.updateTimetableEntry(this.timetableId!, timetableData)
      : this.timetableService.createTimetableEntry(timetableData);

    operation.subscribe({
      next: (response) => {
        if (response.success) {
          const message = this.isEditMode ? 'Timetable updated successfully!' : 'Timetable created successfully!';
          this.showSuccess(message);
          this.router.navigate(['/dashboard/admin/timetable']);
        }
        this.submitting = false;
      },
      error: (error) => {
        console.error('Error saving timetable:', error);
        this.showError(error.error?.message || 'Failed to save timetable');
        this.submitting = false;
      }
    });
  }

  onCancel(): void {
    this.router.navigate(['/dashboard/admin/timetable']);
  }

  // Utility methods
  private markFormGroupTouched(): void {
    Object.keys(this.timetableForm.controls).forEach(key => {
      this.timetableForm.get(key)?.markAsTouched();
    });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  // Form getters for validation
  get program() { return this.timetableForm.get('program'); }
  get department() { return this.timetableForm.get('department'); }
  get classControl() { return this.timetableForm.get('class'); }
  get subject() { return this.timetableForm.get('subject'); }
  get teacher() { return this.timetableForm.get('teacher'); }
  get dayOfWeek() { return this.timetableForm.get('dayOfWeek'); }
  get startTime() { return this.timetableForm.get('startTime'); }
  get duration() { return this.timetableForm.get('duration'); }
  get semester() { return this.timetableForm.get('semester'); }
  get academicYear() { return this.timetableForm.get('academicYear'); }
}
