{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { DashboardOverviewComponent } from './dashboard-overview/dashboard-overview.component';\nimport { ProgramsComponent } from './programs/programs.component';\nimport { TimetableComponent } from './timetable/timetable.component';\nimport { NoticesComponent } from './notices/notices.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: DashboardOverviewComponent\n}, {\n  path: 'overview',\n  component: DashboardOverviewComponent\n}, {\n  path: 'programs',\n  component: ProgramsComponent\n}, {\n  path: 'timetable',\n  component: TimetableComponent\n}, {\n  path: 'notices',\n  component: NoticesComponent\n}, {\n  path: 'department',\n  loadChildren: () => import('./department/department.module').then(m => m.DepartmentModule)\n}, {\n  path: 'classes',\n  loadChildren: () => import('./classes/classes.module').then(m => m.ClassesModule)\n}, {\n  path: 'teacher',\n  loadChildren: () => import('./teacher/teacher.module').then(m => m.TeacherModule)\n}, {\n  path: 'students',\n  loadChildren: () => import('./students/students.module').then(m => m.StudentsModule)\n}, {\n  path: 'subjects',\n  loadChildren: () => import('./subject/subject.module').then(m => m.SubjectModule)\n}];\nexport class AdminDashboardRoutingModule {\n  static #_ = this.ɵfac = function AdminDashboardRoutingModule_Factory(t) {\n    return new (t || AdminDashboardRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AdminDashboardRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild(routes), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AdminDashboardRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "DashboardOverviewComponent", "ProgramsComponent", "TimetableComponent", "NoticesComponent", "routes", "path", "component", "loadChildren", "then", "m", "DepartmentModule", "ClassesModule", "TeacherModule", "StudentsModule", "SubjectModule", "AdminDashboardRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\admin-dashboard-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { DashboardOverviewComponent } from './dashboard-overview/dashboard-overview.component';\r\nimport { ProgramsComponent } from './programs/programs.component';\r\nimport { TimetableComponent } from './timetable/timetable.component';\r\nimport { NoticesComponent } from './notices/notices.component';\r\n\r\nconst routes: Routes = [\r\n  { path: '', component: DashboardOverviewComponent },\r\n  { path: 'overview', component: DashboardOverviewComponent },\r\n  { path: 'programs', component: ProgramsComponent },\r\n  { path: 'timetable', component: TimetableComponent },\r\n  { path: 'notices', component: NoticesComponent },\r\n  { path: 'department', loadChildren: () => import('./department/department.module').then(m => m.DepartmentModule) },\r\n  { path: 'classes', loadChildren: () => import('./classes/classes.module').then(m => m.ClassesModule) },\r\n  { path: 'teacher', loadChildren: () => import('./teacher/teacher.module').then(m => m.TeacherModule) },\r\n  { path: 'students', loadChildren: () => import('./students/students.module').then(m => m.StudentsModule) },\r\n  { path: 'subjects', loadChildren: () => import('./subject/subject.module').then(m => m.SubjectModule) },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class AdminDashboardRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,0BAA0B,QAAQ,mDAAmD;AAC9F,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,gBAAgB,QAAQ,6BAA6B;;;AAE9D,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEN;AAA0B,CAAE,EACnD;EAAEK,IAAI,EAAE,UAAU;EAAEC,SAAS,EAAEN;AAA0B,CAAE,EAC3D;EAAEK,IAAI,EAAE,UAAU;EAAEC,SAAS,EAAEL;AAAiB,CAAE,EAClD;EAAEI,IAAI,EAAE,WAAW;EAAEC,SAAS,EAAEJ;AAAkB,CAAE,EACpD;EAAEG,IAAI,EAAE,SAAS;EAAEC,SAAS,EAAEH;AAAgB,CAAE,EAChD;EAAEE,IAAI,EAAE,YAAY;EAAEE,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,gBAAgB;AAAC,CAAE,EAClH;EAAEL,IAAI,EAAE,SAAS;EAAEE,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,aAAa;AAAC,CAAE,EACtG;EAAEN,IAAI,EAAE,SAAS;EAAEE,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACG,aAAa;AAAC,CAAE,EACtG;EAAEP,IAAI,EAAE,UAAU;EAAEE,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACI,cAAc;AAAC,CAAE,EAC1G;EAAER,IAAI,EAAE,UAAU;EAAEE,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,aAAa;AAAC,CAAE,CACxG;AAMD,OAAM,MAAOC,2BAA2B;EAAA,QAAAC,CAAA,G;qBAA3BD,2BAA2B;EAAA;EAAA,QAAAE,EAAA,G;UAA3BF;EAA2B;EAAA,QAAAG,EAAA,G;cAH5BnB,YAAY,CAACoB,QAAQ,CAACf,MAAM,CAAC,EAC7BL,YAAY;EAAA;;;2EAEXgB,2BAA2B;IAAAK,OAAA,GAAAC,EAAA,CAAAtB,YAAA;IAAAuB,OAAA,GAF5BvB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}