{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { LoginComponent } from './login/login.component';\nimport { SignupComponent } from './signup/signup.component';\nimport { EnterCodeManuallyComponent } from './enter-code-manually/enter-code-manually.component';\nimport { ForgotPasswordComponent } from './forgot-password/forgot-password.component';\nimport { VerifyOtpComponent } from './verify-otp/verify-otp.component';\nimport { ResetPasswordComponent } from './reset-password/reset-password.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: LoginComponent\n}, {\n  path: 'sign-up',\n  component: SignupComponent\n}, {\n  path: 'Enter-code',\n  component: EnterCodeManuallyComponent\n}, {\n  path: 'forgot-password',\n  component: ForgotPasswordComponent\n}, {\n  path: 'verify-otp',\n  component: VerifyOtpComponent\n}, {\n  path: 'reset-password',\n  component: ResetPasswordComponent\n}];\nexport class AuthRoutingModule {\n  static #_ = this.ɵfac = function AuthRoutingModule_Factory(t) {\n    return new (t || AuthRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AuthRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild(routes), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AuthRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "LoginComponent", "SignupComponent", "EnterCodeManuallyComponent", "ForgotPasswordComponent", "VerifyOtpComponent", "ResetPasswordComponent", "routes", "path", "component", "AuthRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\auth\\auth-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { LoginComponent } from './login/login.component';\r\nimport { SignupComponent } from './signup/signup.component';\r\nimport { EnterCodeManuallyComponent } from './enter-code-manually/enter-code-manually.component';\r\nimport { ForgotPasswordComponent } from './forgot-password/forgot-password.component';\r\nimport { VerifyOtpComponent } from './verify-otp/verify-otp.component';\r\nimport { ResetPasswordComponent } from './reset-password/reset-password.component';\r\n\r\nconst routes: Routes = [\r\n  {path:'' , component:LoginComponent},\r\n  {path:'sign-up' , component:SignupComponent},\r\n  {path:'Enter-code' , component:EnterCodeManuallyComponent},\r\n  {path:'forgot-password' , component:ForgotPasswordComponent},\r\n  {path:'verify-otp' , component:VerifyOtpComponent},\r\n  {path:'reset-password' , component:ResetPasswordComponent},\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class AuthRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,0BAA0B,QAAQ,qDAAqD;AAChG,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,sBAAsB,QAAQ,2CAA2C;;;AAElF,MAAMC,MAAM,GAAW,CACrB;EAACC,IAAI,EAAC,EAAE;EAAGC,SAAS,EAACR;AAAc,CAAC,EACpC;EAACO,IAAI,EAAC,SAAS;EAAGC,SAAS,EAACP;AAAe,CAAC,EAC5C;EAACM,IAAI,EAAC,YAAY;EAAGC,SAAS,EAACN;AAA0B,CAAC,EAC1D;EAACK,IAAI,EAAC,iBAAiB;EAAGC,SAAS,EAACL;AAAuB,CAAC,EAC5D;EAACI,IAAI,EAAC,YAAY;EAAGC,SAAS,EAACJ;AAAkB,CAAC,EAClD;EAACG,IAAI,EAAC,gBAAgB;EAAGC,SAAS,EAACH;AAAsB,CAAC,CAC3D;AAMD,OAAM,MAAOI,iBAAiB;EAAA,QAAAC,CAAA,G;qBAAjBD,iBAAiB;EAAA;EAAA,QAAAE,EAAA,G;UAAjBF;EAAiB;EAAA,QAAAG,EAAA,G;cAHlBb,YAAY,CAACc,QAAQ,CAACP,MAAM,CAAC,EAC7BP,YAAY;EAAA;;;2EAEXU,iBAAiB;IAAAK,OAAA,GAAAC,EAAA,CAAAhB,YAAA;IAAAiB,OAAA,GAFlBjB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}