{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { UsersRoutingModule } from './users-routing.module';\nimport { MaterialModule } from 'src/app/material';\nimport * as i0 from \"@angular/core\";\nexport let UsersModule = /*#__PURE__*/(() => {\n  class UsersModule {\n    static #_ = this.ɵfac = function UsersModule_Factory(t) {\n      return new (t || UsersModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: UsersModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, UsersRoutingModule, ReactiveFormsModule, FormsModule, MaterialModule]\n    });\n  }\n  return UsersModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}