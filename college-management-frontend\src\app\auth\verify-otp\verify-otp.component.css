.container-fluid {
  height: 100vh;
  overflow: hidden;
}

.key {
  font-size: 3rem;
  color: #29578c;
  margin-bottom: 1rem;
}

h1 {
  color: #29578c;
  font-weight: 600;
  font-size: 2rem;
}

h2 {
  color: #29578c;
  font-weight: 500;
}

p {
  color: #6c757d;
  margin-bottom: 1rem;
}

.text-muted {
  font-size: 0.9rem;
}

/* OTP Input Styling */
.otp-container {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin: 2rem 0;
}

.otp-input {
  width: 50px;
  height: 50px;
  text-align: center;
  font-size: 1.5rem;
  font-weight: 600;
  border: 2px solid #ced4da;
  border-radius: 8px;
  background-color: #f8f9fa;
  transition: all 0.3s ease;
}

.otp-input:focus {
  outline: none;
  border-color: #29578c;
  background-color: white;
  box-shadow: 0 0 0 0.2rem rgba(41, 87, 140, 0.25);
}

.otp-input.is-invalid {
  border-color: #dc3545;
  background-color: #fff5f5;
}

.otp-input:valid {
  border-color: #28a745;
  background-color: #f0fff4;
}

/* <PERSON><PERSON> Styling */
.btn {
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.submit {
  background-color: #29578c;
  color: white;
  font-size: 1rem;
}

.submit:hover:not(:disabled) {
  background-color: #1e3f63;
  transform: translateY(-1px);
}

.submit:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.resend {
  background-color: transparent;
  color: #29578c;
  border: 2px solid #29578c;
  padding: 8px 16px;
  font-size: 0.9rem;
}

.resend:hover:not(:disabled) {
  background-color: #29578c;
  color: white;
}

.resend:disabled {
  background-color: transparent;
  color: #6c757d;
  border-color: #6c757d;
  cursor: not-allowed;
}

/* Spinner */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Links */
a {
  color: #29578c;
  text-decoration: none;
  font-weight: 500;
}

a:hover {
  color: #1e3f63;
  text-decoration: underline;
}

/* Background Image */
.image-container {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 60%;
  overflow: hidden;
}

.image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0.8;
}

/* Blockquote Styling */
.blockquote {
  border-left: 4px solid #29578c;
  padding-left: 1rem;
}

.blockquote h2 {
  margin-bottom: 1rem;
}

.blockquote-footer {
  color: #6c757d;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .otp-container {
    gap: 8px;
  }
  
  .otp-input {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }
  
  h1 {
    font-size: 1.5rem;
  }
  
  .key {
    font-size: 2.5rem;
  }
}

@media (max-width: 480px) {
  .otp-container {
    gap: 5px;
  }
  
  .otp-input {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }
  
  .container-fluid {
    padding: 10px;
  }
}

/* Animation for invalid input */
.otp-input.is-invalid {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Success state */
.otp-input:valid:not(:placeholder-shown) {
  animation: pulse 0.3s ease-in-out;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}
