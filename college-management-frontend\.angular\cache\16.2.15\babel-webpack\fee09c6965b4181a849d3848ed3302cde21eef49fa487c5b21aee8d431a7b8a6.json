{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/user.service\";\nimport * as i2 from \"../../../services/dashboard.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/progress-spinner\";\nimport * as i9 from \"@angular/material/tooltip\";\nfunction StudentSubjectsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading your subjects...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StudentSubjectsComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Error Loading Subjects\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function StudentSubjectsComponent_div_11_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.refreshData());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction StudentSubjectsComponent_div_12_div_24_mat_card_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 26)(1, \"mat-card-header\")(2, \"div\", 27)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"subject\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"mat-card-title\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"mat-card-content\")(10, \"div\", 28)(11, \"div\", 29)(12, \"div\", 30)(13, \"span\", 31);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 32)(17, \"h4\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"p\");\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"div\", 33)(22, \"div\", 34);\n    i0.ɵɵelement(23, \"div\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 36)(25, \"span\");\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\");\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"span\");\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"div\", 37)(32, \"div\", 38)(33, \"mat-icon\");\n    i0.ɵɵtext(34, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 39)(36, \"span\", 40);\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"span\", 41);\n    i0.ɵɵtext(39, \"Present\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(40, \"div\", 38)(41, \"mat-icon\");\n    i0.ɵɵtext(42, \"cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"div\", 39)(44, \"span\", 40);\n    i0.ɵɵtext(45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"span\", 41);\n    i0.ɵɵtext(47, \"Absent\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(48, \"div\", 38)(49, \"mat-icon\");\n    i0.ɵɵtext(50, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"div\", 39)(52, \"span\", 40);\n    i0.ɵɵtext(53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"span\", 41);\n    i0.ɵɵtext(55, \"Total Classes\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(56, \"mat-card-actions\")(57, \"button\", 42)(58, \"mat-icon\");\n    i0.ɵɵtext(59, \"fact_check\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(60, \" View Attendance \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const subject_r9 = ctx.$implicit;\n    const ctx_r8 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(subject_r9.subjectName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Subject Code: \", subject_r9._id, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(ctx_r8.getAttendanceStatus(subject_r9.percentage));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(15, 18, subject_r9.percentage, \"1.0-0\"), \"%\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r8.getAttendanceMessage(subject_r9.percentage));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", subject_r9.present, \" out of \", subject_r9.total, \" classes attended\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r8.getAttendanceStatus(subject_r9.percentage));\n    i0.ɵɵstyleProp(\"width\", subject_r9.percentage, \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Present: \", subject_r9.present, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Absent: \", subject_r9.absent, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Total: \", subject_r9.total, \"\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(subject_r9.present);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(subject_r9.absent);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(subject_r9.total);\n  }\n}\nfunction StudentSubjectsComponent_div_12_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtemplate(1, StudentSubjectsComponent_div_12_div_24_mat_card_1_Template, 61, 21, \"mat-card\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.studentDashboard.subjectAttendance);\n  }\n}\nfunction StudentSubjectsComponent_div_12_ng_template_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 43)(1, \"mat-card-content\")(2, \"div\", 44)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"subject\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h3\");\n    i0.ɵɵtext(6, \"No Subjects Found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8, \"You don't have any subjects assigned yet. Please contact your administrator.\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction StudentSubjectsComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11)(2, \"mat-card\", 12)(3, \"mat-card-content\")(4, \"div\", 13)(5, \"div\", 14)(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"subject\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 15)(9, \"h3\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"p\");\n    i0.ɵɵtext(12, \"Total Subjects\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(13, \"mat-card\", 16)(14, \"mat-card-content\")(15, \"div\", 13)(16, \"div\", 14)(17, \"mat-icon\");\n    i0.ɵɵtext(18, \"trending_up\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 15)(20, \"h3\");\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"p\");\n    i0.ɵɵtext(23, \"Overall Attendance\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵtemplate(24, StudentSubjectsComponent_div_12_div_24_Template, 2, 1, \"div\", 17);\n    i0.ɵɵtemplate(25, StudentSubjectsComponent_div_12_ng_template_25_Template, 9, 0, \"ng-template\", null, 18, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementStart(27, \"div\", 19)(28, \"h3\");\n    i0.ɵɵtext(29, \"Quick Actions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 20)(31, \"button\", 21)(32, \"mat-icon\");\n    i0.ɵɵtext(33, \"fact_check\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(34, \" View Full Attendance \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"button\", 22)(36, \"mat-icon\");\n    i0.ɵɵtext(37, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(38, \" View Teachers \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"button\", 23)(40, \"mat-icon\");\n    i0.ɵɵtext(41, \"class\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(42, \" View Classes \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const _r6 = i0.ɵɵreference(26);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate((ctx_r2.studentDashboard.subjectAttendance == null ? null : ctx_r2.studentDashboard.subjectAttendance.length) || 0);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.calculateOverallPercentage(), \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r2.studentDashboard.subjectAttendance == null ? null : ctx_r2.studentDashboard.subjectAttendance.length) > 0)(\"ngIfElse\", _r6);\n  }\n}\nexport class StudentSubjectsComponent {\n  constructor(userService, dashboardService) {\n    this.userService = userService;\n    this.dashboardService = dashboardService;\n    this.loading = false;\n    this.error = null;\n  }\n  ngOnInit() {\n    this.currentUser = this.userService.getUserFromLocalStorage()?.user;\n    this.loadStudentSubjects();\n  }\n  loadStudentSubjects() {\n    if (!this.currentUser) return;\n    this.loading = true;\n    this.error = null;\n    this.dashboardService.getStudentDashboard(this.currentUser._id).subscribe({\n      next: response => {\n        if (response.success) {\n          this.studentDashboard = response.dashboard;\n        } else {\n          this.error = 'Failed to load student subjects';\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading student subjects:', error);\n        this.error = 'Error loading subjects data';\n        this.loading = false;\n      }\n    });\n  }\n  refreshData() {\n    this.loadStudentSubjects();\n  }\n  getAttendanceStatus(percentage) {\n    if (percentage >= 75) return 'good';\n    if (percentage >= 60) return 'warning';\n    return 'danger';\n  }\n  getAttendanceMessage(percentage) {\n    if (percentage >= 75) return 'Good Attendance';\n    if (percentage >= 60) return 'Needs Improvement';\n    return 'Poor Attendance';\n  }\n  calculateOverallPercentage() {\n    if (!this.studentDashboard?.subjectAttendance?.length) return 0;\n    const totalPresent = this.studentDashboard.subjectAttendance.reduce((sum, subject) => sum + subject.present, 0);\n    const totalClasses = this.studentDashboard.subjectAttendance.reduce((sum, subject) => sum + subject.total, 0);\n    return totalClasses > 0 ? Math.round(totalPresent / totalClasses * 100) : 0;\n  }\n  static #_ = this.ɵfac = function StudentSubjectsComponent_Factory(t) {\n    return new (t || StudentSubjectsComponent)(i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i2.DashboardService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: StudentSubjectsComponent,\n    selectors: [[\"app-student-subjects\"]],\n    decls: 13,\n    vars: 3,\n    consts: [[1, \"student-subjects-container\"], [1, \"page-header\"], [1, \"header-content\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Refresh Data\", 3, \"click\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"subjects-content\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"error-container\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"subjects-content\"], [1, \"summary-stats\"], [1, \"stat-card\", \"total\"], [1, \"stat-content\"], [1, \"stat-icon\"], [1, \"stat-info\"], [1, \"stat-card\", \"average\"], [\"class\", \"subjects-grid\", 4, \"ngIf\", \"ngIfElse\"], [\"noSubjects\", \"\"], [1, \"quick-actions\"], [1, \"action-buttons\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/dashboard/student/attendance\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"routerLink\", \"/dashboard/student/teachers\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/dashboard/student/classes\"], [1, \"subjects-grid\"], [\"class\", \"subject-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"subject-card\"], [\"mat-card-avatar\", \"\", 1, \"subject-avatar\"], [1, \"subject-details\"], [1, \"attendance-summary\"], [1, \"attendance-circle\"], [1, \"percentage\"], [1, \"attendance-info\"], [1, \"progress-container\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"progress-labels\"], [1, \"subject-stats\"], [1, \"stat-item\"], [1, \"stat-details\"], [1, \"stat-value\"], [1, \"stat-label\"], [\"mat-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/dashboard/student/attendance\"], [1, \"no-data-card\"], [1, \"no-data-content\"]],\n    template: function StudentSubjectsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\");\n        i0.ɵɵtext(4, \"My Subjects\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"p\");\n        i0.ɵɵtext(6, \"View your enrolled subjects and attendance details\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(7, \"button\", 3);\n        i0.ɵɵlistener(\"click\", function StudentSubjectsComponent_Template_button_click_7_listener() {\n          return ctx.refreshData();\n        });\n        i0.ɵɵelementStart(8, \"mat-icon\");\n        i0.ɵɵtext(9, \"refresh\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(10, StudentSubjectsComponent_div_10_Template, 4, 0, \"div\", 4);\n        i0.ɵɵtemplate(11, StudentSubjectsComponent_div_11_Template, 11, 1, \"div\", 5);\n        i0.ɵɵtemplate(12, StudentSubjectsComponent_div_12_Template, 43, 4, \"div\", 6);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.studentDashboard && !ctx.loading && !ctx.error);\n      }\n    },\n    dependencies: [i3.NgForOf, i3.NgIf, i4.RouterLink, i5.MatButton, i5.MatIconButton, i6.MatCard, i6.MatCardActions, i6.MatCardAvatar, i6.MatCardContent, i6.MatCardHeader, i6.MatCardSubtitle, i6.MatCardTitle, i7.MatIcon, i8.MatProgressSpinner, i9.MatTooltip, i3.DecimalPipe],\n    styles: [\".student-subjects-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.page-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 30px;\\n  padding-bottom: 20px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.header-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #333;\\n  font-size: 2rem;\\n  font-weight: 500;\\n}\\n\\n.header-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 5px 0 0 0;\\n  color: #666;\\n  font-size: 1rem;\\n}\\n\\n.loading-container[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 60px 20px;\\n  text-align: center;\\n}\\n\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  color: #666;\\n}\\n\\n.error-container[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  width: 48px;\\n  height: 48px;\\n  color: #f44336;\\n  margin-bottom: 20px;\\n}\\n\\n.subjects-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 30px;\\n}\\n\\n.summary-stats[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 30px;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n}\\n\\n.stat-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n}\\n\\n.stat-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  width: 32px;\\n  height: 32px;\\n  opacity: 0.8;\\n}\\n\\n.stat-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 5px 0;\\n  font-size: 1.8rem;\\n  font-weight: 600;\\n}\\n\\n.stat-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  opacity: 0.9;\\n  font-size: 0.9rem;\\n}\\n\\n.subjects-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n  gap: 25px;\\n}\\n\\n.subject-card[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\\n}\\n\\n.subject-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);\\n}\\n\\n.subject-avatar[_ngcontent-%COMP%] {\\n  background: #2196f3;\\n  color: white;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.subject-avatar[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n}\\n\\n.subject-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n}\\n\\n.attendance-summary[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 20px;\\n  padding: 15px;\\n  background: #f8f9fa;\\n  border-radius: 8px;\\n}\\n\\n.attendance-circle[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: 600;\\n  font-size: 1rem;\\n  color: white;\\n  flex-shrink: 0;\\n}\\n\\n.attendance-circle.good[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4caf50, #2e7d32);\\n}\\n\\n.attendance-circle.warning[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff9800, #f57c00);\\n}\\n\\n.attendance-circle.danger[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f44336, #d32f2f);\\n}\\n\\n.attendance-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 5px 0;\\n  color: #333;\\n  font-size: 1.1rem;\\n  font-weight: 500;\\n}\\n\\n.attendance-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 0.9rem;\\n}\\n\\n.progress-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 10px;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 8px;\\n  background: #e0e0e0;\\n  border-radius: 4px;\\n  overflow: hidden;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  border-radius: 4px;\\n  transition: width 0.3s ease;\\n}\\n\\n.progress-fill.good[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #4caf50, #2e7d32);\\n}\\n\\n.progress-fill.warning[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #ff9800, #f57c00);\\n}\\n\\n.progress-fill.danger[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #f44336, #d32f2f);\\n}\\n\\n.progress-labels[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  font-size: 0.8rem;\\n  color: #666;\\n}\\n\\n.subject-stats[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(3, 1fr);\\n  gap: 15px;\\n}\\n\\n.stat-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  padding: 12px;\\n  background: #f8f9fa;\\n  border-radius: 6px;\\n}\\n\\n.stat-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  width: 20px;\\n  height: 20px;\\n  color: #2196f3;\\n}\\n\\n.stat-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.stat-value[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 1.1rem;\\n  color: #333;\\n}\\n\\n.stat-label[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #666;\\n}\\n\\n.no-data-card[_ngcontent-%COMP%] {\\n  margin: 40px 0;\\n}\\n\\n.no-data-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n\\n.no-data-content[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  width: 64px;\\n  height: 64px;\\n  color: #ccc;\\n  margin-bottom: 20px;\\n}\\n\\n.no-data-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 10px 0;\\n  color: #666;\\n  font-size: 1.3rem;\\n}\\n\\n.no-data-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #999;\\n  font-size: 1rem;\\n}\\n\\n.quick-actions[_ngcontent-%COMP%] {\\n  margin-top: 30px;\\n}\\n\\n.quick-actions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 20px 0;\\n  color: #333;\\n  font-size: 1.3rem;\\n  font-weight: 500;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n  flex-wrap: wrap;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 12px 24px;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  width: 20px;\\n  height: 20px;\\n}\\n\\n@media (max-width: 768px) {\\n  .student-subjects-container[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n  \\n  .page-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 15px;\\n  }\\n  \\n  .summary-stats[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  \\n  .subjects-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  \\n  .attendance-summary[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n  }\\n  \\n  .subject-stats[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  \\n  .action-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  \\n  .action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "StudentSubjectsComponent_div_11_Template_button_click_7_listener", "ɵɵrestoreView", "_r4", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "refreshData", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "error", "subject_r9", "subjectName", "ɵɵtextInterpolate1", "_id", "ɵɵclassMap", "ctx_r8", "getAttendanceStatus", "percentage", "ɵɵpipeBind2", "getAttendanceMessage", "ɵɵtextInterpolate2", "present", "total", "ɵɵstyleProp", "absent", "ɵɵtemplate", "StudentSubjectsComponent_div_12_div_24_mat_card_1_Template", "ɵɵproperty", "ctx_r5", "studentDashboard", "subjectAttendance", "StudentSubjectsComponent_div_12_div_24_Template", "StudentSubjectsComponent_div_12_ng_template_25_Template", "ɵɵtemplateRefExtractor", "ctx_r2", "length", "calculateOverallPercentage", "_r6", "StudentSubjectsComponent", "constructor", "userService", "dashboardService", "loading", "ngOnInit", "currentUser", "getUserFromLocalStorage", "user", "loadStudentSubjects", "getStudentDashboard", "subscribe", "next", "response", "success", "dashboard", "console", "totalPresent", "reduce", "sum", "subject", "totalClasses", "Math", "round", "_", "ɵɵdirectiveInject", "i1", "UserService", "i2", "DashboardService", "_2", "selectors", "decls", "vars", "consts", "template", "StudentSubjectsComponent_Template", "rf", "ctx", "StudentSubjectsComponent_Template_button_click_7_listener", "StudentSubjectsComponent_div_10_Template", "StudentSubjectsComponent_div_11_Template", "StudentSubjectsComponent_div_12_Template"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\student-dashboard\\student-subjects\\student-subjects.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\student-dashboard\\student-subjects\\student-subjects.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { UserService } from '../../../services/user.service';\r\nimport { DashboardService } from '../../../services/dashboard.service';\r\n\r\n@Component({\r\n  selector: 'app-student-subjects',\r\n  templateUrl: './student-subjects.component.html',\r\n  styleUrls: ['./student-subjects.component.css']\r\n})\r\nexport class StudentSubjectsComponent implements OnInit {\r\n  currentUser: any;\r\n  studentDashboard: any;\r\n  loading = false;\r\n  error: string | null = null;\r\n\r\n  constructor(\r\n    private userService: UserService,\r\n    private dashboardService: DashboardService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.currentUser = this.userService.getUserFromLocalStorage()?.user;\r\n    this.loadStudentSubjects();\r\n  }\r\n\r\n  loadStudentSubjects(): void {\r\n    if (!this.currentUser) return;\r\n\r\n    this.loading = true;\r\n    this.error = null;\r\n\r\n    this.dashboardService.getStudentDashboard(this.currentUser._id).subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.studentDashboard = response.dashboard;\r\n        } else {\r\n          this.error = 'Failed to load student subjects';\r\n        }\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading student subjects:', error);\r\n        this.error = 'Error loading subjects data';\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  refreshData(): void {\r\n    this.loadStudentSubjects();\r\n  }\r\n\r\n  getAttendanceStatus(percentage: number): string {\r\n    if (percentage >= 75) return 'good';\r\n    if (percentage >= 60) return 'warning';\r\n    return 'danger';\r\n  }\r\n\r\n  getAttendanceMessage(percentage: number): string {\r\n    if (percentage >= 75) return 'Good Attendance';\r\n    if (percentage >= 60) return 'Needs Improvement';\r\n    return 'Poor Attendance';\r\n  }\r\n\r\n  calculateOverallPercentage(): number {\r\n    if (!this.studentDashboard?.subjectAttendance?.length) return 0;\r\n\r\n    const totalPresent = this.studentDashboard.subjectAttendance.reduce((sum: number, subject: any) => sum + subject.present, 0);\r\n    const totalClasses = this.studentDashboard.subjectAttendance.reduce((sum: number, subject: any) => sum + subject.total, 0);\r\n\r\n    return totalClasses > 0 ? Math.round((totalPresent / totalClasses) * 100) : 0;\r\n  }\r\n}\r\n", "<div class=\"student-subjects-container\">\r\n  <!-- Header -->\r\n  <div class=\"page-header\">\r\n    <div class=\"header-content\">\r\n      <h1>My Subjects</h1>\r\n      <p>View your enrolled subjects and attendance details</p>\r\n    </div>\r\n    <button mat-icon-button (click)=\"refreshData()\" matTooltip=\"Refresh Data\">\r\n      <mat-icon>refresh</mat-icon>\r\n    </button>\r\n  </div>\r\n\r\n  <!-- Loading State -->\r\n  <div *ngIf=\"loading\" class=\"loading-container\">\r\n    <mat-spinner></mat-spinner>\r\n    <p>Loading your subjects...</p>\r\n  </div>\r\n\r\n  <!-- Error State -->\r\n  <div *ngIf=\"error && !loading\" class=\"error-container\">\r\n    <mat-icon>error</mat-icon>\r\n    <h3>Error Loading Subjects</h3>\r\n    <p>{{ error }}</p>\r\n    <button mat-raised-button color=\"primary\" (click)=\"refreshData()\">\r\n      <mat-icon>refresh</mat-icon>\r\n      Try Again\r\n    </button>\r\n  </div>\r\n\r\n  <!-- Content -->\r\n  <div *ngIf=\"studentDashboard && !loading && !error\" class=\"subjects-content\">\r\n    <!-- Summary Stats -->\r\n    <div class=\"summary-stats\">\r\n      <mat-card class=\"stat-card total\">\r\n        <mat-card-content>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon\">\r\n              <mat-icon>subject</mat-icon>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ studentDashboard.subjectAttendance?.length || 0 }}</h3>\r\n              <p>Total Subjects</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n\r\n      <mat-card class=\"stat-card average\">\r\n        <mat-card-content>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon\">\r\n              <mat-icon>trending_up</mat-icon>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ calculateOverallPercentage() }}%</h3>\r\n              <p>Overall Attendance</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n    </div>\r\n\r\n    <!-- Subjects List -->\r\n    <div *ngIf=\"studentDashboard.subjectAttendance?.length > 0; else noSubjects\" class=\"subjects-grid\">\r\n      <mat-card *ngFor=\"let subject of studentDashboard.subjectAttendance\" class=\"subject-card\">\r\n        <mat-card-header>\r\n          <div mat-card-avatar class=\"subject-avatar\">\r\n            <mat-icon>subject</mat-icon>\r\n          </div>\r\n          <mat-card-title>{{ subject.subjectName }}</mat-card-title>\r\n          <mat-card-subtitle>Subject Code: {{ subject._id }}</mat-card-subtitle>\r\n        </mat-card-header>\r\n        \r\n        <mat-card-content>\r\n          <div class=\"subject-details\">\r\n            <!-- Attendance Summary -->\r\n            <div class=\"attendance-summary\">\r\n              <div class=\"attendance-circle\" [class]=\"getAttendanceStatus(subject.percentage)\">\r\n                <span class=\"percentage\">{{ subject.percentage | number:'1.0-0' }}%</span>\r\n              </div>\r\n              <div class=\"attendance-info\">\r\n                <h4>{{ getAttendanceMessage(subject.percentage) }}</h4>\r\n                <p>{{ subject.present }} out of {{ subject.total }} classes attended</p>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Progress Bar -->\r\n            <div class=\"progress-container\">\r\n              <div class=\"progress-bar\">\r\n                <div class=\"progress-fill\" \r\n                     [style.width.%]=\"subject.percentage\"\r\n                     [class]=\"getAttendanceStatus(subject.percentage)\">\r\n                </div>\r\n              </div>\r\n              <div class=\"progress-labels\">\r\n                <span>Present: {{ subject.present }}</span>\r\n                <span>Absent: {{ subject.absent }}</span>\r\n                <span>Total: {{ subject.total }}</span>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Subject Stats -->\r\n            <div class=\"subject-stats\">\r\n              <div class=\"stat-item\">\r\n                <mat-icon>check_circle</mat-icon>\r\n                <div class=\"stat-details\">\r\n                  <span class=\"stat-value\">{{ subject.present }}</span>\r\n                  <span class=\"stat-label\">Present</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"stat-item\">\r\n                <mat-icon>cancel</mat-icon>\r\n                <div class=\"stat-details\">\r\n                  <span class=\"stat-value\">{{ subject.absent }}</span>\r\n                  <span class=\"stat-label\">Absent</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"stat-item\">\r\n                <mat-icon>event</mat-icon>\r\n                <div class=\"stat-details\">\r\n                  <span class=\"stat-value\">{{ subject.total }}</span>\r\n                  <span class=\"stat-label\">Total Classes</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n\r\n        <mat-card-actions>\r\n          <button mat-button color=\"primary\" routerLink=\"/dashboard/student/attendance\">\r\n            <mat-icon>fact_check</mat-icon>\r\n            View Attendance\r\n          </button>\r\n        </mat-card-actions>\r\n      </mat-card>\r\n    </div>\r\n\r\n    <!-- No Subjects State -->\r\n    <ng-template #noSubjects>\r\n      <mat-card class=\"no-data-card\">\r\n        <mat-card-content>\r\n          <div class=\"no-data-content\">\r\n            <mat-icon>subject</mat-icon>\r\n            <h3>No Subjects Found</h3>\r\n            <p>You don't have any subjects assigned yet. Please contact your administrator.</p>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n    </ng-template>\r\n\r\n    <!-- Quick Actions -->\r\n    <div class=\"quick-actions\">\r\n      <h3>Quick Actions</h3>\r\n      <div class=\"action-buttons\">\r\n        <button mat-raised-button color=\"primary\" routerLink=\"/dashboard/student/attendance\">\r\n          <mat-icon>fact_check</mat-icon>\r\n          View Full Attendance\r\n        </button>\r\n        <button mat-raised-button color=\"accent\" routerLink=\"/dashboard/student/teachers\">\r\n          <mat-icon>person</mat-icon>\r\n          View Teachers\r\n        </button>\r\n        <button mat-raised-button color=\"primary\" routerLink=\"/dashboard/student/classes\">\r\n          <mat-icon>class</mat-icon>\r\n          View Classes\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": ";;;;;;;;;;;;ICaEA,EAAA,CAAAC,cAAA,aAA+C;IAC7CD,EAAA,CAAAE,SAAA,kBAA2B;IAC3BF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,+BAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;;IAIjCJ,EAAA,CAAAC,cAAA,aAAuD;IAC3CD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC1BJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,6BAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC/BJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAW;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAClBJ,EAAA,CAAAC,cAAA,gBAAkE;IAAxBD,EAAA,CAAAK,UAAA,mBAAAC,iEAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAC/DZ,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC5BJ,EAAA,CAAAG,MAAA,mBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAJNJ,EAAA,CAAAa,SAAA,GAAW;IAAXb,EAAA,CAAAc,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IA0CZhB,EAAA,CAAAC,cAAA,mBAA0F;IAG1ED,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAE9BJ,EAAA,CAAAC,cAAA,qBAAgB;IAAAD,EAAA,CAAAG,MAAA,GAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IAC1DJ,EAAA,CAAAC,cAAA,wBAAmB;IAAAD,EAAA,CAAAG,MAAA,GAA+B;IAAAH,EAAA,CAAAI,YAAA,EAAoB;IAGxEJ,EAAA,CAAAC,cAAA,uBAAkB;IAKeD,EAAA,CAAAG,MAAA,IAA0C;;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAE5EJ,EAAA,CAAAC,cAAA,eAA6B;IACvBD,EAAA,CAAAG,MAAA,IAA8C;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACvDJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,IAAiE;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAK5EJ,EAAA,CAAAC,cAAA,eAAgC;IAE5BD,EAAA,CAAAE,SAAA,eAGM;IACRF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAA6B;IACrBD,EAAA,CAAAG,MAAA,IAA8B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC3CJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,IAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACzCJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,IAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAK3CJ,EAAA,CAAAC,cAAA,eAA2B;IAEbD,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACjCJ,EAAA,CAAAC,cAAA,eAA0B;IACCD,EAAA,CAAAG,MAAA,IAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACrDJ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAG3CJ,EAAA,CAAAC,cAAA,eAAuB;IACXD,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC3BJ,EAAA,CAAAC,cAAA,eAA0B;IACCD,EAAA,CAAAG,MAAA,IAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACpDJ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAG1CJ,EAAA,CAAAC,cAAA,eAAuB;IACXD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC1BJ,EAAA,CAAAC,cAAA,eAA0B;IACCD,EAAA,CAAAG,MAAA,IAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACnDJ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAOvDJ,EAAA,CAAAC,cAAA,wBAAkB;IAEJD,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC/BJ,EAAA,CAAAG,MAAA,yBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;IA/DOJ,EAAA,CAAAa,SAAA,GAAyB;IAAzBb,EAAA,CAAAc,iBAAA,CAAAG,UAAA,CAAAC,WAAA,CAAyB;IACtBlB,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAAmB,kBAAA,mBAAAF,UAAA,CAAAG,GAAA,KAA+B;IAOfpB,EAAA,CAAAa,SAAA,GAAiD;IAAjDb,EAAA,CAAAqB,UAAA,CAAAC,MAAA,CAAAC,mBAAA,CAAAN,UAAA,CAAAO,UAAA,EAAiD;IACrDxB,EAAA,CAAAa,SAAA,GAA0C;IAA1Cb,EAAA,CAAAmB,kBAAA,KAAAnB,EAAA,CAAAyB,WAAA,SAAAR,UAAA,CAAAO,UAAA,gBAA0C;IAG/DxB,EAAA,CAAAa,SAAA,GAA8C;IAA9Cb,EAAA,CAAAc,iBAAA,CAAAQ,MAAA,CAAAI,oBAAA,CAAAT,UAAA,CAAAO,UAAA,EAA8C;IAC/CxB,EAAA,CAAAa,SAAA,GAAiE;IAAjEb,EAAA,CAAA2B,kBAAA,KAAAV,UAAA,CAAAW,OAAA,cAAAX,UAAA,CAAAY,KAAA,sBAAiE;IAS/D7B,EAAA,CAAAa,SAAA,GAAiD;IAAjDb,EAAA,CAAAqB,UAAA,CAAAC,MAAA,CAAAC,mBAAA,CAAAN,UAAA,CAAAO,UAAA,EAAiD;IADjDxB,EAAA,CAAA8B,WAAA,UAAAb,UAAA,CAAAO,UAAA,MAAoC;IAKnCxB,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAAmB,kBAAA,cAAAF,UAAA,CAAAW,OAAA,KAA8B;IAC9B5B,EAAA,CAAAa,SAAA,GAA4B;IAA5Bb,EAAA,CAAAmB,kBAAA,aAAAF,UAAA,CAAAc,MAAA,KAA4B;IAC5B/B,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAmB,kBAAA,YAAAF,UAAA,CAAAY,KAAA,KAA0B;IASL7B,EAAA,CAAAa,SAAA,GAAqB;IAArBb,EAAA,CAAAc,iBAAA,CAAAG,UAAA,CAAAW,OAAA,CAAqB;IAOrB5B,EAAA,CAAAa,SAAA,GAAoB;IAApBb,EAAA,CAAAc,iBAAA,CAAAG,UAAA,CAAAc,MAAA,CAAoB;IAOpB/B,EAAA,CAAAa,SAAA,GAAmB;IAAnBb,EAAA,CAAAc,iBAAA,CAAAG,UAAA,CAAAY,KAAA,CAAmB;;;;;IAzD1D7B,EAAA,CAAAC,cAAA,cAAmG;IACjGD,EAAA,CAAAgC,UAAA,IAAAC,0DAAA,yBAsEW;IACbjC,EAAA,CAAAI,YAAA,EAAM;;;;IAvE0BJ,EAAA,CAAAa,SAAA,GAAqC;IAArCb,EAAA,CAAAkC,UAAA,YAAAC,MAAA,CAAAC,gBAAA,CAAAC,iBAAA,CAAqC;;;;;IA2EnErC,EAAA,CAAAC,cAAA,mBAA+B;IAGfD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC5BJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,wBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC1BJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,mFAA4E;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAlH7FJ,EAAA,CAAAC,cAAA,cAA6E;IAOvDD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAE9BJ,EAAA,CAAAC,cAAA,cAAuB;IACjBD,EAAA,CAAAG,MAAA,IAAqD;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC9DJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,sBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAM7BJ,EAAA,CAAAC,cAAA,oBAAoC;IAIlBD,EAAA,CAAAG,MAAA,mBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAElCJ,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAG,MAAA,IAAmC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC5CJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,0BAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAQnCJ,EAAA,CAAAgC,UAAA,KAAAM,+CAAA,kBAwEM;IAGNtC,EAAA,CAAAgC,UAAA,KAAAO,uDAAA,iCAAAvC,EAAA,CAAAwC,sBAAA,CAUc;IAGdxC,EAAA,CAAAC,cAAA,eAA2B;IACrBD,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACtBJ,EAAA,CAAAC,cAAA,eAA4B;IAEdD,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC/BJ,EAAA,CAAAG,MAAA,8BACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAAkF;IACtED,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC3BJ,EAAA,CAAAG,MAAA,uBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAAkF;IACtED,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC1BJ,EAAA,CAAAG,MAAA,sBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;IA7HCJ,EAAA,CAAAa,SAAA,IAAqD;IAArDb,EAAA,CAAAc,iBAAA,EAAA2B,MAAA,CAAAL,gBAAA,CAAAC,iBAAA,kBAAAI,MAAA,CAAAL,gBAAA,CAAAC,iBAAA,CAAAK,MAAA,OAAqD;IAcrD1C,EAAA,CAAAa,SAAA,IAAmC;IAAnCb,EAAA,CAAAmB,kBAAA,KAAAsB,MAAA,CAAAE,0BAAA,QAAmC;IAS3C3C,EAAA,CAAAa,SAAA,GAAsD;IAAtDb,EAAA,CAAAkC,UAAA,UAAAO,MAAA,CAAAL,gBAAA,CAAAC,iBAAA,kBAAAI,MAAA,CAAAL,gBAAA,CAAAC,iBAAA,CAAAK,MAAA,MAAsD,aAAAE,GAAA;;;ADtDhE,OAAM,MAAOC,wBAAwB;EAMnCC,YACUC,WAAwB,EACxBC,gBAAkC;IADlC,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAL1B,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAjC,KAAK,GAAkB,IAAI;EAKxB;EAEHkC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,GAAG,IAAI,CAACJ,WAAW,CAACK,uBAAuB,EAAE,EAAEC,IAAI;IACnE,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAA,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACH,WAAW,EAAE;IAEvB,IAAI,CAACF,OAAO,GAAG,IAAI;IACnB,IAAI,CAACjC,KAAK,GAAG,IAAI;IAEjB,IAAI,CAACgC,gBAAgB,CAACO,mBAAmB,CAAC,IAAI,CAACJ,WAAW,CAAC/B,GAAG,CAAC,CAACoC,SAAS,CAAC;MACxEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACvB,gBAAgB,GAAGsB,QAAQ,CAACE,SAAS;SAC3C,MAAM;UACL,IAAI,CAAC5C,KAAK,GAAG,iCAAiC;;QAEhD,IAAI,CAACiC,OAAO,GAAG,KAAK;MACtB,CAAC;MACDjC,KAAK,EAAGA,KAAK,IAAI;QACf6C,OAAO,CAAC7C,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD,IAAI,CAACA,KAAK,GAAG,6BAA6B;QAC1C,IAAI,CAACiC,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEArC,WAAWA,CAAA;IACT,IAAI,CAAC0C,mBAAmB,EAAE;EAC5B;EAEA/B,mBAAmBA,CAACC,UAAkB;IACpC,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO,MAAM;IACnC,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO,SAAS;IACtC,OAAO,QAAQ;EACjB;EAEAE,oBAAoBA,CAACF,UAAkB;IACrC,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO,iBAAiB;IAC9C,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO,mBAAmB;IAChD,OAAO,iBAAiB;EAC1B;EAEAmB,0BAA0BA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACP,gBAAgB,EAAEC,iBAAiB,EAAEK,MAAM,EAAE,OAAO,CAAC;IAE/D,MAAMoB,YAAY,GAAG,IAAI,CAAC1B,gBAAgB,CAACC,iBAAiB,CAAC0B,MAAM,CAAC,CAACC,GAAW,EAAEC,OAAY,KAAKD,GAAG,GAAGC,OAAO,CAACrC,OAAO,EAAE,CAAC,CAAC;IAC5H,MAAMsC,YAAY,GAAG,IAAI,CAAC9B,gBAAgB,CAACC,iBAAiB,CAAC0B,MAAM,CAAC,CAACC,GAAW,EAAEC,OAAY,KAAKD,GAAG,GAAGC,OAAO,CAACpC,KAAK,EAAE,CAAC,CAAC;IAE1H,OAAOqC,YAAY,GAAG,CAAC,GAAGC,IAAI,CAACC,KAAK,CAAEN,YAAY,GAAGI,YAAY,GAAI,GAAG,CAAC,GAAG,CAAC;EAC/E;EAAC,QAAAG,CAAA,G;qBA9DUxB,wBAAwB,EAAA7C,EAAA,CAAAsE,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxE,EAAA,CAAAsE,iBAAA,CAAAG,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAxB9B,wBAAwB;IAAA+B,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCTrClF,EAAA,CAAAC,cAAA,aAAwC;QAI9BD,EAAA,CAAAG,MAAA,kBAAW;QAAAH,EAAA,CAAAI,YAAA,EAAK;QACpBJ,EAAA,CAAAC,cAAA,QAAG;QAAAD,EAAA,CAAAG,MAAA,yDAAkD;QAAAH,EAAA,CAAAI,YAAA,EAAI;QAE3DJ,EAAA,CAAAC,cAAA,gBAA0E;QAAlDD,EAAA,CAAAK,UAAA,mBAAA+E,0DAAA;UAAA,OAASD,GAAA,CAAAvE,WAAA,EAAa;QAAA,EAAC;QAC7CZ,EAAA,CAAAC,cAAA,eAAU;QAAAD,EAAA,CAAAG,MAAA,cAAO;QAAAH,EAAA,CAAAI,YAAA,EAAW;QAKhCJ,EAAA,CAAAgC,UAAA,KAAAqD,wCAAA,iBAGM;QAGNrF,EAAA,CAAAgC,UAAA,KAAAsD,wCAAA,kBAQM;QAGNtF,EAAA,CAAAgC,UAAA,KAAAuD,wCAAA,kBA0IM;QACRvF,EAAA,CAAAI,YAAA,EAAM;;;QA5JEJ,EAAA,CAAAa,SAAA,IAAa;QAAbb,EAAA,CAAAkC,UAAA,SAAAiD,GAAA,CAAAlC,OAAA,CAAa;QAMbjD,EAAA,CAAAa,SAAA,GAAuB;QAAvBb,EAAA,CAAAkC,UAAA,SAAAiD,GAAA,CAAAnE,KAAA,KAAAmE,GAAA,CAAAlC,OAAA,CAAuB;QAWvBjD,EAAA,CAAAa,SAAA,GAA4C;QAA5Cb,EAAA,CAAAkC,UAAA,SAAAiD,GAAA,CAAA/C,gBAAA,KAAA+C,GAAA,CAAAlC,OAAA,KAAAkC,GAAA,CAAAnE,KAAA,CAA4C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}