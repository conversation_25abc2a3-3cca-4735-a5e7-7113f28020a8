.profile-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #1976d2;
  font-weight: 500;
}

.subtitle {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  text-align: center;
}

.loading-container p {
  margin-top: 16px;
  color: #666;
}

.profile-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Profile Summary Card */
.profile-summary {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  color: white;
  margin-bottom: 24px;
}

.profile-header {
  display: flex;
  align-items: center;
  gap: 16px;
}

.profile-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.profile-avatar mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
}

.profile-info {
  flex: 1;
}

.profile-info h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 500;
}

.role-badge {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  margin: 0 0 8px 0;
}

.email {
  margin: 0;
  opacity: 0.9;
  font-size: 14px;
}

.profile-status {
  text-align: right;
}

.status-badge {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.2);
}

.status-badge.active {
  background: #4caf50;
}

/* Tabs */
.profile-tabs {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-content {
  padding: 24px;
}

/* Form Styles */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.student-fields,
.teacher-fields {
  grid-column: 1 / -1;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 8px;
  margin: 16px 0;
}

.academic-section {
  grid-column: 1 / -1;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 16px 0;
}

.academic-section h3 {
  margin: 0 0 16px 0;
  color: #1976d2;
  font-size: 18px;
  font-weight: 500;
}

.academic-section .form-grid {
  margin-bottom: 0;
}

/* Password Form */
.password-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-width: 400px;
}

/* Account Info */
.account-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #eee;
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  font-weight: 500;
  color: #666;
}

.value {
  color: #333;
}

.value.active {
  color: #4caf50;
  font-weight: 500;
}

/* Card Actions */
mat-card-actions {
  display: flex;
  gap: 12px;
  padding: 16px 24px;
  background: #f8f9fa;
  margin: 0 -24px -24px -24px;
  border-radius: 0 0 8px 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-container {
    padding: 16px;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .student-fields,
  .teacher-fields {
    grid-template-columns: 1fr;
  }

  .profile-header {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .profile-status {
    text-align: center;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

/* Material Form Field Customization */
mat-form-field {
  width: 100%;
}

mat-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

mat-card-header {
  background: #f8f9fa;
  margin: -24px -24px 24px -24px;
  padding: 16px 24px;
  border-radius: 8px 8px 0 0;
}

mat-card-title {
  color: #1976d2;
  font-size: 18px;
  font-weight: 500;
}

mat-card-subtitle {
  color: #666;
  font-size: 14px;
}

/* Spinner in buttons */
button mat-spinner {
  margin-right: 8px;
}

/* Checkbox styling */
mat-checkbox {
  margin: 16px 0;
}

/* Tab group styling */
::ng-deep .mat-tab-group.profile-tabs .mat-tab-header {
  border-bottom: 1px solid #e0e0e0;
}

::ng-deep .mat-tab-group.profile-tabs .mat-tab-label {
  min-width: 120px;
  padding: 0 24px;
}

::ng-deep .mat-tab-group.profile-tabs .mat-tab-body-wrapper {
  background: white;
}
