{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class AuthService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.apiUrl = environment.apiUrl; // Use environment API URL\n    this.isLoggedInSubject = new BehaviorSubject(false);\n    this.isLoggedIn$ = this.isLoggedInSubject.asObservable();\n    // Check if user is already logged in\n    const token = localStorage.getItem('token');\n    if (token) {\n      this.isLoggedInSubject.next(true);\n    }\n  }\n  signup(userData) {\n    return this.http.post(`${this.apiUrl}/signup`, userData);\n  }\n  login(credentials) {\n    return this.http.post(`${this.apiUrl}/login`, credentials);\n  }\n  forgotPassword(email) {\n    return this.http.post(`${this.apiUrl}/forgot-password`, {\n      email\n    });\n  }\n  verifyOtp(data) {\n    return this.http.post(`${this.apiUrl}/verify-otp`, data);\n  }\n  resendOtp(email) {\n    return this.http.post(`${this.apiUrl}/resend-otp`, {\n      email\n    });\n  }\n  resetPassword(data) {\n    return this.http.post(`${this.apiUrl}/reset-password`, data);\n  }\n  // Additional methods for user management\n  getAllUsersByRole(role) {\n    return this.http.get(`${this.apiUrl}/api/users/${role}`);\n  }\n  getAllUsers() {\n    return this.http.get(`${this.apiUrl}/allUser`);\n  }\n  deleteUser(id) {\n    return this.http.delete(`${this.apiUrl}/deleteUser/${id}`);\n  }\n  logout() {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    this.isLoggedInSubject.next(false);\n    this.router.navigate(['/auth']);\n  }\n  setToken(token) {\n    localStorage.setItem('token', token);\n    this.isLoggedInSubject.next(true);\n  }\n  getToken() {\n    return localStorage.getItem('token');\n  }\n  isAuthenticated() {\n    return !!this.getToken();\n  }\n  static #_ = this.ɵfac = function AuthService_Factory(t) {\n    return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthService,\n    factory: AuthService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["BehaviorSubject", "environment", "AuthService", "constructor", "http", "router", "apiUrl", "isLoggedInSubject", "isLoggedIn$", "asObservable", "token", "localStorage", "getItem", "next", "signup", "userData", "post", "login", "credentials", "forgotPassword", "email", "verifyOtp", "data", "resendOtp", "resetPassword", "getAllUsersByRole", "role", "get", "getAllUsers", "deleteUser", "id", "delete", "logout", "removeItem", "navigate", "setToken", "setItem", "getToken", "isAuthenticated", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\auth\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable, BehaviorSubject } from 'rxjs';\nimport { Router } from '@angular/router';\nimport { environment } from 'src/environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthService {\n  private apiUrl = environment.apiUrl; // Use environment API URL\n  private isLoggedInSubject = new BehaviorSubject<boolean>(false);\n  public isLoggedIn$ = this.isLoggedInSubject.asObservable();\n\n  constructor(private http: HttpClient, private router: Router) {\n    // Check if user is already logged in\n    const token = localStorage.getItem('token');\n    if (token) {\n      this.isLoggedInSubject.next(true);\n    }\n  }\n\n  signup(userData: any): Observable<any> {\n    return this.http.post(`${this.apiUrl}/signup`, userData);\n  }\n\n  login(credentials: any): Observable<any> {\n    return this.http.post(`${this.apiUrl}/login`, credentials);\n  }\n\n  forgotPassword(email: string): Observable<any> {\n    return this.http.post(`${this.apiUrl}/forgot-password`, { email });\n  }\n\n  verifyOtp(data: any): Observable<any> {\n    return this.http.post(`${this.apiUrl}/verify-otp`, data);\n  }\n\n  resendOtp(email: string): Observable<any> {\n    return this.http.post(`${this.apiUrl}/resend-otp`, { email });\n  }\n\n  resetPassword(data: any): Observable<any> {\n    return this.http.post(`${this.apiUrl}/reset-password`, data);\n  }\n\n  // Additional methods for user management\n  getAllUsersByRole(role: string): Observable<any> {\n    return this.http.get(`${this.apiUrl}/api/users/${role}`);\n  }\n\n  getAllUsers(): Observable<any> {\n    return this.http.get(`${this.apiUrl}/allUser`);\n  }\n\n  deleteUser(id: string): Observable<any> {\n    return this.http.delete(`${this.apiUrl}/deleteUser/${id}`);\n  }\n\n  logout(): void {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    this.isLoggedInSubject.next(false);\n    this.router.navigate(['/auth']);\n  }\n\n  setToken(token: string): void {\n    localStorage.setItem('token', token);\n    this.isLoggedInSubject.next(true);\n  }\n\n  getToken(): string | null {\n    return localStorage.getItem('token');\n  }\n\n  isAuthenticated(): boolean {\n    return !!this.getToken();\n  }\n}\n\n"], "mappings": "AAEA,SAAqBA,eAAe,QAAQ,MAAM;AAElD,SAASC,WAAW,QAAQ,8BAA8B;;;;AAK1D,OAAM,MAAOC,WAAW;EAKtBC,YAAoBC,IAAgB,EAAUC,MAAc;IAAxC,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,MAAM,GAANA,MAAM;IAJ5C,KAAAC,MAAM,GAAGL,WAAW,CAACK,MAAM,CAAC,CAAC;IAC7B,KAAAC,iBAAiB,GAAG,IAAIP,eAAe,CAAU,KAAK,CAAC;IACxD,KAAAQ,WAAW,GAAG,IAAI,CAACD,iBAAiB,CAACE,YAAY,EAAE;IAGxD;IACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACT,IAAI,CAACH,iBAAiB,CAACM,IAAI,CAAC,IAAI,CAAC;;EAErC;EAEAC,MAAMA,CAACC,QAAa;IAClB,OAAO,IAAI,CAACX,IAAI,CAACY,IAAI,CAAC,GAAG,IAAI,CAACV,MAAM,SAAS,EAAES,QAAQ,CAAC;EAC1D;EAEAE,KAAKA,CAACC,WAAgB;IACpB,OAAO,IAAI,CAACd,IAAI,CAACY,IAAI,CAAC,GAAG,IAAI,CAACV,MAAM,QAAQ,EAAEY,WAAW,CAAC;EAC5D;EAEAC,cAAcA,CAACC,KAAa;IAC1B,OAAO,IAAI,CAAChB,IAAI,CAACY,IAAI,CAAC,GAAG,IAAI,CAACV,MAAM,kBAAkB,EAAE;MAAEc;IAAK,CAAE,CAAC;EACpE;EAEAC,SAASA,CAACC,IAAS;IACjB,OAAO,IAAI,CAAClB,IAAI,CAACY,IAAI,CAAC,GAAG,IAAI,CAACV,MAAM,aAAa,EAAEgB,IAAI,CAAC;EAC1D;EAEAC,SAASA,CAACH,KAAa;IACrB,OAAO,IAAI,CAAChB,IAAI,CAACY,IAAI,CAAC,GAAG,IAAI,CAACV,MAAM,aAAa,EAAE;MAAEc;IAAK,CAAE,CAAC;EAC/D;EAEAI,aAAaA,CAACF,IAAS;IACrB,OAAO,IAAI,CAAClB,IAAI,CAACY,IAAI,CAAC,GAAG,IAAI,CAACV,MAAM,iBAAiB,EAAEgB,IAAI,CAAC;EAC9D;EAEA;EACAG,iBAAiBA,CAACC,IAAY;IAC5B,OAAO,IAAI,CAACtB,IAAI,CAACuB,GAAG,CAAC,GAAG,IAAI,CAACrB,MAAM,cAAcoB,IAAI,EAAE,CAAC;EAC1D;EAEAE,WAAWA,CAAA;IACT,OAAO,IAAI,CAACxB,IAAI,CAACuB,GAAG,CAAC,GAAG,IAAI,CAACrB,MAAM,UAAU,CAAC;EAChD;EAEAuB,UAAUA,CAACC,EAAU;IACnB,OAAO,IAAI,CAAC1B,IAAI,CAAC2B,MAAM,CAAC,GAAG,IAAI,CAACzB,MAAM,eAAewB,EAAE,EAAE,CAAC;EAC5D;EAEAE,MAAMA,CAAA;IACJrB,YAAY,CAACsB,UAAU,CAAC,OAAO,CAAC;IAChCtB,YAAY,CAACsB,UAAU,CAAC,MAAM,CAAC;IAC/B,IAAI,CAAC1B,iBAAiB,CAACM,IAAI,CAAC,KAAK,CAAC;IAClC,IAAI,CAACR,MAAM,CAAC6B,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACjC;EAEAC,QAAQA,CAACzB,KAAa;IACpBC,YAAY,CAACyB,OAAO,CAAC,OAAO,EAAE1B,KAAK,CAAC;IACpC,IAAI,CAACH,iBAAiB,CAACM,IAAI,CAAC,IAAI,CAAC;EACnC;EAEAwB,QAAQA,CAAA;IACN,OAAO1B,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EACtC;EAEA0B,eAAeA,CAAA;IACb,OAAO,CAAC,CAAC,IAAI,CAACD,QAAQ,EAAE;EAC1B;EAAC,QAAAE,CAAA,G;qBApEUrC,WAAW,EAAAsC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAX5C,WAAW;IAAA6C,OAAA,EAAX7C,WAAW,CAAA8C,IAAA;IAAAC,UAAA,EAFV;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}