{"ast": null, "code": "import { throwError } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/snack-bar\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../services/user.service\";\nexport class ErrorInterceptor {\n  constructor(snackBar, router, userService) {\n    this.snackBar = snackBar;\n    this.router = router;\n    this.userService = userService;\n  }\n  intercept(req, next) {\n    return next.handle(req).pipe(catchError(error => {\n      let errorMessage = 'An unexpected error occurred';\n      if (error.error instanceof ErrorEvent) {\n        // Client-side error\n        errorMessage = `Error: ${error.error.message}`;\n      } else {\n        // Server-side error\n        switch (error.status) {\n          case 400:\n            errorMessage = error.error?.message || 'Bad Request';\n            break;\n          case 401:\n            errorMessage = 'Unauthorized. Please login again.';\n            this.handleUnauthorized();\n            break;\n          case 403:\n            errorMessage = 'Access forbidden. You don\\'t have permission.';\n            break;\n          case 404:\n            errorMessage = 'Resource not found';\n            break;\n          case 422:\n            errorMessage = error.error?.message || 'Validation error';\n            break;\n          case 500:\n            errorMessage = 'Internal server error. Please try again later.';\n            break;\n          case 503:\n            errorMessage = 'Service unavailable. Please try again later.';\n            break;\n          default:\n            errorMessage = error.error?.message || `Error Code: ${error.status}`;\n        }\n      }\n      // Show error message to user\n      this.snackBar.open(errorMessage, 'Close', {\n        duration: 5000,\n        panelClass: ['error-snackbar']\n      });\n      console.error('HTTP Error:', error);\n      return throwError(() => error);\n    }));\n  }\n  handleUnauthorized() {\n    // Clear user data and redirect to login\n    this.userService.logout();\n    this.router.navigate(['/auth/login']);\n  }\n  static #_ = this.ɵfac = function ErrorInterceptor_Factory(t) {\n    return new (t || ErrorInterceptor)(i0.ɵɵinject(i1.MatSnackBar), i0.ɵɵinject(i2.Router), i0.ɵɵinject(i3.UserService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ErrorInterceptor,\n    factory: ErrorInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["throwError", "catchError", "ErrorInterceptor", "constructor", "snackBar", "router", "userService", "intercept", "req", "next", "handle", "pipe", "error", "errorMessage", "ErrorEvent", "message", "status", "handleUnauthorized", "open", "duration", "panelClass", "console", "logout", "navigate", "_", "i0", "ɵɵinject", "i1", "MatSnackBar", "i2", "Router", "i3", "UserService", "_2", "factory", "ɵfac"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\interceptors\\error.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse } from '@angular/common/http';\r\nimport { Observable, throwError } from 'rxjs';\r\nimport { catchError } from 'rxjs/operators';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { Router } from '@angular/router';\r\nimport { UserService } from '../services/user.service';\r\n\r\n@Injectable()\r\nexport class ErrorInterceptor implements HttpInterceptor {\r\n\r\n  constructor(\r\n    private snackBar: MatSnackBar,\r\n    private router: Router,\r\n    private userService: UserService\r\n  ) {}\r\n\r\n  intercept(req: HttpRequest<any>, next: <PERSON>ttpHand<PERSON>): Observable<HttpEvent<any>> {\r\n    return next.handle(req).pipe(\r\n      catchError((error: HttpErrorResponse) => {\r\n        let errorMessage = 'An unexpected error occurred';\r\n\r\n        if (error.error instanceof ErrorEvent) {\r\n          // Client-side error\r\n          errorMessage = `Error: ${error.error.message}`;\r\n        } else {\r\n          // Server-side error\r\n          switch (error.status) {\r\n            case 400:\r\n              errorMessage = error.error?.message || 'Bad Request';\r\n              break;\r\n            case 401:\r\n              errorMessage = 'Unauthorized. Please login again.';\r\n              this.handleUnauthorized();\r\n              break;\r\n            case 403:\r\n              errorMessage = 'Access forbidden. You don\\'t have permission.';\r\n              break;\r\n            case 404:\r\n              errorMessage = 'Resource not found';\r\n              break;\r\n            case 422:\r\n              errorMessage = error.error?.message || 'Validation error';\r\n              break;\r\n            case 500:\r\n              errorMessage = 'Internal server error. Please try again later.';\r\n              break;\r\n            case 503:\r\n              errorMessage = 'Service unavailable. Please try again later.';\r\n              break;\r\n            default:\r\n              errorMessage = error.error?.message || `Error Code: ${error.status}`;\r\n          }\r\n        }\r\n\r\n        // Show error message to user\r\n        this.snackBar.open(errorMessage, 'Close', {\r\n          duration: 5000,\r\n          panelClass: ['error-snackbar']\r\n        });\r\n\r\n        console.error('HTTP Error:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  private handleUnauthorized(): void {\r\n    // Clear user data and redirect to login\r\n    this.userService.logout();\r\n    this.router.navigate(['/auth/login']);\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAAqBA,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,QAAQ,gBAAgB;;;;;AAM3C,OAAM,MAAOC,gBAAgB;EAE3BC,YACUC,QAAqB,EACrBC,MAAc,EACdC,WAAwB;IAFxB,KAAAF,QAAQ,GAARA,QAAQ;IACR,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;EAClB;EAEHC,SAASA,CAACC,GAAqB,EAAEC,IAAiB;IAChD,OAAOA,IAAI,CAACC,MAAM,CAACF,GAAG,CAAC,CAACG,IAAI,CAC1BV,UAAU,CAAEW,KAAwB,IAAI;MACtC,IAAIC,YAAY,GAAG,8BAA8B;MAEjD,IAAID,KAAK,CAACA,KAAK,YAAYE,UAAU,EAAE;QACrC;QACAD,YAAY,GAAG,UAAUD,KAAK,CAACA,KAAK,CAACG,OAAO,EAAE;OAC/C,MAAM;QACL;QACA,QAAQH,KAAK,CAACI,MAAM;UAClB,KAAK,GAAG;YACNH,YAAY,GAAGD,KAAK,CAACA,KAAK,EAAEG,OAAO,IAAI,aAAa;YACpD;UACF,KAAK,GAAG;YACNF,YAAY,GAAG,mCAAmC;YAClD,IAAI,CAACI,kBAAkB,EAAE;YACzB;UACF,KAAK,GAAG;YACNJ,YAAY,GAAG,+CAA+C;YAC9D;UACF,KAAK,GAAG;YACNA,YAAY,GAAG,oBAAoB;YACnC;UACF,KAAK,GAAG;YACNA,YAAY,GAAGD,KAAK,CAACA,KAAK,EAAEG,OAAO,IAAI,kBAAkB;YACzD;UACF,KAAK,GAAG;YACNF,YAAY,GAAG,gDAAgD;YAC/D;UACF,KAAK,GAAG;YACNA,YAAY,GAAG,8CAA8C;YAC7D;UACF;YACEA,YAAY,GAAGD,KAAK,CAACA,KAAK,EAAEG,OAAO,IAAI,eAAeH,KAAK,CAACI,MAAM,EAAE;;;MAI1E;MACA,IAAI,CAACZ,QAAQ,CAACc,IAAI,CAACL,YAAY,EAAE,OAAO,EAAE;QACxCM,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,CAAC,gBAAgB;OAC9B,CAAC;MAEFC,OAAO,CAACT,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnC,OAAOZ,UAAU,CAAC,MAAMY,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEQK,kBAAkBA,CAAA;IACxB;IACA,IAAI,CAACX,WAAW,CAACgB,MAAM,EAAE;IACzB,IAAI,CAACjB,MAAM,CAACkB,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;EAAC,QAAAC,CAAA,G;qBA9DUtB,gBAAgB,EAAAuB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAhB/B,gBAAgB;IAAAgC,OAAA,EAAhBhC,gBAAgB,CAAAiC;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}