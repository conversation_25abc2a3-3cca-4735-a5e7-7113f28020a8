{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { LoginComponent } from './login/login.component';\nimport { SignupComponent } from './signup/signup.component';\nimport { EnterCodeManuallyComponent } from './enter-code-manually/enter-code-manually.component';\nimport { ForgotPasswordComponent } from './forgot-password/forgot-password.component';\nimport { ResetPasswordComponent } from './reset-password/reset-password.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: LoginComponent\n}, {\n  path: 'sign-up',\n  component: SignupComponent\n}, {\n  path: 'Enter-code',\n  component: EnterCodeManuallyComponent\n}, {\n  path: 'forgot-password',\n  component: ForgotPasswordComponent\n}, {\n  path: 'reset-password',\n  component: ResetPasswordComponent\n}];\nexport let AuthRoutingModule = /*#__PURE__*/(() => {\n  class AuthRoutingModule {\n    static #_ = this.ɵfac = function AuthRoutingModule_Factory(t) {\n      return new (t || AuthRoutingModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AuthRoutingModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n  return AuthRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}