import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { TimetableListComponent } from './timetable-list/timetable-list.component';
import { TimetableCreateComponent } from './timetable-create/timetable-create.component';

const routes: Routes = [
  { path: '', component: TimetableListComponent },
  { path: 'list', component: TimetableListComponent },
  { path: 'create', component: TimetableCreateComponent },
  { path: 'edit/:id', component: TimetableCreateComponent },
  { path: 'view/:id', component: TimetableCreateComponent }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class TimetableRoutingModule { }
