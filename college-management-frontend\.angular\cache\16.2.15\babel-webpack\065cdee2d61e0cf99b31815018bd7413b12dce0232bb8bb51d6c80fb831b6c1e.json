{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/services/subject.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"src/app/services/department.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/icon\";\nfunction SubjectFormComponent_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"save\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Save \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction SubjectFormComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 18);\n    i0.ɵɵtext(1, \" Saving... \");\n  }\n}\nfunction SubjectFormComponent_option_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dept_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", dept_r4._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", dept_r4.name, \" \");\n  }\n}\nexport let SubjectFormComponent = /*#__PURE__*/(() => {\n  class SubjectFormComponent {\n    constructor(fb, subjectService, router, ActivatedRoute, departmentservice) {\n      this.fb = fb;\n      this.subjectService = subjectService;\n      this.router = router;\n      this.ActivatedRoute = ActivatedRoute;\n      this.departmentservice = departmentservice;\n      this.editingSubjectId = null;\n      this.isLoading = false;\n      this.departments = [];\n      this.subjectForm = this.fb.group({\n        subjectName: ['', Validators.required],\n        code: [''],\n        department: ['', Validators.required]\n      });\n    }\n    ngOnInit() {\n      this.editingSubjectId = this.ActivatedRoute.snapshot.paramMap.get('id');\n      this.loadDepartments();\n      if (this.editingSubjectId) {\n        this.loadSubjectData(this.editingSubjectId);\n      }\n    }\n    loadDepartments() {\n      this.departmentservice.getdepartment().subscribe(res => {\n        this.departments = res.departments;\n      }, err => {\n        console.error('Failed to load departments', err);\n      });\n    }\n    // Load subject data for edit\n    loadSubjectData(id) {\n      this.subjectService.getSubjectById(id).subscribe(subject => {\n        this.subjectForm.patchValue({\n          subjectName: subject?.subject?.subjectName,\n          code: subject?.subject?.code\n        });\n      }, error => {\n        console.error('Error fetching subject:', error);\n      });\n    }\n    // Submit form\n    onSubmit() {\n      if (this.subjectForm.invalid) return;\n      this.isLoading = true;\n      const subjectData = this.subjectForm.value;\n      const request = this.editingSubjectId ? this.subjectService.updateSubject(this.editingSubjectId, subjectData) : this.subjectService.addSubject(subjectData);\n      request.subscribe(res => {\n        this.isLoading = false;\n        Swal.fire({\n          title: res.message,\n          icon: 'success',\n          confirmButtonColor: '#3085d6',\n          timer: 1500\n        }).then(() => {\n          this.router.navigate(['/dashboard/admin/subjects']);\n          this.clearForm();\n        });\n      }, error => {\n        this.isLoading = false;\n        console.error('Submission error:', error);\n        Swal.fire('Error', 'Something went wrong.', 'error');\n      });\n    }\n    // Navigate back\n    goBack() {\n      this.router.navigate(['/dashboard/admin/subjects']);\n    }\n    // Clear form\n    clearForm() {\n      this.subjectForm.reset();\n      this.editingSubjectId = null;\n    }\n    static #_ = this.ɵfac = function SubjectFormComponent_Factory(t) {\n      return new (t || SubjectFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.SubjectService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.DepartmentService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SubjectFormComponent,\n      selectors: [[\"app-subject-form\"]],\n      decls: 30,\n      vars: 6,\n      consts: [[1, \"form-container\"], [1, \"form-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"d-flex\", \"gap-2\"], [1, \"btn-back\", 3, \"click\"], [1, \"btn\", \"btn-primary\", \"btn-save\", 3, \"disabled\", \"click\"], [4, \"ngIf\", \"ngIfElse\"], [\"loading\", \"\"], [3, \"formGroup\"], [1, \"form-grid\"], [1, \"form-group\", \"mb-3\"], [\"for\", \"subjectName\"], [\"type\", \"text\", \"placeholder\", \"Enter Subject Name\", \"formControlName\", \"subjectName\", 1, \"form-control\"], [\"for\", \"department\"], [\"formControlName\", \"department\", 1, \"form-control\", 2, \"cursor\", \"pointer\"], [\"value\", \"\", \"disabled\", \"\", \"selected\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"code\"], [\"type\", \"text\", \"placeholder\", \"Enter Subject Code\", \"formControlName\", \"code\", 1, \"form-control\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"text-light\", \"me-1\"], [3, \"value\"]],\n      template: function SubjectFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 2)(5, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function SubjectFormComponent_Template_button_click_5_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵelementStart(6, \"mat-icon\");\n          i0.ɵɵtext(7, \"arrow_back\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(8, \" Back \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function SubjectFormComponent_Template_button_click_9_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtemplate(10, SubjectFormComponent_ng_container_10_Template, 4, 0, \"ng-container\", 5);\n          i0.ɵɵtemplate(11, SubjectFormComponent_ng_template_11_Template, 2, 0, \"ng-template\", null, 6, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"form\", 7)(14, \"div\", 8)(15, \"div\", 9)(16, \"label\", 10);\n          i0.ɵɵtext(17, \"Subject Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(18, \"input\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 9)(20, \"label\", 12);\n          i0.ɵɵtext(21, \"Department\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"select\", 13)(23, \"option\", 14);\n          i0.ɵɵtext(24, \"Select Department\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(25, SubjectFormComponent_option_25_Template, 2, 2, \"option\", 15);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"div\", 9)(27, \"label\", 16);\n          i0.ɵɵtext(28, \"Code (optional)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(29, \"input\", 17);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const _r1 = i0.ɵɵreference(12);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.editingSubjectId ? \"Update Subject\" : \"Add Subject\");\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", ctx.subjectForm.invalid || ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading)(\"ngIfElse\", _r1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"formGroup\", ctx.subjectForm);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngForOf\", ctx.departments);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i6.MatIcon, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName]\n    });\n  }\n  return SubjectFormComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}