import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { UserService } from 'src/app/services/user.service';

@Component({
  selector: 'app-settings',
  templateUrl: './settings.component.html',
  styleUrls: ['./settings.component.css']
})
export class SettingsComponent implements OnInit {
  profileForm: FormGroup;
  passwordForm: FormGroup;
  systemForm: FormGroup;
  
  loading = false;
  currentUser: any;
  
  // Settings tabs
  selectedTab = 0;
  
  // System settings
  systemSettings = {
    instituteName: 'Government Post Graduate College',
    instituteAddress: 'Swabi, KPK, Pakistan',
    contactEmail: '<EMAIL>',
    contactPhone: '+92-938-222222',
    academicYear: '2024-2025',
    currentSemester: 'Fall 2024',
    allowStudentRegistration: true,
    allowTeacherRegistration: false,
    enableNotifications: true,
    enableEmailNotifications: true,
    enableSMSNotifications: false,
    maintenanceMode: false
  };

  constructor(
    private fb: FormBuilder,
    private userService: UserService,
    private snackBar: MatSnackBar
  ) {
    this.profileForm = this.createProfileForm();
    this.passwordForm = this.createPasswordForm();
    this.systemForm = this.createSystemForm();
  }

  ngOnInit(): void {
    this.loadCurrentUser();
    this.loadSystemSettings();
  }

  createProfileForm(): FormGroup {
    return this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      contact: ['', [Validators.required, Validators.pattern(/^[0-9]{10,15}$/)]],
      address: [''],
      qualification: [''],
      experience: [''],
      joiningDate: ['']
    });
  }

  createPasswordForm(): FormGroup {
    return this.fb.group({
      currentPassword: ['', [Validators.required]],
      newPassword: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', [Validators.required]]
    }, { validators: this.passwordMatchValidator });
  }

  createSystemForm(): FormGroup {
    return this.fb.group({
      instituteName: [this.systemSettings.instituteName, Validators.required],
      instituteAddress: [this.systemSettings.instituteAddress, Validators.required],
      contactEmail: [this.systemSettings.contactEmail, [Validators.required, Validators.email]],
      contactPhone: [this.systemSettings.contactPhone, Validators.required],
      academicYear: [this.systemSettings.academicYear, Validators.required],
      currentSemester: [this.systemSettings.currentSemester, Validators.required],
      allowStudentRegistration: [this.systemSettings.allowStudentRegistration],
      allowTeacherRegistration: [this.systemSettings.allowTeacherRegistration],
      enableNotifications: [this.systemSettings.enableNotifications],
      enableEmailNotifications: [this.systemSettings.enableEmailNotifications],
      enableSMSNotifications: [this.systemSettings.enableSMSNotifications],
      maintenanceMode: [this.systemSettings.maintenanceMode]
    });
  }

  passwordMatchValidator(form: FormGroup) {
    const newPassword = form.get('newPassword');
    const confirmPassword = form.get('confirmPassword');
    
    if (newPassword && confirmPassword && newPassword.value !== confirmPassword.value) {
      confirmPassword.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    }
    
    return null;
  }

  loadCurrentUser(): void {
    const userData = this.userService.getUserFromLocalStorage();
    if (userData && userData.user) {
      this.currentUser = userData.user;
      this.profileForm.patchValue({
        name: this.currentUser.name,
        email: this.currentUser.email,
        contact: this.currentUser.contact,
        address: this.currentUser.address,
        qualification: this.currentUser.qualification,
        experience: this.currentUser.experience,
        joiningDate: this.currentUser.joiningDate
      });
    }
  }

  loadSystemSettings(): void {
    // In a real application, you would load these from a backend service
    // For now, we'll use the default values
    this.systemForm.patchValue(this.systemSettings);
  }

  onTabChange(index: any): void {
    this.selectedTab = index;
  }

  updateProfile(): void {
    if (this.profileForm.invalid) {
      this.markFormGroupTouched(this.profileForm);
      this.showError('Please fill in all required fields correctly');
      return;
    }

    this.loading = true;
    const profileData = this.profileForm.value;

    this.userService.updateUserProfile(this.currentUser._id, profileData).subscribe({
      next: (response) => {
        if (response.success) {
          this.showSuccess('Profile updated successfully!');
          // Update local storage
          const userData = this.userService.getUserFromLocalStorage();
          if (userData) {
            userData.user = { ...userData.user, ...profileData };
            localStorage.setItem('user', JSON.stringify(userData));
          }
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error updating profile:', error);
        this.showError('Failed to update profile');
        this.loading = false;
      }
    });
  }

  changePassword(): void {
    if (this.passwordForm.invalid) {
      this.markFormGroupTouched(this.passwordForm);
      this.showError('Please fill in all required fields correctly');
      return;
    }

    this.loading = true;
    const passwordData = {
      currentPassword: this.passwordForm.value.currentPassword,
      newPassword: this.passwordForm.value.newPassword
    };

    this.userService.changePassword(this.currentUser._id, passwordData).subscribe({
      next: (response) => {
        if (response.success) {
          this.showSuccess('Password changed successfully!');
          this.passwordForm.reset();
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error changing password:', error);
        this.showError(error.error?.message || 'Failed to change password');
        this.loading = false;
      }
    });
  }

  updateSystemSettings(): void {
    if (this.systemForm.invalid) {
      this.markFormGroupTouched(this.systemForm);
      this.showError('Please fill in all required fields correctly');
      return;
    }

    this.loading = true;
    const systemData = this.systemForm.value;

    // In a real application, you would save these to a backend service
    // For now, we'll just update the local object and show success
    this.systemSettings = { ...this.systemSettings, ...systemData };
    
    setTimeout(() => {
      this.showSuccess('System settings updated successfully!');
      this.loading = false;
    }, 1000);
  }

  resetSystemSettings(): void {
    this.systemForm.patchValue(this.systemSettings);
    this.showSuccess('Settings reset to saved values');
  }

  exportData(): void {
    this.showSuccess('Data export feature will be implemented soon');
  }

  importData(): void {
    this.showSuccess('Data import feature will be implemented soon');
  }

  backupDatabase(): void {
    this.showSuccess('Database backup feature will be implemented soon');
  }

  // Utility methods
  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      formGroup.get(key)?.markAsTouched();
    });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  // Form getters for validation
  get name() { return this.profileForm.get('name'); }
  get email() { return this.profileForm.get('email'); }
  get contact() { return this.profileForm.get('contact'); }
  get currentPassword() { return this.passwordForm.get('currentPassword'); }
  get newPassword() { return this.passwordForm.get('newPassword'); }
  get confirmPassword() { return this.passwordForm.get('confirmPassword'); }
}
