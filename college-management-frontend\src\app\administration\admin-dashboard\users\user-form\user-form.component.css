.maindiv {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.secondarydiv {
  background: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  max-width: 1200px;
  margin: 0 auto;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #dee2e6;
}

.header-section h2 {
  margin: 0;
  color: #29578c;
  font-weight: 600;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  font-weight: 500;
  color: #495057;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.help-icon {
  font-size: 16px;
  color: #6c757d;
  cursor: help;
  transition: color 0.2s ease;
}

.help-icon:hover {
  color: #29578c;
}

.form-control {
  padding: 10px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
  border-color: #29578c;
  box-shadow: 0 0 0 0.2rem rgba(41, 87, 140, 0.25);
  outline: 0;
}

.form-control.is-invalid {
  border-color: #dc3545;
}

.form-control.is-invalid:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 5px;
  font-size: 12px;
  color: #dc3545;
  padding: 4px 8px;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-5px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Success state for valid fields */
.form-control.ng-valid.ng-touched:not(.ng-pristine) {
  border-color: #28a745;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

/* Enhanced focus states */
.form-control:focus {
  border-color: #29578c;
  box-shadow: 0 0 0 0.2rem rgba(41, 87, 140, 0.25);
}

.input-group {
  display: flex;
}

.input-group .form-control {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.input-group .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: 0;
}

.form-check {
  padding-left: 1.5rem;
}

.form-check-input {
  margin-left: -1.5rem;
  margin-top: 0.25rem;
}

.form-check-label {
  font-weight: 500;
  color: #495057;
}

.form-actions {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #dee2e6;
  display: flex;
  gap: 10px;
}

.btn {
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
}

.btn-primary {
  background-color: #29578c;
  border-color: #29578c;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #1e3f63;
  border-color: #1e3f63;
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-outline-secondary {
  color: #6c757d;
  border-color: #6c757d;
  background-color: transparent;
}

.btn-outline-secondary:hover {
  color: white;
  background-color: #6c757d;
  border-color: #6c757d;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

.spinner-border {
  width: 3rem;
  height: 3rem;
}

.row {
  margin-left: -15px;
  margin-right: -15px;
}

.col-md-6 {
  padding-left: 15px;
  padding-right: 15px;
}

textarea.form-control {
  resize: vertical;
  min-height: 60px;
}

select.form-control {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 8px center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: 40px;
  appearance: none;
}

@media (max-width: 768px) {
  .header-section {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .form-actions .btn {
    width: 100%;
  }
  
  .col-md-6 {
    padding-left: 0;
    padding-right: 0;
  }
  
  .row {
    margin-left: 0;
    margin-right: 0;
  }
}
