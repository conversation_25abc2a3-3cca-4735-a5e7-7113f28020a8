{"ast": null, "code": "import { Chart, registerables } from 'chart.js';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/user.service\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@angular/common\";\nChart.register(...registerables);\nexport class ChartsComponent {\n  constructor(userService) {\n    this.userService = userService;\n    this.profileName = '<PERSON>';\n    this.currentDate = new Date();\n    this.selectedPeriod = 'month';\n    this.charts = [];\n  }\n  ngOnInit() {\n    const user = this.userService.getUserFromLocalStorage().user;\n    this.profileName = user.name;\n    this.createCharts();\n  }\n  ngOnDestroy() {\n    // Destroy all charts to prevent memory leaks\n    this.charts.forEach(chart => chart.destroy());\n  }\n  updateCharts() {\n    // This would be where you'd update chart data based on selected period\n    // For now, we'll just recreate them\n    this.charts.forEach(chart => chart.destroy());\n    this.createCharts();\n  }\n  createCharts() {\n    const chartConfig = {\n      responsive: true,\n      maintainAspectRatio: false,\n      plugins: {\n        legend: {\n          position: 'top',\n          labels: {\n            color: '#515154',\n            font: {\n              family: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif',\n              size: 12\n            }\n          }\n        },\n        tooltip: {\n          backgroundColor: '#11418e',\n          titleFont: {\n            family: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif',\n            size: 12\n          },\n          bodyFont: {\n            family: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif',\n            size: 12\n          },\n          padding: 10,\n          cornerRadius: 8\n        }\n      },\n      scales: {\n        x: {\n          grid: {\n            display: false\n          },\n          ticks: {\n            color: '#515154'\n          }\n        },\n        y: {\n          grid: {\n            color: '#eaeaea'\n          },\n          ticks: {\n            color: '#515154'\n          }\n        }\n      }\n    };\n    // Class 11 Attendance Chart\n    const class11AttendanceChart = new Chart(document.getElementById('class11AttendanceChart'), {\n      type: 'doughnut',\n      data: {\n        labels: ['Present', 'Absent'],\n        datasets: [{\n          data: [170, 30],\n          backgroundColor: ['#11418e', '#515154'],\n          borderWidth: 0\n        }]\n      },\n      options: chartConfig\n    });\n    this.charts.push(class11AttendanceChart);\n    // Class 12 Attendance Chart\n    // const class12AttendanceChart = new Chart(\n    //   document.getElementById('class12AttendanceChart') as HTMLCanvasElement,\n    //   {\n    //     type: 'doughnut',\n    //     data: {\n    //       labels: ['Present', 'Absent'],\n    //       datasets: [{\n    //         data: [160, 20],\n    //         backgroundColor: ['#11418e', '#515154'],\n    //         borderWidth: 0\n    //       }]\n    //     },\n    //     options: chartConfig\n    //   }\n    // );\n    // this.charts.push(class12AttendanceChart);\n    // Class 11 Department Chart\n    const class11DepartmentChart = new Chart(document.getElementById('class11DepartmentChart'), {\n      type: 'bar',\n      data: {\n        labels: ['Pre-Medical', 'Pre-Engineering', 'Computer Science', 'Arts'],\n        datasets: [{\n          data: [50, 60, 30, 60],\n          backgroundColor: ['#11418e', '#3a6ba6', '#5c7fb3', '#7e93c1'],\n          borderWidth: 0,\n          borderRadius: 6\n        }]\n      },\n      options: chartConfig\n    });\n    this.charts.push(class11DepartmentChart);\n    // Class 12 Department Chart\n    // const class12DepartmentChart = new Chart(\n    //   document.getElementById('class12DepartmentChart') as HTMLCanvasElement,\n    //   {\n    //     type: 'bar',\n    //     data: {\n    //       labels: ['Pre-Medical', 'Pre-Engineering', 'Computer Science', 'Arts'],\n    //       datasets: [{\n    //         data: [45, 50, 35, 50],\n    //         backgroundColor: ['#11418e', '#3a6ba6', '#5c7fb3', '#7e93c1'],\n    //         borderWidth: 0,\n    //         borderRadius: 6\n    //       }]\n    //     },\n    //     options: chartConfig\n    //   }\n    // );\n    // this.charts.push(class12DepartmentChart);\n    // Teachers by Department Chart\n    const teachersDepartmentChart = new Chart(document.getElementById('teachersDepartmentChart'), {\n      type: 'bar',\n      data: {\n        labels: ['Pre-Medical', 'Pre-Engineering', 'Computer Science', 'Arts'],\n        datasets: [{\n          data: [8, 10, 6, 6],\n          backgroundColor: ['#11418e', '#3a6ba6', '#5c7fb3', '#7e93c1'],\n          borderWidth: 0,\n          borderRadius: 6\n        }]\n      },\n      options: {\n        ...chartConfig,\n        indexAxis: 'y'\n      }\n    });\n    this.charts.push(teachersDepartmentChart);\n  }\n  static #_ = this.ɵfac = function ChartsComponent_Factory(t) {\n    return new (t || ChartsComponent)(i0.ɵɵdirectiveInject(i1.UserService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ChartsComponent,\n    selectors: [[\"app-charts\"]],\n    decls: 91,\n    vars: 6,\n    consts: [[1, \"dashboard-container\"], [1, \"dashboard-header\"], [1, \"header-content\"], [1, \"subtitle\"], [1, \"last-updated\"], [1, \"badge\"], [1, \"stats-grid\"], [1, \"stat-card\"], [1, \"stat-content\"], [1, \"stat-info\"], [1, \"stat-value\"], [1, \"stat-detail\"], [1, \"stat-icon\"], [1, \"fas\", \"fa-users\"], [1, \"fas\", \"fa-chalkboard-teacher\"], [1, \"fas\", \"fa-check-circle\"], [1, \"fas\", \"fa-exclamation-circle\"], [1, \"charts-section\"], [1, \"chart-card\"], [1, \"chart-header\"], [1, \"chart-controls\"], [1, \"form-select\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"value\", \"month\"], [\"value\", \"week\"], [\"value\", \"today\"], [\"value\", \"year\"], [1, \"chart-body\"], [\"id\", \"class11AttendanceChart\"], [\"id\", \"class11DepartmentChart\"], [1, \"chart-card\", \"full-width\"], [1, \"btn-view-all\"], [\"id\", \"teachersDepartmentChart\"]],\n    template: function ChartsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\")(4, \"h1\");\n        i0.ɵɵtext(5, \"Welcome back, \");\n        i0.ɵɵelementStart(6, \"span\");\n        i0.ɵɵtext(7);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(8, \" \\uD83D\\uDC4B\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"p\", 3);\n        i0.ɵɵtext(10, \"Track and manage college attendance efficiently\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"div\", 4)(12, \"span\", 5);\n        i0.ɵɵtext(13);\n        i0.ɵɵpipe(14, \"date\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(15, \"div\", 6)(16, \"div\", 7)(17, \"div\", 8)(18, \"div\", 9)(19, \"h3\");\n        i0.ɵɵtext(20, \"Total Students\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(21, \"p\", 10);\n        i0.ɵɵtext(22, \"380\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(23, \"p\", 11);\n        i0.ɵɵtext(24, \"Class 11: 200 | Class 12: 180\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(25, \"div\", 12);\n        i0.ɵɵelement(26, \"i\", 13);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(27, \"div\", 7)(28, \"div\", 8)(29, \"div\", 9)(30, \"h3\");\n        i0.ɵɵtext(31, \"Total Teachers\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(32, \"p\", 10);\n        i0.ɵɵtext(33, \"30\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(34, \"p\", 11);\n        i0.ɵɵtext(35, \"5 Departments\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(36, \"div\", 12);\n        i0.ɵɵelement(37, \"i\", 14);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(38, \"div\", 7)(39, \"div\", 8)(40, \"div\", 9)(41, \"h3\");\n        i0.ɵɵtext(42, \"Present Today\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(43, \"p\", 10);\n        i0.ɵɵtext(44, \"330\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(45, \"p\", 11);\n        i0.ɵɵtext(46, \"Class 11: 170 | Class 12: 160\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(47, \"div\", 12);\n        i0.ɵɵelement(48, \"i\", 15);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(49, \"div\", 7)(50, \"div\", 8)(51, \"div\", 9)(52, \"h3\");\n        i0.ɵɵtext(53, \"Absent Today\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(54, \"p\", 10);\n        i0.ɵɵtext(55, \"50\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(56, \"p\", 11);\n        i0.ɵɵtext(57, \"Class 11: 30 | Class 12: 20\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(58, \"div\", 12);\n        i0.ɵɵelement(59, \"i\", 16);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(60, \"div\", 17)(61, \"div\", 18)(62, \"div\", 19)(63, \"h3\");\n        i0.ɵɵtext(64, \"Attendance Overview\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(65, \"div\", 20)(66, \"select\", 21);\n        i0.ɵɵlistener(\"ngModelChange\", function ChartsComponent_Template_select_ngModelChange_66_listener($event) {\n          return ctx.selectedPeriod = $event;\n        })(\"change\", function ChartsComponent_Template_select_change_66_listener() {\n          return ctx.updateCharts();\n        });\n        i0.ɵɵelementStart(67, \"option\", 22);\n        i0.ɵɵtext(68, \"This Month\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(69, \"option\", 23);\n        i0.ɵɵtext(70, \"This Week\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(71, \"option\", 24);\n        i0.ɵɵtext(72, \"Today\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(73, \"option\", 25);\n        i0.ɵɵtext(74, \"This Year\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(75, \"div\", 26);\n        i0.ɵɵelement(76, \"canvas\", 27);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(77, \"div\", 18)(78, \"div\", 19)(79, \"h3\");\n        i0.ɵɵtext(80, \"Department Distribution\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(81, \"div\", 26);\n        i0.ɵɵelement(82, \"canvas\", 28);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(83, \"div\", 29)(84, \"div\", 19)(85, \"h3\");\n        i0.ɵɵtext(86, \"Teachers by Department\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(87, \"button\", 30);\n        i0.ɵɵtext(88, \"View All\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(89, \"div\", 26);\n        i0.ɵɵelement(90, \"canvas\", 31);\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(7);\n        i0.ɵɵtextInterpolate(ctx.profileName);\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate1(\"Last updated: \", i0.ɵɵpipeBind2(14, 3, ctx.currentDate, \"medium\"), \"\");\n        i0.ɵɵadvance(53);\n        i0.ɵɵproperty(\"ngModel\", ctx.selectedPeriod);\n      }\n    },\n    dependencies: [i2.NgSelectOption, i2.ɵNgSelectMultipleOption, i2.SelectControlValueAccessor, i2.NgControlStatus, i2.NgModel, i3.DatePipe],\n    styles: [\".dashboard-container[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  max-width: 100%;\\n  overflow-x: hidden;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 12px;\\n  padding: 1.5rem;\\n  margin-bottom: 1.5rem;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  flex-wrap: wrap;\\n  gap: 1rem;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  color: #11418e;\\n  font-size: 1.8rem;\\n  font-weight: 600;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #515154;\\n  font-weight: 500;\\n}\\n\\n.subtitle[_ngcontent-%COMP%] {\\n  color: #515154;\\n  margin-bottom: 0;\\n  font-size: 0.95rem;\\n}\\n\\n.last-updated[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  background-color: #f0f4f9;\\n  color: #515154;\\n  font-weight: 500;\\n  padding: 0.5rem 1rem;\\n  border-radius: 8px;\\n  font-size: 0.85rem;\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));\\n  gap: 1.5rem;\\n  margin-bottom: 1.5rem;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 12px;\\n  padding: 1.5rem;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\\n}\\n\\n.stat-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);\\n}\\n\\n.stat-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.stat-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #515154;\\n  font-size: 1rem;\\n  font-weight: 500;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.stat-value[_ngcontent-%COMP%] {\\n  color: #11418e;\\n  font-size: 1.8rem;\\n  font-weight: 600;\\n  margin-bottom: 0.25rem;\\n}\\n\\n.stat-detail[_ngcontent-%COMP%] {\\n  color: #515154;\\n  font-size: 0.85rem;\\n  margin-bottom: 0;\\n  opacity: 0.8;\\n}\\n\\n.stat-icon[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  background-color: #f0f4f9;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #11418e;\\n  font-size: 1.2rem;\\n}\\n\\n.charts-section[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));\\n  gap: 1.5rem;\\n}\\n\\n.chart-card[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 12px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\\n  padding: 1.5rem;\\n  height: 400px;\\n  max-height: 400px;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.chart-card.full-width[_ngcontent-%COMP%] {\\n  grid-column: 1 / -1;\\n}\\n\\n.chart-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1.5rem;\\n}\\n\\n.chart-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #11418e;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  margin: 0;\\n}\\n\\n.chart-body[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 300px;\\n  max-height: 300px;\\n  overflow: hidden;\\n  position: relative;\\n}\\n\\n.chart-body[_ngcontent-%COMP%]   canvas[_ngcontent-%COMP%] {\\n  max-height: 100% !important;\\n  max-width: 100% !important;\\n}\\n\\n.chart-controls[_ngcontent-%COMP%]   .form-select[_ngcontent-%COMP%] {\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  padding: 0.4rem 1rem;\\n  font-size: 0.85rem;\\n  color: #515154;\\n  background-color: #f8f9fa;\\n  max-width: 150px;\\n}\\n\\n.btn-view-all[_ngcontent-%COMP%] {\\n  background-color: #f0f4f9;\\n  color: #11418e;\\n  border: none;\\n  padding: 0.5rem 1rem;\\n  border-radius: 8px;\\n  font-size: 0.85rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s;\\n}\\n\\n.btn-view-all[_ngcontent-%COMP%]:hover {\\n  background-color: #e0e8f0;\\n}\\n\\n.chart-body[_ngcontent-%COMP%]   canvas[_ngcontent-%COMP%] {\\n  width: 100% !important;\\n  height: 100% !important;\\n  display: block;\\n}\\n\\n\\n\\n\\n@media (max-width: 1199.98px) {\\n  .charts-section[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  }\\n}\\n\\n@media (max-width: 991.98px) {\\n  .dashboard-container[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  \\n  .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 1fr;\\n  }\\n  \\n  .charts-section[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  \\n  .dashboard-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  \\n  .chart-body[_ngcontent-%COMP%] {\\n    height: 250px;\\n  }\\n}\\n\\n@media (max-width: 767.98px) {\\n  .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  \\n  .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 0.5rem;\\n  }\\n  \\n  .last-updated[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  \\n  .last-updated[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n    width: 100%;\\n    text-align: center;\\n  }\\n  .charts-section[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  \\n}\\n\\n@media (max-width: 575.98px) {\\n  .dashboard-container[_ngcontent-%COMP%] {\\n    padding: 0px;\\n  }\\n  \\n  .dashboard-header[_ngcontent-%COMP%], .stat-card[_ngcontent-%COMP%], .chart-card[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  \\n  .chart-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 0.5rem;\\n  }\\n  \\n  .chart-controls[_ngcontent-%COMP%]   .form-select[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n    width: 100%;\\n  }\\n  .charts-section[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  \\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Chart", "registerables", "register", "ChartsComponent", "constructor", "userService", "profileName", "currentDate", "Date", "<PERSON><PERSON><PERSON><PERSON>", "charts", "ngOnInit", "user", "getUserFromLocalStorage", "name", "createCharts", "ngOnDestroy", "for<PERSON>ach", "chart", "destroy", "updateCharts", "chartConfig", "responsive", "maintainAspectRatio", "plugins", "legend", "position", "labels", "color", "font", "family", "size", "tooltip", "backgroundColor", "titleFont", "bodyFont", "padding", "cornerRadius", "scales", "x", "grid", "display", "ticks", "y", "class11AttendanceChart", "document", "getElementById", "type", "data", "datasets", "borderWidth", "options", "push", "class11DepartmentChart", "borderRadius", "teachersDepartmentChart", "indexAxis", "_", "i0", "ɵɵdirectiveInject", "i1", "UserService", "_2", "selectors", "decls", "vars", "consts", "template", "ChartsComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "ChartsComponent_Template_select_ngModelChange_66_listener", "$event", "ChartsComponent_Template_select_change_66_listener", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "ɵɵproperty"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\charts\\charts.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\charts\\charts.component.html"], "sourcesContent": ["import { Component, OnInit, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';\r\nimport { Chart, ChartType, registerables } from 'chart.js';\r\nimport { UserService } from 'src/app/services/user.service';\r\n\r\nChart.register(...registerables);\r\n\r\n@Component({\r\n  selector: 'app-charts',\r\n  templateUrl: './charts.component.html',\r\n  styleUrls: ['./charts.component.css']\r\n})\r\nexport class ChartsComponent implements OnInit, OnDestroy {\r\n  profileName: string = '<PERSON>';\r\n  currentDate: Date = new Date();\r\n  selectedPeriod: string = 'month';\r\n  charts: any[] = [];\r\n\r\n  constructor(private userService: UserService) { }\r\n\r\n  ngOnInit() {\r\n    const user = this.userService.getUserFromLocalStorage().user;\r\n    this.profileName = user.name;\r\n    this.createCharts();\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    // Destroy all charts to prevent memory leaks\r\n    this.charts.forEach(chart => chart.destroy());\r\n  }\r\n\r\n  updateCharts() {\r\n    // This would be where you'd update chart data based on selected period\r\n    // For now, we'll just recreate them\r\n    this.charts.forEach(chart => chart.destroy());\r\n    this.createCharts();\r\n  }\r\n\r\n  createCharts() {\r\n    const chartConfig = {\r\n      responsive: true,\r\n      maintainAspectRatio: false,\r\n      plugins: {\r\n        legend: {\r\n          position: 'top' as const,\r\n          labels: {\r\n            color: '#515154',\r\n            font: {\r\n              family: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif',\r\n              size: 12\r\n            }\r\n          }\r\n        },\r\n        tooltip: {\r\n          backgroundColor: '#11418e',\r\n          titleFont: {\r\n            family: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif',\r\n            size: 12\r\n          },\r\n          bodyFont: {\r\n            family: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif',\r\n            size: 12\r\n          },\r\n          padding: 10,\r\n          cornerRadius: 8\r\n        }\r\n      },\r\n      scales: {\r\n        x: {\r\n          grid: {\r\n            display: false\r\n          },\r\n          ticks: {\r\n            color: '#515154'\r\n          }\r\n        },\r\n        y: {\r\n          grid: {\r\n            color: '#eaeaea'\r\n          },\r\n          ticks: {\r\n            color: '#515154'\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    // Class 11 Attendance Chart\r\n    const class11AttendanceChart: Chart<'doughnut', number[], string> = new Chart(\r\n      document.getElementById('class11AttendanceChart') as HTMLCanvasElement,\r\n      {\r\n        type: 'doughnut',\r\n        data: {\r\n          labels: ['Present', 'Absent'],\r\n          datasets: [{\r\n            data: [170, 30],\r\n            backgroundColor: ['#11418e', '#515154'],\r\n            borderWidth: 0\r\n          }]\r\n        },\r\n        options: chartConfig\r\n      }\r\n    );\r\n    this.charts.push(class11AttendanceChart);\r\n\r\n    // Class 12 Attendance Chart\r\n    // const class12AttendanceChart = new Chart(\r\n    //   document.getElementById('class12AttendanceChart') as HTMLCanvasElement,\r\n    //   {\r\n    //     type: 'doughnut',\r\n    //     data: {\r\n    //       labels: ['Present', 'Absent'],\r\n    //       datasets: [{\r\n    //         data: [160, 20],\r\n    //         backgroundColor: ['#11418e', '#515154'],\r\n    //         borderWidth: 0\r\n    //       }]\r\n    //     },\r\n    //     options: chartConfig\r\n    //   }\r\n    // );\r\n    // this.charts.push(class12AttendanceChart);\r\n\r\n    // Class 11 Department Chart\r\n    const class11DepartmentChart = new Chart(\r\n      document.getElementById('class11DepartmentChart') as HTMLCanvasElement,\r\n      {\r\n        type: 'bar',\r\n        data: {\r\n          labels: ['Pre-Medical', 'Pre-Engineering', 'Computer Science', 'Arts'],\r\n          datasets: [{\r\n            data: [50, 60, 30, 60],\r\n            backgroundColor: ['#11418e', '#3a6ba6', '#5c7fb3', '#7e93c1'],\r\n            borderWidth: 0,\r\n            borderRadius: 6\r\n          }]\r\n        },\r\n        options: chartConfig\r\n      }\r\n    );\r\n    this.charts.push(class11DepartmentChart);\r\n\r\n    // Class 12 Department Chart\r\n    // const class12DepartmentChart = new Chart(\r\n    //   document.getElementById('class12DepartmentChart') as HTMLCanvasElement,\r\n    //   {\r\n    //     type: 'bar',\r\n    //     data: {\r\n    //       labels: ['Pre-Medical', 'Pre-Engineering', 'Computer Science', 'Arts'],\r\n    //       datasets: [{\r\n    //         data: [45, 50, 35, 50],\r\n    //         backgroundColor: ['#11418e', '#3a6ba6', '#5c7fb3', '#7e93c1'],\r\n    //         borderWidth: 0,\r\n    //         borderRadius: 6\r\n    //       }]\r\n    //     },\r\n    //     options: chartConfig\r\n    //   }\r\n    // );\r\n    // this.charts.push(class12DepartmentChart);\r\n\r\n    // Teachers by Department Chart\r\n    const teachersDepartmentChart = new Chart(\r\n      document.getElementById('teachersDepartmentChart') as HTMLCanvasElement,\r\n      {\r\n        type: 'bar',\r\n        data: {\r\n          labels: ['Pre-Medical', 'Pre-Engineering', 'Computer Science', 'Arts'],\r\n          datasets: [{\r\n            data: [8, 10, 6, 6],\r\n            backgroundColor: ['#11418e', '#3a6ba6', '#5c7fb3', '#7e93c1'],\r\n            borderWidth: 0,\r\n            borderRadius: 6\r\n          }]\r\n        },\r\n        options: {\r\n          ...chartConfig,\r\n          indexAxis: 'y'\r\n        }\r\n      }\r\n    );\r\n    this.charts.push(teachersDepartmentChart);\r\n  }\r\n}", "<div class=\"dashboard-container\">\r\n  <!-- Dashboard Header -->\r\n  <div class=\"dashboard-header\">\r\n    <div class=\"header-content\">\r\n      <div>\r\n        <h1>Welcome back, <span>{{profileName}}</span> 👋</h1>\r\n        <p class=\"subtitle\">Track and manage college attendance efficiently</p>\r\n      </div>\r\n      <div class=\"last-updated\">\r\n        <span class=\"badge\">Last updated: {{currentDate | date:'medium'}}</span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Stats Cards -->\r\n  <div class=\"stats-grid\">\r\n    <div class=\"stat-card\">\r\n      <div class=\"stat-content\">\r\n        <div class=\"stat-info\">\r\n          <h3>Total Students</h3>\r\n          <p class=\"stat-value\">380</p>\r\n          <p class=\"stat-detail\">Class 11: 200 | Class 12: 180</p>\r\n        </div>\r\n        <div class=\"stat-icon\">\r\n          <i class=\"fas fa-users\"></i>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"stat-card\">\r\n      <div class=\"stat-content\">\r\n        <div class=\"stat-info\">\r\n          <h3>Total Teachers</h3>\r\n          <p class=\"stat-value\">30</p>\r\n          <p class=\"stat-detail\">5 Departments</p>\r\n        </div>\r\n        <div class=\"stat-icon\">\r\n          <i class=\"fas fa-chalkboard-teacher\"></i>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"stat-card\">\r\n      <div class=\"stat-content\">\r\n        <div class=\"stat-info\">\r\n          <h3>Present Today</h3>\r\n          <p class=\"stat-value\">330</p>\r\n          <p class=\"stat-detail\">Class 11: 170 | Class 12: 160</p>\r\n        </div>\r\n        <div class=\"stat-icon\">\r\n          <i class=\"fas fa-check-circle\"></i>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"stat-card\">\r\n      <div class=\"stat-content\">\r\n        <div class=\"stat-info\">\r\n          <h3>Absent Today</h3>\r\n          <p class=\"stat-value\">50</p>\r\n          <p class=\"stat-detail\">Class 11: 30 | Class 12: 20</p>\r\n        </div>\r\n        <div class=\"stat-icon\">\r\n          <i class=\"fas fa-exclamation-circle\"></i>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Charts Section -->\r\n  <div class=\"charts-section\">\r\n    <div class=\"chart-card\">\r\n      <div class=\"chart-header\">\r\n        <h3>Attendance Overview</h3>\r\n        <div class=\"chart-controls\">\r\n          <select class=\"form-select\" [(ngModel)]=\"selectedPeriod\" (change)=\"updateCharts()\">\r\n            <option value=\"month\">This Month</option>\r\n            <option value=\"week\">This Week</option>\r\n            <option value=\"today\">Today</option>\r\n            <option value=\"year\">This Year</option>\r\n          </select>\r\n        </div>\r\n      </div>\r\n      <div class=\"chart-body\">\r\n        <canvas id=\"class11AttendanceChart\"></canvas>\r\n      </div>\r\n    </div>\r\n    <div class=\"chart-card\">\r\n      <div class=\"chart-header\">\r\n        <h3>Department Distribution</h3>\r\n      </div>\r\n      <div class=\"chart-body\">\r\n        <canvas id=\"class11DepartmentChart\"></canvas>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"chart-card full-width\">\r\n      <div class=\"chart-header\">\r\n        <h3>Teachers by Department</h3>\r\n        <button class=\"btn-view-all\">View All</button>\r\n      </div>\r\n      <div class=\"chart-body\">\r\n        <canvas id=\"teachersDepartmentChart\"></canvas>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>"], "mappings": "AACA,SAASA,KAAK,EAAaC,aAAa,QAAQ,UAAU;;;;;AAG1DD,KAAK,CAACE,QAAQ,CAAC,GAAGD,aAAa,CAAC;AAOhC,OAAM,MAAOE,eAAe;EAM1BC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAL/B,KAAAC,WAAW,GAAW,iBAAiB;IACvC,KAAAC,WAAW,GAAS,IAAIC,IAAI,EAAE;IAC9B,KAAAC,cAAc,GAAW,OAAO;IAChC,KAAAC,MAAM,GAAU,EAAE;EAE8B;EAEhDC,QAAQA,CAAA;IACN,MAAMC,IAAI,GAAG,IAAI,CAACP,WAAW,CAACQ,uBAAuB,EAAE,CAACD,IAAI;IAC5D,IAAI,CAACN,WAAW,GAAGM,IAAI,CAACE,IAAI;IAC5B,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,CAACN,MAAM,CAACO,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,OAAO,EAAE,CAAC;EAC/C;EAEAC,YAAYA,CAAA;IACV;IACA;IACA,IAAI,CAACV,MAAM,CAACO,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,OAAO,EAAE,CAAC;IAC7C,IAAI,CAACJ,YAAY,EAAE;EACrB;EAEAA,YAAYA,CAAA;IACV,MAAMM,WAAW,GAAG;MAClBC,UAAU,EAAE,IAAI;MAChBC,mBAAmB,EAAE,KAAK;MAC1BC,OAAO,EAAE;QACPC,MAAM,EAAE;UACNC,QAAQ,EAAE,KAAc;UACxBC,MAAM,EAAE;YACNC,KAAK,EAAE,SAAS;YAChBC,IAAI,EAAE;cACJC,MAAM,EAAE,+CAA+C;cACvDC,IAAI,EAAE;;;SAGX;QACDC,OAAO,EAAE;UACPC,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAE;YACTJ,MAAM,EAAE,+CAA+C;YACvDC,IAAI,EAAE;WACP;UACDI,QAAQ,EAAE;YACRL,MAAM,EAAE,+CAA+C;YACvDC,IAAI,EAAE;WACP;UACDK,OAAO,EAAE,EAAE;UACXC,YAAY,EAAE;;OAEjB;MACDC,MAAM,EAAE;QACNC,CAAC,EAAE;UACDC,IAAI,EAAE;YACJC,OAAO,EAAE;WACV;UACDC,KAAK,EAAE;YACLd,KAAK,EAAE;;SAEV;QACDe,CAAC,EAAE;UACDH,IAAI,EAAE;YACJZ,KAAK,EAAE;WACR;UACDc,KAAK,EAAE;YACLd,KAAK,EAAE;;;;KAId;IAED;IACA,MAAMgB,sBAAsB,GAAwC,IAAI5C,KAAK,CAC3E6C,QAAQ,CAACC,cAAc,CAAC,wBAAwB,CAAsB,EACtE;MACEC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE;QACJrB,MAAM,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC;QAC7BsB,QAAQ,EAAE,CAAC;UACTD,IAAI,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC;UACff,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;UACvCiB,WAAW,EAAE;SACd;OACF;MACDC,OAAO,EAAE9B;KACV,CACF;IACD,IAAI,CAACX,MAAM,CAAC0C,IAAI,CAACR,sBAAsB,CAAC;IAExC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA,MAAMS,sBAAsB,GAAG,IAAIrD,KAAK,CACtC6C,QAAQ,CAACC,cAAc,CAAC,wBAAwB,CAAsB,EACtE;MACEC,IAAI,EAAE,KAAK;MACXC,IAAI,EAAE;QACJrB,MAAM,EAAE,CAAC,aAAa,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,MAAM,CAAC;QACtEsB,QAAQ,EAAE,CAAC;UACTD,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;UACtBf,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;UAC7DiB,WAAW,EAAE,CAAC;UACdI,YAAY,EAAE;SACf;OACF;MACDH,OAAO,EAAE9B;KACV,CACF;IACD,IAAI,CAACX,MAAM,CAAC0C,IAAI,CAACC,sBAAsB,CAAC;IAExC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA,MAAME,uBAAuB,GAAG,IAAIvD,KAAK,CACvC6C,QAAQ,CAACC,cAAc,CAAC,yBAAyB,CAAsB,EACvE;MACEC,IAAI,EAAE,KAAK;MACXC,IAAI,EAAE;QACJrB,MAAM,EAAE,CAAC,aAAa,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,MAAM,CAAC;QACtEsB,QAAQ,EAAE,CAAC;UACTD,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;UACnBf,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;UAC7DiB,WAAW,EAAE,CAAC;UACdI,YAAY,EAAE;SACf;OACF;MACDH,OAAO,EAAE;QACP,GAAG9B,WAAW;QACdmC,SAAS,EAAE;;KAEd,CACF;IACD,IAAI,CAAC9C,MAAM,CAAC0C,IAAI,CAACG,uBAAuB,CAAC;EAC3C;EAAC,QAAAE,CAAA,G;qBA1KUtD,eAAe,EAAAuD,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAf3D,eAAe;IAAA4D,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCX5BX,EAAA,CAAAa,cAAA,aAAiC;QAKrBb,EAAA,CAAAc,MAAA,qBAAc;QAAAd,EAAA,CAAAa,cAAA,WAAM;QAAAb,EAAA,CAAAc,MAAA,GAAe;QAAAd,EAAA,CAAAe,YAAA,EAAO;QAACf,EAAA,CAAAc,MAAA,oBAAE;QAAAd,EAAA,CAAAe,YAAA,EAAK;QACtDf,EAAA,CAAAa,cAAA,WAAoB;QAAAb,EAAA,CAAAc,MAAA,uDAA+C;QAAAd,EAAA,CAAAe,YAAA,EAAI;QAEzEf,EAAA,CAAAa,cAAA,cAA0B;QACJb,EAAA,CAAAc,MAAA,IAA6C;;QAAAd,EAAA,CAAAe,YAAA,EAAO;QAM9Ef,EAAA,CAAAa,cAAA,cAAwB;QAIZb,EAAA,CAAAc,MAAA,sBAAc;QAAAd,EAAA,CAAAe,YAAA,EAAK;QACvBf,EAAA,CAAAa,cAAA,aAAsB;QAAAb,EAAA,CAAAc,MAAA,WAAG;QAAAd,EAAA,CAAAe,YAAA,EAAI;QAC7Bf,EAAA,CAAAa,cAAA,aAAuB;QAAAb,EAAA,CAAAc,MAAA,qCAA6B;QAAAd,EAAA,CAAAe,YAAA,EAAI;QAE1Df,EAAA,CAAAa,cAAA,eAAuB;QACrBb,EAAA,CAAAgB,SAAA,aAA4B;QAC9BhB,EAAA,CAAAe,YAAA,EAAM;QAIVf,EAAA,CAAAa,cAAA,cAAuB;QAGbb,EAAA,CAAAc,MAAA,sBAAc;QAAAd,EAAA,CAAAe,YAAA,EAAK;QACvBf,EAAA,CAAAa,cAAA,aAAsB;QAAAb,EAAA,CAAAc,MAAA,UAAE;QAAAd,EAAA,CAAAe,YAAA,EAAI;QAC5Bf,EAAA,CAAAa,cAAA,aAAuB;QAAAb,EAAA,CAAAc,MAAA,qBAAa;QAAAd,EAAA,CAAAe,YAAA,EAAI;QAE1Cf,EAAA,CAAAa,cAAA,eAAuB;QACrBb,EAAA,CAAAgB,SAAA,aAAyC;QAC3ChB,EAAA,CAAAe,YAAA,EAAM;QAIVf,EAAA,CAAAa,cAAA,cAAuB;QAGbb,EAAA,CAAAc,MAAA,qBAAa;QAAAd,EAAA,CAAAe,YAAA,EAAK;QACtBf,EAAA,CAAAa,cAAA,aAAsB;QAAAb,EAAA,CAAAc,MAAA,WAAG;QAAAd,EAAA,CAAAe,YAAA,EAAI;QAC7Bf,EAAA,CAAAa,cAAA,aAAuB;QAAAb,EAAA,CAAAc,MAAA,qCAA6B;QAAAd,EAAA,CAAAe,YAAA,EAAI;QAE1Df,EAAA,CAAAa,cAAA,eAAuB;QACrBb,EAAA,CAAAgB,SAAA,aAAmC;QACrChB,EAAA,CAAAe,YAAA,EAAM;QAIVf,EAAA,CAAAa,cAAA,cAAuB;QAGbb,EAAA,CAAAc,MAAA,oBAAY;QAAAd,EAAA,CAAAe,YAAA,EAAK;QACrBf,EAAA,CAAAa,cAAA,aAAsB;QAAAb,EAAA,CAAAc,MAAA,UAAE;QAAAd,EAAA,CAAAe,YAAA,EAAI;QAC5Bf,EAAA,CAAAa,cAAA,aAAuB;QAAAb,EAAA,CAAAc,MAAA,mCAA2B;QAAAd,EAAA,CAAAe,YAAA,EAAI;QAExDf,EAAA,CAAAa,cAAA,eAAuB;QACrBb,EAAA,CAAAgB,SAAA,aAAyC;QAC3ChB,EAAA,CAAAe,YAAA,EAAM;QAMZf,EAAA,CAAAa,cAAA,eAA4B;QAGlBb,EAAA,CAAAc,MAAA,2BAAmB;QAAAd,EAAA,CAAAe,YAAA,EAAK;QAC5Bf,EAAA,CAAAa,cAAA,eAA4B;QACEb,EAAA,CAAAiB,UAAA,2BAAAC,0DAAAC,MAAA;UAAA,OAAAP,GAAA,CAAA7D,cAAA,GAAAoE,MAAA;QAAA,EAA4B,oBAAAC,mDAAA;UAAA,OAAWR,GAAA,CAAAlD,YAAA,EAAc;QAAA,EAAzB;QACtDsC,EAAA,CAAAa,cAAA,kBAAsB;QAAAb,EAAA,CAAAc,MAAA,kBAAU;QAAAd,EAAA,CAAAe,YAAA,EAAS;QACzCf,EAAA,CAAAa,cAAA,kBAAqB;QAAAb,EAAA,CAAAc,MAAA,iBAAS;QAAAd,EAAA,CAAAe,YAAA,EAAS;QACvCf,EAAA,CAAAa,cAAA,kBAAsB;QAAAb,EAAA,CAAAc,MAAA,aAAK;QAAAd,EAAA,CAAAe,YAAA,EAAS;QACpCf,EAAA,CAAAa,cAAA,kBAAqB;QAAAb,EAAA,CAAAc,MAAA,iBAAS;QAAAd,EAAA,CAAAe,YAAA,EAAS;QAI7Cf,EAAA,CAAAa,cAAA,eAAwB;QACtBb,EAAA,CAAAgB,SAAA,kBAA6C;QAC/ChB,EAAA,CAAAe,YAAA,EAAM;QAERf,EAAA,CAAAa,cAAA,eAAwB;QAEhBb,EAAA,CAAAc,MAAA,+BAAuB;QAAAd,EAAA,CAAAe,YAAA,EAAK;QAElCf,EAAA,CAAAa,cAAA,eAAwB;QACtBb,EAAA,CAAAgB,SAAA,kBAA6C;QAC/ChB,EAAA,CAAAe,YAAA,EAAM;QAGRf,EAAA,CAAAa,cAAA,eAAmC;QAE3Bb,EAAA,CAAAc,MAAA,8BAAsB;QAAAd,EAAA,CAAAe,YAAA,EAAK;QAC/Bf,EAAA,CAAAa,cAAA,kBAA6B;QAAAb,EAAA,CAAAc,MAAA,gBAAQ;QAAAd,EAAA,CAAAe,YAAA,EAAS;QAEhDf,EAAA,CAAAa,cAAA,eAAwB;QACtBb,EAAA,CAAAgB,SAAA,kBAA8C;QAChDhB,EAAA,CAAAe,YAAA,EAAM;;;QAlGoBf,EAAA,CAAAqB,SAAA,GAAe;QAAfrB,EAAA,CAAAsB,iBAAA,CAAAV,GAAA,CAAAhE,WAAA,CAAe;QAInBoD,EAAA,CAAAqB,SAAA,GAA6C;QAA7CrB,EAAA,CAAAuB,kBAAA,mBAAAvB,EAAA,CAAAwB,WAAA,QAAAZ,GAAA,CAAA/D,WAAA,gBAA6C;QAkEnCmD,EAAA,CAAAqB,SAAA,IAA4B;QAA5BrB,EAAA,CAAAyB,UAAA,YAAAb,GAAA,CAAA7D,cAAA,CAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}