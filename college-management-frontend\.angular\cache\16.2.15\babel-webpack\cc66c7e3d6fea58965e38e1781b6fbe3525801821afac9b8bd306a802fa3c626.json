{"ast": null, "code": "import Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../services/notice.service\";\nimport * as i3 from \"../../../services/user.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/checkbox\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/forms\";\nfunction StudentNoticeComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"span\", 8);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 9);\n    i0.ɵɵtext(5, \"Loading notices...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StudentNoticeComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"mat-icon\", 10);\n    i0.ɵɵtext(2, \"notifications_none\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\", 11);\n    i0.ɵɵtext(4, \"No Notices Available\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 12);\n    i0.ɵɵtext(6, \"There are no notices to display at this time.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StudentNoticeComponent_div_10_tr_26_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"mat-icon\", 39);\n    i0.ɵɵtext(2, \"push_pin\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Pinned \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StudentNoticeComponent_div_10_tr_26_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const notice_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" Expires: \", i0.ɵɵpipeBind2(2, 1, notice_r4.expiryDate, \"short\"), \" \");\n  }\n}\nfunction StudentNoticeComponent_div_10_tr_26_mat_icon_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 41);\n    i0.ɵɵtext(1, \" check_circle \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StudentNoticeComponent_div_10_tr_26_button_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function StudentNoticeComponent_div_10_tr_26_button_34_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const notice_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.markAsRead(notice_r4));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"done\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StudentNoticeComponent_div_10_tr_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"mat-checkbox\", 29);\n    i0.ɵɵlistener(\"change\", function StudentNoticeComponent_div_10_tr_26_Template_mat_checkbox_change_2_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r14);\n      const notice_r4 = restoredCtx.$implicit;\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r13.toggleNoticeSelection(notice_r4._id));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\")(4, \"div\", 30)(5, \"mat-icon\", 31);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\")(8, \"strong\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, StudentNoticeComponent_div_10_tr_26_div_10_Template, 4, 0, \"div\", 32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"td\")(12, \"span\", 33);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"td\")(15, \"span\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"td\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\")(20, \"div\")(21, \"small\");\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, StudentNoticeComponent_div_10_tr_26_div_24_Template, 3, 4, \"div\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"td\")(26, \"div\", 30);\n    i0.ɵɵtemplate(27, StudentNoticeComponent_div_10_tr_26_mat_icon_27_Template, 2, 0, \"mat-icon\", 35);\n    i0.ɵɵelementStart(28, \"span\");\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"td\")(31, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function StudentNoticeComponent_div_10_tr_26_Template_button_click_31_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r14);\n      const notice_r4 = restoredCtx.$implicit;\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.viewNoticeDetails(notice_r4));\n    });\n    i0.ɵɵelementStart(32, \"mat-icon\");\n    i0.ɵɵtext(33, \"visibility\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(34, StudentNoticeComponent_div_10_tr_26_button_34_Template, 3, 0, \"button\", 37);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const notice_r4 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"table-warning\", notice_r4.isPinned)(\"table-success\", ctx_r3.isNoticeRead(notice_r4));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r3.selectedNotices.has(notice_r4._id));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.getCategoryIcon(notice_r4.category));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"text-muted\", ctx_r3.isNoticeRead(notice_r4));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(notice_r4.title);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", notice_r4.isPinned);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(notice_r4.category);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMapInterpolate1(\"badge bg-\", ctx_r3.getPriorityColor(notice_r4.priority), \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", notice_r4.priority, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(notice_r4.author || \"Unknown\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(23, 25, notice_r4.publishDate, \"short\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", notice_r4.expiryDate);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isNoticeRead(notice_r4));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"text-success\", ctx_r3.isNoticeRead(notice_r4))(\"text-warning\", !ctx_r3.isNoticeRead(notice_r4));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.isNoticeRead(notice_r4) ? \"Read\" : \"Unread\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.isNoticeRead(notice_r4));\n  }\n}\nfunction StudentNoticeComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 13)(2, \"div\", 14)(3, \"table\", 15)(4, \"thead\", 16)(5, \"tr\")(6, \"th\", 17)(7, \"mat-checkbox\", 18);\n    i0.ɵɵlistener(\"ngModelChange\", function StudentNoticeComponent_div_10_Template_mat_checkbox_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.isChecked = $event);\n    })(\"change\", function StudentNoticeComponent_div_10_Template_mat_checkbox_change_7_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.toggleCheckbox());\n    });\n    i0.ɵɵtext(8, \" Select All \");\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"arrow_downward\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"th\");\n    i0.ɵɵtext(12, \"Title\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\");\n    i0.ɵɵtext(14, \"Category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"th\");\n    i0.ɵɵtext(16, \"Priority\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"th\");\n    i0.ɵɵtext(18, \"Author\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"th\");\n    i0.ɵɵtext(20, \"Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"th\");\n    i0.ɵɵtext(22, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"th\");\n    i0.ɵɵtext(24, \"Actions\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"tbody\");\n    i0.ɵɵtemplate(26, StudentNoticeComponent_div_10_tr_26_Template, 35, 28, \"tr\", 19);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(27, \"div\", 20)(28, \"div\", 21)(29, \"div\", 22)(30, \"div\", 23)(31, \"h5\", 24);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"p\", 25);\n    i0.ɵɵtext(34, \"Total Notices\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(35, \"div\", 21)(36, \"div\", 22)(37, \"div\", 23)(38, \"h5\", 26);\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"p\", 25);\n    i0.ɵɵtext(41, \"Unread\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(42, \"div\", 21)(43, \"div\", 22)(44, \"div\", 23)(45, \"h5\", 27);\n    i0.ɵɵtext(46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"p\", 25);\n    i0.ɵɵtext(48, \"Read\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(49, \"div\", 21)(50, \"div\", 22)(51, \"div\", 23)(52, \"h5\", 28);\n    i0.ɵɵtext(53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"p\", 25);\n    i0.ɵɵtext(55, \"Pinned\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.isChecked)(\"indeterminate\", ctx_r2.isIndeterminate);\n    i0.ɵɵadvance(19);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.notices);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.notices.length);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.unreadNoticesCount, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.readNoticesCount, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.pinnedNoticesCount, \" \");\n  }\n}\nexport class StudentNoticeComponent {\n  constructor(router, noticeService, userService) {\n    this.router = router;\n    this.noticeService = noticeService;\n    this.userService = userService;\n    this.isChecked = false;\n    this.isIndeterminate = true;\n    this.studentstable = false;\n    this.notices = [];\n    this.loading = false;\n    this.selectedNotices = new Set();\n  }\n  ngOnInit() {\n    this.currentUser = this.userService.getUserFromLocalStorage()?.user;\n    this.loadNotices();\n  }\n  loadNotices() {\n    if (!this.currentUser) return;\n    this.loading = true;\n    this.noticeService.getNoticesForUser(this.currentUser._id).subscribe({\n      next: response => {\n        if (response.success && response.notices) {\n          this.notices = response.notices;\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading notices:', error);\n        Swal.fire({\n          icon: 'error',\n          title: 'Error',\n          text: 'Failed to load notices. Please try again.'\n        });\n        this.loading = false;\n      }\n    });\n  }\n  toggleCheckbox() {\n    if (this.isIndeterminate) {\n      this.isIndeterminate = false;\n      this.isChecked = true;\n      // Select all notices\n      this.notices.forEach(notice => {\n        if (notice._id) this.selectedNotices.add(notice._id);\n      });\n    } else if (this.isChecked) {\n      this.isChecked = false;\n      // Deselect all notices\n      this.selectedNotices.clear();\n    } else {\n      this.isIndeterminate = true;\n      // Select some notices (partial selection)\n      this.selectedNotices.clear();\n    }\n  }\n  toggleNoticeSelection(noticeId) {\n    if (this.selectedNotices.has(noticeId)) {\n      this.selectedNotices.delete(noticeId);\n    } else {\n      this.selectedNotices.add(noticeId);\n    }\n    this.updateCheckboxState();\n  }\n  updateCheckboxState() {\n    const totalNotices = this.notices.length;\n    const selectedCount = this.selectedNotices.size;\n    if (selectedCount === 0) {\n      this.isChecked = false;\n      this.isIndeterminate = false;\n    } else if (selectedCount === totalNotices) {\n      this.isChecked = true;\n      this.isIndeterminate = false;\n    } else {\n      this.isChecked = false;\n      this.isIndeterminate = true;\n    }\n  }\n  markAsRead(notice) {\n    if (!notice._id || !this.currentUser) return;\n    this.noticeService.markAsRead(notice._id, this.currentUser._id).subscribe({\n      next: response => {\n        if (response.success) {\n          // Update local notice data\n          const noticeIndex = this.notices.findIndex(n => n._id === notice._id);\n          if (noticeIndex !== -1) {\n            if (!this.notices[noticeIndex].readBy) {\n              this.notices[noticeIndex].readBy = [];\n            }\n            this.notices[noticeIndex].readBy.push({\n              user: this.currentUser._id,\n              readAt: new Date()\n            });\n          }\n        }\n      },\n      error: error => {\n        console.error('Error marking notice as read:', error);\n      }\n    });\n  }\n  isNoticeRead(notice) {\n    if (!notice.readBy || !this.currentUser) return false;\n    return notice.readBy.some(read => read.user === this.currentUser._id);\n  }\n  getPriorityColor(priority) {\n    switch (priority) {\n      case 'Low':\n        return 'success';\n      case 'Medium':\n        return 'warning';\n      case 'High':\n        return 'danger';\n      case 'Urgent':\n        return 'dark';\n      default:\n        return 'secondary';\n    }\n  }\n  getCategoryIcon(category) {\n    switch (category) {\n      case 'Academic':\n        return 'school';\n      case 'Administrative':\n        return 'admin_panel_settings';\n      case 'Event':\n        return 'event';\n      case 'Holiday':\n        return 'celebration';\n      case 'Examination':\n        return 'quiz';\n      case 'Emergency':\n        return 'warning';\n      default:\n        return 'info';\n    }\n  }\n  viewNoticeDetails(notice) {\n    if (!this.isNoticeRead(notice)) {\n      this.markAsRead(notice);\n    }\n    Swal.fire({\n      title: notice.title,\n      html: `\n        <div class=\"text-start\">\n          <div class=\"mb-3\">\n            <span class=\"badge bg-${this.getPriorityColor(notice.priority)} me-2\">${notice.priority}</span>\n            <span class=\"badge bg-info\">${notice.category}</span>\n          </div>\n          <div class=\"mb-3\">\n            <strong>Author:</strong> ${notice.author || 'Unknown'}\n          </div>\n          <div class=\"mb-3\">\n            <strong>Published:</strong> ${new Date(notice.publishDate).toLocaleDateString()}\n          </div>\n          ${notice.expiryDate ? `<div class=\"mb-3\"><strong>Expires:</strong> ${new Date(notice.expiryDate).toLocaleDateString()}</div>` : ''}\n          <div class=\"content\">\n            ${notice.content}\n          </div>\n        </div>\n      `,\n      width: '600px',\n      showCloseButton: true,\n      confirmButtonText: 'Close'\n    });\n  }\n  refreshNotices() {\n    this.loadNotices();\n  }\n  get unreadNoticesCount() {\n    return this.notices.filter(notice => !this.isNoticeRead(notice)).length;\n  }\n  get readNoticesCount() {\n    return this.notices.filter(notice => this.isNoticeRead(notice)).length;\n  }\n  get pinnedNoticesCount() {\n    return this.notices.filter(notice => notice.isPinned).length;\n  }\n  static #_ = this.ɵfac = function StudentNoticeComponent_Factory(t) {\n    return new (t || StudentNoticeComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.NoticeService), i0.ɵɵdirectiveInject(i3.UserService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: StudentNoticeComponent,\n    selectors: [[\"app-student-notice\"]],\n    decls: 11,\n    vars: 3,\n    consts: [[1, \"container-fluid\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [1, \"fw-bold\"], [1, \"btn\", \"btn-outline-primary\", 3, \"click\"], [\"class\", \"text-center py-5\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"text-center\", \"py-5\"], [\"role\", \"status\", 1, \"spinner-border\"], [1, \"visually-hidden\"], [1, \"mt-2\"], [1, \"text-muted\", 2, \"font-size\", \"64px\"], [1, \"text-muted\", \"mt-3\"], [1, \"text-muted\"], [1, \"table-container\", \"mb-4\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\"], [1, \"table-light\"], [1, \"one\"], [\"color\", \"primary\", 3, \"ngModel\", \"indeterminate\", \"ngModelChange\", \"change\"], [3, \"table-warning\", \"table-success\", 4, \"ngFor\", \"ngForOf\"], [1, \"row\", \"mt-4\"], [1, \"col-md-3\"], [1, \"card\", \"text-center\"], [1, \"card-body\"], [1, \"card-title\"], [1, \"card-text\", \"text-muted\"], [1, \"card-title\", \"text-warning\"], [1, \"card-title\", \"text-success\"], [1, \"card-title\", \"text-info\"], [\"color\", \"primary\", 3, \"checked\", \"change\"], [1, \"d-flex\", \"align-items-center\"], [1, \"me-2\", \"text-primary\"], [\"class\", \"small text-warning\", 4, \"ngIf\"], [1, \"badge\", \"bg-info\"], [\"class\", \"small text-muted\", 4, \"ngIf\"], [\"class\", \"text-success small-icon me-1\", 4, \"ngIf\"], [\"title\", \"View Details\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [\"class\", \"btn btn-sm btn-outline-success ms-1\", \"title\", \"Mark as Read\", 3, \"click\", 4, \"ngIf\"], [1, \"small\", \"text-warning\"], [1, \"small-icon\"], [1, \"small\", \"text-muted\"], [1, \"text-success\", \"small-icon\", \"me-1\"], [\"title\", \"Mark as Read\", 1, \"btn\", \"btn-sm\", \"btn-outline-success\", \"ms-1\", 3, \"click\"]],\n    template: function StudentNoticeComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\", 2);\n        i0.ɵɵtext(3, \"Notices\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"button\", 3);\n        i0.ɵɵlistener(\"click\", function StudentNoticeComponent_Template_button_click_4_listener() {\n          return ctx.refreshNotices();\n        });\n        i0.ɵɵelementStart(5, \"mat-icon\");\n        i0.ɵɵtext(6, \"refresh\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(7, \" Refresh \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(8, StudentNoticeComponent_div_8_Template, 6, 0, \"div\", 4);\n        i0.ɵɵtemplate(9, StudentNoticeComponent_div_9_Template, 7, 0, \"div\", 4);\n        i0.ɵɵtemplate(10, StudentNoticeComponent_div_10_Template, 56, 7, \"div\", 5);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.notices.length === 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.notices.length > 0);\n      }\n    },\n    dependencies: [i4.NgForOf, i4.NgIf, i5.MatCheckbox, i6.MatIcon, i7.NgControlStatus, i7.NgModel, i4.DatePipe],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "notice_r4", "expiryDate", "ɵɵlistener", "StudentNoticeComponent_div_10_tr_26_button_34_Template_button_click_0_listener", "ɵɵrestoreView", "_r12", "ɵɵnextContext", "$implicit", "ctx_r10", "ɵɵresetView", "mark<PERSON><PERSON><PERSON>", "StudentNoticeComponent_div_10_tr_26_Template_mat_checkbox_change_2_listener", "restoredCtx", "_r14", "ctx_r13", "toggleNoticeSelection", "_id", "ɵɵtemplate", "StudentNoticeComponent_div_10_tr_26_div_10_Template", "StudentNoticeComponent_div_10_tr_26_div_24_Template", "StudentNoticeComponent_div_10_tr_26_mat_icon_27_Template", "StudentNoticeComponent_div_10_tr_26_Template_button_click_31_listener", "ctx_r15", "viewNoticeDetails", "StudentNoticeComponent_div_10_tr_26_button_34_Template", "ɵɵclassProp", "isPinned", "ctx_r3", "isNoticeRead", "ɵɵproperty", "selectedNotices", "has", "ɵɵtextInterpolate", "getCategoryIcon", "category", "title", "ɵɵclassMapInterpolate1", "getPriorityColor", "priority", "author", "publishDate", "StudentNoticeComponent_div_10_Template_mat_checkbox_ngModelChange_7_listener", "$event", "_r17", "ctx_r16", "isChecked", "StudentNoticeComponent_div_10_Template_mat_checkbox_change_7_listener", "ctx_r18", "toggleCheckbox", "StudentNoticeComponent_div_10_tr_26_Template", "ctx_r2", "isIndeterminate", "notices", "length", "unreadNoticesCount", "readNoticesCount", "pinnedNoticesCount", "StudentNoticeComponent", "constructor", "router", "noticeService", "userService", "studentstable", "loading", "Set", "ngOnInit", "currentUser", "getUserFromLocalStorage", "user", "loadNotices", "getNoticesForUser", "subscribe", "next", "response", "success", "error", "console", "fire", "icon", "text", "for<PERSON>ach", "notice", "add", "clear", "noticeId", "delete", "updateCheckboxState", "totalNotices", "selectedCount", "size", "noticeIndex", "findIndex", "n", "readBy", "push", "readAt", "Date", "some", "read", "html", "toLocaleDateString", "content", "width", "showCloseButton", "confirmButtonText", "refreshNotices", "filter", "_", "ɵɵdirectiveInject", "i1", "Router", "i2", "NoticeService", "i3", "UserService", "_2", "selectors", "decls", "vars", "consts", "template", "StudentNoticeComponent_Template", "rf", "ctx", "StudentNoticeComponent_Template_button_click_4_listener", "StudentNoticeComponent_div_8_Template", "StudentNoticeComponent_div_9_Template", "StudentNoticeComponent_div_10_Template"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\student-dashboard\\student-notice\\student-notice.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\student-dashboard\\student-notice\\student-notice.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { NoticeService, Notice } from '../../../services/notice.service';\r\nimport { UserService } from '../../../services/user.service';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-student-notice',\r\n  templateUrl: './student-notice.component.html',\r\n  styleUrls: ['./student-notice.component.css']\r\n})\r\nexport class StudentNoticeComponent implements OnInit {\r\n  isChecked: boolean = false;\r\n  isIndeterminate: boolean = true;\r\n  studentstable: boolean = false;\r\n\r\n  notices: Notice[] = [];\r\n  loading = false;\r\n  currentUser: any;\r\n  selectedNotices: Set<string> = new Set();\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private noticeService: NoticeService,\r\n    private userService: UserService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.currentUser = this.userService.getUserFromLocalStorage()?.user;\r\n    this.loadNotices();\r\n  }\r\n\r\n  loadNotices(): void {\r\n    if (!this.currentUser) return;\r\n\r\n    this.loading = true;\r\n    this.noticeService.getNoticesForUser(this.currentUser._id).subscribe({\r\n      next: (response) => {\r\n        if (response.success && response.notices) {\r\n          this.notices = response.notices;\r\n        }\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading notices:', error);\r\n        Swal.fire({\r\n          icon: 'error',\r\n          title: 'Error',\r\n          text: 'Failed to load notices. Please try again.',\r\n        });\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  toggleCheckbox(): void {\r\n    if (this.isIndeterminate) {\r\n      this.isIndeterminate = false;\r\n      this.isChecked = true;\r\n      // Select all notices\r\n      this.notices.forEach(notice => {\r\n        if (notice._id) this.selectedNotices.add(notice._id);\r\n      });\r\n    } else if (this.isChecked) {\r\n      this.isChecked = false;\r\n      // Deselect all notices\r\n      this.selectedNotices.clear();\r\n    } else {\r\n      this.isIndeterminate = true;\r\n      // Select some notices (partial selection)\r\n      this.selectedNotices.clear();\r\n    }\r\n  }\r\n\r\n  toggleNoticeSelection(noticeId: string): void {\r\n    if (this.selectedNotices.has(noticeId)) {\r\n      this.selectedNotices.delete(noticeId);\r\n    } else {\r\n      this.selectedNotices.add(noticeId);\r\n    }\r\n    this.updateCheckboxState();\r\n  }\r\n\r\n  updateCheckboxState(): void {\r\n    const totalNotices = this.notices.length;\r\n    const selectedCount = this.selectedNotices.size;\r\n\r\n    if (selectedCount === 0) {\r\n      this.isChecked = false;\r\n      this.isIndeterminate = false;\r\n    } else if (selectedCount === totalNotices) {\r\n      this.isChecked = true;\r\n      this.isIndeterminate = false;\r\n    } else {\r\n      this.isChecked = false;\r\n      this.isIndeterminate = true;\r\n    }\r\n  }\r\n\r\n  markAsRead(notice: Notice): void {\r\n    if (!notice._id || !this.currentUser) return;\r\n\r\n    this.noticeService.markAsRead(notice._id, this.currentUser._id).subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          // Update local notice data\r\n          const noticeIndex = this.notices.findIndex(n => n._id === notice._id);\r\n          if (noticeIndex !== -1) {\r\n            if (!this.notices[noticeIndex].readBy) {\r\n              this.notices[noticeIndex].readBy = [];\r\n            }\r\n            this.notices[noticeIndex].readBy!.push({\r\n              user: this.currentUser._id,\r\n              readAt: new Date()\r\n            });\r\n          }\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error marking notice as read:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  isNoticeRead(notice: Notice): boolean {\r\n    if (!notice.readBy || !this.currentUser) return false;\r\n    return notice.readBy.some(read => read.user === this.currentUser._id);\r\n  }\r\n\r\n  getPriorityColor(priority: string): string {\r\n    switch (priority) {\r\n      case 'Low': return 'success';\r\n      case 'Medium': return 'warning';\r\n      case 'High': return 'danger';\r\n      case 'Urgent': return 'dark';\r\n      default: return 'secondary';\r\n    }\r\n  }\r\n\r\n  getCategoryIcon(category: string): string {\r\n    switch (category) {\r\n      case 'Academic': return 'school';\r\n      case 'Administrative': return 'admin_panel_settings';\r\n      case 'Event': return 'event';\r\n      case 'Holiday': return 'celebration';\r\n      case 'Examination': return 'quiz';\r\n      case 'Emergency': return 'warning';\r\n      default: return 'info';\r\n    }\r\n  }\r\n\r\n  viewNoticeDetails(notice: Notice): void {\r\n    if (!this.isNoticeRead(notice)) {\r\n      this.markAsRead(notice);\r\n    }\r\n\r\n    Swal.fire({\r\n      title: notice.title,\r\n      html: `\r\n        <div class=\"text-start\">\r\n          <div class=\"mb-3\">\r\n            <span class=\"badge bg-${this.getPriorityColor(notice.priority)} me-2\">${notice.priority}</span>\r\n            <span class=\"badge bg-info\">${notice.category}</span>\r\n          </div>\r\n          <div class=\"mb-3\">\r\n            <strong>Author:</strong> ${notice.author || 'Unknown'}\r\n          </div>\r\n          <div class=\"mb-3\">\r\n            <strong>Published:</strong> ${new Date(notice.publishDate).toLocaleDateString()}\r\n          </div>\r\n          ${notice.expiryDate ? `<div class=\"mb-3\"><strong>Expires:</strong> ${new Date(notice.expiryDate).toLocaleDateString()}</div>` : ''}\r\n          <div class=\"content\">\r\n            ${notice.content}\r\n          </div>\r\n        </div>\r\n      `,\r\n      width: '600px',\r\n      showCloseButton: true,\r\n      confirmButtonText: 'Close'\r\n    });\r\n  }\r\n\r\n  refreshNotices(): void {\r\n    this.loadNotices();\r\n  }\r\n\r\n  get unreadNoticesCount(): number {\r\n    return this.notices.filter(notice => !this.isNoticeRead(notice)).length;\r\n  }\r\n\r\n  get readNoticesCount(): number {\r\n    return this.notices.filter(notice => this.isNoticeRead(notice)).length;\r\n  }\r\n\r\n  get pinnedNoticesCount(): number {\r\n    return this.notices.filter(notice => notice.isPinned).length;\r\n  }\r\n}\r\n", "<div class=\"container-fluid\">\r\n  <div class=\"d-flex justify-content-between align-items-center mb-4\">\r\n    <h1 class=\"fw-bold\">Notices</h1>\r\n    <button class=\"btn btn-outline-primary\" (click)=\"refreshNotices()\">\r\n      <mat-icon>refresh</mat-icon> Refresh\r\n    </button>\r\n  </div>\r\n\r\n  <div *ngIf=\"loading\" class=\"text-center py-5\">\r\n    <div class=\"spinner-border\" role=\"status\">\r\n      <span class=\"visually-hidden\">Loading...</span>\r\n    </div>\r\n    <p class=\"mt-2\">Loading notices...</p>\r\n  </div>\r\n\r\n  <div *ngIf=\"!loading && notices.length === 0\" class=\"text-center py-5\">\r\n    <mat-icon class=\"text-muted\" style=\"font-size: 64px;\">notifications_none</mat-icon>\r\n    <h4 class=\"text-muted mt-3\">No Notices Available</h4>\r\n    <p class=\"text-muted\">There are no notices to display at this time.</p>\r\n  </div>\r\n\r\n  <div *ngIf=\"!loading && notices.length > 0\">\r\n    <div class=\"table-container mb-4\">\r\n      <div class=\"table-responsive\">\r\n        <table class=\"table table-hover\">\r\n          <thead class=\"table-light\">\r\n            <tr>\r\n              <th class=\"one\">\r\n                <mat-checkbox\r\n                  color=\"primary\"\r\n                  [(ngModel)]=\"isChecked\"\r\n                  [indeterminate]=\"isIndeterminate\"\r\n                  (change)=\"toggleCheckbox()\">\r\n                  Select All <mat-icon>arrow_downward</mat-icon>\r\n                </mat-checkbox>\r\n              </th>\r\n              <th>Title</th>\r\n              <th>Category</th>\r\n              <th>Priority</th>\r\n              <th>Author</th>\r\n              <th>Date</th>\r\n              <th>Status</th>\r\n              <th>Actions</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let notice of notices\"\r\n                [class.table-warning]=\"notice.isPinned\"\r\n                [class.table-success]=\"isNoticeRead(notice)\">\r\n              <td>\r\n                <mat-checkbox\r\n                  color=\"primary\"\r\n                  [checked]=\"selectedNotices.has(notice._id!)\"\r\n                  (change)=\"toggleNoticeSelection(notice._id!)\">\r\n                </mat-checkbox>\r\n              </td>\r\n              <td>\r\n                <div class=\"d-flex align-items-center\">\r\n                  <mat-icon class=\"me-2 text-primary\">{{ getCategoryIcon(notice.category) }}</mat-icon>\r\n                  <div>\r\n                    <strong [class.text-muted]=\"isNoticeRead(notice)\">{{ notice.title }}</strong>\r\n                    <div *ngIf=\"notice.isPinned\" class=\"small text-warning\">\r\n                      <mat-icon class=\"small-icon\">push_pin</mat-icon> Pinned\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </td>\r\n              <td>\r\n                <span class=\"badge bg-info\">{{ notice.category }}</span>\r\n              </td>\r\n              <td>\r\n                <span class=\"badge bg-{{ getPriorityColor(notice.priority) }}\">\r\n                  {{ notice.priority }}\r\n                </span>\r\n              </td>\r\n              <td>{{ notice.author || 'Unknown' }}</td>\r\n              <td>\r\n                <div>\r\n                  <small>{{ notice.publishDate | date:'short' }}</small>\r\n                  <div *ngIf=\"notice.expiryDate\" class=\"small text-muted\">\r\n                    Expires: {{ notice.expiryDate | date:'short' }}\r\n                  </div>\r\n                </div>\r\n              </td>\r\n              <td>\r\n                <div class=\"d-flex align-items-center\">\r\n                  <mat-icon\r\n                    *ngIf=\"isNoticeRead(notice)\"\r\n                    class=\"text-success small-icon me-1\">\r\n                    check_circle\r\n                  </mat-icon>\r\n                  <span [class.text-success]=\"isNoticeRead(notice)\"\r\n                        [class.text-warning]=\"!isNoticeRead(notice)\">\r\n                    {{ isNoticeRead(notice) ? 'Read' : 'Unread' }}\r\n                  </span>\r\n                </div>\r\n              </td>\r\n              <td>\r\n                <button\r\n                  class=\"btn btn-sm btn-outline-primary\"\r\n                  (click)=\"viewNoticeDetails(notice)\"\r\n                  title=\"View Details\">\r\n                  <mat-icon>visibility</mat-icon>\r\n                </button>\r\n                <button\r\n                  *ngIf=\"!isNoticeRead(notice)\"\r\n                  class=\"btn btn-sm btn-outline-success ms-1\"\r\n                  (click)=\"markAsRead(notice)\"\r\n                  title=\"Mark as Read\">\r\n                  <mat-icon>done</mat-icon>\r\n                </button>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Notice Statistics -->\r\n    <div class=\"row mt-4\">\r\n      <div class=\"col-md-3\">\r\n        <div class=\"card text-center\">\r\n          <div class=\"card-body\">\r\n            <h5 class=\"card-title\">{{ notices.length }}</h5>\r\n            <p class=\"card-text text-muted\">Total Notices</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-3\">\r\n        <div class=\"card text-center\">\r\n          <div class=\"card-body\">\r\n            <h5 class=\"card-title text-warning\">\r\n              {{ unreadNoticesCount }}\r\n            </h5>\r\n            <p class=\"card-text text-muted\">Unread</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-3\">\r\n        <div class=\"card text-center\">\r\n          <div class=\"card-body\">\r\n            <h5 class=\"card-title text-success\">\r\n              {{ readNoticesCount }}\r\n            </h5>\r\n            <p class=\"card-text text-muted\">Read</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-3\">\r\n        <div class=\"card text-center\">\r\n          <div class=\"card-body\">\r\n            <h5 class=\"card-title text-info\">\r\n              {{ pinnedNoticesCount }}\r\n            </h5>\r\n            <p class=\"card-text text-muted\">Pinned</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>"], "mappings": "AAIA,OAAOA,IAAI,MAAM,aAAa;;;;;;;;;;;ICI5BC,EAAA,CAAAC,cAAA,aAA8C;IAEZD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEjDH,EAAA,CAAAC,cAAA,WAAgB;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAGxCH,EAAA,CAAAC,cAAA,aAAuE;IACfD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACnFH,EAAA,CAAAC,cAAA,aAA4B;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrDH,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAE,MAAA,oDAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IA2CvDH,EAAA,CAAAC,cAAA,cAAwD;IACzBD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,eACnD;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAgBRH,EAAA,CAAAC,cAAA,cAAwD;IACtDD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,eAAAL,EAAA,CAAAM,WAAA,OAAAC,SAAA,CAAAC,UAAA,gBACF;;;;;IAKAR,EAAA,CAAAC,cAAA,mBAEuC;IACrCD,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;;IAcbH,EAAA,CAAAC,cAAA,iBAIuB;IADrBD,EAAA,CAAAS,UAAA,mBAAAC,+EAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,IAAA;MAAA,MAAAL,SAAA,GAAAP,EAAA,CAAAa,aAAA,GAAAC,SAAA;MAAA,MAAAC,OAAA,GAAAf,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAgB,WAAA,CAAAD,OAAA,CAAAE,UAAA,CAAAV,SAAA,CAAkB;IAAA,EAAC;IAE5BP,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;;IA/D/BH,EAAA,CAAAC,cAAA,SAEiD;IAK3CD,EAAA,CAAAS,UAAA,oBAAAS,4EAAA;MAAA,MAAAC,WAAA,GAAAnB,EAAA,CAAAW,aAAA,CAAAS,IAAA;MAAA,MAAAb,SAAA,GAAAY,WAAA,CAAAL,SAAA;MAAA,MAAAO,OAAA,GAAArB,EAAA,CAAAa,aAAA;MAAA,OAAUb,EAAA,CAAAgB,WAAA,CAAAK,OAAA,CAAAC,qBAAA,CAAAf,SAAA,CAAAgB,GAAA,CAAkC;IAAA,EAAC;IAC/CvB,EAAA,CAAAG,YAAA,EAAe;IAEjBH,EAAA,CAAAC,cAAA,SAAI;IAEoCD,EAAA,CAAAE,MAAA,GAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACrFH,EAAA,CAAAC,cAAA,UAAK;IAC+CD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC7EH,EAAA,CAAAwB,UAAA,KAAAC,mDAAA,kBAEM;IACRzB,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,UAAI;IAC0BD,EAAA,CAAAE,MAAA,IAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE1DH,EAAA,CAAAC,cAAA,UAAI;IAEAD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzCH,EAAA,CAAAC,cAAA,UAAI;IAEOD,EAAA,CAAAE,MAAA,IAAuC;;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtDH,EAAA,CAAAwB,UAAA,KAAAE,mDAAA,kBAEM;IACR1B,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,UAAI;IAEAD,EAAA,CAAAwB,UAAA,KAAAG,wDAAA,uBAIW;IACX3B,EAAA,CAAAC,cAAA,YACmD;IACjDD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGXH,EAAA,CAAAC,cAAA,UAAI;IAGAD,EAAA,CAAAS,UAAA,mBAAAmB,sEAAA;MAAA,MAAAT,WAAA,GAAAnB,EAAA,CAAAW,aAAA,CAAAS,IAAA;MAAA,MAAAb,SAAA,GAAAY,WAAA,CAAAL,SAAA;MAAA,MAAAe,OAAA,GAAA7B,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAgB,WAAA,CAAAa,OAAA,CAAAC,iBAAA,CAAAvB,SAAA,CAAyB;IAAA,EAAC;IAEnCP,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEjCH,EAAA,CAAAwB,UAAA,KAAAO,sDAAA,qBAMS;IACX/B,EAAA,CAAAG,YAAA,EAAK;;;;;IAhEHH,EAAA,CAAAgC,WAAA,kBAAAzB,SAAA,CAAA0B,QAAA,CAAuC,kBAAAC,MAAA,CAAAC,YAAA,CAAA5B,SAAA;IAKrCP,EAAA,CAAAI,SAAA,GAA4C;IAA5CJ,EAAA,CAAAoC,UAAA,YAAAF,MAAA,CAAAG,eAAA,CAAAC,GAAA,CAAA/B,SAAA,CAAAgB,GAAA,EAA4C;IAMRvB,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAuC,iBAAA,CAAAL,MAAA,CAAAM,eAAA,CAAAjC,SAAA,CAAAkC,QAAA,EAAsC;IAEhEzC,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAgC,WAAA,eAAAE,MAAA,CAAAC,YAAA,CAAA5B,SAAA,EAAyC;IAACP,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAuC,iBAAA,CAAAhC,SAAA,CAAAmC,KAAA,CAAkB;IAC9D1C,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAoC,UAAA,SAAA7B,SAAA,CAAA0B,QAAA,CAAqB;IAOHjC,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAuC,iBAAA,CAAAhC,SAAA,CAAAkC,QAAA,CAAqB;IAG3CzC,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAA2C,sBAAA,cAAAT,MAAA,CAAAU,gBAAA,CAAArC,SAAA,CAAAsC,QAAA,MAAwD;IAC5D7C,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAE,SAAA,CAAAsC,QAAA,MACF;IAEE7C,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAuC,iBAAA,CAAAhC,SAAA,CAAAuC,MAAA,cAAgC;IAGzB9C,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAuC,iBAAA,CAAAvC,EAAA,CAAAM,WAAA,SAAAC,SAAA,CAAAwC,WAAA,WAAuC;IACxC/C,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAoC,UAAA,SAAA7B,SAAA,CAAAC,UAAA,CAAuB;IAQ1BR,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAoC,UAAA,SAAAF,MAAA,CAAAC,YAAA,CAAA5B,SAAA,EAA0B;IAIvBP,EAAA,CAAAI,SAAA,GAA2C;IAA3CJ,EAAA,CAAAgC,WAAA,iBAAAE,MAAA,CAAAC,YAAA,CAAA5B,SAAA,EAA2C,kBAAA2B,MAAA,CAAAC,YAAA,CAAA5B,SAAA;IAE/CP,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAA6B,MAAA,CAAAC,YAAA,CAAA5B,SAAA,2BACF;IAWCP,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAoC,UAAA,UAAAF,MAAA,CAAAC,YAAA,CAAA5B,SAAA,EAA2B;;;;;;IApF5CP,EAAA,CAAAC,cAAA,UAA4C;IAS5BD,EAAA,CAAAS,UAAA,2BAAAuC,6EAAAC,MAAA;MAAAjD,EAAA,CAAAW,aAAA,CAAAuC,IAAA;MAAA,MAAAC,OAAA,GAAAnD,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAgB,WAAA,CAAAmC,OAAA,CAAAC,SAAA,GAAAH,MAAA;IAAA,EAAuB,oBAAAI,sEAAA;MAAArD,EAAA,CAAAW,aAAA,CAAAuC,IAAA;MAAA,MAAAI,OAAA,GAAAtD,EAAA,CAAAa,aAAA;MAAA,OAEbb,EAAA,CAAAgB,WAAA,CAAAsC,OAAA,CAAAC,cAAA,EAAgB;IAAA,EAFH;IAGvBvD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAGlDH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACdH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACbH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGpBH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAwB,UAAA,KAAAgC,4CAAA,mBAkEK;IACPxD,EAAA,CAAAG,YAAA,EAAQ;IAMdH,EAAA,CAAAC,cAAA,eAAsB;IAISD,EAAA,CAAAE,MAAA,IAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChDH,EAAA,CAAAC,cAAA,aAAgC;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAIvDH,EAAA,CAAAC,cAAA,eAAsB;IAIdD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAAgC;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAIhDH,EAAA,CAAAC,cAAA,eAAsB;IAIdD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAAgC;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAI9CH,EAAA,CAAAC,cAAA,eAAsB;IAIdD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAAgC;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IA5HpCH,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAoC,UAAA,YAAAqB,MAAA,CAAAL,SAAA,CAAuB,kBAAAK,MAAA,CAAAC,eAAA;IAgBN1D,EAAA,CAAAI,SAAA,IAAU;IAAVJ,EAAA,CAAAoC,UAAA,YAAAqB,MAAA,CAAAE,OAAA,CAAU;IA6EV3D,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAuC,iBAAA,CAAAkB,MAAA,CAAAE,OAAA,CAAAC,MAAA,CAAoB;IASzC5D,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAoD,MAAA,CAAAI,kBAAA,MACF;IASE7D,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAoD,MAAA,CAAAK,gBAAA,MACF;IASE9D,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAoD,MAAA,CAAAM,kBAAA,MACF;;;AD9IZ,OAAM,MAAOC,sBAAsB;EAUjCC,YACUC,MAAc,EACdC,aAA4B,EAC5BC,WAAwB;IAFxB,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IAZrB,KAAAhB,SAAS,GAAY,KAAK;IAC1B,KAAAM,eAAe,GAAY,IAAI;IAC/B,KAAAW,aAAa,GAAY,KAAK;IAE9B,KAAAV,OAAO,GAAa,EAAE;IACtB,KAAAW,OAAO,GAAG,KAAK;IAEf,KAAAjC,eAAe,GAAgB,IAAIkC,GAAG,EAAE;EAMrC;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,GAAG,IAAI,CAACL,WAAW,CAACM,uBAAuB,EAAE,EAAEC,IAAI;IACnE,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAA,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACH,WAAW,EAAE;IAEvB,IAAI,CAACH,OAAO,GAAG,IAAI;IACnB,IAAI,CAACH,aAAa,CAACU,iBAAiB,CAAC,IAAI,CAACJ,WAAW,CAAClD,GAAG,CAAC,CAACuD,SAAS,CAAC;MACnEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACrB,OAAO,EAAE;UACxC,IAAI,CAACA,OAAO,GAAGqB,QAAQ,CAACrB,OAAO;;QAEjC,IAAI,CAACW,OAAO,GAAG,KAAK;MACtB,CAAC;MACDY,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9CnF,IAAI,CAACqF,IAAI,CAAC;UACRC,IAAI,EAAE,OAAO;UACb3C,KAAK,EAAE,OAAO;UACd4C,IAAI,EAAE;SACP,CAAC;QACF,IAAI,CAAChB,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAf,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACG,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACN,SAAS,GAAG,IAAI;MACrB;MACA,IAAI,CAACO,OAAO,CAAC4B,OAAO,CAACC,MAAM,IAAG;QAC5B,IAAIA,MAAM,CAACjE,GAAG,EAAE,IAAI,CAACc,eAAe,CAACoD,GAAG,CAACD,MAAM,CAACjE,GAAG,CAAC;MACtD,CAAC,CAAC;KACH,MAAM,IAAI,IAAI,CAAC6B,SAAS,EAAE;MACzB,IAAI,CAACA,SAAS,GAAG,KAAK;MACtB;MACA,IAAI,CAACf,eAAe,CAACqD,KAAK,EAAE;KAC7B,MAAM;MACL,IAAI,CAAChC,eAAe,GAAG,IAAI;MAC3B;MACA,IAAI,CAACrB,eAAe,CAACqD,KAAK,EAAE;;EAEhC;EAEApE,qBAAqBA,CAACqE,QAAgB;IACpC,IAAI,IAAI,CAACtD,eAAe,CAACC,GAAG,CAACqD,QAAQ,CAAC,EAAE;MACtC,IAAI,CAACtD,eAAe,CAACuD,MAAM,CAACD,QAAQ,CAAC;KACtC,MAAM;MACL,IAAI,CAACtD,eAAe,CAACoD,GAAG,CAACE,QAAQ,CAAC;;IAEpC,IAAI,CAACE,mBAAmB,EAAE;EAC5B;EAEAA,mBAAmBA,CAAA;IACjB,MAAMC,YAAY,GAAG,IAAI,CAACnC,OAAO,CAACC,MAAM;IACxC,MAAMmC,aAAa,GAAG,IAAI,CAAC1D,eAAe,CAAC2D,IAAI;IAE/C,IAAID,aAAa,KAAK,CAAC,EAAE;MACvB,IAAI,CAAC3C,SAAS,GAAG,KAAK;MACtB,IAAI,CAACM,eAAe,GAAG,KAAK;KAC7B,MAAM,IAAIqC,aAAa,KAAKD,YAAY,EAAE;MACzC,IAAI,CAAC1C,SAAS,GAAG,IAAI;MACrB,IAAI,CAACM,eAAe,GAAG,KAAK;KAC7B,MAAM;MACL,IAAI,CAACN,SAAS,GAAG,KAAK;MACtB,IAAI,CAACM,eAAe,GAAG,IAAI;;EAE/B;EAEAzC,UAAUA,CAACuE,MAAc;IACvB,IAAI,CAACA,MAAM,CAACjE,GAAG,IAAI,CAAC,IAAI,CAACkD,WAAW,EAAE;IAEtC,IAAI,CAACN,aAAa,CAAClD,UAAU,CAACuE,MAAM,CAACjE,GAAG,EAAE,IAAI,CAACkD,WAAW,CAAClD,GAAG,CAAC,CAACuD,SAAS,CAAC;MACxEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB;UACA,MAAMgB,WAAW,GAAG,IAAI,CAACtC,OAAO,CAACuC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAC5E,GAAG,KAAKiE,MAAM,CAACjE,GAAG,CAAC;UACrE,IAAI0E,WAAW,KAAK,CAAC,CAAC,EAAE;YACtB,IAAI,CAAC,IAAI,CAACtC,OAAO,CAACsC,WAAW,CAAC,CAACG,MAAM,EAAE;cACrC,IAAI,CAACzC,OAAO,CAACsC,WAAW,CAAC,CAACG,MAAM,GAAG,EAAE;;YAEvC,IAAI,CAACzC,OAAO,CAACsC,WAAW,CAAC,CAACG,MAAO,CAACC,IAAI,CAAC;cACrC1B,IAAI,EAAE,IAAI,CAACF,WAAW,CAAClD,GAAG;cAC1B+E,MAAM,EAAE,IAAIC,IAAI;aACjB,CAAC;;;MAGR,CAAC;MACDrB,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACvD;KACD,CAAC;EACJ;EAEA/C,YAAYA,CAACqD,MAAc;IACzB,IAAI,CAACA,MAAM,CAACY,MAAM,IAAI,CAAC,IAAI,CAAC3B,WAAW,EAAE,OAAO,KAAK;IACrD,OAAOe,MAAM,CAACY,MAAM,CAACI,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC9B,IAAI,KAAK,IAAI,CAACF,WAAW,CAAClD,GAAG,CAAC;EACvE;EAEAqB,gBAAgBA,CAACC,QAAgB;IAC/B,QAAQA,QAAQ;MACd,KAAK,KAAK;QAAE,OAAO,SAAS;MAC5B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,MAAM;QAAE,OAAO,QAAQ;MAC5B,KAAK,QAAQ;QAAE,OAAO,MAAM;MAC5B;QAAS,OAAO,WAAW;;EAE/B;EAEAL,eAAeA,CAACC,QAAgB;IAC9B,QAAQA,QAAQ;MACd,KAAK,UAAU;QAAE,OAAO,QAAQ;MAChC,KAAK,gBAAgB;QAAE,OAAO,sBAAsB;MACpD,KAAK,OAAO;QAAE,OAAO,OAAO;MAC5B,KAAK,SAAS;QAAE,OAAO,aAAa;MACpC,KAAK,aAAa;QAAE,OAAO,MAAM;MACjC,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC;QAAS,OAAO,MAAM;;EAE1B;EAEAX,iBAAiBA,CAAC0D,MAAc;IAC9B,IAAI,CAAC,IAAI,CAACrD,YAAY,CAACqD,MAAM,CAAC,EAAE;MAC9B,IAAI,CAACvE,UAAU,CAACuE,MAAM,CAAC;;IAGzBzF,IAAI,CAACqF,IAAI,CAAC;MACR1C,KAAK,EAAE8C,MAAM,CAAC9C,KAAK;MACnBgE,IAAI,EAAE;;;oCAGwB,IAAI,CAAC9D,gBAAgB,CAAC4C,MAAM,CAAC3C,QAAQ,CAAC,UAAU2C,MAAM,CAAC3C,QAAQ;0CACzD2C,MAAM,CAAC/C,QAAQ;;;uCAGlB+C,MAAM,CAAC1C,MAAM,IAAI,SAAS;;;0CAGvB,IAAIyD,IAAI,CAACf,MAAM,CAACzC,WAAW,CAAC,CAAC4D,kBAAkB,EAAE;;YAE/EnB,MAAM,CAAChF,UAAU,GAAG,+CAA+C,IAAI+F,IAAI,CAACf,MAAM,CAAChF,UAAU,CAAC,CAACmG,kBAAkB,EAAE,QAAQ,GAAG,EAAE;;cAE9HnB,MAAM,CAACoB,OAAO;;;OAGrB;MACDC,KAAK,EAAE,OAAO;MACdC,eAAe,EAAE,IAAI;MACrBC,iBAAiB,EAAE;KACpB,CAAC;EACJ;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAACpC,WAAW,EAAE;EACpB;EAEA,IAAIf,kBAAkBA,CAAA;IACpB,OAAO,IAAI,CAACF,OAAO,CAACsD,MAAM,CAACzB,MAAM,IAAI,CAAC,IAAI,CAACrD,YAAY,CAACqD,MAAM,CAAC,CAAC,CAAC5B,MAAM;EACzE;EAEA,IAAIE,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAACH,OAAO,CAACsD,MAAM,CAACzB,MAAM,IAAI,IAAI,CAACrD,YAAY,CAACqD,MAAM,CAAC,CAAC,CAAC5B,MAAM;EACxE;EAEA,IAAIG,kBAAkBA,CAAA;IACpB,OAAO,IAAI,CAACJ,OAAO,CAACsD,MAAM,CAACzB,MAAM,IAAIA,MAAM,CAACvD,QAAQ,CAAC,CAAC2B,MAAM;EAC9D;EAAC,QAAAsD,CAAA,G;qBAzLUlD,sBAAsB,EAAAhE,EAAA,CAAAmH,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAArH,EAAA,CAAAmH,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAvH,EAAA,CAAAmH,iBAAA,CAAAK,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAtB1D,sBAAsB;IAAA2D,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCXnCjI,EAAA,CAAAC,cAAA,aAA6B;QAELD,EAAA,CAAAE,MAAA,cAAO;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAChCH,EAAA,CAAAC,cAAA,gBAAmE;QAA3BD,EAAA,CAAAS,UAAA,mBAAA0H,wDAAA;UAAA,OAASD,GAAA,CAAAlB,cAAA,EAAgB;QAAA,EAAC;QAChEhH,EAAA,CAAAC,cAAA,eAAU;QAAAD,EAAA,CAAAE,MAAA,cAAO;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAACH,EAAA,CAAAE,MAAA,gBAC/B;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAGXH,EAAA,CAAAwB,UAAA,IAAA4G,qCAAA,iBAKM;QAENpI,EAAA,CAAAwB,UAAA,IAAA6G,qCAAA,iBAIM;QAENrI,EAAA,CAAAwB,UAAA,KAAA8G,sCAAA,kBA0IM;QACRtI,EAAA,CAAAG,YAAA,EAAM;;;QAxJEH,EAAA,CAAAI,SAAA,GAAa;QAAbJ,EAAA,CAAAoC,UAAA,SAAA8F,GAAA,CAAA5D,OAAA,CAAa;QAObtE,EAAA,CAAAI,SAAA,GAAsC;QAAtCJ,EAAA,CAAAoC,UAAA,UAAA8F,GAAA,CAAA5D,OAAA,IAAA4D,GAAA,CAAAvE,OAAA,CAAAC,MAAA,OAAsC;QAMtC5D,EAAA,CAAAI,SAAA,GAAoC;QAApCJ,EAAA,CAAAoC,UAAA,UAAA8F,GAAA,CAAA5D,OAAA,IAAA4D,GAAA,CAAAvE,OAAA,CAAAC,MAAA,KAAoC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}