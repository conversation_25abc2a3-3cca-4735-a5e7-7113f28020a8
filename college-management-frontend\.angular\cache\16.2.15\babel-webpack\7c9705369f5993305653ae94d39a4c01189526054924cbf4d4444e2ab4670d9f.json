{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { TeacherOverviewComponent } from './teacher-overview/teacher-overview.component';\nimport { TeacherAttendnaceComponent } from './teacher-attendnace/teacher-attendnace.component';\nimport { TeacherStudentsComponent } from './teacher-students/teacher-students.component';\nimport { TeacherAllStudentsComponent } from './teacher-all-students/teacher-all-students.component';\nimport { TeacherClassesComponent } from './teacher-classes/teacher-classes.component';\nimport { StudentDetailComponent } from './student-detail/student-detail.component';\nimport { TeacherNoticeComponent } from './teacher-notice/teacher-notice.component';\nimport { TeacherComplaintComponent } from './teacher-complaint/teacher-complaint.component';\nimport { TeacherSubjectsComponent } from './teacher-subjects/teacher-subjects.component';\nimport { TeacherTimetableComponent } from './teacher-timetable/teacher-timetable.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: TeacherOverviewComponent\n}, {\n  path: 'overview',\n  component: TeacherOverviewComponent\n}, {\n  path: 'attendance',\n  component: TeacherAttendnaceComponent\n}, {\n  path: 'students',\n  component: TeacherStudentsComponent\n}, {\n  path: 'class-students',\n  component: TeacherStudentsComponent\n}, {\n  path: 'all-students',\n  component: TeacherAllStudentsComponent\n}, {\n  path: 'classes',\n  component: TeacherClassesComponent\n}, {\n  path: 'subjects',\n  component: TeacherSubjectsComponent\n}, {\n  path: 'timetable',\n  component: TeacherTimetableComponent\n}, {\n  path: 'student-detail',\n  component: StudentDetailComponent\n}, {\n  path: 'notice',\n  component: TeacherNoticeComponent\n}, {\n  path: 'complaint',\n  component: TeacherComplaintComponent\n}];\nexport class TeacherDashboardRoutingModule {\n  static #_ = this.ɵfac = function TeacherDashboardRoutingModule_Factory(t) {\n    return new (t || TeacherDashboardRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: TeacherDashboardRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild(routes), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(TeacherDashboardRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "TeacherOverviewComponent", "TeacherAttendnaceComponent", "TeacherStudentsComponent", "TeacherAllStudentsComponent", "TeacherClassesComponent", "StudentDetailComponent", "TeacherNoticeComponent", "TeacherComplaintComponent", "TeacherSubjectsComponent", "TeacherTimetableComponent", "routes", "path", "component", "TeacherDashboardRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\teacher-dashboard\\teacher-dashboard-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { TeacherOverviewComponent } from './teacher-overview/teacher-overview.component';\r\nimport { TeacherAttendnaceComponent } from './teacher-attendnace/teacher-attendnace.component';\r\nimport { TeacherStudentsComponent } from './teacher-students/teacher-students.component';\r\nimport { TeacherAllStudentsComponent } from './teacher-all-students/teacher-all-students.component';\r\nimport { TeacherClassesComponent } from './teacher-classes/teacher-classes.component';\r\nimport { StudentDetailComponent } from './student-detail/student-detail.component';\r\nimport { TeacherNoticeComponent } from './teacher-notice/teacher-notice.component';\r\nimport { TeacherComplaintComponent } from './teacher-complaint/teacher-complaint.component';\r\nimport { TeacherSubjectsComponent } from './teacher-subjects/teacher-subjects.component';\r\nimport { TeacherTimetableComponent } from './teacher-timetable/teacher-timetable.component';\r\n\r\nconst routes: Routes = [\r\n  { path: '', component: TeacherOverviewComponent },\r\n  { path: 'overview', component: TeacherOverviewComponent },\r\n  { path: 'attendance', component: TeacherAttendnaceComponent },\r\n  { path: 'students', component: TeacherStudentsComponent },\r\n  { path: 'class-students', component: TeacherStudentsComponent },\r\n  { path: 'all-students', component: TeacherAllStudentsComponent },\r\n  { path: 'classes', component: TeacherClassesComponent },\r\n  { path: 'subjects', component: TeacherSubjectsComponent },\r\n  { path: 'timetable', component: TeacherTimetableComponent },\r\n  { path: 'student-detail', component: StudentDetailComponent },\r\n  { path: 'notice', component: TeacherNoticeComponent },\r\n  { path: 'complaint', component: TeacherComplaintComponent }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class TeacherDashboardRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,0BAA0B,QAAQ,mDAAmD;AAC9F,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,2BAA2B,QAAQ,uDAAuD;AACnG,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,yBAAyB,QAAQ,iDAAiD;AAC3F,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,yBAAyB,QAAQ,iDAAiD;;;AAE3F,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEZ;AAAwB,CAAE,EACjD;EAAEW,IAAI,EAAE,UAAU;EAAEC,SAAS,EAAEZ;AAAwB,CAAE,EACzD;EAAEW,IAAI,EAAE,YAAY;EAAEC,SAAS,EAAEX;AAA0B,CAAE,EAC7D;EAAEU,IAAI,EAAE,UAAU;EAAEC,SAAS,EAAEV;AAAwB,CAAE,EACzD;EAAES,IAAI,EAAE,gBAAgB;EAAEC,SAAS,EAAEV;AAAwB,CAAE,EAC/D;EAAES,IAAI,EAAE,cAAc;EAAEC,SAAS,EAAET;AAA2B,CAAE,EAChE;EAAEQ,IAAI,EAAE,SAAS;EAAEC,SAAS,EAAER;AAAuB,CAAE,EACvD;EAAEO,IAAI,EAAE,UAAU;EAAEC,SAAS,EAAEJ;AAAwB,CAAE,EACzD;EAAEG,IAAI,EAAE,WAAW;EAAEC,SAAS,EAAEH;AAAyB,CAAE,EAC3D;EAAEE,IAAI,EAAE,gBAAgB;EAAEC,SAAS,EAAEP;AAAsB,CAAE,EAC7D;EAAEM,IAAI,EAAE,QAAQ;EAAEC,SAAS,EAAEN;AAAsB,CAAE,EACrD;EAAEK,IAAI,EAAE,WAAW;EAAEC,SAAS,EAAEL;AAAyB,CAAE,CAC5D;AAMD,OAAM,MAAOM,6BAA6B;EAAA,QAAAC,CAAA,G;qBAA7BD,6BAA6B;EAAA;EAAA,QAAAE,EAAA,G;UAA7BF;EAA6B;EAAA,QAAAG,EAAA,G;cAH9BjB,YAAY,CAACkB,QAAQ,CAACP,MAAM,CAAC,EAC7BX,YAAY;EAAA;;;2EAEXc,6BAA6B;IAAAK,OAAA,GAAAC,EAAA,CAAApB,YAAA;IAAAqB,OAAA,GAF9BrB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}