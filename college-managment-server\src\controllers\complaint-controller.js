const Complaint = require('../models/complaint');
const User = require('../models/userModel');

// Create a new complaint
const createComplaint = async (req, res) => {
    try {
        const {
            title,
            description,
            complainant,
            category,
            priority
        } = req.body;

        // Validate required fields
        if (!title || !description || !complainant) {
            return res.status(400).json({
                success: false,
                message: 'Title, description, and complainant are required'
            });
        }

        // Get complainant details
        const complainantUser = await User.findById(complainant);
        if (!complainantUser) {
            return res.status(404).json({
                success: false,
                message: 'Complainant not found'
            });
        }

        const complaint = new Complaint({
            title,
            description,
            complainant,
            complainantRole: complainantUser.role,
            category: category || 'Other',
            priority: priority || 'Medium'
        });

        await complaint.save();
        await complaint.populate('complainant', 'name email role');

        res.status(201).json({
            success: true,
            message: 'Complaint created successfully',
            complaint
        });
    } catch (error) {
        console.error('Error creating complaint:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

// Get all complaints with filtering and pagination
const getAllComplaints = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            status,
            category,
            priority,
            complainant,
            assignedTo,
            sortBy = 'createdAt',
            sortOrder = 'desc'
        } = req.query;

        // Build filter object
        const filter = { isActive: true };
        if (status) filter.status = status;
        if (category) filter.category = category;
        if (priority) filter.priority = priority;
        if (complainant) filter.complainant = complainant;
        if (assignedTo) filter.assignedTo = assignedTo;

        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sortOptions = {};
        sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

        const complaints = await Complaint.find(filter)
            .populate('complainant', 'name email role regNo')
            .populate('assignedTo', 'name email role')
            .populate('resolvedBy', 'name email role')
            .sort(sortOptions)
            .skip(skip)
            .limit(parseInt(limit));

        const totalComplaints = await Complaint.countDocuments(filter);
        const totalPages = Math.ceil(totalComplaints / parseInt(limit));

        res.status(200).json({
            success: true,
            complaints,
            pagination: {
                currentPage: parseInt(page),
                totalPages,
                totalComplaints,
                hasNext: parseInt(page) < totalPages,
                hasPrev: parseInt(page) > 1
            }
        });
    } catch (error) {
        console.error('Error fetching complaints:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

// Get complaint by ID
const getComplaintById = async (req, res) => {
    try {
        const { id } = req.params;

        const complaint = await Complaint.findById(id)
            .populate('complainant', 'name email role regNo')
            .populate('assignedTo', 'name email role')
            .populate('resolvedBy', 'name email role')
            .populate('comments.user', 'name email role');

        if (!complaint) {
            return res.status(404).json({
                success: false,
                message: 'Complaint not found'
            });
        }

        res.status(200).json({
            success: true,
            complaint
        });
    } catch (error) {
        console.error('Error fetching complaint:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

// Update complaint status
const updateComplaintStatus = async (req, res) => {
    try {
        const { id } = req.params;
        const { status, assignedTo, resolution, resolvedBy } = req.body;

        const complaint = await Complaint.findById(id);
        if (!complaint) {
            return res.status(404).json({
                success: false,
                message: 'Complaint not found'
            });
        }

        // Update fields
        if (status) complaint.status = status;
        if (assignedTo) complaint.assignedTo = assignedTo;
        if (resolution) complaint.resolution = resolution;
        
        if (status === 'Resolved' && resolvedBy) {
            complaint.resolvedBy = resolvedBy;
            complaint.resolvedAt = new Date();
        }

        await complaint.save();
        await complaint.populate([
            { path: 'complainant', select: 'name email role regNo' },
            { path: 'assignedTo', select: 'name email role' },
            { path: 'resolvedBy', select: 'name email role' }
        ]);

        res.status(200).json({
            success: true,
            message: 'Complaint updated successfully',
            complaint
        });
    } catch (error) {
        console.error('Error updating complaint:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

// Add comment to complaint
const addComment = async (req, res) => {
    try {
        const { id } = req.params;
        const { comment, user } = req.body;

        if (!comment || !user) {
            return res.status(400).json({
                success: false,
                message: 'Comment and user are required'
            });
        }

        const complaint = await Complaint.findById(id);
        if (!complaint) {
            return res.status(404).json({
                success: false,
                message: 'Complaint not found'
            });
        }

        complaint.comments.push({
            user,
            comment,
            createdAt: new Date()
        });

        await complaint.save();
        await complaint.populate('comments.user', 'name email role');

        res.status(200).json({
            success: true,
            message: 'Comment added successfully',
            complaint
        });
    } catch (error) {
        console.error('Error adding comment:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

// Get complaints by user
const getComplaintsByUser = async (req, res) => {
    try {
        const { userId } = req.params;
        const { page = 1, limit = 10 } = req.query;

        const skip = (parseInt(page) - 1) * parseInt(limit);

        const complaints = await Complaint.find({
            complainant: userId,
            isActive: true
        })
            .populate('assignedTo', 'name email role')
            .populate('resolvedBy', 'name email role')
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(parseInt(limit));

        const totalComplaints = await Complaint.countDocuments({
            complainant: userId,
            isActive: true
        });

        res.status(200).json({
            success: true,
            complaints,
            totalComplaints
        });
    } catch (error) {
        console.error('Error fetching user complaints:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

// Delete complaint (soft delete)
const deleteComplaint = async (req, res) => {
    try {
        const { id } = req.params;

        const complaint = await Complaint.findByIdAndUpdate(
            id,
            { isActive: false },
            { new: true }
        );

        if (!complaint) {
            return res.status(404).json({
                success: false,
                message: 'Complaint not found'
            });
        }

        res.status(200).json({
            success: true,
            message: 'Complaint deleted successfully'
        });
    } catch (error) {
        console.error('Error deleting complaint:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

module.exports = {
    createComplaint,
    getAllComplaints,
    getComplaintById,
    updateComplaintStatus,
    addComment,
    getComplaintsByUser,
    deleteComplaint
};
