{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../sidebar/sidebar.component\";\nimport * as i3 from \"../header/header.component\";\nexport let MainComponent = /*#__PURE__*/(() => {\n  class MainComponent {\n    constructor() {\n      this.isSidebarOpen = true;\n      this.isMobile = false;\n    }\n    onResize(event) {\n      this.checkScreenSize();\n    }\n    ngOnInit() {\n      this.checkScreenSize();\n    }\n    checkScreenSize() {\n      this.isMobile = window.innerWidth < 992;\n      if (this.isMobile) {\n        this.isSidebarOpen = false;\n      }\n    }\n    toggleSidebar() {\n      if (this.isMobile) {\n        this.isSidebarOpen = !this.isSidebarOpen;\n      } else {\n        this.isSidebarOpen = !this.isSidebarOpen;\n      }\n    }\n    static #_ = this.ɵfac = function MainComponent_Factory(t) {\n      return new (t || MainComponent)();\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MainComponent,\n      selectors: [[\"app-main\"]],\n      hostBindings: function MainComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"resize\", function MainComponent_resize_HostBindingHandler($event) {\n            return ctx.onResize($event);\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      decls: 8,\n      vars: 5,\n      consts: [[1, \"app-container\"], [1, \"sidebar-wrapper\"], [3, \"isSidebarOpen\", \"toggle\"], [1, \"main-content\"], [3, \"toggleSidebar\"], [1, \"maindiv\"], [1, \"router-container\"]],\n      template: function MainComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"app-sidebar\", 2);\n          i0.ɵɵlistener(\"toggle\", function MainComponent_Template_app_sidebar_toggle_2_listener() {\n            return ctx.toggleSidebar();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"app-header\", 4);\n          i0.ɵɵlistener(\"toggleSidebar\", function MainComponent_Template_app_header_toggleSidebar_4_listener() {\n            return ctx.toggleSidebar();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6);\n          i0.ɵɵelement(7, \"router-outlet\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"sidebar-open\", ctx.isSidebarOpen)(\"sidebar-closed\", !ctx.isSidebarOpen);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"isSidebarOpen\", ctx.isSidebarOpen);\n        }\n      },\n      dependencies: [i1.RouterOutlet, i2.SidebarComponent, i3.HeaderComponent],\n      styles: [\".app-container[_ngcontent-%COMP%]{display:flex;min-height:100vh;background-color:#f5f7fa;transition:all .3s ease;width:100%;overflow-x:hidden}.sidebar-wrapper[_ngcontent-%COMP%]{position:fixed;height:100vh;z-index:1000;transition:all .3s ease}.main-content[_ngcontent-%COMP%]{flex:1;margin-left:250px;transition:all .3s ease;min-height:100vh;display:flex;flex-direction:column}.router-container[_ngcontent-%COMP%]{flex:1;background-color:#f5f7fa}.sidebar-closed[_ngcontent-%COMP%]   .sidebar-wrapper[_ngcontent-%COMP%]{width:70px}.sidebar-closed[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]{margin-left:70px}@media (max-width: 991.98px){.sidebar-wrapper[_ngcontent-%COMP%]{transform:translate(-100%);width:250px;position:absolute}.sidebar-open[_ngcontent-%COMP%]   .sidebar-wrapper[_ngcontent-%COMP%]{transform:translate(0)}.sidebar-closed[_ngcontent-%COMP%]   .sidebar-wrapper[_ngcontent-%COMP%]{transform:translate(-100%);width:250px}.main-content[_ngcontent-%COMP%]{margin-left:0!important}}@media print{.sidebar-wrapper[_ngcontent-%COMP%]{display:none}.main-content[_ngcontent-%COMP%]{margin-left:0!important}}\"]\n    });\n  }\n  return MainComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}