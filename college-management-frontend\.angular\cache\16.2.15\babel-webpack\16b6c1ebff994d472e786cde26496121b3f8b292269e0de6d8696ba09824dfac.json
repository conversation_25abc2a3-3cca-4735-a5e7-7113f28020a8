{"ast": null, "code": "import Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/subject.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/checkbox\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/forms\";\nfunction SubjectListComponent_tr_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 18)(2, \"mat-checkbox\", 19);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\", 20);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\", 20);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 21)(9, \"div\", 22)(10, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function SubjectListComponent_tr_31_Template_button_click_10_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r3);\n      const subject_r1 = restoredCtx.$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.deleteSubject(subject_r1._id));\n    });\n    i0.ɵɵelementStart(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"delete\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function SubjectListComponent_tr_31_Template_button_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r3);\n      const subject_r1 = restoredCtx.$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.editSubject(subject_r1));\n    });\n    i0.ɵɵelementStart(14, \"mat-icon\");\n    i0.ɵɵtext(15, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function SubjectListComponent_tr_31_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.showComingSoon());\n    });\n    i0.ɵɵelementStart(17, \"mat-icon\");\n    i0.ɵɵtext(18, \"remove_red_eye\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const subject_r1 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", subject_r1 == null ? null : subject_r1.subjectName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", subject_r1 == null ? null : subject_r1.department == null ? null : subject_r1.department.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", subject_r1 == null ? null : subject_r1.code, \" \");\n  }\n}\nexport let SubjectListComponent = /*#__PURE__*/(() => {\n  class SubjectListComponent {\n    constructor(subjectService, router) {\n      this.subjectService = subjectService;\n      this.router = router;\n      this.currentPage = 1;\n      this.itemsPerPage = 5;\n      this.searchQuery = '';\n      this.subjects = [];\n      this.isChecked = false;\n      this.isIndeterminate = true;\n    }\n    ngOnInit() {\n      this.getSubjects();\n    }\n    searchsubject() {\n      if (this.searchQuery) {\n        this.subjects = this.subjects.filter(subject => {\n          return subject.subjectName.toLowerCase().includes(this.searchQuery.toLowerCase()) || subject.code.toLowerCase().includes(this.searchQuery);\n        });\n      } else {\n        this.subjects = this.painatedSubject;\n      }\n      this.currentPage = 1;\n    }\n    get painatedSubject() {\n      const startIndex = (this.currentPage - 1) * this.itemsPerPage;\n      const endIndex = startIndex + this.itemsPerPage;\n      return this.subjects.slice(startIndex, endIndex);\n    }\n    get totalPages() {\n      return Math.ceil(this.subjects.length / this.itemsPerPage);\n    }\n    nextPage() {\n      if (this.currentPage < this.totalPages) {\n        this.currentPage++;\n      }\n    }\n    previousPage() {\n      if (this.currentPage > 1) {\n        this.currentPage--;\n      }\n    }\n    // Edit subject\n    editSubject(subject) {\n      this.router.navigate(['/dashboard/admin/subjects/add-subject/' + '' + subject._id]);\n      // this.subjectForm.patchValue({\n      //   subjectName: subject.subjectName,\n      //   code: subject.code\n      // });\n      // this.editingSubjectId = subject._id;\n    }\n    // Fetch all subjects\n    getSubjects() {\n      this.subjectService.getSubjects().subscribe(data => {\n        this.subjects = data.subjects;\n      }, error => {\n        console.error('Error fetching subjects:', error);\n      });\n    }\n    deleteSubject(id) {\n      Swal.fire({\n        title: 'Are you sure?',\n        text: 'This action will permanently delete the subject.',\n        icon: 'warning',\n        showCancelButton: true,\n        confirmButtonColor: '#d33',\n        cancelButtonColor: '#3085d6',\n        confirmButtonText: 'Yes, delete it!',\n        cancelButtonText: 'Cancel'\n      }).then(result => {\n        if (result.isConfirmed) {\n          // Proceed with deletion\n          this.subjectService.deleteSubject(id).subscribe(res => {\n            Swal.fire({\n              title: res.message,\n              icon: 'success',\n              confirmButtonText: 'OK',\n              confirmButtonColor: '#3085d6',\n              timer: 2000\n            }).then(() => {\n              // Update the UI without reloading\n              this.subjects = this.subjects.filter(subject => subject._id !== id);\n            });\n          }, error => {\n            console.error('Error deleting subject:', error);\n            Swal.fire({\n              title: 'Error',\n              text: 'Failed to delete the subject. Please try again.',\n              icon: 'error',\n              confirmButtonColor: '#3085d6'\n            });\n          });\n        }\n      });\n    }\n    toggleCheckbox() {\n      if (this.isIndeterminate) {\n        this.isIndeterminate = false;\n        this.isChecked = true;\n      } else if (this.isChecked) {\n        this.isChecked = false;\n      } else {\n        this.isIndeterminate = true;\n      }\n    }\n    showComingSoon() {\n      Swal.fire({\n        title: 'Coming Soon!',\n        text: 'This feature is under development.',\n        icon: 'info',\n        confirmButtonText: 'OK',\n        confirmButtonColor: '#3085d6'\n      });\n    }\n    static #_ = this.ɵfac = function SubjectListComponent_Factory(t) {\n      return new (t || SubjectListComponent)(i0.ɵɵdirectiveInject(i1.SubjectService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SubjectListComponent,\n      selectors: [[\"app-subject-list\"]],\n      decls: 41,\n      vars: 8,\n      consts: [[1, \"maindiv\"], [1, \"secondarydiv\"], [1, \"my-3\", \"d-flex\", \"justify-content-between\", \"searchAndtab\"], [1, \"d-flex\", \"flex-wrap\", \"gap-3\"], [\"routerLink\", \"/dashboard/admin/subjects/add-subject\", 1, \"btn\", \"btn-top\", \"border\", \"d-flex\", \"align-items-center\"], [1, \"search-container\"], [1, \"search-input-wrapper\"], [\"type\", \"text\", \"placeholder\", \"Search\", 1, \"search-input\", 3, \"ngModel\", \"input\", \"ngModelChange\"], [1, \"search-icon\"], [1, \"table-container\"], [1, \"table-responsive\"], [1, \"tablehead\"], [\"color\", \"primary\", 3, \"ngModel\", \"indeterminate\", \"ngModelChange\", \"change\"], [1, \"two\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"p-3\", \"align-items-center\", \"justify-content-between\"], [1, \"btn\", \"btn-top\", \"border\", 3, \"disabled\", \"click\"], [1, \"page\"], [\"data-label\", \"Teacher Name\", 1, \"name\"], [\"color\", \"primary\"], [\"data-label\", \"Class\", 1, \"para\"], [\"data-label\", \"Action\", 1, \"para\"], [1, \"d-flex\", \"gap-2\"], [1, \"btn\", \"btndelete\", \"d-flex\", \"justify-content-center\", \"align-items-center\", 3, \"click\"], [1, \"btn\", \"btnedit\", \"d-flex\", \"justify-content-center\", \"align-items-center\", 3, \"click\"]],\n      template: function SubjectListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"button\", 4)(5, \"mat-icon\");\n          i0.ɵɵtext(6, \"add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(7, \"\\u00A0 Add New subject\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\")(9, \"div\", 5)(10, \"div\", 6)(11, \"input\", 7);\n          i0.ɵɵlistener(\"input\", function SubjectListComponent_Template_input_input_11_listener() {\n            return ctx.searchsubject();\n          })(\"ngModelChange\", function SubjectListComponent_Template_input_ngModelChange_11_listener($event) {\n            return ctx.searchQuery = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"mat-icon\", 8);\n          i0.ɵɵtext(13, \"search\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(14, \"div\", 9)(15, \"div\", 10)(16, \"table\")(17, \"thead\")(18, \"tr\", 11)(19, \"th\")(20, \"mat-checkbox\", 12);\n          i0.ɵɵlistener(\"ngModelChange\", function SubjectListComponent_Template_mat_checkbox_ngModelChange_20_listener($event) {\n            return ctx.isChecked = $event;\n          })(\"change\", function SubjectListComponent_Template_mat_checkbox_change_20_listener() {\n            return ctx.toggleCheckbox();\n          });\n          i0.ɵɵtext(21, \" Subject name \");\n          i0.ɵɵelementStart(22, \"mat-icon\");\n          i0.ɵɵtext(23, \"arrow_downward\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(24, \"th\");\n          i0.ɵɵtext(25, \"Department \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"th\");\n          i0.ɵɵtext(27, \"code \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"th\", 13);\n          i0.ɵɵtext(29, \"Actions\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(30, \"tbody\");\n          i0.ɵɵtemplate(31, SubjectListComponent_tr_31_Template, 19, 3, \"tr\", 14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(32, \"div\", 15)(33, \"div\")(34, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function SubjectListComponent_Template_button_click_34_listener() {\n            return ctx.previousPage();\n          });\n          i0.ɵɵtext(35, \"Previous\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"div\", 17);\n          i0.ɵɵtext(37);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"div\")(39, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function SubjectListComponent_Template_button_click_39_listener() {\n            return ctx.nextPage();\n          });\n          i0.ɵɵtext(40, \"Next\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngModel\", ctx.isChecked)(\"indeterminate\", ctx.isIndeterminate);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngForOf\", ctx.painatedSubject);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.currentPage === 1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate2(\"Page \", ctx.currentPage, \" of \", ctx.totalPages, \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.currentPage === ctx.totalPages);\n        }\n      },\n      dependencies: [i3.NgForOf, i2.RouterLink, i4.MatCheckbox, i5.MatIcon, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgModel]\n    });\n  }\n  return SubjectListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}