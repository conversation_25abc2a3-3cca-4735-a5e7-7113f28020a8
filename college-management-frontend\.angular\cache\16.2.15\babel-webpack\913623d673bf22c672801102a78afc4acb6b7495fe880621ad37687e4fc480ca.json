{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/material/icon\";\nconst _c0 = function (a0, a1, a2) {\n  return {\n    \"text-success\": a0,\n    \"text-danger\": a1,\n    \"text-warning\": a2\n  };\n};\nfunction AttendanceComponent_tr_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 14);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 15);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 16);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 17);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 18);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 19);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 20);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\", 21);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 22);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const student_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(student_r1.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(student_r1.rollNumber);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(student_r1.fatherName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(student_r1.department);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(student_r1.class);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(student_r1.section);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(student_r1.registrationNo);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(student_r1.mobileNo);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(10, _c0, student_r1.status === \"present\", student_r1.status === \"absent\", student_r1.status === \"onleave\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", student_r1.status === \"present\" ? \"Present\" : student_r1.status === \"absent\" ? \"Absent\" : \"On Leave\", \" \");\n  }\n}\nexport let AttendanceComponent = /*#__PURE__*/(() => {\n  class AttendanceComponent {\n    constructor(router) {\n      this.router = router;\n      this.searchQuery = '';\n      this.currentPage = 1;\n      this.itemsPerPage = 7;\n      // Dropdown options\n      // departments = ['Pre-Medical', 'Pre-Engineering', 'Computer Science'];\n      // classes = ['1st Year', '2nd Year'];\n      // sections = ['A', 'B', 'C'];\n      // fathername=['ahmad' , 'sherali' , 'osama']\n      // Selected filters\n      this.selectedClass = '';\n      this.selectedDepartment = '';\n      this.selectedSection = '';\n      this.students = [{\n        name: 'John Doe',\n        fatherName: 'Ahmad',\n        rollNumber: '01',\n        department: 'Pre-Medical',\n        class: '1st Year',\n        section: 'A',\n        mobileNo: '**********',\n        registrationNo: 'REG001',\n        status: 'present'\n      }, {\n        name: 'Jane Smith',\n        fatherName: 'Sherali',\n        rollNumber: '02',\n        department: 'Pre-Engineering',\n        class: '2nd Year',\n        section: 'B',\n        mobileNo: '**********',\n        registrationNo: 'REG002',\n        status: 'absent'\n      }, {\n        name: 'Alice Johnson',\n        fatherName: 'Osama',\n        rollNumber: '03',\n        department: 'Computer Science',\n        class: '1st Year',\n        section: 'C',\n        mobileNo: '**********',\n        registrationNo: 'REG003',\n        status: 'present'\n      }, {\n        name: 'Bob Lee',\n        fatherName: 'Ahmad',\n        rollNumber: '04',\n        department: 'Pre-Medical',\n        class: '2nd Year',\n        section: 'A',\n        mobileNo: '**********',\n        registrationNo: 'REG004',\n        status: 'present'\n      }, {\n        name: 'Mary Williams',\n        fatherName: 'Sherali',\n        rollNumber: '05',\n        department: 'Pre-Engineering',\n        class: '1st Year',\n        section: 'B',\n        mobileNo: '**********',\n        registrationNo: 'REG005',\n        status: 'absent'\n      }, {\n        name: 'Steve Brown',\n        fatherName: 'Osama',\n        rollNumber: '06',\n        department: 'Computer Science',\n        class: '2nd Year',\n        section: 'C',\n        mobileNo: '**********',\n        registrationNo: 'REG006',\n        status: 'onleave'\n      }, {\n        name: 'Sarah Davis',\n        fatherName: 'Ahmad',\n        rollNumber: '07',\n        department: 'Pre-Medical',\n        class: '1st Year',\n        section: 'A',\n        mobileNo: '**********',\n        registrationNo: 'REG007',\n        status: 'absent'\n      }, {\n        name: 'Michael Miller',\n        fatherName: 'Sherali',\n        rollNumber: '08',\n        department: 'Pre-Engineering',\n        class: '2nd Year',\n        section: 'B',\n        mobileNo: '**********',\n        registrationNo: 'REG008',\n        status: 'present'\n      }, {\n        name: 'David Wilson',\n        fatherName: 'Osama',\n        rollNumber: '09',\n        department: 'Computer Science',\n        class: '1st Year',\n        section: 'C',\n        mobileNo: '**********',\n        registrationNo: 'REG009',\n        status: 'onleave'\n      }, {\n        name: 'Linda Moore',\n        fatherName: 'Ahmad',\n        rollNumber: '10',\n        department: 'Pre-Medical',\n        class: '2nd Year',\n        section: 'A',\n        mobileNo: '**********',\n        registrationNo: 'REG010',\n        status: 'present'\n      }];\n    }\n    // Filter students based on search query, selected class, department, and section\n    get filteredStudents() {\n      return this.students.filter(student => {\n        const searchLower = this.searchQuery.toLowerCase();\n        const matchesName = student.name.toLowerCase().includes(searchLower);\n        const matchesRollNumber = student.rollNumber.toLowerCase().includes(searchLower);\n        const matchesStatus = student.status.toLowerCase().includes(searchLower);\n        const matchesDepartment = student.department.toLowerCase().includes(searchLower);\n        const matchesClass = student.class.toLowerCase().includes(searchLower);\n        const matchesSection = student.section.toLowerCase().includes(searchLower);\n        const matchesFilters = (this.selectedClass ? student.class === this.selectedClass : true) && (this.selectedDepartment ? student.department === this.selectedDepartment : true) && (this.selectedSection ? student.section === this.selectedSection : true);\n        return (matchesName || matchesRollNumber || matchesStatus || matchesDepartment || matchesClass || matchesSection) && matchesFilters;\n      });\n    }\n    // Reset filters\n    resetFilters() {\n      this.selectedClass = '';\n      this.selectedDepartment = '';\n      this.selectedSection = '';\n      this.searchQuery = '';\n    }\n    // Toggle status based on checkbox\n    toggleStatus(student, isChecked) {\n      student.status = isChecked ? 'present' : 'absent';\n    }\n    addnewstudent() {\n      this.router.navigate(['/dashboard/admin/students/add-student']);\n    }\n    get paginatedStudents() {\n      const startIndex = (this.currentPage - 1) * this.itemsPerPage;\n      const endIndex = startIndex + this.itemsPerPage;\n      return this.filteredStudents.slice(startIndex, endIndex);\n    }\n    get totalPages() {\n      return Math.ceil(this.filteredStudents.length / this.itemsPerPage);\n    }\n    nextPage() {\n      if (this.currentPage < this.totalPages) {\n        this.currentPage++;\n      }\n    }\n    previousPage() {\n      if (this.currentPage > 1) {\n        this.currentPage--;\n      }\n    }\n    static #_ = this.ɵfac = function AttendanceComponent_Factory(t) {\n      return new (t || AttendanceComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AttendanceComponent,\n      selectors: [[\"app-attendance\"]],\n      decls: 46,\n      vars: 6,\n      consts: [[1, \"maindiv\"], [1, \"container\"], [\"for\", \"search\", 1, \"mb-2\"], [\"type\", \"text\", \"placeholder\", \"Search by roll number, name, department, class, section, or status...\", 1, \"form-control\", \"mb-3\", 3, \"ngModel\", \"ngModelChange\"], [1, \"table-container\", \"mb-4\"], [1, \"table-responsive\"], [1, \"tablehead\"], [1, \"one\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"p-3\", \"align-items-center\", \"justify-content-between\"], [1, \"btn\", \"btn-top\", \"border\", 3, \"disabled\", \"click\"], [1, \"page\"], [1, \"d-flex\", \"justify-content-end\"], [1, \"btn\", \"btn-success\", \"d-flex\", \"justify-content-end\"], [\"data-label\", \"Name\"], [\"data-label\", \"Roll Number\"], [\"data-label\", \"Father's Name\"], [\"data-label\", \"Department\"], [\"data-label\", \"Class\"], [\"data-label\", \"section\"], [\"data-label\", \"Reg-No\"], [\"data-label\", \"Mobile-No\"], [3, \"ngClass\"]],\n      template: function AttendanceComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n          i0.ɵɵtext(3, \"Attendance Form\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"label\", 2);\n          i0.ɵɵtext(5, \"Search\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"input\", 3);\n          i0.ɵɵlistener(\"ngModelChange\", function AttendanceComponent_Template_input_ngModelChange_6_listener($event) {\n            return ctx.searchQuery = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 4)(8, \"div\", 5)(9, \"table\")(10, \"thead\")(11, \"tr\", 6)(12, \"th\");\n          i0.ɵɵtext(13, \"Roll Number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"th\", 7);\n          i0.ɵɵtext(15, \"Name \");\n          i0.ɵɵelementStart(16, \"mat-icon\");\n          i0.ɵɵtext(17, \"arrow_downward\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"th\");\n          i0.ɵɵtext(19, \"Father's Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"th\");\n          i0.ɵɵtext(21, \"Department\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"th\");\n          i0.ɵɵtext(23, \"Class\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"th\");\n          i0.ɵɵtext(25, \"Section\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"th\");\n          i0.ɵɵtext(27, \"Mobile No\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"th\");\n          i0.ɵɵtext(29, \"Registration No\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"th\");\n          i0.ɵɵtext(31, \"Status\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(32, \"tbody\");\n          i0.ɵɵtemplate(33, AttendanceComponent_tr_33_Template, 19, 14, \"tr\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(34, \"div\", 9)(35, \"div\")(36, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function AttendanceComponent_Template_button_click_36_listener() {\n            return ctx.previousPage();\n          });\n          i0.ɵɵtext(37, \"Previous\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"div\", 11);\n          i0.ɵɵtext(39);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"div\")(41, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function AttendanceComponent_Template_button_click_41_listener() {\n            return ctx.nextPage();\n          });\n          i0.ɵɵtext(42, \"Next\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(43, \"div\", 12)(44, \"button\", 13);\n          i0.ɵɵtext(45, \"save\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance(27);\n          i0.ɵɵproperty(\"ngForOf\", ctx.paginatedStudents);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.currentPage === 1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate2(\"Page \", ctx.currentPage, \" of \", ctx.totalPages, \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.currentPage === ctx.totalPages);\n        }\n      },\n      dependencies: [i2.NgClass, i2.NgForOf, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, i4.MatIcon]\n    });\n  }\n  return AttendanceComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}