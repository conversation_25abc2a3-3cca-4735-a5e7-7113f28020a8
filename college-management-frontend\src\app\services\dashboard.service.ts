import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { PrincipalDashboard, TeacherDashboard, StudentDashboard } from '../models/dashboard';

@Injectable({
  providedIn: 'root'
})
export class DashboardService {
  private apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) { }

  // Principal Dashboard
  getPrincipalDashboardStats(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/dashboard/principal/stats`);
  }

  getAllUsersForPrincipal(filters?: {
    role?: string;
    program?: string;
    department?: string;
    semester?: number;
    isActive?: boolean;
    page?: number;
    limit?: number;
    search?: string;
  }): Observable<any> {
    let params = new HttpParams();
    
    if (filters) {
      Object.keys(filters).forEach(key => {
        const value = (filters as any)[key];
        if (value !== undefined && value !== null) {
          params = params.set(key, value.toString());
        }
      });
    }

    return this.http.get<any>(`${this.apiUrl}/dashboard/principal/users`, { params });
  }

  getAttendanceSummary(filters?: {
    program?: string;
    department?: string;
    class?: string;
    subject?: string;
    startDate?: string;
    endDate?: string;
    page?: number;
    limit?: number;
  }): Observable<any> {
    let params = new HttpParams();
    
    if (filters) {
      Object.keys(filters).forEach(key => {
        const value = (filters as any)[key];
        if (value !== undefined && value !== null) {
          params = params.set(key, value.toString());
        }
      });
    }

    return this.http.get<any>(`${this.apiUrl}/dashboard/principal/attendance`, { params });
  }

  // Teacher Dashboard
  getTeacherDashboard(teacherId: string, academicYear?: string): Observable<any> {
    let params = new HttpParams();
    if (academicYear) params = params.set('academicYear', academicYear);

    return this.http.get<any>(`${this.apiUrl}/dashboard/teacher/${teacherId}`, { params });
  }

  // Student Dashboard
  getStudentDashboard(studentId: string, academicYear?: string): Observable<any> {
    let params = new HttpParams();
    if (academicYear) params = params.set('academicYear', academicYear);

    return this.http.get<any>(`${this.apiUrl}/dashboard/student/${studentId}`, { params });
  }
}
