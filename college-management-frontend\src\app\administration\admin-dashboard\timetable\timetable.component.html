<div class="timetable-container">
  <!-- Header -->
  <div class="page-header">
    <h1>Timetable Management</h1>
    <div class="header-actions">
      <button mat-icon-button (click)="loadTimetables()" matTooltip="Refresh">
        <mat-icon>refresh</mat-icon>
      </button>
    </div>
  </div>

  <!-- Create/Edit Timetable Form -->
  <mat-card class="form-card">
    <mat-card-header>
      <mat-card-title>
        {{ editingTimetableId ? 'Edit Timetable Entry' : 'Create Timetable Entry' }}
      </mat-card-title>
      <mat-card-subtitle>
        {{ editingTimetableId ? 'Update existing class session' : 'Schedule a new class session' }}
      </mat-card-subtitle>
    </mat-card-header>
    <mat-card-content>
      <form [formGroup]="timetableForm" (ngSubmit)="onSubmit()">
        <div class="form-grid">
          <!-- Program Selection -->
          <mat-form-field appearance="outline">
            <mat-label>Program</mat-label>
            <mat-select formControlName="program" required>
              <mat-option value="">Select Program</mat-option>
              <mat-option *ngFor="let program of programs" [value]="program._id">
                {{ program.name }} - {{ program.fullName }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="timetableForm.get('program')?.hasError('required')">
              Program is required
            </mat-error>
          </mat-form-field>

          <!-- Department Selection -->
          <mat-form-field appearance="outline">
            <mat-label>Department</mat-label>
            <mat-select formControlName="department" required [disabled]="!timetableForm.get('program')?.value">
              <mat-option value="">{{ !timetableForm.get('program')?.value ? 'Select Program First' : 'Select Department' }}</mat-option>
              <mat-option *ngFor="let department of filteredDepartments" [value]="department._id">
                {{ department.name }} ({{ department.code }})
              </mat-option>
            </mat-select>
            <mat-error *ngIf="timetableForm.get('department')?.hasError('required')">
              Department is required
            </mat-error>
          </mat-form-field>

          <!-- Semester Selection -->
          <mat-form-field appearance="outline">
            <mat-label>{{ isIntermediateProgram() ? 'Academic Year' : 'Semester' }}</mat-label>
            <mat-select formControlName="semester" required>
              <mat-option value="">{{ isIntermediateProgram() ? 'Select Year' : 'Select Semester' }}</mat-option>
              <!-- For Intermediate Programs (Year-based) -->
              <ng-container *ngIf="isIntermediateProgram()">
                <mat-option value="1">1st Year</mat-option>
                <mat-option value="2">2nd Year</mat-option>
              </ng-container>
              <!-- For BS/MS Programs (Semester-based) -->
              <ng-container *ngIf="!isIntermediateProgram()">
                <mat-option value="1">1st Semester</mat-option>
                <mat-option value="2">2nd Semester</mat-option>
                <mat-option value="3">3rd Semester</mat-option>
                <mat-option value="4">4th Semester</mat-option>
                <mat-option value="5">5th Semester</mat-option>
                <mat-option value="6">6th Semester</mat-option>
                <mat-option value="7">7th Semester</mat-option>
                <mat-option value="8">8th Semester</mat-option>
              </ng-container>
            </mat-select>
            <mat-error *ngIf="timetableForm.get('semester')?.hasError('required')">
              {{ isIntermediateProgram() ? 'Year' : 'Semester' }} is required
            </mat-error>
          </mat-form-field>

          <!-- Class Selection -->
          <mat-form-field appearance="outline">
            <mat-label>Class</mat-label>
            <mat-select formControlName="class" required [disabled]="!timetableForm.get('department')?.value">
              <mat-option value="">{{ !timetableForm.get('department')?.value ? 'Select Department First' : 'Select Class' }}</mat-option>
              <mat-option *ngFor="let class of filteredClasses" [value]="class._id">
                {{ class.className }}{{ class.section ? ' - Section ' + class.section : '' }}
                ({{ class?.program?.name }} - {{ class?.department?.name }})
              </mat-option>
            </mat-select>
            <mat-error *ngIf="timetableForm.get('class')?.hasError('required')">
              Class is required
            </mat-error>
          </mat-form-field>

          <!-- Subject Selection -->
          <mat-form-field appearance="outline">
            <mat-label>Subject</mat-label>
            <mat-select formControlName="subject" required [disabled]="!timetableForm.get('department')?.value">
              <mat-option value="">{{ !timetableForm.get('department')?.value ? 'Select Department First' : 'Select Subject' }}</mat-option>
              <mat-option *ngFor="let subject of filteredSubjects" [value]="subject._id">
                {{ subject.subjectName }} ({{ subject.code }})
              </mat-option>
            </mat-select>
            <mat-error *ngIf="timetableForm.get('subject')?.hasError('required')">
              Subject is required
            </mat-error>
          </mat-form-field>

          <!-- Teacher Selection -->
          <mat-form-field appearance="outline">
            <mat-label>Teacher</mat-label>
            <mat-select formControlName="teacher" required [disabled]="!timetableForm.get('program')?.value">
              <mat-option value="">{{ !timetableForm.get('program')?.value ? 'Select Program First' : 'Select Teacher' }}</mat-option>
              <mat-option *ngFor="let teacher of filteredTeachers" [value]="teacher._id">
                {{ teacher.name }} - {{ teacher.designation || 'Teacher' }}
                <small *ngIf="teacher.department">({{ teacher.department.name }})</small>
              </mat-option>
            </mat-select>
            <mat-error *ngIf="timetableForm.get('teacher')?.hasError('required')">
              Teacher is required
            </mat-error>
          </mat-form-field>

          <!-- Day of Week -->
          <mat-form-field appearance="outline">
            <mat-label>Day of Week</mat-label>
            <mat-select formControlName="dayOfWeek" required>
              <mat-option *ngFor="let day of daysOfWeek" [value]="day">
                {{ day }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="timetableForm.get('dayOfWeek')?.hasError('required')">
              Day is required
            </mat-error>
          </mat-form-field>

          <!-- Start Time -->
          <mat-form-field appearance="outline">
            <mat-label>Start Time</mat-label>
            <mat-select formControlName="startTime" required>
              <mat-option *ngFor="let time of availableStartTimes" [value]="time">
                {{ time }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="timetableForm.get('startTime')?.hasError('required')">
              Start time is required
            </mat-error>
          </mat-form-field>

          <!-- Duration -->
          <mat-form-field appearance="outline">
            <mat-label>Class Duration</mat-label>
            <mat-select formControlName="duration" required>
              <mat-option value="">Select Duration</mat-option>
              <mat-option *ngFor="let duration of availableDurations" [value]="duration.value">
                {{ duration.label }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="timetableForm.get('duration')?.hasError('required')">
              Duration is required
            </mat-error>
            <mat-hint *ngIf="!selectedProgram">Select a program first to see duration options</mat-hint>
          </mat-form-field>

          <!-- End Time (Auto-calculated) -->
          <mat-form-field appearance="outline">
            <mat-label>End Time (Auto-calculated)</mat-label>
            <input matInput formControlName="endTime" readonly>
            <mat-hint>Automatically calculated based on start time and duration</mat-hint>
          </mat-form-field>

          <!-- Room -->
          <mat-form-field appearance="outline">
            <mat-label>Room (Optional)</mat-label>
            <input matInput formControlName="room" placeholder="e.g., Room 101">
          </mat-form-field>

          <!-- Academic Year -->
          <mat-form-field appearance="outline">
            <mat-label>Academic Year</mat-label>
            <input matInput formControlName="academicYear" placeholder="e.g., 2024-2025" required>
            <mat-error *ngIf="timetableForm.get('academicYear')?.hasError('required')">
              Academic year is required
            </mat-error>
          </mat-form-field>

          <!-- Notes -->
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Notes (Optional)</mat-label>
            <textarea matInput formControlName="notes" rows="3" placeholder="Additional notes..."></textarea>
          </mat-form-field>
        </div>

        <div class="form-actions">
          <button mat-raised-button type="button" (click)="timetableForm.reset()" *ngIf="!editingTimetableId">
            <mat-icon>clear</mat-icon>
            Clear
          </button>
          <button mat-raised-button color="primary" type="submit" [disabled]="!timetableForm.valid || submitting">
            <mat-icon>save</mat-icon>
            {{ submitting ? (editingTimetableId ? 'Updating...' : 'Creating...') : (editingTimetableId ? 'Update Entry' : 'Create Entry') }}
          </button>
          <button mat-button type="button" (click)="cancelEdit()" *ngIf="editingTimetableId" color="warn">
            <mat-icon>cancel</mat-icon>
            Cancel Edit
          </button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>

  <!-- Existing Timetables -->
  <mat-card class="table-card">
    <mat-card-header>
      <mat-card-title>Current Timetable</mat-card-title>
      <mat-card-subtitle>All scheduled classes</mat-card-subtitle>
    </mat-card-header>
    <mat-card-content>
      <div *ngIf="loading" class="loading-container">
        <mat-spinner></mat-spinner>
        <p>Loading timetables...</p>
      </div>

      <div *ngIf="!loading && timetables.length > 0" class="table-container">
        <table mat-table [dataSource]="timetables" class="timetable-table">
          <!-- Day Column -->
          <ng-container matColumnDef="dayOfWeek">
            <th mat-header-cell *matHeaderCellDef>Day</th>
            <td mat-cell *matCellDef="let timetable">{{ timetable.dayOfWeek }}</td>
          </ng-container>

          <!-- Schedule Info Column -->
          <ng-container matColumnDef="scheduleInfo">
            <th mat-header-cell *matHeaderCellDef>Schedule Details</th>
            <td mat-cell *matCellDef="let timetable">
              <div class="schedule-info">
                <div class="teacher-class">
                  <strong>{{ timetable.teacher?.name || 'Unknown Teacher' }}</strong> with
                  <strong>{{ getClassDisplayName(timetable.class) }}</strong>
                </div>
                <div class="subject-info">
                  <span class="subject">{{ timetable.subject?.subjectName || 'Unknown Subject' }}</span>
                  <span class="department text-muted">({{ timetable.department?.name || 'Unknown Dept' }})</span>
                </div>
                <div class="semester-info">
                  <small class="text-info">
                    {{ isIntermediateProgram() && timetable.program?.name === 'Intermediate' ?
                        getSemesterDisplayText(timetable.semester) + ' Year' :
                        getSemesterDisplayText(timetable.semester) + ' Semester' }}
                    - {{ timetable.academicYear }}
                  </small>
                </div>
              </div>
            </td>
          </ng-container>

          <!-- Time Slot Column -->
          <ng-container matColumnDef="timeSlot">
            <th mat-header-cell *matHeaderCellDef>Time & Duration</th>
            <td mat-cell *matCellDef="let timetable">
              <div>{{ formatTimeSlot(timetable.timeSlot) }}</div>
              <small class="text-muted">{{ getDurationLabel(timetable.timeSlot.duration) }}</small>
            </td>
          </ng-container>

          <!-- Program Column -->
          <ng-container matColumnDef="program">
            <th mat-header-cell *matHeaderCellDef>Program</th>
            <td mat-cell *matCellDef="let timetable">{{ timetable.program.name }}</td>
          </ng-container>



          <!-- Room Column -->
          <ng-container matColumnDef="room">
            <th mat-header-cell *matHeaderCellDef>Room</th>
            <td mat-cell *matCellDef="let timetable">{{ timetable.room || 'N/A' }}</td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let timetable">
              <div class="action-buttons">
                <button mat-icon-button color="primary" (click)="editTimetableEntry(timetable)" matTooltip="Edit">
                  <mat-icon>edit</mat-icon>
                </button>
                <button mat-icon-button color="warn" (click)="deleteTimetableEntry(timetable)" matTooltip="Delete">
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
      </div>

      <div *ngIf="!loading && timetables.length === 0" class="no-data">
        <mat-icon>schedule</mat-icon>
        <h3>No Timetable Entries</h3>
        <p>Create your first timetable entry using the form above.</p>
      </div>
    </mat-card-content>
  </mat-card>
</div>
