{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MaterialModule } from 'src/app/material';\nimport { TeacherDashboardRoutingModule } from './teacher-dashboard-routing.module';\nimport * as i0 from \"@angular/core\";\nexport let TeacherDashboardModule = /*#__PURE__*/(() => {\n  class TeacherDashboardModule {\n    static #_ = this.ɵfac = function TeacherDashboardModule_Factory(t) {\n      return new (t || TeacherDashboardModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: TeacherDashboardModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, TeacherDashboardRoutingModule, MaterialModule]\n    });\n  }\n  return TeacherDashboardModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}