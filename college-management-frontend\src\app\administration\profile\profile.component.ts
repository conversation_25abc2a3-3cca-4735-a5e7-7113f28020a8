import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { UserService } from '../../services/user.service';
import { ProgramService } from '../../services/program.service';
import { DepartmentService } from '../../services/department.service';
import { ClassesService } from '../../services/classes.service';
import { User, Program, Department, Class } from '../../models/user';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.css']
})
export class ProfileComponent implements OnInit {
  profileForm: FormGroup;
  passwordForm: FormGroup;
  currentUser: User | null = null;
  loading = false;
  updating = false;
  changingPassword = false;
  
  // Data for dropdowns
  programs: Program[] = [];
  departments: Department[] = [];
  classes: Class[] = [];
  filteredDepartments: Department[] = [];
  filteredClasses: Class[] = [];

  // Tab management
  selectedTabIndex = 0;

  constructor(
    private fb: FormBuilder,
    private userService: UserService,
    private programService: ProgramService,
    private departmentService: DepartmentService,
    private classesService: ClassesService,
    private snackBar: MatSnackBar
  ) {
    this.profileForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      contact: [''],
      address: [''],
      father_name: [''],
      regNo: [''],
      rollNo: [''],
      program: [''],
      department: [''],
      classId: [''],
      semester: [''],
      academicYear: [''],
      designation: [''],
      position: [''],
      isVisiting: [false]
    });

    this.passwordForm = this.fb.group({
      currentPassword: ['', [Validators.required]],
      newPassword: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', [Validators.required]]
    }, { validators: this.passwordMatchValidator });
  }

  ngOnInit(): void {
    this.loadCurrentUser();
    this.loadDropdownData();
  }

  loadCurrentUser(): void {
    this.loading = true;
    const userData = this.userService.getUserFromLocalStorage();
    
    if (userData?.user?._id) {
      this.userService.getUserProfile(userData.user._id).subscribe({
        next: (response) => {
          if (response.success) {
            this.currentUser = response.user;
            this.populateForm();
          }
          this.loading = false;
        },
        error: (error) => {
          console.error('Error loading profile:', error);
          this.snackBar.open('Error loading profile', 'Close', { duration: 3000 });
          this.loading = false;
        }
      });
    } else {
      this.loading = false;
      this.snackBar.open('User not found', 'Close', { duration: 3000 });
    }
  }

  loadDropdownData(): void {
    // Load programs
    this.programService.getAllPrograms(true).subscribe({
      next: (response) => {
        if (response.success) {
          this.programs = response.programs;
        }
      },
      error: (error) => console.error('Error loading programs:', error)
    });

    // Load departments
    this.departmentService.getAllDepartments({ isActive: true }).subscribe({
      next: (response) => {
        if (response.success) {
          this.departments = response.departments;
          this.filteredDepartments = this.departments;
        }
      },
      error: (error) => console.error('Error loading departments:', error)
    });

    // Load classes
    this.classesService.getAllClasses({ isActive: true }).subscribe({
      next: (response) => {
        if (response.success) {
          this.classes = response.classes;
          this.filteredClasses = this.classes;
        }
      },
      error: (error) => console.error('Error loading classes:', error)
    });
  }

  populateForm(): void {
    if (this.currentUser) {
      this.profileForm.patchValue({
        name: this.currentUser.name,
        email: this.currentUser.email,
        contact: this.currentUser.contact,
        address: this.currentUser.address,
        father_name: this.currentUser.father_name,
        regNo: this.currentUser.regNo,
        rollNo: this.currentUser.rollNo,
        program: this.currentUser.program?._id,
        department: this.currentUser.department?._id,
        classId: typeof this.currentUser.classId === 'object' ? this.currentUser.classId._id : this.currentUser.classId,
        semester: this.currentUser.semester,
        academicYear: this.currentUser.academicYear,
        designation: this.currentUser.designation,
        position: this.currentUser.position,
        isVisiting: this.currentUser.isVisiting
      });

      // Filter departments and classes based on program
      this.onProgramChange();
    }
  }

  onProgramChange(): void {
    const selectedProgram = this.profileForm.get('program')?.value;
    if (selectedProgram) {
      this.filteredDepartments = this.departments.filter(dept => 
        dept.program._id === selectedProgram
      );
      this.filteredClasses = this.classes.filter(cls => 
        cls.program._id === selectedProgram
      );
    } else {
      this.filteredDepartments = this.departments;
      this.filteredClasses = this.classes;
    }
    
    // Reset department and class if they're not valid for the new program
    const currentDept = this.profileForm.get('department')?.value;
    const currentClass = this.profileForm.get('classId')?.value;
    
    if (currentDept && !this.filteredDepartments.find(d => d._id === currentDept)) {
      this.profileForm.patchValue({ department: '', classId: '' });
    }
    
    if (currentClass && !this.filteredClasses.find(c => c._id === currentClass)) {
      this.profileForm.patchValue({ classId: '' });
    }
  }

  onDepartmentChange(): void {
    const selectedDepartment = this.profileForm.get('department')?.value;
    const selectedProgram = this.profileForm.get('program')?.value;
    
    if (selectedDepartment && selectedProgram) {
      this.filteredClasses = this.classes.filter(cls => 
        cls.program._id === selectedProgram && cls.department._id === selectedDepartment
      );
      
      // Reset class if it's not valid for the new department
      const currentClass = this.profileForm.get('classId')?.value;
      if (currentClass && !this.filteredClasses.find(c => c._id === currentClass)) {
        this.profileForm.patchValue({ classId: '' });
      }
    }
  }

  updateProfile(): void {
    if (this.profileForm.invalid || !this.currentUser) {
      this.markFormGroupTouched(this.profileForm);
      return;
    }

    this.updating = true;
    const formData = this.profileForm.value;

    this.userService.updateUserProfile(this.currentUser._id, formData).subscribe({
      next: (response) => {
        if (response.success) {
          this.currentUser = response.user;
          this.snackBar.open('Profile updated successfully', 'Close', { duration: 3000 });
          
          // Update local storage with new user data
          const userData = this.userService.getUserFromLocalStorage();
          if (userData) {
            userData.user = response.user;
            localStorage.setItem('user', JSON.stringify(userData));
          }
        }
        this.updating = false;
      },
      error: (error) => {
        console.error('Error updating profile:', error);
        this.snackBar.open('Error updating profile', 'Close', { duration: 3000 });
        this.updating = false;
      }
    });
  }

  changePassword(): void {
    if (this.passwordForm.invalid || !this.currentUser) {
      this.markFormGroupTouched(this.passwordForm);
      return;
    }

    this.changingPassword = true;
    const { currentPassword, newPassword } = this.passwordForm.value;

    this.userService.changePassword(this.currentUser._id, { currentPassword, newPassword }).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open('Password changed successfully', 'Close', { duration: 3000 });
          this.passwordForm.reset();
        }
        this.changingPassword = false;
      },
      error: (error) => {
        console.error('Error changing password:', error);
        this.snackBar.open('Error changing password', 'Close', { duration: 3000 });
        this.changingPassword = false;
      }
    });
  }

  passwordMatchValidator(form: FormGroup) {
    const newPassword = form.get('newPassword');
    const confirmPassword = form.get('confirmPassword');
    
    if (newPassword && confirmPassword && newPassword.value !== confirmPassword.value) {
      confirmPassword.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    }
    
    return null;
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

  getErrorMessage(fieldName: string, form: FormGroup = this.profileForm): string {
    const control = form.get(fieldName);
    if (control?.hasError('required')) {
      return `${fieldName} is required`;
    }
    if (control?.hasError('email')) {
      return 'Please enter a valid email';
    }
    if (control?.hasError('minlength')) {
      const minLength = control.errors?.['minlength']?.requiredLength;
      return `${fieldName} must be at least ${minLength} characters`;
    }
    if (control?.hasError('passwordMismatch')) {
      return 'Passwords do not match';
    }
    return '';
  }

  isStudent(): boolean {
    return this.currentUser?.role === 'Student';
  }

  isTeacher(): boolean {
    return this.currentUser?.role === 'Teacher';
  }

  isPrincipal(): boolean {
    return this.currentUser?.role === 'Principal';
  }
}
