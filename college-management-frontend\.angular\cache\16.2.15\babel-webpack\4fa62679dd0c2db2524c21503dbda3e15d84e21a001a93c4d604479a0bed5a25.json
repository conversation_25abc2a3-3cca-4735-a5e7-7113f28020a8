{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class TimetableService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = environment.apiUrl;\n  }\n  // Create new timetable entry\n  createTimetableEntry(timetable) {\n    return this.http.post(`${this.apiUrl}/timetable`, timetable);\n  }\n  // Get all timetable entries with filters\n  getAllTimetableEntries(filters) {\n    let params = new HttpParams();\n    if (filters) {\n      Object.keys(filters).forEach(key => {\n        const value = filters[key];\n        if (value !== undefined && value !== null) {\n          params = params.set(key, value.toString());\n        }\n      });\n    }\n    return this.http.get(`${this.apiUrl}/timetables`, {\n      params\n    });\n  }\n  // Get timetable by teacher\n  getTimetableByTeacher(teacherId, academicYear, dayOfWeek) {\n    let params = new HttpParams();\n    if (academicYear) params = params.set('academicYear', academicYear);\n    if (dayOfWeek) params = params.set('dayOfWeek', dayOfWeek);\n    return this.http.get(`${this.apiUrl}/timetable/teacher/${teacherId}`, {\n      params\n    });\n  }\n  // Get timetable by class\n  getTimetableByClass(classId, academicYear, dayOfWeek) {\n    let params = new HttpParams();\n    if (academicYear) params = params.set('academicYear', academicYear);\n    if (dayOfWeek) params = params.set('dayOfWeek', dayOfWeek);\n    return this.http.get(`${this.apiUrl}/timetable/class/${classId}`, {\n      params\n    });\n  }\n  // Get timetable by student\n  getTimetableByStudent(studentId, academicYear, dayOfWeek) {\n    let params = new HttpParams();\n    if (academicYear) params = params.set('academicYear', academicYear);\n    if (dayOfWeek) params = params.set('dayOfWeek', dayOfWeek);\n    return this.http.get(`${this.apiUrl}/timetable/student/${studentId}`, {\n      params\n    });\n  }\n  // Get timetable entry by ID\n  getTimetableEntryById(id) {\n    return this.http.get(`${this.apiUrl}/timetable/${id}`);\n  }\n  // Update timetable entry\n  updateTimetableEntry(id, timetable) {\n    return this.http.put(`${this.apiUrl}/timetable/${id}`, timetable);\n  }\n  // Delete timetable entry\n  deleteTimetableEntry(id) {\n    return this.http.delete(`${this.apiUrl}/timetable/${id}`);\n  }\n  static #_ = this.ɵfac = function TimetableService_Factory(t) {\n    return new (t || TimetableService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: TimetableService,\n    factory: TimetableService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpParams", "environment", "TimetableService", "constructor", "http", "apiUrl", "createTimetableEntry", "timetable", "post", "getAllTimetableEntries", "filters", "params", "Object", "keys", "for<PERSON>ach", "key", "value", "undefined", "set", "toString", "get", "getTimetableByTeacher", "teacherId", "academicYear", "dayOfWeek", "getTimetableByClass", "classId", "getTimetableByStudent", "studentId", "getTimetableEntryById", "id", "updateTimetableEntry", "put", "deleteTimetableEntry", "delete", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\services\\timetable.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { environment } from 'src/environments/environment';\r\nimport { Timetable, TimetableCreateRequest } from '../models/timetable';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class TimetableService {\r\n  private apiUrl = environment.apiUrl;\r\n\r\n  constructor(private http: HttpClient) { }\r\n\r\n  // Create new timetable entry\r\n  createTimetableEntry(timetable: TimetableCreateRequest): Observable<any> {\r\n    return this.http.post<any>(`${this.apiUrl}/timetable`, timetable);\r\n  }\r\n\r\n  // Get all timetable entries with filters\r\n  getAllTimetableEntries(filters?: {\r\n    program?: string;\r\n    department?: string;\r\n    class?: string;\r\n    teacher?: string;\r\n    dayOfWeek?: string;\r\n    academicYear?: string;\r\n    semester?: number;\r\n    isActive?: boolean;\r\n  }): Observable<any> {\r\n    let params = new HttpParams();\r\n    \r\n    if (filters) {\r\n      Object.keys(filters).forEach(key => {\r\n        const value = (filters as any)[key];\r\n        if (value !== undefined && value !== null) {\r\n          params = params.set(key, value.toString());\r\n        }\r\n      });\r\n    }\r\n\r\n    return this.http.get<any>(`${this.apiUrl}/timetables`, { params });\r\n  }\r\n\r\n  // Get timetable by teacher\r\n  getTimetableByTeacher(teacherId: string, academicYear?: string, dayOfWeek?: string): Observable<any> {\r\n    let params = new HttpParams();\r\n    if (academicYear) params = params.set('academicYear', academicYear);\r\n    if (dayOfWeek) params = params.set('dayOfWeek', dayOfWeek);\r\n\r\n    return this.http.get<any>(`${this.apiUrl}/timetable/teacher/${teacherId}`, { params });\r\n  }\r\n\r\n  // Get timetable by class\r\n  getTimetableByClass(classId: string, academicYear?: string, dayOfWeek?: string): Observable<any> {\r\n    let params = new HttpParams();\r\n    if (academicYear) params = params.set('academicYear', academicYear);\r\n    if (dayOfWeek) params = params.set('dayOfWeek', dayOfWeek);\r\n\r\n    return this.http.get<any>(`${this.apiUrl}/timetable/class/${classId}`, { params });\r\n  }\r\n\r\n  // Get timetable by student\r\n  getTimetableByStudent(studentId: string, academicYear?: string, dayOfWeek?: string): Observable<any> {\r\n    let params = new HttpParams();\r\n    if (academicYear) params = params.set('academicYear', academicYear);\r\n    if (dayOfWeek) params = params.set('dayOfWeek', dayOfWeek);\r\n\r\n    return this.http.get<any>(`${this.apiUrl}/timetable/student/${studentId}`, { params });\r\n  }\r\n\r\n  // Get timetable entry by ID\r\n  getTimetableEntryById(id: string): Observable<any> {\r\n    return this.http.get<any>(`${this.apiUrl}/timetable/${id}`);\r\n  }\r\n\r\n  // Update timetable entry\r\n  updateTimetableEntry(id: string, timetable: Partial<TimetableCreateRequest>): Observable<any> {\r\n    return this.http.put<any>(`${this.apiUrl}/timetable/${id}`, timetable);\r\n  }\r\n\r\n  // Delete timetable entry\r\n  deleteTimetableEntry(id: string): Observable<any> {\r\n    return this.http.delete<any>(`${this.apiUrl}/timetable/${id}`);\r\n  }\r\n}\r\n"], "mappings": "AACA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,WAAW,QAAQ,8BAA8B;;;AAM1D,OAAM,MAAOC,gBAAgB;EAG3BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAGJ,WAAW,CAACI,MAAM;EAEK;EAExC;EACAC,oBAAoBA,CAACC,SAAiC;IACpD,OAAO,IAAI,CAACH,IAAI,CAACI,IAAI,CAAM,GAAG,IAAI,CAACH,MAAM,YAAY,EAAEE,SAAS,CAAC;EACnE;EAEA;EACAE,sBAAsBA,CAACC,OAStB;IACC,IAAIC,MAAM,GAAG,IAAIX,UAAU,EAAE;IAE7B,IAAIU,OAAO,EAAE;MACXE,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC,CAACI,OAAO,CAACC,GAAG,IAAG;QACjC,MAAMC,KAAK,GAAIN,OAAe,CAACK,GAAG,CAAC;QACnC,IAAIC,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,EAAE;UACzCL,MAAM,GAAGA,MAAM,CAACO,GAAG,CAACH,GAAG,EAAEC,KAAK,CAACG,QAAQ,EAAE,CAAC;;MAE9C,CAAC,CAAC;;IAGJ,OAAO,IAAI,CAACf,IAAI,CAACgB,GAAG,CAAM,GAAG,IAAI,CAACf,MAAM,aAAa,EAAE;MAAEM;IAAM,CAAE,CAAC;EACpE;EAEA;EACAU,qBAAqBA,CAACC,SAAiB,EAAEC,YAAqB,EAAEC,SAAkB;IAChF,IAAIb,MAAM,GAAG,IAAIX,UAAU,EAAE;IAC7B,IAAIuB,YAAY,EAAEZ,MAAM,GAAGA,MAAM,CAACO,GAAG,CAAC,cAAc,EAAEK,YAAY,CAAC;IACnE,IAAIC,SAAS,EAAEb,MAAM,GAAGA,MAAM,CAACO,GAAG,CAAC,WAAW,EAAEM,SAAS,CAAC;IAE1D,OAAO,IAAI,CAACpB,IAAI,CAACgB,GAAG,CAAM,GAAG,IAAI,CAACf,MAAM,sBAAsBiB,SAAS,EAAE,EAAE;MAAEX;IAAM,CAAE,CAAC;EACxF;EAEA;EACAc,mBAAmBA,CAACC,OAAe,EAAEH,YAAqB,EAAEC,SAAkB;IAC5E,IAAIb,MAAM,GAAG,IAAIX,UAAU,EAAE;IAC7B,IAAIuB,YAAY,EAAEZ,MAAM,GAAGA,MAAM,CAACO,GAAG,CAAC,cAAc,EAAEK,YAAY,CAAC;IACnE,IAAIC,SAAS,EAAEb,MAAM,GAAGA,MAAM,CAACO,GAAG,CAAC,WAAW,EAAEM,SAAS,CAAC;IAE1D,OAAO,IAAI,CAACpB,IAAI,CAACgB,GAAG,CAAM,GAAG,IAAI,CAACf,MAAM,oBAAoBqB,OAAO,EAAE,EAAE;MAAEf;IAAM,CAAE,CAAC;EACpF;EAEA;EACAgB,qBAAqBA,CAACC,SAAiB,EAAEL,YAAqB,EAAEC,SAAkB;IAChF,IAAIb,MAAM,GAAG,IAAIX,UAAU,EAAE;IAC7B,IAAIuB,YAAY,EAAEZ,MAAM,GAAGA,MAAM,CAACO,GAAG,CAAC,cAAc,EAAEK,YAAY,CAAC;IACnE,IAAIC,SAAS,EAAEb,MAAM,GAAGA,MAAM,CAACO,GAAG,CAAC,WAAW,EAAEM,SAAS,CAAC;IAE1D,OAAO,IAAI,CAACpB,IAAI,CAACgB,GAAG,CAAM,GAAG,IAAI,CAACf,MAAM,sBAAsBuB,SAAS,EAAE,EAAE;MAAEjB;IAAM,CAAE,CAAC;EACxF;EAEA;EACAkB,qBAAqBA,CAACC,EAAU;IAC9B,OAAO,IAAI,CAAC1B,IAAI,CAACgB,GAAG,CAAM,GAAG,IAAI,CAACf,MAAM,cAAcyB,EAAE,EAAE,CAAC;EAC7D;EAEA;EACAC,oBAAoBA,CAACD,EAAU,EAAEvB,SAA0C;IACzE,OAAO,IAAI,CAACH,IAAI,CAAC4B,GAAG,CAAM,GAAG,IAAI,CAAC3B,MAAM,cAAcyB,EAAE,EAAE,EAAEvB,SAAS,CAAC;EACxE;EAEA;EACA0B,oBAAoBA,CAACH,EAAU;IAC7B,OAAO,IAAI,CAAC1B,IAAI,CAAC8B,MAAM,CAAM,GAAG,IAAI,CAAC7B,MAAM,cAAcyB,EAAE,EAAE,CAAC;EAChE;EAAC,QAAAK,CAAA,G;qBA3EUjC,gBAAgB,EAAAkC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAhBtC,gBAAgB;IAAAuC,OAAA,EAAhBvC,gBAAgB,CAAAwC,IAAA;IAAAC,UAAA,EAFf;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}