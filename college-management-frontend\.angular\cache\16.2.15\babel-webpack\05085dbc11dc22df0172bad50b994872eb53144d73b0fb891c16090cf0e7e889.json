{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\n// Angular Material Modules\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatTooltipModule } from '@angular/material/tooltip';\n// Components\nimport { SettingsComponent } from './settings.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: SettingsComponent\n}];\nexport class SettingsModule {\n  static #_ = this.ɵfac = function SettingsModule_Factory(t) {\n    return new (t || SettingsModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: SettingsModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, ReactiveFormsModule, FormsModule, RouterModule.forChild(routes),\n    // Angular Material\n    MatCardModule, MatButtonModule, MatIconModule, MatFormFieldModule, MatInputModule, MatSelectModule, MatTabsModule, MatSlideToggleModule, MatProgressSpinnerModule, MatSnackBarModule, MatTooltipModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SettingsModule, {\n    declarations: [SettingsComponent],\n    imports: [CommonModule, ReactiveFormsModule, FormsModule, i1.RouterModule,\n    // Angular Material\n    MatCardModule, MatButtonModule, MatIconModule, MatFormFieldModule, MatInputModule, MatSelectModule, MatTabsModule, MatSlideToggleModule, MatProgressSpinnerModule, MatSnackBarModule, MatTooltipModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "FormsModule", "RouterModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatInputModule", "MatSelectModule", "MatTabsModule", "MatSlideToggleModule", "MatProgressSpinnerModule", "MatSnackBarModule", "MatTooltipModule", "SettingsComponent", "routes", "path", "component", "SettingsModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\settings\\settings.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\r\nimport { RouterModule, Routes } from '@angular/router';\r\n\r\n// Angular Material Modules\r\nimport { MatCardModule } from '@angular/material/card';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { MatSelectModule } from '@angular/material/select';\r\nimport { MatTabsModule } from '@angular/material/tabs';\r\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\r\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\r\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\r\nimport { MatTooltipModule } from '@angular/material/tooltip';\r\n\r\n// Components\r\nimport { SettingsComponent } from './settings.component';\r\n\r\nconst routes: Routes = [\r\n  { path: '', component: SettingsComponent }\r\n];\r\n\r\n@NgModule({\r\n  declarations: [\r\n    SettingsComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    FormsModule,\r\n    RouterModule.forChild(routes),\r\n    \r\n    // Angular Material\r\n    MatCardModule,\r\n    MatButtonModule,\r\n    MatIconModule,\r\n    MatFormFieldModule,\r\n    MatInputModule,\r\n    MatSelectModule,\r\n    MatTabsModule,\r\n    MatSlideToggleModule,\r\n    MatProgressSpinnerModule,\r\n    MatSnackBarModule,\r\n    MatTooltipModule\r\n  ]\r\n})\r\nexport class SettingsModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAgB,iBAAiB;AAEtD;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,gBAAgB,QAAQ,2BAA2B;AAE5D;AACA,SAASC,iBAAiB,QAAQ,sBAAsB;;;AAExD,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEH;AAAiB,CAAE,CAC3C;AA0BD,OAAM,MAAOI,cAAc;EAAA,QAAAC,CAAA,G;qBAAdD,cAAc;EAAA;EAAA,QAAAE,EAAA,G;UAAdF;EAAc;EAAA,QAAAG,EAAA,G;cAnBvBtB,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,YAAY,CAACoB,QAAQ,CAACP,MAAM,CAAC;IAE7B;IACAZ,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,eAAe,EACfC,aAAa,EACbC,oBAAoB,EACpBC,wBAAwB,EACxBC,iBAAiB,EACjBC,gBAAgB;EAAA;;;2EAGPK,cAAc;IAAAK,YAAA,GAtBvBT,iBAAiB;IAAAU,OAAA,GAGjBzB,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EAAAwB,EAAA,CAAAvB,YAAA;IAGX;IACAC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,eAAe,EACfC,aAAa,EACbC,oBAAoB,EACpBC,wBAAwB,EACxBC,iBAAiB,EACjBC,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}