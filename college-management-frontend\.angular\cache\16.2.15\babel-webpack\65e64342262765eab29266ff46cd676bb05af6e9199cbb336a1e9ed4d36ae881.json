{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/role.service\";\nimport * as i4 from \"src/app/services/user.service\";\nimport * as i5 from \"@angular/common\";\nfunction LoginComponent_span_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sign in\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_span_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"span\", 31);\n    i0.ɵɵtext(2, \" Signing in... \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class LoginComponent {\n  constructor(fb, router, roleService, userService) {\n    this.fb = fb;\n    this.router = router;\n    this.roleService = roleService;\n    this.userService = userService;\n    this.showPassword = false;\n    this.loginError = ''; // To store error messages\n    this.loading = false;\n  }\n  ngOnInit() {\n    // Initialize form with validation\n    this.loginForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(6)]],\n      remember: [false]\n    });\n  }\n  onSubmit() {\n    // Reset the error message\n    this.loginError = '';\n    this.loading = true;\n    if (this.loginForm.valid) {\n      // Capture form values\n      const {\n        email,\n        password\n      } = this.loginForm.value;\n      // Call the UserService's login method\n      this.userService.login({\n        email,\n        password\n      }).subscribe({\n        next: res => {\n          if (res.success) {\n            console.log(res, \"role\");\n            let role = res.user.role;\n            console.log(role);\n            this.loading = false;\n            // Check role and perform role-based login\n            if (role === 'Principal') {\n              this.roleService.setRole('Principal');\n              console.log('Principal logged in');\n              this.router.navigate(['/dashboard/admin']);\n            } else if (role === 'Teacher') {\n              this.roleService.setRole('Teacher');\n              console.log('Teacher logged in');\n              this.router.navigate(['/dashboard/teacher']);\n            } else if (role === 'Student') {\n              this.roleService.setRole('Student');\n              console.log('Student logged in');\n              this.router.navigate(['/dashboard/student']);\n            } else {\n              this.loginError = 'Invalid credentials for the selected role';\n              console.log('Invalid credentials');\n              return; // Stop further execution\n            }\n            // Show success message\n            Swal.fire({\n              icon: 'success',\n              title: 'Login Successful',\n              text: `Welcome ${res.user.name}!`,\n              timer: 2000,\n              showConfirmButton: false\n            });\n          } else {\n            this.loginError = res.message || 'Login failed';\n            Swal.fire({\n              icon: 'error',\n              title: 'Login Failed',\n              text: this.loginError\n            });\n            this.loading = false;\n          }\n        },\n        error: error => {\n          console.error('Login failed', error);\n          Swal.fire({\n            icon: 'error',\n            title: 'Login Failed',\n            text: error.error.message\n          });\n          this.loginError = 'Server error, please try again later.';\n          this.loading = false;\n        }\n      });\n    } else {\n      // If the form is invalid, display an error message\n      this.loginError = 'Please fill in all fields correctly';\n      console.log('Form is invalid');\n      this.loading = false;\n    }\n  }\n  // Toggle the visibility of the password\n  togglePasswordVisibility() {\n    this.showPassword = !this.showPassword;\n  }\n  static #_ = this.ɵfac = function LoginComponent_Factory(t) {\n    return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.RoleService), i0.ɵɵdirectiveInject(i4.UserService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LoginComponent,\n    selectors: [[\"app-login\"]],\n    decls: 48,\n    vars: 6,\n    consts: [[1, \"container-fluid\"], [1, \"row\", \"vh-100\"], [1, \"col-lg-5\", \"d-flex\", \"flex-column\", \"justify-content-center\", \"align-items-center\", \"p-3\", \"bg-white\"], [1, \"w-100\", 2, \"max-width\", \"400px\"], [1, \"mb-4\"], [\"src\", \"../../../assets/images/logo.jpeg\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"mb-3\"], [\"for\", \"email\", 1, \"form-label\"], [\"type\", \"email\", \"id\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter your email\", 1, \"form-control\"], [1, \"mb-3\", \"position-relative\"], [\"for\", \"password\", 1, \"form-label\"], [\"id\", \"password\", \"formControlName\", \"password\", \"placeholder\", \"Enter your password\", 1, \"form-control\", 3, \"type\"], [1, \"fas\", \"viewpassword\", 3, \"ngClass\", \"click\"], [1, \"form-check\", \"mb-3\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"type\", \"checkbox\", \"formControlName\", \"remember\", \"id\", \"remember\", 1, \"form-check-input\"], [\"for\", \"remember\", 1, \"form-check-label\"], [\"routerLink\", \"/auth/forgot-password\", 1, \"mt-0\"], [1, \"forgot\", 2, \"cursor\", \"pointer\"], [1, \"d-grid\", \"gap-2\"], [\"type\", \"submit\", 1, \"btn\", \"submit\", 3, \"disabled\"], [4, \"ngIf\"], [1, \"mt-3\"], [\"routerLink\", \"/auth/sign-up\"], [1, \"col-lg-7\", \"d-none\", \"d-lg-flex\", \"flex-column\", \"align-items-start\", \"bg-light\", \"position-relative\"], [1, \"text-start\", \"p-5\", \"w-100\"], [1, \"blockquote\"], [1, \"blockquote-footer\"], [\"title\", \"Source Title\"], [1, \"image-container\"], [\"src\", \"../../../assets/images/background.jpeg\", \"alt\", \"Stats graph\", 1, \"img-fluid\", \"position-absolute\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"]],\n    template: function LoginComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h2\", 4);\n        i0.ɵɵelement(5, \"img\", 5);\n        i0.ɵɵtext(6, \" GPGC (Swabi)\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"h1\", 4);\n        i0.ɵɵtext(8, \"Log in\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"p\");\n        i0.ɵɵtext(10, \"Welcome back! Please enter your details.\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"form\", 6);\n        i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_11_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵelementStart(12, \"div\", 7)(13, \"label\", 8);\n        i0.ɵɵtext(14, \"Email\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(15, \"input\", 9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(16, \"div\", 10)(17, \"label\", 11);\n        i0.ɵɵtext(18, \"Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(19, \"input\", 12);\n        i0.ɵɵelementStart(20, \"i\", 13);\n        i0.ɵɵlistener(\"click\", function LoginComponent_Template_i_click_20_listener() {\n          return ctx.togglePasswordVisibility();\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(21, \"div\", 14)(22, \"div\");\n        i0.ɵɵelement(23, \"input\", 15);\n        i0.ɵɵelementStart(24, \"label\", 16);\n        i0.ɵɵtext(25, \"Remember me\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(26, \"p\", 17)(27, \"a\", 18);\n        i0.ɵɵtext(28, \"Forgot password?\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(29, \"div\", 19)(30, \"button\", 20);\n        i0.ɵɵtemplate(31, LoginComponent_span_31_Template, 2, 0, \"span\", 21);\n        i0.ɵɵtemplate(32, LoginComponent_span_32_Template, 3, 0, \"span\", 21);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(33, \"p\", 22);\n        i0.ɵɵtext(34, \"Don't have an account? \");\n        i0.ɵɵelementStart(35, \"a\", 23);\n        i0.ɵɵtext(36, \"Sign up\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(37, \"div\", 24)(38, \"div\", 25)(39, \"blockquote\", 26)(40, \"h2\", 4);\n        i0.ɵɵtext(41, \"College management system Login page\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(42, \"footer\", 27);\n        i0.ɵɵtext(43, \"Name\");\n        i0.ɵɵelementStart(44, \"cite\", 28);\n        i0.ɵɵtext(45, \"Owner ~ GPGC SWABI\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(46, \"div\", 29);\n        i0.ɵɵelement(47, \"img\", 30);\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(11);\n        i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"type\", ctx.showPassword ? \"text\" : \"password\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngClass\", ctx.showPassword ? \"fa-eye-slash\" : \"fa-eye\");\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"disabled\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n      }\n    },\n    dependencies: [i5.NgClass, i5.NgIf, i2.RouterLink, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n    styles: [\"body[_ngcontent-%COMP%], html[_ngcontent-%COMP%] {\\n    margin: 0;\\n    padding: 0;\\n    overflow-x: hidden; \\n\\n    height: 100%;\\n}\\n\\n\\n\\n.image-container[_ngcontent-%COMP%] {\\n    width: 100%;\\n    height: 100%;\\n    position: relative;\\n}\\n\\n.image-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    max-width: 80%;\\n    height: auto;\\n    position: absolute;\\n    right: 0;\\n    bottom: 0;\\n    object-fit: contain; \\n\\n    border-radius: 20px;\\n}\\n.submit[_ngcontent-%COMP%]{\\n    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);\\n    color: var(--white);\\n    border: none;\\n    padding: var(--spacing-md) var(--spacing-xl);\\n    border-radius: var(--border-radius-md);\\n    font-weight: var(--font-weight-medium);\\n    transition: var(--transition-normal);\\n    box-shadow: var(--shadow-md);\\n    font-family: var(--font-family-primary);\\n}\\n\\n.submit[_ngcontent-%COMP%]:hover {\\n    transform: translateY(-2px);\\n    box-shadow: var(--shadow-lg);\\n}\\n\\n.submit[_ngcontent-%COMP%]:active {\\n    transform: translateY(0);\\n    box-shadow: var(--shadow-md);\\n}\\n\\n.forgot[_ngcontent-%COMP%]{\\n    color: var(--primary-color);\\n    text-decoration: none;\\n    font-weight: var(--font-weight-bold);\\n    transition: var(--transition-fast);\\n}\\n\\n.forgot[_ngcontent-%COMP%]:hover {\\n    color: var(--primary-dark);\\n    text-decoration: underline;\\n}\\n\\na[_ngcontent-%COMP%]{\\n    cursor: pointer;\\n    color: var(--primary-color);\\n    font-weight: var(--font-weight-bold);\\n    transition: var(--transition-fast);\\n    text-decoration: none;\\n}\\n\\na[_ngcontent-%COMP%]:hover {\\n    color: var(--primary-dark);\\n    text-decoration: underline;\\n}\\n\\n\\n.form-control[_ngcontent-%COMP%] {\\n    border: 2px solid var(--gray-300);\\n    border-radius: var(--border-radius-md);\\n    padding: var(--spacing-md) var(--spacing-lg);\\n    font-size: var(--font-size-base);\\n    transition: var(--transition-fast);\\n    font-family: var(--font-family-primary);\\n    box-shadow: var(--shadow-sm);\\n}\\n\\n.form-control[_ngcontent-%COMP%]:focus {\\n    border-color: var(--primary-color);\\n    box-shadow: 0 0 0 3px rgba(41, 87, 140, 0.1), var(--shadow-md);\\n    outline: none;\\n    transform: translateY(-1px);\\n}\\n\\n.form-control[_ngcontent-%COMP%]:hover {\\n    border-color: var(--gray-400);\\n    box-shadow: var(--shadow-md);\\n}\\n\\n.form-label[_ngcontent-%COMP%] {\\n    font-weight: var(--font-weight-medium);\\n    color: var(--gray-700);\\n    margin-bottom: var(--spacing-sm);\\n    font-family: var(--font-family-primary);\\n}\\n\\n.viewpassword[_ngcontent-%COMP%]{\\n    position: absolute;\\n    right: var(--spacing-md);\\n    transform: translateY(-50%);\\n    cursor: pointer;\\n    bottom: 7%;\\n    color: var(--gray-500);\\n    transition: var(--transition-fast);\\n    padding: var(--spacing-xs);\\n    border-radius: var(--border-radius-sm);\\n}\\n\\n.viewpassword[_ngcontent-%COMP%]:hover {\\n    color: var(--primary-color);\\n    background-color: var(--gray-100);\\n}\\n\\n\\n\\n.login-container[_ngcontent-%COMP%] {\\n    background: linear-gradient(135deg, var(--white) 0%, var(--gray-50) 100%);\\n    border-radius: var(--border-radius-xl);\\n    box-shadow: var(--shadow-xl);\\n    padding: var(--spacing-2xl);\\n    border: 1px solid var(--gray-200);\\n}\\n\\n\\n\\n.brand-logo[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n    border-radius: var(--border-radius-full);\\n    box-shadow: var(--shadow-md);\\n    border: 2px solid var(--primary-color);\\n    margin-right: var(--spacing-md);\\n}\\n\\n.brand-text[_ngcontent-%COMP%] {\\n    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);\\n    -webkit-background-clip: text;\\n    -webkit-text-fill-color: transparent;\\n    background-clip: text;\\n    font-weight: var(--font-weight-bold);\\n    font-family: var(--font-family-primary);\\n}\\n\\n\\n\\n.welcome-title[_ngcontent-%COMP%] {\\n    color: var(--gray-900);\\n    font-weight: var(--font-weight-semibold);\\n    margin-bottom: var(--spacing-lg);\\n    font-family: var(--font-family-primary);\\n}\\n\\n.welcome-subtitle[_ngcontent-%COMP%] {\\n    color: var(--gray-600);\\n    margin-bottom: var(--spacing-2xl);\\n    font-family: var(--font-family-primary);\\n}\\n\\n\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "LoginComponent", "constructor", "fb", "router", "roleService", "userService", "showPassword", "loginError", "loading", "ngOnInit", "loginForm", "group", "email", "required", "password", "<PERSON><PERSON><PERSON><PERSON>", "remember", "onSubmit", "valid", "value", "login", "subscribe", "next", "res", "success", "console", "log", "role", "user", "setRole", "navigate", "fire", "icon", "title", "text", "name", "timer", "showConfirmButton", "message", "error", "togglePasswordVisibility", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "Router", "i3", "RoleService", "i4", "UserService", "_2", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_11_listener", "LoginComponent_Template_i_click_20_listener", "ɵɵtemplate", "LoginComponent_span_31_Template", "LoginComponent_span_32_Template", "ɵɵadvance", "ɵɵproperty"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\auth\\login\\login.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\auth\\login\\login.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { RoleService } from 'src/app/services/role.service';\r\nimport { UserService } from 'src/app/services/user.service';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-login',\r\n  templateUrl: './login.component.html',\r\n  styleUrls: ['./login.component.css']\r\n})\r\nexport class LoginComponent implements OnInit {\r\n  showPassword = false;\r\n  loginForm!: FormGroup;\r\n  loginError = ''; // To store error messages\r\n  loading: boolean = false;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private router: Router,\r\n    public roleService: RoleService,\r\n    private userService: UserService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    // Initialize form with validation\r\n    this.loginForm = this.fb.group({\r\n      email: ['', [Validators.required, Validators.email]],\r\n      password: ['', [Validators.required, Validators.minLength(6)]],\r\n      remember: [false]\r\n    });\r\n  }\r\n\r\n  onSubmit() {\r\n    // Reset the error message\r\n    this.loginError = '';\r\n    this.loading = true; \r\n    if (this.loginForm.valid) {\r\n      // Capture form values\r\n      const { email, password } = this.loginForm.value;\r\n\r\n      // Call the UserService's login method\r\n      this.userService.login({ email, password }).subscribe({\r\n        next: (res: any) => {\r\n          if (res.success) {\r\n            console.log(res , \"role\")\r\n           let  role = res.user.role\r\n           console.log(role);\r\n           this.loading = false;\r\n            // Check role and perform role-based login\r\n            if (role === 'Principal') {\r\n              this.roleService.setRole('Principal');\r\n              console.log('Principal logged in');\r\n              this.router.navigate(['/dashboard/admin']);\r\n            } else if (role === 'Teacher') {\r\n              this.roleService.setRole('Teacher');\r\n              console.log('Teacher logged in');\r\n              this.router.navigate(['/dashboard/teacher']);\r\n            } else if (role === 'Student') {\r\n              this.roleService.setRole('Student');\r\n              console.log('Student logged in');\r\n              this.router.navigate(['/dashboard/student']);\r\n            } else {\r\n              this.loginError = 'Invalid credentials for the selected role';\r\n              console.log('Invalid credentials');\r\n              return; // Stop further execution\r\n            }\r\n\r\n            // Show success message\r\n            Swal.fire({\r\n              icon: 'success',\r\n              title: 'Login Successful',\r\n              text: `Welcome ${res.user.name}!`,\r\n              timer: 2000,\r\n              showConfirmButton: false\r\n            });\r\n          } else {\r\n            this.loginError = res.message || 'Login failed';\r\n            Swal.fire({\r\n              icon: 'error',\r\n              title: 'Login Failed',\r\n              text: this.loginError,\r\n            });\r\n            this.loading = false;\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('Login failed', error);\r\n          Swal.fire({\r\n            icon: 'error',\r\n            title: 'Login Failed',\r\n            text: error.error.message,\r\n            });\r\n          this.loginError = 'Server error, please try again later.';\r\n          this.loading = false;\r\n        }\r\n      });\r\n    } else {\r\n      // If the form is invalid, display an error message\r\n      this.loginError = 'Please fill in all fields correctly';\r\n      console.log('Form is invalid');\r\n      this.loading = false;\r\n    }\r\n  }\r\n\r\n  // Toggle the visibility of the password\r\n  togglePasswordVisibility() {\r\n    this.showPassword = !this.showPassword;\r\n  }\r\n}\r\n", "<div class=\"container-fluid\">\r\n    <div class=\"row vh-100\">\r\n      <div class=\"col-lg-5 d-flex flex-column justify-content-center align-items-center p-3 bg-white\">\r\n        <div class=\"w-100\" style=\"max-width: 400px;\">\r\n          <h2 class=\"mb-4\"> <img src=\"../../../assets/images/logo.jpeg\"/> GPGC (Swabi)</h2>\r\n          <h1 class=\"mb-4\">Log in</h1>\r\n          <p>Welcome back! Please enter your details.</p>\r\n  \r\n          <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\">\r\n            <div class=\"mb-3\">\r\n              <label for=\"email\" class=\"form-label\">Email</label>\r\n              <input type=\"email\" class=\"form-control\" id=\"email\" formControlName=\"email\" placeholder=\"Enter your email\">\r\n            </div>\r\n  \r\n            <!-- <div class=\"mb-3\">\r\n              <label for=\"role\" class=\"form-label \">Role</label>\r\n              <select class=\"form-control\" formControlName=\"role\">\r\n                <option value=\"Admin\" selected>Admin</option>\r\n                <option value=\"Student\">Student</option>\r\n                <option value=\"Teacher\">Teacher</option>\r\n              </select>\r\n            </div> -->\r\n            <div class=\"mb-3 position-relative\">\r\n                <label for=\"password\" class=\"form-label\">Password</label>\r\n                <input [type]=\"showPassword ? 'text' : 'password'\" class=\"form-control\" id=\"password\" formControlName=\"password\" placeholder=\"Enter your password\">\r\n                <i class=\"fas viewpassword\" [ngClass]=\"showPassword ? 'fa-eye-slash' : 'fa-eye'\" (click)=\"togglePasswordVisibility()\" ></i>\r\n              </div>\r\n              \r\n            <div class=\"form-check mb-3 d-flex justify-content-between align-items-center\">\r\n              <div>\r\n                <input class=\"form-check-input\" type=\"checkbox\" formControlName=\"remember\" id=\"remember\">\r\n                <label class=\"form-check-label\" for=\"remember\">Remember me</label>\r\n              </div>\r\n              <p class=\"mt-0\" routerLink=\"/auth/forgot-password\"><a class=\"forgot\" style=\"cursor: pointer;\">Forgot password?</a></p>\r\n            </div>\r\n  \r\n            <!-- <div class=\"d-grid gap-2\">\r\n              <button type=\"submit\" class=\"btn submit\">Sign in</button>\r\n            </div> -->\r\n            <div class=\"d-grid gap-2\">\r\n            <button type=\"submit\" class=\"btn submit\" [disabled]=\"loading\">\r\n              <span *ngIf=\"!loading\">Sign in</span>\r\n              <span *ngIf=\"loading\">\r\n                <span class=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\r\n                Signing in...\r\n              </span>\r\n            </button>\r\n            \r\n          </div>\r\n            <p class=\"mt-3\">Don't have an account? <a routerLink=\"/auth/sign-up\">Sign up</a></p>\r\n          </form>\r\n        </div>\r\n      </div>\r\n  \r\n      <div class=\"col-lg-7 d-none d-lg-flex flex-column align-items-start bg-light position-relative\">\r\n        <div class=\"text-start p-5 w-100\">\r\n          <blockquote class=\"blockquote\">\r\n            <h2 class=\"mb-4\">College management system Login page</h2>\r\n            <footer class=\"blockquote-footer\">Name<cite title=\"Source Title\">Owner ~ GPGC SWABI</cite></footer>\r\n          </blockquote>\r\n        </div>\r\n        <div class=\"image-container\">\r\n          <img src=\"../../../assets/images/background.jpeg\" class=\"img-fluid position-absolute\" alt=\"Stats graph\">\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  "], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAInE,OAAOC,IAAI,MAAM,aAAa;;;;;;;;;ICoChBC,EAAA,CAAAC,cAAA,WAAuB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACrCH,EAAA,CAAAC,cAAA,WAAsB;IACpBD,EAAA,CAAAI,SAAA,eAA4F;IAC5FJ,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;ADjCrB,OAAM,MAAOE,cAAc;EAMzBC,YACUC,EAAe,EACfC,MAAc,EACfC,WAAwB,EACvBC,WAAwB;IAHxB,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IACP,KAAAC,WAAW,GAAXA,WAAW;IACV,KAAAC,WAAW,GAAXA,WAAW;IATrB,KAAAC,YAAY,GAAG,KAAK;IAEpB,KAAAC,UAAU,GAAG,EAAE,CAAC,CAAC;IACjB,KAAAC,OAAO,GAAY,KAAK;EAOrB;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,SAAS,GAAG,IAAI,CAACR,EAAE,CAACS,KAAK,CAAC;MAC7BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACnB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAACmB,KAAK,CAAC,CAAC;MACpDE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACrB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAACsB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DC,QAAQ,EAAE,CAAC,KAAK;KACjB,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACV,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,IAAI,CAACE,SAAS,CAACQ,KAAK,EAAE;MACxB;MACA,MAAM;QAAEN,KAAK;QAAEE;MAAQ,CAAE,GAAG,IAAI,CAACJ,SAAS,CAACS,KAAK;MAEhD;MACA,IAAI,CAACd,WAAW,CAACe,KAAK,CAAC;QAAER,KAAK;QAAEE;MAAQ,CAAE,CAAC,CAACO,SAAS,CAAC;QACpDC,IAAI,EAAGC,GAAQ,IAAI;UACjB,IAAIA,GAAG,CAACC,OAAO,EAAE;YACfC,OAAO,CAACC,GAAG,CAACH,GAAG,EAAG,MAAM,CAAC;YAC1B,IAAKI,IAAI,GAAGJ,GAAG,CAACK,IAAI,CAACD,IAAI;YACzBF,OAAO,CAACC,GAAG,CAACC,IAAI,CAAC;YACjB,IAAI,CAACnB,OAAO,GAAG,KAAK;YACnB;YACA,IAAImB,IAAI,KAAK,WAAW,EAAE;cACxB,IAAI,CAACvB,WAAW,CAACyB,OAAO,CAAC,WAAW,CAAC;cACrCJ,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;cAClC,IAAI,CAACvB,MAAM,CAAC2B,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;aAC3C,MAAM,IAAIH,IAAI,KAAK,SAAS,EAAE;cAC7B,IAAI,CAACvB,WAAW,CAACyB,OAAO,CAAC,SAAS,CAAC;cACnCJ,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;cAChC,IAAI,CAACvB,MAAM,CAAC2B,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;aAC7C,MAAM,IAAIH,IAAI,KAAK,SAAS,EAAE;cAC7B,IAAI,CAACvB,WAAW,CAACyB,OAAO,CAAC,SAAS,CAAC;cACnCJ,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;cAChC,IAAI,CAACvB,MAAM,CAAC2B,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;aAC7C,MAAM;cACL,IAAI,CAACvB,UAAU,GAAG,2CAA2C;cAC7DkB,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;cAClC,OAAO,CAAC;;YAGV;YACAhC,IAAI,CAACqC,IAAI,CAAC;cACRC,IAAI,EAAE,SAAS;cACfC,KAAK,EAAE,kBAAkB;cACzBC,IAAI,EAAE,WAAWX,GAAG,CAACK,IAAI,CAACO,IAAI,GAAG;cACjCC,KAAK,EAAE,IAAI;cACXC,iBAAiB,EAAE;aACpB,CAAC;WACH,MAAM;YACL,IAAI,CAAC9B,UAAU,GAAGgB,GAAG,CAACe,OAAO,IAAI,cAAc;YAC/C5C,IAAI,CAACqC,IAAI,CAAC;cACRC,IAAI,EAAE,OAAO;cACbC,KAAK,EAAE,cAAc;cACrBC,IAAI,EAAE,IAAI,CAAC3B;aACZ,CAAC;YACF,IAAI,CAACC,OAAO,GAAG,KAAK;;QAExB,CAAC;QACD+B,KAAK,EAAGA,KAAK,IAAI;UACfd,OAAO,CAACc,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;UACpC7C,IAAI,CAACqC,IAAI,CAAC;YACRC,IAAI,EAAE,OAAO;YACbC,KAAK,EAAE,cAAc;YACrBC,IAAI,EAAEK,KAAK,CAACA,KAAK,CAACD;WACjB,CAAC;UACJ,IAAI,CAAC/B,UAAU,GAAG,uCAAuC;UACzD,IAAI,CAACC,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAACD,UAAU,GAAG,qCAAqC;MACvDkB,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;MAC9B,IAAI,CAAClB,OAAO,GAAG,KAAK;;EAExB;EAEA;EACAgC,wBAAwBA,CAAA;IACtB,IAAI,CAAClC,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAAC,QAAAmC,CAAA,G;qBAjGUzC,cAAc,EAAAL,EAAA,CAAA+C,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjD,EAAA,CAAA+C,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAnD,EAAA,CAAA+C,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAArD,EAAA,CAAA+C,iBAAA,CAAAO,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAdnD,cAAc;IAAAoD,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCZ3B/D,EAAA,CAAAC,cAAA,aAA6B;QAIDD,EAAA,CAAAI,SAAA,aAA6C;QAACJ,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACjFH,EAAA,CAAAC,cAAA,YAAiB;QAAAD,EAAA,CAAAE,MAAA,aAAM;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC5BH,EAAA,CAAAC,cAAA,QAAG;QAAAD,EAAA,CAAAE,MAAA,gDAAwC;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAE/CH,EAAA,CAAAC,cAAA,eAAsD;QAAxBD,EAAA,CAAAiE,UAAA,sBAAAC,kDAAA;UAAA,OAAYF,GAAA,CAAA1C,QAAA,EAAU;QAAA,EAAC;QACnDtB,EAAA,CAAAC,cAAA,cAAkB;QACsBD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACnDH,EAAA,CAAAI,SAAA,gBAA2G;QAC7GJ,EAAA,CAAAG,YAAA,EAAM;QAUNH,EAAA,CAAAC,cAAA,eAAoC;QACSD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACzDH,EAAA,CAAAI,SAAA,iBAAmJ;QACnJJ,EAAA,CAAAC,cAAA,aAAuH;QAAtCD,EAAA,CAAAiE,UAAA,mBAAAE,4CAAA;UAAA,OAASH,GAAA,CAAAnB,wBAAA,EAA0B;QAAA,EAAC;QAAE7C,EAAA,CAAAG,YAAA,EAAI;QAG/HH,EAAA,CAAAC,cAAA,eAA+E;QAE3ED,EAAA,CAAAI,SAAA,iBAAyF;QACzFJ,EAAA,CAAAC,cAAA,iBAA+C;QAAAD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAEpEH,EAAA,CAAAC,cAAA,aAAmD;QAA2CD,EAAA,CAAAE,MAAA,wBAAgB;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAMpHH,EAAA,CAAAC,cAAA,eAA0B;QAExBD,EAAA,CAAAoE,UAAA,KAAAC,+BAAA,mBAAqC;QACrCrE,EAAA,CAAAoE,UAAA,KAAAE,+BAAA,mBAGO;QACTtE,EAAA,CAAAG,YAAA,EAAS;QAGTH,EAAA,CAAAC,cAAA,aAAgB;QAAAD,EAAA,CAAAE,MAAA,+BAAuB;QAAAF,EAAA,CAAAC,cAAA,aAA8B;QAAAD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAKtFH,EAAA,CAAAC,cAAA,eAAgG;QAGzED,EAAA,CAAAE,MAAA,4CAAoC;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC1DH,EAAA,CAAAC,cAAA,kBAAkC;QAAAD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAC,cAAA,gBAA2B;QAAAD,EAAA,CAAAE,MAAA,0BAAkB;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAG9FH,EAAA,CAAAC,cAAA,eAA6B;QAC3BD,EAAA,CAAAI,SAAA,eAAwG;QAC1GJ,EAAA,CAAAG,YAAA,EAAM;;;QAvDEH,EAAA,CAAAuE,SAAA,IAAuB;QAAvBvE,EAAA,CAAAwE,UAAA,cAAAR,GAAA,CAAAjD,SAAA,CAAuB;QAgBhBf,EAAA,CAAAuE,SAAA,GAA2C;QAA3CvE,EAAA,CAAAwE,UAAA,SAAAR,GAAA,CAAArD,YAAA,uBAA2C;QACtBX,EAAA,CAAAuE,SAAA,GAAoD;QAApDvE,EAAA,CAAAwE,UAAA,YAAAR,GAAA,CAAArD,YAAA,6BAAoD;QAe3CX,EAAA,CAAAuE,SAAA,IAAoB;QAApBvE,EAAA,CAAAwE,UAAA,aAAAR,GAAA,CAAAnD,OAAA,CAAoB;QACpDb,EAAA,CAAAuE,SAAA,GAAc;QAAdvE,EAAA,CAAAwE,UAAA,UAAAR,GAAA,CAAAnD,OAAA,CAAc;QACdb,EAAA,CAAAuE,SAAA,GAAa;QAAbvE,EAAA,CAAAwE,UAAA,SAAAR,GAAA,CAAAnD,OAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}