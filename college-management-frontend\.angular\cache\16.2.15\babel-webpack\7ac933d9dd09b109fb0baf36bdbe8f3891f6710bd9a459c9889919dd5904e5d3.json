{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { UX_TEXT_STANDARDS } from 'src/app/shared/constants/ux-text-standards';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/role.service\";\nimport * as i4 from \"src/app/services/user.service\";\nimport * as i5 from \"src/app/shared/services/ux-helper.service\";\nimport * as i6 from \"@angular/common\";\nfunction LoginComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"small\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getFieldErrorMessage(\"email\"));\n  }\n}\nfunction LoginComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"small\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldErrorMessage(\"password\"));\n  }\n}\nfunction LoginComponent_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sign in\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_span_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"span\", 34);\n    i0.ɵɵtext(2, \" Signing in... \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class LoginComponent {\n  constructor(fb, router, roleService, userService, uxHelper) {\n    this.fb = fb;\n    this.router = router;\n    this.roleService = roleService;\n    this.userService = userService;\n    this.uxHelper = uxHelper;\n    this.showPassword = false;\n    this.loginError = ''; // To store error messages\n    this.loading = false;\n    // UX Text Standards\n    this.UX_TEXT = UX_TEXT_STANDARDS;\n  }\n  ngOnInit() {\n    // Initialize form with validation\n    this.loginForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(6)]],\n      remember: [false]\n    });\n  }\n  onSubmit() {\n    // Reset the error message\n    this.loginError = '';\n    this.loading = true;\n    if (this.loginForm.valid) {\n      // Capture form values\n      const {\n        email,\n        password\n      } = this.loginForm.value;\n      // Call the UserService's login method\n      this.userService.login({\n        email,\n        password\n      }).subscribe({\n        next: res => {\n          if (res.success) {\n            const role = res.user.role;\n            this.loading = false;\n            // Check role and perform role-based login\n            if (role === 'Principal') {\n              this.roleService.setRole('Principal');\n              this.router.navigate(['/dashboard/admin']);\n            } else if (role === 'Teacher') {\n              this.roleService.setRole('Teacher');\n              this.router.navigate(['/dashboard/teacher']);\n            } else if (role === 'Student') {\n              this.roleService.setRole('Student');\n              this.router.navigate(['/dashboard/student']);\n            } else {\n              this.loginError = 'Invalid credentials for the selected role';\n              return; // Stop further execution\n            }\n            // Show user-friendly welcome message\n            this.uxHelper.showWelcomeMessage(res.user.name, res.user.role);\n          } else {\n            this.loginError = res.message || 'Login failed';\n            this.uxHelper.showErrorMessage('UNAUTHORIZED', 'Invalid email or password. Please check your credentials and try again.');\n            this.loading = false;\n          }\n        },\n        error: error => {\n          this.loading = false;\n          // Show user-friendly error messages based on error type\n          if (error.status === 401) {\n            this.uxHelper.showErrorMessage('UNAUTHORIZED', 'Invalid email or password. Please check your credentials and try again.');\n          } else if (error.status === 0) {\n            this.uxHelper.showErrorMessage('NETWORK_ERROR');\n          } else {\n            this.uxHelper.showErrorMessage('SERVER_ERROR', 'Unable to sign in right now. Please try again in a few minutes.');\n          }\n          this.loginError = 'Please try again.';\n        }\n      });\n    } else {\n      // If the form is invalid, display an error message\n      this.loginError = 'Please fill in all fields correctly';\n      this.loading = false;\n    }\n  }\n  // Toggle the visibility of the password\n  togglePasswordVisibility() {\n    this.showPassword = !this.showPassword;\n  }\n  // Helper methods for template\n  getFieldErrorMessage(fieldName) {\n    const field = this.loginForm.get(fieldName);\n    if (field && field.invalid && field.touched) {\n      const errors = field.errors;\n      if (errors) {\n        const errorType = Object.keys(errors)[0];\n        return this.uxHelper.getValidationErrorMessage(fieldName, errorType, errors[errorType]);\n      }\n    }\n    return '';\n  }\n  getTooltip(fieldKey) {\n    return this.uxHelper.getTooltipText(fieldKey);\n  }\n  getLabel(labelKey) {\n    return this.uxHelper.getLabelText(labelKey);\n  }\n  getPlaceholder(placeholderKey) {\n    return this.uxHelper.getPlaceholderText(placeholderKey);\n  }\n  static #_ = this.ɵfac = function LoginComponent_Factory(t) {\n    return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.RoleService), i0.ɵɵdirectiveInject(i4.UserService), i0.ɵɵdirectiveInject(i5.UxHelperService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LoginComponent,\n    selectors: [[\"app-login\"]],\n    decls: 52,\n    vars: 18,\n    consts: [[1, \"container-fluid\"], [1, \"row\", \"vh-100\"], [1, \"col-lg-5\", \"d-flex\", \"flex-column\", \"justify-content-center\", \"align-items-center\", \"p-3\", \"bg-white\"], [1, \"w-100\", 2, \"max-width\", \"400px\"], [1, \"mb-4\"], [\"src\", \"../../../assets/images/logo.jpeg\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"mb-3\"], [\"for\", \"email\", 1, \"form-label\"], [1, \"fas\", \"fa-question-circle\", \"help-icon\", 3, \"title\"], [\"type\", \"email\", \"id\", \"email\", \"formControlName\", \"email\", 1, \"form-control\", 3, \"placeholder\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [1, \"mb-3\", \"position-relative\"], [\"for\", \"password\", 1, \"form-label\"], [\"id\", \"password\", \"formControlName\", \"password\", \"placeholder\", \"Enter your password\", 1, \"form-control\", 3, \"type\"], [1, \"fas\", \"viewpassword\", 3, \"ngClass\", \"title\", \"click\"], [1, \"form-check\", \"mb-3\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"type\", \"checkbox\", \"formControlName\", \"remember\", \"id\", \"remember\", 1, \"form-check-input\"], [\"for\", \"remember\", 1, \"form-check-label\"], [\"routerLink\", \"/auth/forgot-password\", 1, \"mt-0\"], [1, \"forgot\", 2, \"cursor\", \"pointer\"], [1, \"d-grid\", \"gap-2\"], [\"type\", \"submit\", 1, \"btn\", \"submit\", 3, \"disabled\"], [4, \"ngIf\"], [1, \"mt-3\"], [\"routerLink\", \"/auth/sign-up\"], [1, \"col-lg-7\", \"d-none\", \"d-lg-flex\", \"flex-column\", \"align-items-start\", \"bg-light\", \"position-relative\"], [1, \"text-start\", \"p-5\", \"w-100\"], [1, \"blockquote\"], [1, \"blockquote-footer\"], [\"title\", \"Source Title\"], [1, \"image-container\"], [\"src\", \"../../../assets/images/background.jpeg\", \"alt\", \"Stats graph\", 1, \"img-fluid\", \"position-absolute\"], [1, \"invalid-feedback\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"]],\n    template: function LoginComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h2\", 4);\n        i0.ɵɵelement(5, \"img\", 5);\n        i0.ɵɵtext(6, \" GPGC (Swabi)\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"h1\", 4);\n        i0.ɵɵtext(8, \"Log in\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"p\");\n        i0.ɵɵtext(10, \"Welcome back! Please enter your details.\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"form\", 6);\n        i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_11_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵelementStart(12, \"div\", 7)(13, \"label\", 8);\n        i0.ɵɵtext(14);\n        i0.ɵɵelement(15, \"i\", 9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(16, \"input\", 10);\n        i0.ɵɵtemplate(17, LoginComponent_div_17_Template, 3, 1, \"div\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(18, \"div\", 12)(19, \"label\", 13);\n        i0.ɵɵtext(20);\n        i0.ɵɵelement(21, \"i\", 9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(22, \"input\", 14);\n        i0.ɵɵelementStart(23, \"i\", 15);\n        i0.ɵɵlistener(\"click\", function LoginComponent_Template_i_click_23_listener() {\n          return ctx.togglePasswordVisibility();\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(24, LoginComponent_div_24_Template, 3, 1, \"div\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"div\", 16)(26, \"div\");\n        i0.ɵɵelement(27, \"input\", 17);\n        i0.ɵɵelementStart(28, \"label\", 18);\n        i0.ɵɵtext(29, \"Remember me\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(30, \"p\", 19)(31, \"a\", 20);\n        i0.ɵɵtext(32, \"Forgot password?\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(33, \"div\", 21)(34, \"button\", 22);\n        i0.ɵɵtemplate(35, LoginComponent_span_35_Template, 2, 0, \"span\", 23);\n        i0.ɵɵtemplate(36, LoginComponent_span_36_Template, 3, 0, \"span\", 23);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(37, \"p\", 24);\n        i0.ɵɵtext(38, \"Don't have an account? \");\n        i0.ɵɵelementStart(39, \"a\", 25);\n        i0.ɵɵtext(40, \"Sign up\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(41, \"div\", 26)(42, \"div\", 27)(43, \"blockquote\", 28)(44, \"h2\", 4);\n        i0.ɵɵtext(45, \"College management system Login page\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(46, \"footer\", 29);\n        i0.ɵɵtext(47, \"Name\");\n        i0.ɵɵelementStart(48, \"cite\", 30);\n        i0.ɵɵtext(49, \"Owner ~ GPGC SWABI\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(50, \"div\", 31);\n        i0.ɵɵelement(51, \"img\", 32);\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        let tmp_3_0;\n        let tmp_5_0;\n        let tmp_8_0;\n        let tmp_12_0;\n        i0.ɵɵadvance(11);\n        i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", ctx.getLabel(\"EMAIL_ADDRESS\"), \" \");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"title\", ctx.getTooltip(\"EMAIL_ADDRESS\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵclassProp(\"is-invalid\", ((tmp_3_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_3_0.touched));\n        i0.ɵɵproperty(\"placeholder\", ctx.getPlaceholder(\"ENTER_EMAIL\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_5_0.touched));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", ctx.getLabel(\"PASSWORD\"), \" \");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"title\", ctx.getTooltip(\"PASSWORD\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵclassProp(\"is-invalid\", ((tmp_8_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_8_0.touched));\n        i0.ɵɵproperty(\"type\", ctx.showPassword ? \"text\" : \"password\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngClass\", ctx.showPassword ? \"fa-eye-slash\" : \"fa-eye\")(\"title\", ctx.showPassword ? \"Hide password\" : \"Show password\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_12_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_12_0.invalid) && ((tmp_12_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_12_0.touched));\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"disabled\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n      }\n    },\n    dependencies: [i6.NgClass, i6.NgIf, i2.RouterLink, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n    styles: [\"body[_ngcontent-%COMP%], html[_ngcontent-%COMP%] {\\n    margin: 0;\\n    padding: 0;\\n    overflow-x: hidden; \\n\\n    height: 100%;\\n}\\n\\n\\n\\n.image-container[_ngcontent-%COMP%] {\\n    width: 100%;\\n    height: 100%;\\n    position: relative;\\n}\\n\\n.image-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    max-width: 80%;\\n    height: auto;\\n    position: absolute;\\n    right: 0;\\n    bottom: 0;\\n    object-fit: contain; \\n\\n    border-radius: 20px;\\n}\\n.submit[_ngcontent-%COMP%]{\\n    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);\\n    color: var(--white);\\n    border: none;\\n    padding: var(--spacing-md) var(--spacing-xl);\\n    border-radius: var(--border-radius-md);\\n    font-weight: var(--font-weight-medium);\\n    transition: var(--transition-normal);\\n    box-shadow: var(--shadow-md);\\n    font-family: var(--font-family-primary);\\n}\\n\\n.submit[_ngcontent-%COMP%]:hover {\\n    transform: translateY(-2px);\\n    box-shadow: var(--shadow-lg);\\n}\\n\\n.submit[_ngcontent-%COMP%]:active {\\n    transform: translateY(0);\\n    box-shadow: var(--shadow-md);\\n}\\n\\n.forgot[_ngcontent-%COMP%]{\\n    color: var(--primary-color);\\n    text-decoration: none;\\n    font-weight: var(--font-weight-bold);\\n    transition: var(--transition-fast);\\n}\\n\\n.forgot[_ngcontent-%COMP%]:hover {\\n    color: var(--primary-dark);\\n    text-decoration: underline;\\n}\\n\\na[_ngcontent-%COMP%]{\\n    cursor: pointer;\\n    color: var(--primary-color);\\n    font-weight: var(--font-weight-bold);\\n    transition: var(--transition-fast);\\n    text-decoration: none;\\n}\\n\\na[_ngcontent-%COMP%]:hover {\\n    color: var(--primary-dark);\\n    text-decoration: underline;\\n}\\n\\n\\n.form-control[_ngcontent-%COMP%] {\\n    border: 2px solid var(--gray-300);\\n    border-radius: var(--border-radius-md);\\n    padding: var(--spacing-md) var(--spacing-lg);\\n    font-size: var(--font-size-base);\\n    transition: var(--transition-fast);\\n    font-family: var(--font-family-primary);\\n    box-shadow: var(--shadow-sm);\\n}\\n\\n.form-control[_ngcontent-%COMP%]:focus {\\n    border-color: var(--primary-color);\\n    box-shadow: 0 0 0 3px rgba(41, 87, 140, 0.1), var(--shadow-md);\\n    outline: none;\\n    transform: translateY(-1px);\\n}\\n\\n.form-control[_ngcontent-%COMP%]:hover {\\n    border-color: var(--gray-400);\\n    box-shadow: var(--shadow-md);\\n}\\n\\n.form-label[_ngcontent-%COMP%] {\\n    font-weight: var(--font-weight-medium);\\n    color: var(--gray-700);\\n    margin-bottom: var(--spacing-sm);\\n    font-family: var(--font-family-primary);\\n}\\n\\n.viewpassword[_ngcontent-%COMP%]{\\n    position: absolute;\\n    right: var(--spacing-md);\\n    transform: translateY(-50%);\\n    cursor: pointer;\\n    bottom: 7%;\\n    color: var(--gray-500);\\n    transition: var(--transition-fast);\\n    padding: var(--spacing-xs);\\n    border-radius: var(--border-radius-sm);\\n}\\n\\n.viewpassword[_ngcontent-%COMP%]:hover {\\n    color: var(--primary-color);\\n    background-color: var(--gray-100);\\n}\\n\\n\\n\\n.login-container[_ngcontent-%COMP%] {\\n    background: linear-gradient(135deg, var(--white) 0%, var(--gray-50) 100%);\\n    border-radius: var(--border-radius-xl);\\n    box-shadow: var(--shadow-xl);\\n    padding: var(--spacing-2xl);\\n    border: 1px solid var(--gray-200);\\n}\\n\\n\\n\\n.brand-logo[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n    border-radius: var(--border-radius-full);\\n    box-shadow: var(--shadow-md);\\n    border: 2px solid var(--primary-color);\\n    margin-right: var(--spacing-md);\\n}\\n\\n.brand-text[_ngcontent-%COMP%] {\\n    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);\\n    -webkit-background-clip: text;\\n    -webkit-text-fill-color: transparent;\\n    background-clip: text;\\n    font-weight: var(--font-weight-bold);\\n    font-family: var(--font-family-primary);\\n}\\n\\n\\n\\n.welcome-title[_ngcontent-%COMP%] {\\n    color: var(--gray-900);\\n    font-weight: var(--font-weight-semibold);\\n    margin-bottom: var(--spacing-lg);\\n    font-family: var(--font-family-primary);\\n}\\n\\n.welcome-subtitle[_ngcontent-%COMP%] {\\n    color: var(--gray-600);\\n    margin-bottom: var(--spacing-2xl);\\n    font-family: var(--font-family-primary);\\n}\\n\\n\\n\\n.form-label[_ngcontent-%COMP%] {\\n    display: flex;\\n    align-items: center;\\n    gap: 8px;\\n}\\n\\n.help-icon[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n    color: #6c757d;\\n    cursor: help;\\n    transition: color 0.2s ease;\\n}\\n\\n.help-icon[_ngcontent-%COMP%]:hover {\\n    color: #29578c;\\n}\\n\\n\\n\\n.invalid-feedback[_ngcontent-%COMP%] {\\n    display: block;\\n    color: #dc3545;\\n    font-size: 12px;\\n    margin-top: 5px;\\n    padding: 4px 8px;\\n    background-color: #f8d7da;\\n    border: 1px solid #f5c6cb;\\n    border-radius: 4px;\\n    animation: _ngcontent-%COMP%_fadeIn 0.3s ease;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n    from { opacity: 0; transform: translateY(-5px); }\\n    to { opacity: 1; transform: translateY(0); }\\n}\\n\\n\\n\\n.form-control.ng-valid.ng-touched[_ngcontent-%COMP%]:not(.ng-pristine) {\\n    border-color: #28a745;\\n    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "UX_TEXT_STANDARDS", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "getFieldErrorMessage", "ctx_r1", "ɵɵelement", "LoginComponent", "constructor", "fb", "router", "roleService", "userService", "uxHelper", "showPassword", "loginError", "loading", "UX_TEXT", "ngOnInit", "loginForm", "group", "email", "required", "password", "<PERSON><PERSON><PERSON><PERSON>", "remember", "onSubmit", "valid", "value", "login", "subscribe", "next", "res", "success", "role", "user", "setRole", "navigate", "showWelcomeMessage", "name", "message", "showErrorMessage", "error", "status", "togglePasswordVisibility", "fieldName", "field", "get", "invalid", "touched", "errors", "errorType", "Object", "keys", "getValidationErrorMessage", "getTooltip", "<PERSON><PERSON><PERSON>", "getTooltipText", "get<PERSON><PERSON><PERSON>", "labelKey", "getLabelText", "getPlaceholder", "placeholder<PERSON><PERSON>", "getPlaceholderText", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "Router", "i3", "RoleService", "i4", "UserService", "i5", "UxHelperService", "_2", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_11_listener", "ɵɵtemplate", "LoginComponent_div_17_Template", "LoginComponent_Template_i_click_23_listener", "LoginComponent_div_24_Template", "LoginComponent_span_35_Template", "LoginComponent_span_36_Template", "ɵɵproperty", "ɵɵtextInterpolate1", "ɵɵclassProp", "tmp_3_0", "tmp_5_0", "tmp_8_0", "tmp_12_0"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\auth\\login\\login.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\auth\\login\\login.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { RoleService } from 'src/app/services/role.service';\r\nimport { UserService } from 'src/app/services/user.service';\r\nimport { UxHelperService } from 'src/app/shared/services/ux-helper.service';\r\nimport { UX_TEXT_STANDARDS } from 'src/app/shared/constants/ux-text-standards';\r\n\r\n@Component({\r\n  selector: 'app-login',\r\n  templateUrl: './login.component.html',\r\n  styleUrls: ['./login.component.css']\r\n})\r\nexport class LoginComponent implements OnInit {\r\n  showPassword = false;\r\n  loginForm!: FormGroup;\r\n  loginError = ''; // To store error messages\r\n  loading: boolean = false;\r\n\r\n  // UX Text Standards\r\n  readonly UX_TEXT = UX_TEXT_STANDARDS;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private router: Router,\r\n    public roleService: RoleService,\r\n    private userService: UserService,\r\n    private uxHelper: UxHelperService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    // Initialize form with validation\r\n    this.loginForm = this.fb.group({\r\n      email: ['', [Validators.required, Validators.email]],\r\n      password: ['', [Validators.required, Validators.minLength(6)]],\r\n      remember: [false]\r\n    });\r\n  }\r\n\r\n  onSubmit() {\r\n    // Reset the error message\r\n    this.loginError = '';\r\n    this.loading = true; \r\n    if (this.loginForm.valid) {\r\n      // Capture form values\r\n      const { email, password } = this.loginForm.value;\r\n\r\n      // Call the UserService's login method\r\n      this.userService.login({ email, password }).subscribe({\r\n        next: (res: any) => {\r\n          if (res.success) {\r\n            const role = res.user.role;\r\n            this.loading = false;\r\n\r\n            // Check role and perform role-based login\r\n            if (role === 'Principal') {\r\n              this.roleService.setRole('Principal');\r\n              this.router.navigate(['/dashboard/admin']);\r\n            } else if (role === 'Teacher') {\r\n              this.roleService.setRole('Teacher');\r\n              this.router.navigate(['/dashboard/teacher']);\r\n            } else if (role === 'Student') {\r\n              this.roleService.setRole('Student');\r\n              this.router.navigate(['/dashboard/student']);\r\n            } else {\r\n              this.loginError = 'Invalid credentials for the selected role';\r\n              return; // Stop further execution\r\n            }\r\n\r\n            // Show user-friendly welcome message\r\n            this.uxHelper.showWelcomeMessage(res.user.name, res.user.role);\r\n          } else {\r\n            this.loginError = res.message || 'Login failed';\r\n            this.uxHelper.showErrorMessage('UNAUTHORIZED', 'Invalid email or password. Please check your credentials and try again.');\r\n            this.loading = false;\r\n          }\r\n        },\r\n        error: (error) => {\r\n          this.loading = false;\r\n\r\n          // Show user-friendly error messages based on error type\r\n          if (error.status === 401) {\r\n            this.uxHelper.showErrorMessage('UNAUTHORIZED', 'Invalid email or password. Please check your credentials and try again.');\r\n          } else if (error.status === 0) {\r\n            this.uxHelper.showErrorMessage('NETWORK_ERROR');\r\n          } else {\r\n            this.uxHelper.showErrorMessage('SERVER_ERROR', 'Unable to sign in right now. Please try again in a few minutes.');\r\n          }\r\n\r\n          this.loginError = 'Please try again.';\r\n        }\r\n      });\r\n    } else {\r\n      // If the form is invalid, display an error message\r\n      this.loginError = 'Please fill in all fields correctly';\r\n      this.loading = false;\r\n    }\r\n  }\r\n\r\n  // Toggle the visibility of the password\r\n  togglePasswordVisibility() {\r\n    this.showPassword = !this.showPassword;\r\n  }\r\n\r\n  // Helper methods for template\r\n  getFieldErrorMessage(fieldName: string): string {\r\n    const field = this.loginForm.get(fieldName);\r\n    if (field && field.invalid && field.touched) {\r\n      const errors = field.errors;\r\n      if (errors) {\r\n        const errorType = Object.keys(errors)[0];\r\n        return this.uxHelper.getValidationErrorMessage(fieldName, errorType, errors[errorType]);\r\n      }\r\n    }\r\n    return '';\r\n  }\r\n\r\n  getTooltip(fieldKey: keyof typeof UX_TEXT_STANDARDS.TOOLTIPS): string {\r\n    return this.uxHelper.getTooltipText(fieldKey);\r\n  }\r\n\r\n  getLabel(labelKey: keyof typeof UX_TEXT_STANDARDS.FORM_LABELS): string {\r\n    return this.uxHelper.getLabelText(labelKey);\r\n  }\r\n\r\n  getPlaceholder(placeholderKey: keyof typeof UX_TEXT_STANDARDS.PLACEHOLDERS): string {\r\n    return this.uxHelper.getPlaceholderText(placeholderKey);\r\n  }\r\n}\r\n", "<div class=\"container-fluid\">\r\n    <div class=\"row vh-100\">\r\n      <div class=\"col-lg-5 d-flex flex-column justify-content-center align-items-center p-3 bg-white\">\r\n        <div class=\"w-100\" style=\"max-width: 400px;\">\r\n          <h2 class=\"mb-4\"> <img src=\"../../../assets/images/logo.jpeg\"/> GPGC (Swabi)</h2>\r\n          <h1 class=\"mb-4\">Log in</h1>\r\n          <p>Welcome back! Please enter your details.</p>\r\n  \r\n          <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\">\r\n            <div class=\"mb-3\">\r\n              <label for=\"email\" class=\"form-label\">\r\n                {{ getLabel('EMAIL_ADDRESS') }}\r\n                <i class=\"fas fa-question-circle help-icon\"\r\n                   [title]=\"getTooltip('EMAIL_ADDRESS')\"></i>\r\n              </label>\r\n              <input\r\n                type=\"email\"\r\n                class=\"form-control\"\r\n                id=\"email\"\r\n                formControlName=\"email\"\r\n                [placeholder]=\"getPlaceholder('ENTER_EMAIL')\"\r\n                [class.is-invalid]=\"loginForm.get('email')?.invalid && loginForm.get('email')?.touched\">\r\n              <div class=\"invalid-feedback\" *ngIf=\"loginForm.get('email')?.invalid && loginForm.get('email')?.touched\">\r\n                <small>{{ getFieldErrorMessage('email') }}</small>\r\n              </div>\r\n            </div>\r\n  \r\n            <!-- <div class=\"mb-3\">\r\n              <label for=\"role\" class=\"form-label \">Role</label>\r\n              <select class=\"form-control\" formControlName=\"role\">\r\n                <option value=\"Admin\" selected>Admin</option>\r\n                <option value=\"Student\">Student</option>\r\n                <option value=\"Teacher\">Teacher</option>\r\n              </select>\r\n            </div> -->\r\n            <div class=\"mb-3 position-relative\">\r\n                <label for=\"password\" class=\"form-label\">\r\n                  {{ getLabel('PASSWORD') }}\r\n                  <i class=\"fas fa-question-circle help-icon\"\r\n                     [title]=\"getTooltip('PASSWORD')\"></i>\r\n                </label>\r\n                <input\r\n                  [type]=\"showPassword ? 'text' : 'password'\"\r\n                  class=\"form-control\"\r\n                  id=\"password\"\r\n                  formControlName=\"password\"\r\n                  placeholder=\"Enter your password\"\r\n                  [class.is-invalid]=\"loginForm.get('password')?.invalid && loginForm.get('password')?.touched\">\r\n                <i class=\"fas viewpassword\"\r\n                   [ngClass]=\"showPassword ? 'fa-eye-slash' : 'fa-eye'\"\r\n                   [title]=\"showPassword ? 'Hide password' : 'Show password'\"\r\n                   (click)=\"togglePasswordVisibility()\"></i>\r\n                <div class=\"invalid-feedback\" *ngIf=\"loginForm.get('password')?.invalid && loginForm.get('password')?.touched\">\r\n                  <small>{{ getFieldErrorMessage('password') }}</small>\r\n                </div>\r\n              </div>\r\n              \r\n            <div class=\"form-check mb-3 d-flex justify-content-between align-items-center\">\r\n              <div>\r\n                <input class=\"form-check-input\" type=\"checkbox\" formControlName=\"remember\" id=\"remember\">\r\n                <label class=\"form-check-label\" for=\"remember\">Remember me</label>\r\n              </div>\r\n              <p class=\"mt-0\" routerLink=\"/auth/forgot-password\"><a class=\"forgot\" style=\"cursor: pointer;\">Forgot password?</a></p>\r\n            </div>\r\n  \r\n            <!-- <div class=\"d-grid gap-2\">\r\n              <button type=\"submit\" class=\"btn submit\">Sign in</button>\r\n            </div> -->\r\n            <div class=\"d-grid gap-2\">\r\n            <button type=\"submit\" class=\"btn submit\" [disabled]=\"loading\">\r\n              <span *ngIf=\"!loading\">Sign in</span>\r\n              <span *ngIf=\"loading\">\r\n                <span class=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\r\n                Signing in...\r\n              </span>\r\n            </button>\r\n            \r\n          </div>\r\n            <p class=\"mt-3\">Don't have an account? <a routerLink=\"/auth/sign-up\">Sign up</a></p>\r\n          </form>\r\n        </div>\r\n      </div>\r\n  \r\n      <div class=\"col-lg-7 d-none d-lg-flex flex-column align-items-start bg-light position-relative\">\r\n        <div class=\"text-start p-5 w-100\">\r\n          <blockquote class=\"blockquote\">\r\n            <h2 class=\"mb-4\">College management system Login page</h2>\r\n            <footer class=\"blockquote-footer\">Name<cite title=\"Source Title\">Owner ~ GPGC SWABI</cite></footer>\r\n          </blockquote>\r\n        </div>\r\n        <div class=\"image-container\">\r\n          <img src=\"../../../assets/images/background.jpeg\" class=\"img-fluid position-absolute\" alt=\"Stats graph\">\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  "], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAKnE,SAASC,iBAAiB,QAAQ,4CAA4C;;;;;;;;;;ICgBhEC,EAAA,CAAAC,cAAA,cAAyG;IAChGD,EAAA,CAAAE,MAAA,GAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA3CH,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,oBAAA,UAAmC;;;;;IA6B1CP,EAAA,CAAAC,cAAA,cAA+G;IACtGD,EAAA,CAAAE,MAAA,GAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA9CH,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAK,iBAAA,CAAAG,MAAA,CAAAD,oBAAA,aAAsC;;;;;IAiBjDP,EAAA,CAAAC,cAAA,WAAuB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACrCH,EAAA,CAAAC,cAAA,WAAsB;IACpBD,EAAA,CAAAS,SAAA,eAA4F;IAC5FT,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;AD7DrB,OAAM,MAAOO,cAAc;EASzBC,YACUC,EAAe,EACfC,MAAc,EACfC,WAAwB,EACvBC,WAAwB,EACxBC,QAAyB;IAJzB,KAAAJ,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IACP,KAAAC,WAAW,GAAXA,WAAW;IACV,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAblB,KAAAC,YAAY,GAAG,KAAK;IAEpB,KAAAC,UAAU,GAAG,EAAE,CAAC,CAAC;IACjB,KAAAC,OAAO,GAAY,KAAK;IAExB;IACS,KAAAC,OAAO,GAAGrB,iBAAiB;EAQjC;EAEHsB,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,SAAS,GAAG,IAAI,CAACV,EAAE,CAACW,KAAK,CAAC;MAC7BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC1B,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC0B,KAAK,CAAC,CAAC;MACpDE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC5B,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC6B,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DC,QAAQ,EAAE,CAAC,KAAK;KACjB,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACX,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,IAAI,CAACG,SAAS,CAACQ,KAAK,EAAE;MACxB;MACA,MAAM;QAAEN,KAAK;QAAEE;MAAQ,CAAE,GAAG,IAAI,CAACJ,SAAS,CAACS,KAAK;MAEhD;MACA,IAAI,CAAChB,WAAW,CAACiB,KAAK,CAAC;QAAER,KAAK;QAAEE;MAAQ,CAAE,CAAC,CAACO,SAAS,CAAC;QACpDC,IAAI,EAAGC,GAAQ,IAAI;UACjB,IAAIA,GAAG,CAACC,OAAO,EAAE;YACf,MAAMC,IAAI,GAAGF,GAAG,CAACG,IAAI,CAACD,IAAI;YAC1B,IAAI,CAAClB,OAAO,GAAG,KAAK;YAEpB;YACA,IAAIkB,IAAI,KAAK,WAAW,EAAE;cACxB,IAAI,CAACvB,WAAW,CAACyB,OAAO,CAAC,WAAW,CAAC;cACrC,IAAI,CAAC1B,MAAM,CAAC2B,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;aAC3C,MAAM,IAAIH,IAAI,KAAK,SAAS,EAAE;cAC7B,IAAI,CAACvB,WAAW,CAACyB,OAAO,CAAC,SAAS,CAAC;cACnC,IAAI,CAAC1B,MAAM,CAAC2B,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;aAC7C,MAAM,IAAIH,IAAI,KAAK,SAAS,EAAE;cAC7B,IAAI,CAACvB,WAAW,CAACyB,OAAO,CAAC,SAAS,CAAC;cACnC,IAAI,CAAC1B,MAAM,CAAC2B,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;aAC7C,MAAM;cACL,IAAI,CAACtB,UAAU,GAAG,2CAA2C;cAC7D,OAAO,CAAC;;YAGV;YACA,IAAI,CAACF,QAAQ,CAACyB,kBAAkB,CAACN,GAAG,CAACG,IAAI,CAACI,IAAI,EAAEP,GAAG,CAACG,IAAI,CAACD,IAAI,CAAC;WAC/D,MAAM;YACL,IAAI,CAACnB,UAAU,GAAGiB,GAAG,CAACQ,OAAO,IAAI,cAAc;YAC/C,IAAI,CAAC3B,QAAQ,CAAC4B,gBAAgB,CAAC,cAAc,EAAE,yEAAyE,CAAC;YACzH,IAAI,CAACzB,OAAO,GAAG,KAAK;;QAExB,CAAC;QACD0B,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAC1B,OAAO,GAAG,KAAK;UAEpB;UACA,IAAI0B,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;YACxB,IAAI,CAAC9B,QAAQ,CAAC4B,gBAAgB,CAAC,cAAc,EAAE,yEAAyE,CAAC;WAC1H,MAAM,IAAIC,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;YAC7B,IAAI,CAAC9B,QAAQ,CAAC4B,gBAAgB,CAAC,eAAe,CAAC;WAChD,MAAM;YACL,IAAI,CAAC5B,QAAQ,CAAC4B,gBAAgB,CAAC,cAAc,EAAE,iEAAiE,CAAC;;UAGnH,IAAI,CAAC1B,UAAU,GAAG,mBAAmB;QACvC;OACD,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAACA,UAAU,GAAG,qCAAqC;MACvD,IAAI,CAACC,OAAO,GAAG,KAAK;;EAExB;EAEA;EACA4B,wBAAwBA,CAAA;IACtB,IAAI,CAAC9B,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEA;EACAV,oBAAoBA,CAACyC,SAAiB;IACpC,MAAMC,KAAK,GAAG,IAAI,CAAC3B,SAAS,CAAC4B,GAAG,CAACF,SAAS,CAAC;IAC3C,IAAIC,KAAK,IAAIA,KAAK,CAACE,OAAO,IAAIF,KAAK,CAACG,OAAO,EAAE;MAC3C,MAAMC,MAAM,GAAGJ,KAAK,CAACI,MAAM;MAC3B,IAAIA,MAAM,EAAE;QACV,MAAMC,SAAS,GAAGC,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC,CAAC,CAAC,CAAC;QACxC,OAAO,IAAI,CAACrC,QAAQ,CAACyC,yBAAyB,CAACT,SAAS,EAAEM,SAAS,EAAED,MAAM,CAACC,SAAS,CAAC,CAAC;;;IAG3F,OAAO,EAAE;EACX;EAEAI,UAAUA,CAACC,QAAiD;IAC1D,OAAO,IAAI,CAAC3C,QAAQ,CAAC4C,cAAc,CAACD,QAAQ,CAAC;EAC/C;EAEAE,QAAQA,CAACC,QAAoD;IAC3D,OAAO,IAAI,CAAC9C,QAAQ,CAAC+C,YAAY,CAACD,QAAQ,CAAC;EAC7C;EAEAE,cAAcA,CAACC,cAA2D;IACxE,OAAO,IAAI,CAACjD,QAAQ,CAACkD,kBAAkB,CAACD,cAAc,CAAC;EACzD;EAAC,QAAAE,CAAA,G;qBAlHUzD,cAAc,EAAAV,EAAA,CAAAoE,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtE,EAAA,CAAAoE,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAxE,EAAA,CAAAoE,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA1E,EAAA,CAAAoE,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAA5E,EAAA,CAAAoE,iBAAA,CAAAS,EAAA,CAAAC,eAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAdrE,cAAc;IAAAsE,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCb3BtF,EAAA,CAAAC,cAAA,aAA6B;QAIDD,EAAA,CAAAS,SAAA,aAA6C;QAACT,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACjFH,EAAA,CAAAC,cAAA,YAAiB;QAAAD,EAAA,CAAAE,MAAA,aAAM;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC5BH,EAAA,CAAAC,cAAA,QAAG;QAAAD,EAAA,CAAAE,MAAA,gDAAwC;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAE/CH,EAAA,CAAAC,cAAA,eAAsD;QAAxBD,EAAA,CAAAwF,UAAA,sBAAAC,kDAAA;UAAA,OAAYF,GAAA,CAAA1D,QAAA,EAAU;QAAA,EAAC;QACnD7B,EAAA,CAAAC,cAAA,cAAkB;QAEdD,EAAA,CAAAE,MAAA,IACA;QAAAF,EAAA,CAAAS,SAAA,YAC6C;QAC/CT,EAAA,CAAAG,YAAA,EAAQ;QACRH,EAAA,CAAAS,SAAA,iBAM0F;QAC1FT,EAAA,CAAA0F,UAAA,KAAAC,8BAAA,kBAEM;QACR3F,EAAA,CAAAG,YAAA,EAAM;QAUNH,EAAA,CAAAC,cAAA,eAAoC;QAE9BD,EAAA,CAAAE,MAAA,IACA;QAAAF,EAAA,CAAAS,SAAA,YACwC;QAC1CT,EAAA,CAAAG,YAAA,EAAQ;QACRH,EAAA,CAAAS,SAAA,iBAMgG;QAChGT,EAAA,CAAAC,cAAA,aAGwC;QAArCD,EAAA,CAAAwF,UAAA,mBAAAI,4CAAA;UAAA,OAASL,GAAA,CAAAxC,wBAAA,EAA0B;QAAA,EAAC;QAAC/C,EAAA,CAAAG,YAAA,EAAI;QAC5CH,EAAA,CAAA0F,UAAA,KAAAG,8BAAA,kBAEM;QACR7F,EAAA,CAAAG,YAAA,EAAM;QAERH,EAAA,CAAAC,cAAA,eAA+E;QAE3ED,EAAA,CAAAS,SAAA,iBAAyF;QACzFT,EAAA,CAAAC,cAAA,iBAA+C;QAAAD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAEpEH,EAAA,CAAAC,cAAA,aAAmD;QAA2CD,EAAA,CAAAE,MAAA,wBAAgB;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAMpHH,EAAA,CAAAC,cAAA,eAA0B;QAExBD,EAAA,CAAA0F,UAAA,KAAAI,+BAAA,mBAAqC;QACrC9F,EAAA,CAAA0F,UAAA,KAAAK,+BAAA,mBAGO;QACT/F,EAAA,CAAAG,YAAA,EAAS;QAGTH,EAAA,CAAAC,cAAA,aAAgB;QAAAD,EAAA,CAAAE,MAAA,+BAAuB;QAAAF,EAAA,CAAAC,cAAA,aAA8B;QAAAD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAKtFH,EAAA,CAAAC,cAAA,eAAgG;QAGzED,EAAA,CAAAE,MAAA,4CAAoC;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC1DH,EAAA,CAAAC,cAAA,kBAAkC;QAAAD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAC,cAAA,gBAA2B;QAAAD,EAAA,CAAAE,MAAA,0BAAkB;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAG9FH,EAAA,CAAAC,cAAA,eAA6B;QAC3BD,EAAA,CAAAS,SAAA,eAAwG;QAC1GT,EAAA,CAAAG,YAAA,EAAM;;;;;;;QApFEH,EAAA,CAAAI,SAAA,IAAuB;QAAvBJ,EAAA,CAAAgG,UAAA,cAAAT,GAAA,CAAAjE,SAAA,CAAuB;QAGvBtB,EAAA,CAAAI,SAAA,GACA;QADAJ,EAAA,CAAAiG,kBAAA,MAAAV,GAAA,CAAA1B,QAAA,uBACA;QACG7D,EAAA,CAAAI,SAAA,GAAqC;QAArCJ,EAAA,CAAAgG,UAAA,UAAAT,GAAA,CAAA7B,UAAA,kBAAqC;QAQxC1D,EAAA,CAAAI,SAAA,GAAuF;QAAvFJ,EAAA,CAAAkG,WAAA,iBAAAC,OAAA,GAAAZ,GAAA,CAAAjE,SAAA,CAAA4B,GAAA,4BAAAiD,OAAA,CAAAhD,OAAA,OAAAgD,OAAA,GAAAZ,GAAA,CAAAjE,SAAA,CAAA4B,GAAA,4BAAAiD,OAAA,CAAA/C,OAAA,EAAuF;QADvFpD,EAAA,CAAAgG,UAAA,gBAAAT,GAAA,CAAAvB,cAAA,gBAA6C;QAEhBhE,EAAA,CAAAI,SAAA,GAAwE;QAAxEJ,EAAA,CAAAgG,UAAA,WAAAI,OAAA,GAAAb,GAAA,CAAAjE,SAAA,CAAA4B,GAAA,4BAAAkD,OAAA,CAAAjD,OAAA,OAAAiD,OAAA,GAAAb,GAAA,CAAAjE,SAAA,CAAA4B,GAAA,4BAAAkD,OAAA,CAAAhD,OAAA,EAAwE;QAenGpD,EAAA,CAAAI,SAAA,GACA;QADAJ,EAAA,CAAAiG,kBAAA,MAAAV,GAAA,CAAA1B,QAAA,kBACA;QACG7D,EAAA,CAAAI,SAAA,GAAgC;QAAhCJ,EAAA,CAAAgG,UAAA,UAAAT,GAAA,CAAA7B,UAAA,aAAgC;QAQnC1D,EAAA,CAAAI,SAAA,GAA6F;QAA7FJ,EAAA,CAAAkG,WAAA,iBAAAG,OAAA,GAAAd,GAAA,CAAAjE,SAAA,CAAA4B,GAAA,+BAAAmD,OAAA,CAAAlD,OAAA,OAAAkD,OAAA,GAAAd,GAAA,CAAAjE,SAAA,CAAA4B,GAAA,+BAAAmD,OAAA,CAAAjD,OAAA,EAA6F;QAL7FpD,EAAA,CAAAgG,UAAA,SAAAT,GAAA,CAAAtE,YAAA,uBAA2C;QAO1CjB,EAAA,CAAAI,SAAA,GAAoD;QAApDJ,EAAA,CAAAgG,UAAA,YAAAT,GAAA,CAAAtE,YAAA,6BAAoD,UAAAsE,GAAA,CAAAtE,YAAA;QAGxBjB,EAAA,CAAAI,SAAA,GAA8E;QAA9EJ,EAAA,CAAAgG,UAAA,WAAAM,QAAA,GAAAf,GAAA,CAAAjE,SAAA,CAAA4B,GAAA,+BAAAoD,QAAA,CAAAnD,OAAA,OAAAmD,QAAA,GAAAf,GAAA,CAAAjE,SAAA,CAAA4B,GAAA,+BAAAoD,QAAA,CAAAlD,OAAA,EAA8E;QAiBxEpD,EAAA,CAAAI,SAAA,IAAoB;QAApBJ,EAAA,CAAAgG,UAAA,aAAAT,GAAA,CAAApE,OAAA,CAAoB;QACpDnB,EAAA,CAAAI,SAAA,GAAc;QAAdJ,EAAA,CAAAgG,UAAA,UAAAT,GAAA,CAAApE,OAAA,CAAc;QACdnB,EAAA,CAAAI,SAAA,GAAa;QAAbJ,EAAA,CAAAgG,UAAA,SAAAT,GAAA,CAAApE,OAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}