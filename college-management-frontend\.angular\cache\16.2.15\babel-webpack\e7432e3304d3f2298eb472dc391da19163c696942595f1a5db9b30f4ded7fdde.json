{"ast": null, "code": "import { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Inject, Input, EventEmitter, inject, Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Attribute, ContentChildren, Output, ContentChild, ViewChild, QueryList, forwardRef, Self, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i3 from '@angular/material/core';\nimport { mixinTabIndex, mixinColor, mixinDisableRipple, mixinDisabled, MatRippleLoader, MAT_RIPPLE_GLOBAL_OPTIONS, mixinErrorState, MatCommonModule, MatRippleModule, ErrorStateMatcher } from '@angular/material/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { FocusKeyManager } from '@angular/cdk/a11y';\nimport { Subject, merge } from 'rxjs';\nimport { take, takeUntil, startWith, switchMap } from 'rxjs/operators';\nimport { ENTER, SPACE, BACKSPACE, DELETE, TAB, hasModifierKey } from '@angular/cdk/keycodes';\nimport * as i1$1 from '@angular/cdk/bidi';\nimport * as i2$1 from '@angular/forms';\nimport { NG_VALUE_ACCESSOR, Validators } from '@angular/forms';\nimport * as i1$2 from '@angular/material/form-field';\nimport { MatFormFieldControl, MAT_FORM_FIELD } from '@angular/material/form-field';\n\n/** Injection token to be used to override the default options for the chips module. */\nfunction MatChip_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 7);\n    i0.ɵɵprojection(1, 1);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatChip_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 8);\n    i0.ɵɵprojection(1, 2);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = [\"*\", [[\"mat-chip-avatar\"], [\"\", \"matChipAvatar\", \"\"]], [[\"mat-chip-trailing-icon\"], [\"\", \"matChipRemove\", \"\"], [\"\", \"matChipTrailingIcon\", \"\"]]];\nconst _c1 = [\"*\", \"mat-chip-avatar, [matChipAvatar]\", \"mat-chip-trailing-icon,[matChipRemove],[matChipTrailingIcon]\"];\nfunction MatChipOption_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 8);\n    i0.ɵɵprojection(1, 1);\n    i0.ɵɵelementStart(2, \"span\", 9);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 10);\n    i0.ɵɵelement(4, \"path\", 11);\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction MatChipOption_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 12);\n    i0.ɵɵprojection(1, 2);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c2 = \".mdc-evolution-chip,.mdc-evolution-chip__cell,.mdc-evolution-chip__action{display:inline-flex;align-items:center}.mdc-evolution-chip{position:relative;max-width:100%}.mdc-evolution-chip .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}.mdc-evolution-chip__cell,.mdc-evolution-chip__action{height:100%}.mdc-evolution-chip__cell--primary{overflow-x:hidden}.mdc-evolution-chip__cell--trailing{flex:1 0 auto}.mdc-evolution-chip__action{align-items:center;background:none;border:none;box-sizing:content-box;cursor:pointer;display:inline-flex;justify-content:center;outline:none;padding:0;text-decoration:none;color:inherit}.mdc-evolution-chip__action--presentational{cursor:auto}.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{pointer-events:none}.mdc-evolution-chip__action--primary{overflow-x:hidden}.mdc-evolution-chip__action--trailing{position:relative;overflow:visible}.mdc-evolution-chip__action--primary:before{box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;pointer-events:none;top:0;width:100%;z-index:1}.mdc-evolution-chip--touch{margin-top:8px;margin-bottom:8px}.mdc-evolution-chip__action-touch{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%)}.mdc-evolution-chip__text-label{white-space:nowrap;user-select:none;text-overflow:ellipsis;overflow:hidden}.mdc-evolution-chip__graphic{align-items:center;display:inline-flex;justify-content:center;overflow:hidden;pointer-events:none;position:relative;flex:1 0 auto}.mdc-evolution-chip__checkmark{position:absolute;opacity:0;top:50%;left:50%}.mdc-evolution-chip--selectable:not(.mdc-evolution-chip--selected):not(.mdc-evolution-chip--with-primary-icon) .mdc-evolution-chip__graphic{width:0}.mdc-evolution-chip__checkmark-background{opacity:0}.mdc-evolution-chip__checkmark-svg{display:block}.mdc-evolution-chip__checkmark-path{stroke-width:2px;stroke-dasharray:29.7833385;stroke-dashoffset:29.7833385;stroke:currentColor}.mdc-evolution-chip--selecting .mdc-evolution-chip__graphic{transition:width 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark{transition:transform 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-75%, -50%)}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark-path{transition:stroke-dashoffset 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--deselecting .mdc-evolution-chip__graphic{transition:width 100ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--deselecting .mdc-evolution-chip__checkmark{transition:opacity 50ms 0ms linear,transform 100ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-75%, -50%)}.mdc-evolution-chip--deselecting .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}.mdc-evolution-chip--selecting-with-primary-icon .mdc-evolution-chip__icon--primary{transition:opacity 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selecting-with-primary-icon .mdc-evolution-chip__checkmark-path{transition:stroke-dashoffset 150ms 75ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--deselecting-with-primary-icon .mdc-evolution-chip__icon--primary{transition:opacity 150ms 75ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--deselecting-with-primary-icon .mdc-evolution-chip__checkmark{transition:opacity 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-50%, -50%)}.mdc-evolution-chip--deselecting-with-primary-icon .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}.mdc-evolution-chip--selected .mdc-evolution-chip__icon--primary{opacity:0}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{transform:translate(-50%, -50%);opacity:1}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}@keyframes mdc-evolution-chip-enter{from{transform:scale(0.8);opacity:.4}to{transform:scale(1);opacity:1}}.mdc-evolution-chip--enter{animation:mdc-evolution-chip-enter 100ms 0ms cubic-bezier(0, 0, 0.2, 1)}@keyframes mdc-evolution-chip-exit{from{opacity:1}to{opacity:0}}.mdc-evolution-chip--exit{animation:mdc-evolution-chip-exit 75ms 0ms cubic-bezier(0.4, 0, 1, 1)}.mdc-evolution-chip--hidden{opacity:0;pointer-events:none;transition:width 150ms 0ms cubic-bezier(0.4, 0, 1, 1)}.mat-mdc-standard-chip{border-radius:var(--mdc-chip-container-shape-radius);height:var(--mdc-chip-container-height);--mdc-chip-container-shape-family:rounded;--mdc-chip-container-shape-radius:16px 16px 16px 16px;--mdc-chip-with-avatar-avatar-shape-family:rounded;--mdc-chip-with-avatar-avatar-shape-radius:14px 14px 14px 14px;--mdc-chip-with-avatar-avatar-size:28px;--mdc-chip-with-icon-icon-size:18px}.mat-mdc-standard-chip .mdc-evolution-chip__ripple{border-radius:var(--mdc-chip-container-shape-radius)}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary:before{border-radius:var(--mdc-chip-container-shape-radius)}.mat-mdc-standard-chip .mdc-evolution-chip__icon--primary{border-radius:var(--mdc-chip-with-avatar-avatar-shape-radius)}.mat-mdc-standard-chip.mdc-evolution-chip--selectable:not(.mdc-evolution-chip--with-primary-icon){--mdc-chip-graphic-selected-width:var(--mdc-chip-with-avatar-avatar-size)}.mat-mdc-standard-chip .mdc-evolution-chip__graphic{height:var(--mdc-chip-with-avatar-avatar-size);width:var(--mdc-chip-with-avatar-avatar-size);font-size:var(--mdc-chip-with-avatar-avatar-size)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-container-color)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-elevated-disabled-container-color)}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-elevated-disabled-container-color)}.mat-mdc-standard-chip .mdc-evolution-chip__text-label{font-family:var(--mdc-chip-label-text-font);line-height:var(--mdc-chip-label-text-line-height);font-size:var(--mdc-chip-label-text-size);font-weight:var(--mdc-chip-label-text-weight);letter-spacing:var(--mdc-chip-label-text-tracking)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-label-text-color)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label{color:var(--mdc-chip-disabled-label-text-color)}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label{color:var(--mdc-chip-disabled-label-text-color)}.mat-mdc-standard-chip .mdc-evolution-chip__icon--primary{height:var(--mdc-chip-with-icon-icon-size);width:var(--mdc-chip-with-icon-icon-size);font-size:var(--mdc-chip-with-icon-icon-size)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-icon-color)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-disabled-icon-color)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-selected-icon-color)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-disabled-icon-color)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__icon--trailing{color:var(--mdc-chip-with-trailing-icon-trailing-icon-color)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{color:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-color)}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary.mdc-ripple-upgraded--background-focused .mdc-evolution-chip__ripple::before,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary:not(.mdc-ripple-upgraded):focus .mdc-evolution-chip__ripple::before{transition-duration:75ms;opacity:var(--mdc-chip-focus-state-layer-opacity)}.mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color);opacity:var(--mdc-chip-focus-state-layer-opacity)}.mat-mdc-standard-chip .mdc-evolution-chip__checkmark{height:20px;width:20px}.mat-mdc-standard-chip .mdc-evolution-chip__icon--trailing{height:18px;width:18px;font-size:18px}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:12px}[dir=rtl] .mat-mdc-standard-chip .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary[dir=rtl]{padding-left:12px;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic,.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic[dir=rtl]{padding-left:6px;padding-right:6px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary[dir=rtl]{padding-left:12px;padding-right:0}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing,.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing[dir=rtl]{padding-left:8px;padding-right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__ripple--trailing{left:8px;right:initial}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__ripple--trailing,.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__ripple--trailing[dir=rtl]{left:initial;right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary[dir=rtl]{padding-left:0;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic,.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic[dir=rtl]{padding-left:6px;padding-right:6px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing,.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing[dir=rtl]{padding-left:8px;padding-right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__ripple--trailing{left:8px;right:initial}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__ripple--trailing,.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__ripple--trailing[dir=rtl]{left:initial;right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary[dir=rtl]{padding-left:0;padding-right:0}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-selected-icon-color, currentColor)}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic,.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic[dir=rtl]{padding-left:8px;padding-right:4px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary,.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary[dir=rtl]{padding-left:12px;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic,.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic[dir=rtl]{padding-left:8px;padding-right:4px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing,.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing[dir=rtl]{padding-left:8px;padding-right:8px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__ripple--trailing{left:8px;right:initial}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__ripple--trailing,.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__ripple--trailing[dir=rtl]{left:initial;right:8px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary,.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary[dir=rtl]{padding-left:0;padding-right:0}.mat-mdc-standard-chip{-webkit-tap-highlight-color:rgba(0,0,0,0)}.cdk-high-contrast-active .mat-mdc-standard-chip{outline:solid 1px}.cdk-high-contrast-active .mat-mdc-standard-chip .mdc-evolution-chip__checkmark-path{stroke:CanvasText !important}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{opacity:.4}.mat-mdc-standard-chip .mdc-evolution-chip__cell--primary,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip .mat-mdc-chip-action-label{overflow:visible}.mat-mdc-standard-chip .mdc-evolution-chip__cell--primary{flex-basis:100%}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary{font:inherit;letter-spacing:inherit;white-space:inherit}.mat-mdc-standard-chip .mat-mdc-chip-graphic,.mat-mdc-standard-chip .mat-mdc-chip-trailing-icon{box-sizing:content-box}.mat-mdc-standard-chip._mat-animation-noopable,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__graphic,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark-path{transition-duration:1ms;animation-duration:1ms}.mat-mdc-basic-chip .mdc-evolution-chip__action--primary{font:inherit}.mat-mdc-chip-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;opacity:0;border-radius:inherit;transition:opacity 150ms linear}._mat-animation-noopable .mat-mdc-chip-focus-overlay{transition:none}.mat-mdc-basic-chip .mat-mdc-chip-focus-overlay{display:none}.mat-mdc-chip:hover .mat-mdc-chip-focus-overlay{opacity:.04}.mat-mdc-chip.cdk-focused .mat-mdc-chip-focus-overlay{opacity:.12}.mat-mdc-chip .mat-ripple.mat-mdc-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-chip-avatar{text-align:center;line-height:1;color:var(--mdc-chip-with-icon-icon-color, currentColor)}.mat-mdc-chip{position:relative;z-index:0}.mat-mdc-chip-action-label{text-align:left;z-index:1}[dir=rtl] .mat-mdc-chip-action-label{text-align:right}.mat-mdc-chip.mdc-evolution-chip--with-trailing-action .mat-mdc-chip-action-label{position:relative}.mat-mdc-chip-action-label .mat-mdc-chip-primary-focus-indicator{position:absolute;top:0;right:0;bottom:0;left:0;pointer-events:none}.mat-mdc-chip-action-label .mat-mdc-focus-indicator::before{margin:calc(calc(var(--mat-mdc-focus-indicator-border-width, 3px) + 2px) * -1)}.mat-mdc-chip-remove{opacity:.54}.mat-mdc-chip-remove:focus{opacity:1}.mat-mdc-chip-remove::before{margin:calc(var(--mat-mdc-focus-indicator-border-width, 3px) * -1);left:8px;right:8px}.mat-mdc-chip-remove .mat-icon{width:inherit;height:inherit;font-size:inherit;box-sizing:content-box}.mat-chip-edit-input{cursor:text;display:inline-block;color:inherit;outline:0}.cdk-high-contrast-active .mat-mdc-chip-selected:not(.mat-mdc-chip-multiple){outline-width:3px}.mat-mdc-chip-action:focus .mat-mdc-focus-indicator::before{content:\\\"\\\"}\";\nfunction MatChipRow_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"span\", 8);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction MatChipRow_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 9);\n    i0.ɵɵprojection(1);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatChipRow_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵprojection(1, 1);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction MatChipRow_ng_container_5_ng_content_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 2, [\"*ngIf\", \"contentEditInput; else defaultMatChipEditInput\"]);\n  }\n}\nfunction MatChipRow_ng_container_5_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 12);\n  }\n}\nfunction MatChipRow_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MatChipRow_ng_container_5_ng_content_1_Template, 1, 0, \"ng-content\", 10);\n    i0.ɵɵtemplate(2, MatChipRow_ng_container_5_ng_template_2_Template, 1, 0, \"ng-template\", null, 11, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const _r6 = i0.ɵɵreference(3);\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.contentEditInput)(\"ngIfElse\", _r6);\n  }\n}\nfunction MatChipRow_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵprojection(1, 3);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c3 = [[[\"mat-chip-avatar\"], [\"\", \"matChipAvatar\", \"\"]], \"*\", [[\"\", \"matChipEditInput\", \"\"]], [[\"mat-chip-trailing-icon\"], [\"\", \"matChipRemove\", \"\"], [\"\", \"matChipTrailingIcon\", \"\"]]];\nconst _c4 = [\"mat-chip-avatar, [matChipAvatar]\", \"*\", \"[matChipEditInput]\", \"mat-chip-trailing-icon,[matChipRemove],[matChipTrailingIcon]\"];\nconst _c5 = [\"*\"];\nconst _c6 = \".mdc-evolution-chip-set{display:flex}.mdc-evolution-chip-set:focus{outline:none}.mdc-evolution-chip-set__chips{display:flex;flex-flow:wrap;min-width:0}.mdc-evolution-chip-set--overflow .mdc-evolution-chip-set__chips{flex-flow:nowrap}.mdc-evolution-chip-set .mdc-evolution-chip-set__chips{margin-left:-8px;margin-right:0}[dir=rtl] .mdc-evolution-chip-set .mdc-evolution-chip-set__chips,.mdc-evolution-chip-set .mdc-evolution-chip-set__chips[dir=rtl]{margin-left:0;margin-right:-8px}.mdc-evolution-chip-set .mdc-evolution-chip{margin-left:8px;margin-right:0}[dir=rtl] .mdc-evolution-chip-set .mdc-evolution-chip,.mdc-evolution-chip-set .mdc-evolution-chip[dir=rtl]{margin-left:0;margin-right:8px}.mdc-evolution-chip-set .mdc-evolution-chip{margin-top:4px;margin-bottom:4px}.mat-mdc-chip-set .mdc-evolution-chip-set__chips{min-width:100%}.mat-mdc-chip-set-stacked{flex-direction:column;align-items:flex-start}.mat-mdc-chip-set-stacked .mat-mdc-chip{width:100%}.mat-mdc-chip-set-stacked .mdc-evolution-chip__graphic{flex-grow:0}.mat-mdc-chip-set-stacked .mdc-evolution-chip__action--primary{flex-basis:100%;justify-content:start}input.mat-mdc-chip-input{flex:1 0 150px;margin-left:8px}[dir=rtl] input.mat-mdc-chip-input{margin-left:0;margin-right:8px}\";\nconst MAT_CHIPS_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('mat-chips-default-options');\n/**\n * Injection token that can be used to reference instances of `MatChipAvatar`. It serves as\n * alternative token to the actual `MatChipAvatar` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_CHIP_AVATAR = /*#__PURE__*/new InjectionToken('MatChipAvatar');\n/**\n * Injection token that can be used to reference instances of `MatChipTrailingIcon`. It serves as\n * alternative token to the actual `MatChipTrailingIcon` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_CHIP_TRAILING_ICON = /*#__PURE__*/new InjectionToken('MatChipTrailingIcon');\n/**\n * Injection token that can be used to reference instances of `MatChipRemove`. It serves as\n * alternative token to the actual `MatChipRemove` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_CHIP_REMOVE = /*#__PURE__*/new InjectionToken('MatChipRemove');\n/**\n * Injection token used to avoid a circular dependency between the `MatChip` and `MatChipAction`.\n */\nconst MAT_CHIP = /*#__PURE__*/new InjectionToken('MatChip');\nclass _MatChipActionBase {}\nconst _MatChipActionMixinBase = /*#__PURE__*/mixinTabIndex(_MatChipActionBase, -1);\n/**\n * Section within a chip.\n * @docs-private\n */\nlet MatChipAction = /*#__PURE__*/(() => {\n  class MatChipAction extends _MatChipActionMixinBase {\n    /** Whether the action is disabled. */\n    get disabled() {\n      return this._disabled || this._parentChip.disabled;\n    }\n    set disabled(value) {\n      this._disabled = coerceBooleanProperty(value);\n    }\n    /**\n     * Determine the value of the disabled attribute for this chip action.\n     */\n    _getDisabledAttribute() {\n      // When this chip action is disabled and focusing disabled chips is not permitted, return empty\n      // string to indicate that disabled attribute should be included.\n      return this.disabled && !this._allowFocusWhenDisabled ? '' : null;\n    }\n    /**\n     * Determine the value of the tabindex attribute for this chip action.\n     */\n    _getTabindex() {\n      return this.disabled && !this._allowFocusWhenDisabled || !this.isInteractive ? null : this.tabIndex.toString();\n    }\n    constructor(_elementRef, _parentChip) {\n      super();\n      this._elementRef = _elementRef;\n      this._parentChip = _parentChip;\n      /** Whether the action is interactive. */\n      this.isInteractive = true;\n      /** Whether this is the primary action in the chip. */\n      this._isPrimary = true;\n      this._disabled = false;\n      /**\n       * Private API to allow focusing this chip when it is disabled.\n       */\n      this._allowFocusWhenDisabled = false;\n      if (_elementRef.nativeElement.nodeName === 'BUTTON') {\n        _elementRef.nativeElement.setAttribute('type', 'button');\n      }\n    }\n    focus() {\n      this._elementRef.nativeElement.focus();\n    }\n    _handleClick(event) {\n      if (!this.disabled && this.isInteractive && this._isPrimary) {\n        event.preventDefault();\n        this._parentChip._handlePrimaryActionInteraction();\n      }\n    }\n    _handleKeydown(event) {\n      if ((event.keyCode === ENTER || event.keyCode === SPACE) && !this.disabled && this.isInteractive && this._isPrimary && !this._parentChip._isEditing) {\n        event.preventDefault();\n        this._parentChip._handlePrimaryActionInteraction();\n      }\n    }\n    static #_ = this.ɵfac = function MatChipAction_Factory(t) {\n      return new (t || MatChipAction)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MAT_CHIP));\n    };\n    static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatChipAction,\n      selectors: [[\"\", \"matChipAction\", \"\"]],\n      hostAttrs: [1, \"mdc-evolution-chip__action\", \"mat-mdc-chip-action\"],\n      hostVars: 9,\n      hostBindings: function MatChipAction_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function MatChipAction_click_HostBindingHandler($event) {\n            return ctx._handleClick($event);\n          })(\"keydown\", function MatChipAction_keydown_HostBindingHandler($event) {\n            return ctx._handleKeydown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"tabindex\", ctx._getTabindex())(\"disabled\", ctx._getDisabledAttribute())(\"aria-disabled\", ctx.disabled);\n          i0.ɵɵclassProp(\"mdc-evolution-chip__action--primary\", ctx._isPrimary)(\"mdc-evolution-chip__action--presentational\", !ctx.isInteractive)(\"mdc-evolution-chip__action--trailing\", !ctx._isPrimary);\n        }\n      },\n      inputs: {\n        disabled: \"disabled\",\n        tabIndex: \"tabIndex\",\n        isInteractive: \"isInteractive\",\n        _allowFocusWhenDisabled: \"_allowFocusWhenDisabled\"\n      },\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return MatChipAction;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Avatar image within a chip. */\nlet MatChipAvatar = /*#__PURE__*/(() => {\n  class MatChipAvatar {\n    static #_ = this.ɵfac = function MatChipAvatar_Factory(t) {\n      return new (t || MatChipAvatar)();\n    };\n    static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatChipAvatar,\n      selectors: [[\"mat-chip-avatar\"], [\"\", \"matChipAvatar\", \"\"]],\n      hostAttrs: [\"role\", \"img\", 1, \"mat-mdc-chip-avatar\", \"mdc-evolution-chip__icon\", \"mdc-evolution-chip__icon--primary\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_CHIP_AVATAR,\n        useExisting: MatChipAvatar\n      }])]\n    });\n  }\n  return MatChipAvatar;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Non-interactive trailing icon in a chip. */\nlet MatChipTrailingIcon = /*#__PURE__*/(() => {\n  class MatChipTrailingIcon extends MatChipAction {\n    constructor() {\n      super(...arguments);\n      /**\n       * MDC considers all trailing actions as a remove icon,\n       * but we support non-interactive trailing icons.\n       */\n      this.isInteractive = false;\n      this._isPrimary = false;\n    }\n    static #_ = this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatChipTrailingIcon_BaseFactory;\n      return function MatChipTrailingIcon_Factory(t) {\n        return (ɵMatChipTrailingIcon_BaseFactory || (ɵMatChipTrailingIcon_BaseFactory = i0.ɵɵgetInheritedFactory(MatChipTrailingIcon)))(t || MatChipTrailingIcon);\n      };\n    }();\n    static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatChipTrailingIcon,\n      selectors: [[\"mat-chip-trailing-icon\"], [\"\", \"matChipTrailingIcon\", \"\"]],\n      hostAttrs: [\"aria-hidden\", \"true\", 1, \"mat-mdc-chip-trailing-icon\", \"mdc-evolution-chip__icon\", \"mdc-evolution-chip__icon--trailing\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_CHIP_TRAILING_ICON,\n        useExisting: MatChipTrailingIcon\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return MatChipTrailingIcon;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Directive to remove the parent chip when the trailing icon is clicked or\n * when the ENTER key is pressed on it.\n *\n * Recommended for use with the Material Design \"cancel\" icon\n * available at https://material.io/icons/#ic_cancel.\n *\n * Example:\n *\n * ```\n * <mat-chip>\n *   <mat-icon matChipRemove>cancel</mat-icon>\n * </mat-chip>\n * ```\n */\nlet MatChipRemove = /*#__PURE__*/(() => {\n  class MatChipRemove extends MatChipAction {\n    constructor() {\n      super(...arguments);\n      this._isPrimary = false;\n    }\n    _handleClick(event) {\n      if (!this.disabled) {\n        event.stopPropagation();\n        event.preventDefault();\n        this._parentChip.remove();\n      }\n    }\n    _handleKeydown(event) {\n      if ((event.keyCode === ENTER || event.keyCode === SPACE) && !this.disabled) {\n        event.stopPropagation();\n        event.preventDefault();\n        this._parentChip.remove();\n      }\n    }\n    static #_ = this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatChipRemove_BaseFactory;\n      return function MatChipRemove_Factory(t) {\n        return (ɵMatChipRemove_BaseFactory || (ɵMatChipRemove_BaseFactory = i0.ɵɵgetInheritedFactory(MatChipRemove)))(t || MatChipRemove);\n      };\n    }();\n    static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatChipRemove,\n      selectors: [[\"\", \"matChipRemove\", \"\"]],\n      hostAttrs: [\"role\", \"button\", 1, \"mat-mdc-chip-remove\", \"mat-mdc-chip-trailing-icon\", \"mat-mdc-focus-indicator\", \"mdc-evolution-chip__icon\", \"mdc-evolution-chip__icon--trailing\"],\n      hostVars: 1,\n      hostBindings: function MatChipRemove_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-hidden\", null);\n        }\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_CHIP_REMOVE,\n        useExisting: MatChipRemove\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return MatChipRemove;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet uid = 0;\n/**\n * Boilerplate for applying mixins to MatChip.\n * @docs-private\n */\nconst _MatChipMixinBase = /*#__PURE__*/mixinTabIndex( /*#__PURE__*/mixinColor( /*#__PURE__*/mixinDisableRipple( /*#__PURE__*/mixinDisabled(class {\n  constructor(_elementRef) {\n    this._elementRef = _elementRef;\n  }\n})), 'primary'), -1);\n/**\n * Material design styled Chip base component. Used inside the MatChipSet component.\n *\n * Extended by MatChipOption and MatChipRow for different interaction patterns.\n */\nlet MatChip = /*#__PURE__*/(() => {\n  class MatChip extends _MatChipMixinBase {\n    _hasFocus() {\n      return this._hasFocusInternal;\n    }\n    /**\n     * The value of the chip. Defaults to the content inside\n     * the `mat-mdc-chip-action-label` element.\n     */\n    get value() {\n      return this._value !== undefined ? this._value : this._textElement.textContent.trim();\n    }\n    set value(value) {\n      this._value = value;\n    }\n    /**\n     * Determines whether or not the chip displays the remove styling and emits (removed) events.\n     */\n    get removable() {\n      return this._removable;\n    }\n    set removable(value) {\n      this._removable = coerceBooleanProperty(value);\n    }\n    /**\n     * Colors the chip for emphasis as if it were selected.\n     */\n    get highlighted() {\n      return this._highlighted;\n    }\n    set highlighted(value) {\n      this._highlighted = coerceBooleanProperty(value);\n    }\n    /**\n     * Reference to the MatRipple instance of the chip.\n     * @deprecated Considered an implementation detail. To be removed.\n     * @breaking-change 17.0.0\n     */\n    get ripple() {\n      return this._rippleLoader?.getRipple(this._elementRef.nativeElement);\n    }\n    set ripple(v) {\n      this._rippleLoader?.attachRipple(this._elementRef.nativeElement, v);\n    }\n    constructor(_changeDetectorRef, elementRef, _ngZone, _focusMonitor, _document, animationMode, _globalRippleOptions, tabIndex) {\n      super(elementRef);\n      this._changeDetectorRef = _changeDetectorRef;\n      this._ngZone = _ngZone;\n      this._focusMonitor = _focusMonitor;\n      this._globalRippleOptions = _globalRippleOptions;\n      /** Emits when the chip is focused. */\n      this._onFocus = new Subject();\n      /** Emits when the chip is blurred. */\n      this._onBlur = new Subject();\n      /** Role for the root of the chip. */\n      this.role = null;\n      /** Whether the chip has focus. */\n      this._hasFocusInternal = false;\n      /** A unique id for the chip. If none is supplied, it will be auto-generated. */\n      this.id = `mat-mdc-chip-${uid++}`;\n      // TODO(#26104): Consider deprecating and using `_computeAriaAccessibleName` instead.\n      // `ariaLabel` may be unnecessary, and `_computeAriaAccessibleName` only supports\n      // datepicker's use case.\n      /** ARIA label for the content of the chip. */\n      this.ariaLabel = null;\n      // TODO(#26104): Consider deprecating and using `_computeAriaAccessibleName` instead.\n      // `ariaDescription` may be unnecessary, and `_computeAriaAccessibleName` only supports\n      // datepicker's use case.\n      /** ARIA description for the content of the chip. */\n      this.ariaDescription = null;\n      /** Id of a span that contains this chip's aria description. */\n      this._ariaDescriptionId = `${this.id}-aria-description`;\n      this._removable = true;\n      this._highlighted = false;\n      /** Emitted when a chip is to be removed. */\n      this.removed = new EventEmitter();\n      /** Emitted when the chip is destroyed. */\n      this.destroyed = new EventEmitter();\n      /** The unstyled chip selector for this component. */\n      this.basicChipAttrName = 'mat-basic-chip';\n      /**\n       * Handles the lazy creation of the MatChip ripple.\n       * Used to improve initial load time of large applications.\n       */\n      this._rippleLoader = inject(MatRippleLoader);\n      this._document = _document;\n      this._animationsDisabled = animationMode === 'NoopAnimations';\n      if (tabIndex != null) {\n        this.tabIndex = parseInt(tabIndex) ?? this.defaultTabIndex;\n      }\n      this._monitorFocus();\n      this._rippleLoader?.configureRipple(this._elementRef.nativeElement, {\n        className: 'mat-mdc-chip-ripple',\n        disabled: this._isRippleDisabled()\n      });\n    }\n    ngOnInit() {\n      // This check needs to happen in `ngOnInit` so the overridden value of\n      // `basicChipAttrName` coming from base classes can be picked up.\n      const element = this._elementRef.nativeElement;\n      this._isBasicChip = element.hasAttribute(this.basicChipAttrName) || element.tagName.toLowerCase() === this.basicChipAttrName;\n    }\n    ngAfterViewInit() {\n      this._textElement = this._elementRef.nativeElement.querySelector('.mat-mdc-chip-action-label');\n      if (this._pendingFocus) {\n        this._pendingFocus = false;\n        this.focus();\n      }\n    }\n    ngAfterContentInit() {\n      // Since the styling depends on the presence of some\n      // actions, we have to mark for check on changes.\n      this._actionChanges = merge(this._allLeadingIcons.changes, this._allTrailingIcons.changes, this._allRemoveIcons.changes).subscribe(() => this._changeDetectorRef.markForCheck());\n    }\n    ngDoCheck() {\n      this._rippleLoader.setDisabled(this._elementRef.nativeElement, this._isRippleDisabled());\n    }\n    ngOnDestroy() {\n      this._focusMonitor.stopMonitoring(this._elementRef);\n      this._rippleLoader?.destroyRipple(this._elementRef.nativeElement);\n      this._actionChanges?.unsubscribe();\n      this.destroyed.emit({\n        chip: this\n      });\n      this.destroyed.complete();\n    }\n    /**\n     * Allows for programmatic removal of the chip.\n     *\n     * Informs any listeners of the removal request. Does not remove the chip from the DOM.\n     */\n    remove() {\n      if (this.removable) {\n        this.removed.emit({\n          chip: this\n        });\n      }\n    }\n    /** Whether or not the ripple should be disabled. */\n    _isRippleDisabled() {\n      return this.disabled || this.disableRipple || this._animationsDisabled || this._isBasicChip || !!this._globalRippleOptions?.disabled;\n    }\n    /** Returns whether the chip has a trailing icon. */\n    _hasTrailingIcon() {\n      return !!(this.trailingIcon || this.removeIcon);\n    }\n    /** Handles keyboard events on the chip. */\n    _handleKeydown(event) {\n      if (event.keyCode === BACKSPACE || event.keyCode === DELETE) {\n        event.preventDefault();\n        this.remove();\n      }\n    }\n    /** Allows for programmatic focusing of the chip. */\n    focus() {\n      if (!this.disabled) {\n        // If `focus` is called before `ngAfterViewInit`, we won't have access to the primary action.\n        // This can happen if the consumer tries to focus a chip immediately after it is added.\n        // Queue the method to be called again on init.\n        if (this.primaryAction) {\n          this.primaryAction.focus();\n        } else {\n          this._pendingFocus = true;\n        }\n      }\n    }\n    /** Gets the action that contains a specific target node. */\n    _getSourceAction(target) {\n      return this._getActions().find(action => {\n        const element = action._elementRef.nativeElement;\n        return element === target || element.contains(target);\n      });\n    }\n    /** Gets all of the actions within the chip. */\n    _getActions() {\n      const result = [];\n      if (this.primaryAction) {\n        result.push(this.primaryAction);\n      }\n      if (this.removeIcon) {\n        result.push(this.removeIcon);\n      }\n      if (this.trailingIcon) {\n        result.push(this.trailingIcon);\n      }\n      return result;\n    }\n    /** Handles interactions with the primary action of the chip. */\n    _handlePrimaryActionInteraction() {\n      // Empty here, but is overwritten in child classes.\n    }\n    /** Starts the focus monitoring process on the chip. */\n    _monitorFocus() {\n      this._focusMonitor.monitor(this._elementRef, true).subscribe(origin => {\n        const hasFocus = origin !== null;\n        if (hasFocus !== this._hasFocusInternal) {\n          this._hasFocusInternal = hasFocus;\n          if (hasFocus) {\n            this._onFocus.next({\n              chip: this\n            });\n          } else {\n            // When animations are enabled, Angular may end up removing the chip from the DOM a little\n            // earlier than usual, causing it to be blurred and throwing off the logic in the chip list\n            // that moves focus not the next item. To work around the issue, we defer marking the chip\n            // as not focused until the next time the zone stabilizes.\n            this._ngZone.onStable.pipe(take(1)).subscribe(() => this._ngZone.run(() => this._onBlur.next({\n              chip: this\n            })));\n          }\n        }\n      });\n    }\n    static #_ = this.ɵfac = function MatChip_Factory(t) {\n      return new (t || MatChip)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1.FocusMonitor), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(MAT_RIPPLE_GLOBAL_OPTIONS, 8), i0.ɵɵinjectAttribute('tabindex'));\n    };\n    static #_2 = this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatChip,\n      selectors: [[\"mat-basic-chip\"], [\"\", \"mat-basic-chip\", \"\"], [\"mat-chip\"], [\"\", \"mat-chip\", \"\"]],\n      contentQueries: function MatChip_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_AVATAR, 5);\n          i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_TRAILING_ICON, 5);\n          i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_REMOVE, 5);\n          i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_AVATAR, 5);\n          i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_TRAILING_ICON, 5);\n          i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_REMOVE, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.leadingIcon = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.trailingIcon = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.removeIcon = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allLeadingIcons = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allTrailingIcons = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allRemoveIcons = _t);\n        }\n      },\n      viewQuery: function MatChip_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(MatChipAction, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.primaryAction = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mat-mdc-chip\"],\n      hostVars: 30,\n      hostBindings: function MatChip_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function MatChip_keydown_HostBindingHandler($event) {\n            return ctx._handleKeydown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"id\", ctx.id);\n          i0.ɵɵattribute(\"role\", ctx.role)(\"tabindex\", ctx.role ? ctx.tabIndex : null)(\"aria-label\", ctx.ariaLabel);\n          i0.ɵɵclassProp(\"mdc-evolution-chip\", !ctx._isBasicChip)(\"mdc-evolution-chip--disabled\", ctx.disabled)(\"mdc-evolution-chip--with-trailing-action\", ctx._hasTrailingIcon())(\"mdc-evolution-chip--with-primary-graphic\", ctx.leadingIcon)(\"mdc-evolution-chip--with-primary-icon\", ctx.leadingIcon)(\"mdc-evolution-chip--with-avatar\", ctx.leadingIcon)(\"mat-mdc-chip-with-avatar\", ctx.leadingIcon)(\"mat-mdc-chip-highlighted\", ctx.highlighted)(\"mat-mdc-chip-disabled\", ctx.disabled)(\"mat-mdc-basic-chip\", ctx._isBasicChip)(\"mat-mdc-standard-chip\", !ctx._isBasicChip)(\"mat-mdc-chip-with-trailing-icon\", ctx._hasTrailingIcon())(\"_mat-animation-noopable\", ctx._animationsDisabled);\n        }\n      },\n      inputs: {\n        color: \"color\",\n        disabled: \"disabled\",\n        disableRipple: \"disableRipple\",\n        tabIndex: \"tabIndex\",\n        role: \"role\",\n        id: \"id\",\n        ariaLabel: [\"aria-label\", \"ariaLabel\"],\n        ariaDescription: [\"aria-description\", \"ariaDescription\"],\n        value: \"value\",\n        removable: \"removable\",\n        highlighted: \"highlighted\"\n      },\n      outputs: {\n        removed: \"removed\",\n        destroyed: \"destroyed\"\n      },\n      exportAs: [\"matChip\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_CHIP,\n        useExisting: MatChip\n      }]), i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c1,\n      decls: 8,\n      vars: 3,\n      consts: [[1, \"mat-mdc-chip-focus-overlay\"], [1, \"mdc-evolution-chip__cell\", \"mdc-evolution-chip__cell--primary\"], [\"matChipAction\", \"\", 3, \"isInteractive\"], [\"class\", \"mdc-evolution-chip__graphic mat-mdc-chip-graphic\", 4, \"ngIf\"], [1, \"mdc-evolution-chip__text-label\", \"mat-mdc-chip-action-label\"], [1, \"mat-mdc-chip-primary-focus-indicator\", \"mat-mdc-focus-indicator\"], [\"class\", \"mdc-evolution-chip__cell mdc-evolution-chip__cell--trailing\", 4, \"ngIf\"], [1, \"mdc-evolution-chip__graphic\", \"mat-mdc-chip-graphic\"], [1, \"mdc-evolution-chip__cell\", \"mdc-evolution-chip__cell--trailing\"]],\n      template: function MatChip_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵelement(0, \"span\", 0);\n          i0.ɵɵelementStart(1, \"span\", 1)(2, \"span\", 2);\n          i0.ɵɵtemplate(3, MatChip_span_3_Template, 2, 0, \"span\", 3);\n          i0.ɵɵelementStart(4, \"span\", 4);\n          i0.ɵɵprojection(5);\n          i0.ɵɵelement(6, \"span\", 5);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(7, MatChip_span_7_Template, 2, 0, \"span\", 6);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"isInteractive\", false);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.leadingIcon);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx._hasTrailingIcon());\n        }\n      },\n      dependencies: [i2.NgIf, MatChipAction],\n      styles: [\".mdc-evolution-chip,.mdc-evolution-chip__cell,.mdc-evolution-chip__action{display:inline-flex;align-items:center}.mdc-evolution-chip{position:relative;max-width:100%}.mdc-evolution-chip .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}.mdc-evolution-chip__cell,.mdc-evolution-chip__action{height:100%}.mdc-evolution-chip__cell--primary{overflow-x:hidden}.mdc-evolution-chip__cell--trailing{flex:1 0 auto}.mdc-evolution-chip__action{align-items:center;background:none;border:none;box-sizing:content-box;cursor:pointer;display:inline-flex;justify-content:center;outline:none;padding:0;text-decoration:none;color:inherit}.mdc-evolution-chip__action--presentational{cursor:auto}.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{pointer-events:none}.mdc-evolution-chip__action--primary{overflow-x:hidden}.mdc-evolution-chip__action--trailing{position:relative;overflow:visible}.mdc-evolution-chip__action--primary:before{box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;pointer-events:none;top:0;width:100%;z-index:1}.mdc-evolution-chip--touch{margin-top:8px;margin-bottom:8px}.mdc-evolution-chip__action-touch{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%)}.mdc-evolution-chip__text-label{white-space:nowrap;user-select:none;text-overflow:ellipsis;overflow:hidden}.mdc-evolution-chip__graphic{align-items:center;display:inline-flex;justify-content:center;overflow:hidden;pointer-events:none;position:relative;flex:1 0 auto}.mdc-evolution-chip__checkmark{position:absolute;opacity:0;top:50%;left:50%}.mdc-evolution-chip--selectable:not(.mdc-evolution-chip--selected):not(.mdc-evolution-chip--with-primary-icon) .mdc-evolution-chip__graphic{width:0}.mdc-evolution-chip__checkmark-background{opacity:0}.mdc-evolution-chip__checkmark-svg{display:block}.mdc-evolution-chip__checkmark-path{stroke-width:2px;stroke-dasharray:29.7833385;stroke-dashoffset:29.7833385;stroke:currentColor}.mdc-evolution-chip--selecting .mdc-evolution-chip__graphic{transition:width 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark{transition:transform 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-75%, -50%)}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark-path{transition:stroke-dashoffset 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--deselecting .mdc-evolution-chip__graphic{transition:width 100ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--deselecting .mdc-evolution-chip__checkmark{transition:opacity 50ms 0ms linear,transform 100ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-75%, -50%)}.mdc-evolution-chip--deselecting .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}.mdc-evolution-chip--selecting-with-primary-icon .mdc-evolution-chip__icon--primary{transition:opacity 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selecting-with-primary-icon .mdc-evolution-chip__checkmark-path{transition:stroke-dashoffset 150ms 75ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--deselecting-with-primary-icon .mdc-evolution-chip__icon--primary{transition:opacity 150ms 75ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--deselecting-with-primary-icon .mdc-evolution-chip__checkmark{transition:opacity 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-50%, -50%)}.mdc-evolution-chip--deselecting-with-primary-icon .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}.mdc-evolution-chip--selected .mdc-evolution-chip__icon--primary{opacity:0}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{transform:translate(-50%, -50%);opacity:1}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}@keyframes mdc-evolution-chip-enter{from{transform:scale(0.8);opacity:.4}to{transform:scale(1);opacity:1}}.mdc-evolution-chip--enter{animation:mdc-evolution-chip-enter 100ms 0ms cubic-bezier(0, 0, 0.2, 1)}@keyframes mdc-evolution-chip-exit{from{opacity:1}to{opacity:0}}.mdc-evolution-chip--exit{animation:mdc-evolution-chip-exit 75ms 0ms cubic-bezier(0.4, 0, 1, 1)}.mdc-evolution-chip--hidden{opacity:0;pointer-events:none;transition:width 150ms 0ms cubic-bezier(0.4, 0, 1, 1)}.mat-mdc-standard-chip{border-radius:var(--mdc-chip-container-shape-radius);height:var(--mdc-chip-container-height);--mdc-chip-container-shape-family:rounded;--mdc-chip-container-shape-radius:16px 16px 16px 16px;--mdc-chip-with-avatar-avatar-shape-family:rounded;--mdc-chip-with-avatar-avatar-shape-radius:14px 14px 14px 14px;--mdc-chip-with-avatar-avatar-size:28px;--mdc-chip-with-icon-icon-size:18px}.mat-mdc-standard-chip .mdc-evolution-chip__ripple{border-radius:var(--mdc-chip-container-shape-radius)}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary:before{border-radius:var(--mdc-chip-container-shape-radius)}.mat-mdc-standard-chip .mdc-evolution-chip__icon--primary{border-radius:var(--mdc-chip-with-avatar-avatar-shape-radius)}.mat-mdc-standard-chip.mdc-evolution-chip--selectable:not(.mdc-evolution-chip--with-primary-icon){--mdc-chip-graphic-selected-width:var(--mdc-chip-with-avatar-avatar-size)}.mat-mdc-standard-chip .mdc-evolution-chip__graphic{height:var(--mdc-chip-with-avatar-avatar-size);width:var(--mdc-chip-with-avatar-avatar-size);font-size:var(--mdc-chip-with-avatar-avatar-size)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-container-color)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-elevated-disabled-container-color)}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-elevated-disabled-container-color)}.mat-mdc-standard-chip .mdc-evolution-chip__text-label{font-family:var(--mdc-chip-label-text-font);line-height:var(--mdc-chip-label-text-line-height);font-size:var(--mdc-chip-label-text-size);font-weight:var(--mdc-chip-label-text-weight);letter-spacing:var(--mdc-chip-label-text-tracking)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-label-text-color)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label{color:var(--mdc-chip-disabled-label-text-color)}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label{color:var(--mdc-chip-disabled-label-text-color)}.mat-mdc-standard-chip .mdc-evolution-chip__icon--primary{height:var(--mdc-chip-with-icon-icon-size);width:var(--mdc-chip-with-icon-icon-size);font-size:var(--mdc-chip-with-icon-icon-size)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-icon-color)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-disabled-icon-color)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-selected-icon-color)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-disabled-icon-color)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__icon--trailing{color:var(--mdc-chip-with-trailing-icon-trailing-icon-color)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{color:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-color)}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary.mdc-ripple-upgraded--background-focused .mdc-evolution-chip__ripple::before,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary:not(.mdc-ripple-upgraded):focus .mdc-evolution-chip__ripple::before{transition-duration:75ms;opacity:var(--mdc-chip-focus-state-layer-opacity)}.mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color);opacity:var(--mdc-chip-focus-state-layer-opacity)}.mat-mdc-standard-chip .mdc-evolution-chip__checkmark{height:20px;width:20px}.mat-mdc-standard-chip .mdc-evolution-chip__icon--trailing{height:18px;width:18px;font-size:18px}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:12px}[dir=rtl] .mat-mdc-standard-chip .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary[dir=rtl]{padding-left:12px;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic,.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic[dir=rtl]{padding-left:6px;padding-right:6px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary[dir=rtl]{padding-left:12px;padding-right:0}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing,.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing[dir=rtl]{padding-left:8px;padding-right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__ripple--trailing{left:8px;right:initial}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__ripple--trailing,.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__ripple--trailing[dir=rtl]{left:initial;right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary[dir=rtl]{padding-left:0;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic,.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic[dir=rtl]{padding-left:6px;padding-right:6px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing,.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing[dir=rtl]{padding-left:8px;padding-right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__ripple--trailing{left:8px;right:initial}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__ripple--trailing,.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__ripple--trailing[dir=rtl]{left:initial;right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary[dir=rtl]{padding-left:0;padding-right:0}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-selected-icon-color, currentColor)}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic,.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic[dir=rtl]{padding-left:8px;padding-right:4px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary,.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary[dir=rtl]{padding-left:12px;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic,.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic[dir=rtl]{padding-left:8px;padding-right:4px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing,.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing[dir=rtl]{padding-left:8px;padding-right:8px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__ripple--trailing{left:8px;right:initial}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__ripple--trailing,.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__ripple--trailing[dir=rtl]{left:initial;right:8px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary,.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary[dir=rtl]{padding-left:0;padding-right:0}.mat-mdc-standard-chip{-webkit-tap-highlight-color:rgba(0,0,0,0)}.cdk-high-contrast-active .mat-mdc-standard-chip{outline:solid 1px}.cdk-high-contrast-active .mat-mdc-standard-chip .mdc-evolution-chip__checkmark-path{stroke:CanvasText !important}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{opacity:.4}.mat-mdc-standard-chip .mdc-evolution-chip__cell--primary,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip .mat-mdc-chip-action-label{overflow:visible}.mat-mdc-standard-chip .mdc-evolution-chip__cell--primary{flex-basis:100%}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary{font:inherit;letter-spacing:inherit;white-space:inherit}.mat-mdc-standard-chip .mat-mdc-chip-graphic,.mat-mdc-standard-chip .mat-mdc-chip-trailing-icon{box-sizing:content-box}.mat-mdc-standard-chip._mat-animation-noopable,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__graphic,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark-path{transition-duration:1ms;animation-duration:1ms}.mat-mdc-basic-chip .mdc-evolution-chip__action--primary{font:inherit}.mat-mdc-chip-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;opacity:0;border-radius:inherit;transition:opacity 150ms linear}._mat-animation-noopable .mat-mdc-chip-focus-overlay{transition:none}.mat-mdc-basic-chip .mat-mdc-chip-focus-overlay{display:none}.mat-mdc-chip:hover .mat-mdc-chip-focus-overlay{opacity:.04}.mat-mdc-chip.cdk-focused .mat-mdc-chip-focus-overlay{opacity:.12}.mat-mdc-chip .mat-ripple.mat-mdc-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-chip-avatar{text-align:center;line-height:1;color:var(--mdc-chip-with-icon-icon-color, currentColor)}.mat-mdc-chip{position:relative;z-index:0}.mat-mdc-chip-action-label{text-align:left;z-index:1}[dir=rtl] .mat-mdc-chip-action-label{text-align:right}.mat-mdc-chip.mdc-evolution-chip--with-trailing-action .mat-mdc-chip-action-label{position:relative}.mat-mdc-chip-action-label .mat-mdc-chip-primary-focus-indicator{position:absolute;top:0;right:0;bottom:0;left:0;pointer-events:none}.mat-mdc-chip-action-label .mat-mdc-focus-indicator::before{margin:calc(calc(var(--mat-mdc-focus-indicator-border-width, 3px) + 2px) * -1)}.mat-mdc-chip-remove{opacity:.54}.mat-mdc-chip-remove:focus{opacity:1}.mat-mdc-chip-remove::before{margin:calc(var(--mat-mdc-focus-indicator-border-width, 3px) * -1);left:8px;right:8px}.mat-mdc-chip-remove .mat-icon{width:inherit;height:inherit;font-size:inherit;box-sizing:content-box}.mat-chip-edit-input{cursor:text;display:inline-block;color:inherit;outline:0}.cdk-high-contrast-active .mat-mdc-chip-selected:not(.mat-mdc-chip-multiple){outline-width:3px}.mat-mdc-chip-action:focus .mat-mdc-focus-indicator::before{content:\\\"\\\"}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatChip;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Event object emitted by MatChipOption when selected or deselected. */\nclass MatChipSelectionChange {\n  constructor( /** Reference to the chip that emitted the event. */\n  source, /** Whether the chip that emitted the event is selected. */\n  selected, /** Whether the selection change was a result of a user interaction. */\n  isUserInput = false) {\n    this.source = source;\n    this.selected = selected;\n    this.isUserInput = isUserInput;\n  }\n}\n/**\n * An extension of the MatChip component that supports chip selection. Used with MatChipListbox.\n *\n * Unlike other chips, the user can focus on disabled chip options inside a MatChipListbox. The\n * user cannot click disabled chips.\n */\nlet MatChipOption = /*#__PURE__*/(() => {\n  class MatChipOption extends MatChip {\n    constructor() {\n      super(...arguments);\n      /** Default chip options. */\n      this._defaultOptions = inject(MAT_CHIPS_DEFAULT_OPTIONS, {\n        optional: true\n      });\n      /** Whether the chip list is selectable. */\n      this.chipListSelectable = true;\n      /** Whether the chip list is in multi-selection mode. */\n      this._chipListMultiple = false;\n      /** Whether the chip list hides single-selection indicator. */\n      this._chipListHideSingleSelectionIndicator = this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n      this._selectable = true;\n      this._selected = false;\n      /** The unstyled chip selector for this component. */\n      this.basicChipAttrName = 'mat-basic-chip-option';\n      /** Emitted when the chip is selected or deselected. */\n      this.selectionChange = new EventEmitter();\n    }\n    /**\n     * Whether or not the chip is selectable.\n     *\n     * When a chip is not selectable, changes to its selected state are always\n     * ignored. By default an option chip is selectable, and it becomes\n     * non-selectable if its parent chip list is not selectable.\n     */\n    get selectable() {\n      return this._selectable && this.chipListSelectable;\n    }\n    set selectable(value) {\n      this._selectable = coerceBooleanProperty(value);\n      this._changeDetectorRef.markForCheck();\n    }\n    /** Whether the chip is selected. */\n    get selected() {\n      return this._selected;\n    }\n    set selected(value) {\n      this._setSelectedState(coerceBooleanProperty(value), false, true);\n    }\n    /**\n     * The ARIA selected applied to the chip. Conforms to WAI ARIA best practices for listbox\n     * interaction patterns.\n     *\n     * From [WAI ARIA Listbox authoring practices guide](\n     * https://www.w3.org/WAI/ARIA/apg/patterns/listbox/):\n     *  \"If any options are selected, each selected option has either aria-selected or aria-checked\n     *  set to true. All options that are selectable but not selected have either aria-selected or\n     *  aria-checked set to false.\"\n     *\n     * Set `aria-selected=\"false\"` on not-selected listbox options that are selectable to fix\n     * VoiceOver reading every option as \"selected\" (#25736).\n     */\n    get ariaSelected() {\n      return this.selectable ? this.selected.toString() : null;\n    }\n    ngOnInit() {\n      super.ngOnInit();\n      this.role = 'presentation';\n    }\n    /** Selects the chip. */\n    select() {\n      this._setSelectedState(true, false, true);\n    }\n    /** Deselects the chip. */\n    deselect() {\n      this._setSelectedState(false, false, true);\n    }\n    /** Selects this chip and emits userInputSelection event */\n    selectViaInteraction() {\n      this._setSelectedState(true, true, true);\n    }\n    /** Toggles the current selected state of this chip. */\n    toggleSelected(isUserInput = false) {\n      this._setSelectedState(!this.selected, isUserInput, true);\n      return this.selected;\n    }\n    _handlePrimaryActionInteraction() {\n      if (!this.disabled) {\n        // Interacting with the primary action implies that the chip already has focus, however\n        // there's a bug in Safari where focus ends up lingering on the previous chip (see #27544).\n        // We work around it by explicitly focusing the primary action of the current chip.\n        this.focus();\n        if (this.selectable) {\n          this.toggleSelected(true);\n        }\n      }\n    }\n    _hasLeadingGraphic() {\n      if (this.leadingIcon) {\n        return true;\n      }\n      // The checkmark graphic communicates selected state for both single-select and multi-select.\n      // Include checkmark in single-select to fix a11y issue where selected state is communicated\n      // visually only using color (#25886).\n      return !this._chipListHideSingleSelectionIndicator || this._chipListMultiple;\n    }\n    _setSelectedState(isSelected, isUserInput, emitEvent) {\n      if (isSelected !== this.selected) {\n        this._selected = isSelected;\n        if (emitEvent) {\n          this.selectionChange.emit({\n            source: this,\n            isUserInput,\n            selected: this.selected\n          });\n        }\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n    static #_ = this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatChipOption_BaseFactory;\n      return function MatChipOption_Factory(t) {\n        return (ɵMatChipOption_BaseFactory || (ɵMatChipOption_BaseFactory = i0.ɵɵgetInheritedFactory(MatChipOption)))(t || MatChipOption);\n      };\n    }();\n    static #_2 = this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatChipOption,\n      selectors: [[\"mat-basic-chip-option\"], [\"\", \"mat-basic-chip-option\", \"\"], [\"mat-chip-option\"], [\"\", \"mat-chip-option\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-chip\", \"mat-mdc-chip-option\"],\n      hostVars: 37,\n      hostBindings: function MatChipOption_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"id\", ctx.id);\n          i0.ɵɵattribute(\"tabindex\", null)(\"aria-label\", null)(\"aria-description\", null)(\"role\", ctx.role);\n          i0.ɵɵclassProp(\"mdc-evolution-chip\", !ctx._isBasicChip)(\"mdc-evolution-chip--filter\", !ctx._isBasicChip)(\"mdc-evolution-chip--selectable\", !ctx._isBasicChip)(\"mat-mdc-chip-selected\", ctx.selected)(\"mat-mdc-chip-multiple\", ctx._chipListMultiple)(\"mat-mdc-chip-disabled\", ctx.disabled)(\"mat-mdc-chip-with-avatar\", ctx.leadingIcon)(\"mdc-evolution-chip--disabled\", ctx.disabled)(\"mdc-evolution-chip--selected\", ctx.selected)(\"mdc-evolution-chip--selecting\", !ctx._animationsDisabled)(\"mdc-evolution-chip--with-trailing-action\", ctx._hasTrailingIcon())(\"mdc-evolution-chip--with-primary-icon\", ctx.leadingIcon)(\"mdc-evolution-chip--with-primary-graphic\", ctx._hasLeadingGraphic())(\"mdc-evolution-chip--with-avatar\", ctx.leadingIcon)(\"mat-mdc-chip-highlighted\", ctx.highlighted)(\"mat-mdc-chip-with-trailing-icon\", ctx._hasTrailingIcon());\n        }\n      },\n      inputs: {\n        color: \"color\",\n        disabled: \"disabled\",\n        disableRipple: \"disableRipple\",\n        tabIndex: \"tabIndex\",\n        selectable: \"selectable\",\n        selected: \"selected\"\n      },\n      outputs: {\n        selectionChange: \"selectionChange\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MatChip,\n        useExisting: MatChipOption\n      }, {\n        provide: MAT_CHIP,\n        useExisting: MatChipOption\n      }]), i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c1,\n      decls: 10,\n      vars: 9,\n      consts: [[1, \"mat-mdc-chip-focus-overlay\"], [1, \"mdc-evolution-chip__cell\", \"mdc-evolution-chip__cell--primary\"], [\"matChipAction\", \"\", \"role\", \"option\", 3, \"tabIndex\", \"_allowFocusWhenDisabled\"], [\"class\", \"mdc-evolution-chip__graphic mat-mdc-chip-graphic\", 4, \"ngIf\"], [1, \"mdc-evolution-chip__text-label\", \"mat-mdc-chip-action-label\"], [1, \"mat-mdc-chip-primary-focus-indicator\", \"mat-mdc-focus-indicator\"], [\"class\", \"mdc-evolution-chip__cell mdc-evolution-chip__cell--trailing\", 4, \"ngIf\"], [1, \"cdk-visually-hidden\", 3, \"id\"], [1, \"mdc-evolution-chip__graphic\", \"mat-mdc-chip-graphic\"], [1, \"mdc-evolution-chip__checkmark\"], [\"viewBox\", \"-2 -3 30 30\", \"focusable\", \"false\", \"aria-hidden\", \"true\", 1, \"mdc-evolution-chip__checkmark-svg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"d\", \"M1.73,12.91 8.1,19.28 22.79,4.59\", 1, \"mdc-evolution-chip__checkmark-path\"], [1, \"mdc-evolution-chip__cell\", \"mdc-evolution-chip__cell--trailing\"]],\n      template: function MatChipOption_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵelement(0, \"span\", 0);\n          i0.ɵɵelementStart(1, \"span\", 1)(2, \"button\", 2);\n          i0.ɵɵtemplate(3, MatChipOption_span_3_Template, 5, 0, \"span\", 3);\n          i0.ɵɵelementStart(4, \"span\", 4);\n          i0.ɵɵprojection(5);\n          i0.ɵɵelement(6, \"span\", 5);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(7, MatChipOption_span_7_Template, 2, 0, \"span\", 6);\n          i0.ɵɵelementStart(8, \"span\", 7);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"tabIndex\", ctx.tabIndex)(\"_allowFocusWhenDisabled\", true);\n          i0.ɵɵattribute(\"aria-selected\", ctx.ariaSelected)(\"aria-label\", ctx.ariaLabel)(\"aria-describedby\", ctx._ariaDescriptionId);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx._hasLeadingGraphic());\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx._hasTrailingIcon());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"id\", ctx._ariaDescriptionId);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate(ctx.ariaDescription);\n        }\n      },\n      dependencies: [i2.NgIf, MatChipAction],\n      styles: [_c2],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatChipOption;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * A directive that makes a span editable and exposes functions to modify and retrieve the\n * element's contents.\n */\nlet MatChipEditInput = /*#__PURE__*/(() => {\n  class MatChipEditInput {\n    constructor(_elementRef, _document) {\n      this._elementRef = _elementRef;\n      this._document = _document;\n    }\n    initialize(initialValue) {\n      this.getNativeElement().focus();\n      this.setValue(initialValue);\n    }\n    getNativeElement() {\n      return this._elementRef.nativeElement;\n    }\n    setValue(value) {\n      this.getNativeElement().textContent = value;\n      this._moveCursorToEndOfInput();\n    }\n    getValue() {\n      return this.getNativeElement().textContent || '';\n    }\n    _moveCursorToEndOfInput() {\n      const range = this._document.createRange();\n      range.selectNodeContents(this.getNativeElement());\n      range.collapse(false);\n      const sel = window.getSelection();\n      sel.removeAllRanges();\n      sel.addRange(range);\n    }\n    static #_ = this.ɵfac = function MatChipEditInput_Factory(t) {\n      return new (t || MatChipEditInput)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(DOCUMENT));\n    };\n    static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatChipEditInput,\n      selectors: [[\"span\", \"matChipEditInput\", \"\"]],\n      hostAttrs: [\"role\", \"textbox\", \"tabindex\", \"-1\", \"contenteditable\", \"true\", 1, \"mat-chip-edit-input\"]\n    });\n  }\n  return MatChipEditInput;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * An extension of the MatChip component used with MatChipGrid and\n * the matChipInputFor directive.\n */\nlet MatChipRow = /*#__PURE__*/(() => {\n  class MatChipRow extends MatChip {\n    constructor(changeDetectorRef, elementRef, ngZone, focusMonitor, _document, animationMode, globalRippleOptions, tabIndex) {\n      super(changeDetectorRef, elementRef, ngZone, focusMonitor, _document, animationMode, globalRippleOptions, tabIndex);\n      this.basicChipAttrName = 'mat-basic-chip-row';\n      /**\n       * The editing action has to be triggered in a timeout. While we're waiting on it, a blur\n       * event might occur which will interrupt the editing. This flag is used to avoid interruptions\n       * while the editing action is being initialized.\n       */\n      this._editStartPending = false;\n      this.editable = false;\n      /** Emitted when the chip is edited. */\n      this.edited = new EventEmitter();\n      this._isEditing = false;\n      this.role = 'row';\n      this._onBlur.pipe(takeUntil(this.destroyed)).subscribe(() => {\n        if (this._isEditing && !this._editStartPending) {\n          this._onEditFinish();\n        }\n      });\n    }\n    _hasTrailingIcon() {\n      // The trailing icon is hidden while editing.\n      return !this._isEditing && super._hasTrailingIcon();\n    }\n    /** Sends focus to the first gridcell when the user clicks anywhere inside the chip. */\n    _handleFocus() {\n      if (!this._isEditing && !this.disabled) {\n        this.focus();\n      }\n    }\n    _handleKeydown(event) {\n      if (event.keyCode === ENTER && !this.disabled) {\n        if (this._isEditing) {\n          event.preventDefault();\n          this._onEditFinish();\n        } else if (this.editable) {\n          this._startEditing(event);\n        }\n      } else if (this._isEditing) {\n        // Stop the event from reaching the chip set in order to avoid navigating.\n        event.stopPropagation();\n      } else {\n        super._handleKeydown(event);\n      }\n    }\n    _handleDoubleclick(event) {\n      if (!this.disabled && this.editable) {\n        this._startEditing(event);\n      }\n    }\n    _startEditing(event) {\n      if (!this.primaryAction || this.removeIcon && this._getSourceAction(event.target) === this.removeIcon) {\n        return;\n      }\n      // The value depends on the DOM so we need to extract it before we flip the flag.\n      const value = this.value;\n      this._isEditing = this._editStartPending = true;\n      // Starting the editing sequence below depends on the edit input\n      // query resolving on time. Trigger a synchronous change detection to\n      // ensure that it happens by the time we hit the timeout below.\n      this._changeDetectorRef.detectChanges();\n      // TODO(crisbeto): this timeout shouldn't be necessary given the `detectChange` call above.\n      // Defer initializing the input so it has time to be added to the DOM.\n      setTimeout(() => {\n        this._getEditInput().initialize(value);\n        this._editStartPending = false;\n      });\n    }\n    _onEditFinish() {\n      this._isEditing = this._editStartPending = false;\n      this.edited.emit({\n        chip: this,\n        value: this._getEditInput().getValue()\n      });\n      // If the edit input is still focused or focus was returned to the body after it was destroyed,\n      // return focus to the chip contents.\n      if (this._document.activeElement === this._getEditInput().getNativeElement() || this._document.activeElement === this._document.body) {\n        this.primaryAction.focus();\n      }\n    }\n    _isRippleDisabled() {\n      return super._isRippleDisabled() || this._isEditing;\n    }\n    /**\n     * Gets the projected chip edit input, or the default input if none is projected in. One of these\n     * two values is guaranteed to be defined.\n     */\n    _getEditInput() {\n      return this.contentEditInput || this.defaultEditInput;\n    }\n    static #_ = this.ɵfac = function MatChipRow_Factory(t) {\n      return new (t || MatChipRow)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1.FocusMonitor), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(MAT_RIPPLE_GLOBAL_OPTIONS, 8), i0.ɵɵinjectAttribute('tabindex'));\n    };\n    static #_2 = this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatChipRow,\n      selectors: [[\"mat-chip-row\"], [\"\", \"mat-chip-row\", \"\"], [\"mat-basic-chip-row\"], [\"\", \"mat-basic-chip-row\", \"\"]],\n      contentQueries: function MatChipRow_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatChipEditInput, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.contentEditInput = _t.first);\n        }\n      },\n      viewQuery: function MatChipRow_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(MatChipEditInput, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.defaultEditInput = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mat-mdc-chip\", \"mat-mdc-chip-row\", \"mdc-evolution-chip\"],\n      hostVars: 27,\n      hostBindings: function MatChipRow_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"focus\", function MatChipRow_focus_HostBindingHandler($event) {\n            return ctx._handleFocus($event);\n          })(\"dblclick\", function MatChipRow_dblclick_HostBindingHandler($event) {\n            return ctx._handleDoubleclick($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"id\", ctx.id);\n          i0.ɵɵattribute(\"tabindex\", ctx.disabled ? null : -1)(\"aria-label\", null)(\"aria-description\", null)(\"role\", ctx.role);\n          i0.ɵɵclassProp(\"mat-mdc-chip-with-avatar\", ctx.leadingIcon)(\"mat-mdc-chip-disabled\", ctx.disabled)(\"mat-mdc-chip-editing\", ctx._isEditing)(\"mat-mdc-chip-editable\", ctx.editable)(\"mdc-evolution-chip--disabled\", ctx.disabled)(\"mdc-evolution-chip--with-trailing-action\", ctx._hasTrailingIcon())(\"mdc-evolution-chip--with-primary-graphic\", ctx.leadingIcon)(\"mdc-evolution-chip--with-primary-icon\", ctx.leadingIcon)(\"mdc-evolution-chip--with-avatar\", ctx.leadingIcon)(\"mat-mdc-chip-highlighted\", ctx.highlighted)(\"mat-mdc-chip-with-trailing-icon\", ctx._hasTrailingIcon());\n        }\n      },\n      inputs: {\n        color: \"color\",\n        disabled: \"disabled\",\n        disableRipple: \"disableRipple\",\n        tabIndex: \"tabIndex\",\n        editable: \"editable\"\n      },\n      outputs: {\n        edited: \"edited\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MatChip,\n        useExisting: MatChipRow\n      }, {\n        provide: MAT_CHIP,\n        useExisting: MatChipRow\n      }]), i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c4,\n      decls: 10,\n      vars: 12,\n      consts: [[4, \"ngIf\"], [\"role\", \"gridcell\", \"matChipAction\", \"\", 1, \"mdc-evolution-chip__cell\", \"mdc-evolution-chip__cell--primary\", 3, \"tabIndex\", \"disabled\"], [\"class\", \"mdc-evolution-chip__graphic mat-mdc-chip-graphic\", 4, \"ngIf\"], [1, \"mdc-evolution-chip__text-label\", \"mat-mdc-chip-action-label\", 3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"aria-hidden\", \"true\", 1, \"mat-mdc-chip-primary-focus-indicator\", \"mat-mdc-focus-indicator\"], [\"class\", \"mdc-evolution-chip__cell mdc-evolution-chip__cell--trailing\", \"role\", \"gridcell\", 4, \"ngIf\"], [1, \"cdk-visually-hidden\", 3, \"id\"], [1, \"mat-mdc-chip-focus-overlay\"], [1, \"mdc-evolution-chip__graphic\", \"mat-mdc-chip-graphic\"], [4, \"ngIf\", \"ngIfElse\"], [\"defaultMatChipEditInput\", \"\"], [\"matChipEditInput\", \"\"], [\"role\", \"gridcell\", 1, \"mdc-evolution-chip__cell\", \"mdc-evolution-chip__cell--trailing\"]],\n      template: function MatChipRow_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c3);\n          i0.ɵɵtemplate(0, MatChipRow_ng_container_0_Template, 2, 0, \"ng-container\", 0);\n          i0.ɵɵelementStart(1, \"span\", 1);\n          i0.ɵɵtemplate(2, MatChipRow_span_2_Template, 2, 0, \"span\", 2);\n          i0.ɵɵelementStart(3, \"span\", 3);\n          i0.ɵɵtemplate(4, MatChipRow_ng_container_4_Template, 2, 0, \"ng-container\", 4);\n          i0.ɵɵtemplate(5, MatChipRow_ng_container_5_Template, 4, 2, \"ng-container\", 4);\n          i0.ɵɵelement(6, \"span\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(7, MatChipRow_span_7_Template, 2, 0, \"span\", 6);\n          i0.ɵɵelementStart(8, \"span\", 7);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", !ctx._isEditing);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"tabIndex\", ctx.tabIndex)(\"disabled\", ctx.disabled);\n          i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel)(\"aria-describedby\", ctx._ariaDescriptionId);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.leadingIcon);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngSwitch\", ctx._isEditing);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngSwitchCase\", false);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngSwitchCase\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx._hasTrailingIcon());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"id\", ctx._ariaDescriptionId);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate(ctx.ariaDescription);\n        }\n      },\n      dependencies: [i2.NgIf, i2.NgSwitch, i2.NgSwitchCase, MatChipAction, MatChipEditInput],\n      styles: [_c2],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatChipRow;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Boilerplate for applying mixins to MatChipSet.\n * @docs-private\n */\nclass MatChipSetBase {\n  constructor(_elementRef) {}\n}\nconst _MatChipSetMixinBase = /*#__PURE__*/mixinTabIndex(MatChipSetBase);\n/**\n * Basic container component for the MatChip component.\n *\n * Extended by MatChipListbox and MatChipGrid for different interaction patterns.\n */\nlet MatChipSet = /*#__PURE__*/(() => {\n  class MatChipSet extends _MatChipSetMixinBase {\n    /** Combined stream of all of the child chips' focus events. */\n    get chipFocusChanges() {\n      return this._getChipStream(chip => chip._onFocus);\n    }\n    /** Combined stream of all of the child chips' remove events. */\n    get chipDestroyedChanges() {\n      return this._getChipStream(chip => chip.destroyed);\n    }\n    /** Whether the chip set is disabled. */\n    get disabled() {\n      return this._disabled;\n    }\n    set disabled(value) {\n      this._disabled = coerceBooleanProperty(value);\n      this._syncChipsState();\n    }\n    /** Whether the chip list contains chips or not. */\n    get empty() {\n      return !this._chips || this._chips.length === 0;\n    }\n    /** The ARIA role applied to the chip set. */\n    get role() {\n      if (this._explicitRole) {\n        return this._explicitRole;\n      }\n      return this.empty ? null : this._defaultRole;\n    }\n    set role(value) {\n      this._explicitRole = value;\n    }\n    /** Whether any of the chips inside of this chip-set has focus. */\n    get focused() {\n      return this._hasFocusedChip();\n    }\n    constructor(_elementRef, _changeDetectorRef, _dir) {\n      super(_elementRef);\n      this._elementRef = _elementRef;\n      this._changeDetectorRef = _changeDetectorRef;\n      this._dir = _dir;\n      /** Index of the last destroyed chip that had focus. */\n      this._lastDestroyedFocusedChipIndex = null;\n      /** Subject that emits when the component has been destroyed. */\n      this._destroyed = new Subject();\n      /** Role to use if it hasn't been overwritten by the user. */\n      this._defaultRole = 'presentation';\n      this._disabled = false;\n      this._explicitRole = null;\n      /** Flat list of all the actions contained within the chips. */\n      this._chipActions = new QueryList();\n    }\n    ngAfterViewInit() {\n      this._setUpFocusManagement();\n      this._trackChipSetChanges();\n      this._trackDestroyedFocusedChip();\n    }\n    ngOnDestroy() {\n      this._keyManager?.destroy();\n      this._chipActions.destroy();\n      this._destroyed.next();\n      this._destroyed.complete();\n    }\n    /** Checks whether any of the chips is focused. */\n    _hasFocusedChip() {\n      return this._chips && this._chips.some(chip => chip._hasFocus());\n    }\n    /** Syncs the chip-set's state with the individual chips. */\n    _syncChipsState() {\n      if (this._chips) {\n        this._chips.forEach(chip => {\n          chip.disabled = this._disabled;\n          chip._changeDetectorRef.markForCheck();\n        });\n      }\n    }\n    /** Dummy method for subclasses to override. Base chip set cannot be focused. */\n    focus() {}\n    /** Handles keyboard events on the chip set. */\n    _handleKeydown(event) {\n      if (this._originatesFromChip(event)) {\n        this._keyManager.onKeydown(event);\n      }\n    }\n    /**\n     * Utility to ensure all indexes are valid.\n     *\n     * @param index The index to be checked.\n     * @returns True if the index is valid for our list of chips.\n     */\n    _isValidIndex(index) {\n      return index >= 0 && index < this._chips.length;\n    }\n    /**\n     * Removes the `tabindex` from the chip set and resets it back afterwards, allowing the\n     * user to tab out of it. This prevents the set from capturing focus and redirecting\n     * it back to the first chip, creating a focus trap, if it user tries to tab away.\n     */\n    _allowFocusEscape() {\n      if (this.tabIndex !== -1) {\n        const previousTabIndex = this.tabIndex;\n        this.tabIndex = -1;\n        // Note that this needs to be a `setTimeout`, because a `Promise.resolve`\n        // doesn't allow enough time for the focus to escape.\n        setTimeout(() => this.tabIndex = previousTabIndex);\n      }\n    }\n    /**\n     * Gets a stream of events from all the chips within the set.\n     * The stream will automatically incorporate any newly-added chips.\n     */\n    _getChipStream(mappingFunction) {\n      return this._chips.changes.pipe(startWith(null), switchMap(() => merge(...this._chips.map(mappingFunction))));\n    }\n    /** Checks whether an event comes from inside a chip element. */\n    _originatesFromChip(event) {\n      let currentElement = event.target;\n      while (currentElement && currentElement !== this._elementRef.nativeElement) {\n        if (currentElement.classList.contains('mat-mdc-chip')) {\n          return true;\n        }\n        currentElement = currentElement.parentElement;\n      }\n      return false;\n    }\n    /** Sets up the chip set's focus management logic. */\n    _setUpFocusManagement() {\n      // Create a flat `QueryList` containing the actions of all of the chips.\n      // This allows us to navigate both within the chip and move to the next/previous\n      // one using the existing `ListKeyManager`.\n      this._chips.changes.pipe(startWith(this._chips)).subscribe(chips => {\n        const actions = [];\n        chips.forEach(chip => chip._getActions().forEach(action => actions.push(action)));\n        this._chipActions.reset(actions);\n        this._chipActions.notifyOnChanges();\n      });\n      this._keyManager = new FocusKeyManager(this._chipActions).withVerticalOrientation().withHorizontalOrientation(this._dir ? this._dir.value : 'ltr').withHomeAndEnd().skipPredicate(action => this._skipPredicate(action));\n      // Keep the manager active index in sync so that navigation picks\n      // up from the current chip if the user clicks into the list directly.\n      this.chipFocusChanges.pipe(takeUntil(this._destroyed)).subscribe(({\n        chip\n      }) => {\n        const action = chip._getSourceAction(document.activeElement);\n        if (action) {\n          this._keyManager.updateActiveItem(action);\n        }\n      });\n      this._dir?.change.pipe(takeUntil(this._destroyed)).subscribe(direction => this._keyManager.withHorizontalOrientation(direction));\n    }\n    /**\n     * Determines if key manager should avoid putting a given chip action in the tab index. Skip\n     * non-interactive and disabled actions since the user can't do anything with them.\n     */\n    _skipPredicate(action) {\n      // Skip chips that the user cannot interact with. `mat-chip-set` does not permit focusing disabled\n      // chips.\n      return !action.isInteractive || action.disabled;\n    }\n    /** Listens to changes in the chip set and syncs up the state of the individual chips. */\n    _trackChipSetChanges() {\n      this._chips.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n        if (this.disabled) {\n          // Since this happens after the content has been\n          // checked, we need to defer it to the next tick.\n          Promise.resolve().then(() => this._syncChipsState());\n        }\n        this._redirectDestroyedChipFocus();\n      });\n    }\n    /** Starts tracking the destroyed chips in order to capture the focused one. */\n    _trackDestroyedFocusedChip() {\n      this.chipDestroyedChanges.pipe(takeUntil(this._destroyed)).subscribe(event => {\n        const chipArray = this._chips.toArray();\n        const chipIndex = chipArray.indexOf(event.chip);\n        // If the focused chip is destroyed, save its index so that we can move focus to the next\n        // chip. We only save the index here, rather than move the focus immediately, because we want\n        // to wait until the chip is removed from the chip list before focusing the next one. This\n        // allows us to keep focus on the same index if the chip gets swapped out.\n        if (this._isValidIndex(chipIndex) && event.chip._hasFocus()) {\n          this._lastDestroyedFocusedChipIndex = chipIndex;\n        }\n      });\n    }\n    /**\n     * Finds the next appropriate chip to move focus to,\n     * if the currently-focused chip is destroyed.\n     */\n    _redirectDestroyedChipFocus() {\n      if (this._lastDestroyedFocusedChipIndex == null) {\n        return;\n      }\n      if (this._chips.length) {\n        const newIndex = Math.min(this._lastDestroyedFocusedChipIndex, this._chips.length - 1);\n        const chipToFocus = this._chips.toArray()[newIndex];\n        if (chipToFocus.disabled) {\n          // If we're down to one disabled chip, move focus back to the set.\n          if (this._chips.length === 1) {\n            this.focus();\n          } else {\n            this._keyManager.setPreviousItemActive();\n          }\n        } else {\n          chipToFocus.focus();\n        }\n      } else {\n        this.focus();\n      }\n      this._lastDestroyedFocusedChipIndex = null;\n    }\n    static #_ = this.ɵfac = function MatChipSet_Factory(t) {\n      return new (t || MatChipSet)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.Directionality, 8));\n    };\n    static #_2 = this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatChipSet,\n      selectors: [[\"mat-chip-set\"]],\n      contentQueries: function MatChipSet_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatChip, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._chips = _t);\n        }\n      },\n      hostAttrs: [1, \"mat-mdc-chip-set\", \"mdc-evolution-chip-set\"],\n      hostVars: 1,\n      hostBindings: function MatChipSet_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function MatChipSet_keydown_HostBindingHandler($event) {\n            return ctx._handleKeydown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"role\", ctx.role);\n        }\n      },\n      inputs: {\n        disabled: \"disabled\",\n        role: \"role\"\n      },\n      features: [i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c5,\n      decls: 2,\n      vars: 0,\n      consts: [[\"role\", \"presentation\", 1, \"mdc-evolution-chip-set__chips\"]],\n      template: function MatChipSet_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [\".mdc-evolution-chip-set{display:flex}.mdc-evolution-chip-set:focus{outline:none}.mdc-evolution-chip-set__chips{display:flex;flex-flow:wrap;min-width:0}.mdc-evolution-chip-set--overflow .mdc-evolution-chip-set__chips{flex-flow:nowrap}.mdc-evolution-chip-set .mdc-evolution-chip-set__chips{margin-left:-8px;margin-right:0}[dir=rtl] .mdc-evolution-chip-set .mdc-evolution-chip-set__chips,.mdc-evolution-chip-set .mdc-evolution-chip-set__chips[dir=rtl]{margin-left:0;margin-right:-8px}.mdc-evolution-chip-set .mdc-evolution-chip{margin-left:8px;margin-right:0}[dir=rtl] .mdc-evolution-chip-set .mdc-evolution-chip,.mdc-evolution-chip-set .mdc-evolution-chip[dir=rtl]{margin-left:0;margin-right:8px}.mdc-evolution-chip-set .mdc-evolution-chip{margin-top:4px;margin-bottom:4px}.mat-mdc-chip-set .mdc-evolution-chip-set__chips{min-width:100%}.mat-mdc-chip-set-stacked{flex-direction:column;align-items:flex-start}.mat-mdc-chip-set-stacked .mat-mdc-chip{width:100%}.mat-mdc-chip-set-stacked .mdc-evolution-chip__graphic{flex-grow:0}.mat-mdc-chip-set-stacked .mdc-evolution-chip__action--primary{flex-basis:100%;justify-content:start}input.mat-mdc-chip-input{flex:1 0 150px;margin-left:8px}[dir=rtl] input.mat-mdc-chip-input{margin-left:0;margin-right:8px}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatChipSet;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Change event object that is emitted when the chip listbox value has changed. */\nclass MatChipListboxChange {\n  constructor( /** Chip listbox that emitted the event. */\n  source, /** Value of the chip listbox when the event was emitted. */\n  value) {\n    this.source = source;\n    this.value = value;\n  }\n}\n/**\n * Provider Expression that allows mat-chip-listbox to register as a ControlValueAccessor.\n * This allows it to support [(ngModel)].\n * @docs-private\n */\nconst MAT_CHIP_LISTBOX_CONTROL_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: /*#__PURE__*/forwardRef(() => MatChipListbox),\n  multi: true\n};\n/**\n * An extension of the MatChipSet component that supports chip selection.\n * Used with MatChipOption chips.\n */\nlet MatChipListbox = /*#__PURE__*/(() => {\n  class MatChipListbox extends MatChipSet {\n    constructor() {\n      super(...arguments);\n      /**\n       * Function when touched. Set as part of ControlValueAccessor implementation.\n       * @docs-private\n       */\n      this._onTouched = () => {};\n      /**\n       * Function when changed. Set as part of ControlValueAccessor implementation.\n       * @docs-private\n       */\n      this._onChange = () => {};\n      // TODO: MDC uses `grid` here\n      this._defaultRole = 'listbox';\n      /** Default chip options. */\n      this._defaultOptions = inject(MAT_CHIPS_DEFAULT_OPTIONS, {\n        optional: true\n      });\n      this._multiple = false;\n      /** Orientation of the chip list. */\n      this.ariaOrientation = 'horizontal';\n      this._selectable = true;\n      /**\n       * A function to compare the option values with the selected values. The first argument\n       * is a value from an option. The second is a value from the selection. A boolean\n       * should be returned.\n       */\n      this.compareWith = (o1, o2) => o1 === o2;\n      this._required = false;\n      this._hideSingleSelectionIndicator = this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n      /** Event emitted when the selected chip listbox value has been changed by the user. */\n      this.change = new EventEmitter();\n      this._chips = undefined;\n    }\n    /** Whether the user should be allowed to select multiple chips. */\n    get multiple() {\n      return this._multiple;\n    }\n    set multiple(value) {\n      this._multiple = coerceBooleanProperty(value);\n      this._syncListboxProperties();\n    }\n    /** The array of selected chips inside the chip listbox. */\n    get selected() {\n      const selectedChips = this._chips.toArray().filter(chip => chip.selected);\n      return this.multiple ? selectedChips : selectedChips[0];\n    }\n    /**\n     * Whether or not this chip listbox is selectable.\n     *\n     * When a chip listbox is not selectable, the selected states for all\n     * the chips inside the chip listbox are always ignored.\n     */\n    get selectable() {\n      return this._selectable;\n    }\n    set selectable(value) {\n      this._selectable = coerceBooleanProperty(value);\n      this._syncListboxProperties();\n    }\n    /** Whether this chip listbox is required. */\n    get required() {\n      return this._required;\n    }\n    set required(value) {\n      this._required = coerceBooleanProperty(value);\n    }\n    /** Whether checkmark indicator for single-selection options is hidden. */\n    get hideSingleSelectionIndicator() {\n      return this._hideSingleSelectionIndicator;\n    }\n    set hideSingleSelectionIndicator(value) {\n      this._hideSingleSelectionIndicator = coerceBooleanProperty(value);\n      this._syncListboxProperties();\n    }\n    /** Combined stream of all of the child chips' selection change events. */\n    get chipSelectionChanges() {\n      return this._getChipStream(chip => chip.selectionChange);\n    }\n    /** Combined stream of all of the child chips' blur events. */\n    get chipBlurChanges() {\n      return this._getChipStream(chip => chip._onBlur);\n    }\n    /** The value of the listbox, which is the combined value of the selected chips. */\n    get value() {\n      return this._value;\n    }\n    set value(value) {\n      this.writeValue(value);\n      this._value = value;\n    }\n    ngAfterContentInit() {\n      if (this._pendingInitialValue !== undefined) {\n        Promise.resolve().then(() => {\n          this._setSelectionByValue(this._pendingInitialValue, false);\n          this._pendingInitialValue = undefined;\n        });\n      }\n      this._chips.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n        // Update listbox selectable/multiple properties on chips\n        this._syncListboxProperties();\n      });\n      this.chipBlurChanges.pipe(takeUntil(this._destroyed)).subscribe(() => this._blur());\n      this.chipSelectionChanges.pipe(takeUntil(this._destroyed)).subscribe(event => {\n        if (!this.multiple) {\n          this._chips.forEach(chip => {\n            if (chip !== event.source) {\n              chip._setSelectedState(false, false, false);\n            }\n          });\n        }\n        if (event.isUserInput) {\n          this._propagateChanges();\n        }\n      });\n    }\n    /**\n     * Focuses the first selected chip in this chip listbox, or the first non-disabled chip when there\n     * are no selected chips.\n     */\n    focus() {\n      if (this.disabled) {\n        return;\n      }\n      const firstSelectedChip = this._getFirstSelectedChip();\n      if (firstSelectedChip && !firstSelectedChip.disabled) {\n        firstSelectedChip.focus();\n      } else if (this._chips.length > 0) {\n        this._keyManager.setFirstItemActive();\n      } else {\n        this._elementRef.nativeElement.focus();\n      }\n    }\n    /**\n     * Implemented as part of ControlValueAccessor.\n     * @docs-private\n     */\n    writeValue(value) {\n      if (this._chips) {\n        this._setSelectionByValue(value, false);\n      } else if (value != null) {\n        this._pendingInitialValue = value;\n      }\n    }\n    /**\n     * Implemented as part of ControlValueAccessor.\n     * @docs-private\n     */\n    registerOnChange(fn) {\n      this._onChange = fn;\n    }\n    /**\n     * Implemented as part of ControlValueAccessor.\n     * @docs-private\n     */\n    registerOnTouched(fn) {\n      this._onTouched = fn;\n    }\n    /**\n     * Implemented as part of ControlValueAccessor.\n     * @docs-private\n     */\n    setDisabledState(isDisabled) {\n      this.disabled = isDisabled;\n    }\n    /** Selects all chips with value. */\n    _setSelectionByValue(value, isUserInput = true) {\n      this._clearSelection();\n      if (Array.isArray(value)) {\n        value.forEach(currentValue => this._selectValue(currentValue, isUserInput));\n      } else {\n        this._selectValue(value, isUserInput);\n      }\n    }\n    /** When blurred, marks the field as touched when focus moved outside the chip listbox. */\n    _blur() {\n      if (!this.disabled) {\n        // Wait to see if focus moves to an individual chip.\n        setTimeout(() => {\n          if (!this.focused) {\n            this._markAsTouched();\n          }\n        });\n      }\n    }\n    _keydown(event) {\n      if (event.keyCode === TAB) {\n        super._allowFocusEscape();\n      }\n    }\n    /** Marks the field as touched */\n    _markAsTouched() {\n      this._onTouched();\n      this._changeDetectorRef.markForCheck();\n    }\n    /** Emits change event to set the model value. */\n    _propagateChanges() {\n      let valueToEmit = null;\n      if (Array.isArray(this.selected)) {\n        valueToEmit = this.selected.map(chip => chip.value);\n      } else {\n        valueToEmit = this.selected ? this.selected.value : undefined;\n      }\n      this._value = valueToEmit;\n      this.change.emit(new MatChipListboxChange(this, valueToEmit));\n      this._onChange(valueToEmit);\n      this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Deselects every chip in the listbox.\n     * @param skip Chip that should not be deselected.\n     */\n    _clearSelection(skip) {\n      this._chips.forEach(chip => {\n        if (chip !== skip) {\n          chip.deselect();\n        }\n      });\n    }\n    /**\n     * Finds and selects the chip based on its value.\n     * @returns Chip that has the corresponding value.\n     */\n    _selectValue(value, isUserInput) {\n      const correspondingChip = this._chips.find(chip => {\n        return chip.value != null && this.compareWith(chip.value, value);\n      });\n      if (correspondingChip) {\n        isUserInput ? correspondingChip.selectViaInteraction() : correspondingChip.select();\n      }\n      return correspondingChip;\n    }\n    /** Syncs the chip-listbox selection state with the individual chips. */\n    _syncListboxProperties() {\n      if (this._chips) {\n        // Defer setting the value in order to avoid the \"Expression\n        // has changed after it was checked\" errors from Angular.\n        Promise.resolve().then(() => {\n          this._chips.forEach(chip => {\n            chip._chipListMultiple = this.multiple;\n            chip.chipListSelectable = this._selectable;\n            chip._chipListHideSingleSelectionIndicator = this.hideSingleSelectionIndicator;\n            chip._changeDetectorRef.markForCheck();\n          });\n        });\n      }\n    }\n    /** Returns the first selected chip in this listbox, or undefined if no chips are selected. */\n    _getFirstSelectedChip() {\n      if (Array.isArray(this.selected)) {\n        return this.selected.length ? this.selected[0] : undefined;\n      } else {\n        return this.selected;\n      }\n    }\n    /**\n     * Determines if key manager should avoid putting a given chip action in the tab index. Skip\n     * non-interactive actions since the user can't do anything with them.\n     */\n    _skipPredicate(action) {\n      // Override the skip predicate in the base class to avoid skipping disabled chips. Allow\n      // disabled chip options to receive focus to align with WAI ARIA recommendation. Normally WAI\n      // ARIA's instructions are to exclude disabled items from the tab order, but it makes a few\n      // exceptions for compound widgets.\n      //\n      // From [Developing a Keyboard Interface](\n      // https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n      //   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n      //   Listbox...\"\n      return !action.isInteractive;\n    }\n    static #_ = this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatChipListbox_BaseFactory;\n      return function MatChipListbox_Factory(t) {\n        return (ɵMatChipListbox_BaseFactory || (ɵMatChipListbox_BaseFactory = i0.ɵɵgetInheritedFactory(MatChipListbox)))(t || MatChipListbox);\n      };\n    }();\n    static #_2 = this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatChipListbox,\n      selectors: [[\"mat-chip-listbox\"]],\n      contentQueries: function MatChipListbox_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatChipOption, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._chips = _t);\n        }\n      },\n      hostAttrs: [\"ngSkipHydration\", \"\", 1, \"mdc-evolution-chip-set\", \"mat-mdc-chip-listbox\"],\n      hostVars: 11,\n      hostBindings: function MatChipListbox_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"focus\", function MatChipListbox_focus_HostBindingHandler() {\n            return ctx.focus();\n          })(\"blur\", function MatChipListbox_blur_HostBindingHandler() {\n            return ctx._blur();\n          })(\"keydown\", function MatChipListbox_keydown_HostBindingHandler($event) {\n            return ctx._keydown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"tabIndex\", ctx.empty ? -1 : ctx.tabIndex);\n          i0.ɵɵattribute(\"role\", ctx.role)(\"aria-describedby\", ctx._ariaDescribedby || null)(\"aria-required\", ctx.role ? ctx.required : null)(\"aria-disabled\", ctx.disabled.toString())(\"aria-multiselectable\", ctx.multiple)(\"aria-orientation\", ctx.ariaOrientation);\n          i0.ɵɵclassProp(\"mat-mdc-chip-list-disabled\", ctx.disabled)(\"mat-mdc-chip-list-required\", ctx.required);\n        }\n      },\n      inputs: {\n        tabIndex: \"tabIndex\",\n        multiple: \"multiple\",\n        ariaOrientation: [\"aria-orientation\", \"ariaOrientation\"],\n        selectable: \"selectable\",\n        compareWith: \"compareWith\",\n        required: \"required\",\n        hideSingleSelectionIndicator: \"hideSingleSelectionIndicator\",\n        value: \"value\"\n      },\n      outputs: {\n        change: \"change\"\n      },\n      features: [i0.ɵɵProvidersFeature([MAT_CHIP_LISTBOX_CONTROL_VALUE_ACCESSOR]), i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c5,\n      decls: 2,\n      vars: 0,\n      consts: [[\"role\", \"presentation\", 1, \"mdc-evolution-chip-set__chips\"]],\n      template: function MatChipListbox_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [_c6],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatChipListbox;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Change event object that is emitted when the chip grid value has changed. */\nclass MatChipGridChange {\n  constructor( /** Chip grid that emitted the event. */\n  source, /** Value of the chip grid when the event was emitted. */\n  value) {\n    this.source = source;\n    this.value = value;\n  }\n}\n/**\n * Boilerplate for applying mixins to MatChipGrid.\n * @docs-private\n */\nclass MatChipGridBase extends MatChipSet {\n  constructor(elementRef, changeDetectorRef, dir, _defaultErrorStateMatcher, _parentForm, _parentFormGroup,\n  /**\n   * Form control bound to the component.\n   * Implemented as part of `MatFormFieldControl`.\n   * @docs-private\n   */\n  ngControl) {\n    super(elementRef, changeDetectorRef, dir);\n    this._defaultErrorStateMatcher = _defaultErrorStateMatcher;\n    this._parentForm = _parentForm;\n    this._parentFormGroup = _parentFormGroup;\n    this.ngControl = ngControl;\n    /**\n     * Emits whenever the component state changes and should cause the parent\n     * form-field to update. Implemented as part of `MatFormFieldControl`.\n     * @docs-private\n     */\n    this.stateChanges = new Subject();\n  }\n}\nconst _MatChipGridMixinBase = /*#__PURE__*/mixinErrorState(MatChipGridBase);\n/**\n * An extension of the MatChipSet component used with MatChipRow chips and\n * the matChipInputFor directive.\n */\nlet MatChipGrid = /*#__PURE__*/(() => {\n  class MatChipGrid extends _MatChipGridMixinBase {\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get disabled() {\n      return this.ngControl ? !!this.ngControl.disabled : this._disabled;\n    }\n    set disabled(value) {\n      this._disabled = coerceBooleanProperty(value);\n      this._syncChipsState();\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get id() {\n      return this._chipInput.id;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get empty() {\n      return (!this._chipInput || this._chipInput.empty) && (!this._chips || this._chips.length === 0);\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get placeholder() {\n      return this._chipInput ? this._chipInput.placeholder : this._placeholder;\n    }\n    set placeholder(value) {\n      this._placeholder = value;\n      this.stateChanges.next();\n    }\n    /** Whether any chips or the matChipInput inside of this chip-grid has focus. */\n    get focused() {\n      return this._chipInput.focused || this._hasFocusedChip();\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get required() {\n      return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n    }\n    set required(value) {\n      this._required = coerceBooleanProperty(value);\n      this.stateChanges.next();\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get shouldLabelFloat() {\n      return !this.empty || this.focused;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get value() {\n      return this._value;\n    }\n    set value(value) {\n      this._value = value;\n    }\n    /** Combined stream of all of the child chips' blur events. */\n    get chipBlurChanges() {\n      return this._getChipStream(chip => chip._onBlur);\n    }\n    constructor(elementRef, changeDetectorRef, dir, parentForm, parentFormGroup, defaultErrorStateMatcher, ngControl) {\n      super(elementRef, changeDetectorRef, dir, defaultErrorStateMatcher, parentForm, parentFormGroup, ngControl);\n      /**\n       * Implemented as part of MatFormFieldControl.\n       * @docs-private\n       */\n      this.controlType = 'mat-chip-grid';\n      this._defaultRole = 'grid';\n      /**\n       * List of element ids to propagate to the chipInput's aria-describedby attribute.\n       */\n      this._ariaDescribedbyIds = [];\n      /**\n       * Function when touched. Set as part of ControlValueAccessor implementation.\n       * @docs-private\n       */\n      this._onTouched = () => {};\n      /**\n       * Function when changed. Set as part of ControlValueAccessor implementation.\n       * @docs-private\n       */\n      this._onChange = () => {};\n      this._value = [];\n      /** Emits when the chip grid value has been changed by the user. */\n      this.change = new EventEmitter();\n      /**\n       * Emits whenever the raw value of the chip-grid changes. This is here primarily\n       * to facilitate the two-way binding for the `value` input.\n       * @docs-private\n       */\n      this.valueChange = new EventEmitter();\n      this._chips = undefined;\n      if (this.ngControl) {\n        this.ngControl.valueAccessor = this;\n      }\n    }\n    ngAfterContentInit() {\n      this.chipBlurChanges.pipe(takeUntil(this._destroyed)).subscribe(() => {\n        this._blur();\n        this.stateChanges.next();\n      });\n      merge(this.chipFocusChanges, this._chips.changes).pipe(takeUntil(this._destroyed)).subscribe(() => this.stateChanges.next());\n    }\n    ngAfterViewInit() {\n      super.ngAfterViewInit();\n      if (!this._chipInput && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('mat-chip-grid must be used in combination with matChipInputFor.');\n      }\n    }\n    ngDoCheck() {\n      if (this.ngControl) {\n        // We need to re-evaluate this on every change detection cycle, because there are some\n        // error triggers that we can't subscribe to (e.g. parent form submissions). This means\n        // that whatever logic is in here has to be super lean or we risk destroying the performance.\n        this.updateErrorState();\n      }\n    }\n    ngOnDestroy() {\n      super.ngOnDestroy();\n      this.stateChanges.complete();\n    }\n    /** Associates an HTML input element with this chip grid. */\n    registerInput(inputElement) {\n      this._chipInput = inputElement;\n      this._chipInput.setDescribedByIds(this._ariaDescribedbyIds);\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    onContainerClick(event) {\n      if (!this.disabled && !this._originatesFromChip(event)) {\n        this.focus();\n      }\n    }\n    /**\n     * Focuses the first chip in this chip grid, or the associated input when there\n     * are no eligible chips.\n     */\n    focus() {\n      if (this.disabled || this._chipInput.focused) {\n        return;\n      }\n      if (!this._chips.length || this._chips.first.disabled) {\n        // Delay until the next tick, because this can cause a \"changed after checked\"\n        // error if the input does something on focus (e.g. opens an autocomplete).\n        Promise.resolve().then(() => this._chipInput.focus());\n      } else if (this._chips.length) {\n        this._keyManager.setFirstItemActive();\n      }\n      this.stateChanges.next();\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    setDescribedByIds(ids) {\n      // We must keep this up to date to handle the case where ids are set\n      // before the chip input is registered.\n      this._ariaDescribedbyIds = ids;\n      this._chipInput?.setDescribedByIds(ids);\n    }\n    /**\n     * Implemented as part of ControlValueAccessor.\n     * @docs-private\n     */\n    writeValue(value) {\n      // The user is responsible for creating the child chips, so we just store the value.\n      this._value = value;\n    }\n    /**\n     * Implemented as part of ControlValueAccessor.\n     * @docs-private\n     */\n    registerOnChange(fn) {\n      this._onChange = fn;\n    }\n    /**\n     * Implemented as part of ControlValueAccessor.\n     * @docs-private\n     */\n    registerOnTouched(fn) {\n      this._onTouched = fn;\n    }\n    /**\n     * Implemented as part of ControlValueAccessor.\n     * @docs-private\n     */\n    setDisabledState(isDisabled) {\n      this.disabled = isDisabled;\n      this.stateChanges.next();\n    }\n    /** When blurred, mark the field as touched when focus moved outside the chip grid. */\n    _blur() {\n      if (!this.disabled) {\n        // Check whether the focus moved to chip input.\n        // If the focus is not moved to chip input, mark the field as touched. If the focus moved\n        // to chip input, do nothing.\n        // Timeout is needed to wait for the focus() event trigger on chip input.\n        setTimeout(() => {\n          if (!this.focused) {\n            this._propagateChanges();\n            this._markAsTouched();\n          }\n        });\n      }\n    }\n    /**\n     * Removes the `tabindex` from the chip grid and resets it back afterwards, allowing the\n     * user to tab out of it. This prevents the grid from capturing focus and redirecting\n     * it back to the first chip, creating a focus trap, if it user tries to tab away.\n     */\n    _allowFocusEscape() {\n      if (!this._chipInput.focused) {\n        super._allowFocusEscape();\n      }\n    }\n    /** Handles custom keyboard events. */\n    _handleKeydown(event) {\n      if (event.keyCode === TAB) {\n        if (this._chipInput.focused && hasModifierKey(event, 'shiftKey') && this._chips.length && !this._chips.last.disabled) {\n          event.preventDefault();\n          if (this._keyManager.activeItem) {\n            this._keyManager.setActiveItem(this._keyManager.activeItem);\n          } else {\n            this._focusLastChip();\n          }\n        } else {\n          // Use the super method here since it doesn't check for the input\n          // focused state. This allows focus to escape if there's only one\n          // disabled chip left in the list.\n          super._allowFocusEscape();\n        }\n      } else if (!this._chipInput.focused) {\n        super._handleKeydown(event);\n      }\n      this.stateChanges.next();\n    }\n    _focusLastChip() {\n      if (this._chips.length) {\n        this._chips.last.focus();\n      }\n    }\n    /** Emits change event to set the model value. */\n    _propagateChanges() {\n      const valueToEmit = this._chips.length ? this._chips.toArray().map(chip => chip.value) : [];\n      this._value = valueToEmit;\n      this.change.emit(new MatChipGridChange(this, valueToEmit));\n      this.valueChange.emit(valueToEmit);\n      this._onChange(valueToEmit);\n      this._changeDetectorRef.markForCheck();\n    }\n    /** Mark the field as touched */\n    _markAsTouched() {\n      this._onTouched();\n      this._changeDetectorRef.markForCheck();\n      this.stateChanges.next();\n    }\n    static #_ = this.ɵfac = function MatChipGrid_Factory(t) {\n      return new (t || MatChipGrid)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.Directionality, 8), i0.ɵɵdirectiveInject(i2$1.NgForm, 8), i0.ɵɵdirectiveInject(i2$1.FormGroupDirective, 8), i0.ɵɵdirectiveInject(i3.ErrorStateMatcher), i0.ɵɵdirectiveInject(i2$1.NgControl, 10));\n    };\n    static #_2 = this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatChipGrid,\n      selectors: [[\"mat-chip-grid\"]],\n      contentQueries: function MatChipGrid_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatChipRow, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._chips = _t);\n        }\n      },\n      hostAttrs: [1, \"mat-mdc-chip-set\", \"mat-mdc-chip-grid\", \"mdc-evolution-chip-set\"],\n      hostVars: 10,\n      hostBindings: function MatChipGrid_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"focus\", function MatChipGrid_focus_HostBindingHandler() {\n            return ctx.focus();\n          })(\"blur\", function MatChipGrid_blur_HostBindingHandler() {\n            return ctx._blur();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"tabIndex\", ctx._chips && ctx._chips.length === 0 ? -1 : ctx.tabIndex);\n          i0.ɵɵattribute(\"role\", ctx.role)(\"aria-disabled\", ctx.disabled.toString())(\"aria-invalid\", ctx.errorState);\n          i0.ɵɵclassProp(\"mat-mdc-chip-list-disabled\", ctx.disabled)(\"mat-mdc-chip-list-invalid\", ctx.errorState)(\"mat-mdc-chip-list-required\", ctx.required);\n        }\n      },\n      inputs: {\n        tabIndex: \"tabIndex\",\n        disabled: \"disabled\",\n        placeholder: \"placeholder\",\n        required: \"required\",\n        value: \"value\",\n        errorStateMatcher: \"errorStateMatcher\"\n      },\n      outputs: {\n        change: \"change\",\n        valueChange: \"valueChange\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MatFormFieldControl,\n        useExisting: MatChipGrid\n      }]), i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c5,\n      decls: 2,\n      vars: 0,\n      consts: [[\"role\", \"presentation\", 1, \"mdc-evolution-chip-set__chips\"]],\n      template: function MatChipGrid_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [_c6],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatChipGrid;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n// Increasing integer for generating unique ids.\nlet nextUniqueId = 0;\n/**\n * Directive that adds chip-specific behaviors to an input element inside `<mat-form-field>`.\n * May be placed inside or outside of a `<mat-chip-grid>`.\n */\nlet MatChipInput = /*#__PURE__*/(() => {\n  class MatChipInput {\n    /** Register input for chip list */\n    set chipGrid(value) {\n      if (value) {\n        this._chipGrid = value;\n        this._chipGrid.registerInput(this);\n      }\n    }\n    /**\n     * Whether or not the chipEnd event will be emitted when the input is blurred.\n     */\n    get addOnBlur() {\n      return this._addOnBlur;\n    }\n    set addOnBlur(value) {\n      this._addOnBlur = coerceBooleanProperty(value);\n    }\n    /** Whether the input is disabled. */\n    get disabled() {\n      return this._disabled || this._chipGrid && this._chipGrid.disabled;\n    }\n    set disabled(value) {\n      this._disabled = coerceBooleanProperty(value);\n    }\n    /** Whether the input is empty. */\n    get empty() {\n      return !this.inputElement.value;\n    }\n    constructor(_elementRef, defaultOptions, formField) {\n      this._elementRef = _elementRef;\n      /** Whether the control is focused. */\n      this.focused = false;\n      this._addOnBlur = false;\n      /** Emitted when a chip is to be added. */\n      this.chipEnd = new EventEmitter();\n      /** The input's placeholder text. */\n      this.placeholder = '';\n      /** Unique id for the input. */\n      this.id = `mat-mdc-chip-list-input-${nextUniqueId++}`;\n      this._disabled = false;\n      this.inputElement = this._elementRef.nativeElement;\n      this.separatorKeyCodes = defaultOptions.separatorKeyCodes;\n      if (formField) {\n        this.inputElement.classList.add('mat-mdc-form-field-input-control');\n      }\n    }\n    ngOnChanges() {\n      this._chipGrid.stateChanges.next();\n    }\n    ngOnDestroy() {\n      this.chipEnd.complete();\n    }\n    ngAfterContentInit() {\n      this._focusLastChipOnBackspace = this.empty;\n    }\n    /** Utility method to make host definition/tests more clear. */\n    _keydown(event) {\n      if (event) {\n        // To prevent the user from accidentally deleting chips when pressing BACKSPACE continuously,\n        // We focus the last chip on backspace only after the user has released the backspace button,\n        // And the input is empty (see behaviour in _keyup)\n        if (event.keyCode === BACKSPACE && this._focusLastChipOnBackspace) {\n          this._chipGrid._focusLastChip();\n          event.preventDefault();\n          return;\n        } else {\n          this._focusLastChipOnBackspace = false;\n        }\n      }\n      this._emitChipEnd(event);\n    }\n    /**\n     * Pass events to the keyboard manager. Available here for tests.\n     */\n    _keyup(event) {\n      // Allow user to move focus to chips next time he presses backspace\n      if (!this._focusLastChipOnBackspace && event.keyCode === BACKSPACE && this.empty) {\n        this._focusLastChipOnBackspace = true;\n        event.preventDefault();\n      }\n    }\n    /** Checks to see if the blur should emit the (chipEnd) event. */\n    _blur() {\n      if (this.addOnBlur) {\n        this._emitChipEnd();\n      }\n      this.focused = false;\n      // Blur the chip list if it is not focused\n      if (!this._chipGrid.focused) {\n        this._chipGrid._blur();\n      }\n      this._chipGrid.stateChanges.next();\n    }\n    _focus() {\n      this.focused = true;\n      this._focusLastChipOnBackspace = this.empty;\n      this._chipGrid.stateChanges.next();\n    }\n    /** Checks to see if the (chipEnd) event needs to be emitted. */\n    _emitChipEnd(event) {\n      if (!event || this._isSeparatorKey(event)) {\n        this.chipEnd.emit({\n          input: this.inputElement,\n          value: this.inputElement.value,\n          chipInput: this\n        });\n        event?.preventDefault();\n      }\n    }\n    _onInput() {\n      // Let chip list know whenever the value changes.\n      this._chipGrid.stateChanges.next();\n    }\n    /** Focuses the input. */\n    focus() {\n      this.inputElement.focus();\n    }\n    /** Clears the input */\n    clear() {\n      this.inputElement.value = '';\n      this._focusLastChipOnBackspace = true;\n    }\n    setDescribedByIds(ids) {\n      const element = this._elementRef.nativeElement;\n      // Set the value directly in the DOM since this binding\n      // is prone to \"changed after checked\" errors.\n      if (ids.length) {\n        element.setAttribute('aria-describedby', ids.join(' '));\n      } else {\n        element.removeAttribute('aria-describedby');\n      }\n    }\n    /** Checks whether a keycode is one of the configured separators. */\n    _isSeparatorKey(event) {\n      return !hasModifierKey(event) && new Set(this.separatorKeyCodes).has(event.keyCode);\n    }\n    static #_ = this.ɵfac = function MatChipInput_Factory(t) {\n      return new (t || MatChipInput)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MAT_CHIPS_DEFAULT_OPTIONS), i0.ɵɵdirectiveInject(MAT_FORM_FIELD, 8));\n    };\n    static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatChipInput,\n      selectors: [[\"input\", \"matChipInputFor\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-chip-input\", \"mat-mdc-input-element\", \"mdc-text-field__input\", \"mat-input-element\"],\n      hostVars: 6,\n      hostBindings: function MatChipInput_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function MatChipInput_keydown_HostBindingHandler($event) {\n            return ctx._keydown($event);\n          })(\"keyup\", function MatChipInput_keyup_HostBindingHandler($event) {\n            return ctx._keyup($event);\n          })(\"blur\", function MatChipInput_blur_HostBindingHandler() {\n            return ctx._blur();\n          })(\"focus\", function MatChipInput_focus_HostBindingHandler() {\n            return ctx._focus();\n          })(\"input\", function MatChipInput_input_HostBindingHandler() {\n            return ctx._onInput();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"id\", ctx.id);\n          i0.ɵɵattribute(\"disabled\", ctx.disabled || null)(\"placeholder\", ctx.placeholder || null)(\"aria-invalid\", ctx._chipGrid && ctx._chipGrid.ngControl ? ctx._chipGrid.ngControl.invalid : null)(\"aria-required\", ctx._chipGrid && ctx._chipGrid.required || null)(\"required\", ctx._chipGrid && ctx._chipGrid.required || null);\n        }\n      },\n      inputs: {\n        chipGrid: [\"matChipInputFor\", \"chipGrid\"],\n        addOnBlur: [\"matChipInputAddOnBlur\", \"addOnBlur\"],\n        separatorKeyCodes: [\"matChipInputSeparatorKeyCodes\", \"separatorKeyCodes\"],\n        placeholder: \"placeholder\",\n        id: \"id\",\n        disabled: \"disabled\"\n      },\n      outputs: {\n        chipEnd: \"matChipInputTokenEnd\"\n      },\n      exportAs: [\"matChipInput\", \"matChipInputFor\"],\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n  return MatChipInput;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst CHIP_DECLARATIONS = [MatChip, MatChipAvatar, MatChipEditInput, MatChipGrid, MatChipInput, MatChipListbox, MatChipOption, MatChipRemove, MatChipRow, MatChipSet, MatChipTrailingIcon];\nlet MatChipsModule = /*#__PURE__*/(() => {\n  class MatChipsModule {\n    static #_ = this.ɵfac = function MatChipsModule_Factory(t) {\n      return new (t || MatChipsModule)();\n    };\n    static #_2 = this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatChipsModule\n    });\n    static #_3 = this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [ErrorStateMatcher, {\n        provide: MAT_CHIPS_DEFAULT_OPTIONS,\n        useValue: {\n          separatorKeyCodes: [ENTER]\n        }\n      }],\n      imports: [MatCommonModule, CommonModule, MatRippleModule, MatCommonModule]\n    });\n  }\n  return MatChipsModule;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_CHIP, MAT_CHIPS_DEFAULT_OPTIONS, MAT_CHIP_AVATAR, MAT_CHIP_LISTBOX_CONTROL_VALUE_ACCESSOR, MAT_CHIP_REMOVE, MAT_CHIP_TRAILING_ICON, MatChip, MatChipAvatar, MatChipEditInput, MatChipGrid, MatChipGridChange, MatChipInput, MatChipListbox, MatChipListboxChange, MatChipOption, MatChipRemove, MatChipRow, MatChipSelectionChange, MatChipSet, MatChipTrailingIcon, MatChipsModule };\n//# sourceMappingURL=chips.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}