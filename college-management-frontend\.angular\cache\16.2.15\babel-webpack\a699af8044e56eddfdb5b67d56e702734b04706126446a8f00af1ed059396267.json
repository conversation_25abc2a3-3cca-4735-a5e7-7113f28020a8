{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/auth.service\";\nimport * as i4 from \"src/app/services/user.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/icon\";\nfunction UserFormComponent_form_9_div_7_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserFormComponent_form_9_div_7_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Name must be at least 2 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserFormComponent_form_9_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtemplate(1, UserFormComponent_form_9_div_7_small_1_Template, 2, 0, \"small\", 32);\n    i0.ɵɵtemplate(2, UserFormComponent_form_9_div_7_small_2_Template, 2, 0, \"small\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r2.userForm.get(\"name\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r2.userForm.get(\"name\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"minlength\"]);\n  }\n}\nfunction UserFormComponent_form_9_div_13_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Email is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserFormComponent_form_9_div_13_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Please enter a valid email\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserFormComponent_form_9_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtemplate(1, UserFormComponent_form_9_div_13_small_1_Template, 2, 0, \"small\", 32);\n    i0.ɵɵtemplate(2, UserFormComponent_form_9_div_13_small_2_Template, 2, 0, \"small\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r3.userForm.get(\"email\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r3.userForm.get(\"email\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"email\"]);\n  }\n}\nfunction UserFormComponent_form_9_div_14_div_9_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Password is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserFormComponent_form_9_div_14_div_9_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Password must be at least 6 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserFormComponent_form_9_div_14_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtemplate(1, UserFormComponent_form_9_div_14_div_9_small_1_Template, 2, 0, \"small\", 32);\n    i0.ɵɵtemplate(2, UserFormComponent_form_9_div_14_div_9_small_2_Template, 2, 0, \"small\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(3);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r21.userForm.get(\"password\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r21.userForm.get(\"password\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"minlength\"]);\n  }\n}\nfunction UserFormComponent_form_9_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"label\", 33);\n    i0.ɵɵtext(3, \"Password *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 34);\n    i0.ɵɵelement(5, \"input\", 35);\n    i0.ɵɵelementStart(6, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function UserFormComponent_form_9_div_14_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r24 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r24.togglePasswordVisibility());\n    });\n    i0.ɵɵelementStart(7, \"mat-icon\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(9, UserFormComponent_form_9_div_14_div_9_Template, 3, 2, \"div\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_3_0;\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_0_0 = ctx_r4.userForm.get(\"password\")) == null ? null : tmp_0_0.invalid) && ((tmp_0_0 = ctx_r4.userForm.get(\"password\")) == null ? null : tmp_0_0.touched));\n    i0.ɵɵproperty(\"type\", ctx_r4.showPassword ? \"text\" : \"password\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r4.showPassword ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r4.userForm.get(\"password\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r4.userForm.get(\"password\")) == null ? null : tmp_3_0.touched));\n  }\n}\nfunction UserFormComponent_form_9_option_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const role_r26 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", role_r26);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(role_r26);\n  }\n}\nfunction UserFormComponent_form_9_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"small\");\n    i0.ɵɵtext(2, \"Role is required\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserFormComponent_form_9_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"small\");\n    i0.ɵɵtext(2, \"Please enter a valid contact number (10-15 digits)\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserFormComponent_form_9_div_35_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"small\");\n    i0.ɵɵtext(2, \"Registration number is required for students\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserFormComponent_form_9_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"label\", 38);\n    i0.ɵɵtext(3, \"Registration Number *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 39);\n    i0.ɵɵtemplate(5, UserFormComponent_form_9_div_35_div_5_Template, 3, 0, \"div\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_0_0 = ctx_r8.userForm.get(\"regNo\")) == null ? null : tmp_0_0.invalid) && ((tmp_0_0 = ctx_r8.userForm.get(\"regNo\")) == null ? null : tmp_0_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r8.userForm.get(\"regNo\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r8.userForm.get(\"regNo\")) == null ? null : tmp_1_0.touched));\n  }\n}\nfunction UserFormComponent_form_9_div_36_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"small\");\n    i0.ɵɵtext(2, \"Roll number is required for students\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserFormComponent_form_9_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"label\", 40);\n    i0.ɵɵtext(3, \"Roll Number *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 41);\n    i0.ɵɵtemplate(5, UserFormComponent_form_9_div_36_div_5_Template, 3, 0, \"div\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_0_0 = ctx_r9.userForm.get(\"rollNo\")) == null ? null : tmp_0_0.invalid) && ((tmp_0_0 = ctx_r9.userForm.get(\"rollNo\")) == null ? null : tmp_0_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r9.userForm.get(\"rollNo\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r9.userForm.get(\"rollNo\")) == null ? null : tmp_1_0.touched));\n  }\n}\nfunction UserFormComponent_form_9_div_37_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"small\");\n    i0.ɵɵtext(2, \"Father's name is required for students\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserFormComponent_form_9_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"label\", 42);\n    i0.ɵɵtext(3, \"Father's Name *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 43);\n    i0.ɵɵtemplate(5, UserFormComponent_form_9_div_37_div_5_Template, 3, 0, \"div\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_0_0 = ctx_r10.userForm.get(\"father_name\")) == null ? null : tmp_0_0.invalid) && ((tmp_0_0 = ctx_r10.userForm.get(\"father_name\")) == null ? null : tmp_0_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r10.userForm.get(\"father_name\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r10.userForm.get(\"father_name\")) == null ? null : tmp_1_0.touched));\n  }\n}\nfunction UserFormComponent_form_9_div_38_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"small\");\n    i0.ɵɵtext(2, \"Designation is required\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserFormComponent_form_9_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"label\", 44);\n    i0.ɵɵtext(3, \"Designation *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 45);\n    i0.ɵɵtemplate(5, UserFormComponent_form_9_div_38_div_5_Template, 3, 0, \"div\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_0_0 = ctx_r11.userForm.get(\"designation\")) == null ? null : tmp_0_0.invalid) && ((tmp_0_0 = ctx_r11.userForm.get(\"designation\")) == null ? null : tmp_0_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r11.userForm.get(\"designation\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r11.userForm.get(\"designation\")) == null ? null : tmp_1_0.touched));\n  }\n}\nfunction UserFormComponent_form_9_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"label\", 46);\n    i0.ɵɵtext(3, \"Position\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 47);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserFormComponent_form_9_div_40_option_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dept_r33 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", dept_r33);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(dept_r33);\n  }\n}\nfunction UserFormComponent_form_9_div_40_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"small\");\n    i0.ɵɵtext(2, \"Department is required\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserFormComponent_form_9_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"label\", 48);\n    i0.ɵɵtext(3, \"Department *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"select\", 49)(5, \"option\", 18);\n    i0.ɵɵtext(6, \"Select Department\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, UserFormComponent_form_9_div_40_option_7_Template, 2, 2, \"option\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, UserFormComponent_form_9_div_40_div_8_Template, 3, 0, \"div\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_2_0;\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_0_0 = ctx_r13.userForm.get(\"department\")) == null ? null : tmp_0_0.invalid) && ((tmp_0_0 = ctx_r13.userForm.get(\"department\")) == null ? null : tmp_0_0.touched));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r13.departments);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r13.userForm.get(\"department\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r13.userForm.get(\"department\")) == null ? null : tmp_2_0.touched));\n  }\n}\nfunction UserFormComponent_form_9_div_41_option_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const cls_r35 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", cls_r35);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(cls_r35);\n  }\n}\nfunction UserFormComponent_form_9_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"label\", 50);\n    i0.ɵɵtext(3, \"Class\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"select\", 51)(5, \"option\", 18);\n    i0.ɵɵtext(6, \"Select Class\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, UserFormComponent_form_9_div_41_option_7_Template, 2, 2, \"option\", 19);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r14.classes);\n  }\n}\nfunction UserFormComponent_form_9_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"div\", 24);\n    i0.ɵɵelement(3, \"input\", 52);\n    i0.ɵɵelementStart(4, \"label\", 53);\n    i0.ɵɵtext(5, \" Visiting Faculty \");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction UserFormComponent_form_9_span_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 54);\n  }\n}\nfunction UserFormComponent_form_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 6);\n    i0.ɵɵlistener(\"ngSubmit\", function UserFormComponent_form_9_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.onSubmit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 7)(2, \"div\", 8)(3, \"div\", 9)(4, \"label\", 10);\n    i0.ɵɵtext(5, \"Full Name *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"input\", 11);\n    i0.ɵɵtemplate(7, UserFormComponent_form_9_div_7_Template, 3, 2, \"div\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 8)(9, \"div\", 9)(10, \"label\", 13);\n    i0.ɵɵtext(11, \"Email *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"input\", 14);\n    i0.ɵɵtemplate(13, UserFormComponent_form_9_div_13_Template, 3, 2, \"div\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(14, UserFormComponent_form_9_div_14_Template, 10, 5, \"div\", 15);\n    i0.ɵɵelementStart(15, \"div\", 8)(16, \"div\", 9)(17, \"label\", 16);\n    i0.ɵɵtext(18, \"Role *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"select\", 17)(20, \"option\", 18);\n    i0.ɵɵtext(21, \"Select Role\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, UserFormComponent_form_9_option_22_Template, 2, 2, \"option\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(23, UserFormComponent_form_9_div_23_Template, 3, 0, \"div\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 8)(25, \"div\", 9)(26, \"label\", 20);\n    i0.ɵɵtext(27, \"Contact Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(28, \"input\", 21);\n    i0.ɵɵtemplate(29, UserFormComponent_form_9_div_29_Template, 3, 0, \"div\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 8)(31, \"div\", 9)(32, \"label\", 22);\n    i0.ɵɵtext(33, \"Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(34, \"textarea\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(35, UserFormComponent_form_9_div_35_Template, 6, 3, \"div\", 15);\n    i0.ɵɵtemplate(36, UserFormComponent_form_9_div_36_Template, 6, 3, \"div\", 15);\n    i0.ɵɵtemplate(37, UserFormComponent_form_9_div_37_Template, 6, 3, \"div\", 15);\n    i0.ɵɵtemplate(38, UserFormComponent_form_9_div_38_Template, 6, 3, \"div\", 15);\n    i0.ɵɵtemplate(39, UserFormComponent_form_9_div_39_Template, 5, 0, \"div\", 15);\n    i0.ɵɵtemplate(40, UserFormComponent_form_9_div_40_Template, 9, 4, \"div\", 15);\n    i0.ɵɵtemplate(41, UserFormComponent_form_9_div_41_Template, 8, 1, \"div\", 15);\n    i0.ɵɵelementStart(42, \"div\", 8)(43, \"div\", 9)(44, \"div\", 24);\n    i0.ɵɵelement(45, \"input\", 25);\n    i0.ɵɵelementStart(46, \"label\", 26);\n    i0.ɵɵtext(47, \" Active User \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(48, UserFormComponent_form_9_div_48_Template, 6, 0, \"div\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 27)(50, \"button\", 28);\n    i0.ɵɵtemplate(51, UserFormComponent_form_9_span_51_Template, 1, 0, \"span\", 29);\n    i0.ɵɵtext(52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function UserFormComponent_form_9_Template_button_click_53_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.cancel());\n    });\n    i0.ɵɵtext(54, \" Cancel \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    let tmp_6_0;\n    let tmp_8_0;\n    let tmp_9_0;\n    let tmp_10_0;\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.userForm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_1_0 = ctx_r0.userForm.get(\"name\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r0.userForm.get(\"name\")) == null ? null : tmp_1_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r0.userForm.get(\"name\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r0.userForm.get(\"name\")) == null ? null : tmp_2_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_3_0 = ctx_r0.userForm.get(\"email\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r0.userForm.get(\"email\")) == null ? null : tmp_3_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r0.userForm.get(\"email\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx_r0.userForm.get(\"email\")) == null ? null : tmp_4_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isEditMode);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_6_0 = ctx_r0.userForm.get(\"role\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx_r0.userForm.get(\"role\")) == null ? null : tmp_6_0.touched));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.roles);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx_r0.userForm.get(\"role\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx_r0.userForm.get(\"role\")) == null ? null : tmp_8_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_9_0 = ctx_r0.userForm.get(\"contact\")) == null ? null : tmp_9_0.invalid) && ((tmp_9_0 = ctx_r0.userForm.get(\"contact\")) == null ? null : tmp_9_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_10_0 = ctx_r0.userForm.get(\"contact\")) == null ? null : tmp_10_0.invalid) && ((tmp_10_0 = ctx_r0.userForm.get(\"contact\")) == null ? null : tmp_10_0.touched));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isStudent);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isStudent);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isStudent);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isTeacher || ctx_r0.isPrincipal);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isTeacher || ctx_r0.isPrincipal);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isStudent || ctx_r0.isTeacher || ctx_r0.isPrincipal);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isStudent);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isTeacher);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.loading || ctx_r0.userForm.invalid);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.loading ? \"Saving...\" : ctx_r0.isEditMode ? \"Update User\" : \"Create User\", \" \");\n  }\n}\nfunction UserFormComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 56)(2, \"span\", 57);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 58);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.isEditMode ? \"Loading user data...\" : \"Creating user...\");\n  }\n}\nexport class UserFormComponent {\n  constructor(fb, route, router, authService, userService) {\n    this.fb = fb;\n    this.route = route;\n    this.router = router;\n    this.authService = authService;\n    this.userService = userService;\n    this.isEditMode = false;\n    this.userId = null;\n    this.loading = false;\n    this.showPassword = false;\n    this.roles = ['Student', 'Teacher', 'Principal', 'Admin'];\n    this.departments = ['Pre-Medical', 'Pre-Engineering', 'Computer Science', 'Arts', 'Commerce'];\n    this.classes = ['1st Year', '2nd Year'];\n    this.sections = ['A', 'B', 'C'];\n  }\n  ngOnInit() {\n    this.userId = this.route.snapshot.paramMap.get('id');\n    this.isEditMode = !!this.userId;\n    this.initializeForm();\n    if (this.isEditMode) {\n      this.loadUser();\n    }\n  }\n  initializeForm() {\n    this.userForm = this.fb.group({\n      name: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      password: [this.isEditMode ? '' : '', this.isEditMode ? [] : [Validators.required, Validators.minLength(6)]],\n      role: ['', Validators.required],\n      address: [''],\n      regNo: [''],\n      rollNo: [''],\n      contact: ['', [Validators.pattern(/^\\d{10,15}$/)]],\n      father_name: [''],\n      department: [''],\n      designation: [''],\n      position: [''],\n      isVisiting: [false],\n      classId: [''],\n      isActive: [true]\n    });\n    // Add conditional validators based on role\n    this.userForm.get('role')?.valueChanges.subscribe(role => {\n      this.updateValidatorsBasedOnRole(role);\n    });\n  }\n  updateValidatorsBasedOnRole(role) {\n    const regNoControl = this.userForm.get('regNo');\n    const rollNoControl = this.userForm.get('rollNo');\n    const departmentControl = this.userForm.get('department');\n    const fatherNameControl = this.userForm.get('father_name');\n    const designationControl = this.userForm.get('designation');\n    // Clear existing validators\n    regNoControl?.clearValidators();\n    rollNoControl?.clearValidators();\n    departmentControl?.clearValidators();\n    fatherNameControl?.clearValidators();\n    designationControl?.clearValidators();\n    if (role === 'Student') {\n      regNoControl?.setValidators([Validators.required]);\n      rollNoControl?.setValidators([Validators.required]);\n      departmentControl?.setValidators([Validators.required]);\n      fatherNameControl?.setValidators([Validators.required]);\n    } else if (role === 'Teacher' || role === 'Principal') {\n      departmentControl?.setValidators([Validators.required]);\n      designationControl?.setValidators([Validators.required]);\n    }\n    // Update validity\n    regNoControl?.updateValueAndValidity();\n    rollNoControl?.updateValueAndValidity();\n    departmentControl?.updateValueAndValidity();\n    fatherNameControl?.updateValueAndValidity();\n    designationControl?.updateValueAndValidity();\n  }\n  loadUser() {\n    if (!this.userId) return;\n    this.loading = true;\n    // Since we don't have a get single user API, we'll get all users and find the one we need\n    this.authService.getAllUsers().subscribe({\n      next: response => {\n        this.loading = false;\n        if (response.success) {\n          const user = response.users.find(u => u._id === this.userId);\n          if (user) {\n            this.populateForm(user);\n          } else {\n            this.showError('User not found');\n            this.router.navigate(['/dashboard/admin/users']);\n          }\n        }\n      },\n      error: error => {\n        this.loading = false;\n        console.error('Error loading user:', error);\n        this.showError('Failed to load user data');\n      }\n    });\n  }\n  populateForm(user) {\n    this.userForm.patchValue({\n      name: user.name,\n      email: user.email,\n      role: user.role,\n      address: user.address || '',\n      regNo: user.regNo || '',\n      rollNo: user.rollNo || '',\n      contact: user.contact || '',\n      father_name: user.father_name || '',\n      department: user.department || '',\n      designation: user.designation || '',\n      position: user.position || '',\n      isVisiting: user.isVisiting || false,\n      classId: user.classId || '',\n      isActive: user.isActive !== false\n    });\n  }\n  onSubmit() {\n    if (this.userForm.valid) {\n      this.loading = true;\n      const formData = this.userForm.value;\n      // Remove password if it's empty in edit mode\n      if (this.isEditMode && !formData.password) {\n        delete formData.password;\n      }\n      if (this.isEditMode) {\n        this.updateUser(formData);\n      } else {\n        this.createUser(formData);\n      }\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n  createUser(userData) {\n    this.authService.signup(userData).subscribe({\n      next: response => {\n        this.loading = false;\n        if (response.success) {\n          Swal.fire({\n            title: 'Success!',\n            text: 'User created successfully.',\n            icon: 'success',\n            confirmButtonColor: '#29578c'\n          }).then(() => {\n            this.router.navigate(['/dashboard/admin/users']);\n          });\n        }\n      },\n      error: error => {\n        this.loading = false;\n        console.error('Error creating user:', error);\n        const errorMessage = error.error?.message || 'Failed to create user';\n        this.showError(errorMessage);\n      }\n    });\n  }\n  updateUser(userData) {\n    // For now, we'll use the register endpoint to update user data\n    // In a real application, you would have a dedicated update endpoint\n    this.loading = false;\n    Swal.fire({\n      title: 'Feature Not Available',\n      text: 'User update functionality needs a dedicated API endpoint. Please contact the administrator.',\n      icon: 'info',\n      confirmButtonColor: '#29578c'\n    }).then(() => {\n      this.router.navigate(['/dashboard/admin/users']);\n    });\n  }\n  togglePasswordVisibility() {\n    this.showPassword = !this.showPassword;\n  }\n  cancel() {\n    this.router.navigate(['/dashboard/admin/users']);\n  }\n  markFormGroupTouched() {\n    Object.keys(this.userForm.controls).forEach(key => {\n      this.userForm.get(key)?.markAsTouched();\n    });\n  }\n  showError(message) {\n    Swal.fire({\n      title: 'Error',\n      text: message,\n      icon: 'error',\n      confirmButtonColor: '#29578c'\n    });\n  }\n  // Getter methods for template\n  get isStudent() {\n    return this.userForm.get('role')?.value === 'Student';\n  }\n  get isTeacher() {\n    return this.userForm.get('role')?.value === 'Teacher';\n  }\n  get isPrincipal() {\n    return this.userForm.get('role')?.value === 'Principal';\n  }\n  static #_ = this.ɵfac = function UserFormComponent_Factory(t) {\n    return new (t || UserFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.UserService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: UserFormComponent,\n    selectors: [[\"app-user-form\"]],\n    decls: 11,\n    vars: 3,\n    consts: [[1, \"maindiv\"], [1, \"secondarydiv\"], [1, \"header-section\"], [1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [\"class\", \"text-center py-5\", 4, \"ngIf\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"row\"], [1, \"col-md-6\"], [1, \"form-group\"], [\"for\", \"name\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"name\", \"formControlName\", \"name\", 1, \"form-control\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"for\", \"email\", 1, \"form-label\"], [\"type\", \"email\", \"id\", \"email\", \"formControlName\", \"email\", 1, \"form-control\"], [\"class\", \"col-md-6\", 4, \"ngIf\"], [\"for\", \"role\", 1, \"form-label\"], [\"id\", \"role\", \"formControlName\", \"role\", 1, \"form-control\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"contact\", 1, \"form-label\"], [\"type\", \"tel\", \"id\", \"contact\", \"formControlName\", \"contact\", \"placeholder\", \"e.g., 03001234567\", 1, \"form-control\"], [\"for\", \"address\", 1, \"form-label\"], [\"id\", \"address\", \"formControlName\", \"address\", \"rows\", \"2\", \"placeholder\", \"Enter full address\", 1, \"form-control\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"id\", \"isActive\", \"formControlName\", \"isActive\", 1, \"form-check-input\"], [\"for\", \"isActive\", 1, \"form-check-label\"], [1, \"form-actions\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", \"role\", \"status\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", \"ms-2\", 3, \"click\"], [1, \"invalid-feedback\"], [4, \"ngIf\"], [\"for\", \"password\", 1, \"form-label\"], [1, \"input-group\"], [\"id\", \"password\", \"formControlName\", \"password\", 1, \"form-control\", 3, \"type\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [3, \"value\"], [\"for\", \"regNo\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"regNo\", \"formControlName\", \"regNo\", 1, \"form-control\"], [\"for\", \"rollNo\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"rollNo\", \"formControlName\", \"rollNo\", 1, \"form-control\"], [\"for\", \"father_name\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"father_name\", \"formControlName\", \"father_name\", 1, \"form-control\"], [\"for\", \"designation\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"designation\", \"formControlName\", \"designation\", \"placeholder\", \"e.g., Assistant Professor, Lecturer\", 1, \"form-control\"], [\"for\", \"position\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"position\", \"formControlName\", \"position\", \"placeholder\", \"e.g., Head of Department\", 1, \"form-control\"], [\"for\", \"department\", 1, \"form-label\"], [\"id\", \"department\", \"formControlName\", \"department\", 1, \"form-control\"], [\"for\", \"classId\", 1, \"form-label\"], [\"id\", \"classId\", \"formControlName\", \"classId\", 1, \"form-control\"], [\"type\", \"checkbox\", \"id\", \"isVisiting\", \"formControlName\", \"isVisiting\", 1, \"form-check-input\"], [\"for\", \"isVisiting\", 1, \"form-check-label\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"], [1, \"text-center\", \"py-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-2\"]],\n    template: function UserFormComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\");\n        i0.ɵɵtext(4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"button\", 3);\n        i0.ɵɵlistener(\"click\", function UserFormComponent_Template_button_click_5_listener() {\n          return ctx.cancel();\n        });\n        i0.ɵɵelementStart(6, \"mat-icon\");\n        i0.ɵɵtext(7, \"arrow_back\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(8, \" Back to Users \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(9, UserFormComponent_form_9_Template, 55, 26, \"form\", 4);\n        i0.ɵɵtemplate(10, UserFormComponent_div_10_Template, 6, 1, \"div\", 5);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(ctx.isEditMode ? \"Edit User\" : \"Add New User\");\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n      }\n    },\n    dependencies: [i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.MatIcon],\n    styles: [\".maindiv[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background-color: #f8f9fa;\\n  min-height: 100vh;\\n}\\n\\n.secondarydiv[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 8px;\\n  padding: 30px;\\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.header-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 30px;\\n  padding-bottom: 20px;\\n  border-bottom: 1px solid #dee2e6;\\n}\\n\\n.header-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #29578c;\\n  font-weight: 600;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.form-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #495057;\\n  margin-bottom: 8px;\\n  display: block;\\n}\\n\\n.form-control[_ngcontent-%COMP%] {\\n  padding: 10px 12px;\\n  border: 1px solid #ced4da;\\n  border-radius: 4px;\\n  font-size: 14px;\\n  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\\n}\\n\\n.form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #29578c;\\n  box-shadow: 0 0 0 0.2rem rgba(41, 87, 140, 0.25);\\n  outline: 0;\\n}\\n\\n.form-control.is-invalid[_ngcontent-%COMP%] {\\n  border-color: #dc3545;\\n}\\n\\n.form-control.is-invalid[_ngcontent-%COMP%]:focus {\\n  border-color: #dc3545;\\n  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);\\n}\\n\\n.invalid-feedback[_ngcontent-%COMP%] {\\n  display: block;\\n  width: 100%;\\n  margin-top: 5px;\\n  font-size: 12px;\\n  color: #dc3545;\\n}\\n\\n.input-group[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n\\n.input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border-top-right-radius: 0;\\n  border-bottom-right-radius: 0;\\n}\\n\\n.input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-top-left-radius: 0;\\n  border-bottom-left-radius: 0;\\n  border-left: 0;\\n}\\n\\n.form-check[_ngcontent-%COMP%] {\\n  padding-left: 1.5rem;\\n}\\n\\n.form-check-input[_ngcontent-%COMP%] {\\n  margin-left: -1.5rem;\\n  margin-top: 0.25rem;\\n}\\n\\n.form-check-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #495057;\\n}\\n\\n.form-actions[_ngcontent-%COMP%] {\\n  margin-top: 40px;\\n  padding-top: 20px;\\n  border-top: 1px solid #dee2e6;\\n  display: flex;\\n  gap: 10px;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 10px 20px;\\n  border-radius: 4px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  border: 1px solid transparent;\\n  cursor: pointer;\\n  transition: all 0.15s ease-in-out;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background-color: #29578c;\\n  border-color: #29578c;\\n  color: white;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #1e3f63;\\n  border-color: #1e3f63;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n\\n.btn-outline-secondary[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  border-color: #6c757d;\\n  background-color: transparent;\\n}\\n\\n.btn-outline-secondary[_ngcontent-%COMP%]:hover {\\n  color: white;\\n  background-color: #6c757d;\\n  border-color: #6c757d;\\n}\\n\\n.spinner-border-sm[_ngcontent-%COMP%] {\\n  width: 1rem;\\n  height: 1rem;\\n}\\n\\n.spinner-border[_ngcontent-%COMP%] {\\n  width: 3rem;\\n  height: 3rem;\\n}\\n\\n.row[_ngcontent-%COMP%] {\\n  margin-left: -15px;\\n  margin-right: -15px;\\n}\\n\\n.col-md-6[_ngcontent-%COMP%] {\\n  padding-left: 15px;\\n  padding-right: 15px;\\n}\\n\\ntextarea.form-control[_ngcontent-%COMP%] {\\n  resize: vertical;\\n  min-height: 60px;\\n}\\n\\nselect.form-control[_ngcontent-%COMP%] {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e\\\");\\n  background-position: right 8px center;\\n  background-repeat: no-repeat;\\n  background-size: 16px 12px;\\n  padding-right: 40px;\\n  appearance: none;\\n}\\n\\n@media (max-width: 768px) {\\n  .header-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 15px;\\n    text-align: center;\\n  }\\n  \\n  .form-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  \\n  .form-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  \\n  .col-md-6[_ngcontent-%COMP%] {\\n    padding-left: 0;\\n    padding-right: 0;\\n  }\\n  \\n  .row[_ngcontent-%COMP%] {\\n    margin-left: 0;\\n    margin-right: 0;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "UserFormComponent_form_9_div_7_small_1_Template", "UserFormComponent_form_9_div_7_small_2_Template", "ɵɵadvance", "ɵɵproperty", "tmp_0_0", "ctx_r2", "userForm", "get", "errors", "tmp_1_0", "UserFormComponent_form_9_div_13_small_1_Template", "UserFormComponent_form_9_div_13_small_2_Template", "ctx_r3", "UserFormComponent_form_9_div_14_div_9_small_1_Template", "UserFormComponent_form_9_div_14_div_9_small_2_Template", "ctx_r21", "ɵɵelement", "ɵɵlistener", "UserFormComponent_form_9_div_14_Template_button_click_6_listener", "ɵɵrestoreView", "_r25", "ctx_r24", "ɵɵnextContext", "ɵɵresetView", "togglePasswordVisibility", "UserFormComponent_form_9_div_14_div_9_Template", "ɵɵclassProp", "ctx_r4", "invalid", "touched", "showPassword", "ɵɵtextInterpolate", "tmp_3_0", "role_r26", "UserFormComponent_form_9_div_35_div_5_Template", "ctx_r8", "UserFormComponent_form_9_div_36_div_5_Template", "ctx_r9", "UserFormComponent_form_9_div_37_div_5_Template", "ctx_r10", "UserFormComponent_form_9_div_38_div_5_Template", "ctx_r11", "dept_r33", "UserFormComponent_form_9_div_40_option_7_Template", "UserFormComponent_form_9_div_40_div_8_Template", "ctx_r13", "departments", "tmp_2_0", "cls_r35", "UserFormComponent_form_9_div_41_option_7_Template", "ctx_r14", "classes", "UserFormComponent_form_9_Template_form_ngSubmit_0_listener", "_r37", "ctx_r36", "onSubmit", "UserFormComponent_form_9_div_7_Template", "UserFormComponent_form_9_div_13_Template", "UserFormComponent_form_9_div_14_Template", "UserFormComponent_form_9_option_22_Template", "UserFormComponent_form_9_div_23_Template", "UserFormComponent_form_9_div_29_Template", "UserFormComponent_form_9_div_35_Template", "UserFormComponent_form_9_div_36_Template", "UserFormComponent_form_9_div_37_Template", "UserFormComponent_form_9_div_38_Template", "UserFormComponent_form_9_div_39_Template", "UserFormComponent_form_9_div_40_Template", "UserFormComponent_form_9_div_41_Template", "UserFormComponent_form_9_div_48_Template", "UserFormComponent_form_9_span_51_Template", "UserFormComponent_form_9_Template_button_click_53_listener", "ctx_r38", "cancel", "ctx_r0", "tmp_4_0", "isEditMode", "tmp_6_0", "roles", "tmp_8_0", "tmp_9_0", "tmp_10_0", "isStudent", "<PERSON><PERSON><PERSON>er", "is<PERSON><PERSON><PERSON>pal", "loading", "ɵɵtextInterpolate1", "ctx_r1", "UserFormComponent", "constructor", "fb", "route", "router", "authService", "userService", "userId", "sections", "ngOnInit", "snapshot", "paramMap", "initializeForm", "loadUser", "group", "name", "required", "<PERSON><PERSON><PERSON><PERSON>", "email", "password", "role", "address", "regNo", "rollNo", "contact", "pattern", "father_name", "department", "designation", "position", "isVisiting", "classId", "isActive", "valueChanges", "subscribe", "updateValidatorsBasedOnRole", "regNoControl", "rollNoControl", "departmentControl", "fatherNameControl", "designationControl", "clearValidators", "setValidators", "updateValueAndValidity", "getAllUsers", "next", "response", "success", "user", "users", "find", "u", "_id", "populateForm", "showError", "navigate", "error", "console", "patchValue", "valid", "formData", "value", "updateUser", "createUser", "markFormGroupTouched", "userData", "signup", "fire", "title", "text", "icon", "confirmButtonColor", "then", "errorMessage", "message", "Object", "keys", "controls", "for<PERSON>ach", "key", "<PERSON><PERSON><PERSON><PERSON>ched", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivatedRoute", "Router", "i3", "AuthService", "i4", "UserService", "_2", "selectors", "decls", "vars", "consts", "template", "UserFormComponent_Template", "rf", "ctx", "UserFormComponent_Template_button_click_5_listener", "UserFormComponent_form_9_Template", "UserFormComponent_div_10_Template"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\users\\user-form\\user-form.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\users\\user-form\\user-form.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { AuthService } from 'src/app/services/auth.service';\nimport { UserService } from 'src/app/services/user.service';\nimport { User } from 'src/app/models/user';\nimport Swal from 'sweetalert2';\n\n@Component({\n  selector: 'app-user-form',\n  templateUrl: './user-form.component.html',\n  styleUrls: ['./user-form.component.css']\n})\nexport class UserFormComponent implements OnInit {\n  userForm!: FormGroup;\n  isEditMode = false;\n  userId: string | null = null;\n  loading = false;\n  showPassword = false;\n\n  roles = ['Student', 'Teacher', 'Principal', 'Admin'];\n  departments = ['Pre-Medical', 'Pre-Engineering', 'Computer Science', 'Arts', 'Commerce'];\n  classes = ['1st Year', '2nd Year'];\n  sections = ['A', 'B', 'C'];\n\n  constructor(\n    private fb: FormBuilder,\n    private route: ActivatedRoute,\n    private router: Router,\n    private authService: AuthService,\n    private userService: UserService\n  ) {}\n\n  ngOnInit(): void {\n    this.userId = this.route.snapshot.paramMap.get('id');\n    this.isEditMode = !!this.userId;\n\n    this.initializeForm();\n\n    if (this.isEditMode) {\n      this.loadUser();\n    }\n  }\n\n  initializeForm(): void {\n    this.userForm = this.fb.group({\n      name: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      password: [this.isEditMode ? '' : '', this.isEditMode ? [] : [Validators.required, Validators.minLength(6)]],\n      role: ['', Validators.required],\n      address: [''],\n      regNo: [''],\n      rollNo: [''],\n      contact: ['', [Validators.pattern(/^\\d{10,15}$/)]],\n      father_name: [''],\n      department: [''],\n      designation: [''],\n      position: [''],\n      isVisiting: [false],\n      classId: [''],\n      isActive: [true]\n    });\n\n    // Add conditional validators based on role\n    this.userForm.get('role')?.valueChanges.subscribe(role => {\n      this.updateValidatorsBasedOnRole(role);\n    });\n  }\n\n  updateValidatorsBasedOnRole(role: string): void {\n    const regNoControl = this.userForm.get('regNo');\n    const rollNoControl = this.userForm.get('rollNo');\n    const departmentControl = this.userForm.get('department');\n    const fatherNameControl = this.userForm.get('father_name');\n    const designationControl = this.userForm.get('designation');\n\n    // Clear existing validators\n    regNoControl?.clearValidators();\n    rollNoControl?.clearValidators();\n    departmentControl?.clearValidators();\n    fatherNameControl?.clearValidators();\n    designationControl?.clearValidators();\n\n    if (role === 'Student') {\n      regNoControl?.setValidators([Validators.required]);\n      rollNoControl?.setValidators([Validators.required]);\n      departmentControl?.setValidators([Validators.required]);\n      fatherNameControl?.setValidators([Validators.required]);\n    } else if (role === 'Teacher' || role === 'Principal') {\n      departmentControl?.setValidators([Validators.required]);\n      designationControl?.setValidators([Validators.required]);\n    }\n\n    // Update validity\n    regNoControl?.updateValueAndValidity();\n    rollNoControl?.updateValueAndValidity();\n    departmentControl?.updateValueAndValidity();\n    fatherNameControl?.updateValueAndValidity();\n    designationControl?.updateValueAndValidity();\n  }\n\n  loadUser(): void {\n    if (!this.userId) return;\n\n    this.loading = true;\n    // Since we don't have a get single user API, we'll get all users and find the one we need\n    this.authService.getAllUsers().subscribe({\n      next: (response) => {\n        this.loading = false;\n        if (response.success) {\n          const user = response.users.find((u: User) => u._id === this.userId);\n          if (user) {\n            this.populateForm(user);\n          } else {\n            this.showError('User not found');\n            this.router.navigate(['/dashboard/admin/users']);\n          }\n        }\n      },\n      error: (error) => {\n        this.loading = false;\n        console.error('Error loading user:', error);\n        this.showError('Failed to load user data');\n      }\n    });\n  }\n\n  populateForm(user: User): void {\n    this.userForm.patchValue({\n      name: user.name,\n      email: user.email,\n      role: user.role,\n      address: user.address || '',\n      regNo: user.regNo || '',\n      rollNo: user.rollNo || '',\n      contact: user.contact || '',\n      father_name: user.father_name || '',\n      department: user.department || '',\n      designation: user.designation || '',\n      position: user.position || '',\n      isVisiting: user.isVisiting || false,\n      classId: user.classId || '',\n      isActive: user.isActive !== false\n    });\n  }\n\n  onSubmit(): void {\n    if (this.userForm.valid) {\n      this.loading = true;\n      const formData = this.userForm.value;\n\n      // Remove password if it's empty in edit mode\n      if (this.isEditMode && !formData.password) {\n        delete formData.password;\n      }\n\n      if (this.isEditMode) {\n        this.updateUser(formData);\n      } else {\n        this.createUser(formData);\n      }\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n\n  createUser(userData: any): void {\n    this.authService.signup(userData).subscribe({\n      next: (response) => {\n        this.loading = false;\n        if (response.success) {\n          Swal.fire({\n            title: 'Success!',\n            text: 'User created successfully.',\n            icon: 'success',\n            confirmButtonColor: '#29578c'\n          }).then(() => {\n            this.router.navigate(['/dashboard/admin/users']);\n          });\n        }\n      },\n      error: (error) => {\n        this.loading = false;\n        console.error('Error creating user:', error);\n        const errorMessage = error.error?.message || 'Failed to create user';\n        this.showError(errorMessage);\n      }\n    });\n  }\n\n  updateUser(userData: any): void {\n    // For now, we'll use the register endpoint to update user data\n    // In a real application, you would have a dedicated update endpoint\n    this.loading = false;\n    Swal.fire({\n      title: 'Feature Not Available',\n      text: 'User update functionality needs a dedicated API endpoint. Please contact the administrator.',\n      icon: 'info',\n      confirmButtonColor: '#29578c'\n    }).then(() => {\n      this.router.navigate(['/dashboard/admin/users']);\n    });\n  }\n\n  togglePasswordVisibility(): void {\n    this.showPassword = !this.showPassword;\n  }\n\n  cancel(): void {\n    this.router.navigate(['/dashboard/admin/users']);\n  }\n\n  private markFormGroupTouched(): void {\n    Object.keys(this.userForm.controls).forEach(key => {\n      this.userForm.get(key)?.markAsTouched();\n    });\n  }\n\n  private showError(message: string): void {\n    Swal.fire({\n      title: 'Error',\n      text: message,\n      icon: 'error',\n      confirmButtonColor: '#29578c'\n    });\n  }\n\n  // Getter methods for template\n  get isStudent(): boolean {\n    return this.userForm.get('role')?.value === 'Student';\n  }\n\n  get isTeacher(): boolean {\n    return this.userForm.get('role')?.value === 'Teacher';\n  }\n\n  get isPrincipal(): boolean {\n    return this.userForm.get('role')?.value === 'Principal';\n  }\n}\n", "<div class=\"maindiv\">\n  <div class=\"secondarydiv\">\n    <div class=\"header-section\">\n      <h2>{{ isEditMode ? 'Edit User' : 'Add New User' }}</h2>\n      <button class=\"btn btn-outline-secondary\" (click)=\"cancel()\">\n        <mat-icon>arrow_back</mat-icon> Back to Users\n      </button>\n    </div>\n\n    <form [formGroup]=\"userForm\" (ngSubmit)=\"onSubmit()\" *ngIf=\"!loading\">\n      <div class=\"row\">\n        <!-- Basic Information -->\n        <div class=\"col-md-6\">\n          <div class=\"form-group\">\n            <label for=\"name\" class=\"form-label\">Full Name *</label>\n            <input \n              type=\"text\" \n              id=\"name\" \n              class=\"form-control\" \n              formControlName=\"name\"\n              [class.is-invalid]=\"userForm.get('name')?.invalid && userForm.get('name')?.touched\">\n            <div class=\"invalid-feedback\" *ngIf=\"userForm.get('name')?.invalid && userForm.get('name')?.touched\">\n              <small *ngIf=\"userForm.get('name')?.errors?.['required']\">Name is required</small>\n              <small *ngIf=\"userForm.get('name')?.errors?.['minlength']\">Name must be at least 2 characters</small>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"col-md-6\">\n          <div class=\"form-group\">\n            <label for=\"email\" class=\"form-label\">Email *</label>\n            <input \n              type=\"email\" \n              id=\"email\" \n              class=\"form-control\" \n              formControlName=\"email\"\n              [class.is-invalid]=\"userForm.get('email')?.invalid && userForm.get('email')?.touched\">\n            <div class=\"invalid-feedback\" *ngIf=\"userForm.get('email')?.invalid && userForm.get('email')?.touched\">\n              <small *ngIf=\"userForm.get('email')?.errors?.['required']\">Email is required</small>\n              <small *ngIf=\"userForm.get('email')?.errors?.['email']\">Please enter a valid email</small>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"col-md-6\" *ngIf=\"!isEditMode\">\n          <div class=\"form-group\">\n            <label for=\"password\" class=\"form-label\">Password *</label>\n            <div class=\"input-group\">\n              <input \n                [type]=\"showPassword ? 'text' : 'password'\" \n                id=\"password\" \n                class=\"form-control\" \n                formControlName=\"password\"\n                [class.is-invalid]=\"userForm.get('password')?.invalid && userForm.get('password')?.touched\">\n              <button \n                class=\"btn btn-outline-secondary\" \n                type=\"button\" \n                (click)=\"togglePasswordVisibility()\">\n                <mat-icon>{{ showPassword ? 'visibility_off' : 'visibility' }}</mat-icon>\n              </button>\n            </div>\n            <div class=\"invalid-feedback\" *ngIf=\"userForm.get('password')?.invalid && userForm.get('password')?.touched\">\n              <small *ngIf=\"userForm.get('password')?.errors?.['required']\">Password is required</small>\n              <small *ngIf=\"userForm.get('password')?.errors?.['minlength']\">Password must be at least 6 characters</small>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"col-md-6\">\n          <div class=\"form-group\">\n            <label for=\"role\" class=\"form-label\">Role *</label>\n            <select \n              id=\"role\" \n              class=\"form-control\" \n              formControlName=\"role\"\n              [class.is-invalid]=\"userForm.get('role')?.invalid && userForm.get('role')?.touched\">\n              <option value=\"\">Select Role</option>\n              <option *ngFor=\"let role of roles\" [value]=\"role\">{{ role }}</option>\n            </select>\n            <div class=\"invalid-feedback\" *ngIf=\"userForm.get('role')?.invalid && userForm.get('role')?.touched\">\n              <small>Role is required</small>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"col-md-6\">\n          <div class=\"form-group\">\n            <label for=\"contact\" class=\"form-label\">Contact Number</label>\n            <input \n              type=\"tel\" \n              id=\"contact\" \n              class=\"form-control\" \n              formControlName=\"contact\"\n              placeholder=\"e.g., 03001234567\"\n              [class.is-invalid]=\"userForm.get('contact')?.invalid && userForm.get('contact')?.touched\">\n            <div class=\"invalid-feedback\" *ngIf=\"userForm.get('contact')?.invalid && userForm.get('contact')?.touched\">\n              <small>Please enter a valid contact number (10-15 digits)</small>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"col-md-6\">\n          <div class=\"form-group\">\n            <label for=\"address\" class=\"form-label\">Address</label>\n            <textarea \n              id=\"address\" \n              class=\"form-control\" \n              formControlName=\"address\" \n              rows=\"2\"\n              placeholder=\"Enter full address\"></textarea>\n          </div>\n        </div>\n\n        <!-- Student-specific fields -->\n        <div class=\"col-md-6\" *ngIf=\"isStudent\">\n          <div class=\"form-group\">\n            <label for=\"regNo\" class=\"form-label\">Registration Number *</label>\n            <input \n              type=\"text\" \n              id=\"regNo\" \n              class=\"form-control\" \n              formControlName=\"regNo\"\n              [class.is-invalid]=\"userForm.get('regNo')?.invalid && userForm.get('regNo')?.touched\">\n            <div class=\"invalid-feedback\" *ngIf=\"userForm.get('regNo')?.invalid && userForm.get('regNo')?.touched\">\n              <small>Registration number is required for students</small>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"col-md-6\" *ngIf=\"isStudent\">\n          <div class=\"form-group\">\n            <label for=\"rollNo\" class=\"form-label\">Roll Number *</label>\n            <input \n              type=\"text\" \n              id=\"rollNo\" \n              class=\"form-control\" \n              formControlName=\"rollNo\"\n              [class.is-invalid]=\"userForm.get('rollNo')?.invalid && userForm.get('rollNo')?.touched\">\n            <div class=\"invalid-feedback\" *ngIf=\"userForm.get('rollNo')?.invalid && userForm.get('rollNo')?.touched\">\n              <small>Roll number is required for students</small>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"col-md-6\" *ngIf=\"isStudent\">\n          <div class=\"form-group\">\n            <label for=\"father_name\" class=\"form-label\">Father's Name *</label>\n            <input \n              type=\"text\" \n              id=\"father_name\" \n              class=\"form-control\" \n              formControlName=\"father_name\"\n              [class.is-invalid]=\"userForm.get('father_name')?.invalid && userForm.get('father_name')?.touched\">\n            <div class=\"invalid-feedback\" *ngIf=\"userForm.get('father_name')?.invalid && userForm.get('father_name')?.touched\">\n              <small>Father's name is required for students</small>\n            </div>\n          </div>\n        </div>\n\n        <!-- Teacher/Principal-specific fields -->\n        <div class=\"col-md-6\" *ngIf=\"isTeacher || isPrincipal\">\n          <div class=\"form-group\">\n            <label for=\"designation\" class=\"form-label\">Designation *</label>\n            <input \n              type=\"text\" \n              id=\"designation\" \n              class=\"form-control\" \n              formControlName=\"designation\"\n              placeholder=\"e.g., Assistant Professor, Lecturer\"\n              [class.is-invalid]=\"userForm.get('designation')?.invalid && userForm.get('designation')?.touched\">\n            <div class=\"invalid-feedback\" *ngIf=\"userForm.get('designation')?.invalid && userForm.get('designation')?.touched\">\n              <small>Designation is required</small>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"col-md-6\" *ngIf=\"isTeacher || isPrincipal\">\n          <div class=\"form-group\">\n            <label for=\"position\" class=\"form-label\">Position</label>\n            <input \n              type=\"text\" \n              id=\"position\" \n              class=\"form-control\" \n              formControlName=\"position\"\n              placeholder=\"e.g., Head of Department\">\n          </div>\n        </div>\n\n        <!-- Common fields for academic roles -->\n        <div class=\"col-md-6\" *ngIf=\"isStudent || isTeacher || isPrincipal\">\n          <div class=\"form-group\">\n            <label for=\"department\" class=\"form-label\">Department *</label>\n            <select \n              id=\"department\" \n              class=\"form-control\" \n              formControlName=\"department\"\n              [class.is-invalid]=\"userForm.get('department')?.invalid && userForm.get('department')?.touched\">\n              <option value=\"\">Select Department</option>\n              <option *ngFor=\"let dept of departments\" [value]=\"dept\">{{ dept }}</option>\n            </select>\n            <div class=\"invalid-feedback\" *ngIf=\"userForm.get('department')?.invalid && userForm.get('department')?.touched\">\n              <small>Department is required</small>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"col-md-6\" *ngIf=\"isStudent\">\n          <div class=\"form-group\">\n            <label for=\"classId\" class=\"form-label\">Class</label>\n            <select id=\"classId\" class=\"form-control\" formControlName=\"classId\">\n              <option value=\"\">Select Class</option>\n              <option *ngFor=\"let cls of classes\" [value]=\"cls\">{{ cls }}</option>\n            </select>\n          </div>\n        </div>\n\n        <!-- Status -->\n        <div class=\"col-md-6\">\n          <div class=\"form-group\">\n            <div class=\"form-check\">\n              <input \n                class=\"form-check-input\" \n                type=\"checkbox\" \n                id=\"isActive\" \n                formControlName=\"isActive\">\n              <label class=\"form-check-label\" for=\"isActive\">\n                Active User\n              </label>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"col-md-6\" *ngIf=\"isTeacher\">\n          <div class=\"form-group\">\n            <div class=\"form-check\">\n              <input \n                class=\"form-check-input\" \n                type=\"checkbox\" \n                id=\"isVisiting\" \n                formControlName=\"isVisiting\">\n              <label class=\"form-check-label\" for=\"isVisiting\">\n                Visiting Faculty\n              </label>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Form Actions -->\n      <div class=\"form-actions\">\n        <button \n          type=\"submit\" \n          class=\"btn btn-primary\" \n          [disabled]=\"loading || userForm.invalid\">\n          <span *ngIf=\"loading\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\n          {{ loading ? 'Saving...' : (isEditMode ? 'Update User' : 'Create User') }}\n        </button>\n        <button type=\"button\" class=\"btn btn-outline-secondary ms-2\" (click)=\"cancel()\">\n          Cancel\n        </button>\n      </div>\n    </form>\n\n    <!-- Loading State -->\n    <div *ngIf=\"loading\" class=\"text-center py-5\">\n      <div class=\"spinner-border text-primary\" role=\"status\">\n        <span class=\"visually-hidden\">Loading...</span>\n      </div>\n      <p class=\"mt-2\">{{ isEditMode ? 'Loading user data...' : 'Creating user...' }}</p>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAKnE,OAAOC,IAAI,MAAM,aAAa;;;;;;;;;;ICgBhBC,EAAA,CAAAC,cAAA,YAA0D;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAClFH,EAAA,CAAAC,cAAA,YAA2D;IAAAD,EAAA,CAAAE,MAAA,yCAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAFvGH,EAAA,CAAAC,cAAA,cAAqG;IACnGD,EAAA,CAAAI,UAAA,IAAAC,+CAAA,oBAAkF;IAClFL,EAAA,CAAAI,UAAA,IAAAE,+CAAA,oBAAqG;IACvGN,EAAA,CAAAG,YAAA,EAAM;;;;;;IAFIH,EAAA,CAAAO,SAAA,GAAgD;IAAhDP,EAAA,CAAAQ,UAAA,UAAAC,OAAA,GAAAC,MAAA,CAAAC,QAAA,CAAAC,GAAA,2BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAgD;IAChDb,EAAA,CAAAO,SAAA,GAAiD;IAAjDP,EAAA,CAAAQ,UAAA,UAAAM,OAAA,GAAAJ,MAAA,CAAAC,QAAA,CAAAC,GAAA,2BAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,cAAiD;;;;;IAezDb,EAAA,CAAAC,cAAA,YAA2D;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IACpFH,EAAA,CAAAC,cAAA,YAAwD;IAAAD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAF5FH,EAAA,CAAAC,cAAA,cAAuG;IACrGD,EAAA,CAAAI,UAAA,IAAAW,gDAAA,oBAAoF;IACpFf,EAAA,CAAAI,UAAA,IAAAY,gDAAA,oBAA0F;IAC5FhB,EAAA,CAAAG,YAAA,EAAM;;;;;;IAFIH,EAAA,CAAAO,SAAA,GAAiD;IAAjDP,EAAA,CAAAQ,UAAA,UAAAC,OAAA,GAAAQ,MAAA,CAAAN,QAAA,CAAAC,GAAA,4BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAiD;IACjDb,EAAA,CAAAO,SAAA,GAA8C;IAA9CP,EAAA,CAAAQ,UAAA,UAAAM,OAAA,GAAAG,MAAA,CAAAN,QAAA,CAAAC,GAAA,4BAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,UAA8C;;;;;IAuBtDb,EAAA,CAAAC,cAAA,YAA8D;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAC1FH,EAAA,CAAAC,cAAA,YAA+D;IAAAD,EAAA,CAAAE,MAAA,6CAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAF/GH,EAAA,CAAAC,cAAA,cAA6G;IAC3GD,EAAA,CAAAI,UAAA,IAAAc,sDAAA,oBAA0F;IAC1FlB,EAAA,CAAAI,UAAA,IAAAe,sDAAA,oBAA6G;IAC/GnB,EAAA,CAAAG,YAAA,EAAM;;;;;;IAFIH,EAAA,CAAAO,SAAA,GAAoD;IAApDP,EAAA,CAAAQ,UAAA,UAAAC,OAAA,GAAAW,OAAA,CAAAT,QAAA,CAAAC,GAAA,+BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAoD;IACpDb,EAAA,CAAAO,SAAA,GAAqD;IAArDP,EAAA,CAAAQ,UAAA,UAAAM,OAAA,GAAAM,OAAA,CAAAT,QAAA,CAAAC,GAAA,+BAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,cAAqD;;;;;;IAnBnEb,EAAA,CAAAC,cAAA,aAA0C;IAEGD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC3DH,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAqB,SAAA,gBAK8F;IAC9FrB,EAAA,CAAAC,cAAA,iBAGuC;IAArCD,EAAA,CAAAsB,UAAA,mBAAAC,iEAAA;MAAAvB,EAAA,CAAAwB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAA1B,EAAA,CAAA2B,aAAA;MAAA,OAAS3B,EAAA,CAAA4B,WAAA,CAAAF,OAAA,CAAAG,wBAAA,EAA0B;IAAA,EAAC;IACpC7B,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAG7EH,EAAA,CAAAI,UAAA,IAAA0B,8CAAA,kBAGM;IACR9B,EAAA,CAAAG,YAAA,EAAM;;;;;;IAZAH,EAAA,CAAAO,SAAA,GAA2F;IAA3FP,EAAA,CAAA+B,WAAA,iBAAAtB,OAAA,GAAAuB,MAAA,CAAArB,QAAA,CAAAC,GAAA,+BAAAH,OAAA,CAAAwB,OAAA,OAAAxB,OAAA,GAAAuB,MAAA,CAAArB,QAAA,CAAAC,GAAA,+BAAAH,OAAA,CAAAyB,OAAA,EAA2F;IAJ3FlC,EAAA,CAAAQ,UAAA,SAAAwB,MAAA,CAAAG,YAAA,uBAA2C;IASjCnC,EAAA,CAAAO,SAAA,GAAoD;IAApDP,EAAA,CAAAoC,iBAAA,CAAAJ,MAAA,CAAAG,YAAA,mCAAoD;IAGnCnC,EAAA,CAAAO,SAAA,GAA4E;IAA5EP,EAAA,CAAAQ,UAAA,WAAA6B,OAAA,GAAAL,MAAA,CAAArB,QAAA,CAAAC,GAAA,+BAAAyB,OAAA,CAAAJ,OAAA,OAAAI,OAAA,GAAAL,MAAA,CAAArB,QAAA,CAAAC,GAAA,+BAAAyB,OAAA,CAAAH,OAAA,EAA4E;;;;;IAgBzGlC,EAAA,CAAAC,cAAA,iBAAkD;IAAAD,EAAA,CAAAE,MAAA,GAAU;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAlCH,EAAA,CAAAQ,UAAA,UAAA8B,QAAA,CAAc;IAACtC,EAAA,CAAAO,SAAA,GAAU;IAAVP,EAAA,CAAAoC,iBAAA,CAAAE,QAAA,CAAU;;;;;IAE9DtC,EAAA,CAAAC,cAAA,cAAqG;IAC5FD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAejCH,EAAA,CAAAC,cAAA,cAA2G;IAClGD,EAAA,CAAAE,MAAA,yDAAkD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IA2BnEH,EAAA,CAAAC,cAAA,cAAuG;IAC9FD,EAAA,CAAAE,MAAA,mDAA4C;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAVjEH,EAAA,CAAAC,cAAA,aAAwC;IAEED,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnEH,EAAA,CAAAqB,SAAA,gBAKwF;IACxFrB,EAAA,CAAAI,UAAA,IAAAmC,8CAAA,kBAEM;IACRvC,EAAA,CAAAG,YAAA,EAAM;;;;;;IAJFH,EAAA,CAAAO,SAAA,GAAqF;IAArFP,EAAA,CAAA+B,WAAA,iBAAAtB,OAAA,GAAA+B,MAAA,CAAA7B,QAAA,CAAAC,GAAA,4BAAAH,OAAA,CAAAwB,OAAA,OAAAxB,OAAA,GAAA+B,MAAA,CAAA7B,QAAA,CAAAC,GAAA,4BAAAH,OAAA,CAAAyB,OAAA,EAAqF;IACxDlC,EAAA,CAAAO,SAAA,GAAsE;IAAtEP,EAAA,CAAAQ,UAAA,WAAAM,OAAA,GAAA0B,MAAA,CAAA7B,QAAA,CAAAC,GAAA,4BAAAE,OAAA,CAAAmB,OAAA,OAAAnB,OAAA,GAAA0B,MAAA,CAAA7B,QAAA,CAAAC,GAAA,4BAAAE,OAAA,CAAAoB,OAAA,EAAsE;;;;;IAerGlC,EAAA,CAAAC,cAAA,cAAyG;IAChGD,EAAA,CAAAE,MAAA,2CAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAVzDH,EAAA,CAAAC,cAAA,aAAwC;IAEGD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC5DH,EAAA,CAAAqB,SAAA,gBAK0F;IAC1FrB,EAAA,CAAAI,UAAA,IAAAqC,8CAAA,kBAEM;IACRzC,EAAA,CAAAG,YAAA,EAAM;;;;;;IAJFH,EAAA,CAAAO,SAAA,GAAuF;IAAvFP,EAAA,CAAA+B,WAAA,iBAAAtB,OAAA,GAAAiC,MAAA,CAAA/B,QAAA,CAAAC,GAAA,6BAAAH,OAAA,CAAAwB,OAAA,OAAAxB,OAAA,GAAAiC,MAAA,CAAA/B,QAAA,CAAAC,GAAA,6BAAAH,OAAA,CAAAyB,OAAA,EAAuF;IAC1DlC,EAAA,CAAAO,SAAA,GAAwE;IAAxEP,EAAA,CAAAQ,UAAA,WAAAM,OAAA,GAAA4B,MAAA,CAAA/B,QAAA,CAAAC,GAAA,6BAAAE,OAAA,CAAAmB,OAAA,OAAAnB,OAAA,GAAA4B,MAAA,CAAA/B,QAAA,CAAAC,GAAA,6BAAAE,OAAA,CAAAoB,OAAA,EAAwE;;;;;IAevGlC,EAAA,CAAAC,cAAA,cAAmH;IAC1GD,EAAA,CAAAE,MAAA,6CAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAV3DH,EAAA,CAAAC,cAAA,aAAwC;IAEQD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnEH,EAAA,CAAAqB,SAAA,gBAKoG;IACpGrB,EAAA,CAAAI,UAAA,IAAAuC,8CAAA,kBAEM;IACR3C,EAAA,CAAAG,YAAA,EAAM;;;;;;IAJFH,EAAA,CAAAO,SAAA,GAAiG;IAAjGP,EAAA,CAAA+B,WAAA,iBAAAtB,OAAA,GAAAmC,OAAA,CAAAjC,QAAA,CAAAC,GAAA,kCAAAH,OAAA,CAAAwB,OAAA,OAAAxB,OAAA,GAAAmC,OAAA,CAAAjC,QAAA,CAAAC,GAAA,kCAAAH,OAAA,CAAAyB,OAAA,EAAiG;IACpElC,EAAA,CAAAO,SAAA,GAAkF;IAAlFP,EAAA,CAAAQ,UAAA,WAAAM,OAAA,GAAA8B,OAAA,CAAAjC,QAAA,CAAAC,GAAA,kCAAAE,OAAA,CAAAmB,OAAA,OAAAnB,OAAA,GAAA8B,OAAA,CAAAjC,QAAA,CAAAC,GAAA,kCAAAE,OAAA,CAAAoB,OAAA,EAAkF;;;;;IAiBjHlC,EAAA,CAAAC,cAAA,cAAmH;IAC1GD,EAAA,CAAAE,MAAA,8BAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAX5CH,EAAA,CAAAC,cAAA,aAAuD;IAEPD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACjEH,EAAA,CAAAqB,SAAA,gBAMoG;IACpGrB,EAAA,CAAAI,UAAA,IAAAyC,8CAAA,kBAEM;IACR7C,EAAA,CAAAG,YAAA,EAAM;;;;;;IAJFH,EAAA,CAAAO,SAAA,GAAiG;IAAjGP,EAAA,CAAA+B,WAAA,iBAAAtB,OAAA,GAAAqC,OAAA,CAAAnC,QAAA,CAAAC,GAAA,kCAAAH,OAAA,CAAAwB,OAAA,OAAAxB,OAAA,GAAAqC,OAAA,CAAAnC,QAAA,CAAAC,GAAA,kCAAAH,OAAA,CAAAyB,OAAA,EAAiG;IACpElC,EAAA,CAAAO,SAAA,GAAkF;IAAlFP,EAAA,CAAAQ,UAAA,WAAAM,OAAA,GAAAgC,OAAA,CAAAnC,QAAA,CAAAC,GAAA,kCAAAE,OAAA,CAAAmB,OAAA,OAAAnB,OAAA,GAAAgC,OAAA,CAAAnC,QAAA,CAAAC,GAAA,kCAAAE,OAAA,CAAAoB,OAAA,EAAkF;;;;;IAMrHlC,EAAA,CAAAC,cAAA,aAAuD;IAEVD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzDH,EAAA,CAAAqB,SAAA,gBAKyC;IAC3CrB,EAAA,CAAAG,YAAA,EAAM;;;;;IAaFH,EAAA,CAAAC,cAAA,iBAAwD;IAAAD,EAAA,CAAAE,MAAA,GAAU;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAlCH,EAAA,CAAAQ,UAAA,UAAAuC,QAAA,CAAc;IAAC/C,EAAA,CAAAO,SAAA,GAAU;IAAVP,EAAA,CAAAoC,iBAAA,CAAAW,QAAA,CAAU;;;;;IAEpE/C,EAAA,CAAAC,cAAA,cAAiH;IACxGD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAZ3CH,EAAA,CAAAC,cAAA,aAAoE;IAErBD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC/DH,EAAA,CAAAC,cAAA,iBAIkG;IAC/ED,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC3CH,EAAA,CAAAI,UAAA,IAAA4C,iDAAA,qBAA2E;IAC7EhD,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAI,UAAA,IAAA6C,8CAAA,kBAEM;IACRjD,EAAA,CAAAG,YAAA,EAAM;;;;;;IAPFH,EAAA,CAAAO,SAAA,GAA+F;IAA/FP,EAAA,CAAA+B,WAAA,iBAAAtB,OAAA,GAAAyC,OAAA,CAAAvC,QAAA,CAAAC,GAAA,iCAAAH,OAAA,CAAAwB,OAAA,OAAAxB,OAAA,GAAAyC,OAAA,CAAAvC,QAAA,CAAAC,GAAA,iCAAAH,OAAA,CAAAyB,OAAA,EAA+F;IAEtElC,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAQ,UAAA,YAAA0C,OAAA,CAAAC,WAAA,CAAc;IAEVnD,EAAA,CAAAO,SAAA,GAAgF;IAAhFP,EAAA,CAAAQ,UAAA,WAAA4C,OAAA,GAAAF,OAAA,CAAAvC,QAAA,CAAAC,GAAA,iCAAAwC,OAAA,CAAAnB,OAAA,OAAAmB,OAAA,GAAAF,OAAA,CAAAvC,QAAA,CAAAC,GAAA,iCAAAwC,OAAA,CAAAlB,OAAA,EAAgF;;;;;IAW7GlC,EAAA,CAAAC,cAAA,iBAAkD;IAAAD,EAAA,CAAAE,MAAA,GAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAhCH,EAAA,CAAAQ,UAAA,UAAA6C,OAAA,CAAa;IAACrD,EAAA,CAAAO,SAAA,GAAS;IAATP,EAAA,CAAAoC,iBAAA,CAAAiB,OAAA,CAAS;;;;;IALjErD,EAAA,CAAAC,cAAA,aAAwC;IAEID,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACrDH,EAAA,CAAAC,cAAA,iBAAoE;IACjDD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACtCH,EAAA,CAAAI,UAAA,IAAAkD,iDAAA,qBAAoE;IACtEtD,EAAA,CAAAG,YAAA,EAAS;;;;IADiBH,EAAA,CAAAO,SAAA,GAAU;IAAVP,EAAA,CAAAQ,UAAA,YAAA+C,OAAA,CAAAC,OAAA,CAAU;;;;;IAqBxCxD,EAAA,CAAAC,cAAA,aAAwC;IAGlCD,EAAA,CAAAqB,SAAA,gBAI+B;IAC/BrB,EAAA,CAAAC,cAAA,gBAAiD;IAC/CD,EAAA,CAAAE,MAAA,yBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAYZH,EAAA,CAAAqB,SAAA,eAAyF;;;;;;IArP/FrB,EAAA,CAAAC,cAAA,cAAsE;IAAzCD,EAAA,CAAAsB,UAAA,sBAAAmC,2DAAA;MAAAzD,EAAA,CAAAwB,aAAA,CAAAkC,IAAA;MAAA,MAAAC,OAAA,GAAA3D,EAAA,CAAA2B,aAAA;MAAA,OAAY3B,EAAA,CAAA4B,WAAA,CAAA+B,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IAClD5D,EAAA,CAAAC,cAAA,aAAiB;IAI0BD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACxDH,EAAA,CAAAqB,SAAA,gBAKsF;IACtFrB,EAAA,CAAAI,UAAA,IAAAyD,uCAAA,kBAGM;IACR7D,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,aAAsB;IAEoBD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACrDH,EAAA,CAAAqB,SAAA,iBAKwF;IACxFrB,EAAA,CAAAI,UAAA,KAAA0D,wCAAA,kBAGM;IACR9D,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAI,UAAA,KAAA2D,wCAAA,mBAsBM;IAEN/D,EAAA,CAAAC,cAAA,cAAsB;IAEmBD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnDH,EAAA,CAAAC,cAAA,kBAIsF;IACnED,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACrCH,EAAA,CAAAI,UAAA,KAAA4D,2CAAA,qBAAqE;IACvEhE,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAI,UAAA,KAAA6D,wCAAA,kBAEM;IACRjE,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,cAAsB;IAEsBD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9DH,EAAA,CAAAqB,SAAA,iBAM4F;IAC5FrB,EAAA,CAAAI,UAAA,KAAA8D,wCAAA,kBAEM;IACRlE,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,cAAsB;IAEsBD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvDH,EAAA,CAAAqB,SAAA,oBAK8C;IAChDrB,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAI,UAAA,KAAA+D,wCAAA,kBAaM;IAENnE,EAAA,CAAAI,UAAA,KAAAgE,wCAAA,kBAaM;IAENpE,EAAA,CAAAI,UAAA,KAAAiE,wCAAA,kBAaM;IAGNrE,EAAA,CAAAI,UAAA,KAAAkE,wCAAA,kBAcM;IAENtE,EAAA,CAAAI,UAAA,KAAAmE,wCAAA,kBAUM;IAGNvE,EAAA,CAAAI,UAAA,KAAAoE,wCAAA,kBAeM;IAENxE,EAAA,CAAAI,UAAA,KAAAqE,wCAAA,kBAQM;IAGNzE,EAAA,CAAAC,cAAA,cAAsB;IAGhBD,EAAA,CAAAqB,SAAA,iBAI6B;IAC7BrB,EAAA,CAAAC,cAAA,iBAA+C;IAC7CD,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAKdH,EAAA,CAAAI,UAAA,KAAAsE,wCAAA,kBAaM;IACR1E,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA0B;IAKtBD,EAAA,CAAAI,UAAA,KAAAuE,yCAAA,mBAAyF;IACzF3E,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAgF;IAAnBD,EAAA,CAAAsB,UAAA,mBAAAsD,2DAAA;MAAA5E,EAAA,CAAAwB,aAAA,CAAAkC,IAAA;MAAA,MAAAmB,OAAA,GAAA7E,EAAA,CAAA2B,aAAA;MAAA,OAAS3B,EAAA,CAAA4B,WAAA,CAAAiD,OAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IAC7E9E,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;;;;;IA1PPH,EAAA,CAAAQ,UAAA,cAAAuE,MAAA,CAAApE,QAAA,CAAsB;IAWlBX,EAAA,CAAAO,SAAA,GAAmF;IAAnFP,EAAA,CAAA+B,WAAA,iBAAAjB,OAAA,GAAAiE,MAAA,CAAApE,QAAA,CAAAC,GAAA,2BAAAE,OAAA,CAAAmB,OAAA,OAAAnB,OAAA,GAAAiE,MAAA,CAAApE,QAAA,CAAAC,GAAA,2BAAAE,OAAA,CAAAoB,OAAA,EAAmF;IACtDlC,EAAA,CAAAO,SAAA,GAAoE;IAApEP,EAAA,CAAAQ,UAAA,WAAA4C,OAAA,GAAA2B,MAAA,CAAApE,QAAA,CAAAC,GAAA,2BAAAwC,OAAA,CAAAnB,OAAA,OAAAmB,OAAA,GAAA2B,MAAA,CAAApE,QAAA,CAAAC,GAAA,2BAAAwC,OAAA,CAAAlB,OAAA,EAAoE;IAejGlC,EAAA,CAAAO,SAAA,GAAqF;IAArFP,EAAA,CAAA+B,WAAA,iBAAAM,OAAA,GAAA0C,MAAA,CAAApE,QAAA,CAAAC,GAAA,4BAAAyB,OAAA,CAAAJ,OAAA,OAAAI,OAAA,GAAA0C,MAAA,CAAApE,QAAA,CAAAC,GAAA,4BAAAyB,OAAA,CAAAH,OAAA,EAAqF;IACxDlC,EAAA,CAAAO,SAAA,GAAsE;IAAtEP,EAAA,CAAAQ,UAAA,WAAAwE,OAAA,GAAAD,MAAA,CAAApE,QAAA,CAAAC,GAAA,4BAAAoE,OAAA,CAAA/C,OAAA,OAAA+C,OAAA,GAAAD,MAAA,CAAApE,QAAA,CAAAC,GAAA,4BAAAoE,OAAA,CAAA9C,OAAA,EAAsE;IAOlFlC,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAQ,UAAA,UAAAuE,MAAA,CAAAE,UAAA,CAAiB;IA+BlCjF,EAAA,CAAAO,SAAA,GAAmF;IAAnFP,EAAA,CAAA+B,WAAA,iBAAAmD,OAAA,GAAAH,MAAA,CAAApE,QAAA,CAAAC,GAAA,2BAAAsE,OAAA,CAAAjD,OAAA,OAAAiD,OAAA,GAAAH,MAAA,CAAApE,QAAA,CAAAC,GAAA,2BAAAsE,OAAA,CAAAhD,OAAA,EAAmF;IAE1DlC,EAAA,CAAAO,SAAA,GAAQ;IAARP,EAAA,CAAAQ,UAAA,YAAAuE,MAAA,CAAAI,KAAA,CAAQ;IAEJnF,EAAA,CAAAO,SAAA,GAAoE;IAApEP,EAAA,CAAAQ,UAAA,WAAA4E,OAAA,GAAAL,MAAA,CAAApE,QAAA,CAAAC,GAAA,2BAAAwE,OAAA,CAAAnD,OAAA,OAAAmD,OAAA,GAAAL,MAAA,CAAApE,QAAA,CAAAC,GAAA,2BAAAwE,OAAA,CAAAlD,OAAA,EAAoE;IAejGlC,EAAA,CAAAO,SAAA,GAAyF;IAAzFP,EAAA,CAAA+B,WAAA,iBAAAsD,OAAA,GAAAN,MAAA,CAAApE,QAAA,CAAAC,GAAA,8BAAAyE,OAAA,CAAApD,OAAA,OAAAoD,OAAA,GAAAN,MAAA,CAAApE,QAAA,CAAAC,GAAA,8BAAAyE,OAAA,CAAAnD,OAAA,EAAyF;IAC5DlC,EAAA,CAAAO,SAAA,GAA0E;IAA1EP,EAAA,CAAAQ,UAAA,WAAA8E,QAAA,GAAAP,MAAA,CAAApE,QAAA,CAAAC,GAAA,8BAAA0E,QAAA,CAAArD,OAAA,OAAAqD,QAAA,GAAAP,MAAA,CAAApE,QAAA,CAAAC,GAAA,8BAAA0E,QAAA,CAAApD,OAAA,EAA0E;IAmBtFlC,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAQ,UAAA,SAAAuE,MAAA,CAAAQ,SAAA,CAAe;IAefvF,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAQ,UAAA,SAAAuE,MAAA,CAAAQ,SAAA,CAAe;IAefvF,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAQ,UAAA,SAAAuE,MAAA,CAAAQ,SAAA,CAAe;IAgBfvF,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAAQ,UAAA,SAAAuE,MAAA,CAAAS,SAAA,IAAAT,MAAA,CAAAU,WAAA,CAA8B;IAgB9BzF,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAAQ,UAAA,SAAAuE,MAAA,CAAAS,SAAA,IAAAT,MAAA,CAAAU,WAAA,CAA8B;IAa9BzF,EAAA,CAAAO,SAAA,GAA2C;IAA3CP,EAAA,CAAAQ,UAAA,SAAAuE,MAAA,CAAAQ,SAAA,IAAAR,MAAA,CAAAS,SAAA,IAAAT,MAAA,CAAAU,WAAA,CAA2C;IAiB3CzF,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAQ,UAAA,SAAAuE,MAAA,CAAAQ,SAAA,CAAe;IA0BfvF,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAQ,UAAA,SAAAuE,MAAA,CAAAS,SAAA,CAAe;IAqBpCxF,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAQ,UAAA,aAAAuE,MAAA,CAAAW,OAAA,IAAAX,MAAA,CAAApE,QAAA,CAAAsB,OAAA,CAAwC;IACjCjC,EAAA,CAAAO,SAAA,GAAa;IAAbP,EAAA,CAAAQ,UAAA,SAAAuE,MAAA,CAAAW,OAAA,CAAa;IACpB1F,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAA2F,kBAAA,MAAAZ,MAAA,CAAAW,OAAA,iBAAAX,MAAA,CAAAE,UAAA,sCACF;;;;;IAQJjF,EAAA,CAAAC,cAAA,cAA8C;IAEZD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEjDH,EAAA,CAAAC,cAAA,YAAgB;IAAAD,EAAA,CAAAE,MAAA,GAA8D;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAAlEH,EAAA,CAAAO,SAAA,GAA8D;IAA9DP,EAAA,CAAAoC,iBAAA,CAAAwD,MAAA,CAAAX,UAAA,+CAA8D;;;AD/PpF,OAAM,MAAOY,iBAAiB;EAY5BC,YACUC,EAAe,EACfC,KAAqB,EACrBC,MAAc,EACdC,WAAwB,EACxBC,WAAwB;IAJxB,KAAAJ,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IAfrB,KAAAlB,UAAU,GAAG,KAAK;IAClB,KAAAmB,MAAM,GAAkB,IAAI;IAC5B,KAAAV,OAAO,GAAG,KAAK;IACf,KAAAvD,YAAY,GAAG,KAAK;IAEpB,KAAAgD,KAAK,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC;IACpD,KAAAhC,WAAW,GAAG,CAAC,aAAa,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,MAAM,EAAE,UAAU,CAAC;IACxF,KAAAK,OAAO,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC;IAClC,KAAA6C,QAAQ,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAQvB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACF,MAAM,GAAG,IAAI,CAACJ,KAAK,CAACO,QAAQ,CAACC,QAAQ,CAAC5F,GAAG,CAAC,IAAI,CAAC;IACpD,IAAI,CAACqE,UAAU,GAAG,CAAC,CAAC,IAAI,CAACmB,MAAM;IAE/B,IAAI,CAACK,cAAc,EAAE;IAErB,IAAI,IAAI,CAACxB,UAAU,EAAE;MACnB,IAAI,CAACyB,QAAQ,EAAE;;EAEnB;EAEAD,cAAcA,CAAA;IACZ,IAAI,CAAC9F,QAAQ,GAAG,IAAI,CAACoF,EAAE,CAACY,KAAK,CAAC;MAC5BC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC9G,UAAU,CAAC+G,QAAQ,EAAE/G,UAAU,CAACgH,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1DC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACjH,UAAU,CAAC+G,QAAQ,EAAE/G,UAAU,CAACiH,KAAK,CAAC,CAAC;MACpDC,QAAQ,EAAE,CAAC,IAAI,CAAC/B,UAAU,GAAG,EAAE,GAAG,EAAE,EAAE,IAAI,CAACA,UAAU,GAAG,EAAE,GAAG,CAACnF,UAAU,CAAC+G,QAAQ,EAAE/G,UAAU,CAACgH,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5GG,IAAI,EAAE,CAAC,EAAE,EAAEnH,UAAU,CAAC+G,QAAQ,CAAC;MAC/BK,OAAO,EAAE,CAAC,EAAE,CAAC;MACbC,KAAK,EAAE,CAAC,EAAE,CAAC;MACXC,MAAM,EAAE,CAAC,EAAE,CAAC;MACZC,OAAO,EAAE,CAAC,EAAE,EAAE,CAACvH,UAAU,CAACwH,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;MAClDC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,UAAU,EAAE,CAAC,KAAK,CAAC;MACnBC,OAAO,EAAE,CAAC,EAAE,CAAC;MACbC,QAAQ,EAAE,CAAC,IAAI;KAChB,CAAC;IAEF;IACA,IAAI,CAAClH,QAAQ,CAACC,GAAG,CAAC,MAAM,CAAC,EAAEkH,YAAY,CAACC,SAAS,CAACd,IAAI,IAAG;MACvD,IAAI,CAACe,2BAA2B,CAACf,IAAI,CAAC;IACxC,CAAC,CAAC;EACJ;EAEAe,2BAA2BA,CAACf,IAAY;IACtC,MAAMgB,YAAY,GAAG,IAAI,CAACtH,QAAQ,CAACC,GAAG,CAAC,OAAO,CAAC;IAC/C,MAAMsH,aAAa,GAAG,IAAI,CAACvH,QAAQ,CAACC,GAAG,CAAC,QAAQ,CAAC;IACjD,MAAMuH,iBAAiB,GAAG,IAAI,CAACxH,QAAQ,CAACC,GAAG,CAAC,YAAY,CAAC;IACzD,MAAMwH,iBAAiB,GAAG,IAAI,CAACzH,QAAQ,CAACC,GAAG,CAAC,aAAa,CAAC;IAC1D,MAAMyH,kBAAkB,GAAG,IAAI,CAAC1H,QAAQ,CAACC,GAAG,CAAC,aAAa,CAAC;IAE3D;IACAqH,YAAY,EAAEK,eAAe,EAAE;IAC/BJ,aAAa,EAAEI,eAAe,EAAE;IAChCH,iBAAiB,EAAEG,eAAe,EAAE;IACpCF,iBAAiB,EAAEE,eAAe,EAAE;IACpCD,kBAAkB,EAAEC,eAAe,EAAE;IAErC,IAAIrB,IAAI,KAAK,SAAS,EAAE;MACtBgB,YAAY,EAAEM,aAAa,CAAC,CAACzI,UAAU,CAAC+G,QAAQ,CAAC,CAAC;MAClDqB,aAAa,EAAEK,aAAa,CAAC,CAACzI,UAAU,CAAC+G,QAAQ,CAAC,CAAC;MACnDsB,iBAAiB,EAAEI,aAAa,CAAC,CAACzI,UAAU,CAAC+G,QAAQ,CAAC,CAAC;MACvDuB,iBAAiB,EAAEG,aAAa,CAAC,CAACzI,UAAU,CAAC+G,QAAQ,CAAC,CAAC;KACxD,MAAM,IAAII,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,WAAW,EAAE;MACrDkB,iBAAiB,EAAEI,aAAa,CAAC,CAACzI,UAAU,CAAC+G,QAAQ,CAAC,CAAC;MACvDwB,kBAAkB,EAAEE,aAAa,CAAC,CAACzI,UAAU,CAAC+G,QAAQ,CAAC,CAAC;;IAG1D;IACAoB,YAAY,EAAEO,sBAAsB,EAAE;IACtCN,aAAa,EAAEM,sBAAsB,EAAE;IACvCL,iBAAiB,EAAEK,sBAAsB,EAAE;IAC3CJ,iBAAiB,EAAEI,sBAAsB,EAAE;IAC3CH,kBAAkB,EAAEG,sBAAsB,EAAE;EAC9C;EAEA9B,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACN,MAAM,EAAE;IAElB,IAAI,CAACV,OAAO,GAAG,IAAI;IACnB;IACA,IAAI,CAACQ,WAAW,CAACuC,WAAW,EAAE,CAACV,SAAS,CAAC;MACvCW,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACjD,OAAO,GAAG,KAAK;QACpB,IAAIiD,QAAQ,CAACC,OAAO,EAAE;UACpB,MAAMC,IAAI,GAAGF,QAAQ,CAACG,KAAK,CAACC,IAAI,CAAEC,CAAO,IAAKA,CAAC,CAACC,GAAG,KAAK,IAAI,CAAC7C,MAAM,CAAC;UACpE,IAAIyC,IAAI,EAAE;YACR,IAAI,CAACK,YAAY,CAACL,IAAI,CAAC;WACxB,MAAM;YACL,IAAI,CAACM,SAAS,CAAC,gBAAgB,CAAC;YAChC,IAAI,CAAClD,MAAM,CAACmD,QAAQ,CAAC,CAAC,wBAAwB,CAAC,CAAC;;;MAGtD,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC3D,OAAO,GAAG,KAAK;QACpB4D,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C,IAAI,CAACF,SAAS,CAAC,0BAA0B,CAAC;MAC5C;KACD,CAAC;EACJ;EAEAD,YAAYA,CAACL,IAAU;IACrB,IAAI,CAAClI,QAAQ,CAAC4I,UAAU,CAAC;MACvB3C,IAAI,EAAEiC,IAAI,CAACjC,IAAI;MACfG,KAAK,EAAE8B,IAAI,CAAC9B,KAAK;MACjBE,IAAI,EAAE4B,IAAI,CAAC5B,IAAI;MACfC,OAAO,EAAE2B,IAAI,CAAC3B,OAAO,IAAI,EAAE;MAC3BC,KAAK,EAAE0B,IAAI,CAAC1B,KAAK,IAAI,EAAE;MACvBC,MAAM,EAAEyB,IAAI,CAACzB,MAAM,IAAI,EAAE;MACzBC,OAAO,EAAEwB,IAAI,CAACxB,OAAO,IAAI,EAAE;MAC3BE,WAAW,EAAEsB,IAAI,CAACtB,WAAW,IAAI,EAAE;MACnCC,UAAU,EAAEqB,IAAI,CAACrB,UAAU,IAAI,EAAE;MACjCC,WAAW,EAAEoB,IAAI,CAACpB,WAAW,IAAI,EAAE;MACnCC,QAAQ,EAAEmB,IAAI,CAACnB,QAAQ,IAAI,EAAE;MAC7BC,UAAU,EAAEkB,IAAI,CAAClB,UAAU,IAAI,KAAK;MACpCC,OAAO,EAAEiB,IAAI,CAACjB,OAAO,IAAI,EAAE;MAC3BC,QAAQ,EAAEgB,IAAI,CAAChB,QAAQ,KAAK;KAC7B,CAAC;EACJ;EAEAjE,QAAQA,CAAA;IACN,IAAI,IAAI,CAACjD,QAAQ,CAAC6I,KAAK,EAAE;MACvB,IAAI,CAAC9D,OAAO,GAAG,IAAI;MACnB,MAAM+D,QAAQ,GAAG,IAAI,CAAC9I,QAAQ,CAAC+I,KAAK;MAEpC;MACA,IAAI,IAAI,CAACzE,UAAU,IAAI,CAACwE,QAAQ,CAACzC,QAAQ,EAAE;QACzC,OAAOyC,QAAQ,CAACzC,QAAQ;;MAG1B,IAAI,IAAI,CAAC/B,UAAU,EAAE;QACnB,IAAI,CAAC0E,UAAU,CAACF,QAAQ,CAAC;OAC1B,MAAM;QACL,IAAI,CAACG,UAAU,CAACH,QAAQ,CAAC;;KAE5B,MAAM;MACL,IAAI,CAACI,oBAAoB,EAAE;;EAE/B;EAEAD,UAAUA,CAACE,QAAa;IACtB,IAAI,CAAC5D,WAAW,CAAC6D,MAAM,CAACD,QAAQ,CAAC,CAAC/B,SAAS,CAAC;MAC1CW,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACjD,OAAO,GAAG,KAAK;QACpB,IAAIiD,QAAQ,CAACC,OAAO,EAAE;UACpB7I,IAAI,CAACiK,IAAI,CAAC;YACRC,KAAK,EAAE,UAAU;YACjBC,IAAI,EAAE,4BAA4B;YAClCC,IAAI,EAAE,SAAS;YACfC,kBAAkB,EAAE;WACrB,CAAC,CAACC,IAAI,CAAC,MAAK;YACX,IAAI,CAACpE,MAAM,CAACmD,QAAQ,CAAC,CAAC,wBAAwB,CAAC,CAAC;UAClD,CAAC,CAAC;;MAEN,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC3D,OAAO,GAAG,KAAK;QACpB4D,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,MAAMiB,YAAY,GAAGjB,KAAK,CAACA,KAAK,EAAEkB,OAAO,IAAI,uBAAuB;QACpE,IAAI,CAACpB,SAAS,CAACmB,YAAY,CAAC;MAC9B;KACD,CAAC;EACJ;EAEAX,UAAUA,CAACG,QAAa;IACtB;IACA;IACA,IAAI,CAACpE,OAAO,GAAG,KAAK;IACpB3F,IAAI,CAACiK,IAAI,CAAC;MACRC,KAAK,EAAE,uBAAuB;MAC9BC,IAAI,EAAE,6FAA6F;MACnGC,IAAI,EAAE,MAAM;MACZC,kBAAkB,EAAE;KACrB,CAAC,CAACC,IAAI,CAAC,MAAK;MACX,IAAI,CAACpE,MAAM,CAACmD,QAAQ,CAAC,CAAC,wBAAwB,CAAC,CAAC;IAClD,CAAC,CAAC;EACJ;EAEAvH,wBAAwBA,CAAA;IACtB,IAAI,CAACM,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEA2C,MAAMA,CAAA;IACJ,IAAI,CAACmB,MAAM,CAACmD,QAAQ,CAAC,CAAC,wBAAwB,CAAC,CAAC;EAClD;EAEQS,oBAAoBA,CAAA;IAC1BW,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC9J,QAAQ,CAAC+J,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MAChD,IAAI,CAACjK,QAAQ,CAACC,GAAG,CAACgK,GAAG,CAAC,EAAEC,aAAa,EAAE;IACzC,CAAC,CAAC;EACJ;EAEQ1B,SAASA,CAACoB,OAAe;IAC/BxK,IAAI,CAACiK,IAAI,CAAC;MACRC,KAAK,EAAE,OAAO;MACdC,IAAI,EAAEK,OAAO;MACbJ,IAAI,EAAE,OAAO;MACbC,kBAAkB,EAAE;KACrB,CAAC;EACJ;EAEA;EACA,IAAI7E,SAASA,CAAA;IACX,OAAO,IAAI,CAAC5E,QAAQ,CAACC,GAAG,CAAC,MAAM,CAAC,EAAE8I,KAAK,KAAK,SAAS;EACvD;EAEA,IAAIlE,SAASA,CAAA;IACX,OAAO,IAAI,CAAC7E,QAAQ,CAACC,GAAG,CAAC,MAAM,CAAC,EAAE8I,KAAK,KAAK,SAAS;EACvD;EAEA,IAAIjE,WAAWA,CAAA;IACb,OAAO,IAAI,CAAC9E,QAAQ,CAACC,GAAG,CAAC,MAAM,CAAC,EAAE8I,KAAK,KAAK,WAAW;EACzD;EAAC,QAAAoB,CAAA,G;qBAjOUjF,iBAAiB,EAAA7F,EAAA,CAAA+K,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjL,EAAA,CAAA+K,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAnL,EAAA,CAAA+K,iBAAA,CAAAG,EAAA,CAAAE,MAAA,GAAApL,EAAA,CAAA+K,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAAtL,EAAA,CAAA+K,iBAAA,CAAAQ,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAjB5F,iBAAiB;IAAA6F,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCb9BhM,EAAA,CAAAC,cAAA,aAAqB;QAGXD,EAAA,CAAAE,MAAA,GAA+C;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACxDH,EAAA,CAAAC,cAAA,gBAA6D;QAAnBD,EAAA,CAAAsB,UAAA,mBAAA4K,mDAAA;UAAA,OAASD,GAAA,CAAAnH,MAAA,EAAQ;QAAA,EAAC;QAC1D9E,EAAA,CAAAC,cAAA,eAAU;QAAAD,EAAA,CAAAE,MAAA,iBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAACH,EAAA,CAAAE,MAAA,sBAClC;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAGXH,EAAA,CAAAI,UAAA,IAAA+L,iCAAA,oBA4PO;QAGPnM,EAAA,CAAAI,UAAA,KAAAgM,iCAAA,iBAKM;QACRpM,EAAA,CAAAG,YAAA,EAAM;;;QA3QEH,EAAA,CAAAO,SAAA,GAA+C;QAA/CP,EAAA,CAAAoC,iBAAA,CAAA6J,GAAA,CAAAhH,UAAA,gCAA+C;QAMCjF,EAAA,CAAAO,SAAA,GAAc;QAAdP,EAAA,CAAAQ,UAAA,UAAAyL,GAAA,CAAAvG,OAAA,CAAc;QA+P9D1F,EAAA,CAAAO,SAAA,GAAa;QAAbP,EAAA,CAAAQ,UAAA,SAAAyL,GAAA,CAAAvG,OAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}