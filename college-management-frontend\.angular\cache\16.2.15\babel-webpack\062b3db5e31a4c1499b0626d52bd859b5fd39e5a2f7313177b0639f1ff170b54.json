{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/user.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/classes.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction AddStudentComponent_option_93_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const class_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", class_r1._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate3(\" \", class_r1.className, \" - \", class_r1.department, \" (\", class_r1.section, \") \");\n  }\n}\nexport class AddStudentComponent {\n  constructor(userService, router, classService) {\n    this.userService = userService;\n    this.router = router;\n    this.classService = classService;\n    this.classes = []; // Holds the classes data\n    // Student model to store form data\n    this.student = {\n      name: '',\n      email: '',\n      password: '',\n      rollno: '',\n      reg_no: '',\n      father_name: '',\n      father_contact: '',\n      contact_no: '',\n      address: '',\n      date_of_birth: '',\n      school_last_attendance: '',\n      year_of_passing: '',\n      className: '',\n      section: '',\n      department: '',\n      classId: ''\n    };\n    this.getClasses();\n  }\n  // Fetch all classes from the API\n  getClasses() {\n    this.classService.getClasses().subscribe(response => {\n      if (response.success) {\n        this.classes = response.classes;\n        console.log(\"this is clases\", this.classes);\n      }\n    }, error => {\n      console.error('Error fetching classes:', error);\n    });\n  }\n  // Validate required fields\n  validateForm() {\n    const requiredFields = [{\n      field: this.student.name,\n      name: 'Student Name'\n    }, {\n      field: this.student.father_name,\n      name: 'Father\\'s Name'\n    }, {\n      field: this.student.father_contact,\n      name: 'Father\\'s Contact'\n    }, {\n      field: this.student.date_of_birth,\n      name: 'Date of Birth'\n    }, {\n      field: this.student.email,\n      name: 'Email'\n    }, {\n      field: this.student.password,\n      name: 'Password'\n    }, {\n      field: this.student.rollno,\n      name: 'Roll Number'\n    }, {\n      field: this.student.reg_no,\n      name: 'Registration Number'\n    }, {\n      field: this.student.classId,\n      name: 'Class'\n    }];\n    for (const field of requiredFields) {\n      if (!field.field || field.field.trim() === '') {\n        alert(`Please fill in the ${field.name} field.`);\n        return false;\n      }\n    }\n    // Email validation\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    if (!emailRegex.test(this.student.email)) {\n      alert('Please enter a valid email address.');\n      return false;\n    }\n    // Phone number validation (basic)\n    const phoneRegex = /^[0-9]{10,15}$/;\n    if (!phoneRegex.test(this.student.father_contact.replace(/\\s+/g, ''))) {\n      alert('Please enter a valid father\\'s contact number (10-15 digits).');\n      return false;\n    }\n    return true;\n  }\n  // Save student data and file (if any)\n  onSave() {\n    // Validate form before saving\n    if (!this.validateForm()) {\n      return;\n    }\n    const studentData = {\n      name: this.student.name,\n      email: this.student.email,\n      password: this.student.password,\n      rollNo: this.student.rollno,\n      regNo: this.student.reg_no,\n      father_name: this.student.father_name,\n      father_contact: this.student.father_contact,\n      contact: this.student.contact_no,\n      address: this.student.address,\n      date_of_birth: this.student.date_of_birth,\n      school_last_attendance: this.student.school_last_attendance,\n      year_of_passing: this.student.year_of_passing,\n      role: 'Student',\n      className: this.student.className,\n      section: this.student.section,\n      department: this.student.department,\n      classId: this.student.classId\n    };\n    // Send student data to the backend using userService\n    this.userService.register(studentData).subscribe({\n      next: response => {\n        // Handle success response\n        console.log('Registration successful:', response);\n        alert('Student registered successfully!');\n        this.router.navigate(['/dashboard/admin/students/student-list']);\n        // Optionally, clear the form\n        this.resetForm();\n      },\n      error: error => {\n        // Handle error response\n        console.error('Error registering student:', error);\n        alert(`Error: ${error.error.message || 'Failed to register student'}`);\n      }\n    });\n  }\n  resetForm() {\n    this.student = {\n      name: '',\n      email: '',\n      password: '',\n      rollno: '',\n      reg_no: '',\n      father_name: '',\n      father_contact: '',\n      contact_no: '',\n      address: '',\n      date_of_birth: '',\n      school_last_attendance: '',\n      year_of_passing: '',\n      className: '',\n      section: '',\n      department: '',\n      classId: ''\n    };\n  }\n  static #_ = this.ɵfac = function AddStudentComponent_Factory(t) {\n    return new (t || AddStudentComponent)(i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.ClassesService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AddStudentComponent,\n    selectors: [[\"app-add-student\"]],\n    decls: 115,\n    vars: 14,\n    consts: [[1, \"form-container\"], [1, \"form-header\"], [1, \"fas\", \"fa-user-plus\", \"me-2\"], [1, \"form-grid\"], [1, \"form-section\"], [1, \"section-title\"], [1, \"fas\", \"fa-user\", \"me-2\"], [1, \"form-group\"], [\"for\", \"name\"], [1, \"fas\", \"fa-user\", \"me-1\"], [1, \"required\"], [\"type\", \"text\", \"name\", \"name\", \"id\", \"name\", \"placeholder\", \"Enter full name\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"father_name\"], [1, \"fas\", \"fa-male\", \"me-1\"], [\"type\", \"text\", \"name\", \"father_name\", \"id\", \"father_name\", \"placeholder\", \"Enter father's full name\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"date_of_birth\"], [1, \"fas\", \"fa-calendar\", \"me-1\"], [\"type\", \"date\", \"name\", \"date_of_birth\", \"id\", \"date_of_birth\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"address\"], [1, \"fas\", \"fa-map-marker-alt\", \"me-1\"], [\"name\", \"address\", \"id\", \"address\", \"rows\", \"3\", \"placeholder\", \"Enter complete address\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"fas\", \"fa-phone\", \"me-2\"], [\"for\", \"contact_no\"], [1, \"fas\", \"fa-mobile-alt\", \"me-1\"], [\"type\", \"tel\", \"name\", \"contact_no\", \"id\", \"contact_no\", \"placeholder\", \"Enter student's mobile number\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"father_contact\"], [1, \"fas\", \"fa-phone\", \"me-1\"], [\"type\", \"tel\", \"name\", \"father_contact\", \"id\", \"father_contact\", \"placeholder\", \"Enter father's mobile number\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"email\"], [1, \"fas\", \"fa-envelope\", \"me-1\"], [\"type\", \"email\", \"name\", \"email\", \"id\", \"email\", \"placeholder\", \"Enter email address\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"password\"], [1, \"fas\", \"fa-lock\", \"me-1\"], [\"type\", \"password\", \"name\", \"password\", \"id\", \"password\", \"placeholder\", \"Enter password\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"fas\", \"fa-graduation-cap\", \"me-2\"], [\"for\", \"rollno\"], [1, \"fas\", \"fa-id-card\", \"me-1\"], [\"type\", \"text\", \"name\", \"rollno\", \"id\", \"rollno\", \"placeholder\", \"Enter roll number\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"reg_no\"], [1, \"fas\", \"fa-certificate\", \"me-1\"], [\"type\", \"text\", \"name\", \"reg_no\", \"id\", \"reg_no\", \"placeholder\", \"Enter registration number\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"class\"], [1, \"fas\", \"fa-chalkboard\", \"me-1\"], [\"name\", \"class\", \"id\", \"class\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"value\", \"\", \"disabled\", \"\", \"selected\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"school_last_attendance\"], [1, \"fas\", \"fa-school\", \"me-1\"], [1, \"optional\"], [\"type\", \"text\", \"name\", \"school_last_attendance\", \"id\", \"school_last_attendance\", \"placeholder\", \"Enter previous school name\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"year_of_passing\"], [1, \"fas\", \"fa-calendar-check\", \"me-1\"], [\"type\", \"number\", \"name\", \"year_of_passing\", \"id\", \"year_of_passing\", \"placeholder\", \"Enter year of passing\", \"min\", \"1990\", \"max\", \"2030\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"form-actions\"], [\"type\", \"button\", 1, \"btn\", \"btn-back\", \"me-3\", 3, \"click\"], [1, \"fas\", \"fa-undo\", \"me-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-save\", 3, \"click\"], [1, \"fas\", \"fa-save\", \"me-1\"], [3, \"value\"]],\n    template: function AddStudentComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n        i0.ɵɵelement(3, \"i\", 2);\n        i0.ɵɵtext(4, \"Add New Student\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(5, \"div\", 3)(6, \"div\", 4)(7, \"h3\", 5);\n        i0.ɵɵelement(8, \"i\", 6);\n        i0.ɵɵtext(9, \"Personal Information\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"div\", 7)(11, \"label\", 8);\n        i0.ɵɵelement(12, \"i\", 9);\n        i0.ɵɵtext(13, \"Student Name \");\n        i0.ɵɵelementStart(14, \"span\", 10);\n        i0.ɵɵtext(15, \"*\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(16, \"input\", 11);\n        i0.ɵɵlistener(\"ngModelChange\", function AddStudentComponent_Template_input_ngModelChange_16_listener($event) {\n          return ctx.student.name = $event;\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(17, \"div\", 7)(18, \"label\", 12);\n        i0.ɵɵelement(19, \"i\", 13);\n        i0.ɵɵtext(20, \"Father's Name \");\n        i0.ɵɵelementStart(21, \"span\", 10);\n        i0.ɵɵtext(22, \"*\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(23, \"input\", 14);\n        i0.ɵɵlistener(\"ngModelChange\", function AddStudentComponent_Template_input_ngModelChange_23_listener($event) {\n          return ctx.student.father_name = $event;\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(24, \"div\", 7)(25, \"label\", 15);\n        i0.ɵɵelement(26, \"i\", 16);\n        i0.ɵɵtext(27, \"Date of Birth \");\n        i0.ɵɵelementStart(28, \"span\", 10);\n        i0.ɵɵtext(29, \"*\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(30, \"input\", 17);\n        i0.ɵɵlistener(\"ngModelChange\", function AddStudentComponent_Template_input_ngModelChange_30_listener($event) {\n          return ctx.student.date_of_birth = $event;\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(31, \"div\", 7)(32, \"label\", 18);\n        i0.ɵɵelement(33, \"i\", 19);\n        i0.ɵɵtext(34, \"Address\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(35, \"textarea\", 20);\n        i0.ɵɵlistener(\"ngModelChange\", function AddStudentComponent_Template_textarea_ngModelChange_35_listener($event) {\n          return ctx.student.address = $event;\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(36, \"div\", 4)(37, \"h3\", 5);\n        i0.ɵɵelement(38, \"i\", 21);\n        i0.ɵɵtext(39, \"Contact Information\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(40, \"div\", 7)(41, \"label\", 22);\n        i0.ɵɵelement(42, \"i\", 23);\n        i0.ɵɵtext(43, \"Student Contact Number\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(44, \"input\", 24);\n        i0.ɵɵlistener(\"ngModelChange\", function AddStudentComponent_Template_input_ngModelChange_44_listener($event) {\n          return ctx.student.contact_no = $event;\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(45, \"div\", 7)(46, \"label\", 25);\n        i0.ɵɵelement(47, \"i\", 26);\n        i0.ɵɵtext(48, \"Father's Contact Number \");\n        i0.ɵɵelementStart(49, \"span\", 10);\n        i0.ɵɵtext(50, \"*\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(51, \"input\", 27);\n        i0.ɵɵlistener(\"ngModelChange\", function AddStudentComponent_Template_input_ngModelChange_51_listener($event) {\n          return ctx.student.father_contact = $event;\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(52, \"div\", 7)(53, \"label\", 28);\n        i0.ɵɵelement(54, \"i\", 29);\n        i0.ɵɵtext(55, \"Email Address \");\n        i0.ɵɵelementStart(56, \"span\", 10);\n        i0.ɵɵtext(57, \"*\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(58, \"input\", 30);\n        i0.ɵɵlistener(\"ngModelChange\", function AddStudentComponent_Template_input_ngModelChange_58_listener($event) {\n          return ctx.student.email = $event;\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(59, \"div\", 7)(60, \"label\", 31);\n        i0.ɵɵelement(61, \"i\", 32);\n        i0.ɵɵtext(62, \"Password \");\n        i0.ɵɵelementStart(63, \"span\", 10);\n        i0.ɵɵtext(64, \"*\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(65, \"input\", 33);\n        i0.ɵɵlistener(\"ngModelChange\", function AddStudentComponent_Template_input_ngModelChange_65_listener($event) {\n          return ctx.student.password = $event;\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(66, \"div\", 4)(67, \"h3\", 5);\n        i0.ɵɵelement(68, \"i\", 34);\n        i0.ɵɵtext(69, \"Academic Information\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(70, \"div\", 7)(71, \"label\", 35);\n        i0.ɵɵelement(72, \"i\", 36);\n        i0.ɵɵtext(73, \"Roll Number \");\n        i0.ɵɵelementStart(74, \"span\", 10);\n        i0.ɵɵtext(75, \"*\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(76, \"input\", 37);\n        i0.ɵɵlistener(\"ngModelChange\", function AddStudentComponent_Template_input_ngModelChange_76_listener($event) {\n          return ctx.student.rollno = $event;\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(77, \"div\", 7)(78, \"label\", 38);\n        i0.ɵɵelement(79, \"i\", 39);\n        i0.ɵɵtext(80, \"Registration Number \");\n        i0.ɵɵelementStart(81, \"span\", 10);\n        i0.ɵɵtext(82, \"*\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(83, \"input\", 40);\n        i0.ɵɵlistener(\"ngModelChange\", function AddStudentComponent_Template_input_ngModelChange_83_listener($event) {\n          return ctx.student.reg_no = $event;\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(84, \"div\", 7)(85, \"label\", 41);\n        i0.ɵɵelement(86, \"i\", 42);\n        i0.ɵɵtext(87, \"Class \");\n        i0.ɵɵelementStart(88, \"span\", 10);\n        i0.ɵɵtext(89, \"*\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(90, \"select\", 43);\n        i0.ɵɵlistener(\"ngModelChange\", function AddStudentComponent_Template_select_ngModelChange_90_listener($event) {\n          return ctx.student.classId = $event;\n        });\n        i0.ɵɵelementStart(91, \"option\", 44);\n        i0.ɵɵtext(92, \"Select a class\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(93, AddStudentComponent_option_93_Template, 2, 4, \"option\", 45);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(94, \"div\", 7)(95, \"label\", 46);\n        i0.ɵɵelement(96, \"i\", 47);\n        i0.ɵɵtext(97, \"School Last Attended \");\n        i0.ɵɵelementStart(98, \"span\", 48);\n        i0.ɵɵtext(99, \"(Optional)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(100, \"input\", 49);\n        i0.ɵɵlistener(\"ngModelChange\", function AddStudentComponent_Template_input_ngModelChange_100_listener($event) {\n          return ctx.student.school_last_attendance = $event;\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(101, \"div\", 7)(102, \"label\", 50);\n        i0.ɵɵelement(103, \"i\", 51);\n        i0.ɵɵtext(104, \"Year of Passing \");\n        i0.ɵɵelementStart(105, \"span\", 48);\n        i0.ɵɵtext(106, \"(Optional)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(107, \"input\", 52);\n        i0.ɵɵlistener(\"ngModelChange\", function AddStudentComponent_Template_input_ngModelChange_107_listener($event) {\n          return ctx.student.year_of_passing = $event;\n        });\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(108, \"div\", 53)(109, \"button\", 54);\n        i0.ɵɵlistener(\"click\", function AddStudentComponent_Template_button_click_109_listener() {\n          return ctx.resetForm();\n        });\n        i0.ɵɵelement(110, \"i\", 55);\n        i0.ɵɵtext(111, \"Reset Form \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(112, \"button\", 56);\n        i0.ɵɵlistener(\"click\", function AddStudentComponent_Template_button_click_112_listener() {\n          return ctx.onSave();\n        });\n        i0.ɵɵelement(113, \"i\", 57);\n        i0.ɵɵtext(114, \"Save Student \");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(16);\n        i0.ɵɵproperty(\"ngModel\", ctx.student.name);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngModel\", ctx.student.father_name);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngModel\", ctx.student.date_of_birth);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngModel\", ctx.student.address);\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"ngModel\", ctx.student.contact_no);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngModel\", ctx.student.father_contact);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngModel\", ctx.student.email);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngModel\", ctx.student.password);\n        i0.ɵɵadvance(11);\n        i0.ɵɵproperty(\"ngModel\", ctx.student.rollno);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngModel\", ctx.student.reg_no);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngModel\", ctx.student.classId);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.classes);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngModel\", ctx.student.school_last_attendance);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngModel\", ctx.student.year_of_passing);\n      }\n    },\n    dependencies: [i4.NgForOf, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.DefaultValueAccessor, i5.NumberValueAccessor, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.RequiredValidator, i5.MinValidator, i5.MaxValidator, i5.NgModel],\n    styles: [\"\\n\\n.form-container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 32px;\\n  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);\\n  border-radius: 16px;\\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);\\n  border: 1px solid #e2e8f0;\\n}\\n\\n.form-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 40px;\\n  padding-bottom: 20px;\\n  border-bottom: 2px solid #e2e8f0;\\n}\\n\\n.form-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  color: #11418e;\\n  margin: 0;\\n  font-size: 28px;\\n  font-weight: 700;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.form-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n  gap: 32px;\\n  margin-bottom: 32px;\\n}\\n\\n.form-section[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  padding: 24px;\\n  border-radius: 12px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\\n  border: 1px solid #f1f5f9;\\n  transition: all 0.3s ease;\\n}\\n\\n.form-section[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);\\n  transform: translateY(-2px);\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  color: #1e293b;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin-bottom: 20px;\\n  padding-bottom: 12px;\\n  border-bottom: 2px solid #f1f5f9;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.section-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #11418e;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 8px;\\n  color: #374151;\\n  font-weight: 600;\\n  font-size: 14px;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #6b7280;\\n  margin-right: 6px;\\n}\\n\\n.required[_ngcontent-%COMP%] {\\n  color: #ef4444;\\n  font-weight: 500;\\n  margin-left: 4px;\\n}\\n\\n.optional[_ngcontent-%COMP%] {\\n  color: #6b7280;\\n  font-weight: 400;\\n  font-size: 12px;\\n  margin-left: 4px;\\n}\\n\\n.form-control[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 12px 16px;\\n  border: 2px solid #e5e7eb;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  transition: all 0.3s ease;\\n  background-color: #ffffff;\\n}\\n\\n.form-control[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #11418e;\\n  box-shadow: 0 0 0 3px rgba(17, 65, 142, 0.1);\\n  background-color: #fafbfc;\\n}\\n\\n.form-control[_ngcontent-%COMP%]:hover {\\n  border-color: #9ca3af;\\n}\\n\\n.form-control[_ngcontent-%COMP%]::placeholder {\\n  color: #9ca3af;\\n  font-style: italic;\\n}\\n\\ntextarea.form-control[_ngcontent-%COMP%] {\\n  resize: vertical;\\n  min-height: 80px;\\n}\\n\\nselect.form-control[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n\\n.form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 16px;\\n  padding-top: 24px;\\n  border-top: 2px solid #f1f5f9;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 12px 24px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  font-size: 14px;\\n  border: none;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  text-decoration: none;\\n}\\n\\n.btn-save[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #11418e 0%, #1e40af 100%);\\n  color: white;\\n  box-shadow: 0 4px 12px rgba(17, 65, 142, 0.3);\\n}\\n\\n.btn-save[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #0f3a7a 0%, #1d4ed8 100%);\\n  box-shadow: 0 6px 16px rgba(17, 65, 142, 0.4);\\n  transform: translateY(-2px);\\n}\\n\\n.btn-back[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  color: #11418e;\\n  border: 2px solid #11418e;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.btn-back[_ngcontent-%COMP%]:hover {\\n  background: #11418e;\\n  color: white;\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(17, 65, 142, 0.3);\\n}\\n\\n\\n\\n@media screen and (max-width: 768px) {\\n  .form-container[_ngcontent-%COMP%] {\\n    padding: 20px;\\n    margin: 10px;\\n  }\\n\\n  .form-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 20px;\\n  }\\n\\n  .form-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n    font-size: 24px;\\n  }\\n\\n  .form-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n\\n  .btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n}\\n\\n@media screen and (max-width: 480px) {\\n  .form-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n    margin: 8px;\\n  }\\n\\n  .form-section[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "class_r1", "_id", "ɵɵadvance", "ɵɵtextInterpolate3", "className", "department", "section", "AddStudentComponent", "constructor", "userService", "router", "classService", "classes", "student", "name", "email", "password", "rollno", "reg_no", "father_name", "father_contact", "contact_no", "address", "date_of_birth", "school_last_attendance", "year_of_passing", "classId", "getClasses", "subscribe", "response", "success", "console", "log", "error", "validateForm", "requiredFields", "field", "trim", "alert", "emailRegex", "test", "phoneRegex", "replace", "onSave", "studentData", "rollNo", "regNo", "contact", "role", "register", "next", "navigate", "resetForm", "message", "_", "ɵɵdirectiveInject", "i1", "UserService", "i2", "Router", "i3", "ClassesService", "_2", "selectors", "decls", "vars", "consts", "template", "AddStudentComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "AddStudentComponent_Template_input_ngModelChange_16_listener", "$event", "AddStudentComponent_Template_input_ngModelChange_23_listener", "AddStudentComponent_Template_input_ngModelChange_30_listener", "AddStudentComponent_Template_textarea_ngModelChange_35_listener", "AddStudentComponent_Template_input_ngModelChange_44_listener", "AddStudentComponent_Template_input_ngModelChange_51_listener", "AddStudentComponent_Template_input_ngModelChange_58_listener", "AddStudentComponent_Template_input_ngModelChange_65_listener", "AddStudentComponent_Template_input_ngModelChange_76_listener", "AddStudentComponent_Template_input_ngModelChange_83_listener", "AddStudentComponent_Template_select_ngModelChange_90_listener", "ɵɵtemplate", "AddStudentComponent_option_93_Template", "AddStudentComponent_Template_input_ngModelChange_100_listener", "AddStudentComponent_Template_input_ngModelChange_107_listener", "AddStudentComponent_Template_button_click_109_listener", "AddStudentComponent_Template_button_click_112_listener"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\students\\add-student\\add-student.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\students\\add-student\\add-student.component.html"], "sourcesContent": ["import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';\r\nimport { Component, ElementRef, ViewChild } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { ClassesService } from 'src/app/services/classes.service';\r\nimport { UserService } from 'src/app/services/user.service';\r\n\r\n@Component({\r\n  selector: 'app-add-student',\r\n  templateUrl: './add-student.component.html',\r\n  styleUrls: ['./add-student.component.css']\r\n})\r\nexport class AddStudentComponent {\r\n  classes: any[] = []; // Holds the classes data\r\n\r\n  constructor(private userService: UserService, private router:Router , private classService:ClassesService){\r\nthis.getClasses()\r\n  }\r\n // Fetch all classes from the API\r\n getClasses(): void {\r\n  this.classService.getClasses().subscribe(\r\n    (response: any) => {\r\n      if (response.success) {\r\n        this.classes = response.classes;\r\n        console.log(\"this is clases\" , this.classes)\r\n        \r\n      }\r\n    },\r\n    (error) => {\r\n      console.error('Error fetching classes:', error);\r\n    }\r\n  );\r\n}\r\n  // Student model to store form data\r\n  student = {\r\n    name: '',\r\n    email: '',\r\n    password: '',\r\n    rollno: '',\r\n    reg_no: '',\r\n    father_name: '',\r\n    father_contact: '',\r\n    contact_no: '',\r\n    address: '',\r\n    date_of_birth: '',\r\n    school_last_attendance: '',\r\n    year_of_passing: '',\r\n    className:'',\r\n    section:'',\r\n    department:'',\r\n    classId: '',\r\n  };\r\n  \r\n \r\n  // Validate required fields\r\n  validateForm(): boolean {\r\n    const requiredFields = [\r\n      { field: this.student.name, name: 'Student Name' },\r\n      { field: this.student.father_name, name: 'Father\\'s Name' },\r\n      { field: this.student.father_contact, name: 'Father\\'s Contact' },\r\n      { field: this.student.date_of_birth, name: 'Date of Birth' },\r\n      { field: this.student.email, name: 'Email' },\r\n      { field: this.student.password, name: 'Password' },\r\n      { field: this.student.rollno, name: 'Roll Number' },\r\n      { field: this.student.reg_no, name: 'Registration Number' },\r\n      { field: this.student.classId, name: 'Class' }\r\n    ];\r\n\r\n    for (const field of requiredFields) {\r\n      if (!field.field || field.field.trim() === '') {\r\n        alert(`Please fill in the ${field.name} field.`);\r\n        return false;\r\n      }\r\n    }\r\n\r\n    // Email validation\r\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n    if (!emailRegex.test(this.student.email)) {\r\n      alert('Please enter a valid email address.');\r\n      return false;\r\n    }\r\n\r\n    // Phone number validation (basic)\r\n    const phoneRegex = /^[0-9]{10,15}$/;\r\n    if (!phoneRegex.test(this.student.father_contact.replace(/\\s+/g, ''))) {\r\n      alert('Please enter a valid father\\'s contact number (10-15 digits).');\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  // Save student data and file (if any)\r\n  onSave() {\r\n    // Validate form before saving\r\n    if (!this.validateForm()) {\r\n      return;\r\n    }\r\n\r\n    const studentData: any = {\r\n      name: this.student.name,\r\n      email: this.student.email,\r\n      password: this.student.password,\r\n      rollNo: this.student.rollno,\r\n      regNo: this.student.reg_no,\r\n      father_name: this.student.father_name,\r\n      father_contact: this.student.father_contact,\r\n      contact: this.student.contact_no,\r\n      address: this.student.address,\r\n      date_of_birth: this.student.date_of_birth,\r\n      school_last_attendance: this.student.school_last_attendance,\r\n      year_of_passing: this.student.year_of_passing,\r\n      role: 'Student',\r\n      className: this.student.className,\r\n      section: this.student.section,\r\n      department: this.student.department,\r\n      classId: this.student.classId,\r\n    };\r\n\r\n    // Send student data to the backend using userService\r\n    this.userService.register(studentData as any).subscribe({\r\n      next: (response) => {\r\n        // Handle success response\r\n        console.log('Registration successful:', response);\r\n        alert('Student registered successfully!');\r\n        this.router.navigate(['/dashboard/admin/students/student-list'])\r\n        // Optionally, clear the form\r\n        this.resetForm();\r\n      },\r\n      error: (error: HttpErrorResponse) => {\r\n        // Handle error response\r\n        console.error('Error registering student:', error);\r\n        alert(`Error: ${error.error.message || 'Failed to register student'}`);\r\n      } \r\n    });\r\n  }\r\n\r\n  resetForm() {\r\n    this.student = {\r\n      name: '',\r\n      email: '',\r\n      password: '',\r\n      rollno: '',\r\n      reg_no: '',\r\n      father_name: '',\r\n      father_contact: '',\r\n      contact_no: '',\r\n      address: '',\r\n      date_of_birth: '',\r\n      school_last_attendance: '',\r\n      year_of_passing: '',\r\n      className:'',\r\n      section:'',\r\n      department:'',\r\n      classId: '',\r\n    };\r\n  }\r\n\r\n}\r\n", "<div class=\"form-container\">\r\n  <div class=\"form-header\">\r\n    <h2><i class=\"fas fa-user-plus me-2\"></i>Add New Student</h2>\r\n  </div>\r\n\r\n  <div class=\"form-grid\">\r\n    <!-- Personal Information Section -->\r\n    <div class=\"form-section\">\r\n      <h3 class=\"section-title\"><i class=\"fas fa-user me-2\"></i>Personal Information</h3>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"name\"><i class=\"fas fa-user me-1\"></i>Student Name <span class=\"required\">*</span></label>\r\n        <input type=\"text\" name=\"name\" id=\"name\" class=\"form-control\" placeholder=\"Enter full name\" [(ngModel)]=\"student.name\" required>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"father_name\"><i class=\"fas fa-male me-1\"></i>Father's Name <span class=\"required\">*</span></label>\r\n        <input type=\"text\" name=\"father_name\" id=\"father_name\" class=\"form-control\" placeholder=\"Enter father's full name\" [(ngModel)]=\"student.father_name\" required>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"date_of_birth\"><i class=\"fas fa-calendar me-1\"></i>Date of Birth <span class=\"required\">*</span></label>\r\n        <input type=\"date\" name=\"date_of_birth\" id=\"date_of_birth\" class=\"form-control\" [(ngModel)]=\"student.date_of_birth\" required>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"address\"><i class=\"fas fa-map-marker-alt me-1\"></i>Address</label>\r\n        <textarea name=\"address\" id=\"address\" class=\"form-control\" rows=\"3\" placeholder=\"Enter complete address\" [(ngModel)]=\"student.address\"></textarea>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Contact Information Section -->\r\n    <div class=\"form-section\">\r\n      <h3 class=\"section-title\"><i class=\"fas fa-phone me-2\"></i>Contact Information</h3>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"contact_no\"><i class=\"fas fa-mobile-alt me-1\"></i>Student Contact Number</label>\r\n        <input type=\"tel\" name=\"contact_no\" id=\"contact_no\" class=\"form-control\" placeholder=\"Enter student's mobile number\" [(ngModel)]=\"student.contact_no\">\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"father_contact\"><i class=\"fas fa-phone me-1\"></i>Father's Contact Number <span class=\"required\">*</span></label>\r\n        <input type=\"tel\" name=\"father_contact\" id=\"father_contact\" class=\"form-control\" placeholder=\"Enter father's mobile number\" [(ngModel)]=\"student.father_contact\" required>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"email\"><i class=\"fas fa-envelope me-1\"></i>Email Address <span class=\"required\">*</span></label>\r\n        <input type=\"email\" name=\"email\" id=\"email\" class=\"form-control\" placeholder=\"Enter email address\" [(ngModel)]=\"student.email\" required>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"password\"><i class=\"fas fa-lock me-1\"></i>Password <span class=\"required\">*</span></label>\r\n        <input type=\"password\" name=\"password\" id=\"password\" class=\"form-control\" placeholder=\"Enter password\" [(ngModel)]=\"student.password\" required>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Academic Information Section -->\r\n    <div class=\"form-section\">\r\n      <h3 class=\"section-title\"><i class=\"fas fa-graduation-cap me-2\"></i>Academic Information</h3>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"rollno\"><i class=\"fas fa-id-card me-1\"></i>Roll Number <span class=\"required\">*</span></label>\r\n        <input type=\"text\" name=\"rollno\" id=\"rollno\" class=\"form-control\" placeholder=\"Enter roll number\" [(ngModel)]=\"student.rollno\" required>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"reg_no\"><i class=\"fas fa-certificate me-1\"></i>Registration Number <span class=\"required\">*</span></label>\r\n        <input type=\"text\" name=\"reg_no\" id=\"reg_no\" class=\"form-control\" placeholder=\"Enter registration number\" [(ngModel)]=\"student.reg_no\" required>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"class\"><i class=\"fas fa-chalkboard me-1\"></i>Class <span class=\"required\">*</span></label>\r\n        <select name=\"class\" id=\"class\" class=\"form-control\" [(ngModel)]=\"student.classId\" required>\r\n          <option value=\"\" disabled selected>Select a class</option>\r\n          <option *ngFor=\"let class of classes\" [value]=\"class._id\">\r\n            {{ class.className }} - {{ class.department }} ({{ class.section }})\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"school_last_attendance\"><i class=\"fas fa-school me-1\"></i>School Last Attended <span class=\"optional\">(Optional)</span></label>\r\n        <input type=\"text\" name=\"school_last_attendance\" id=\"school_last_attendance\" class=\"form-control\" placeholder=\"Enter previous school name\" [(ngModel)]=\"student.school_last_attendance\">\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"year_of_passing\"><i class=\"fas fa-calendar-check me-1\"></i>Year of Passing <span class=\"optional\">(Optional)</span></label>\r\n        <input type=\"number\" name=\"year_of_passing\" id=\"year_of_passing\" class=\"form-control\" placeholder=\"Enter year of passing\" [(ngModel)]=\"student.year_of_passing\" min=\"1990\" max=\"2030\">\r\n      </div>\r\n    </div>\r\n\r\n  </div>\r\n\r\n  <!-- Action Buttons -->\r\n  <div class=\"form-actions\">\r\n    <button type=\"button\" class=\"btn btn-back me-3\" (click)=\"resetForm()\">\r\n      <i class=\"fas fa-undo me-1\"></i>Reset Form\r\n    </button>\r\n    <button type=\"button\" class=\"btn btn-save\" (click)=\"onSave()\">\r\n      <i class=\"fas fa-save me-1\"></i>Save Student\r\n    </button>\r\n  </div>\r\n</div>\r\n"], "mappings": ";;;;;;;;IC0EUA,EAAA,CAAAC,cAAA,iBAA0D;IACxDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAF6BH,EAAA,CAAAI,UAAA,UAAAC,QAAA,CAAAC,GAAA,CAAmB;IACvDN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,QAAA,CAAAI,SAAA,SAAAJ,QAAA,CAAAK,UAAA,QAAAL,QAAA,CAAAM,OAAA,OACF;;;ADjEV,OAAM,MAAOC,mBAAmB;EAG9BC,YAAoBC,WAAwB,EAAUC,MAAa,EAAWC,YAA2B;IAArF,KAAAF,WAAW,GAAXA,WAAW;IAAuB,KAAAC,MAAM,GAANA,MAAM;IAAkB,KAAAC,YAAY,GAAZA,YAAY;IAF1F,KAAAC,OAAO,GAAU,EAAE,CAAC,CAAC;IAoBrB;IACA,KAAAC,OAAO,GAAG;MACRC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,EAAE;MACVC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE,EAAE;MAClBC,UAAU,EAAE,EAAE;MACdC,OAAO,EAAE,EAAE;MACXC,aAAa,EAAE,EAAE;MACjBC,sBAAsB,EAAE,EAAE;MAC1BC,eAAe,EAAE,EAAE;MACnBrB,SAAS,EAAC,EAAE;MACZE,OAAO,EAAC,EAAE;MACVD,UAAU,EAAC,EAAE;MACbqB,OAAO,EAAE;KACV;IAnCH,IAAI,CAACC,UAAU,EAAE;EACf;EACD;EACAA,UAAUA,CAAA;IACT,IAAI,CAAChB,YAAY,CAACgB,UAAU,EAAE,CAACC,SAAS,CACrCC,QAAa,IAAI;MAChB,IAAIA,QAAQ,CAACC,OAAO,EAAE;QACpB,IAAI,CAAClB,OAAO,GAAGiB,QAAQ,CAACjB,OAAO;QAC/BmB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAG,IAAI,CAACpB,OAAO,CAAC;;IAGhD,CAAC,EACAqB,KAAK,IAAI;MACRF,OAAO,CAACE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD,CAAC,CACF;EACH;EAsBE;EACAC,YAAYA,CAAA;IACV,MAAMC,cAAc,GAAG,CACrB;MAAEC,KAAK,EAAE,IAAI,CAACvB,OAAO,CAACC,IAAI;MAAEA,IAAI,EAAE;IAAc,CAAE,EAClD;MAAEsB,KAAK,EAAE,IAAI,CAACvB,OAAO,CAACM,WAAW;MAAEL,IAAI,EAAE;IAAgB,CAAE,EAC3D;MAAEsB,KAAK,EAAE,IAAI,CAACvB,OAAO,CAACO,cAAc;MAAEN,IAAI,EAAE;IAAmB,CAAE,EACjE;MAAEsB,KAAK,EAAE,IAAI,CAACvB,OAAO,CAACU,aAAa;MAAET,IAAI,EAAE;IAAe,CAAE,EAC5D;MAAEsB,KAAK,EAAE,IAAI,CAACvB,OAAO,CAACE,KAAK;MAAED,IAAI,EAAE;IAAO,CAAE,EAC5C;MAAEsB,KAAK,EAAE,IAAI,CAACvB,OAAO,CAACG,QAAQ;MAAEF,IAAI,EAAE;IAAU,CAAE,EAClD;MAAEsB,KAAK,EAAE,IAAI,CAACvB,OAAO,CAACI,MAAM;MAAEH,IAAI,EAAE;IAAa,CAAE,EACnD;MAAEsB,KAAK,EAAE,IAAI,CAACvB,OAAO,CAACK,MAAM;MAAEJ,IAAI,EAAE;IAAqB,CAAE,EAC3D;MAAEsB,KAAK,EAAE,IAAI,CAACvB,OAAO,CAACa,OAAO;MAAEZ,IAAI,EAAE;IAAO,CAAE,CAC/C;IAED,KAAK,MAAMsB,KAAK,IAAID,cAAc,EAAE;MAClC,IAAI,CAACC,KAAK,CAACA,KAAK,IAAIA,KAAK,CAACA,KAAK,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;QAC7CC,KAAK,CAAC,sBAAsBF,KAAK,CAACtB,IAAI,SAAS,CAAC;QAChD,OAAO,KAAK;;;IAIhB;IACA,MAAMyB,UAAU,GAAG,4BAA4B;IAC/C,IAAI,CAACA,UAAU,CAACC,IAAI,CAAC,IAAI,CAAC3B,OAAO,CAACE,KAAK,CAAC,EAAE;MACxCuB,KAAK,CAAC,qCAAqC,CAAC;MAC5C,OAAO,KAAK;;IAGd;IACA,MAAMG,UAAU,GAAG,gBAAgB;IACnC,IAAI,CAACA,UAAU,CAACD,IAAI,CAAC,IAAI,CAAC3B,OAAO,CAACO,cAAc,CAACsB,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,EAAE;MACrEJ,KAAK,CAAC,+DAA+D,CAAC;MACtE,OAAO,KAAK;;IAGd,OAAO,IAAI;EACb;EAEA;EACAK,MAAMA,CAAA;IACJ;IACA,IAAI,CAAC,IAAI,CAACT,YAAY,EAAE,EAAE;MACxB;;IAGF,MAAMU,WAAW,GAAQ;MACvB9B,IAAI,EAAE,IAAI,CAACD,OAAO,CAACC,IAAI;MACvBC,KAAK,EAAE,IAAI,CAACF,OAAO,CAACE,KAAK;MACzBC,QAAQ,EAAE,IAAI,CAACH,OAAO,CAACG,QAAQ;MAC/B6B,MAAM,EAAE,IAAI,CAAChC,OAAO,CAACI,MAAM;MAC3B6B,KAAK,EAAE,IAAI,CAACjC,OAAO,CAACK,MAAM;MAC1BC,WAAW,EAAE,IAAI,CAACN,OAAO,CAACM,WAAW;MACrCC,cAAc,EAAE,IAAI,CAACP,OAAO,CAACO,cAAc;MAC3C2B,OAAO,EAAE,IAAI,CAAClC,OAAO,CAACQ,UAAU;MAChCC,OAAO,EAAE,IAAI,CAACT,OAAO,CAACS,OAAO;MAC7BC,aAAa,EAAE,IAAI,CAACV,OAAO,CAACU,aAAa;MACzCC,sBAAsB,EAAE,IAAI,CAACX,OAAO,CAACW,sBAAsB;MAC3DC,eAAe,EAAE,IAAI,CAACZ,OAAO,CAACY,eAAe;MAC7CuB,IAAI,EAAE,SAAS;MACf5C,SAAS,EAAE,IAAI,CAACS,OAAO,CAACT,SAAS;MACjCE,OAAO,EAAE,IAAI,CAACO,OAAO,CAACP,OAAO;MAC7BD,UAAU,EAAE,IAAI,CAACQ,OAAO,CAACR,UAAU;MACnCqB,OAAO,EAAE,IAAI,CAACb,OAAO,CAACa;KACvB;IAED;IACA,IAAI,CAACjB,WAAW,CAACwC,QAAQ,CAACL,WAAkB,CAAC,CAAChB,SAAS,CAAC;MACtDsB,IAAI,EAAGrB,QAAQ,IAAI;QACjB;QACAE,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEH,QAAQ,CAAC;QACjDS,KAAK,CAAC,kCAAkC,CAAC;QACzC,IAAI,CAAC5B,MAAM,CAACyC,QAAQ,CAAC,CAAC,wCAAwC,CAAC,CAAC;QAChE;QACA,IAAI,CAACC,SAAS,EAAE;MAClB,CAAC;MACDnB,KAAK,EAAGA,KAAwB,IAAI;QAClC;QACAF,OAAO,CAACE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDK,KAAK,CAAC,UAAUL,KAAK,CAACA,KAAK,CAACoB,OAAO,IAAI,4BAA4B,EAAE,CAAC;MACxE;KACD,CAAC;EACJ;EAEAD,SAASA,CAAA;IACP,IAAI,CAACvC,OAAO,GAAG;MACbC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,EAAE;MACVC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE,EAAE;MAClBC,UAAU,EAAE,EAAE;MACdC,OAAO,EAAE,EAAE;MACXC,aAAa,EAAE,EAAE;MACjBC,sBAAsB,EAAE,EAAE;MAC1BC,eAAe,EAAE,EAAE;MACnBrB,SAAS,EAAC,EAAE;MACZE,OAAO,EAAC,EAAE;MACVD,UAAU,EAAC,EAAE;MACbqB,OAAO,EAAE;KACV;EACH;EAAC,QAAA4B,CAAA,G;qBAhJU/C,mBAAmB,EAAAZ,EAAA,CAAA4D,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA9D,EAAA,CAAA4D,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAhE,EAAA,CAAA4D,iBAAA,CAAAK,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAnBvD,mBAAmB;IAAAwD,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCXhC1E,EAAA,CAAAC,cAAA,aAA4B;QAEpBD,EAAA,CAAA4E,SAAA,WAAqC;QAAA5E,EAAA,CAAAE,MAAA,sBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAG/DH,EAAA,CAAAC,cAAA,aAAuB;QAGOD,EAAA,CAAA4E,SAAA,WAAgC;QAAA5E,EAAA,CAAAE,MAAA,2BAAoB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAEnFH,EAAA,CAAAC,cAAA,cAAwB;QACJD,EAAA,CAAA4E,SAAA,YAAgC;QAAA5E,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAC,cAAA,gBAAuB;QAAAD,EAAA,CAAAE,MAAA,SAAC;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAC9FH,EAAA,CAAAC,cAAA,iBAAgI;QAApCD,EAAA,CAAA6E,UAAA,2BAAAC,6DAAAC,MAAA;UAAA,OAAAJ,GAAA,CAAAzD,OAAA,CAAAC,IAAA,GAAA4D,MAAA;QAAA,EAA0B;QAAtH/E,EAAA,CAAAG,YAAA,EAAgI;QAGlIH,EAAA,CAAAC,cAAA,cAAwB;QACGD,EAAA,CAAA4E,SAAA,aAAgC;QAAA5E,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAC,cAAA,gBAAuB;QAAAD,EAAA,CAAAE,MAAA,SAAC;QAAAF,EAAA,CAAAG,YAAA,EAAO;QACtGH,EAAA,CAAAC,cAAA,iBAA8J;QAA3CD,EAAA,CAAA6E,UAAA,2BAAAG,6DAAAD,MAAA;UAAA,OAAAJ,GAAA,CAAAzD,OAAA,CAAAM,WAAA,GAAAuD,MAAA;QAAA,EAAiC;QAApJ/E,EAAA,CAAAG,YAAA,EAA8J;QAGhKH,EAAA,CAAAC,cAAA,cAAwB;QACKD,EAAA,CAAA4E,SAAA,aAAoC;QAAA5E,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAC,cAAA,gBAAuB;QAAAD,EAAA,CAAAE,MAAA,SAAC;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAC5GH,EAAA,CAAAC,cAAA,iBAA6H;QAA7CD,EAAA,CAAA6E,UAAA,2BAAAI,6DAAAF,MAAA;UAAA,OAAAJ,GAAA,CAAAzD,OAAA,CAAAU,aAAA,GAAAmD,MAAA;QAAA,EAAmC;QAAnH/E,EAAA,CAAAG,YAAA,EAA6H;QAG/HH,EAAA,CAAAC,cAAA,cAAwB;QACDD,EAAA,CAAA4E,SAAA,aAA0C;QAAA5E,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC9EH,EAAA,CAAAC,cAAA,oBAAuI;QAA9BD,EAAA,CAAA6E,UAAA,2BAAAK,gEAAAH,MAAA;UAAA,OAAAJ,GAAA,CAAAzD,OAAA,CAAAS,OAAA,GAAAoD,MAAA;QAAA,EAA6B;QAAC/E,EAAA,CAAAG,YAAA,EAAW;QAKtJH,EAAA,CAAAC,cAAA,cAA0B;QACED,EAAA,CAAA4E,SAAA,aAAiC;QAAA5E,EAAA,CAAAE,MAAA,2BAAmB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAEnFH,EAAA,CAAAC,cAAA,cAAwB;QACED,EAAA,CAAA4E,SAAA,aAAsC;QAAA5E,EAAA,CAAAE,MAAA,8BAAsB;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC5FH,EAAA,CAAAC,cAAA,iBAAsJ;QAAjCD,EAAA,CAAA6E,UAAA,2BAAAM,6DAAAJ,MAAA;UAAA,OAAAJ,GAAA,CAAAzD,OAAA,CAAAQ,UAAA,GAAAqD,MAAA;QAAA,EAAgC;QAArJ/E,EAAA,CAAAG,YAAA,EAAsJ;QAGxJH,EAAA,CAAAC,cAAA,cAAwB;QACMD,EAAA,CAAA4E,SAAA,aAAiC;QAAA5E,EAAA,CAAAE,MAAA,gCAAwB;QAAAF,EAAA,CAAAC,cAAA,gBAAuB;QAAAD,EAAA,CAAAE,MAAA,SAAC;QAAAF,EAAA,CAAAG,YAAA,EAAO;QACpHH,EAAA,CAAAC,cAAA,iBAA0K;QAA9CD,EAAA,CAAA6E,UAAA,2BAAAO,6DAAAL,MAAA;UAAA,OAAAJ,GAAA,CAAAzD,OAAA,CAAAO,cAAA,GAAAsD,MAAA;QAAA,EAAoC;QAAhK/E,EAAA,CAAAG,YAAA,EAA0K;QAG5KH,EAAA,CAAAC,cAAA,cAAwB;QACHD,EAAA,CAAA4E,SAAA,aAAoC;QAAA5E,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAC,cAAA,gBAAuB;QAAAD,EAAA,CAAAE,MAAA,SAAC;QAAAF,EAAA,CAAAG,YAAA,EAAO;QACpGH,EAAA,CAAAC,cAAA,iBAAwI;QAArCD,EAAA,CAAA6E,UAAA,2BAAAQ,6DAAAN,MAAA;UAAA,OAAAJ,GAAA,CAAAzD,OAAA,CAAAE,KAAA,GAAA2D,MAAA;QAAA,EAA2B;QAA9H/E,EAAA,CAAAG,YAAA,EAAwI;QAG1IH,EAAA,CAAAC,cAAA,cAAwB;QACAD,EAAA,CAAA4E,SAAA,aAAgC;QAAA5E,EAAA,CAAAE,MAAA,iBAAS;QAAAF,EAAA,CAAAC,cAAA,gBAAuB;QAAAD,EAAA,CAAAE,MAAA,SAAC;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAC9FH,EAAA,CAAAC,cAAA,iBAA+I;QAAxCD,EAAA,CAAA6E,UAAA,2BAAAS,6DAAAP,MAAA;UAAA,OAAAJ,GAAA,CAAAzD,OAAA,CAAAG,QAAA,GAAA0D,MAAA;QAAA,EAA8B;QAArI/E,EAAA,CAAAG,YAAA,EAA+I;QAKnJH,EAAA,CAAAC,cAAA,cAA0B;QACED,EAAA,CAAA4E,SAAA,aAA0C;QAAA5E,EAAA,CAAAE,MAAA,4BAAoB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAE7FH,EAAA,CAAAC,cAAA,cAAwB;QACFD,EAAA,CAAA4E,SAAA,aAAmC;QAAA5E,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAC,cAAA,gBAAuB;QAAAD,EAAA,CAAAE,MAAA,SAAC;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAClGH,EAAA,CAAAC,cAAA,iBAAwI;QAAtCD,EAAA,CAAA6E,UAAA,2BAAAU,6DAAAR,MAAA;UAAA,OAAAJ,GAAA,CAAAzD,OAAA,CAAAI,MAAA,GAAAyD,MAAA;QAAA,EAA4B;QAA9H/E,EAAA,CAAAG,YAAA,EAAwI;QAG1IH,EAAA,CAAAC,cAAA,cAAwB;QACFD,EAAA,CAAA4E,SAAA,aAAuC;QAAA5E,EAAA,CAAAE,MAAA,4BAAoB;QAAAF,EAAA,CAAAC,cAAA,gBAAuB;QAAAD,EAAA,CAAAE,MAAA,SAAC;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAC9GH,EAAA,CAAAC,cAAA,iBAAgJ;QAAtCD,EAAA,CAAA6E,UAAA,2BAAAW,6DAAAT,MAAA;UAAA,OAAAJ,GAAA,CAAAzD,OAAA,CAAAK,MAAA,GAAAwD,MAAA;QAAA,EAA4B;QAAtI/E,EAAA,CAAAG,YAAA,EAAgJ;QAGlJH,EAAA,CAAAC,cAAA,cAAwB;QACHD,EAAA,CAAA4E,SAAA,aAAsC;QAAA5E,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAC,cAAA,gBAAuB;QAAAD,EAAA,CAAAE,MAAA,SAAC;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAC9FH,EAAA,CAAAC,cAAA,kBAA4F;QAAvCD,EAAA,CAAA6E,UAAA,2BAAAY,8DAAAV,MAAA;UAAA,OAAAJ,GAAA,CAAAzD,OAAA,CAAAa,OAAA,GAAAgD,MAAA;QAAA,EAA6B;QAChF/E,EAAA,CAAAC,cAAA,kBAAmC;QAAAD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAC1DH,EAAA,CAAA0F,UAAA,KAAAC,sCAAA,qBAES;QACX3F,EAAA,CAAAG,YAAA,EAAS;QAGXH,EAAA,CAAAC,cAAA,cAAwB;QACcD,EAAA,CAAA4E,SAAA,aAAkC;QAAA5E,EAAA,CAAAE,MAAA,6BAAqB;QAAAF,EAAA,CAAAC,cAAA,gBAAuB;QAAAD,EAAA,CAAAE,MAAA,kBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAO;QACnIH,EAAA,CAAAC,cAAA,kBAAwL;QAA7CD,EAAA,CAAA6E,UAAA,2BAAAe,8DAAAb,MAAA;UAAA,OAAAJ,GAAA,CAAAzD,OAAA,CAAAW,sBAAA,GAAAkD,MAAA;QAAA,EAA4C;QAAvL/E,EAAA,CAAAG,YAAA,EAAwL;QAG1LH,EAAA,CAAAC,cAAA,eAAwB;QACOD,EAAA,CAAA4E,SAAA,cAA0C;QAAA5E,EAAA,CAAAE,MAAA,yBAAgB;QAAAF,EAAA,CAAAC,cAAA,iBAAuB;QAAAD,EAAA,CAAAE,MAAA,mBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAC/HH,EAAA,CAAAC,cAAA,kBAAsL;QAA5DD,EAAA,CAAA6E,UAAA,2BAAAgB,8DAAAd,MAAA;UAAA,OAAAJ,GAAA,CAAAzD,OAAA,CAAAY,eAAA,GAAAiD,MAAA;QAAA,EAAqC;QAA/J/E,EAAA,CAAAG,YAAA,EAAsL;QAO5LH,EAAA,CAAAC,cAAA,gBAA0B;QACwBD,EAAA,CAAA6E,UAAA,mBAAAiB,uDAAA;UAAA,OAASnB,GAAA,CAAAlB,SAAA,EAAW;QAAA,EAAC;QACnEzD,EAAA,CAAA4E,SAAA,cAAgC;QAAA5E,EAAA,CAAAE,MAAA,oBAClC;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,mBAA8D;QAAnBD,EAAA,CAAA6E,UAAA,mBAAAkB,uDAAA;UAAA,OAASpB,GAAA,CAAA3B,MAAA,EAAQ;QAAA,EAAC;QAC3DhD,EAAA,CAAA4E,SAAA,cAAgC;QAAA5E,EAAA,CAAAE,MAAA,sBAClC;QAAAF,EAAA,CAAAG,YAAA,EAAS;;;QAxFuFH,EAAA,CAAAO,SAAA,IAA0B;QAA1BP,EAAA,CAAAI,UAAA,YAAAuE,GAAA,CAAAzD,OAAA,CAAAC,IAAA,CAA0B;QAKHnB,EAAA,CAAAO,SAAA,GAAiC;QAAjCP,EAAA,CAAAI,UAAA,YAAAuE,GAAA,CAAAzD,OAAA,CAAAM,WAAA,CAAiC;QAKpExB,EAAA,CAAAO,SAAA,GAAmC;QAAnCP,EAAA,CAAAI,UAAA,YAAAuE,GAAA,CAAAzD,OAAA,CAAAU,aAAA,CAAmC;QAKV5B,EAAA,CAAAO,SAAA,GAA6B;QAA7BP,EAAA,CAAAI,UAAA,YAAAuE,GAAA,CAAAzD,OAAA,CAAAS,OAAA,CAA6B;QAUjB3B,EAAA,CAAAO,SAAA,GAAgC;QAAhCP,EAAA,CAAAI,UAAA,YAAAuE,GAAA,CAAAzD,OAAA,CAAAQ,UAAA,CAAgC;QAKzB1B,EAAA,CAAAO,SAAA,GAAoC;QAApCP,EAAA,CAAAI,UAAA,YAAAuE,GAAA,CAAAzD,OAAA,CAAAO,cAAA,CAAoC;QAK7DzB,EAAA,CAAAO,SAAA,GAA2B;QAA3BP,EAAA,CAAAI,UAAA,YAAAuE,GAAA,CAAAzD,OAAA,CAAAE,KAAA,CAA2B;QAKvBpB,EAAA,CAAAO,SAAA,GAA8B;QAA9BP,EAAA,CAAAI,UAAA,YAAAuE,GAAA,CAAAzD,OAAA,CAAAG,QAAA,CAA8B;QAUnCrB,EAAA,CAAAO,SAAA,IAA4B;QAA5BP,EAAA,CAAAI,UAAA,YAAAuE,GAAA,CAAAzD,OAAA,CAAAI,MAAA,CAA4B;QAKpBtB,EAAA,CAAAO,SAAA,GAA4B;QAA5BP,EAAA,CAAAI,UAAA,YAAAuE,GAAA,CAAAzD,OAAA,CAAAK,MAAA,CAA4B;QAKjFvB,EAAA,CAAAO,SAAA,GAA6B;QAA7BP,EAAA,CAAAI,UAAA,YAAAuE,GAAA,CAAAzD,OAAA,CAAAa,OAAA,CAA6B;QAEtD/B,EAAA,CAAAO,SAAA,GAAU;QAAVP,EAAA,CAAAI,UAAA,YAAAuE,GAAA,CAAA1D,OAAA,CAAU;QAQqGjB,EAAA,CAAAO,SAAA,GAA4C;QAA5CP,EAAA,CAAAI,UAAA,YAAAuE,GAAA,CAAAzD,OAAA,CAAAW,sBAAA,CAA4C;QAK7D7B,EAAA,CAAAO,SAAA,GAAqC;QAArCP,EAAA,CAAAI,UAAA,YAAAuE,GAAA,CAAAzD,OAAA,CAAAY,eAAA,CAAqC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}