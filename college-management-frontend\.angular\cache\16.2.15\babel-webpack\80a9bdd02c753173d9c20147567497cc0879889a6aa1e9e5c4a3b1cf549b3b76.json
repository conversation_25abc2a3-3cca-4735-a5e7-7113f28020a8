{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/user.service\";\nimport * as i2 from \"../../../services/dashboard.service\";\nimport * as i3 from \"../../../services/timetable.service\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/card\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/progress-spinner\";\nimport * as i11 from \"@angular/material/tooltip\";\nfunction StudentTimetableComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading your timetable...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StudentTimetableComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Error Loading Timetable\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function StudentTimetableComponent_div_11_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.refreshData());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction StudentTimetableComponent_div_12_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r7 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"current-day\", day_r7.toLowerCase() === ctx_r5.getCurrentDay());\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", day_r7, \" \");\n  }\n}\nfunction StudentTimetableComponent_div_12_div_27_div_3_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"div\", 48);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 49);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 50);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const day_r10 = i0.ɵɵnextContext(2).$implicit;\n    const slot_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(slot_r8[day_r10.toLowerCase()].teacher);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(slot_r8[day_r10.toLowerCase()].room);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(slot_r8[day_r10.toLowerCase()].type.toLowerCase());\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", slot_r8[day_r10.toLowerCase()].type, \" \");\n  }\n}\nfunction StudentTimetableComponent_div_12_div_27_div_3_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"div\", 49);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const day_r10 = i0.ɵɵnextContext(2).$implicit;\n    const slot_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(slot_r8[day_r10.toLowerCase()].room);\n  }\n}\nfunction StudentTimetableComponent_div_12_div_27_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"div\", 44);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, StudentTimetableComponent_div_12_div_27_div_3_div_1_div_3_Template, 7, 5, \"div\", 45);\n    i0.ɵɵtemplate(4, StudentTimetableComponent_div_12_div_27_div_3_div_1_div_4_Template, 3, 1, \"div\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r10 = i0.ɵɵnextContext().$implicit;\n    const slot_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"break\", slot_r8[day_r10.toLowerCase()].isBreak);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", slot_r8[day_r10.toLowerCase()].subject, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !slot_r8[day_r10.toLowerCase()].isBreak);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", slot_r8[day_r10.toLowerCase()].isBreak);\n  }\n}\nfunction StudentTimetableComponent_div_12_div_27_div_3_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"span\");\n    i0.ɵɵtext(2, \"Free\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StudentTimetableComponent_div_12_div_27_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵtemplate(1, StudentTimetableComponent_div_12_div_27_div_3_div_1_Template, 5, 5, \"div\", 41);\n    i0.ɵɵtemplate(2, StudentTimetableComponent_div_12_div_27_div_3_div_2_Template, 3, 0, \"div\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r10 = ctx.$implicit;\n    const slot_r8 = i0.ɵɵnextContext().$implicit;\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"current-day\", day_r10.toLowerCase() === ctx_r9.getCurrentDay())(\"has-class\", slot_r8[day_r10.toLowerCase()])(\"break-time\", slot_r8[day_r10.toLowerCase()] == null ? null : slot_r8[day_r10.toLowerCase()].isBreak);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", slot_r8[day_r10.toLowerCase()]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !slot_r8[day_r10.toLowerCase()]);\n  }\n}\nfunction StudentTimetableComponent_div_12_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 38);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, StudentTimetableComponent_div_12_div_27_div_3_Template, 3, 8, \"div\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const slot_r8 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"current-time\", ctx_r6.isCurrentTimeSlot(slot_r8.time));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(slot_r8.time);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.weekDays);\n  }\n}\nfunction StudentTimetableComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11)(2, \"mat-card\", 12)(3, \"mat-card-content\")(4, \"div\", 13)(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"today\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 14)(8, \"h3\");\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"p\");\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"date\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(14, \"div\", 15)(15, \"mat-card\", 16)(16, \"mat-card-header\")(17, \"mat-card-title\");\n    i0.ɵɵtext(18, \"Weekly Schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"mat-card-subtitle\");\n    i0.ɵɵtext(20, \"Your class timetable for this week\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"mat-card-content\")(22, \"div\", 17)(23, \"div\", 18)(24, \"div\", 19);\n    i0.ɵɵtext(25, \"Time\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, StudentTimetableComponent_div_12_div_26_Template, 2, 3, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(27, StudentTimetableComponent_div_12_div_27_Template, 4, 4, \"div\", 21);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(28, \"div\", 22)(29, \"mat-card\", 23)(30, \"mat-card-header\")(31, \"mat-card-title\");\n    i0.ɵɵtext(32, \"Legend\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"mat-card-content\")(34, \"div\", 24)(35, \"div\", 25);\n    i0.ɵɵelement(36, \"div\", 26);\n    i0.ɵɵelementStart(37, \"span\");\n    i0.ɵɵtext(38, \"Lecture\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"div\", 25);\n    i0.ɵɵelement(40, \"div\", 27);\n    i0.ɵɵelementStart(41, \"span\");\n    i0.ɵɵtext(42, \"Lab\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"div\", 25);\n    i0.ɵɵelement(44, \"div\", 28);\n    i0.ɵɵelementStart(45, \"span\");\n    i0.ɵɵtext(46, \"Tutorial\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"div\", 25);\n    i0.ɵɵelement(48, \"div\", 29);\n    i0.ɵɵelementStart(49, \"span\");\n    i0.ɵɵtext(50, \"Break\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(51, \"div\", 25);\n    i0.ɵɵelement(52, \"div\", 30);\n    i0.ɵɵelementStart(53, \"span\");\n    i0.ɵɵtext(54, \"Free Period\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(55, \"div\", 31)(56, \"h3\");\n    i0.ɵɵtext(57, \"Quick Actions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"div\", 32)(59, \"button\", 33)(60, \"mat-icon\");\n    i0.ɵɵtext(61, \"subject\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(62, \" View Subjects \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(63, \"button\", 34)(64, \"mat-icon\");\n    i0.ɵɵtext(65, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(66, \" View Teachers \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(67, \"button\", 35)(68, \"mat-icon\");\n    i0.ɵɵtext(69, \"fact_check\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(70, \" View Attendance \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\"Today is \", i0.ɵɵpipeBind1(10, 4, ctx_r2.getCurrentDay()), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(13, 6, ctx_r2.getCurrentDate(), \"fullDate\"));\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.weekDays);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.timetableData);\n  }\n}\nexport class StudentTimetableComponent {\n  constructor(userService, dashboardService, timetableService, snackBar) {\n    this.userService = userService;\n    this.dashboardService = dashboardService;\n    this.timetableService = timetableService;\n    this.snackBar = snackBar;\n    this.loading = false;\n    this.error = null;\n    // Sample timetable data - in a real app, this would come from the backend\n    this.timetableData = [];\n    this.timeSlots = ['9:00 AM - 10:00 AM', '10:00 AM - 11:00 AM', '11:00 AM - 12:00 PM', '12:00 PM - 1:00 PM', '1:00 PM - 2:00 PM', '2:00 PM - 3:00 PM', '3:00 PM - 4:00 PM', '4:00 PM - 5:00 PM'];\n    this.weekDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];\n  }\n  ngOnInit() {\n    this.currentUser = this.userService.getUserFromLocalStorage()?.user;\n    this.loadStudentTimetable();\n  }\n  loadStudentTimetable() {\n    if (!this.currentUser) return;\n    this.loading = true;\n    this.error = null;\n    // Load both dashboard data and real timetable\n    this.dashboardService.getStudentDashboard(this.currentUser._id).subscribe({\n      next: response => {\n        if (response.success) {\n          this.studentDashboard = response.dashboard;\n          this.loadRealTimetable();\n        } else {\n          this.error = 'Failed to load student dashboard';\n          this.loading = false;\n        }\n      },\n      error: error => {\n        console.error('Error loading student dashboard:', error);\n        this.error = 'Error loading dashboard data';\n        this.loading = false;\n      }\n    });\n  }\n  loadRealTimetable() {\n    this.timetableService.getTimetableByStudent(this.currentUser._id, '2024-2025').subscribe({\n      next: response => {\n        this.loading = false;\n        if (response.success) {\n          this.generateTimetableFromData(response.timetable);\n        } else {\n          // Fallback to sample timetable if no real data\n          this.generateSampleTimetable();\n        }\n      },\n      error: error => {\n        console.error('Error loading real timetable:', error);\n        this.loading = false;\n        // Fallback to sample timetable\n        this.generateSampleTimetable();\n        this.snackBar.open('Using sample timetable data', 'Close', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  generateTimetableFromData(timetableEntries) {\n    this.timetableData = this.timeSlots.map(timeSlot => {\n      const slot = {\n        time: timeSlot\n      };\n      this.weekDays.forEach(day => {\n        // Find matching timetable entry for this day and time\n        const entry = timetableEntries.find(entry => entry.dayOfWeek.toLowerCase() === day.toLowerCase() && entry.timeSlot.startTime === timeSlot.split(' - ')[0]);\n        if (entry) {\n          slot[day.toLowerCase()] = {\n            subject: entry.subject.subjectName,\n            teacher: entry.teacher.name,\n            room: entry.room || 'TBA',\n            type: 'Lecture',\n            code: entry.subject.code,\n            program: entry.program.name,\n            semester: entry.semester\n          };\n        } else {\n          slot[day.toLowerCase()] = null;\n        }\n      });\n      return slot;\n    });\n  }\n  generateSampleTimetable() {\n    // Generate sample timetable based on subjects\n    if (this.studentDashboard?.subjectAttendance) {\n      const subjects = this.studentDashboard.subjectAttendance;\n      this.timetableData = this.timeSlots.map((timeSlot, timeIndex) => {\n        const slot = {\n          time: timeSlot\n        };\n        this.weekDays.forEach((day, dayIndex) => {\n          // Simple logic to assign subjects to time slots\n          const subjectIndex = (timeIndex + dayIndex) % subjects.length;\n          if (subjects[subjectIndex] && timeIndex < 6) {\n            // Skip lunch time and last slot sometimes\n            slot[day.toLowerCase()] = {\n              subject: subjects[subjectIndex].subjectName,\n              teacher: 'Teacher ' + (subjectIndex + 1),\n              room: 'Room ' + (101 + subjectIndex),\n              type: timeIndex % 3 === 0 ? 'Lecture' : timeIndex % 3 === 1 ? 'Lab' : 'Tutorial'\n            };\n          } else if (timeIndex === 4) {\n            // Lunch break\n            slot[day.toLowerCase()] = {\n              subject: 'Lunch Break',\n              teacher: '',\n              room: 'Cafeteria',\n              type: 'Break',\n              isBreak: true\n            };\n          } else {\n            slot[day.toLowerCase()] = null;\n          }\n        });\n        return slot;\n      });\n    }\n  }\n  refreshData() {\n    this.loadStudentTimetable();\n  }\n  getCurrentDay() {\n    const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];\n    return days[new Date().getDay()];\n  }\n  getCurrentDate() {\n    return new Date();\n  }\n  isCurrentTimeSlot(timeSlot) {\n    const now = new Date();\n    const currentHour = now.getHours();\n    const slotHour = parseInt(timeSlot.split(':')[0]);\n    // Simple check if current hour matches slot hour\n    return currentHour === slotHour || slotHour === 12 && currentHour === 0;\n  }\n  static #_ = this.ɵfac = function StudentTimetableComponent_Factory(t) {\n    return new (t || StudentTimetableComponent)(i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i2.DashboardService), i0.ɵɵdirectiveInject(i3.TimetableService), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: StudentTimetableComponent,\n    selectors: [[\"app-student-timetable\"]],\n    decls: 13,\n    vars: 3,\n    consts: [[1, \"student-timetable-container\"], [1, \"page-header\"], [1, \"header-content\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Refresh Data\", 3, \"click\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"timetable-content\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"error-container\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"timetable-content\"], [1, \"current-day-info\"], [1, \"current-day-card\"], [1, \"current-day-content\"], [1, \"day-info\"], [1, \"timetable-wrapper\"], [1, \"timetable-card\"], [1, \"timetable-grid\"], [1, \"timetable-header\"], [1, \"time-header\"], [\"class\", \"day-header\", 3, \"current-day\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"timetable-row\", 3, \"current-time\", 4, \"ngFor\", \"ngForOf\"], [1, \"legend-section\"], [1, \"legend-card\"], [1, \"legend-items\"], [1, \"legend-item\"], [1, \"legend-color\", \"lecture\"], [1, \"legend-color\", \"lab\"], [1, \"legend-color\", \"tutorial\"], [1, \"legend-color\", \"break\"], [1, \"legend-color\", \"free\"], [1, \"quick-actions\"], [1, \"action-buttons\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/dashboard/student/subjects\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"routerLink\", \"/dashboard/student/teachers\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/dashboard/student/attendance\"], [1, \"day-header\"], [1, \"timetable-row\"], [1, \"time-slot\"], [\"class\", \"schedule-cell\", 3, \"current-day\", \"has-class\", \"break-time\", 4, \"ngFor\", \"ngForOf\"], [1, \"schedule-cell\"], [\"class\", \"class-info\", 4, \"ngIf\"], [\"class\", \"free-period\", 4, \"ngIf\"], [1, \"class-info\"], [1, \"subject-name\"], [\"class\", \"class-details\", 4, \"ngIf\"], [\"class\", \"break-details\", 4, \"ngIf\"], [1, \"class-details\"], [1, \"teacher\"], [1, \"room\"], [1, \"type\"], [1, \"break-details\"], [1, \"free-period\"]],\n    template: function StudentTimetableComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\");\n        i0.ɵɵtext(4, \"My Timetable\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"p\");\n        i0.ɵɵtext(6, \"View your weekly class schedule\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(7, \"button\", 3);\n        i0.ɵɵlistener(\"click\", function StudentTimetableComponent_Template_button_click_7_listener() {\n          return ctx.refreshData();\n        });\n        i0.ɵɵelementStart(8, \"mat-icon\");\n        i0.ɵɵtext(9, \"refresh\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(10, StudentTimetableComponent_div_10_Template, 4, 0, \"div\", 4);\n        i0.ɵɵtemplate(11, StudentTimetableComponent_div_11_Template, 11, 1, \"div\", 5);\n        i0.ɵɵtemplate(12, StudentTimetableComponent_div_12_Template, 71, 9, \"div\", 6);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.studentDashboard && !ctx.loading && !ctx.error);\n      }\n    },\n    dependencies: [i5.NgForOf, i5.NgIf, i6.RouterLink, i7.MatButton, i7.MatIconButton, i8.MatCard, i8.MatCardContent, i8.MatCardHeader, i8.MatCardSubtitle, i8.MatCardTitle, i9.MatIcon, i10.MatProgressSpinner, i11.MatTooltip, i5.TitleCasePipe, i5.DatePipe],\n    styles: [\".student-timetable-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  max-width: 1400px;\\n  margin: 0 auto;\\n}\\n\\n.page-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 30px;\\n  padding-bottom: 20px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.header-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #333;\\n  font-size: 2rem;\\n  font-weight: 500;\\n}\\n\\n.header-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 5px 0 0 0;\\n  color: #666;\\n  font-size: 1rem;\\n}\\n\\n.loading-container[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 60px 20px;\\n  text-align: center;\\n}\\n\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  color: #666;\\n}\\n\\n.error-container[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  width: 48px;\\n  height: 48px;\\n  color: #f44336;\\n  margin-bottom: 20px;\\n}\\n\\n.timetable-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 30px;\\n}\\n\\n.current-day-info[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.current-day-card[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n}\\n\\n.current-day-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n}\\n\\n.current-day-content[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  width: 32px;\\n  height: 32px;\\n}\\n\\n.day-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 5px 0;\\n  font-size: 1.3rem;\\n  font-weight: 500;\\n}\\n\\n.day-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  opacity: 0.9;\\n  font-size: 0.9rem;\\n}\\n\\n.timetable-wrapper[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n}\\n\\n.timetable-card[_ngcontent-%COMP%] {\\n  min-width: 800px;\\n}\\n\\n.timetable-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 150px repeat(5, 1fr);\\n  gap: 1px;\\n  background: #e0e0e0;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n\\n.timetable-header[_ngcontent-%COMP%] {\\n  display: contents;\\n}\\n\\n.time-header[_ngcontent-%COMP%], .day-header[_ngcontent-%COMP%] {\\n  background: #2196f3;\\n  color: white;\\n  padding: 15px 10px;\\n  font-weight: 600;\\n  text-align: center;\\n  font-size: 0.9rem;\\n}\\n\\n.day-header.current-day[_ngcontent-%COMP%] {\\n  background: #ff9800;\\n}\\n\\n.timetable-row[_ngcontent-%COMP%] {\\n  display: contents;\\n}\\n\\n.timetable-row.current-time[_ngcontent-%COMP%]   .time-slot[_ngcontent-%COMP%] {\\n  background: #fff3e0;\\n  border-left: 4px solid #ff9800;\\n}\\n\\n.time-slot[_ngcontent-%COMP%] {\\n  background: #f5f5f5;\\n  padding: 15px 10px;\\n  font-weight: 500;\\n  font-size: 0.8rem;\\n  text-align: center;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-right: 1px solid #e0e0e0;\\n}\\n\\n.schedule-cell[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 8px;\\n  min-height: 80px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n  transition: background-color 0.2s ease;\\n}\\n\\n.schedule-cell.current-day[_ngcontent-%COMP%] {\\n  background: #f8f9ff;\\n}\\n\\n.schedule-cell.has-class[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n}\\n\\n.schedule-cell.break-time[_ngcontent-%COMP%] {\\n  background: #fff8e1;\\n}\\n\\n.schedule-cell[_ngcontent-%COMP%]:hover {\\n  background: #f0f0f0;\\n}\\n\\n.class-info[_ngcontent-%COMP%] {\\n  width: 100%;\\n  text-align: center;\\n}\\n\\n.subject-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 0.9rem;\\n  color: #333;\\n  margin-bottom: 8px;\\n  line-height: 1.2;\\n}\\n\\n.subject-name.break[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n  font-size: 0.8rem;\\n}\\n\\n.class-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n}\\n\\n.teacher[_ngcontent-%COMP%], .room[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  color: #666;\\n  line-height: 1.1;\\n}\\n\\n.type[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  padding: 2px 6px;\\n  border-radius: 10px;\\n  font-weight: 500;\\n  margin-top: 4px;\\n}\\n\\n.type.lecture[_ngcontent-%COMP%] {\\n  background: #e3f2fd;\\n  color: #1976d2;\\n}\\n\\n.type.lab[_ngcontent-%COMP%] {\\n  background: #e8f5e8;\\n  color: #2e7d32;\\n}\\n\\n.type.tutorial[_ngcontent-%COMP%] {\\n  background: #fff3e0;\\n  color: #f57c00;\\n}\\n\\n.break-details[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  color: #ff9800;\\n}\\n\\n.free-period[_ngcontent-%COMP%] {\\n  color: #999;\\n  font-size: 0.8rem;\\n  font-style: italic;\\n}\\n\\n.legend-section[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n}\\n\\n.legend-items[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 20px;\\n  margin-top: 15px;\\n}\\n\\n.legend-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 0.9rem;\\n}\\n\\n.legend-color[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  border-radius: 4px;\\n  border: 1px solid #ddd;\\n}\\n\\n.legend-color.lecture[_ngcontent-%COMP%] {\\n  background: #e3f2fd;\\n}\\n\\n.legend-color.lab[_ngcontent-%COMP%] {\\n  background: #e8f5e8;\\n}\\n\\n.legend-color.tutorial[_ngcontent-%COMP%] {\\n  background: #fff3e0;\\n}\\n\\n.legend-color.break[_ngcontent-%COMP%] {\\n  background: #fff8e1;\\n}\\n\\n.legend-color.free[_ngcontent-%COMP%] {\\n  background: #f5f5f5;\\n}\\n\\n.quick-actions[_ngcontent-%COMP%] {\\n  margin-top: 30px;\\n}\\n\\n.quick-actions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 20px 0;\\n  color: #333;\\n  font-size: 1.3rem;\\n  font-weight: 500;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n  flex-wrap: wrap;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 12px 24px;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  width: 20px;\\n  height: 20px;\\n}\\n\\n@media (max-width: 768px) {\\n  .student-timetable-container[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n  \\n  .page-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 15px;\\n  }\\n  \\n  .timetable-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 100px repeat(5, 1fr);\\n    font-size: 0.8rem;\\n  }\\n  \\n  .time-header[_ngcontent-%COMP%], .day-header[_ngcontent-%COMP%] {\\n    padding: 10px 5px;\\n    font-size: 0.8rem;\\n  }\\n  \\n  .schedule-cell[_ngcontent-%COMP%] {\\n    min-height: 60px;\\n    padding: 4px;\\n  }\\n  \\n  .subject-name[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  \\n  .teacher[_ngcontent-%COMP%], .room[_ngcontent-%COMP%], .type[_ngcontent-%COMP%] {\\n    font-size: 0.6rem;\\n  }\\n  \\n  .legend-items[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 10px;\\n  }\\n  \\n  .action-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  \\n  .action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "StudentTimetableComponent_div_11_Template_button_click_7_listener", "ɵɵrestoreView", "_r4", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "refreshData", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "error", "ɵɵclassProp", "day_r7", "toLowerCase", "ctx_r5", "getCurrentDay", "ɵɵtextInterpolate1", "slot_r8", "day_r10", "teacher", "room", "ɵɵclassMap", "type", "ɵɵtemplate", "StudentTimetableComponent_div_12_div_27_div_3_div_1_div_3_Template", "StudentTimetableComponent_div_12_div_27_div_3_div_1_div_4_Template", "isBreak", "subject", "ɵɵproperty", "StudentTimetableComponent_div_12_div_27_div_3_div_1_Template", "StudentTimetableComponent_div_12_div_27_div_3_div_2_Template", "ctx_r9", "StudentTimetableComponent_div_12_div_27_div_3_Template", "ctx_r6", "isCurrentTimeSlot", "time", "weekDays", "StudentTimetableComponent_div_12_div_26_Template", "StudentTimetableComponent_div_12_div_27_Template", "ɵɵpipeBind1", "ctx_r2", "ɵɵpipeBind2", "getCurrentDate", "timetableData", "StudentTimetableComponent", "constructor", "userService", "dashboardService", "timetableService", "snackBar", "loading", "timeSlots", "ngOnInit", "currentUser", "getUserFromLocalStorage", "user", "loadStudentTimetable", "getStudentDashboard", "_id", "subscribe", "next", "response", "success", "studentDashboard", "dashboard", "loadRealTimetable", "console", "getTimetableByStudent", "generateTimetableFromData", "timetable", "generateSampleTimetable", "open", "duration", "timetableEntries", "map", "timeSlot", "slot", "for<PERSON>ach", "day", "entry", "find", "dayOfWeek", "startTime", "split", "subjectName", "name", "code", "program", "semester", "subjectAttendance", "subjects", "timeIndex", "dayIndex", "subjectIndex", "length", "days", "Date", "getDay", "now", "currentHour", "getHours", "slotHour", "parseInt", "_", "ɵɵdirectiveInject", "i1", "UserService", "i2", "DashboardService", "i3", "TimetableService", "i4", "MatSnackBar", "_2", "selectors", "decls", "vars", "consts", "template", "StudentTimetableComponent_Template", "rf", "ctx", "StudentTimetableComponent_Template_button_click_7_listener", "StudentTimetableComponent_div_10_Template", "StudentTimetableComponent_div_11_Template", "StudentTimetableComponent_div_12_Template"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\student-dashboard\\student-timetable\\student-timetable.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\student-dashboard\\student-timetable\\student-timetable.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { UserService } from '../../../services/user.service';\r\nimport { DashboardService } from '../../../services/dashboard.service';\r\nimport { TimetableService } from '../../../services/timetable.service';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\n\r\n@Component({\r\n  selector: 'app-student-timetable',\r\n  templateUrl: './student-timetable.component.html',\r\n  styleUrls: ['./student-timetable.component.css']\r\n})\r\nexport class StudentTimetableComponent implements OnInit {\r\n  currentUser: any;\r\n  studentDashboard: any;\r\n  loading = false;\r\n  error: string | null = null;\r\n  \r\n  // Sample timetable data - in a real app, this would come from the backend\r\n  timetableData: any[] = [];\r\n  timeSlots = [\r\n    '9:00 AM - 10:00 AM',\r\n    '10:00 AM - 11:00 AM', \r\n    '11:00 AM - 12:00 PM',\r\n    '12:00 PM - 1:00 PM',\r\n    '1:00 PM - 2:00 PM',\r\n    '2:00 PM - 3:00 PM',\r\n    '3:00 PM - 4:00 PM',\r\n    '4:00 PM - 5:00 PM'\r\n  ];\r\n  \r\n  weekDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];\r\n\r\n  constructor(\r\n    private userService: UserService,\r\n    private dashboardService: DashboardService,\r\n    private timetableService: TimetableService,\r\n    private snackBar: MatSnackBar\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.currentUser = this.userService.getUserFromLocalStorage()?.user;\r\n    this.loadStudentTimetable();\r\n  }\r\n\r\n  loadStudentTimetable(): void {\r\n    if (!this.currentUser) return;\r\n\r\n    this.loading = true;\r\n    this.error = null;\r\n\r\n    // Load both dashboard data and real timetable\r\n    this.dashboardService.getStudentDashboard(this.currentUser._id).subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.studentDashboard = response.dashboard;\r\n          this.loadRealTimetable();\r\n        } else {\r\n          this.error = 'Failed to load student dashboard';\r\n          this.loading = false;\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading student dashboard:', error);\r\n        this.error = 'Error loading dashboard data';\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  loadRealTimetable(): void {\r\n    this.timetableService.getTimetableByStudent(this.currentUser._id, '2024-2025').subscribe({\r\n      next: (response) => {\r\n        this.loading = false;\r\n        if (response.success) {\r\n          this.generateTimetableFromData(response.timetable);\r\n        } else {\r\n          // Fallback to sample timetable if no real data\r\n          this.generateSampleTimetable();\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading real timetable:', error);\r\n        this.loading = false;\r\n        // Fallback to sample timetable\r\n        this.generateSampleTimetable();\r\n        this.snackBar.open('Using sample timetable data', 'Close', { duration: 3000 });\r\n      }\r\n    });\r\n  }\r\n\r\n  generateTimetableFromData(timetableEntries: any[]): void {\r\n    this.timetableData = this.timeSlots.map((timeSlot) => {\r\n      const slot: any = { time: timeSlot };\r\n\r\n      this.weekDays.forEach((day) => {\r\n        // Find matching timetable entry for this day and time\r\n        const entry = timetableEntries.find(entry =>\r\n          entry.dayOfWeek.toLowerCase() === day.toLowerCase() &&\r\n          entry.timeSlot.startTime === timeSlot.split(' - ')[0]\r\n        );\r\n\r\n        if (entry) {\r\n          slot[day.toLowerCase()] = {\r\n            subject: entry.subject.subjectName,\r\n            teacher: entry.teacher.name,\r\n            room: entry.room || 'TBA',\r\n            type: 'Lecture',\r\n            code: entry.subject.code,\r\n            program: entry.program.name,\r\n            semester: entry.semester\r\n          };\r\n        } else {\r\n          slot[day.toLowerCase()] = null;\r\n        }\r\n      });\r\n\r\n      return slot;\r\n    });\r\n  }\r\n\r\n  generateSampleTimetable(): void {\r\n    // Generate sample timetable based on subjects\r\n    if (this.studentDashboard?.subjectAttendance) {\r\n      const subjects = this.studentDashboard.subjectAttendance;\r\n      this.timetableData = this.timeSlots.map((timeSlot, timeIndex) => {\r\n        const slot: any = { time: timeSlot };\r\n        \r\n        this.weekDays.forEach((day, dayIndex) => {\r\n          // Simple logic to assign subjects to time slots\r\n          const subjectIndex = (timeIndex + dayIndex) % subjects.length;\r\n          if (subjects[subjectIndex] && timeIndex < 6) { // Skip lunch time and last slot sometimes\r\n            slot[day.toLowerCase()] = {\r\n              subject: subjects[subjectIndex].subjectName,\r\n              teacher: 'Teacher ' + (subjectIndex + 1),\r\n              room: 'Room ' + (101 + subjectIndex),\r\n              type: timeIndex % 3 === 0 ? 'Lecture' : timeIndex % 3 === 1 ? 'Lab' : 'Tutorial'\r\n            };\r\n          } else if (timeIndex === 4) { // Lunch break\r\n            slot[day.toLowerCase()] = {\r\n              subject: 'Lunch Break',\r\n              teacher: '',\r\n              room: 'Cafeteria',\r\n              type: 'Break',\r\n              isBreak: true\r\n            };\r\n          } else {\r\n            slot[day.toLowerCase()] = null;\r\n          }\r\n        });\r\n        \r\n        return slot;\r\n      });\r\n    }\r\n  }\r\n\r\n  refreshData(): void {\r\n    this.loadStudentTimetable();\r\n  }\r\n\r\n  getCurrentDay(): string {\r\n    const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];\r\n    return days[new Date().getDay()];\r\n  }\r\n\r\n  getCurrentDate(): Date {\r\n    return new Date();\r\n  }\r\n\r\n  isCurrentTimeSlot(timeSlot: string): boolean {\r\n    const now = new Date();\r\n    const currentHour = now.getHours();\r\n    const slotHour = parseInt(timeSlot.split(':')[0]);\r\n    \r\n    // Simple check if current hour matches slot hour\r\n    return currentHour === slotHour || (slotHour === 12 && currentHour === 0);\r\n  }\r\n}\r\n", "<div class=\"student-timetable-container\">\r\n  <!-- Header -->\r\n  <div class=\"page-header\">\r\n    <div class=\"header-content\">\r\n      <h1>My Timetable</h1>\r\n      <p>View your weekly class schedule</p>\r\n    </div>\r\n    <button mat-icon-button (click)=\"refreshData()\" matTooltip=\"Refresh Data\">\r\n      <mat-icon>refresh</mat-icon>\r\n    </button>\r\n  </div>\r\n\r\n  <!-- Loading State -->\r\n  <div *ngIf=\"loading\" class=\"loading-container\">\r\n    <mat-spinner></mat-spinner>\r\n    <p>Loading your timetable...</p>\r\n  </div>\r\n\r\n  <!-- Error State -->\r\n  <div *ngIf=\"error && !loading\" class=\"error-container\">\r\n    <mat-icon>error</mat-icon>\r\n    <h3>Error Loading Timetable</h3>\r\n    <p>{{ error }}</p>\r\n    <button mat-raised-button color=\"primary\" (click)=\"refreshData()\">\r\n      <mat-icon>refresh</mat-icon>\r\n      Try Again\r\n    </button>\r\n  </div>\r\n\r\n  <!-- Content -->\r\n  <div *ngIf=\"studentDashboard && !loading && !error\" class=\"timetable-content\">\r\n    <!-- Current Day Highlight -->\r\n    <div class=\"current-day-info\">\r\n      <mat-card class=\"current-day-card\">\r\n        <mat-card-content>\r\n          <div class=\"current-day-content\">\r\n            <mat-icon>today</mat-icon>\r\n            <div class=\"day-info\">\r\n              <h3>Today is {{ getCurrentDay() | titlecase }}</h3>\r\n              <p>{{ getCurrentDate() | date:'fullDate' }}</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n    </div>\r\n\r\n    <!-- Timetable Grid -->\r\n    <div class=\"timetable-wrapper\">\r\n      <mat-card class=\"timetable-card\">\r\n        <mat-card-header>\r\n          <mat-card-title>Weekly Schedule</mat-card-title>\r\n          <mat-card-subtitle>Your class timetable for this week</mat-card-subtitle>\r\n        </mat-card-header>\r\n        \r\n        <mat-card-content>\r\n          <div class=\"timetable-grid\">\r\n            <!-- Header Row -->\r\n            <div class=\"timetable-header\">\r\n              <div class=\"time-header\">Time</div>\r\n              <div *ngFor=\"let day of weekDays\" class=\"day-header\" \r\n                   [class.current-day]=\"day.toLowerCase() === getCurrentDay()\">\r\n                {{ day }}\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Time Slots -->\r\n            <div *ngFor=\"let slot of timetableData\" class=\"timetable-row\"\r\n                 [class.current-time]=\"isCurrentTimeSlot(slot.time)\">\r\n              <div class=\"time-slot\">{{ slot.time }}</div>\r\n              \r\n              <div *ngFor=\"let day of weekDays\" class=\"schedule-cell\"\r\n                   [class.current-day]=\"day.toLowerCase() === getCurrentDay()\"\r\n                   [class.has-class]=\"slot[day.toLowerCase()]\"\r\n                   [class.break-time]=\"slot[day.toLowerCase()]?.isBreak\">\r\n                \r\n                <div *ngIf=\"slot[day.toLowerCase()]\" class=\"class-info\">\r\n                  <div class=\"subject-name\" \r\n                       [class.break]=\"slot[day.toLowerCase()].isBreak\">\r\n                    {{ slot[day.toLowerCase()].subject }}\r\n                  </div>\r\n                  <div *ngIf=\"!slot[day.toLowerCase()].isBreak\" class=\"class-details\">\r\n                    <div class=\"teacher\">{{ slot[day.toLowerCase()].teacher }}</div>\r\n                    <div class=\"room\">{{ slot[day.toLowerCase()].room }}</div>\r\n                    <div class=\"type\" [class]=\"slot[day.toLowerCase()].type.toLowerCase()\">\r\n                      {{ slot[day.toLowerCase()].type }}\r\n                    </div>\r\n                  </div>\r\n                  <div *ngIf=\"slot[day.toLowerCase()].isBreak\" class=\"break-details\">\r\n                    <div class=\"room\">{{ slot[day.toLowerCase()].room }}</div>\r\n                  </div>\r\n                </div>\r\n                \r\n                <div *ngIf=\"!slot[day.toLowerCase()]\" class=\"free-period\">\r\n                  <span>Free</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n    </div>\r\n\r\n    <!-- Legend -->\r\n    <div class=\"legend-section\">\r\n      <mat-card class=\"legend-card\">\r\n        <mat-card-header>\r\n          <mat-card-title>Legend</mat-card-title>\r\n        </mat-card-header>\r\n        <mat-card-content>\r\n          <div class=\"legend-items\">\r\n            <div class=\"legend-item\">\r\n              <div class=\"legend-color lecture\"></div>\r\n              <span>Lecture</span>\r\n            </div>\r\n            <div class=\"legend-item\">\r\n              <div class=\"legend-color lab\"></div>\r\n              <span>Lab</span>\r\n            </div>\r\n            <div class=\"legend-item\">\r\n              <div class=\"legend-color tutorial\"></div>\r\n              <span>Tutorial</span>\r\n            </div>\r\n            <div class=\"legend-item\">\r\n              <div class=\"legend-color break\"></div>\r\n              <span>Break</span>\r\n            </div>\r\n            <div class=\"legend-item\">\r\n              <div class=\"legend-color free\"></div>\r\n              <span>Free Period</span>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n    </div>\r\n\r\n    <!-- Quick Actions -->\r\n    <div class=\"quick-actions\">\r\n      <h3>Quick Actions</h3>\r\n      <div class=\"action-buttons\">\r\n        <button mat-raised-button color=\"primary\" routerLink=\"/dashboard/student/subjects\">\r\n          <mat-icon>subject</mat-icon>\r\n          View Subjects\r\n        </button>\r\n        <button mat-raised-button color=\"accent\" routerLink=\"/dashboard/student/teachers\">\r\n          <mat-icon>person</mat-icon>\r\n          View Teachers\r\n        </button>\r\n        <button mat-raised-button color=\"primary\" routerLink=\"/dashboard/student/attendance\">\r\n          <mat-icon>fact_check</mat-icon>\r\n          View Attendance\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": ";;;;;;;;;;;;;;ICaEA,EAAA,CAAAC,cAAA,aAA+C;IAC7CD,EAAA,CAAAE,SAAA,kBAA2B;IAC3BF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,gCAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;;IAIlCJ,EAAA,CAAAC,cAAA,aAAuD;IAC3CD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC1BJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,8BAAuB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAChCJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAW;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAClBJ,EAAA,CAAAC,cAAA,gBAAkE;IAAxBD,EAAA,CAAAK,UAAA,mBAAAC,kEAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAC/DZ,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC5BJ,EAAA,CAAAG,MAAA,mBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAJNJ,EAAA,CAAAa,SAAA,GAAW;IAAXb,EAAA,CAAAc,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IAqCJhB,EAAA,CAAAC,cAAA,cACiE;IAC/DD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAFDJ,EAAA,CAAAiB,WAAA,gBAAAC,MAAA,CAAAC,WAAA,OAAAC,MAAA,CAAAC,aAAA,GAA2D;IAC9DrB,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAsB,kBAAA,MAAAJ,MAAA,MACF;;;;;IAkBIlB,EAAA,CAAAC,cAAA,cAAoE;IAC7CD,EAAA,CAAAG,MAAA,GAAqC;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAChEJ,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAG,MAAA,GAAkC;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAC1DJ,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAJeJ,EAAA,CAAAa,SAAA,GAAqC;IAArCb,EAAA,CAAAc,iBAAA,CAAAS,OAAA,CAAAC,OAAA,CAAAL,WAAA,IAAAM,OAAA,CAAqC;IACxCzB,EAAA,CAAAa,SAAA,GAAkC;IAAlCb,EAAA,CAAAc,iBAAA,CAAAS,OAAA,CAAAC,OAAA,CAAAL,WAAA,IAAAO,IAAA,CAAkC;IAClC1B,EAAA,CAAAa,SAAA,GAAoD;IAApDb,EAAA,CAAA2B,UAAA,CAAAJ,OAAA,CAAAC,OAAA,CAAAL,WAAA,IAAAS,IAAA,CAAAT,WAAA,GAAoD;IACpEnB,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAsB,kBAAA,MAAAC,OAAA,CAAAC,OAAA,CAAAL,WAAA,IAAAS,IAAA,MACF;;;;;IAEF5B,EAAA,CAAAC,cAAA,cAAmE;IAC/CD,EAAA,CAAAG,MAAA,GAAkC;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAAxCJ,EAAA,CAAAa,SAAA,GAAkC;IAAlCb,EAAA,CAAAc,iBAAA,CAAAS,OAAA,CAAAC,OAAA,CAAAL,WAAA,IAAAO,IAAA,CAAkC;;;;;IAbxD1B,EAAA,CAAAC,cAAA,cAAwD;IAGpDD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAA6B,UAAA,IAAAC,kEAAA,kBAMM;IACN9B,EAAA,CAAA6B,UAAA,IAAAE,kEAAA,kBAEM;IACR/B,EAAA,CAAAI,YAAA,EAAM;;;;;IAbCJ,EAAA,CAAAa,SAAA,GAA+C;IAA/Cb,EAAA,CAAAiB,WAAA,UAAAM,OAAA,CAAAC,OAAA,CAAAL,WAAA,IAAAa,OAAA,CAA+C;IAClDhC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAsB,kBAAA,MAAAC,OAAA,CAAAC,OAAA,CAAAL,WAAA,IAAAc,OAAA,MACF;IACMjC,EAAA,CAAAa,SAAA,GAAsC;IAAtCb,EAAA,CAAAkC,UAAA,UAAAX,OAAA,CAAAC,OAAA,CAAAL,WAAA,IAAAa,OAAA,CAAsC;IAOtChC,EAAA,CAAAa,SAAA,GAAqC;IAArCb,EAAA,CAAAkC,UAAA,SAAAX,OAAA,CAAAC,OAAA,CAAAL,WAAA,IAAAa,OAAA,CAAqC;;;;;IAK7ChC,EAAA,CAAAC,cAAA,cAA0D;IAClDD,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAvBrBJ,EAAA,CAAAC,cAAA,cAG2D;IAEzDD,EAAA,CAAA6B,UAAA,IAAAM,4DAAA,kBAeM;IAENnC,EAAA,CAAA6B,UAAA,IAAAO,4DAAA,kBAEM;IACRpC,EAAA,CAAAI,YAAA,EAAM;;;;;;IAxBDJ,EAAA,CAAAiB,WAAA,gBAAAO,OAAA,CAAAL,WAAA,OAAAkB,MAAA,CAAAhB,aAAA,GAA2D,cAAAE,OAAA,CAAAC,OAAA,CAAAL,WAAA,mBAAAI,OAAA,CAAAC,OAAA,CAAAL,WAAA,qBAAAI,OAAA,CAAAC,OAAA,CAAAL,WAAA,IAAAa,OAAA;IAIxDhC,EAAA,CAAAa,SAAA,GAA6B;IAA7Bb,EAAA,CAAAkC,UAAA,SAAAX,OAAA,CAAAC,OAAA,CAAAL,WAAA,IAA6B;IAiB7BnB,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAAkC,UAAA,UAAAX,OAAA,CAAAC,OAAA,CAAAL,WAAA,IAA8B;;;;;IA1BxCnB,EAAA,CAAAC,cAAA,cACyD;IAChCD,EAAA,CAAAG,MAAA,GAAe;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAE5CJ,EAAA,CAAA6B,UAAA,IAAAS,sDAAA,kBAyBM;IACRtC,EAAA,CAAAI,YAAA,EAAM;;;;;IA7BDJ,EAAA,CAAAiB,WAAA,iBAAAsB,MAAA,CAAAC,iBAAA,CAAAjB,OAAA,CAAAkB,IAAA,EAAmD;IAC/BzC,EAAA,CAAAa,SAAA,GAAe;IAAfb,EAAA,CAAAc,iBAAA,CAAAS,OAAA,CAAAkB,IAAA,CAAe;IAEjBzC,EAAA,CAAAa,SAAA,GAAW;IAAXb,EAAA,CAAAkC,UAAA,YAAAK,MAAA,CAAAG,QAAA,CAAW;;;;;IAxC5C1C,EAAA,CAAAC,cAAA,cAA8E;IAM1DD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC1BJ,EAAA,CAAAC,cAAA,cAAsB;IAChBD,EAAA,CAAAG,MAAA,GAA0C;;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACnDJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,IAAwC;;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAQzDJ,EAAA,CAAAC,cAAA,eAA+B;IAGTD,EAAA,CAAAG,MAAA,uBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IAChDJ,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAG,MAAA,0CAAkC;IAAAH,EAAA,CAAAI,YAAA,EAAoB;IAG3EJ,EAAA,CAAAC,cAAA,wBAAkB;IAIaD,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACnCJ,EAAA,CAAA6B,UAAA,KAAAc,gDAAA,kBAGM;IACR3C,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAA6B,UAAA,KAAAe,gDAAA,kBA8BM;IACR5C,EAAA,CAAAI,YAAA,EAAM;IAMZJ,EAAA,CAAAC,cAAA,eAA4B;IAGND,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IAEzCJ,EAAA,CAAAC,cAAA,wBAAkB;IAGZD,EAAA,CAAAE,SAAA,eAAwC;IACxCF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEtBJ,EAAA,CAAAC,cAAA,eAAyB;IACvBD,EAAA,CAAAE,SAAA,eAAoC;IACpCF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,WAAG;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAElBJ,EAAA,CAAAC,cAAA,eAAyB;IACvBD,EAAA,CAAAE,SAAA,eAAyC;IACzCF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEvBJ,EAAA,CAAAC,cAAA,eAAyB;IACvBD,EAAA,CAAAE,SAAA,eAAsC;IACtCF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEpBJ,EAAA,CAAAC,cAAA,eAAyB;IACvBD,EAAA,CAAAE,SAAA,eAAqC;IACrCF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,mBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAQlCJ,EAAA,CAAAC,cAAA,eAA2B;IACrBD,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACtBJ,EAAA,CAAAC,cAAA,eAA4B;IAEdD,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC5BJ,EAAA,CAAAG,MAAA,uBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAAkF;IACtED,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC3BJ,EAAA,CAAAG,MAAA,uBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAAqF;IACzED,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC/BJ,EAAA,CAAAG,MAAA,yBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAhHCJ,EAAA,CAAAa,SAAA,GAA0C;IAA1Cb,EAAA,CAAAsB,kBAAA,cAAAtB,EAAA,CAAA6C,WAAA,QAAAC,MAAA,CAAAzB,aAAA,QAA0C;IAC3CrB,EAAA,CAAAa,SAAA,GAAwC;IAAxCb,EAAA,CAAAc,iBAAA,CAAAd,EAAA,CAAA+C,WAAA,QAAAD,MAAA,CAAAE,cAAA,gBAAwC;IAoBtBhD,EAAA,CAAAa,SAAA,IAAW;IAAXb,EAAA,CAAAkC,UAAA,YAAAY,MAAA,CAAAJ,QAAA,CAAW;IAOZ1C,EAAA,CAAAa,SAAA,GAAgB;IAAhBb,EAAA,CAAAkC,UAAA,YAAAY,MAAA,CAAAG,aAAA,CAAgB;;;ADvDlD,OAAM,MAAOC,yBAAyB;EAqBpCC,YACUC,WAAwB,EACxBC,gBAAkC,EAClCC,gBAAkC,EAClCC,QAAqB;IAHrB,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,QAAQ,GAARA,QAAQ;IAtBlB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAxC,KAAK,GAAkB,IAAI;IAE3B;IACA,KAAAiC,aAAa,GAAU,EAAE;IACzB,KAAAQ,SAAS,GAAG,CACV,oBAAoB,EACpB,qBAAqB,EACrB,qBAAqB,EACrB,oBAAoB,EACpB,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,CACpB;IAED,KAAAf,QAAQ,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,CAAC;EAOhE;EAEHgB,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,GAAG,IAAI,CAACP,WAAW,CAACQ,uBAAuB,EAAE,EAAEC,IAAI;IACnE,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEAA,oBAAoBA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACH,WAAW,EAAE;IAEvB,IAAI,CAACH,OAAO,GAAG,IAAI;IACnB,IAAI,CAACxC,KAAK,GAAG,IAAI;IAEjB;IACA,IAAI,CAACqC,gBAAgB,CAACU,mBAAmB,CAAC,IAAI,CAACJ,WAAW,CAACK,GAAG,CAAC,CAACC,SAAS,CAAC;MACxEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACC,gBAAgB,GAAGF,QAAQ,CAACG,SAAS;UAC1C,IAAI,CAACC,iBAAiB,EAAE;SACzB,MAAM;UACL,IAAI,CAACvD,KAAK,GAAG,kCAAkC;UAC/C,IAAI,CAACwC,OAAO,GAAG,KAAK;;MAExB,CAAC;MACDxC,KAAK,EAAGA,KAAK,IAAI;QACfwD,OAAO,CAACxD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,IAAI,CAACA,KAAK,GAAG,8BAA8B;QAC3C,IAAI,CAACwC,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAe,iBAAiBA,CAAA;IACf,IAAI,CAACjB,gBAAgB,CAACmB,qBAAqB,CAAC,IAAI,CAACd,WAAW,CAACK,GAAG,EAAE,WAAW,CAAC,CAACC,SAAS,CAAC;MACvFC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACX,OAAO,GAAG,KAAK;QACpB,IAAIW,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACM,yBAAyB,CAACP,QAAQ,CAACQ,SAAS,CAAC;SACnD,MAAM;UACL;UACA,IAAI,CAACC,uBAAuB,EAAE;;MAElC,CAAC;MACD5D,KAAK,EAAGA,KAAK,IAAI;QACfwD,OAAO,CAACxD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,IAAI,CAACwC,OAAO,GAAG,KAAK;QACpB;QACA,IAAI,CAACoB,uBAAuB,EAAE;QAC9B,IAAI,CAACrB,QAAQ,CAACsB,IAAI,CAAC,6BAA6B,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MAChF;KACD,CAAC;EACJ;EAEAJ,yBAAyBA,CAACK,gBAAuB;IAC/C,IAAI,CAAC9B,aAAa,GAAG,IAAI,CAACQ,SAAS,CAACuB,GAAG,CAAEC,QAAQ,IAAI;MACnD,MAAMC,IAAI,GAAQ;QAAEzC,IAAI,EAAEwC;MAAQ,CAAE;MAEpC,IAAI,CAACvC,QAAQ,CAACyC,OAAO,CAAEC,GAAG,IAAI;QAC5B;QACA,MAAMC,KAAK,GAAGN,gBAAgB,CAACO,IAAI,CAACD,KAAK,IACvCA,KAAK,CAACE,SAAS,CAACpE,WAAW,EAAE,KAAKiE,GAAG,CAACjE,WAAW,EAAE,IACnDkE,KAAK,CAACJ,QAAQ,CAACO,SAAS,KAAKP,QAAQ,CAACQ,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CACtD;QAED,IAAIJ,KAAK,EAAE;UACTH,IAAI,CAACE,GAAG,CAACjE,WAAW,EAAE,CAAC,GAAG;YACxBc,OAAO,EAAEoD,KAAK,CAACpD,OAAO,CAACyD,WAAW;YAClCjE,OAAO,EAAE4D,KAAK,CAAC5D,OAAO,CAACkE,IAAI;YAC3BjE,IAAI,EAAE2D,KAAK,CAAC3D,IAAI,IAAI,KAAK;YACzBE,IAAI,EAAE,SAAS;YACfgE,IAAI,EAAEP,KAAK,CAACpD,OAAO,CAAC2D,IAAI;YACxBC,OAAO,EAAER,KAAK,CAACQ,OAAO,CAACF,IAAI;YAC3BG,QAAQ,EAAET,KAAK,CAACS;WACjB;SACF,MAAM;UACLZ,IAAI,CAACE,GAAG,CAACjE,WAAW,EAAE,CAAC,GAAG,IAAI;;MAElC,CAAC,CAAC;MAEF,OAAO+D,IAAI;IACb,CAAC,CAAC;EACJ;EAEAN,uBAAuBA,CAAA;IACrB;IACA,IAAI,IAAI,CAACP,gBAAgB,EAAE0B,iBAAiB,EAAE;MAC5C,MAAMC,QAAQ,GAAG,IAAI,CAAC3B,gBAAgB,CAAC0B,iBAAiB;MACxD,IAAI,CAAC9C,aAAa,GAAG,IAAI,CAACQ,SAAS,CAACuB,GAAG,CAAC,CAACC,QAAQ,EAAEgB,SAAS,KAAI;QAC9D,MAAMf,IAAI,GAAQ;UAAEzC,IAAI,EAAEwC;QAAQ,CAAE;QAEpC,IAAI,CAACvC,QAAQ,CAACyC,OAAO,CAAC,CAACC,GAAG,EAAEc,QAAQ,KAAI;UACtC;UACA,MAAMC,YAAY,GAAG,CAACF,SAAS,GAAGC,QAAQ,IAAIF,QAAQ,CAACI,MAAM;UAC7D,IAAIJ,QAAQ,CAACG,YAAY,CAAC,IAAIF,SAAS,GAAG,CAAC,EAAE;YAAE;YAC7Cf,IAAI,CAACE,GAAG,CAACjE,WAAW,EAAE,CAAC,GAAG;cACxBc,OAAO,EAAE+D,QAAQ,CAACG,YAAY,CAAC,CAACT,WAAW;cAC3CjE,OAAO,EAAE,UAAU,IAAI0E,YAAY,GAAG,CAAC,CAAC;cACxCzE,IAAI,EAAE,OAAO,IAAI,GAAG,GAAGyE,YAAY,CAAC;cACpCvE,IAAI,EAAEqE,SAAS,GAAG,CAAC,KAAK,CAAC,GAAG,SAAS,GAAGA,SAAS,GAAG,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG;aACvE;WACF,MAAM,IAAIA,SAAS,KAAK,CAAC,EAAE;YAAE;YAC5Bf,IAAI,CAACE,GAAG,CAACjE,WAAW,EAAE,CAAC,GAAG;cACxBc,OAAO,EAAE,aAAa;cACtBR,OAAO,EAAE,EAAE;cACXC,IAAI,EAAE,WAAW;cACjBE,IAAI,EAAE,OAAO;cACbI,OAAO,EAAE;aACV;WACF,MAAM;YACLkD,IAAI,CAACE,GAAG,CAACjE,WAAW,EAAE,CAAC,GAAG,IAAI;;QAElC,CAAC,CAAC;QAEF,OAAO+D,IAAI;MACb,CAAC,CAAC;;EAEN;EAEAtE,WAAWA,CAAA;IACT,IAAI,CAACkD,oBAAoB,EAAE;EAC7B;EAEAzC,aAAaA,CAAA;IACX,MAAMgF,IAAI,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,CAAC;IAC3F,OAAOA,IAAI,CAAC,IAAIC,IAAI,EAAE,CAACC,MAAM,EAAE,CAAC;EAClC;EAEAvD,cAAcA,CAAA;IACZ,OAAO,IAAIsD,IAAI,EAAE;EACnB;EAEA9D,iBAAiBA,CAACyC,QAAgB;IAChC,MAAMuB,GAAG,GAAG,IAAIF,IAAI,EAAE;IACtB,MAAMG,WAAW,GAAGD,GAAG,CAACE,QAAQ,EAAE;IAClC,MAAMC,QAAQ,GAAGC,QAAQ,CAAC3B,QAAQ,CAACQ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAEjD;IACA,OAAOgB,WAAW,KAAKE,QAAQ,IAAKA,QAAQ,KAAK,EAAE,IAAIF,WAAW,KAAK,CAAE;EAC3E;EAAC,QAAAI,CAAA,G;qBApKU3D,yBAAyB,EAAAlD,EAAA,CAAA8G,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAhH,EAAA,CAAA8G,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAlH,EAAA,CAAA8G,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAApH,EAAA,CAAA8G,iBAAA,CAAAO,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAzBrE,yBAAyB;IAAAsE,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCXtC9H,EAAA,CAAAC,cAAA,aAAyC;QAI/BD,EAAA,CAAAG,MAAA,mBAAY;QAAAH,EAAA,CAAAI,YAAA,EAAK;QACrBJ,EAAA,CAAAC,cAAA,QAAG;QAAAD,EAAA,CAAAG,MAAA,sCAA+B;QAAAH,EAAA,CAAAI,YAAA,EAAI;QAExCJ,EAAA,CAAAC,cAAA,gBAA0E;QAAlDD,EAAA,CAAAK,UAAA,mBAAA2H,2DAAA;UAAA,OAASD,GAAA,CAAAnH,WAAA,EAAa;QAAA,EAAC;QAC7CZ,EAAA,CAAAC,cAAA,eAAU;QAAAD,EAAA,CAAAG,MAAA,cAAO;QAAAH,EAAA,CAAAI,YAAA,EAAW;QAKhCJ,EAAA,CAAA6B,UAAA,KAAAoG,yCAAA,iBAGM;QAGNjI,EAAA,CAAA6B,UAAA,KAAAqG,yCAAA,kBAQM;QAGNlI,EAAA,CAAA6B,UAAA,KAAAsG,yCAAA,kBA2HM;QACRnI,EAAA,CAAAI,YAAA,EAAM;;;QA7IEJ,EAAA,CAAAa,SAAA,IAAa;QAAbb,EAAA,CAAAkC,UAAA,SAAA6F,GAAA,CAAAvE,OAAA,CAAa;QAMbxD,EAAA,CAAAa,SAAA,GAAuB;QAAvBb,EAAA,CAAAkC,UAAA,SAAA6F,GAAA,CAAA/G,KAAA,KAAA+G,GAAA,CAAAvE,OAAA,CAAuB;QAWvBxD,EAAA,CAAAa,SAAA,GAA4C;QAA5Cb,EAAA,CAAAkC,UAAA,SAAA6F,GAAA,CAAA1D,gBAAA,KAAA0D,GAAA,CAAAvE,OAAA,KAAAuE,GAAA,CAAA/G,KAAA,CAA4C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}