const express = require('express');
const router = express.Router();

const {
    createProgram,
    getAllPrograms,
    getProgramById,
    updateProgram,
    deleteProgram,
    permanentDeleteProgram
} = require('../controllers/program-controller.js');

// Routes

// Create a new program
router.post('/program', createProgram);

// Get all programs
router.get('/programs', getAllPrograms);

// Get program by ID
router.get('/program/:id', getProgramById);

// Update program
router.put('/program/:id', updateProgram);

// Delete program (soft delete)
router.delete('/program/:id', deleteProgram);

// Permanently delete program
router.delete('/program/:id/permanent', permanentDeleteProgram);

module.exports = router;
