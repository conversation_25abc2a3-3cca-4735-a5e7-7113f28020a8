{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let ViewClassComponent = /*#__PURE__*/(() => {\n  class ViewClassComponent {\n    static #_ = this.ɵfac = function ViewClassComponent_Factory(t) {\n      return new (t || ViewClassComponent)();\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ViewClassComponent,\n      selectors: [[\"app-view-class\"]],\n      decls: 2,\n      vars: 0,\n      template: function ViewClassComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p\");\n          i0.ɵɵtext(1, \"view-class works!\");\n          i0.ɵɵelementEnd();\n        }\n      }\n    });\n  }\n  return ViewClassComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}