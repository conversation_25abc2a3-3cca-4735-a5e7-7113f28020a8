{"ast": null, "code": "import Swal from 'sweetalert2';\nimport { UX_TEXT_STANDARDS, ROLE_SPECIFIC_TEXT, CONFIRMATION_MESSAGES } from '../constants/ux-text-standards';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/snack-bar\";\nexport class UxHelperService {\n  constructor(snackBar) {\n    this.snackBar = snackBar;\n  }\n  /**\n   * Show user-friendly success message with SweetAlert\n   */\n  showSuccessMessage(messageKey, customMessage) {\n    const message = customMessage || UX_TEXT_STANDARDS.SUCCESS_MESSAGES[messageKey];\n    return Swal.fire({\n      title: 'Success!',\n      text: message,\n      icon: 'success',\n      confirmButtonColor: '#29578c',\n      confirmButtonText: 'Great!',\n      timer: 3000,\n      timerProgressBar: true\n    });\n  }\n  /**\n   * Show user-friendly error message with SweetAlert\n   */\n  showErrorMessage(messageKey, customMessage) {\n    const message = customMessage || UX_TEXT_STANDARDS.ERROR_MESSAGES[messageKey];\n    return Swal.fire({\n      title: 'Oops!',\n      text: message,\n      icon: 'error',\n      confirmButtonColor: '#29578c',\n      confirmButtonText: 'I understand'\n    });\n  }\n  /**\n   * Show confirmation dialog with user-friendly text\n   */\n  showConfirmationDialog(messageKey, customMessage, confirmButtonText = 'Yes, proceed', cancelButtonText = 'Cancel') {\n    const message = customMessage || CONFIRMATION_MESSAGES[messageKey];\n    return Swal.fire({\n      title: 'Please confirm',\n      text: message,\n      icon: 'question',\n      showCancelButton: true,\n      confirmButtonColor: '#29578c',\n      cancelButtonColor: '#6c757d',\n      confirmButtonText: confirmButtonText,\n      cancelButtonText: cancelButtonText,\n      reverseButtons: true\n    });\n  }\n  /**\n   * Show loading message with SweetAlert\n   */\n  showLoadingMessage(messageKey, customMessage) {\n    const message = customMessage || UX_TEXT_STANDARDS.LOADING_MESSAGES[messageKey];\n    Swal.fire({\n      title: 'Please wait...',\n      text: message,\n      allowOutsideClick: false,\n      allowEscapeKey: false,\n      showConfirmButton: false,\n      didOpen: () => {\n        Swal.showLoading();\n      }\n    });\n  }\n  /**\n   * Close any open SweetAlert dialog\n   */\n  closeLoadingMessage() {\n    Swal.close();\n  }\n  /**\n   * Show snackbar notification with user-friendly styling\n   */\n  showSnackbar(message, action = 'Close', duration = 5000, type = 'info') {\n    this.snackBar.open(message, action, {\n      duration: duration,\n      panelClass: [`${type}-snackbar`],\n      horizontalPosition: 'right',\n      verticalPosition: 'top'\n    });\n  }\n  /**\n   * Get role-specific welcome message\n   */\n  getRoleSpecificText(role, textKey) {\n    const roleUpper = role.toUpperCase();\n    return ROLE_SPECIFIC_TEXT[roleUpper]?.[textKey] || '';\n  }\n  /**\n   * Get user-friendly form validation error message\n   */\n  getValidationErrorMessage(fieldName, errorType, customValue) {\n    switch (errorType) {\n      case 'required':\n        return UX_TEXT_STANDARDS.ERROR_MESSAGES.REQUIRED_FIELD;\n      case 'email':\n        return UX_TEXT_STANDARDS.ERROR_MESSAGES.INVALID_EMAIL;\n      case 'minlength':\n        if (fieldName.toLowerCase().includes('password')) {\n          return UX_TEXT_STANDARDS.ERROR_MESSAGES.PASSWORD_TOO_SHORT;\n        }\n        if (fieldName.toLowerCase().includes('name')) {\n          return UX_TEXT_STANDARDS.ERROR_MESSAGES.NAME_TOO_SHORT;\n        }\n        return `This field must be at least ${customValue?.requiredLength || 2} characters long`;\n      case 'pattern':\n        if (fieldName.toLowerCase().includes('phone') || fieldName.toLowerCase().includes('contact')) {\n          return UX_TEXT_STANDARDS.ERROR_MESSAGES.INVALID_PHONE;\n        }\n        return 'Please enter a valid format';\n      case 'mismatch':\n        return UX_TEXT_STANDARDS.ERROR_MESSAGES.PASSWORDS_DONT_MATCH;\n      default:\n        return 'Please check this field and try again';\n    }\n  }\n  /**\n   * Get tooltip text for form fields\n   */\n  getTooltipText(fieldKey) {\n    return UX_TEXT_STANDARDS.TOOLTIPS[fieldKey] || '';\n  }\n  /**\n   * Get user-friendly label text\n   */\n  getLabelText(labelKey) {\n    return UX_TEXT_STANDARDS.FORM_LABELS[labelKey] || '';\n  }\n  /**\n   * Get placeholder text\n   */\n  getPlaceholderText(placeholderKey) {\n    return UX_TEXT_STANDARDS.PLACEHOLDERS[placeholderKey] || '';\n  }\n  /**\n   * Get button text\n   */\n  getButtonText(buttonKey) {\n    return UX_TEXT_STANDARDS.BUTTONS[buttonKey] || '';\n  }\n  /**\n   * Show welcome message based on user role\n   */\n  showWelcomeMessage(userName, userRole) {\n    const roleSpecificWelcome = this.getRoleSpecificText(userRole, 'DASHBOARD_WELCOME');\n    return Swal.fire({\n      title: `Welcome back, ${userName}!`,\n      text: roleSpecificWelcome,\n      icon: 'success',\n      confirmButtonColor: '#29578c',\n      confirmButtonText: 'Let\\'s get started!',\n      timer: 4000,\n      timerProgressBar: true\n    });\n  }\n  /**\n   * Show attendance warning for students\n   */\n  showAttendanceWarning(attendancePercentage, subjectName) {\n    let message = '';\n    let title = '';\n    if (attendancePercentage < 75) {\n      title = 'Attendance Alert!';\n      message = subjectName ? `Your attendance in ${subjectName} is ${attendancePercentage}%. You need at least 75% to be eligible for exams.` : `Your overall attendance is ${attendancePercentage}%. You need at least 75% to be eligible for exams.`;\n    } else if (attendancePercentage < 80) {\n      title = 'Attendance Notice';\n      message = subjectName ? `Your attendance in ${subjectName} is ${attendancePercentage}%. Try to maintain above 80% for better academic standing.` : `Your overall attendance is ${attendancePercentage}%. Try to maintain above 80% for better academic standing.`;\n    }\n    return Swal.fire({\n      title: title,\n      text: message,\n      icon: attendancePercentage < 75 ? 'warning' : 'info',\n      confirmButtonColor: '#29578c',\n      confirmButtonText: 'I understand'\n    });\n  }\n  /**\n   * Show struck-off warning for BS students\n   */\n  showStruckOffWarning(subjectName, consecutiveAbsences) {\n    return Swal.fire({\n      title: 'Important Notice!',\n      text: `You have been absent for ${consecutiveAbsences} consecutive classes in ${subjectName}. You may be struck off from this subject. Please contact your teacher immediately.`,\n      icon: 'warning',\n      confirmButtonColor: '#d33',\n      confirmButtonText: 'Contact Teacher',\n      showCancelButton: true,\n      cancelButtonText: 'I understand'\n    });\n  }\n  static #_ = this.ɵfac = function UxHelperService_Factory(t) {\n    return new (t || UxHelperService)(i0.ɵɵinject(i1.MatSnackBar));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: UxHelperService,\n    factory: UxHelperService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["<PERSON><PERSON>", "UX_TEXT_STANDARDS", "ROLE_SPECIFIC_TEXT", "CONFIRMATION_MESSAGES", "UxHelperService", "constructor", "snackBar", "showSuccessMessage", "message<PERSON>ey", "customMessage", "message", "SUCCESS_MESSAGES", "fire", "title", "text", "icon", "confirmButtonColor", "confirmButtonText", "timer", "timerP<PERSON>ressBar", "showErrorMessage", "ERROR_MESSAGES", "showConfirmationDialog", "cancelButtonText", "showCancelButton", "cancelButtonColor", "reverseButtons", "showLoadingMessage", "LOADING_MESSAGES", "allowOutsideClick", "allowEscapeKey", "showConfirmButton", "did<PERSON><PERSON>", "showLoading", "closeLoadingMessage", "close", "showSnackbar", "action", "duration", "type", "open", "panelClass", "horizontalPosition", "verticalPosition", "getRoleSpecificText", "role", "<PERSON><PERSON><PERSON>", "roleUpper", "toUpperCase", "getValidationErrorMessage", "fieldName", "errorType", "customValue", "REQUIRED_FIELD", "INVALID_EMAIL", "toLowerCase", "includes", "PASSWORD_TOO_SHORT", "NAME_TOO_SHORT", "<PERSON><PERSON><PERSON><PERSON>", "INVALID_PHONE", "PASSWORDS_DONT_MATCH", "getTooltipText", "<PERSON><PERSON><PERSON>", "TOOLTIPS", "getLabelText", "labelKey", "FORM_LABELS", "getPlaceholderText", "placeholder<PERSON><PERSON>", "PLACEHOLDERS", "getButtonText", "button<PERSON>ey", "BUTTONS", "showWelcomeMessage", "userName", "userRole", "roleSpecificWelcome", "showAttendanceWarning", "attendancePercentage", "subjectName", "showStruckOffWarning", "consecutiveAbsences", "_", "i0", "ɵɵinject", "i1", "MatSnackBar", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\shared\\services\\ux-helper.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport Swal from 'sweetalert2';\r\nimport { UX_TEXT_STANDARDS, ROLE_SPECIFIC_TEXT, CONFIRMATION_MESSAGES } from '../constants/ux-text-standards';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class UxHelperService {\r\n\r\n  constructor(private snackBar: MatSnackBar) { }\r\n\r\n  /**\r\n   * Show user-friendly success message with SweetAlert\r\n   */\r\n  showSuccessMessage(messageKey: keyof typeof UX_TEXT_STANDARDS.SUCCESS_MESSAGES, customMessage?: string): Promise<any> {\r\n    const message = customMessage || UX_TEXT_STANDARDS.SUCCESS_MESSAGES[messageKey];\r\n    return Swal.fire({\r\n      title: 'Success!',\r\n      text: message,\r\n      icon: 'success',\r\n      confirmButtonColor: '#29578c',\r\n      confirmButtonText: 'Great!',\r\n      timer: 3000,\r\n      timerProgressBar: true\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Show user-friendly error message with SweetAlert\r\n   */\r\n  showErrorMessage(messageKey: keyof typeof UX_TEXT_STANDARDS.ERROR_MESSAGES, customMessage?: string): Promise<any> {\r\n    const message = customMessage || UX_TEXT_STANDARDS.ERROR_MESSAGES[messageKey];\r\n    return Swal.fire({\r\n      title: 'Oops!',\r\n      text: message,\r\n      icon: 'error',\r\n      confirmButtonColor: '#29578c',\r\n      confirmButtonText: 'I understand'\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Show confirmation dialog with user-friendly text\r\n   */\r\n  showConfirmationDialog(\r\n    messageKey: keyof typeof CONFIRMATION_MESSAGES, \r\n    customMessage?: string,\r\n    confirmButtonText: string = 'Yes, proceed',\r\n    cancelButtonText: string = 'Cancel'\r\n  ): Promise<any> {\r\n    const message = customMessage || CONFIRMATION_MESSAGES[messageKey];\r\n    return Swal.fire({\r\n      title: 'Please confirm',\r\n      text: message,\r\n      icon: 'question',\r\n      showCancelButton: true,\r\n      confirmButtonColor: '#29578c',\r\n      cancelButtonColor: '#6c757d',\r\n      confirmButtonText: confirmButtonText,\r\n      cancelButtonText: cancelButtonText,\r\n      reverseButtons: true\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Show loading message with SweetAlert\r\n   */\r\n  showLoadingMessage(messageKey: keyof typeof UX_TEXT_STANDARDS.LOADING_MESSAGES, customMessage?: string): void {\r\n    const message = customMessage || UX_TEXT_STANDARDS.LOADING_MESSAGES[messageKey];\r\n    Swal.fire({\r\n      title: 'Please wait...',\r\n      text: message,\r\n      allowOutsideClick: false,\r\n      allowEscapeKey: false,\r\n      showConfirmButton: false,\r\n      didOpen: () => {\r\n        Swal.showLoading();\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Close any open SweetAlert dialog\r\n   */\r\n  closeLoadingMessage(): void {\r\n    Swal.close();\r\n  }\r\n\r\n  /**\r\n   * Show snackbar notification with user-friendly styling\r\n   */\r\n  showSnackbar(message: string, action: string = 'Close', duration: number = 5000, type: 'success' | 'error' | 'info' = 'info'): void {\r\n    this.snackBar.open(message, action, {\r\n      duration: duration,\r\n      panelClass: [`${type}-snackbar`],\r\n      horizontalPosition: 'right',\r\n      verticalPosition: 'top'\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get role-specific welcome message\r\n   */\r\n  getRoleSpecificText(role: string, textKey: keyof typeof ROLE_SPECIFIC_TEXT.PRINCIPAL): string {\r\n    const roleUpper = role.toUpperCase() as keyof typeof ROLE_SPECIFIC_TEXT;\r\n    return ROLE_SPECIFIC_TEXT[roleUpper]?.[textKey] || '';\r\n  }\r\n\r\n  /**\r\n   * Get user-friendly form validation error message\r\n   */\r\n  getValidationErrorMessage(fieldName: string, errorType: string, customValue?: any): string {\r\n    switch (errorType) {\r\n      case 'required':\r\n        return UX_TEXT_STANDARDS.ERROR_MESSAGES.REQUIRED_FIELD;\r\n      case 'email':\r\n        return UX_TEXT_STANDARDS.ERROR_MESSAGES.INVALID_EMAIL;\r\n      case 'minlength':\r\n        if (fieldName.toLowerCase().includes('password')) {\r\n          return UX_TEXT_STANDARDS.ERROR_MESSAGES.PASSWORD_TOO_SHORT;\r\n        }\r\n        if (fieldName.toLowerCase().includes('name')) {\r\n          return UX_TEXT_STANDARDS.ERROR_MESSAGES.NAME_TOO_SHORT;\r\n        }\r\n        return `This field must be at least ${customValue?.requiredLength || 2} characters long`;\r\n      case 'pattern':\r\n        if (fieldName.toLowerCase().includes('phone') || fieldName.toLowerCase().includes('contact')) {\r\n          return UX_TEXT_STANDARDS.ERROR_MESSAGES.INVALID_PHONE;\r\n        }\r\n        return 'Please enter a valid format';\r\n      case 'mismatch':\r\n        return UX_TEXT_STANDARDS.ERROR_MESSAGES.PASSWORDS_DONT_MATCH;\r\n      default:\r\n        return 'Please check this field and try again';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get tooltip text for form fields\r\n   */\r\n  getTooltipText(fieldKey: keyof typeof UX_TEXT_STANDARDS.TOOLTIPS): string {\r\n    return UX_TEXT_STANDARDS.TOOLTIPS[fieldKey] || '';\r\n  }\r\n\r\n  /**\r\n   * Get user-friendly label text\r\n   */\r\n  getLabelText(labelKey: keyof typeof UX_TEXT_STANDARDS.FORM_LABELS): string {\r\n    return UX_TEXT_STANDARDS.FORM_LABELS[labelKey] || '';\r\n  }\r\n\r\n  /**\r\n   * Get placeholder text\r\n   */\r\n  getPlaceholderText(placeholderKey: keyof typeof UX_TEXT_STANDARDS.PLACEHOLDERS): string {\r\n    return UX_TEXT_STANDARDS.PLACEHOLDERS[placeholderKey] || '';\r\n  }\r\n\r\n  /**\r\n   * Get button text\r\n   */\r\n  getButtonText(buttonKey: keyof typeof UX_TEXT_STANDARDS.BUTTONS): string {\r\n    return UX_TEXT_STANDARDS.BUTTONS[buttonKey] || '';\r\n  }\r\n\r\n  /**\r\n   * Show welcome message based on user role\r\n   */\r\n  showWelcomeMessage(userName: string, userRole: string): Promise<any> {\r\n    const roleSpecificWelcome = this.getRoleSpecificText(userRole, 'DASHBOARD_WELCOME');\r\n    return Swal.fire({\r\n      title: `Welcome back, ${userName}!`,\r\n      text: roleSpecificWelcome,\r\n      icon: 'success',\r\n      confirmButtonColor: '#29578c',\r\n      confirmButtonText: 'Let\\'s get started!',\r\n      timer: 4000,\r\n      timerProgressBar: true\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Show attendance warning for students\r\n   */\r\n  showAttendanceWarning(attendancePercentage: number, subjectName?: string): Promise<any> {\r\n    let message = '';\r\n    let title = '';\r\n    \r\n    if (attendancePercentage < 75) {\r\n      title = 'Attendance Alert!';\r\n      message = subjectName \r\n        ? `Your attendance in ${subjectName} is ${attendancePercentage}%. You need at least 75% to be eligible for exams.`\r\n        : `Your overall attendance is ${attendancePercentage}%. You need at least 75% to be eligible for exams.`;\r\n    } else if (attendancePercentage < 80) {\r\n      title = 'Attendance Notice';\r\n      message = subjectName\r\n        ? `Your attendance in ${subjectName} is ${attendancePercentage}%. Try to maintain above 80% for better academic standing.`\r\n        : `Your overall attendance is ${attendancePercentage}%. Try to maintain above 80% for better academic standing.`;\r\n    }\r\n\r\n    return Swal.fire({\r\n      title: title,\r\n      text: message,\r\n      icon: attendancePercentage < 75 ? 'warning' : 'info',\r\n      confirmButtonColor: '#29578c',\r\n      confirmButtonText: 'I understand'\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Show struck-off warning for BS students\r\n   */\r\n  showStruckOffWarning(subjectName: string, consecutiveAbsences: number): Promise<any> {\r\n    return Swal.fire({\r\n      title: 'Important Notice!',\r\n      text: `You have been absent for ${consecutiveAbsences} consecutive classes in ${subjectName}. You may be struck off from this subject. Please contact your teacher immediately.`,\r\n      icon: 'warning',\r\n      confirmButtonColor: '#d33',\r\n      confirmButtonText: 'Contact Teacher',\r\n      showCancelButton: true,\r\n      cancelButtonText: 'I understand'\r\n    });\r\n  }\r\n}\r\n"], "mappings": "AAEA,OAAOA,IAAI,MAAM,aAAa;AAC9B,SAASC,iBAAiB,EAAEC,kBAAkB,EAAEC,qBAAqB,QAAQ,gCAAgC;;;AAK7G,OAAM,MAAOC,eAAe;EAE1BC,YAAoBC,QAAqB;IAArB,KAAAA,QAAQ,GAARA,QAAQ;EAAiB;EAE7C;;;EAGAC,kBAAkBA,CAACC,UAA2D,EAAEC,aAAsB;IACpG,MAAMC,OAAO,GAAGD,aAAa,IAAIR,iBAAiB,CAACU,gBAAgB,CAACH,UAAU,CAAC;IAC/E,OAAOR,IAAI,CAACY,IAAI,CAAC;MACfC,KAAK,EAAE,UAAU;MACjBC,IAAI,EAAEJ,OAAO;MACbK,IAAI,EAAE,SAAS;MACfC,kBAAkB,EAAE,SAAS;MAC7BC,iBAAiB,EAAE,QAAQ;MAC3BC,KAAK,EAAE,IAAI;MACXC,gBAAgB,EAAE;KACnB,CAAC;EACJ;EAEA;;;EAGAC,gBAAgBA,CAACZ,UAAyD,EAAEC,aAAsB;IAChG,MAAMC,OAAO,GAAGD,aAAa,IAAIR,iBAAiB,CAACoB,cAAc,CAACb,UAAU,CAAC;IAC7E,OAAOR,IAAI,CAACY,IAAI,CAAC;MACfC,KAAK,EAAE,OAAO;MACdC,IAAI,EAAEJ,OAAO;MACbK,IAAI,EAAE,OAAO;MACbC,kBAAkB,EAAE,SAAS;MAC7BC,iBAAiB,EAAE;KACpB,CAAC;EACJ;EAEA;;;EAGAK,sBAAsBA,CACpBd,UAA8C,EAC9CC,aAAsB,EACtBQ,iBAAA,GAA4B,cAAc,EAC1CM,gBAAA,GAA2B,QAAQ;IAEnC,MAAMb,OAAO,GAAGD,aAAa,IAAIN,qBAAqB,CAACK,UAAU,CAAC;IAClE,OAAOR,IAAI,CAACY,IAAI,CAAC;MACfC,KAAK,EAAE,gBAAgB;MACvBC,IAAI,EAAEJ,OAAO;MACbK,IAAI,EAAE,UAAU;MAChBS,gBAAgB,EAAE,IAAI;MACtBR,kBAAkB,EAAE,SAAS;MAC7BS,iBAAiB,EAAE,SAAS;MAC5BR,iBAAiB,EAAEA,iBAAiB;MACpCM,gBAAgB,EAAEA,gBAAgB;MAClCG,cAAc,EAAE;KACjB,CAAC;EACJ;EAEA;;;EAGAC,kBAAkBA,CAACnB,UAA2D,EAAEC,aAAsB;IACpG,MAAMC,OAAO,GAAGD,aAAa,IAAIR,iBAAiB,CAAC2B,gBAAgB,CAACpB,UAAU,CAAC;IAC/ER,IAAI,CAACY,IAAI,CAAC;MACRC,KAAK,EAAE,gBAAgB;MACvBC,IAAI,EAAEJ,OAAO;MACbmB,iBAAiB,EAAE,KAAK;MACxBC,cAAc,EAAE,KAAK;MACrBC,iBAAiB,EAAE,KAAK;MACxBC,OAAO,EAAEA,CAAA,KAAK;QACZhC,IAAI,CAACiC,WAAW,EAAE;MACpB;KACD,CAAC;EACJ;EAEA;;;EAGAC,mBAAmBA,CAAA;IACjBlC,IAAI,CAACmC,KAAK,EAAE;EACd;EAEA;;;EAGAC,YAAYA,CAAC1B,OAAe,EAAE2B,MAAA,GAAiB,OAAO,EAAEC,QAAA,GAAmB,IAAI,EAAEC,IAAA,GAAqC,MAAM;IAC1H,IAAI,CAACjC,QAAQ,CAACkC,IAAI,CAAC9B,OAAO,EAAE2B,MAAM,EAAE;MAClCC,QAAQ,EAAEA,QAAQ;MAClBG,UAAU,EAAE,CAAC,GAAGF,IAAI,WAAW,CAAC;MAChCG,kBAAkB,EAAE,OAAO;MAC3BC,gBAAgB,EAAE;KACnB,CAAC;EACJ;EAEA;;;EAGAC,mBAAmBA,CAACC,IAAY,EAAEC,OAAkD;IAClF,MAAMC,SAAS,GAAGF,IAAI,CAACG,WAAW,EAAqC;IACvE,OAAO9C,kBAAkB,CAAC6C,SAAS,CAAC,GAAGD,OAAO,CAAC,IAAI,EAAE;EACvD;EAEA;;;EAGAG,yBAAyBA,CAACC,SAAiB,EAAEC,SAAiB,EAAEC,WAAiB;IAC/E,QAAQD,SAAS;MACf,KAAK,UAAU;QACb,OAAOlD,iBAAiB,CAACoB,cAAc,CAACgC,cAAc;MACxD,KAAK,OAAO;QACV,OAAOpD,iBAAiB,CAACoB,cAAc,CAACiC,aAAa;MACvD,KAAK,WAAW;QACd,IAAIJ,SAAS,CAACK,WAAW,EAAE,CAACC,QAAQ,CAAC,UAAU,CAAC,EAAE;UAChD,OAAOvD,iBAAiB,CAACoB,cAAc,CAACoC,kBAAkB;;QAE5D,IAAIP,SAAS,CAACK,WAAW,EAAE,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;UAC5C,OAAOvD,iBAAiB,CAACoB,cAAc,CAACqC,cAAc;;QAExD,OAAO,+BAA+BN,WAAW,EAAEO,cAAc,IAAI,CAAC,kBAAkB;MAC1F,KAAK,SAAS;QACZ,IAAIT,SAAS,CAACK,WAAW,EAAE,CAACC,QAAQ,CAAC,OAAO,CAAC,IAAIN,SAAS,CAACK,WAAW,EAAE,CAACC,QAAQ,CAAC,SAAS,CAAC,EAAE;UAC5F,OAAOvD,iBAAiB,CAACoB,cAAc,CAACuC,aAAa;;QAEvD,OAAO,6BAA6B;MACtC,KAAK,UAAU;QACb,OAAO3D,iBAAiB,CAACoB,cAAc,CAACwC,oBAAoB;MAC9D;QACE,OAAO,uCAAuC;;EAEpD;EAEA;;;EAGAC,cAAcA,CAACC,QAAiD;IAC9D,OAAO9D,iBAAiB,CAAC+D,QAAQ,CAACD,QAAQ,CAAC,IAAI,EAAE;EACnD;EAEA;;;EAGAE,YAAYA,CAACC,QAAoD;IAC/D,OAAOjE,iBAAiB,CAACkE,WAAW,CAACD,QAAQ,CAAC,IAAI,EAAE;EACtD;EAEA;;;EAGAE,kBAAkBA,CAACC,cAA2D;IAC5E,OAAOpE,iBAAiB,CAACqE,YAAY,CAACD,cAAc,CAAC,IAAI,EAAE;EAC7D;EAEA;;;EAGAE,aAAaA,CAACC,SAAiD;IAC7D,OAAOvE,iBAAiB,CAACwE,OAAO,CAACD,SAAS,CAAC,IAAI,EAAE;EACnD;EAEA;;;EAGAE,kBAAkBA,CAACC,QAAgB,EAAEC,QAAgB;IACnD,MAAMC,mBAAmB,GAAG,IAAI,CAACjC,mBAAmB,CAACgC,QAAQ,EAAE,mBAAmB,CAAC;IACnF,OAAO5E,IAAI,CAACY,IAAI,CAAC;MACfC,KAAK,EAAE,iBAAiB8D,QAAQ,GAAG;MACnC7D,IAAI,EAAE+D,mBAAmB;MACzB9D,IAAI,EAAE,SAAS;MACfC,kBAAkB,EAAE,SAAS;MAC7BC,iBAAiB,EAAE,qBAAqB;MACxCC,KAAK,EAAE,IAAI;MACXC,gBAAgB,EAAE;KACnB,CAAC;EACJ;EAEA;;;EAGA2D,qBAAqBA,CAACC,oBAA4B,EAAEC,WAAoB;IACtE,IAAItE,OAAO,GAAG,EAAE;IAChB,IAAIG,KAAK,GAAG,EAAE;IAEd,IAAIkE,oBAAoB,GAAG,EAAE,EAAE;MAC7BlE,KAAK,GAAG,mBAAmB;MAC3BH,OAAO,GAAGsE,WAAW,GACjB,sBAAsBA,WAAW,OAAOD,oBAAoB,oDAAoD,GAChH,8BAA8BA,oBAAoB,oDAAoD;KAC3G,MAAM,IAAIA,oBAAoB,GAAG,EAAE,EAAE;MACpClE,KAAK,GAAG,mBAAmB;MAC3BH,OAAO,GAAGsE,WAAW,GACjB,sBAAsBA,WAAW,OAAOD,oBAAoB,4DAA4D,GACxH,8BAA8BA,oBAAoB,4DAA4D;;IAGpH,OAAO/E,IAAI,CAACY,IAAI,CAAC;MACfC,KAAK,EAAEA,KAAK;MACZC,IAAI,EAAEJ,OAAO;MACbK,IAAI,EAAEgE,oBAAoB,GAAG,EAAE,GAAG,SAAS,GAAG,MAAM;MACpD/D,kBAAkB,EAAE,SAAS;MAC7BC,iBAAiB,EAAE;KACpB,CAAC;EACJ;EAEA;;;EAGAgE,oBAAoBA,CAACD,WAAmB,EAAEE,mBAA2B;IACnE,OAAOlF,IAAI,CAACY,IAAI,CAAC;MACfC,KAAK,EAAE,mBAAmB;MAC1BC,IAAI,EAAE,4BAA4BoE,mBAAmB,2BAA2BF,WAAW,qFAAqF;MAChLjE,IAAI,EAAE,SAAS;MACfC,kBAAkB,EAAE,MAAM;MAC1BC,iBAAiB,EAAE,iBAAiB;MACpCO,gBAAgB,EAAE,IAAI;MACtBD,gBAAgB,EAAE;KACnB,CAAC;EACJ;EAAC,QAAA4D,CAAA,G;qBAvNU/E,eAAe,EAAAgF,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAfpF,eAAe;IAAAqF,OAAA,EAAfrF,eAAe,CAAAsF,IAAA;IAAAC,UAAA,EAFd;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}