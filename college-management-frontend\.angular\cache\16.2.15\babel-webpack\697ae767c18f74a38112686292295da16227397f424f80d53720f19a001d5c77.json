{"ast": null, "code": "import { FormGroup, Validators } from '@angular/forms';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/timetable.service\";\nimport * as i3 from \"../../../services/program.service\";\nimport * as i4 from \"../../../services/department.service\";\nimport * as i5 from \"../../../services/classes.service\";\nimport * as i6 from \"../../../services/subject.service\";\nimport * as i7 from \"../../../services/user.service\";\nimport * as i8 from \"@angular/material/snack-bar\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"@angular/material/card\";\nimport * as i11 from \"@angular/material/button\";\nimport * as i12 from \"@angular/material/icon\";\nimport * as i13 from \"@angular/material/form-field\";\nimport * as i14 from \"@angular/material/input\";\nimport * as i15 from \"@angular/material/select\";\nimport * as i16 from \"@angular/material/core\";\nimport * as i17 from \"@angular/material/table\";\nimport * as i18 from \"@angular/material/progress-spinner\";\nimport * as i19 from \"@angular/material/tooltip\";\nfunction TimetableComponent_mat_option_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const program_r26 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", program_r26._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", program_r26.name, \" - \", program_r26.fullName, \" \");\n  }\n}\nfunction TimetableComponent_mat_error_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Program is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TimetableComponent_mat_option_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const department_r27 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", department_r27._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", department_r27.name, \" (\", department_r27.code, \") \");\n  }\n}\nfunction TimetableComponent_mat_error_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Department is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TimetableComponent_ng_container_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"mat-option\", 34);\n    i0.ɵɵtext(2, \"1st Year\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"mat-option\", 35);\n    i0.ɵɵtext(4, \"2nd Year\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction TimetableComponent_ng_container_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"mat-option\", 34);\n    i0.ɵɵtext(2, \"1st Semester\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"mat-option\", 35);\n    i0.ɵɵtext(4, \"2nd Semester\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-option\", 36);\n    i0.ɵɵtext(6, \"3rd Semester\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-option\", 37);\n    i0.ɵɵtext(8, \"4th Semester\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-option\", 38);\n    i0.ɵɵtext(10, \"5th Semester\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"mat-option\", 39);\n    i0.ɵɵtext(12, \"6th Semester\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"mat-option\", 40);\n    i0.ɵɵtext(14, \"7th Semester\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"mat-option\", 41);\n    i0.ɵɵtext(16, \"8th Semester\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction TimetableComponent_mat_error_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.isIntermediateProgram() ? \"Year\" : \"Semester\", \" is required \");\n  }\n}\nfunction TimetableComponent_mat_option_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const class_r28 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", class_r28._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate4(\" \", class_r28.className, \"\", class_r28.section ? \" - Section \" + class_r28.section : \"\", \" (\", class_r28 == null ? null : class_r28.program == null ? null : class_r28.program.name, \" - \", class_r28 == null ? null : class_r28.department == null ? null : class_r28.department.name, \") \");\n  }\n}\nfunction TimetableComponent_mat_error_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Class is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TimetableComponent_mat_option_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const subject_r29 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", subject_r29._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", subject_r29.subjectName, \" (\", subject_r29.code, \") \");\n  }\n}\nfunction TimetableComponent_mat_error_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Subject is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TimetableComponent_mat_option_64_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const teacher_r30 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"(\", teacher_r30.department.name, \")\");\n  }\n}\nfunction TimetableComponent_mat_option_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, TimetableComponent_mat_option_64_small_2_Template, 2, 1, \"small\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const teacher_r30 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", teacher_r30._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", teacher_r30.name, \" - \", teacher_r30.designation || \"Teacher\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", teacher_r30.department);\n  }\n}\nfunction TimetableComponent_mat_error_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Teacher is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TimetableComponent_mat_option_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r33 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", day_r33);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", day_r33, \" \");\n  }\n}\nfunction TimetableComponent_mat_error_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Day is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TimetableComponent_mat_option_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const time_r34 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", time_r34);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", time_r34, \" \");\n  }\n}\nfunction TimetableComponent_mat_error_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Start time is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TimetableComponent_mat_option_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const duration_r35 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", duration_r35.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", duration_r35.label, \" \");\n  }\n}\nfunction TimetableComponent_mat_error_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Duration is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TimetableComponent_mat_hint_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-hint\");\n    i0.ɵɵtext(1, \"Select a program first to see duration options\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TimetableComponent_mat_error_101_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Academic year is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TimetableComponent_button_107_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function TimetableComponent_button_107_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.timetableForm.reset());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"clear\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Clear \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TimetableComponent_button_112_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function TimetableComponent_button_112_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.cancelEdit());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Cancel Edit \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TimetableComponent_div_120_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading timetables...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TimetableComponent_div_121_th_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 57);\n    i0.ɵɵtext(1, \"Day\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TimetableComponent_div_121_td_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const timetable_r54 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(timetable_r54.dayOfWeek);\n  }\n}\nfunction TimetableComponent_div_121_th_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 57);\n    i0.ɵɵtext(1, \"Schedule Details\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TimetableComponent_div_121_td_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 58)(1, \"div\", 59)(2, \"div\", 60)(3, \"strong\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" with \");\n    i0.ɵɵelementStart(6, \"strong\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 61)(9, \"span\", 62);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 63);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 64)(14, \"small\", 65);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const timetable_r55 = ctx.$implicit;\n    const ctx_r43 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((timetable_r55.teacher == null ? null : timetable_r55.teacher.name) || \"Unknown Teacher\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r43.getClassDisplayName(timetable_r55.class));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((timetable_r55.subject == null ? null : timetable_r55.subject.subjectName) || \"Unknown Subject\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", (timetable_r55.department == null ? null : timetable_r55.department.name) || \"Unknown Dept\", \")\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r43.isIntermediateProgram() && (timetable_r55.program == null ? null : timetable_r55.program.name) === \"Intermediate\" ? ctx_r43.getSemesterDisplayText(timetable_r55.semester) + \" Year\" : ctx_r43.getSemesterDisplayText(timetable_r55.semester) + \" Semester\", \" - \", timetable_r55.academicYear, \" \");\n  }\n}\nfunction TimetableComponent_div_121_th_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 57);\n    i0.ɵɵtext(1, \"Time & Duration\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TimetableComponent_div_121_td_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 58)(1, \"div\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"small\", 66);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const timetable_r56 = ctx.$implicit;\n    const ctx_r45 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r45.formatTimeSlot(timetable_r56.timeSlot));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r45.getDurationLabel(timetable_r56.timeSlot.duration));\n  }\n}\nfunction TimetableComponent_div_121_th_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 57);\n    i0.ɵɵtext(1, \"Program\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TimetableComponent_div_121_td_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const timetable_r57 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(timetable_r57.program.name);\n  }\n}\nfunction TimetableComponent_div_121_th_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 57);\n    i0.ɵɵtext(1, \"Room\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TimetableComponent_div_121_td_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const timetable_r58 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(timetable_r58.room || \"N/A\");\n  }\n}\nfunction TimetableComponent_div_121_th_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 57);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TimetableComponent_div_121_td_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r61 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 58)(1, \"div\", 67)(2, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function TimetableComponent_div_121_td_19_Template_button_click_2_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r61);\n      const timetable_r59 = restoredCtx.$implicit;\n      const ctx_r60 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r60.editTimetableEntry(timetable_r59));\n    });\n    i0.ɵɵelementStart(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function TimetableComponent_div_121_td_19_Template_button_click_5_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r61);\n      const timetable_r59 = restoredCtx.$implicit;\n      const ctx_r62 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r62.deleteTimetableEntry(timetable_r59));\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"delete\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction TimetableComponent_div_121_tr_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 70);\n  }\n}\nfunction TimetableComponent_div_121_tr_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 71);\n  }\n}\nfunction TimetableComponent_div_121_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"table\", 46);\n    i0.ɵɵelementContainerStart(2, 47);\n    i0.ɵɵtemplate(3, TimetableComponent_div_121_th_3_Template, 2, 0, \"th\", 48);\n    i0.ɵɵtemplate(4, TimetableComponent_div_121_td_4_Template, 2, 1, \"td\", 49);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(5, 50);\n    i0.ɵɵtemplate(6, TimetableComponent_div_121_th_6_Template, 2, 0, \"th\", 48);\n    i0.ɵɵtemplate(7, TimetableComponent_div_121_td_7_Template, 16, 6, \"td\", 49);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(8, 51);\n    i0.ɵɵtemplate(9, TimetableComponent_div_121_th_9_Template, 2, 0, \"th\", 48);\n    i0.ɵɵtemplate(10, TimetableComponent_div_121_td_10_Template, 5, 2, \"td\", 49);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(11, 52);\n    i0.ɵɵtemplate(12, TimetableComponent_div_121_th_12_Template, 2, 0, \"th\", 48);\n    i0.ɵɵtemplate(13, TimetableComponent_div_121_td_13_Template, 2, 1, \"td\", 49);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(14, 53);\n    i0.ɵɵtemplate(15, TimetableComponent_div_121_th_15_Template, 2, 0, \"th\", 48);\n    i0.ɵɵtemplate(16, TimetableComponent_div_121_td_16_Template, 2, 1, \"td\", 49);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(17, 54);\n    i0.ɵɵtemplate(18, TimetableComponent_div_121_th_18_Template, 2, 0, \"th\", 48);\n    i0.ɵɵtemplate(19, TimetableComponent_div_121_td_19_Template, 8, 0, \"td\", 49);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(20, TimetableComponent_div_121_tr_20_Template, 1, 0, \"tr\", 55);\n    i0.ɵɵtemplate(21, TimetableComponent_div_121_tr_21_Template, 1, 0, \"tr\", 56);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"dataSource\", ctx_r24.timetables);\n    i0.ɵɵadvance(19);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r24.displayedColumns);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r24.displayedColumns);\n  }\n}\nfunction TimetableComponent_div_122_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No Timetable Entries\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Create your first timetable entry using the form above.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class TimetableComponent {\n  constructor(fb, timetableService, programService, departmentService, classesService, subjectService, userService, snackBar) {\n    this.fb = fb;\n    this.timetableService = timetableService;\n    this.programService = programService;\n    this.departmentService = departmentService;\n    this.classesService = classesService;\n    this.subjectService = subjectService;\n    this.userService = userService;\n    this.snackBar = snackBar;\n    this.timetables = [];\n    this.programs = [];\n    this.departments = [];\n    this.filteredDepartments = [];\n    this.classes = [];\n    this.filteredClasses = [];\n    this.subjects = [];\n    this.filteredSubjects = [];\n    this.teachers = [];\n    this.filteredTeachers = [];\n    this.loading = false;\n    this.submitting = false;\n    this.editingTimetableId = null;\n    this.daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];\n    this.baseTimeSlots = ['08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30', '12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30', '16:00', '16:30', '17:00', '17:30', '18:00'];\n    this.availableStartTimes = [];\n    this.displayedColumns = ['dayOfWeek', 'scheduleInfo', 'timeSlot', 'program', 'room', 'actions'];\n    // Program-specific duration options\n    this.programDurations = {\n      'Intermediate': [{\n        value: 40,\n        label: '40 minutes'\n      }, {\n        value: 60,\n        label: '1 hour'\n      }],\n      'BS': [{\n        value: 60,\n        label: '1 hour'\n      }, {\n        value: 90,\n        label: '1.5 hours'\n      }, {\n        value: 120,\n        label: '2 hours'\n      }],\n      'MS': [{\n        value: 60,\n        label: '1 hour'\n      }, {\n        value: 90,\n        label: '1.5 hours'\n      }, {\n        value: 120,\n        label: '2 hours'\n      }],\n      'PhD': [{\n        value: 90,\n        label: '1.5 hours'\n      }, {\n        value: 120,\n        label: '2 hours'\n      }, {\n        value: 180,\n        label: '3 hours'\n      }]\n    };\n    this.availableDurations = [];\n    this.selectedProgram = null;\n    this.timetableForm = this.fb.group({\n      program: ['', Validators.required],\n      department: ['', Validators.required],\n      class: ['', Validators.required],\n      subject: ['', Validators.required],\n      teacher: ['', Validators.required],\n      dayOfWeek: ['', Validators.required],\n      startTime: ['', Validators.required],\n      duration: ['', Validators.required],\n      endTime: [{\n        value: '',\n        disabled: true\n      }],\n      room: [''],\n      semester: ['', Validators.required],\n      academicYear: ['2024-2025', Validators.required],\n      notes: ['']\n    });\n  }\n  ngOnInit() {\n    this.loadInitialData();\n    this.setupFormSubscriptions();\n  }\n  isIntermediateProgram() {\n    const programId = this.timetableForm.get('program')?.value;\n    const selectedProgram = this.programs.find(p => p._id === programId);\n    return selectedProgram?.name === 'Intermediate';\n  }\n  getSemesterDisplayText(semester) {\n    const semesterMap = {\n      1: '1st',\n      2: '2nd',\n      3: '3rd',\n      4: '4th',\n      5: '5th',\n      6: '6th',\n      7: '7th',\n      8: '8th'\n    };\n    return semesterMap[semester] || `${semester}th`;\n  }\n  loadInitialData() {\n    this.loading = true;\n    // Load programs\n    this.programService.getAllPrograms(true).subscribe({\n      next: response => {\n        if (response.success) {\n          this.programs = response.programs;\n        }\n      },\n      error: error => console.error('Error loading programs:', error)\n    });\n    // Load teachers\n    this.userService.getUsersByRole('Teacher').subscribe({\n      next: response => {\n        if (response.success) {\n          this.teachers = response.users;\n        }\n      },\n      error: error => console.error('Error loading teachers:', error)\n    });\n    // Load existing timetables\n    this.loadTimetables();\n  }\n  setupFormSubscriptions() {\n    // When program changes, load departments and update duration options\n    this.timetableForm.get('program')?.valueChanges.subscribe(programId => {\n      if (programId) {\n        this.selectedProgram = this.programs.find(p => p._id === programId) || null;\n        this.updateAvailableDurations();\n        this.loadDepartmentsByProgram(programId);\n        this.loadTeachersByProgram(programId);\n        this.timetableForm.patchValue({\n          department: '',\n          class: '',\n          subject: '',\n          duration: '',\n          endTime: ''\n        });\n      }\n    });\n    // When department changes, load classes and subjects\n    this.timetableForm.get('department')?.valueChanges.subscribe(departmentId => {\n      if (departmentId) {\n        this.loadClassesByDepartment(departmentId);\n        this.loadSubjectsByDepartment(departmentId);\n        this.timetableForm.patchValue({\n          class: '',\n          subject: ''\n        });\n      }\n    });\n    // When start time or duration changes, calculate end time\n    this.timetableForm.get('startTime')?.valueChanges.subscribe(() => {\n      this.calculateEndTime();\n    });\n    this.timetableForm.get('duration')?.valueChanges.subscribe(() => {\n      this.calculateEndTime();\n    });\n  }\n  updateAvailableDurations() {\n    if (this.selectedProgram) {\n      this.availableDurations = this.programDurations[this.selectedProgram.name] || [];\n      this.generateAvailableStartTimes();\n    } else {\n      this.availableDurations = [];\n      this.availableStartTimes = [];\n    }\n  }\n  generateAvailableStartTimes() {\n    if (this.selectedProgram) {\n      // For different programs, we might want different time slot granularities\n      if (this.selectedProgram.name === 'Intermediate') {\n        // For Intermediate, allow more flexible timing (every 30 minutes)\n        this.availableStartTimes = this.baseTimeSlots.filter(time => {\n          const hour = parseInt(time.split(':')[0]);\n          return hour >= 8 && hour <= 16; // 8 AM to 4 PM\n        });\n      } else {\n        // For BS/MS/PhD, standard hourly slots\n        this.availableStartTimes = this.baseTimeSlots.filter(time => {\n          const [hour, minute] = time.split(':').map(Number);\n          return hour >= 8 && hour <= 17 && (minute === 0 || minute === 30); // 8 AM to 5 PM\n        });\n      }\n    } else {\n      this.availableStartTimes = this.baseTimeSlots;\n    }\n  }\n  calculateEndTime() {\n    const startTime = this.timetableForm.get('startTime')?.value;\n    const duration = this.timetableForm.get('duration')?.value;\n    if (startTime && duration) {\n      const [hours, minutes] = startTime.split(':').map(Number);\n      const startMinutes = hours * 60 + minutes;\n      const endMinutes = startMinutes + parseInt(duration);\n      const endHours = Math.floor(endMinutes / 60);\n      const endMins = endMinutes % 60;\n      const endTime = `${endHours.toString().padStart(2, '0')}:${endMins.toString().padStart(2, '0')}`;\n      this.timetableForm.get('endTime')?.setValue(endTime);\n    }\n  }\n  loadTeachers() {\n    this.userService.getUsersByRole('Teacher').subscribe({\n      next: response => {\n        if (response.success) {\n          this.teachers = response.users;\n        }\n      },\n      error: error => {\n        console.error('Error loading teachers:', error);\n        Swal.fire('Error', 'Failed to load teachers', 'error');\n      }\n    });\n  }\n  loadDepartmentsByProgram(programId) {\n    this.departmentService.getDepartmentsByProgram(programId, true).subscribe({\n      next: response => {\n        if (response.success) {\n          this.filteredDepartments = response.departments;\n        }\n      },\n      error: error => {\n        console.error('Error loading departments:', error);\n        this.filteredDepartments = [];\n      }\n    });\n  }\n  loadClassesByDepartment(departmentId) {\n    const programId = this.timetableForm.get('program')?.value;\n    this.classesService.getAllClasses({\n      program: programId,\n      department: departmentId,\n      isActive: true\n    }).subscribe({\n      next: response => {\n        if (response.success) {\n          this.filteredClasses = response.classes;\n        }\n      },\n      error: error => {\n        console.error('Error loading classes:', error);\n        this.filteredClasses = [];\n      }\n    });\n  }\n  loadSubjectsByDepartment(departmentId) {\n    this.subjectService.getSubjectsByDepartment(departmentId, true).subscribe({\n      next: response => {\n        if (response.success) {\n          this.filteredSubjects = response.subjects;\n        }\n      },\n      error: error => {\n        console.error('Error loading subjects:', error);\n        this.filteredSubjects = [];\n      }\n    });\n  }\n  loadTeachersByProgram(programId) {\n    this.userService.getUsersByProgram(programId, 'Teacher').subscribe({\n      next: response => {\n        if (response.success) {\n          this.filteredTeachers = response.users;\n        }\n      },\n      error: error => {\n        console.error('Error loading teachers by program:', error);\n        // Fallback to all teachers\n        this.filteredTeachers = this.teachers;\n      }\n    });\n  }\n  loadTimetables() {\n    this.timetableService.getAllTimetableEntries().subscribe({\n      next: response => {\n        if (response.success) {\n          this.timetables = response.timetable;\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading timetables:', error);\n        this.loading = false;\n      }\n    });\n  }\n  onSubmit() {\n    if (this.timetableForm.valid) {\n      this.submitting = true;\n      const formData = this.timetableForm.value;\n      // Ensure proper data structure for backend\n      const timetableData = {\n        program: formData.program,\n        department: formData.department,\n        class: formData.class,\n        subject: formData.subject,\n        teacher: formData.teacher,\n        dayOfWeek: formData.dayOfWeek,\n        timeSlot: {\n          startTime: formData.startTime,\n          endTime: formData.endTime,\n          duration: parseInt(formData.duration)\n        },\n        room: formData.room || '',\n        semester: parseInt(formData.semester),\n        academicYear: formData.academicYear,\n        notes: formData.notes || ''\n      };\n      console.log('Submitting timetable data:', timetableData); // Debug log\n      const request = this.editingTimetableId ? this.timetableService.updateTimetableEntry(this.editingTimetableId, timetableData) : this.timetableService.createTimetableEntry(timetableData);\n      request.subscribe({\n        next: response => {\n          if (response.success) {\n            const action = this.editingTimetableId ? 'updated' : 'created';\n            this.snackBar.open(`Timetable entry ${action} successfully`, 'Close', {\n              duration: 3000\n            });\n            this.timetableForm.reset();\n            this.timetableForm.patchValue({\n              academicYear: '2024-2025'\n            });\n            this.editingTimetableId = null;\n            this.loadTimetables();\n          } else {\n            const action = this.editingTimetableId ? 'update' : 'create';\n            this.snackBar.open(response.message || `Failed to ${action} timetable entry`, 'Close', {\n              duration: 3000\n            });\n          }\n          this.submitting = false;\n        },\n        error: error => {\n          const action = this.editingTimetableId ? 'updating' : 'creating';\n          console.error(`Error ${action} timetable entry:`, error);\n          this.snackBar.open(error.error?.message || `Error ${action} timetable entry`, 'Close', {\n            duration: 3000\n          });\n          this.submitting = false;\n        }\n      });\n    } else {\n      console.log('Form is invalid:', this.timetableForm.errors);\n      this.markFormGroupTouched(this.timetableForm);\n    }\n  }\n  markFormGroupTouched(formGroup) {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      control?.markAsTouched();\n      if (control instanceof FormGroup) {\n        this.markFormGroupTouched(control);\n      }\n    });\n  }\n  editTimetableEntry(timetable) {\n    this.editingTimetableId = timetable._id;\n    // Load related data first\n    this.loadDepartmentsByProgram(timetable.program._id);\n    this.loadClassesByDepartment(timetable.department._id);\n    this.loadSubjectsByDepartment(timetable.department._id);\n    this.loadTeachersByProgram(timetable.program._id);\n    // Populate form with existing data\n    setTimeout(() => {\n      this.timetableForm.patchValue({\n        program: timetable.program._id,\n        department: timetable.department._id,\n        class: timetable.class._id,\n        subject: timetable.subject._id,\n        teacher: timetable.teacher._id,\n        dayOfWeek: timetable.dayOfWeek,\n        startTime: timetable.timeSlot.startTime,\n        endTime: timetable.timeSlot.endTime,\n        duration: timetable.timeSlot.duration,\n        room: timetable.room || '',\n        semester: timetable.semester,\n        academicYear: timetable.academicYear,\n        notes: timetable.notes || ''\n      });\n      // Calculate end time to ensure consistency\n      this.calculateEndTime();\n    }, 1000);\n    // Scroll to form\n    document.querySelector('.form-card')?.scrollIntoView({\n      behavior: 'smooth'\n    });\n  }\n  cancelEdit() {\n    this.editingTimetableId = null;\n    this.timetableForm.reset();\n    this.timetableForm.patchValue({\n      academicYear: '2024-2025'\n    });\n  }\n  deleteTimetableEntry(timetable) {\n    if (confirm('Are you sure you want to delete this timetable entry?')) {\n      this.timetableService.deleteTimetableEntry(timetable._id).subscribe({\n        next: response => {\n          if (response.success) {\n            this.snackBar.open('Timetable entry deleted successfully', 'Close', {\n              duration: 3000\n            });\n            this.loadTimetables();\n          }\n        },\n        error: error => {\n          console.error('Error deleting timetable entry:', error);\n          this.snackBar.open('Error deleting timetable entry', 'Close', {\n            duration: 3000\n          });\n        }\n      });\n    }\n  }\n  formatTimeSlot(timeSlot) {\n    return `${timeSlot.startTime} - ${timeSlot.endTime}`;\n  }\n  formatTimetableEntry(timetable) {\n    const timeSlot = this.formatTimeSlot(timetable.timeSlot);\n    const teacherName = timetable.teacher?.name || 'Unknown Teacher';\n    const className = timetable.class?.className || 'Unknown Class';\n    const programName = timetable.program?.name || '';\n    // Format based on program type\n    if (programName === 'Intermediate') {\n      return `${timeSlot} ${teacherName} with ${className}`;\n    } else {\n      return `${timeSlot} ${teacherName} with ${className}`;\n    }\n  }\n  getClassDisplayName(classData) {\n    if (!classData) return 'Unknown Class';\n    const program = classData.program?.name || '';\n    if (program === 'Intermediate') {\n      return `${classData.className}${classData.section ? ' - Section ' + classData.section : ''}`;\n    } else {\n      return classData.className;\n    }\n  }\n  getDurationLabel(duration) {\n    if (duration < 60) {\n      return `${duration} min`;\n    } else if (duration === 60) {\n      return '1 hour';\n    } else if (duration === 90) {\n      return '1.5 hours';\n    } else if (duration === 120) {\n      return '2 hours';\n    } else if (duration === 180) {\n      return '3 hours';\n    } else {\n      return `${Math.floor(duration / 60)}h ${duration % 60}m`;\n    }\n  }\n  static #_ = this.ɵfac = function TimetableComponent_Factory(t) {\n    return new (t || TimetableComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.TimetableService), i0.ɵɵdirectiveInject(i3.ProgramService), i0.ɵɵdirectiveInject(i4.DepartmentService), i0.ɵɵdirectiveInject(i5.ClassesService), i0.ɵɵdirectiveInject(i6.SubjectService), i0.ɵɵdirectiveInject(i7.UserService), i0.ɵɵdirectiveInject(i8.MatSnackBar));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: TimetableComponent,\n    selectors: [[\"app-timetable\"]],\n    decls: 123,\n    vars: 41,\n    consts: [[1, \"timetable-container\"], [1, \"page-header\"], [1, \"header-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Refresh\", 3, \"click\"], [1, \"form-card\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"form-grid\"], [\"appearance\", \"outline\"], [\"formControlName\", \"program\", \"required\", \"\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [\"formControlName\", \"department\", \"required\", \"\", 3, \"disabled\"], [\"formControlName\", \"semester\", \"required\", \"\"], [\"formControlName\", \"class\", \"required\", \"\", 3, \"disabled\"], [\"formControlName\", \"subject\", \"required\", \"\", 3, \"disabled\"], [\"formControlName\", \"teacher\", \"required\", \"\", 3, \"disabled\"], [\"formControlName\", \"dayOfWeek\", \"required\", \"\"], [\"formControlName\", \"startTime\", \"required\", \"\"], [\"formControlName\", \"duration\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"endTime\", \"readonly\", \"\"], [\"matInput\", \"\", \"formControlName\", \"room\", \"placeholder\", \"e.g., Room 101\"], [\"matInput\", \"\", \"formControlName\", \"academicYear\", \"placeholder\", \"e.g., 2024-2025\", \"required\", \"\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"notes\", \"rows\", \"3\", \"placeholder\", \"Additional notes...\"], [1, \"form-actions\"], [\"mat-raised-button\", \"\", \"type\", \"button\", 3, \"click\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [\"mat-button\", \"\", \"type\", \"button\", \"color\", \"warn\", 3, \"click\", 4, \"ngIf\"], [1, \"table-card\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"table-container\", 4, \"ngIf\"], [\"class\", \"no-data\", 4, \"ngIf\"], [3, \"value\"], [\"value\", \"1\"], [\"value\", \"2\"], [\"value\", \"3\"], [\"value\", \"4\"], [\"value\", \"5\"], [\"value\", \"6\"], [\"value\", \"7\"], [\"value\", \"8\"], [\"mat-raised-button\", \"\", \"type\", \"button\", 3, \"click\"], [\"mat-button\", \"\", \"type\", \"button\", \"color\", \"warn\", 3, \"click\"], [1, \"loading-container\"], [1, \"table-container\"], [\"mat-table\", \"\", 1, \"timetable-table\", 3, \"dataSource\"], [\"matColumnDef\", \"dayOfWeek\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"scheduleInfo\"], [\"matColumnDef\", \"timeSlot\"], [\"matColumnDef\", \"program\"], [\"matColumnDef\", \"room\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [1, \"schedule-info\"], [1, \"teacher-class\"], [1, \"subject-info\"], [1, \"subject\"], [1, \"department\", \"text-muted\"], [1, \"semester-info\"], [1, \"text-info\"], [1, \"text-muted\"], [1, \"action-buttons\"], [\"mat-icon-button\", \"\", \"color\", \"primary\", \"matTooltip\", \"Edit\", 3, \"click\"], [\"mat-icon-button\", \"\", \"color\", \"warn\", \"matTooltip\", \"Delete\", 3, \"click\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"], [1, \"no-data\"]],\n    template: function TimetableComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n        i0.ɵɵtext(3, \"Timetable Management\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\", 2)(5, \"button\", 3);\n        i0.ɵɵlistener(\"click\", function TimetableComponent_Template_button_click_5_listener() {\n          return ctx.loadTimetables();\n        });\n        i0.ɵɵelementStart(6, \"mat-icon\");\n        i0.ɵɵtext(7, \"refresh\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(8, \"mat-card\", 4)(9, \"mat-card-header\")(10, \"mat-card-title\");\n        i0.ɵɵtext(11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"mat-card-subtitle\");\n        i0.ɵɵtext(13);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(14, \"mat-card-content\")(15, \"form\", 5);\n        i0.ɵɵlistener(\"ngSubmit\", function TimetableComponent_Template_form_ngSubmit_15_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵelementStart(16, \"div\", 6)(17, \"mat-form-field\", 7)(18, \"mat-label\");\n        i0.ɵɵtext(19, \"Program\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"mat-select\", 8)(21, \"mat-option\", 9);\n        i0.ɵɵtext(22, \"Select Program\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(23, TimetableComponent_mat_option_23_Template, 2, 3, \"mat-option\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(24, TimetableComponent_mat_error_24_Template, 2, 0, \"mat-error\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"mat-form-field\", 7)(26, \"mat-label\");\n        i0.ɵɵtext(27, \"Department\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(28, \"mat-select\", 12)(29, \"mat-option\", 9);\n        i0.ɵɵtext(30);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(31, TimetableComponent_mat_option_31_Template, 2, 3, \"mat-option\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(32, TimetableComponent_mat_error_32_Template, 2, 0, \"mat-error\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(33, \"mat-form-field\", 7)(34, \"mat-label\");\n        i0.ɵɵtext(35);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(36, \"mat-select\", 13)(37, \"mat-option\", 9);\n        i0.ɵɵtext(38);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(39, TimetableComponent_ng_container_39_Template, 5, 0, \"ng-container\", 11);\n        i0.ɵɵtemplate(40, TimetableComponent_ng_container_40_Template, 17, 0, \"ng-container\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(41, TimetableComponent_mat_error_41_Template, 2, 1, \"mat-error\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(42, \"mat-form-field\", 7)(43, \"mat-label\");\n        i0.ɵɵtext(44, \"Class\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(45, \"mat-select\", 14)(46, \"mat-option\", 9);\n        i0.ɵɵtext(47);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(48, TimetableComponent_mat_option_48_Template, 2, 5, \"mat-option\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(49, TimetableComponent_mat_error_49_Template, 2, 0, \"mat-error\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(50, \"mat-form-field\", 7)(51, \"mat-label\");\n        i0.ɵɵtext(52, \"Subject\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(53, \"mat-select\", 15)(54, \"mat-option\", 9);\n        i0.ɵɵtext(55);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(56, TimetableComponent_mat_option_56_Template, 2, 3, \"mat-option\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(57, TimetableComponent_mat_error_57_Template, 2, 0, \"mat-error\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(58, \"mat-form-field\", 7)(59, \"mat-label\");\n        i0.ɵɵtext(60, \"Teacher\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(61, \"mat-select\", 16)(62, \"mat-option\", 9);\n        i0.ɵɵtext(63);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(64, TimetableComponent_mat_option_64_Template, 3, 4, \"mat-option\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(65, TimetableComponent_mat_error_65_Template, 2, 0, \"mat-error\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(66, \"mat-form-field\", 7)(67, \"mat-label\");\n        i0.ɵɵtext(68, \"Day of Week\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(69, \"mat-select\", 17);\n        i0.ɵɵtemplate(70, TimetableComponent_mat_option_70_Template, 2, 2, \"mat-option\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(71, TimetableComponent_mat_error_71_Template, 2, 0, \"mat-error\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(72, \"mat-form-field\", 7)(73, \"mat-label\");\n        i0.ɵɵtext(74, \"Start Time\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(75, \"mat-select\", 18);\n        i0.ɵɵtemplate(76, TimetableComponent_mat_option_76_Template, 2, 2, \"mat-option\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(77, TimetableComponent_mat_error_77_Template, 2, 0, \"mat-error\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(78, \"mat-form-field\", 7)(79, \"mat-label\");\n        i0.ɵɵtext(80, \"Class Duration\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(81, \"mat-select\", 19)(82, \"mat-option\", 9);\n        i0.ɵɵtext(83, \"Select Duration\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(84, TimetableComponent_mat_option_84_Template, 2, 2, \"mat-option\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(85, TimetableComponent_mat_error_85_Template, 2, 0, \"mat-error\", 11);\n        i0.ɵɵtemplate(86, TimetableComponent_mat_hint_86_Template, 2, 0, \"mat-hint\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(87, \"mat-form-field\", 7)(88, \"mat-label\");\n        i0.ɵɵtext(89, \"End Time (Auto-calculated)\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(90, \"input\", 20);\n        i0.ɵɵelementStart(91, \"mat-hint\");\n        i0.ɵɵtext(92, \"Automatically calculated based on start time and duration\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(93, \"mat-form-field\", 7)(94, \"mat-label\");\n        i0.ɵɵtext(95, \"Room (Optional)\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(96, \"input\", 21);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(97, \"mat-form-field\", 7)(98, \"mat-label\");\n        i0.ɵɵtext(99, \"Academic Year\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(100, \"input\", 22);\n        i0.ɵɵtemplate(101, TimetableComponent_mat_error_101_Template, 2, 0, \"mat-error\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(102, \"mat-form-field\", 23)(103, \"mat-label\");\n        i0.ɵɵtext(104, \"Notes (Optional)\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(105, \"textarea\", 24);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(106, \"div\", 25);\n        i0.ɵɵtemplate(107, TimetableComponent_button_107_Template, 4, 0, \"button\", 26);\n        i0.ɵɵelementStart(108, \"button\", 27)(109, \"mat-icon\");\n        i0.ɵɵtext(110, \"save\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(111);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(112, TimetableComponent_button_112_Template, 4, 0, \"button\", 28);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(113, \"mat-card\", 29)(114, \"mat-card-header\")(115, \"mat-card-title\");\n        i0.ɵɵtext(116, \"Current Timetable\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(117, \"mat-card-subtitle\");\n        i0.ɵɵtext(118, \"All scheduled classes\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(119, \"mat-card-content\");\n        i0.ɵɵtemplate(120, TimetableComponent_div_120_Template, 4, 0, \"div\", 30);\n        i0.ɵɵtemplate(121, TimetableComponent_div_121_Template, 22, 3, \"div\", 31);\n        i0.ɵɵtemplate(122, TimetableComponent_div_122_Template, 7, 0, \"div\", 32);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        let tmp_4_0;\n        let tmp_5_0;\n        let tmp_6_0;\n        let tmp_8_0;\n        let tmp_13_0;\n        let tmp_14_0;\n        let tmp_15_0;\n        let tmp_17_0;\n        let tmp_18_0;\n        let tmp_19_0;\n        let tmp_21_0;\n        let tmp_22_0;\n        let tmp_23_0;\n        let tmp_25_0;\n        let tmp_27_0;\n        let tmp_29_0;\n        let tmp_31_0;\n        let tmp_33_0;\n        i0.ɵɵadvance(11);\n        i0.ɵɵtextInterpolate1(\" \", ctx.editingTimetableId ? \"Edit Timetable Entry\" : \"Create Timetable Entry\", \" \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\" \", ctx.editingTimetableId ? \"Update existing class session\" : \"Schedule a new class session\", \" \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"formGroup\", ctx.timetableForm);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"ngForOf\", ctx.programs);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx.timetableForm.get(\"program\")) == null ? null : tmp_4_0.hasError(\"required\"));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"disabled\", !((tmp_5_0 = ctx.timetableForm.get(\"program\")) == null ? null : tmp_5_0.value));\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(!((tmp_6_0 = ctx.timetableForm.get(\"program\")) == null ? null : tmp_6_0.value) ? \"Select Program First\" : \"Select Department\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngForOf\", ctx.filteredDepartments);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (tmp_8_0 = ctx.timetableForm.get(\"department\")) == null ? null : tmp_8_0.hasError(\"required\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.isIntermediateProgram() ? \"Academic Year\" : \"Semester\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.isIntermediateProgram() ? \"Select Year\" : \"Select Semester\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isIntermediateProgram());\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isIntermediateProgram());\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (tmp_13_0 = ctx.timetableForm.get(\"semester\")) == null ? null : tmp_13_0.hasError(\"required\"));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"disabled\", !((tmp_14_0 = ctx.timetableForm.get(\"department\")) == null ? null : tmp_14_0.value));\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(!((tmp_15_0 = ctx.timetableForm.get(\"department\")) == null ? null : tmp_15_0.value) ? \"Select Department First\" : \"Select Class\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngForOf\", ctx.filteredClasses);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (tmp_17_0 = ctx.timetableForm.get(\"class\")) == null ? null : tmp_17_0.hasError(\"required\"));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"disabled\", !((tmp_18_0 = ctx.timetableForm.get(\"department\")) == null ? null : tmp_18_0.value));\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(!((tmp_19_0 = ctx.timetableForm.get(\"department\")) == null ? null : tmp_19_0.value) ? \"Select Department First\" : \"Select Subject\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngForOf\", ctx.filteredSubjects);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (tmp_21_0 = ctx.timetableForm.get(\"subject\")) == null ? null : tmp_21_0.hasError(\"required\"));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"disabled\", !((tmp_22_0 = ctx.timetableForm.get(\"program\")) == null ? null : tmp_22_0.value));\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(!((tmp_23_0 = ctx.timetableForm.get(\"program\")) == null ? null : tmp_23_0.value) ? \"Select Program First\" : \"Select Teacher\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngForOf\", ctx.filteredTeachers);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (tmp_25_0 = ctx.timetableForm.get(\"teacher\")) == null ? null : tmp_25_0.hasError(\"required\"));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngForOf\", ctx.daysOfWeek);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (tmp_27_0 = ctx.timetableForm.get(\"dayOfWeek\")) == null ? null : tmp_27_0.hasError(\"required\"));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngForOf\", ctx.availableStartTimes);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (tmp_29_0 = ctx.timetableForm.get(\"startTime\")) == null ? null : tmp_29_0.hasError(\"required\"));\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngForOf\", ctx.availableDurations);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (tmp_31_0 = ctx.timetableForm.get(\"duration\")) == null ? null : tmp_31_0.hasError(\"required\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.selectedProgram);\n        i0.ɵɵadvance(15);\n        i0.ɵɵproperty(\"ngIf\", (tmp_33_0 = ctx.timetableForm.get(\"academicYear\")) == null ? null : tmp_33_0.hasError(\"required\"));\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngIf\", !ctx.editingTimetableId);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"disabled\", !ctx.timetableForm.valid || ctx.submitting);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", ctx.submitting ? ctx.editingTimetableId ? \"Updating...\" : \"Creating...\" : ctx.editingTimetableId ? \"Update Entry\" : \"Create Entry\", \" \");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.editingTimetableId);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.timetables.length > 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.timetables.length === 0);\n      }\n    },\n    dependencies: [i9.NgForOf, i9.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormGroupDirective, i1.FormControlName, i10.MatCard, i10.MatCardContent, i10.MatCardHeader, i10.MatCardSubtitle, i10.MatCardTitle, i11.MatButton, i11.MatIconButton, i12.MatIcon, i13.MatFormField, i13.MatLabel, i13.MatHint, i13.MatError, i14.MatInput, i15.MatSelect, i16.MatOption, i17.MatTable, i17.MatHeaderCellDef, i17.MatHeaderRowDef, i17.MatColumnDef, i17.MatCellDef, i17.MatRowDef, i17.MatHeaderCell, i17.MatCell, i17.MatHeaderRow, i17.MatRow, i18.MatProgressSpinner, i19.MatTooltip],\n    styles: [\".timetable-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background-color: #f5f5f5;\\n  min-height: 100vh;\\n}\\n\\n.page-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 30px;\\n  background: white;\\n  padding: 20px;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\\n}\\n\\n.page-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #333;\\n  font-weight: 500;\\n}\\nth[_ngcontent-%COMP%]{\\n  color: green !important;\\n}\\n\\n.form-card[_ngcontent-%COMP%], .table-card[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\\n}\\n\\n.form-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 20px;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  grid-column: 1 / -1;\\n}\\n\\n.form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  justify-content: flex-end;\\n  margin-top: 20px;\\n}\\n\\n.form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px;\\n  text-align: center;\\n}\\n\\n.table-container[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n}\\n\\n.timetable-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  min-width: 800px;\\n}\\n\\n.no-data[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 60px 20px;\\n  text-align: center;\\n  color: #666;\\n}\\n\\n.no-data[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  height: 64px;\\n  width: 64px;\\n  margin-bottom: 16px;\\n  color: #ccc;\\n}\\n\\n.no-data[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #333;\\n}\\n\\n.no-data[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n}\\n\\n\\n\\n.mat-form-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.mat-form-field-appearance-outline[_ngcontent-%COMP%]   .mat-form-field-outline[_ngcontent-%COMP%] {\\n  color: #e0e0e0;\\n}\\n\\n.mat-form-field-appearance-outline.mat-focused[_ngcontent-%COMP%]   .mat-form-field-outline-thick[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n}\\n\\n\\n\\n.mat-table[_ngcontent-%COMP%] {\\n  background: transparent;\\n}\\n\\n.mat-header-cell[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #333;\\n  border-bottom: 2px solid #e0e0e0;\\n}\\n\\n.mat-cell[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n\\n.mat-row[_ngcontent-%COMP%]:hover {\\n  background-color: #f9f9f9;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .timetable-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  \\n  .page-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 16px;\\n    align-items: stretch;\\n  }\\n  \\n  .form-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  \\n  .form-actions[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n  \\n  .timetable-table[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n}\\n\\n\\n\\n.schedule-info[_ngcontent-%COMP%] {\\n  line-height: 1.4;\\n}\\n\\n.teacher-class[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  margin-bottom: 4px;\\n}\\n\\n.teacher-class[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n}\\n\\n.subject-info[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n\\n.subject[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-weight: 500;\\n}\\n\\n.department[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  margin-left: 8px;\\n}\\n\\n.text-muted[_ngcontent-%COMP%] {\\n  color: #666 !important;\\n}\\n\\n\\n\\n.mat-cell[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 11px;\\n  color: #666;\\n  margin-top: 2px;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["FormGroup", "Validators", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "program_r26", "_id", "ɵɵadvance", "ɵɵtextInterpolate2", "name", "fullName", "department_r27", "code", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "ɵɵtextInterpolate1", "ctx_r6", "isIntermediateProgram", "class_r28", "ɵɵtextInterpolate4", "className", "section", "program", "department", "subject_r29", "subjectName", "teacher_r30", "ɵɵtemplate", "TimetableComponent_mat_option_64_small_2_Template", "designation", "day_r33", "time_r34", "duration_r35", "value", "label", "ɵɵlistener", "TimetableComponent_button_107_Template_button_click_0_listener", "ɵɵrestoreView", "_r37", "ctx_r36", "ɵɵnextContext", "ɵɵresetView", "timetableForm", "reset", "TimetableComponent_button_112_Template_button_click_0_listener", "_r39", "ctx_r38", "cancelEdit", "ɵɵelement", "ɵɵtextInterpolate", "timetable_r54", "dayOfWeek", "timetable_r55", "teacher", "ctx_r43", "getClassDisplayName", "class", "subject", "getSemesterDisplayText", "semester", "academicYear", "ctx_r45", "formatTimeSlot", "timetable_r56", "timeSlot", "getDurationLabel", "duration", "timetable_r57", "timetable_r58", "room", "TimetableComponent_div_121_td_19_Template_button_click_2_listener", "restoredCtx", "_r61", "timetable_r59", "$implicit", "ctx_r60", "editTimetableEntry", "TimetableComponent_div_121_td_19_Template_button_click_5_listener", "ctx_r62", "deleteTimetableEntry", "TimetableComponent_div_121_th_3_Template", "TimetableComponent_div_121_td_4_Template", "TimetableComponent_div_121_th_6_Template", "TimetableComponent_div_121_td_7_Template", "TimetableComponent_div_121_th_9_Template", "TimetableComponent_div_121_td_10_Template", "TimetableComponent_div_121_th_12_Template", "TimetableComponent_div_121_td_13_Template", "TimetableComponent_div_121_th_15_Template", "TimetableComponent_div_121_td_16_Template", "TimetableComponent_div_121_th_18_Template", "TimetableComponent_div_121_td_19_Template", "TimetableComponent_div_121_tr_20_Template", "TimetableComponent_div_121_tr_21_Template", "ctx_r24", "timetables", "displayedColumns", "TimetableComponent", "constructor", "fb", "timetableService", "programService", "departmentService", "classesService", "subjectService", "userService", "snackBar", "programs", "departments", "filteredDepartments", "classes", "filteredClasses", "subjects", "filteredSubjects", "teachers", "filteredTeachers", "loading", "submitting", "editingTimetableId", "daysOfWeek", "baseTimeSlots", "availableStartTimes", "programDurations", "availableDurations", "selectedProgram", "group", "required", "startTime", "endTime", "disabled", "notes", "ngOnInit", "loadInitialData", "setupFormSubscriptions", "programId", "get", "find", "p", "semesterMap", "getAllPrograms", "subscribe", "next", "response", "success", "error", "console", "getUsersByRole", "users", "loadTimetables", "valueChanges", "updateAvailableDurations", "loadDepartmentsByProgram", "loadTeachersByProgram", "patchValue", "departmentId", "loadClassesByDepartment", "loadSubjectsByDepartment", "calculateEndTime", "generateAvailableStartTimes", "filter", "time", "hour", "parseInt", "split", "minute", "map", "Number", "hours", "minutes", "startMinutes", "endMinutes", "endHours", "Math", "floor", "endMins", "toString", "padStart", "setValue", "loadTeachers", "fire", "getDepartmentsByProgram", "getAllClasses", "isActive", "getSubjectsByDepartment", "getUsersByProgram", "getAllTimetableEntries", "timetable", "onSubmit", "valid", "formData", "timetableData", "log", "request", "updateTimetableEntry", "createTimetableEntry", "action", "open", "message", "errors", "markFormGroupTouched", "formGroup", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "setTimeout", "document", "querySelector", "scrollIntoView", "behavior", "confirm", "formatTimetableEntry", "<PERSON><PERSON><PERSON>", "programName", "classData", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "TimetableService", "i3", "ProgramService", "i4", "DepartmentService", "i5", "ClassesService", "i6", "SubjectService", "i7", "UserService", "i8", "MatSnackBar", "_2", "selectors", "decls", "vars", "consts", "template", "TimetableComponent_Template", "rf", "ctx", "TimetableComponent_Template_button_click_5_listener", "TimetableComponent_Template_form_ngSubmit_15_listener", "TimetableComponent_mat_option_23_Template", "TimetableComponent_mat_error_24_Template", "TimetableComponent_mat_option_31_Template", "TimetableComponent_mat_error_32_Template", "TimetableComponent_ng_container_39_Template", "TimetableComponent_ng_container_40_Template", "TimetableComponent_mat_error_41_Template", "TimetableComponent_mat_option_48_Template", "TimetableComponent_mat_error_49_Template", "TimetableComponent_mat_option_56_Template", "TimetableComponent_mat_error_57_Template", "TimetableComponent_mat_option_64_Template", "TimetableComponent_mat_error_65_Template", "TimetableComponent_mat_option_70_Template", "TimetableComponent_mat_error_71_Template", "TimetableComponent_mat_option_76_Template", "TimetableComponent_mat_error_77_Template", "TimetableComponent_mat_option_84_Template", "TimetableComponent_mat_error_85_Template", "TimetableComponent_mat_hint_86_Template", "TimetableComponent_mat_error_101_Template", "TimetableComponent_button_107_Template", "TimetableComponent_button_112_Template", "TimetableComponent_div_120_Template", "TimetableComponent_div_121_Template", "TimetableComponent_div_122_Template", "tmp_4_0", "<PERSON><PERSON><PERSON><PERSON>", "tmp_5_0", "tmp_6_0", "tmp_8_0", "tmp_13_0", "tmp_14_0", "tmp_15_0", "tmp_17_0", "tmp_18_0", "tmp_19_0", "tmp_21_0", "tmp_22_0", "tmp_23_0", "tmp_25_0", "tmp_27_0", "tmp_29_0", "tmp_31_0", "tmp_33_0", "length"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\timetable\\timetable.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\timetable\\timetable.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { TimetableService } from '../../../services/timetable.service';\r\nimport { ProgramService } from '../../../services/program.service';\r\nimport { DepartmentService } from '../../../services/department.service';\r\nimport { ClassesService } from '../../../services/classes.service';\r\nimport { SubjectService } from '../../../services/subject.service';\r\nimport { UserService } from '../../../services/user.service';\r\nimport { Timetable } from '../../../models/timetable';\r\nimport { Program, Department, Class, Subject, User } from '../../../models/user';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-timetable',\r\n  templateUrl: './timetable.component.html',\r\n  styleUrls: ['./timetable.component.css']\r\n})\r\nexport class TimetableComponent implements OnInit {\r\n  timetableForm: FormGroup;\r\n  timetables: Timetable[] = [];\r\n  programs: Program[] = [];\r\n  departments: Department[] = [];\r\n  filteredDepartments: Department[] = [];\r\n  classes: Class[] = [];\r\n  filteredClasses: Class[] = [];\r\n  subjects: Subject[] = [];\r\n  filteredSubjects: Subject[] = [];\r\n  teachers: User[] = [];\r\n  filteredTeachers: User[] = [];\r\n\r\n  loading = false;\r\n  submitting = false;\r\n  editingTimetableId: string | null = null;\r\n  \r\n  daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];\r\n  baseTimeSlots = [\r\n    '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',\r\n    '12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30',\r\n    '16:00', '16:30', '17:00', '17:30', '18:00'\r\n  ];\r\n\r\n  availableStartTimes: string[] = [];\r\n\r\n  displayedColumns: string[] = ['dayOfWeek', 'scheduleInfo', 'timeSlot', 'program', 'room', 'actions'];\r\n\r\n  // Program-specific duration options\r\n  programDurations: { [key: string]: { value: number; label: string }[] } = {\r\n    'Intermediate': [\r\n      { value: 40, label: '40 minutes' },\r\n      { value: 60, label: '1 hour' }\r\n    ],\r\n    'BS': [\r\n      { value: 60, label: '1 hour' },\r\n      { value: 90, label: '1.5 hours' },\r\n      { value: 120, label: '2 hours' }\r\n    ],\r\n    'MS': [\r\n      { value: 60, label: '1 hour' },\r\n      { value: 90, label: '1.5 hours' },\r\n      { value: 120, label: '2 hours' }\r\n    ],\r\n    'PhD': [\r\n      { value: 90, label: '1.5 hours' },\r\n      { value: 120, label: '2 hours' },\r\n      { value: 180, label: '3 hours' }\r\n    ]\r\n  };\r\n\r\n  availableDurations: { value: number; label: string }[] = [];\r\n  selectedProgram: Program | null = null;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private timetableService: TimetableService,\r\n    private programService: ProgramService,\r\n    private departmentService: DepartmentService,\r\n    private classesService: ClassesService,\r\n    private subjectService: SubjectService,\r\n    private userService: UserService,\r\n    private snackBar: MatSnackBar\r\n  ) {\r\n    this.timetableForm = this.fb.group({\r\n      program: ['', Validators.required],\r\n      department: ['', Validators.required],\r\n      class: ['', Validators.required],\r\n      subject: ['', Validators.required],\r\n      teacher: ['', Validators.required],\r\n      dayOfWeek: ['', Validators.required],\r\n      startTime: ['', Validators.required],\r\n      duration: ['', Validators.required],\r\n      endTime: [{ value: '', disabled: true }], // Auto-calculated\r\n      room: [''],\r\n      semester: ['', Validators.required],\r\n      academicYear: ['2024-2025', Validators.required],\r\n      notes: ['']\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.loadInitialData();\r\n    this.setupFormSubscriptions();\r\n  }\r\n\r\n  isIntermediateProgram(): boolean {\r\n    const programId = this.timetableForm.get('program')?.value;\r\n    const selectedProgram = this.programs.find(p => p._id === programId);\r\n    return selectedProgram?.name === 'Intermediate';\r\n  }\r\n\r\n  getSemesterDisplayText(semester: number): string {\r\n    const semesterMap: { [key: number]: string } = {\r\n      1: '1st', 2: '2nd', 3: '3rd', 4: '4th',\r\n      5: '5th', 6: '6th', 7: '7th', 8: '8th'\r\n    };\r\n    return semesterMap[semester] || `${semester}th`;\r\n  }\r\n\r\n  loadInitialData(): void {\r\n    this.loading = true;\r\n    \r\n    // Load programs\r\n    this.programService.getAllPrograms(true).subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.programs = response.programs;\r\n        }\r\n      },\r\n      error: (error) => console.error('Error loading programs:', error)\r\n    });\r\n\r\n    // Load teachers\r\n    this.userService.getUsersByRole('Teacher').subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.teachers = response.users;\r\n        }\r\n      },\r\n      error: (error) => console.error('Error loading teachers:', error)\r\n    });\r\n\r\n    // Load existing timetables\r\n    this.loadTimetables();\r\n  }\r\n\r\n  setupFormSubscriptions(): void {\r\n    // When program changes, load departments and update duration options\r\n    this.timetableForm.get('program')?.valueChanges.subscribe(programId => {\r\n      if (programId) {\r\n        this.selectedProgram = this.programs.find(p => p._id === programId) || null;\r\n        this.updateAvailableDurations();\r\n        this.loadDepartmentsByProgram(programId);\r\n        this.loadTeachersByProgram(programId);\r\n        this.timetableForm.patchValue({\r\n          department: '',\r\n          class: '',\r\n          subject: '',\r\n          duration: '',\r\n          endTime: ''\r\n        });\r\n      }\r\n    });\r\n\r\n    // When department changes, load classes and subjects\r\n    this.timetableForm.get('department')?.valueChanges.subscribe(departmentId => {\r\n      if (departmentId) {\r\n        this.loadClassesByDepartment(departmentId);\r\n        this.loadSubjectsByDepartment(departmentId);\r\n        this.timetableForm.patchValue({ class: '', subject: '' });\r\n      }\r\n    });\r\n\r\n    // When start time or duration changes, calculate end time\r\n    this.timetableForm.get('startTime')?.valueChanges.subscribe(() => {\r\n      this.calculateEndTime();\r\n    });\r\n\r\n    this.timetableForm.get('duration')?.valueChanges.subscribe(() => {\r\n      this.calculateEndTime();\r\n    });\r\n  }\r\n\r\n  updateAvailableDurations(): void {\r\n    if (this.selectedProgram) {\r\n      this.availableDurations = this.programDurations[this.selectedProgram.name] || [];\r\n      this.generateAvailableStartTimes();\r\n    } else {\r\n      this.availableDurations = [];\r\n      this.availableStartTimes = [];\r\n    }\r\n  }\r\n\r\n  generateAvailableStartTimes(): void {\r\n    if (this.selectedProgram) {\r\n      // For different programs, we might want different time slot granularities\r\n      if (this.selectedProgram.name === 'Intermediate') {\r\n        // For Intermediate, allow more flexible timing (every 30 minutes)\r\n        this.availableStartTimes = this.baseTimeSlots.filter(time => {\r\n          const hour = parseInt(time.split(':')[0]);\r\n          return hour >= 8 && hour <= 16; // 8 AM to 4 PM\r\n        });\r\n      } else {\r\n        // For BS/MS/PhD, standard hourly slots\r\n        this.availableStartTimes = this.baseTimeSlots.filter(time => {\r\n          const [hour, minute] = time.split(':').map(Number);\r\n          return hour >= 8 && hour <= 17 && (minute === 0 || minute === 30); // 8 AM to 5 PM\r\n        });\r\n      }\r\n    } else {\r\n      this.availableStartTimes = this.baseTimeSlots;\r\n    }\r\n  }\r\n\r\n  calculateEndTime(): void {\r\n    const startTime = this.timetableForm.get('startTime')?.value;\r\n    const duration = this.timetableForm.get('duration')?.value;\r\n\r\n    if (startTime && duration) {\r\n      const [hours, minutes] = startTime.split(':').map(Number);\r\n      const startMinutes = hours * 60 + minutes;\r\n      const endMinutes = startMinutes + parseInt(duration);\r\n\r\n      const endHours = Math.floor(endMinutes / 60);\r\n      const endMins = endMinutes % 60;\r\n\r\n      const endTime = `${endHours.toString().padStart(2, '0')}:${endMins.toString().padStart(2, '0')}`;\r\n      this.timetableForm.get('endTime')?.setValue(endTime);\r\n    }\r\n  }\r\n\r\n  loadTeachers(): void {\r\n    this.userService.getUsersByRole('Teacher').subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.teachers = response.users;\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading teachers:', error);\r\n        Swal.fire('Error', 'Failed to load teachers', 'error');\r\n      }\r\n    });\r\n  }\r\n\r\n  loadDepartmentsByProgram(programId: string): void {\r\n    this.departmentService.getDepartmentsByProgram(programId, true).subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.filteredDepartments = response.departments;\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading departments:', error);\r\n        this.filteredDepartments = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  loadClassesByDepartment(departmentId: string): void {\r\n    const programId = this.timetableForm.get('program')?.value;\r\n    this.classesService.getAllClasses({\r\n      program: programId,\r\n      department: departmentId,\r\n      isActive: true\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.filteredClasses = response.classes;\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading classes:', error);\r\n        this.filteredClasses = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  loadSubjectsByDepartment(departmentId: string): void {\r\n    this.subjectService.getSubjectsByDepartment(departmentId, true).subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.filteredSubjects = response.subjects;\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading subjects:', error);\r\n        this.filteredSubjects = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  loadTeachersByProgram(programId: string): void {\r\n    this.userService.getUsersByProgram(programId, 'Teacher').subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.filteredTeachers = response.users;\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading teachers by program:', error);\r\n        // Fallback to all teachers\r\n        this.filteredTeachers = this.teachers;\r\n      }\r\n    });\r\n  }\r\n\r\n  loadTimetables(): void {\r\n    this.timetableService.getAllTimetableEntries().subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.timetables = response.timetable;\r\n        }\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading timetables:', error);\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (this.timetableForm.valid) {\r\n      this.submitting = true;\r\n      const formData = this.timetableForm.value;\r\n\r\n      // Ensure proper data structure for backend\r\n      const timetableData = {\r\n        program: formData.program,\r\n        department: formData.department,\r\n        class: formData.class,\r\n        subject: formData.subject,\r\n        teacher: formData.teacher,\r\n        dayOfWeek: formData.dayOfWeek,\r\n        timeSlot: {\r\n          startTime: formData.startTime,\r\n          endTime: formData.endTime,\r\n          duration: parseInt(formData.duration)\r\n        },\r\n        room: formData.room || '',\r\n        semester: parseInt(formData.semester),\r\n        academicYear: formData.academicYear,\r\n        notes: formData.notes || ''\r\n      };\r\n\r\n      console.log('Submitting timetable data:', timetableData); // Debug log\r\n\r\n      const request = this.editingTimetableId\r\n        ? this.timetableService.updateTimetableEntry(this.editingTimetableId, timetableData)\r\n        : this.timetableService.createTimetableEntry(timetableData);\r\n\r\n      request.subscribe({\r\n        next: (response) => {\r\n          if (response.success) {\r\n            const action = this.editingTimetableId ? 'updated' : 'created';\r\n            this.snackBar.open(`Timetable entry ${action} successfully`, 'Close', {\r\n              duration: 3000\r\n            });\r\n            this.timetableForm.reset();\r\n            this.timetableForm.patchValue({ academicYear: '2024-2025' });\r\n            this.editingTimetableId = null;\r\n            this.loadTimetables();\r\n          } else {\r\n            const action = this.editingTimetableId ? 'update' : 'create';\r\n            this.snackBar.open(response.message || `Failed to ${action} timetable entry`, 'Close', {\r\n              duration: 3000\r\n            });\r\n          }\r\n          this.submitting = false;\r\n        },\r\n        error: (error) => {\r\n          const action = this.editingTimetableId ? 'updating' : 'creating';\r\n          console.error(`Error ${action} timetable entry:`, error);\r\n          this.snackBar.open(error.error?.message || `Error ${action} timetable entry`, 'Close', {\r\n            duration: 3000\r\n          });\r\n          this.submitting = false;\r\n        }\r\n      });\r\n    } else {\r\n      console.log('Form is invalid:', this.timetableForm.errors);\r\n      this.markFormGroupTouched(this.timetableForm);\r\n    }\r\n  }\r\n\r\n  private markFormGroupTouched(formGroup: FormGroup): void {\r\n    Object.keys(formGroup.controls).forEach(key => {\r\n      const control = formGroup.get(key);\r\n      control?.markAsTouched();\r\n      if (control instanceof FormGroup) {\r\n        this.markFormGroupTouched(control);\r\n      }\r\n    });\r\n  }\r\n\r\n  editTimetableEntry(timetable: any): void {\r\n    this.editingTimetableId = timetable._id;\r\n\r\n    // Load related data first\r\n    this.loadDepartmentsByProgram(timetable.program._id);\r\n    this.loadClassesByDepartment(timetable.department._id);\r\n    this.loadSubjectsByDepartment(timetable.department._id);\r\n    this.loadTeachersByProgram(timetable.program._id);\r\n\r\n    // Populate form with existing data\r\n    setTimeout(() => {\r\n      this.timetableForm.patchValue({\r\n        program: timetable.program._id,\r\n        department: timetable.department._id,\r\n        class: timetable.class._id,\r\n        subject: timetable.subject._id,\r\n        teacher: timetable.teacher._id,\r\n        dayOfWeek: timetable.dayOfWeek,\r\n        startTime: timetable.timeSlot.startTime,\r\n        endTime: timetable.timeSlot.endTime,\r\n        duration: timetable.timeSlot.duration,\r\n        room: timetable.room || '',\r\n        semester: timetable.semester,\r\n        academicYear: timetable.academicYear,\r\n        notes: timetable.notes || ''\r\n      });\r\n\r\n      // Calculate end time to ensure consistency\r\n      this.calculateEndTime();\r\n    }, 1000);\r\n\r\n    // Scroll to form\r\n    document.querySelector('.form-card')?.scrollIntoView({ behavior: 'smooth' });\r\n  }\r\n\r\n  cancelEdit(): void {\r\n    this.editingTimetableId = null;\r\n    this.timetableForm.reset();\r\n    this.timetableForm.patchValue({\r\n      academicYear: '2024-2025'\r\n    });\r\n  }\r\n\r\n  deleteTimetableEntry(timetable: Timetable): void {\r\n    if (confirm('Are you sure you want to delete this timetable entry?')) {\r\n      this.timetableService.deleteTimetableEntry(timetable._id).subscribe({\r\n        next: (response) => {\r\n          if (response.success) {\r\n            this.snackBar.open('Timetable entry deleted successfully', 'Close', {\r\n              duration: 3000\r\n            });\r\n            this.loadTimetables();\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('Error deleting timetable entry:', error);\r\n          this.snackBar.open('Error deleting timetable entry', 'Close', {\r\n            duration: 3000\r\n          });\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  formatTimeSlot(timeSlot: any): string {\r\n    return `${timeSlot.startTime} - ${timeSlot.endTime}`;\r\n  }\r\n\r\n  formatTimetableEntry(timetable: any): string {\r\n    const timeSlot = this.formatTimeSlot(timetable.timeSlot);\r\n    const teacherName = timetable.teacher?.name || 'Unknown Teacher';\r\n    const className = timetable.class?.className || 'Unknown Class';\r\n    const programName = timetable.program?.name || '';\r\n\r\n    // Format based on program type\r\n    if (programName === 'Intermediate') {\r\n      return `${timeSlot} ${teacherName} with ${className}`;\r\n    } else {\r\n      return `${timeSlot} ${teacherName} with ${className}`;\r\n    }\r\n  }\r\n\r\n  getClassDisplayName(classData: any): string {\r\n    if (!classData) return 'Unknown Class';\r\n\r\n    const program = classData.program?.name || '';\r\n    if (program === 'Intermediate') {\r\n      return `${classData.className}${classData.section ? ' - Section ' + classData.section : ''}`;\r\n    } else {\r\n      return classData.className;\r\n    }\r\n  }\r\n\r\n  getDurationLabel(duration: number): string {\r\n    if (duration < 60) {\r\n      return `${duration} min`;\r\n    } else if (duration === 60) {\r\n      return '1 hour';\r\n    } else if (duration === 90) {\r\n      return '1.5 hours';\r\n    } else if (duration === 120) {\r\n      return '2 hours';\r\n    } else if (duration === 180) {\r\n      return '3 hours';\r\n    } else {\r\n      return `${Math.floor(duration / 60)}h ${duration % 60}m`;\r\n    }\r\n  }\r\n}\r\n", "<div class=\"timetable-container\">\r\n  <!-- Header -->\r\n  <div class=\"page-header\">\r\n    <h1>Timetable Management</h1>\r\n    <div class=\"header-actions\">\r\n      <button mat-icon-button (click)=\"loadTimetables()\" matTooltip=\"Refresh\">\r\n        <mat-icon>refresh</mat-icon>\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Create/Edit Timetable Form -->\r\n  <mat-card class=\"form-card\">\r\n    <mat-card-header>\r\n      <mat-card-title>\r\n        {{ editingTimetableId ? 'Edit Timetable Entry' : 'Create Timetable Entry' }}\r\n      </mat-card-title>\r\n      <mat-card-subtitle>\r\n        {{ editingTimetableId ? 'Update existing class session' : 'Schedule a new class session' }}\r\n      </mat-card-subtitle>\r\n    </mat-card-header>\r\n    <mat-card-content>\r\n      <form [formGroup]=\"timetableForm\" (ngSubmit)=\"onSubmit()\">\r\n        <div class=\"form-grid\">\r\n          <!-- Program Selection -->\r\n          <mat-form-field appearance=\"outline\">\r\n            <mat-label>Program</mat-label>\r\n            <mat-select formControlName=\"program\" required>\r\n              <mat-option value=\"\">Select Program</mat-option>\r\n              <mat-option *ngFor=\"let program of programs\" [value]=\"program._id\">\r\n                {{ program.name }} - {{ program.fullName }}\r\n              </mat-option>\r\n            </mat-select>\r\n            <mat-error *ngIf=\"timetableForm.get('program')?.hasError('required')\">\r\n              Program is required\r\n            </mat-error>\r\n          </mat-form-field>\r\n\r\n          <!-- Department Selection -->\r\n          <mat-form-field appearance=\"outline\">\r\n            <mat-label>Department</mat-label>\r\n            <mat-select formControlName=\"department\" required [disabled]=\"!timetableForm.get('program')?.value\">\r\n              <mat-option value=\"\">{{ !timetableForm.get('program')?.value ? 'Select Program First' : 'Select Department' }}</mat-option>\r\n              <mat-option *ngFor=\"let department of filteredDepartments\" [value]=\"department._id\">\r\n                {{ department.name }} ({{ department.code }})\r\n              </mat-option>\r\n            </mat-select>\r\n            <mat-error *ngIf=\"timetableForm.get('department')?.hasError('required')\">\r\n              Department is required\r\n            </mat-error>\r\n          </mat-form-field>\r\n\r\n          <!-- Semester Selection -->\r\n          <mat-form-field appearance=\"outline\">\r\n            <mat-label>{{ isIntermediateProgram() ? 'Academic Year' : 'Semester' }}</mat-label>\r\n            <mat-select formControlName=\"semester\" required>\r\n              <mat-option value=\"\">{{ isIntermediateProgram() ? 'Select Year' : 'Select Semester' }}</mat-option>\r\n              <!-- For Intermediate Programs (Year-based) -->\r\n              <ng-container *ngIf=\"isIntermediateProgram()\">\r\n                <mat-option value=\"1\">1st Year</mat-option>\r\n                <mat-option value=\"2\">2nd Year</mat-option>\r\n              </ng-container>\r\n              <!-- For BS/MS Programs (Semester-based) -->\r\n              <ng-container *ngIf=\"!isIntermediateProgram()\">\r\n                <mat-option value=\"1\">1st Semester</mat-option>\r\n                <mat-option value=\"2\">2nd Semester</mat-option>\r\n                <mat-option value=\"3\">3rd Semester</mat-option>\r\n                <mat-option value=\"4\">4th Semester</mat-option>\r\n                <mat-option value=\"5\">5th Semester</mat-option>\r\n                <mat-option value=\"6\">6th Semester</mat-option>\r\n                <mat-option value=\"7\">7th Semester</mat-option>\r\n                <mat-option value=\"8\">8th Semester</mat-option>\r\n              </ng-container>\r\n            </mat-select>\r\n            <mat-error *ngIf=\"timetableForm.get('semester')?.hasError('required')\">\r\n              {{ isIntermediateProgram() ? 'Year' : 'Semester' }} is required\r\n            </mat-error>\r\n          </mat-form-field>\r\n\r\n          <!-- Class Selection -->\r\n          <mat-form-field appearance=\"outline\">\r\n            <mat-label>Class</mat-label>\r\n            <mat-select formControlName=\"class\" required [disabled]=\"!timetableForm.get('department')?.value\">\r\n              <mat-option value=\"\">{{ !timetableForm.get('department')?.value ? 'Select Department First' : 'Select Class' }}</mat-option>\r\n              <mat-option *ngFor=\"let class of filteredClasses\" [value]=\"class._id\">\r\n                {{ class.className }}{{ class.section ? ' - Section ' + class.section : '' }}\r\n                ({{ class?.program?.name }} - {{ class?.department?.name }})\r\n              </mat-option>\r\n            </mat-select>\r\n            <mat-error *ngIf=\"timetableForm.get('class')?.hasError('required')\">\r\n              Class is required\r\n            </mat-error>\r\n          </mat-form-field>\r\n\r\n          <!-- Subject Selection -->\r\n          <mat-form-field appearance=\"outline\">\r\n            <mat-label>Subject</mat-label>\r\n            <mat-select formControlName=\"subject\" required [disabled]=\"!timetableForm.get('department')?.value\">\r\n              <mat-option value=\"\">{{ !timetableForm.get('department')?.value ? 'Select Department First' : 'Select Subject' }}</mat-option>\r\n              <mat-option *ngFor=\"let subject of filteredSubjects\" [value]=\"subject._id\">\r\n                {{ subject.subjectName }} ({{ subject.code }})\r\n              </mat-option>\r\n            </mat-select>\r\n            <mat-error *ngIf=\"timetableForm.get('subject')?.hasError('required')\">\r\n              Subject is required\r\n            </mat-error>\r\n          </mat-form-field>\r\n\r\n          <!-- Teacher Selection -->\r\n          <mat-form-field appearance=\"outline\">\r\n            <mat-label>Teacher</mat-label>\r\n            <mat-select formControlName=\"teacher\" required [disabled]=\"!timetableForm.get('program')?.value\">\r\n              <mat-option value=\"\">{{ !timetableForm.get('program')?.value ? 'Select Program First' : 'Select Teacher' }}</mat-option>\r\n              <mat-option *ngFor=\"let teacher of filteredTeachers\" [value]=\"teacher._id\">\r\n                {{ teacher.name }} - {{ teacher.designation || 'Teacher' }}\r\n                <small *ngIf=\"teacher.department\">({{ teacher.department.name }})</small>\r\n              </mat-option>\r\n            </mat-select>\r\n            <mat-error *ngIf=\"timetableForm.get('teacher')?.hasError('required')\">\r\n              Teacher is required\r\n            </mat-error>\r\n          </mat-form-field>\r\n\r\n          <!-- Day of Week -->\r\n          <mat-form-field appearance=\"outline\">\r\n            <mat-label>Day of Week</mat-label>\r\n            <mat-select formControlName=\"dayOfWeek\" required>\r\n              <mat-option *ngFor=\"let day of daysOfWeek\" [value]=\"day\">\r\n                {{ day }}\r\n              </mat-option>\r\n            </mat-select>\r\n            <mat-error *ngIf=\"timetableForm.get('dayOfWeek')?.hasError('required')\">\r\n              Day is required\r\n            </mat-error>\r\n          </mat-form-field>\r\n\r\n          <!-- Start Time -->\r\n          <mat-form-field appearance=\"outline\">\r\n            <mat-label>Start Time</mat-label>\r\n            <mat-select formControlName=\"startTime\" required>\r\n              <mat-option *ngFor=\"let time of availableStartTimes\" [value]=\"time\">\r\n                {{ time }}\r\n              </mat-option>\r\n            </mat-select>\r\n            <mat-error *ngIf=\"timetableForm.get('startTime')?.hasError('required')\">\r\n              Start time is required\r\n            </mat-error>\r\n          </mat-form-field>\r\n\r\n          <!-- Duration -->\r\n          <mat-form-field appearance=\"outline\">\r\n            <mat-label>Class Duration</mat-label>\r\n            <mat-select formControlName=\"duration\" required>\r\n              <mat-option value=\"\">Select Duration</mat-option>\r\n              <mat-option *ngFor=\"let duration of availableDurations\" [value]=\"duration.value\">\r\n                {{ duration.label }}\r\n              </mat-option>\r\n            </mat-select>\r\n            <mat-error *ngIf=\"timetableForm.get('duration')?.hasError('required')\">\r\n              Duration is required\r\n            </mat-error>\r\n            <mat-hint *ngIf=\"!selectedProgram\">Select a program first to see duration options</mat-hint>\r\n          </mat-form-field>\r\n\r\n          <!-- End Time (Auto-calculated) -->\r\n          <mat-form-field appearance=\"outline\">\r\n            <mat-label>End Time (Auto-calculated)</mat-label>\r\n            <input matInput formControlName=\"endTime\" readonly>\r\n            <mat-hint>Automatically calculated based on start time and duration</mat-hint>\r\n          </mat-form-field>\r\n\r\n          <!-- Room -->\r\n          <mat-form-field appearance=\"outline\">\r\n            <mat-label>Room (Optional)</mat-label>\r\n            <input matInput formControlName=\"room\" placeholder=\"e.g., Room 101\">\r\n          </mat-form-field>\r\n\r\n          <!-- Academic Year -->\r\n          <mat-form-field appearance=\"outline\">\r\n            <mat-label>Academic Year</mat-label>\r\n            <input matInput formControlName=\"academicYear\" placeholder=\"e.g., 2024-2025\" required>\r\n            <mat-error *ngIf=\"timetableForm.get('academicYear')?.hasError('required')\">\r\n              Academic year is required\r\n            </mat-error>\r\n          </mat-form-field>\r\n\r\n          <!-- Notes -->\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Notes (Optional)</mat-label>\r\n            <textarea matInput formControlName=\"notes\" rows=\"3\" placeholder=\"Additional notes...\"></textarea>\r\n          </mat-form-field>\r\n        </div>\r\n\r\n        <div class=\"form-actions\">\r\n          <button mat-raised-button type=\"button\" (click)=\"timetableForm.reset()\" *ngIf=\"!editingTimetableId\">\r\n            <mat-icon>clear</mat-icon>\r\n            Clear\r\n          </button>\r\n          <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"!timetableForm.valid || submitting\">\r\n            <mat-icon>save</mat-icon>\r\n            {{ submitting ? (editingTimetableId ? 'Updating...' : 'Creating...') : (editingTimetableId ? 'Update Entry' : 'Create Entry') }}\r\n          </button>\r\n          <button mat-button type=\"button\" (click)=\"cancelEdit()\" *ngIf=\"editingTimetableId\" color=\"warn\">\r\n            <mat-icon>cancel</mat-icon>\r\n            Cancel Edit\r\n          </button>\r\n        </div>\r\n      </form>\r\n    </mat-card-content>\r\n  </mat-card>\r\n\r\n  <!-- Existing Timetables -->\r\n  <mat-card class=\"table-card\">\r\n    <mat-card-header>\r\n      <mat-card-title>Current Timetable</mat-card-title>\r\n      <mat-card-subtitle>All scheduled classes</mat-card-subtitle>\r\n    </mat-card-header>\r\n    <mat-card-content>\r\n      <div *ngIf=\"loading\" class=\"loading-container\">\r\n        <mat-spinner></mat-spinner>\r\n        <p>Loading timetables...</p>\r\n      </div>\r\n\r\n      <div *ngIf=\"!loading && timetables.length > 0\" class=\"table-container\">\r\n        <table mat-table [dataSource]=\"timetables\" class=\"timetable-table\">\r\n          <!-- Day Column -->\r\n          <ng-container matColumnDef=\"dayOfWeek\">\r\n            <th mat-header-cell *matHeaderCellDef>Day</th>\r\n            <td mat-cell *matCellDef=\"let timetable\">{{ timetable.dayOfWeek }}</td>\r\n          </ng-container>\r\n\r\n          <!-- Schedule Info Column -->\r\n          <ng-container matColumnDef=\"scheduleInfo\">\r\n            <th mat-header-cell *matHeaderCellDef>Schedule Details</th>\r\n            <td mat-cell *matCellDef=\"let timetable\">\r\n              <div class=\"schedule-info\">\r\n                <div class=\"teacher-class\">\r\n                  <strong>{{ timetable.teacher?.name || 'Unknown Teacher' }}</strong> with\r\n                  <strong>{{ getClassDisplayName(timetable.class) }}</strong>\r\n                </div>\r\n                <div class=\"subject-info\">\r\n                  <span class=\"subject\">{{ timetable.subject?.subjectName || 'Unknown Subject' }}</span>\r\n                  <span class=\"department text-muted\">({{ timetable.department?.name || 'Unknown Dept' }})</span>\r\n                </div>\r\n                <div class=\"semester-info\">\r\n                  <small class=\"text-info\">\r\n                    {{ isIntermediateProgram() && timetable.program?.name === 'Intermediate' ?\r\n                        getSemesterDisplayText(timetable.semester) + ' Year' :\r\n                        getSemesterDisplayText(timetable.semester) + ' Semester' }}\r\n                    - {{ timetable.academicYear }}\r\n                  </small>\r\n                </div>\r\n              </div>\r\n            </td>\r\n          </ng-container>\r\n\r\n          <!-- Time Slot Column -->\r\n          <ng-container matColumnDef=\"timeSlot\">\r\n            <th mat-header-cell *matHeaderCellDef>Time & Duration</th>\r\n            <td mat-cell *matCellDef=\"let timetable\">\r\n              <div>{{ formatTimeSlot(timetable.timeSlot) }}</div>\r\n              <small class=\"text-muted\">{{ getDurationLabel(timetable.timeSlot.duration) }}</small>\r\n            </td>\r\n          </ng-container>\r\n\r\n          <!-- Program Column -->\r\n          <ng-container matColumnDef=\"program\">\r\n            <th mat-header-cell *matHeaderCellDef>Program</th>\r\n            <td mat-cell *matCellDef=\"let timetable\">{{ timetable.program.name }}</td>\r\n          </ng-container>\r\n\r\n\r\n\r\n          <!-- Room Column -->\r\n          <ng-container matColumnDef=\"room\">\r\n            <th mat-header-cell *matHeaderCellDef>Room</th>\r\n            <td mat-cell *matCellDef=\"let timetable\">{{ timetable.room || 'N/A' }}</td>\r\n          </ng-container>\r\n\r\n          <!-- Actions Column -->\r\n          <ng-container matColumnDef=\"actions\">\r\n            <th mat-header-cell *matHeaderCellDef>Actions</th>\r\n            <td mat-cell *matCellDef=\"let timetable\">\r\n              <div class=\"action-buttons\">\r\n                <button mat-icon-button color=\"primary\" (click)=\"editTimetableEntry(timetable)\" matTooltip=\"Edit\">\r\n                  <mat-icon>edit</mat-icon>\r\n                </button>\r\n                <button mat-icon-button color=\"warn\" (click)=\"deleteTimetableEntry(timetable)\" matTooltip=\"Delete\">\r\n                  <mat-icon>delete</mat-icon>\r\n                </button>\r\n              </div>\r\n            </td>\r\n          </ng-container>\r\n\r\n          <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\r\n          <tr mat-row *matRowDef=\"let row; columns: displayedColumns;\"></tr>\r\n        </table>\r\n      </div>\r\n\r\n      <div *ngIf=\"!loading && timetables.length === 0\" class=\"no-data\">\r\n        <mat-icon>schedule</mat-icon>\r\n        <h3>No Timetable Entries</h3>\r\n        <p>Create your first timetable entry using the form above.</p>\r\n      </div>\r\n    </mat-card-content>\r\n  </mat-card>\r\n</div>\r\n"], "mappings": "AACA,SAAsBA,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;AAUnE,OAAOC,IAAI,MAAM,aAAa;;;;;;;;;;;;;;;;;;;;;;;ICkBhBC,EAAA,CAAAC,cAAA,qBAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFgCH,EAAA,CAAAI,UAAA,UAAAC,WAAA,CAAAC,GAAA,CAAqB;IAChEN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,WAAA,CAAAI,IAAA,SAAAJ,WAAA,CAAAK,QAAA,MACF;;;;;IAEFV,EAAA,CAAAC,cAAA,gBAAsE;IACpED,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAQVH,EAAA,CAAAC,cAAA,qBAAoF;IAClFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF8CH,EAAA,CAAAI,UAAA,UAAAO,cAAA,CAAAL,GAAA,CAAwB;IACjFN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAG,cAAA,CAAAF,IAAA,QAAAE,cAAA,CAAAC,IAAA,OACF;;;;;IAEFZ,EAAA,CAAAC,cAAA,gBAAyE;IACvED,EAAA,CAAAE,MAAA,+BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IASVH,EAAA,CAAAa,uBAAA,GAA8C;IAC5Cb,EAAA,CAAAC,cAAA,qBAAsB;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAa;IAC3CH,EAAA,CAAAC,cAAA,qBAAsB;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAa;IAC7CH,EAAA,CAAAc,qBAAA,EAAe;;;;;IAEfd,EAAA,CAAAa,uBAAA,GAA+C;IAC7Cb,EAAA,CAAAC,cAAA,qBAAsB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAa;IAC/CH,EAAA,CAAAC,cAAA,qBAAsB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAa;IAC/CH,EAAA,CAAAC,cAAA,qBAAsB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAa;IAC/CH,EAAA,CAAAC,cAAA,qBAAsB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAa;IAC/CH,EAAA,CAAAC,cAAA,qBAAsB;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAa;IAC/CH,EAAA,CAAAC,cAAA,sBAAsB;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAa;IAC/CH,EAAA,CAAAC,cAAA,sBAAsB;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAa;IAC/CH,EAAA,CAAAC,cAAA,sBAAsB;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAa;IACjDH,EAAA,CAAAc,qBAAA,EAAe;;;;;IAEjBd,EAAA,CAAAC,cAAA,gBAAuE;IACrED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAe,kBAAA,MAAAC,MAAA,CAAAC,qBAAA,0CACF;;;;;IAQEjB,EAAA,CAAAC,cAAA,qBAAsE;IACpED,EAAA,CAAAE,MAAA,GAEF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAHqCH,EAAA,CAAAI,UAAA,UAAAc,SAAA,CAAAZ,GAAA,CAAmB;IACnEN,EAAA,CAAAO,SAAA,GAEF;IAFEP,EAAA,CAAAmB,kBAAA,MAAAD,SAAA,CAAAE,SAAA,MAAAF,SAAA,CAAAG,OAAA,mBAAAH,SAAA,CAAAG,OAAA,aAAAH,SAAA,kBAAAA,SAAA,CAAAI,OAAA,kBAAAJ,SAAA,CAAAI,OAAA,CAAAb,IAAA,SAAAS,SAAA,kBAAAA,SAAA,CAAAK,UAAA,kBAAAL,SAAA,CAAAK,UAAA,CAAAd,IAAA,OAEF;;;;;IAEFT,EAAA,CAAAC,cAAA,gBAAoE;IAClED,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAQVH,EAAA,CAAAC,cAAA,qBAA2E;IACzED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFwCH,EAAA,CAAAI,UAAA,UAAAoB,WAAA,CAAAlB,GAAA,CAAqB;IACxEN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAgB,WAAA,CAAAC,WAAA,QAAAD,WAAA,CAAAZ,IAAA,OACF;;;;;IAEFZ,EAAA,CAAAC,cAAA,gBAAsE;IACpED,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAURH,EAAA,CAAAC,cAAA,YAAkC;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAvCH,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAe,kBAAA,MAAAW,WAAA,CAAAH,UAAA,CAAAd,IAAA,MAA+B;;;;;IAFnET,EAAA,CAAAC,cAAA,qBAA2E;IACzED,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAA2B,UAAA,IAAAC,iDAAA,oBAAyE;IAC3E5B,EAAA,CAAAG,YAAA,EAAa;;;;IAHwCH,EAAA,CAAAI,UAAA,UAAAsB,WAAA,CAAApB,GAAA,CAAqB;IACxEN,EAAA,CAAAO,SAAA,GACA;IADAP,EAAA,CAAAQ,kBAAA,MAAAkB,WAAA,CAAAjB,IAAA,SAAAiB,WAAA,CAAAG,WAAA,mBACA;IAAQ7B,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAI,UAAA,SAAAsB,WAAA,CAAAH,UAAA,CAAwB;;;;;IAGpCvB,EAAA,CAAAC,cAAA,gBAAsE;IACpED,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAOVH,EAAA,CAAAC,cAAA,qBAAyD;IACvDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF8BH,EAAA,CAAAI,UAAA,UAAA0B,OAAA,CAAa;IACtD9B,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAe,kBAAA,MAAAe,OAAA,MACF;;;;;IAEF9B,EAAA,CAAAC,cAAA,gBAAwE;IACtED,EAAA,CAAAE,MAAA,wBACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAOVH,EAAA,CAAAC,cAAA,qBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFwCH,EAAA,CAAAI,UAAA,UAAA2B,QAAA,CAAc;IACjE/B,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAe,kBAAA,MAAAgB,QAAA,MACF;;;;;IAEF/B,EAAA,CAAAC,cAAA,gBAAwE;IACtED,EAAA,CAAAE,MAAA,+BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAQVH,EAAA,CAAAC,cAAA,qBAAiF;IAC/ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF2CH,EAAA,CAAAI,UAAA,UAAA4B,YAAA,CAAAC,KAAA,CAAwB;IAC9EjC,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAe,kBAAA,MAAAiB,YAAA,CAAAE,KAAA,MACF;;;;;IAEFlC,EAAA,CAAAC,cAAA,gBAAuE;IACrED,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAE,MAAA,qDAA8C;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAoB5FH,EAAA,CAAAC,cAAA,gBAA2E;IACzED,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;;IAWdH,EAAA,CAAAC,cAAA,iBAAoG;IAA5DD,EAAA,CAAAmC,UAAA,mBAAAC,+DAAA;MAAApC,EAAA,CAAAqC,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAvC,EAAA,CAAAwC,aAAA;MAAA,OAASxC,EAAA,CAAAyC,WAAA,CAAAF,OAAA,CAAAG,aAAA,CAAAC,KAAA,EAAqB;IAAA,EAAC;IACrE3C,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAE,MAAA,cACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAKTH,EAAA,CAAAC,cAAA,iBAAgG;IAA/DD,EAAA,CAAAmC,UAAA,mBAAAS,+DAAA;MAAA5C,EAAA,CAAAqC,aAAA,CAAAQ,IAAA;MAAA,MAAAC,OAAA,GAAA9C,EAAA,CAAAwC,aAAA;MAAA,OAASxC,EAAA,CAAAyC,WAAA,CAAAK,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IACrD/C,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAE,MAAA,oBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAabH,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAgD,SAAA,kBAA2B;IAC3BhD,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAOxBH,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAC9CH,EAAA,CAAAC,cAAA,aAAyC;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAA9BH,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAiD,iBAAA,CAAAC,aAAA,CAAAC,SAAA,CAAyB;;;;;IAKlEnD,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAC3DH,EAAA,CAAAC,cAAA,aAAyC;IAG3BD,EAAA,CAAAE,MAAA,GAAkD;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,aACpE;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAA0C;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAE7DH,EAAA,CAAAC,cAAA,cAA0B;IACFD,EAAA,CAAAE,MAAA,IAAyD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtFH,EAAA,CAAAC,cAAA,gBAAoC;IAAAD,EAAA,CAAAE,MAAA,IAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEjGH,EAAA,CAAAC,cAAA,eAA2B;IAEvBD,EAAA,CAAAE,MAAA,IAIF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAbAH,EAAA,CAAAO,SAAA,GAAkD;IAAlDP,EAAA,CAAAiD,iBAAA,EAAAG,aAAA,CAAAC,OAAA,kBAAAD,aAAA,CAAAC,OAAA,CAAA5C,IAAA,uBAAkD;IAClDT,EAAA,CAAAO,SAAA,GAA0C;IAA1CP,EAAA,CAAAiD,iBAAA,CAAAK,OAAA,CAAAC,mBAAA,CAAAH,aAAA,CAAAI,KAAA,EAA0C;IAG5BxD,EAAA,CAAAO,SAAA,GAAyD;IAAzDP,EAAA,CAAAiD,iBAAA,EAAAG,aAAA,CAAAK,OAAA,kBAAAL,aAAA,CAAAK,OAAA,CAAAhC,WAAA,uBAAyD;IAC3CzB,EAAA,CAAAO,SAAA,GAAoD;IAApDP,EAAA,CAAAe,kBAAA,OAAAqC,aAAA,CAAA7B,UAAA,kBAAA6B,aAAA,CAAA7B,UAAA,CAAAd,IAAA,yBAAoD;IAItFT,EAAA,CAAAO,SAAA,GAIF;IAJEP,EAAA,CAAAQ,kBAAA,MAAA8C,OAAA,CAAArC,qBAAA,OAAAmC,aAAA,CAAA9B,OAAA,kBAAA8B,aAAA,CAAA9B,OAAA,CAAAb,IAAA,uBAAA6C,OAAA,CAAAI,sBAAA,CAAAN,aAAA,CAAAO,QAAA,cAAAL,OAAA,CAAAI,sBAAA,CAAAN,aAAA,CAAAO,QAAA,wBAAAP,aAAA,CAAAQ,YAAA,MAIF;;;;;IAQN5D,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAC1DH,EAAA,CAAAC,cAAA,aAAyC;IAClCD,EAAA,CAAAE,MAAA,GAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACnDH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAmD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IADhFH,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAiD,iBAAA,CAAAY,OAAA,CAAAC,cAAA,CAAAC,aAAA,CAAAC,QAAA,EAAwC;IACnBhE,EAAA,CAAAO,SAAA,GAAmD;IAAnDP,EAAA,CAAAiD,iBAAA,CAAAY,OAAA,CAAAI,gBAAA,CAAAF,aAAA,CAAAC,QAAA,CAAAE,QAAA,EAAmD;;;;;IAM/ElE,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAClDH,EAAA,CAAAC,cAAA,aAAyC;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAjCH,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAAiD,iBAAA,CAAAkB,aAAA,CAAA7C,OAAA,CAAAb,IAAA,CAA4B;;;;;IAOrET,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAC/CH,EAAA,CAAAC,cAAA,aAAyC;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAlCH,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAiD,iBAAA,CAAAmB,aAAA,CAAAC,IAAA,UAA6B;;;;;IAKtErE,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;;IAClDH,EAAA,CAAAC,cAAA,aAAyC;IAEGD,EAAA,CAAAmC,UAAA,mBAAAmC,kEAAA;MAAA,MAAAC,WAAA,GAAAvE,EAAA,CAAAqC,aAAA,CAAAmC,IAAA;MAAA,MAAAC,aAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAA3E,EAAA,CAAAwC,aAAA;MAAA,OAASxC,EAAA,CAAAyC,WAAA,CAAAkC,OAAA,CAAAC,kBAAA,CAAAH,aAAA,CAA6B;IAAA,EAAC;IAC7EzE,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE3BH,EAAA,CAAAC,cAAA,iBAAmG;IAA9DD,EAAA,CAAAmC,UAAA,mBAAA0C,kEAAA;MAAA,MAAAN,WAAA,GAAAvE,EAAA,CAAAqC,aAAA,CAAAmC,IAAA;MAAA,MAAAC,aAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAI,OAAA,GAAA9E,EAAA,CAAAwC,aAAA;MAAA,OAASxC,EAAA,CAAAyC,WAAA,CAAAqC,OAAA,CAAAC,oBAAA,CAAAN,aAAA,CAA+B;IAAA,EAAC;IAC5EzE,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAMnCH,EAAA,CAAAgD,SAAA,aAA4D;;;;;IAC5DhD,EAAA,CAAAgD,SAAA,aAAkE;;;;;IAxEtEhD,EAAA,CAAAC,cAAA,cAAuE;IAGnED,EAAA,CAAAa,uBAAA,OAAuC;IACrCb,EAAA,CAAA2B,UAAA,IAAAqD,wCAAA,iBAA8C;IAC9ChF,EAAA,CAAA2B,UAAA,IAAAsD,wCAAA,iBAAuE;IACzEjF,EAAA,CAAAc,qBAAA,EAAe;IAGfd,EAAA,CAAAa,uBAAA,OAA0C;IACxCb,EAAA,CAAA2B,UAAA,IAAAuD,wCAAA,iBAA2D;IAC3DlF,EAAA,CAAA2B,UAAA,IAAAwD,wCAAA,kBAmBK;IACPnF,EAAA,CAAAc,qBAAA,EAAe;IAGfd,EAAA,CAAAa,uBAAA,OAAsC;IACpCb,EAAA,CAAA2B,UAAA,IAAAyD,wCAAA,iBAA0D;IAC1DpF,EAAA,CAAA2B,UAAA,KAAA0D,yCAAA,iBAGK;IACPrF,EAAA,CAAAc,qBAAA,EAAe;IAGfd,EAAA,CAAAa,uBAAA,QAAqC;IACnCb,EAAA,CAAA2B,UAAA,KAAA2D,yCAAA,iBAAkD;IAClDtF,EAAA,CAAA2B,UAAA,KAAA4D,yCAAA,iBAA0E;IAC5EvF,EAAA,CAAAc,qBAAA,EAAe;IAKfd,EAAA,CAAAa,uBAAA,QAAkC;IAChCb,EAAA,CAAA2B,UAAA,KAAA6D,yCAAA,iBAA+C;IAC/CxF,EAAA,CAAA2B,UAAA,KAAA8D,yCAAA,iBAA2E;IAC7EzF,EAAA,CAAAc,qBAAA,EAAe;IAGfd,EAAA,CAAAa,uBAAA,QAAqC;IACnCb,EAAA,CAAA2B,UAAA,KAAA+D,yCAAA,iBAAkD;IAClD1F,EAAA,CAAA2B,UAAA,KAAAgE,yCAAA,iBASK;IACP3F,EAAA,CAAAc,qBAAA,EAAe;IAEfd,EAAA,CAAA2B,UAAA,KAAAiE,yCAAA,iBAA4D;IAC5D5F,EAAA,CAAA2B,UAAA,KAAAkE,yCAAA,iBAAkE;IACpE7F,EAAA,CAAAG,YAAA,EAAQ;;;;IAxESH,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAI,UAAA,eAAA0F,OAAA,CAAAC,UAAA,CAAyB;IAsEpB/F,EAAA,CAAAO,SAAA,IAAiC;IAAjCP,EAAA,CAAAI,UAAA,oBAAA0F,OAAA,CAAAE,gBAAA,CAAiC;IACpBhG,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAI,UAAA,qBAAA0F,OAAA,CAAAE,gBAAA,CAA0B;;;;;IAI/DhG,EAAA,CAAAC,cAAA,cAAiE;IACrDD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,8DAAuD;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;AD5RtE,OAAM,MAAO8F,kBAAkB;EAsD7BC,YACUC,EAAe,EACfC,gBAAkC,EAClCC,cAA8B,EAC9BC,iBAAoC,EACpCC,cAA8B,EAC9BC,cAA8B,EAC9BC,WAAwB,EACxBC,QAAqB;IAPrB,KAAAP,EAAE,GAAFA,EAAE;IACF,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IA5DlB,KAAAX,UAAU,GAAgB,EAAE;IAC5B,KAAAY,QAAQ,GAAc,EAAE;IACxB,KAAAC,WAAW,GAAiB,EAAE;IAC9B,KAAAC,mBAAmB,GAAiB,EAAE;IACtC,KAAAC,OAAO,GAAY,EAAE;IACrB,KAAAC,eAAe,GAAY,EAAE;IAC7B,KAAAC,QAAQ,GAAc,EAAE;IACxB,KAAAC,gBAAgB,GAAc,EAAE;IAChC,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,gBAAgB,GAAW,EAAE;IAE7B,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,kBAAkB,GAAkB,IAAI;IAExC,KAAAC,UAAU,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,CAAC;IACjF,KAAAC,aAAa,GAAG,CACd,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EACtE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EACtE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAC5C;IAED,KAAAC,mBAAmB,GAAa,EAAE;IAElC,KAAAzB,gBAAgB,GAAa,CAAC,WAAW,EAAE,cAAc,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,CAAC;IAEpG;IACA,KAAA0B,gBAAgB,GAA0D;MACxE,cAAc,EAAE,CACd;QAAEzF,KAAK,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAY,CAAE,EAClC;QAAED,KAAK,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAQ,CAAE,CAC/B;MACD,IAAI,EAAE,CACJ;QAAED,KAAK,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAQ,CAAE,EAC9B;QAAED,KAAK,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAW,CAAE,EACjC;QAAED,KAAK,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAS,CAAE,CACjC;MACD,IAAI,EAAE,CACJ;QAAED,KAAK,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAQ,CAAE,EAC9B;QAAED,KAAK,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAW,CAAE,EACjC;QAAED,KAAK,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAS,CAAE,CACjC;MACD,KAAK,EAAE,CACL;QAAED,KAAK,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAW,CAAE,EACjC;QAAED,KAAK,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAS,CAAE,EAChC;QAAED,KAAK,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAS,CAAE;KAEnC;IAED,KAAAyF,kBAAkB,GAAuC,EAAE;IAC3D,KAAAC,eAAe,GAAmB,IAAI;IAYpC,IAAI,CAAClF,aAAa,GAAG,IAAI,CAACyD,EAAE,CAAC0B,KAAK,CAAC;MACjCvG,OAAO,EAAE,CAAC,EAAE,EAAExB,UAAU,CAACgI,QAAQ,CAAC;MAClCvG,UAAU,EAAE,CAAC,EAAE,EAAEzB,UAAU,CAACgI,QAAQ,CAAC;MACrCtE,KAAK,EAAE,CAAC,EAAE,EAAE1D,UAAU,CAACgI,QAAQ,CAAC;MAChCrE,OAAO,EAAE,CAAC,EAAE,EAAE3D,UAAU,CAACgI,QAAQ,CAAC;MAClCzE,OAAO,EAAE,CAAC,EAAE,EAAEvD,UAAU,CAACgI,QAAQ,CAAC;MAClC3E,SAAS,EAAE,CAAC,EAAE,EAAErD,UAAU,CAACgI,QAAQ,CAAC;MACpCC,SAAS,EAAE,CAAC,EAAE,EAAEjI,UAAU,CAACgI,QAAQ,CAAC;MACpC5D,QAAQ,EAAE,CAAC,EAAE,EAAEpE,UAAU,CAACgI,QAAQ,CAAC;MACnCE,OAAO,EAAE,CAAC;QAAE/F,KAAK,EAAE,EAAE;QAAEgG,QAAQ,EAAE;MAAI,CAAE,CAAC;MACxC5D,IAAI,EAAE,CAAC,EAAE,CAAC;MACVV,QAAQ,EAAE,CAAC,EAAE,EAAE7D,UAAU,CAACgI,QAAQ,CAAC;MACnClE,YAAY,EAAE,CAAC,WAAW,EAAE9D,UAAU,CAACgI,QAAQ,CAAC;MAChDI,KAAK,EAAE,CAAC,EAAE;KACX,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEApH,qBAAqBA,CAAA;IACnB,MAAMqH,SAAS,GAAG,IAAI,CAAC5F,aAAa,CAAC6F,GAAG,CAAC,SAAS,CAAC,EAAEtG,KAAK;IAC1D,MAAM2F,eAAe,GAAG,IAAI,CAACjB,QAAQ,CAAC6B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnI,GAAG,KAAKgI,SAAS,CAAC;IACpE,OAAOV,eAAe,EAAEnH,IAAI,KAAK,cAAc;EACjD;EAEAiD,sBAAsBA,CAACC,QAAgB;IACrC,MAAM+E,WAAW,GAA8B;MAC7C,CAAC,EAAE,KAAK;MAAE,CAAC,EAAE,KAAK;MAAE,CAAC,EAAE,KAAK;MAAE,CAAC,EAAE,KAAK;MACtC,CAAC,EAAE,KAAK;MAAE,CAAC,EAAE,KAAK;MAAE,CAAC,EAAE,KAAK;MAAE,CAAC,EAAE;KAClC;IACD,OAAOA,WAAW,CAAC/E,QAAQ,CAAC,IAAI,GAAGA,QAAQ,IAAI;EACjD;EAEAyE,eAAeA,CAAA;IACb,IAAI,CAAChB,OAAO,GAAG,IAAI;IAEnB;IACA,IAAI,CAACf,cAAc,CAACsC,cAAc,CAAC,IAAI,CAAC,CAACC,SAAS,CAAC;MACjDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACpC,QAAQ,GAAGmC,QAAQ,CAACnC,QAAQ;;MAErC,CAAC;MACDqC,KAAK,EAAGA,KAAK,IAAKC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK;KACjE,CAAC;IAEF;IACA,IAAI,CAACvC,WAAW,CAACyC,cAAc,CAAC,SAAS,CAAC,CAACN,SAAS,CAAC;MACnDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAAC7B,QAAQ,GAAG4B,QAAQ,CAACK,KAAK;;MAElC,CAAC;MACDH,KAAK,EAAGA,KAAK,IAAKC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK;KACjE,CAAC;IAEF;IACA,IAAI,CAACI,cAAc,EAAE;EACvB;EAEAf,sBAAsBA,CAAA;IACpB;IACA,IAAI,CAAC3F,aAAa,CAAC6F,GAAG,CAAC,SAAS,CAAC,EAAEc,YAAY,CAACT,SAAS,CAACN,SAAS,IAAG;MACpE,IAAIA,SAAS,EAAE;QACb,IAAI,CAACV,eAAe,GAAG,IAAI,CAACjB,QAAQ,CAAC6B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnI,GAAG,KAAKgI,SAAS,CAAC,IAAI,IAAI;QAC3E,IAAI,CAACgB,wBAAwB,EAAE;QAC/B,IAAI,CAACC,wBAAwB,CAACjB,SAAS,CAAC;QACxC,IAAI,CAACkB,qBAAqB,CAAClB,SAAS,CAAC;QACrC,IAAI,CAAC5F,aAAa,CAAC+G,UAAU,CAAC;UAC5BlI,UAAU,EAAE,EAAE;UACdiC,KAAK,EAAE,EAAE;UACTC,OAAO,EAAE,EAAE;UACXS,QAAQ,EAAE,EAAE;UACZ8D,OAAO,EAAE;SACV,CAAC;;IAEN,CAAC,CAAC;IAEF;IACA,IAAI,CAACtF,aAAa,CAAC6F,GAAG,CAAC,YAAY,CAAC,EAAEc,YAAY,CAACT,SAAS,CAACc,YAAY,IAAG;MAC1E,IAAIA,YAAY,EAAE;QAChB,IAAI,CAACC,uBAAuB,CAACD,YAAY,CAAC;QAC1C,IAAI,CAACE,wBAAwB,CAACF,YAAY,CAAC;QAC3C,IAAI,CAAChH,aAAa,CAAC+G,UAAU,CAAC;UAAEjG,KAAK,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE,CAAC;;IAE7D,CAAC,CAAC;IAEF;IACA,IAAI,CAACf,aAAa,CAAC6F,GAAG,CAAC,WAAW,CAAC,EAAEc,YAAY,CAACT,SAAS,CAAC,MAAK;MAC/D,IAAI,CAACiB,gBAAgB,EAAE;IACzB,CAAC,CAAC;IAEF,IAAI,CAACnH,aAAa,CAAC6F,GAAG,CAAC,UAAU,CAAC,EAAEc,YAAY,CAACT,SAAS,CAAC,MAAK;MAC9D,IAAI,CAACiB,gBAAgB,EAAE;IACzB,CAAC,CAAC;EACJ;EAEAP,wBAAwBA,CAAA;IACtB,IAAI,IAAI,CAAC1B,eAAe,EAAE;MACxB,IAAI,CAACD,kBAAkB,GAAG,IAAI,CAACD,gBAAgB,CAAC,IAAI,CAACE,eAAe,CAACnH,IAAI,CAAC,IAAI,EAAE;MAChF,IAAI,CAACqJ,2BAA2B,EAAE;KACnC,MAAM;MACL,IAAI,CAACnC,kBAAkB,GAAG,EAAE;MAC5B,IAAI,CAACF,mBAAmB,GAAG,EAAE;;EAEjC;EAEAqC,2BAA2BA,CAAA;IACzB,IAAI,IAAI,CAAClC,eAAe,EAAE;MACxB;MACA,IAAI,IAAI,CAACA,eAAe,CAACnH,IAAI,KAAK,cAAc,EAAE;QAChD;QACA,IAAI,CAACgH,mBAAmB,GAAG,IAAI,CAACD,aAAa,CAACuC,MAAM,CAACC,IAAI,IAAG;UAC1D,MAAMC,IAAI,GAAGC,QAAQ,CAACF,IAAI,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;UACzC,OAAOF,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,EAAE,CAAC,CAAC;QAClC,CAAC,CAAC;OACH,MAAM;QACL;QACA,IAAI,CAACxC,mBAAmB,GAAG,IAAI,CAACD,aAAa,CAACuC,MAAM,CAACC,IAAI,IAAG;UAC1D,MAAM,CAACC,IAAI,EAAEG,MAAM,CAAC,GAAGJ,IAAI,CAACG,KAAK,CAAC,GAAG,CAAC,CAACE,GAAG,CAACC,MAAM,CAAC;UAClD,OAAOL,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,EAAE,KAAKG,MAAM,KAAK,CAAC,IAAIA,MAAM,KAAK,EAAE,CAAC,CAAC,CAAC;QACrE,CAAC,CAAC;;KAEL,MAAM;MACL,IAAI,CAAC3C,mBAAmB,GAAG,IAAI,CAACD,aAAa;;EAEjD;EAEAqC,gBAAgBA,CAAA;IACd,MAAM9B,SAAS,GAAG,IAAI,CAACrF,aAAa,CAAC6F,GAAG,CAAC,WAAW,CAAC,EAAEtG,KAAK;IAC5D,MAAMiC,QAAQ,GAAG,IAAI,CAACxB,aAAa,CAAC6F,GAAG,CAAC,UAAU,CAAC,EAAEtG,KAAK;IAE1D,IAAI8F,SAAS,IAAI7D,QAAQ,EAAE;MACzB,MAAM,CAACqG,KAAK,EAAEC,OAAO,CAAC,GAAGzC,SAAS,CAACoC,KAAK,CAAC,GAAG,CAAC,CAACE,GAAG,CAACC,MAAM,CAAC;MACzD,MAAMG,YAAY,GAAGF,KAAK,GAAG,EAAE,GAAGC,OAAO;MACzC,MAAME,UAAU,GAAGD,YAAY,GAAGP,QAAQ,CAAChG,QAAQ,CAAC;MAEpD,MAAMyG,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACH,UAAU,GAAG,EAAE,CAAC;MAC5C,MAAMI,OAAO,GAAGJ,UAAU,GAAG,EAAE;MAE/B,MAAM1C,OAAO,GAAG,GAAG2C,QAAQ,CAACI,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,OAAO,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;MAChG,IAAI,CAACtI,aAAa,CAAC6F,GAAG,CAAC,SAAS,CAAC,EAAE0C,QAAQ,CAACjD,OAAO,CAAC;;EAExD;EAEAkD,YAAYA,CAAA;IACV,IAAI,CAACzE,WAAW,CAACyC,cAAc,CAAC,SAAS,CAAC,CAACN,SAAS,CAAC;MACnDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAAC7B,QAAQ,GAAG4B,QAAQ,CAACK,KAAK;;MAElC,CAAC;MACDH,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/CjJ,IAAI,CAACoL,IAAI,CAAC,OAAO,EAAE,yBAAyB,EAAE,OAAO,CAAC;MACxD;KACD,CAAC;EACJ;EAEA5B,wBAAwBA,CAACjB,SAAiB;IACxC,IAAI,CAAChC,iBAAiB,CAAC8E,uBAAuB,CAAC9C,SAAS,EAAE,IAAI,CAAC,CAACM,SAAS,CAAC;MACxEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAAClC,mBAAmB,GAAGiC,QAAQ,CAAClC,WAAW;;MAEnD,CAAC;MACDoC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,IAAI,CAACnC,mBAAmB,GAAG,EAAE;MAC/B;KACD,CAAC;EACJ;EAEA8C,uBAAuBA,CAACD,YAAoB;IAC1C,MAAMpB,SAAS,GAAG,IAAI,CAAC5F,aAAa,CAAC6F,GAAG,CAAC,SAAS,CAAC,EAAEtG,KAAK;IAC1D,IAAI,CAACsE,cAAc,CAAC8E,aAAa,CAAC;MAChC/J,OAAO,EAAEgH,SAAS;MAClB/G,UAAU,EAAEmI,YAAY;MACxB4B,QAAQ,EAAE;KACX,CAAC,CAAC1C,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAAChC,eAAe,GAAG+B,QAAQ,CAAChC,OAAO;;MAE3C,CAAC;MACDkC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACjC,eAAe,GAAG,EAAE;MAC3B;KACD,CAAC;EACJ;EAEA6C,wBAAwBA,CAACF,YAAoB;IAC3C,IAAI,CAAClD,cAAc,CAAC+E,uBAAuB,CAAC7B,YAAY,EAAE,IAAI,CAAC,CAACd,SAAS,CAAC;MACxEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAAC9B,gBAAgB,GAAG6B,QAAQ,CAAC9B,QAAQ;;MAE7C,CAAC;MACDgC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAC/B,gBAAgB,GAAG,EAAE;MAC5B;KACD,CAAC;EACJ;EAEAuC,qBAAqBA,CAAClB,SAAiB;IACrC,IAAI,CAAC7B,WAAW,CAAC+E,iBAAiB,CAAClD,SAAS,EAAE,SAAS,CAAC,CAACM,SAAS,CAAC;MACjEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAAC5B,gBAAgB,GAAG2B,QAAQ,CAACK,KAAK;;MAE1C,CAAC;MACDH,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1D;QACA,IAAI,CAAC7B,gBAAgB,GAAG,IAAI,CAACD,QAAQ;MACvC;KACD,CAAC;EACJ;EAEAkC,cAAcA,CAAA;IACZ,IAAI,CAAChD,gBAAgB,CAACqF,sBAAsB,EAAE,CAAC7C,SAAS,CAAC;MACvDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAAChD,UAAU,GAAG+C,QAAQ,CAAC4C,SAAS;;QAEtC,IAAI,CAACtE,OAAO,GAAG,KAAK;MACtB,CAAC;MACD4B,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,IAAI,CAAC5B,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAuE,QAAQA,CAAA;IACN,IAAI,IAAI,CAACjJ,aAAa,CAACkJ,KAAK,EAAE;MAC5B,IAAI,CAACvE,UAAU,GAAG,IAAI;MACtB,MAAMwE,QAAQ,GAAG,IAAI,CAACnJ,aAAa,CAACT,KAAK;MAEzC;MACA,MAAM6J,aAAa,GAAG;QACpBxK,OAAO,EAAEuK,QAAQ,CAACvK,OAAO;QACzBC,UAAU,EAAEsK,QAAQ,CAACtK,UAAU;QAC/BiC,KAAK,EAAEqI,QAAQ,CAACrI,KAAK;QACrBC,OAAO,EAAEoI,QAAQ,CAACpI,OAAO;QACzBJ,OAAO,EAAEwI,QAAQ,CAACxI,OAAO;QACzBF,SAAS,EAAE0I,QAAQ,CAAC1I,SAAS;QAC7Ba,QAAQ,EAAE;UACR+D,SAAS,EAAE8D,QAAQ,CAAC9D,SAAS;UAC7BC,OAAO,EAAE6D,QAAQ,CAAC7D,OAAO;UACzB9D,QAAQ,EAAEgG,QAAQ,CAAC2B,QAAQ,CAAC3H,QAAQ;SACrC;QACDG,IAAI,EAAEwH,QAAQ,CAACxH,IAAI,IAAI,EAAE;QACzBV,QAAQ,EAAEuG,QAAQ,CAAC2B,QAAQ,CAAClI,QAAQ,CAAC;QACrCC,YAAY,EAAEiI,QAAQ,CAACjI,YAAY;QACnCsE,KAAK,EAAE2D,QAAQ,CAAC3D,KAAK,IAAI;OAC1B;MAEDe,OAAO,CAAC8C,GAAG,CAAC,4BAA4B,EAAED,aAAa,CAAC,CAAC,CAAC;MAE1D,MAAME,OAAO,GAAG,IAAI,CAAC1E,kBAAkB,GACnC,IAAI,CAAClB,gBAAgB,CAAC6F,oBAAoB,CAAC,IAAI,CAAC3E,kBAAkB,EAAEwE,aAAa,CAAC,GAClF,IAAI,CAAC1F,gBAAgB,CAAC8F,oBAAoB,CAACJ,aAAa,CAAC;MAE7DE,OAAO,CAACpD,SAAS,CAAC;QAChBC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;YACpB,MAAMoD,MAAM,GAAG,IAAI,CAAC7E,kBAAkB,GAAG,SAAS,GAAG,SAAS;YAC9D,IAAI,CAACZ,QAAQ,CAAC0F,IAAI,CAAC,mBAAmBD,MAAM,eAAe,EAAE,OAAO,EAAE;cACpEjI,QAAQ,EAAE;aACX,CAAC;YACF,IAAI,CAACxB,aAAa,CAACC,KAAK,EAAE;YAC1B,IAAI,CAACD,aAAa,CAAC+G,UAAU,CAAC;cAAE7F,YAAY,EAAE;YAAW,CAAE,CAAC;YAC5D,IAAI,CAAC0D,kBAAkB,GAAG,IAAI;YAC9B,IAAI,CAAC8B,cAAc,EAAE;WACtB,MAAM;YACL,MAAM+C,MAAM,GAAG,IAAI,CAAC7E,kBAAkB,GAAG,QAAQ,GAAG,QAAQ;YAC5D,IAAI,CAACZ,QAAQ,CAAC0F,IAAI,CAACtD,QAAQ,CAACuD,OAAO,IAAI,aAAaF,MAAM,kBAAkB,EAAE,OAAO,EAAE;cACrFjI,QAAQ,EAAE;aACX,CAAC;;UAEJ,IAAI,CAACmD,UAAU,GAAG,KAAK;QACzB,CAAC;QACD2B,KAAK,EAAGA,KAAK,IAAI;UACf,MAAMmD,MAAM,GAAG,IAAI,CAAC7E,kBAAkB,GAAG,UAAU,GAAG,UAAU;UAChE2B,OAAO,CAACD,KAAK,CAAC,SAASmD,MAAM,mBAAmB,EAAEnD,KAAK,CAAC;UACxD,IAAI,CAACtC,QAAQ,CAAC0F,IAAI,CAACpD,KAAK,CAACA,KAAK,EAAEqD,OAAO,IAAI,SAASF,MAAM,kBAAkB,EAAE,OAAO,EAAE;YACrFjI,QAAQ,EAAE;WACX,CAAC;UACF,IAAI,CAACmD,UAAU,GAAG,KAAK;QACzB;OACD,CAAC;KACH,MAAM;MACL4B,OAAO,CAAC8C,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAACrJ,aAAa,CAAC4J,MAAM,CAAC;MAC1D,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAAC7J,aAAa,CAAC;;EAEjD;EAEQ6J,oBAAoBA,CAACC,SAAoB;IAC/CC,MAAM,CAACC,IAAI,CAACF,SAAS,CAACG,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MAC5C,MAAMC,OAAO,GAAGN,SAAS,CAACjE,GAAG,CAACsE,GAAG,CAAC;MAClCC,OAAO,EAAEC,aAAa,EAAE;MACxB,IAAID,OAAO,YAAYjN,SAAS,EAAE;QAChC,IAAI,CAAC0M,oBAAoB,CAACO,OAAO,CAAC;;IAEtC,CAAC,CAAC;EACJ;EAEAlI,kBAAkBA,CAAC8G,SAAc;IAC/B,IAAI,CAACpE,kBAAkB,GAAGoE,SAAS,CAACpL,GAAG;IAEvC;IACA,IAAI,CAACiJ,wBAAwB,CAACmC,SAAS,CAACpK,OAAO,CAAChB,GAAG,CAAC;IACpD,IAAI,CAACqJ,uBAAuB,CAAC+B,SAAS,CAACnK,UAAU,CAACjB,GAAG,CAAC;IACtD,IAAI,CAACsJ,wBAAwB,CAAC8B,SAAS,CAACnK,UAAU,CAACjB,GAAG,CAAC;IACvD,IAAI,CAACkJ,qBAAqB,CAACkC,SAAS,CAACpK,OAAO,CAAChB,GAAG,CAAC;IAEjD;IACA0M,UAAU,CAAC,MAAK;MACd,IAAI,CAACtK,aAAa,CAAC+G,UAAU,CAAC;QAC5BnI,OAAO,EAAEoK,SAAS,CAACpK,OAAO,CAAChB,GAAG;QAC9BiB,UAAU,EAAEmK,SAAS,CAACnK,UAAU,CAACjB,GAAG;QACpCkD,KAAK,EAAEkI,SAAS,CAAClI,KAAK,CAAClD,GAAG;QAC1BmD,OAAO,EAAEiI,SAAS,CAACjI,OAAO,CAACnD,GAAG;QAC9B+C,OAAO,EAAEqI,SAAS,CAACrI,OAAO,CAAC/C,GAAG;QAC9B6C,SAAS,EAAEuI,SAAS,CAACvI,SAAS;QAC9B4E,SAAS,EAAE2D,SAAS,CAAC1H,QAAQ,CAAC+D,SAAS;QACvCC,OAAO,EAAE0D,SAAS,CAAC1H,QAAQ,CAACgE,OAAO;QACnC9D,QAAQ,EAAEwH,SAAS,CAAC1H,QAAQ,CAACE,QAAQ;QACrCG,IAAI,EAAEqH,SAAS,CAACrH,IAAI,IAAI,EAAE;QAC1BV,QAAQ,EAAE+H,SAAS,CAAC/H,QAAQ;QAC5BC,YAAY,EAAE8H,SAAS,CAAC9H,YAAY;QACpCsE,KAAK,EAAEwD,SAAS,CAACxD,KAAK,IAAI;OAC3B,CAAC;MAEF;MACA,IAAI,CAAC2B,gBAAgB,EAAE;IACzB,CAAC,EAAE,IAAI,CAAC;IAER;IACAoD,QAAQ,CAACC,aAAa,CAAC,YAAY,CAAC,EAAEC,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAQ,CAAE,CAAC;EAC9E;EAEArK,UAAUA,CAAA;IACR,IAAI,CAACuE,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAAC5E,aAAa,CAACC,KAAK,EAAE;IAC1B,IAAI,CAACD,aAAa,CAAC+G,UAAU,CAAC;MAC5B7F,YAAY,EAAE;KACf,CAAC;EACJ;EAEAmB,oBAAoBA,CAAC2G,SAAoB;IACvC,IAAI2B,OAAO,CAAC,uDAAuD,CAAC,EAAE;MACpE,IAAI,CAACjH,gBAAgB,CAACrB,oBAAoB,CAAC2G,SAAS,CAACpL,GAAG,CAAC,CAACsI,SAAS,CAAC;QAClEC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;YACpB,IAAI,CAACrC,QAAQ,CAAC0F,IAAI,CAAC,sCAAsC,EAAE,OAAO,EAAE;cAClElI,QAAQ,EAAE;aACX,CAAC;YACF,IAAI,CAACkF,cAAc,EAAE;;QAEzB,CAAC;QACDJ,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UACvD,IAAI,CAACtC,QAAQ,CAAC0F,IAAI,CAAC,gCAAgC,EAAE,OAAO,EAAE;YAC5DlI,QAAQ,EAAE;WACX,CAAC;QACJ;OACD,CAAC;;EAEN;EAEAJ,cAAcA,CAACE,QAAa;IAC1B,OAAO,GAAGA,QAAQ,CAAC+D,SAAS,MAAM/D,QAAQ,CAACgE,OAAO,EAAE;EACtD;EAEAsF,oBAAoBA,CAAC5B,SAAc;IACjC,MAAM1H,QAAQ,GAAG,IAAI,CAACF,cAAc,CAAC4H,SAAS,CAAC1H,QAAQ,CAAC;IACxD,MAAMuJ,WAAW,GAAG7B,SAAS,CAACrI,OAAO,EAAE5C,IAAI,IAAI,iBAAiB;IAChE,MAAMW,SAAS,GAAGsK,SAAS,CAAClI,KAAK,EAAEpC,SAAS,IAAI,eAAe;IAC/D,MAAMoM,WAAW,GAAG9B,SAAS,CAACpK,OAAO,EAAEb,IAAI,IAAI,EAAE;IAEjD;IACA,IAAI+M,WAAW,KAAK,cAAc,EAAE;MAClC,OAAO,GAAGxJ,QAAQ,IAAIuJ,WAAW,SAASnM,SAAS,EAAE;KACtD,MAAM;MACL,OAAO,GAAG4C,QAAQ,IAAIuJ,WAAW,SAASnM,SAAS,EAAE;;EAEzD;EAEAmC,mBAAmBA,CAACkK,SAAc;IAChC,IAAI,CAACA,SAAS,EAAE,OAAO,eAAe;IAEtC,MAAMnM,OAAO,GAAGmM,SAAS,CAACnM,OAAO,EAAEb,IAAI,IAAI,EAAE;IAC7C,IAAIa,OAAO,KAAK,cAAc,EAAE;MAC9B,OAAO,GAAGmM,SAAS,CAACrM,SAAS,GAAGqM,SAAS,CAACpM,OAAO,GAAG,aAAa,GAAGoM,SAAS,CAACpM,OAAO,GAAG,EAAE,EAAE;KAC7F,MAAM;MACL,OAAOoM,SAAS,CAACrM,SAAS;;EAE9B;EAEA6C,gBAAgBA,CAACC,QAAgB;IAC/B,IAAIA,QAAQ,GAAG,EAAE,EAAE;MACjB,OAAO,GAAGA,QAAQ,MAAM;KACzB,MAAM,IAAIA,QAAQ,KAAK,EAAE,EAAE;MAC1B,OAAO,QAAQ;KAChB,MAAM,IAAIA,QAAQ,KAAK,EAAE,EAAE;MAC1B,OAAO,WAAW;KACnB,MAAM,IAAIA,QAAQ,KAAK,GAAG,EAAE;MAC3B,OAAO,SAAS;KACjB,MAAM,IAAIA,QAAQ,KAAK,GAAG,EAAE;MAC3B,OAAO,SAAS;KACjB,MAAM;MACL,OAAO,GAAG0G,IAAI,CAACC,KAAK,CAAC3G,QAAQ,GAAG,EAAE,CAAC,KAAKA,QAAQ,GAAG,EAAE,GAAG;;EAE5D;EAAC,QAAAwJ,CAAA,G;qBApeUzH,kBAAkB,EAAAjG,EAAA,CAAA2N,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA7N,EAAA,CAAA2N,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAA/N,EAAA,CAAA2N,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAjO,EAAA,CAAA2N,iBAAA,CAAAO,EAAA,CAAAC,iBAAA,GAAAnO,EAAA,CAAA2N,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAArO,EAAA,CAAA2N,iBAAA,CAAAW,EAAA,CAAAC,cAAA,GAAAvO,EAAA,CAAA2N,iBAAA,CAAAa,EAAA,CAAAC,WAAA,GAAAzO,EAAA,CAAA2N,iBAAA,CAAAe,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAlB3I,kBAAkB;IAAA4I,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QClB/BnP,EAAA,CAAAC,cAAA,aAAiC;QAGzBD,EAAA,CAAAE,MAAA,2BAAoB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC7BH,EAAA,CAAAC,cAAA,aAA4B;QACFD,EAAA,CAAAmC,UAAA,mBAAAkN,oDAAA;UAAA,OAASD,GAAA,CAAAhG,cAAA,EAAgB;QAAA,EAAC;QAChDpJ,EAAA,CAAAC,cAAA,eAAU;QAAAD,EAAA,CAAAE,MAAA,cAAO;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAMlCH,EAAA,CAAAC,cAAA,kBAA4B;QAGtBD,EAAA,CAAAE,MAAA,IACF;QAAAF,EAAA,CAAAG,YAAA,EAAiB;QACjBH,EAAA,CAAAC,cAAA,yBAAmB;QACjBD,EAAA,CAAAE,MAAA,IACF;QAAAF,EAAA,CAAAG,YAAA,EAAoB;QAEtBH,EAAA,CAAAC,cAAA,wBAAkB;QACkBD,EAAA,CAAAmC,UAAA,sBAAAmN,sDAAA;UAAA,OAAYF,GAAA,CAAAzD,QAAA,EAAU;QAAA,EAAC;QACvD3L,EAAA,CAAAC,cAAA,cAAuB;QAGRD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC9BH,EAAA,CAAAC,cAAA,qBAA+C;QACxBD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAa;QAChDH,EAAA,CAAA2B,UAAA,KAAA4N,yCAAA,yBAEa;QACfvP,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAA2B,UAAA,KAAA6N,wCAAA,wBAEY;QACdxP,EAAA,CAAAG,YAAA,EAAiB;QAGjBH,EAAA,CAAAC,cAAA,yBAAqC;QACxBD,EAAA,CAAAE,MAAA,kBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACjCH,EAAA,CAAAC,cAAA,sBAAoG;QAC7ED,EAAA,CAAAE,MAAA,IAAyF;QAAAF,EAAA,CAAAG,YAAA,EAAa;QAC3HH,EAAA,CAAA2B,UAAA,KAAA8N,yCAAA,yBAEa;QACfzP,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAA2B,UAAA,KAAA+N,wCAAA,wBAEY;QACd1P,EAAA,CAAAG,YAAA,EAAiB;QAGjBH,EAAA,CAAAC,cAAA,yBAAqC;QACxBD,EAAA,CAAAE,MAAA,IAA4D;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACnFH,EAAA,CAAAC,cAAA,sBAAgD;QACzBD,EAAA,CAAAE,MAAA,IAAiE;QAAAF,EAAA,CAAAG,YAAA,EAAa;QAEnGH,EAAA,CAAA2B,UAAA,KAAAgO,2CAAA,2BAGe;QAEf3P,EAAA,CAAA2B,UAAA,KAAAiO,2CAAA,4BASe;QACjB5P,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAA2B,UAAA,KAAAkO,wCAAA,wBAEY;QACd7P,EAAA,CAAAG,YAAA,EAAiB;QAGjBH,EAAA,CAAAC,cAAA,yBAAqC;QACxBD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC5BH,EAAA,CAAAC,cAAA,sBAAkG;QAC3ED,EAAA,CAAAE,MAAA,IAA0F;QAAAF,EAAA,CAAAG,YAAA,EAAa;QAC5HH,EAAA,CAAA2B,UAAA,KAAAmO,yCAAA,yBAGa;QACf9P,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAA2B,UAAA,KAAAoO,wCAAA,wBAEY;QACd/P,EAAA,CAAAG,YAAA,EAAiB;QAGjBH,EAAA,CAAAC,cAAA,yBAAqC;QACxBD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC9BH,EAAA,CAAAC,cAAA,sBAAoG;QAC7ED,EAAA,CAAAE,MAAA,IAA4F;QAAAF,EAAA,CAAAG,YAAA,EAAa;QAC9HH,EAAA,CAAA2B,UAAA,KAAAqO,yCAAA,yBAEa;QACfhQ,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAA2B,UAAA,KAAAsO,wCAAA,wBAEY;QACdjQ,EAAA,CAAAG,YAAA,EAAiB;QAGjBH,EAAA,CAAAC,cAAA,yBAAqC;QACxBD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC9BH,EAAA,CAAAC,cAAA,sBAAiG;QAC1ED,EAAA,CAAAE,MAAA,IAAsF;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACxHH,EAAA,CAAA2B,UAAA,KAAAuO,yCAAA,yBAGa;QACflQ,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAA2B,UAAA,KAAAwO,wCAAA,wBAEY;QACdnQ,EAAA,CAAAG,YAAA,EAAiB;QAGjBH,EAAA,CAAAC,cAAA,yBAAqC;QACxBD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAClCH,EAAA,CAAAC,cAAA,sBAAiD;QAC/CD,EAAA,CAAA2B,UAAA,KAAAyO,yCAAA,yBAEa;QACfpQ,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAA2B,UAAA,KAAA0O,wCAAA,wBAEY;QACdrQ,EAAA,CAAAG,YAAA,EAAiB;QAGjBH,EAAA,CAAAC,cAAA,yBAAqC;QACxBD,EAAA,CAAAE,MAAA,kBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACjCH,EAAA,CAAAC,cAAA,sBAAiD;QAC/CD,EAAA,CAAA2B,UAAA,KAAA2O,yCAAA,yBAEa;QACftQ,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAA2B,UAAA,KAAA4O,wCAAA,wBAEY;QACdvQ,EAAA,CAAAG,YAAA,EAAiB;QAGjBH,EAAA,CAAAC,cAAA,yBAAqC;QACxBD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACrCH,EAAA,CAAAC,cAAA,sBAAgD;QACzBD,EAAA,CAAAE,MAAA,uBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACjDH,EAAA,CAAA2B,UAAA,KAAA6O,yCAAA,yBAEa;QACfxQ,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAA2B,UAAA,KAAA8O,wCAAA,wBAEY;QACZzQ,EAAA,CAAA2B,UAAA,KAAA+O,uCAAA,uBAA4F;QAC9F1Q,EAAA,CAAAG,YAAA,EAAiB;QAGjBH,EAAA,CAAAC,cAAA,yBAAqC;QACxBD,EAAA,CAAAE,MAAA,kCAA0B;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACjDH,EAAA,CAAAgD,SAAA,iBAAmD;QACnDhD,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,iEAAyD;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAIhFH,EAAA,CAAAC,cAAA,yBAAqC;QACxBD,EAAA,CAAAE,MAAA,uBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACtCH,EAAA,CAAAgD,SAAA,iBAAoE;QACtEhD,EAAA,CAAAG,YAAA,EAAiB;QAGjBH,EAAA,CAAAC,cAAA,yBAAqC;QACxBD,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACpCH,EAAA,CAAAgD,SAAA,kBAAsF;QACtFhD,EAAA,CAAA2B,UAAA,MAAAgP,yCAAA,wBAEY;QACd3Q,EAAA,CAAAG,YAAA,EAAiB;QAGjBH,EAAA,CAAAC,cAAA,2BAAwD;QAC3CD,EAAA,CAAAE,MAAA,yBAAgB;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACvCH,EAAA,CAAAgD,SAAA,qBAAiG;QACnGhD,EAAA,CAAAG,YAAA,EAAiB;QAGnBH,EAAA,CAAAC,cAAA,gBAA0B;QACxBD,EAAA,CAAA2B,UAAA,MAAAiP,sCAAA,qBAGS;QACT5Q,EAAA,CAAAC,cAAA,mBAAwG;QAC5FD,EAAA,CAAAE,MAAA,aAAI;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACzBH,EAAA,CAAAE,MAAA,KACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAA2B,UAAA,MAAAkP,sCAAA,qBAGS;QACX7Q,EAAA,CAAAG,YAAA,EAAM;QAMZH,EAAA,CAAAC,cAAA,qBAA6B;QAETD,EAAA,CAAAE,MAAA,0BAAiB;QAAAF,EAAA,CAAAG,YAAA,EAAiB;QAClDH,EAAA,CAAAC,cAAA,0BAAmB;QAAAD,EAAA,CAAAE,MAAA,8BAAqB;QAAAF,EAAA,CAAAG,YAAA,EAAoB;QAE9DH,EAAA,CAAAC,cAAA,yBAAkB;QAChBD,EAAA,CAAA2B,UAAA,MAAAmP,mCAAA,kBAGM;QAEN9Q,EAAA,CAAA2B,UAAA,MAAAoP,mCAAA,mBA0EM;QAEN/Q,EAAA,CAAA2B,UAAA,MAAAqP,mCAAA,kBAIM;QACRhR,EAAA,CAAAG,YAAA,EAAmB;;;;;;;;;;;;;;;;;;;;;QAjSfH,EAAA,CAAAO,SAAA,IACF;QADEP,EAAA,CAAAe,kBAAA,MAAAqO,GAAA,CAAA9H,kBAAA,0DACF;QAEEtH,EAAA,CAAAO,SAAA,GACF;QADEP,EAAA,CAAAe,kBAAA,MAAAqO,GAAA,CAAA9H,kBAAA,yEACF;QAGMtH,EAAA,CAAAO,SAAA,GAA2B;QAA3BP,EAAA,CAAAI,UAAA,cAAAgP,GAAA,CAAA1M,aAAA,CAA2B;QAOO1C,EAAA,CAAAO,SAAA,GAAW;QAAXP,EAAA,CAAAI,UAAA,YAAAgP,GAAA,CAAAzI,QAAA,CAAW;QAIjC3G,EAAA,CAAAO,SAAA,GAAwD;QAAxDP,EAAA,CAAAI,UAAA,UAAA6Q,OAAA,GAAA7B,GAAA,CAAA1M,aAAA,CAAA6F,GAAA,8BAAA0I,OAAA,CAAAC,QAAA,aAAwD;QAQlBlR,EAAA,CAAAO,SAAA,GAAiD;QAAjDP,EAAA,CAAAI,UAAA,gBAAA+Q,OAAA,GAAA/B,GAAA,CAAA1M,aAAA,CAAA6F,GAAA,8BAAA4I,OAAA,CAAAlP,KAAA,EAAiD;QAC5EjC,EAAA,CAAAO,SAAA,GAAyF;QAAzFP,EAAA,CAAAiD,iBAAA,IAAAmO,OAAA,GAAAhC,GAAA,CAAA1M,aAAA,CAAA6F,GAAA,8BAAA6I,OAAA,CAAAnP,KAAA,iDAAyF;QAC3EjC,EAAA,CAAAO,SAAA,GAAsB;QAAtBP,EAAA,CAAAI,UAAA,YAAAgP,GAAA,CAAAvI,mBAAA,CAAsB;QAI/C7G,EAAA,CAAAO,SAAA,GAA2D;QAA3DP,EAAA,CAAAI,UAAA,UAAAiR,OAAA,GAAAjC,GAAA,CAAA1M,aAAA,CAAA6F,GAAA,iCAAA8I,OAAA,CAAAH,QAAA,aAA2D;QAO5DlR,EAAA,CAAAO,SAAA,GAA4D;QAA5DP,EAAA,CAAAiD,iBAAA,CAAAmM,GAAA,CAAAnO,qBAAA,kCAA4D;QAEhDjB,EAAA,CAAAO,SAAA,GAAiE;QAAjEP,EAAA,CAAAiD,iBAAA,CAAAmM,GAAA,CAAAnO,qBAAA,uCAAiE;QAEvEjB,EAAA,CAAAO,SAAA,GAA6B;QAA7BP,EAAA,CAAAI,UAAA,SAAAgP,GAAA,CAAAnO,qBAAA,GAA6B;QAK7BjB,EAAA,CAAAO,SAAA,GAA8B;QAA9BP,EAAA,CAAAI,UAAA,UAAAgP,GAAA,CAAAnO,qBAAA,GAA8B;QAWnCjB,EAAA,CAAAO,SAAA,GAAyD;QAAzDP,EAAA,CAAAI,UAAA,UAAAkR,QAAA,GAAAlC,GAAA,CAAA1M,aAAA,CAAA6F,GAAA,+BAAA+I,QAAA,CAAAJ,QAAA,aAAyD;QAQxBlR,EAAA,CAAAO,SAAA,GAAoD;QAApDP,EAAA,CAAAI,UAAA,gBAAAmR,QAAA,GAAAnC,GAAA,CAAA1M,aAAA,CAAA6F,GAAA,iCAAAgJ,QAAA,CAAAtP,KAAA,EAAoD;QAC1EjC,EAAA,CAAAO,SAAA,GAA0F;QAA1FP,EAAA,CAAAiD,iBAAA,IAAAuO,QAAA,GAAApC,GAAA,CAAA1M,aAAA,CAAA6F,GAAA,iCAAAiJ,QAAA,CAAAvP,KAAA,+CAA0F;QACjFjC,EAAA,CAAAO,SAAA,GAAkB;QAAlBP,EAAA,CAAAI,UAAA,YAAAgP,GAAA,CAAArI,eAAA,CAAkB;QAKtC/G,EAAA,CAAAO,SAAA,GAAsD;QAAtDP,EAAA,CAAAI,UAAA,UAAAqR,QAAA,GAAArC,GAAA,CAAA1M,aAAA,CAAA6F,GAAA,4BAAAkJ,QAAA,CAAAP,QAAA,aAAsD;QAQnBlR,EAAA,CAAAO,SAAA,GAAoD;QAApDP,EAAA,CAAAI,UAAA,gBAAAsR,QAAA,GAAAtC,GAAA,CAAA1M,aAAA,CAAA6F,GAAA,iCAAAmJ,QAAA,CAAAzP,KAAA,EAAoD;QAC5EjC,EAAA,CAAAO,SAAA,GAA4F;QAA5FP,EAAA,CAAAiD,iBAAA,IAAA0O,QAAA,GAAAvC,GAAA,CAAA1M,aAAA,CAAA6F,GAAA,iCAAAoJ,QAAA,CAAA1P,KAAA,iDAA4F;QACjFjC,EAAA,CAAAO,SAAA,GAAmB;QAAnBP,EAAA,CAAAI,UAAA,YAAAgP,GAAA,CAAAnI,gBAAA,CAAmB;QAIzCjH,EAAA,CAAAO,SAAA,GAAwD;QAAxDP,EAAA,CAAAI,UAAA,UAAAwR,QAAA,GAAAxC,GAAA,CAAA1M,aAAA,CAAA6F,GAAA,8BAAAqJ,QAAA,CAAAV,QAAA,aAAwD;QAQrBlR,EAAA,CAAAO,SAAA,GAAiD;QAAjDP,EAAA,CAAAI,UAAA,gBAAAyR,QAAA,GAAAzC,GAAA,CAAA1M,aAAA,CAAA6F,GAAA,8BAAAsJ,QAAA,CAAA5P,KAAA,EAAiD;QACzEjC,EAAA,CAAAO,SAAA,GAAsF;QAAtFP,EAAA,CAAAiD,iBAAA,IAAA6O,QAAA,GAAA1C,GAAA,CAAA1M,aAAA,CAAA6F,GAAA,8BAAAuJ,QAAA,CAAA7P,KAAA,8CAAsF;QAC3EjC,EAAA,CAAAO,SAAA,GAAmB;QAAnBP,EAAA,CAAAI,UAAA,YAAAgP,GAAA,CAAAjI,gBAAA,CAAmB;QAKzCnH,EAAA,CAAAO,SAAA,GAAwD;QAAxDP,EAAA,CAAAI,UAAA,UAAA2R,QAAA,GAAA3C,GAAA,CAAA1M,aAAA,CAAA6F,GAAA,8BAAAwJ,QAAA,CAAAb,QAAA,aAAwD;QAStClR,EAAA,CAAAO,SAAA,GAAa;QAAbP,EAAA,CAAAI,UAAA,YAAAgP,GAAA,CAAA7H,UAAA,CAAa;QAI/BvH,EAAA,CAAAO,SAAA,GAA0D;QAA1DP,EAAA,CAAAI,UAAA,UAAA4R,QAAA,GAAA5C,GAAA,CAAA1M,aAAA,CAAA6F,GAAA,gCAAAyJ,QAAA,CAAAd,QAAA,aAA0D;QASvClR,EAAA,CAAAO,SAAA,GAAsB;QAAtBP,EAAA,CAAAI,UAAA,YAAAgP,GAAA,CAAA3H,mBAAA,CAAsB;QAIzCzH,EAAA,CAAAO,SAAA,GAA0D;QAA1DP,EAAA,CAAAI,UAAA,UAAA6R,QAAA,GAAA7C,GAAA,CAAA1M,aAAA,CAAA6F,GAAA,gCAAA0J,QAAA,CAAAf,QAAA,aAA0D;QAUnClR,EAAA,CAAAO,SAAA,GAAqB;QAArBP,EAAA,CAAAI,UAAA,YAAAgP,GAAA,CAAAzH,kBAAA,CAAqB;QAI5C3H,EAAA,CAAAO,SAAA,GAAyD;QAAzDP,EAAA,CAAAI,UAAA,UAAA8R,QAAA,GAAA9C,GAAA,CAAA1M,aAAA,CAAA6F,GAAA,+BAAA2J,QAAA,CAAAhB,QAAA,aAAyD;QAG1DlR,EAAA,CAAAO,SAAA,GAAsB;QAAtBP,EAAA,CAAAI,UAAA,UAAAgP,GAAA,CAAAxH,eAAA,CAAsB;QAoBrB5H,EAAA,CAAAO,SAAA,IAA6D;QAA7DP,EAAA,CAAAI,UAAA,UAAA+R,QAAA,GAAA/C,GAAA,CAAA1M,aAAA,CAAA6F,GAAA,mCAAA4J,QAAA,CAAAjB,QAAA,aAA6D;QAaFlR,EAAA,CAAAO,SAAA,GAAyB;QAAzBP,EAAA,CAAAI,UAAA,UAAAgP,GAAA,CAAA9H,kBAAA,CAAyB;QAI1CtH,EAAA,CAAAO,SAAA,GAA+C;QAA/CP,EAAA,CAAAI,UAAA,cAAAgP,GAAA,CAAA1M,aAAA,CAAAkJ,KAAA,IAAAwD,GAAA,CAAA/H,UAAA,CAA+C;QAErGrH,EAAA,CAAAO,SAAA,GACF;QADEP,EAAA,CAAAe,kBAAA,MAAAqO,GAAA,CAAA/H,UAAA,GAAA+H,GAAA,CAAA9H,kBAAA,mCAAA8H,GAAA,CAAA9H,kBAAA,wCACF;QACyDtH,EAAA,CAAAO,SAAA,GAAwB;QAAxBP,EAAA,CAAAI,UAAA,SAAAgP,GAAA,CAAA9H,kBAAA,CAAwB;QAgB/EtH,EAAA,CAAAO,SAAA,GAAa;QAAbP,EAAA,CAAAI,UAAA,SAAAgP,GAAA,CAAAhI,OAAA,CAAa;QAKbpH,EAAA,CAAAO,SAAA,GAAuC;QAAvCP,EAAA,CAAAI,UAAA,UAAAgP,GAAA,CAAAhI,OAAA,IAAAgI,GAAA,CAAArJ,UAAA,CAAAqM,MAAA,KAAuC;QA4EvCpS,EAAA,CAAAO,SAAA,GAAyC;QAAzCP,EAAA,CAAAI,UAAA,UAAAgP,GAAA,CAAAhI,OAAA,IAAAgI,GAAA,CAAArJ,UAAA,CAAAqM,MAAA,OAAyC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}