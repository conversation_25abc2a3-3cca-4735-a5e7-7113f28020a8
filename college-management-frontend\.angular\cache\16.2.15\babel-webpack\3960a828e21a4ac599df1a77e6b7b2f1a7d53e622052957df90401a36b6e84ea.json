{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { AuthRoutingModule } from './auth/auth-routing.module';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { MaterialModule } from './material';\nimport { HttpClientModule } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nexport let AppModule = /*#__PURE__*/(() => {\n  class AppModule {\n    static #_ = this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [BrowserModule, AppRoutingModule, AuthRoutingModule, BrowserAnimationsModule, MaterialModule, HttpClientModule]\n    });\n  }\n  return AppModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}