{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { SubjectListComponent } from './subject-list/subject-list.component';\nimport { SubjectFormComponent } from './subject-form/subject-form.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: SubjectListComponent\n}, {\n  path: 'add-subject',\n  component: SubjectFormComponent\n}, {\n  path: 'add-subject/:id',\n  component: SubjectFormComponent\n}];\nexport let SubjectRoutingModule = /*#__PURE__*/(() => {\n  class SubjectRoutingModule {\n    static #_ = this.ɵfac = function SubjectRoutingModule_Factory(t) {\n      return new (t || SubjectRoutingModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SubjectRoutingModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n  return SubjectRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}