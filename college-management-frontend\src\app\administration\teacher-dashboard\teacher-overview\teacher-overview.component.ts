import { Component, OnInit } from '@angular/core';
import { DashboardService } from '../../../services/dashboard.service';
import { UserService } from '../../../services/user.service';
import { TeacherDashboard } from '../../../models/dashboard';

@Component({
  selector: 'app-teacher-overview',
  templateUrl: './teacher-overview.component.html',
  styleUrls: ['./teacher-overview.component.css']
})
export class TeacherOverviewComponent implements OnInit {
  teacherDashboard: TeacherDashboard | null = null;
  loading = true;
  error: string | null = null;
  currentUser: any;
  currentDate = new Date();

  constructor(
    private dashboardService: DashboardService,
    private userService: UserService
  ) {}

  ngOnInit(): void {
    this.currentUser = this.userService.getUserFromLocalStorage()?.user;
    if (this.currentUser) {
      this.loadTeacherDashboard();
    } else {
      this.error = 'User not found';
      this.loading = false;
    }
  }

  loadTeacherDashboard(): void {
    this.loading = true;
    this.error = null;

    this.dashboardService.getTeacherDashboard(this.currentUser._id).subscribe({
      next: (response) => {
        if (response.success) {
          this.teacherDashboard = response.dashboard;
        } else {
          this.error = 'Failed to load teacher dashboard';
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading teacher dashboard:', error);
        this.error = 'Error loading dashboard data';
        this.loading = false;
      }
    });
  }

  refreshData(): void {
    this.loadTeacherDashboard();
  }

  getTodayClasses(): any[] {
    if (!this.teacherDashboard?.timetable) return [];
    
    const today = new Date().toLocaleDateString('en-US', { weekday: 'long' });
    return this.teacherDashboard.timetable.filter(entry => entry.dayOfWeek === today);
  }

  getUpcomingClass(): any {
    const todayClasses = this.getTodayClasses();
    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();

    return todayClasses.find(classItem => {
      const [hours, minutes] = classItem.timeSlot.startTime.split(':').map(Number);
      const classTime = hours * 60 + minutes;
      return classTime > currentTime;
    });
  }
}
