{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { AuthRoutingModule } from './auth/auth-routing.module';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { MaterialModule } from './material';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { ErrorInterceptor } from './interceptors/error.interceptor';\nimport { AuthInterceptor } from './interceptors/auth.interceptor';\nimport * as i0 from \"@angular/core\";\nexport class AppModule {\n  static #_ = this.ɵfac = function AppModule_Factory(t) {\n    return new (t || AppModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AppModule,\n    bootstrap: [AppComponent]\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [{\n      provide: HTTP_INTERCEPTORS,\n      useClass: AuthInterceptor,\n      multi: true\n    }, {\n      provide: HTTP_INTERCEPTORS,\n      useClass: ErrorInterceptor,\n      multi: true\n    }],\n    imports: [BrowserModule, AppRoutingModule, AuthRoutingModule, BrowserAnimationsModule, MaterialModule, HttpClientModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent],\n    imports: [BrowserModule, AppRoutingModule, AuthRoutingModule, BrowserAnimationsModule, MaterialModule, HttpClientModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "AppRoutingModule", "AppComponent", "AuthRoutingModule", "BrowserAnimationsModule", "MaterialModule", "HttpClientModule", "HTTP_INTERCEPTORS", "ErrorInterceptor", "AuthInterceptor", "AppModule", "_", "_2", "bootstrap", "_3", "provide", "useClass", "multi", "imports", "declarations"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { BrowserModule } from '@angular/platform-browser';\r\n\r\nimport { AppRoutingModule } from './app-routing.module';\r\nimport { AppComponent } from './app.component';\r\nimport { AuthRoutingModule } from './auth/auth-routing.module';\r\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\r\nimport { MaterialModule } from './material';\r\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\r\nimport { ErrorInterceptor } from './interceptors/error.interceptor';\r\nimport { AuthInterceptor } from './interceptors/auth.interceptor';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    AppComponent\r\n  ],\r\n  imports: [\r\n    BrowserModule,\r\n    AppRoutingModule,\r\n    AuthRoutingModule,\r\n    BrowserAnimationsModule,\r\n    MaterialModule,\r\n    HttpClientModule\r\n  ],\r\n  providers: [\r\n    {\r\n      provide: HTTP_INTERCEPTORS,\r\n      useClass: AuthInterceptor,\r\n      multi: true\r\n    },\r\n    {\r\n      provide: HTTP_INTERCEPTORS,\r\n      useClass: ErrorInterceptor,\r\n      multi: true\r\n    }\r\n  ],\r\n  bootstrap: [AppComponent]\r\n})\r\nexport class AppModule { }\r\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AAEzD,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,cAAc,QAAQ,YAAY;AAC3C,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,sBAAsB;AAC1E,SAASC,gBAAgB,QAAQ,kCAAkC;AACnE,SAASC,eAAe,QAAQ,iCAAiC;;AA6BjE,OAAM,MAAOC,SAAS;EAAA,QAAAC,CAAA,G;qBAATD,SAAS;EAAA;EAAA,QAAAE,EAAA,G;UAATF,SAAS;IAAAG,SAAA,GAFRX,YAAY;EAAA;EAAA,QAAAY,EAAA,G;eAZb,CACT;MACEC,OAAO,EAAER,iBAAiB;MAC1BS,QAAQ,EAAEP,eAAe;MACzBQ,KAAK,EAAE;KACR,EACD;MACEF,OAAO,EAAER,iBAAiB;MAC1BS,QAAQ,EAAER,gBAAgB;MAC1BS,KAAK,EAAE;KACR,CACF;IAAAC,OAAA,GAlBClB,aAAa,EACbC,gBAAgB,EAChBE,iBAAiB,EACjBC,uBAAuB,EACvBC,cAAc,EACdC,gBAAgB;EAAA;;;2EAgBPI,SAAS;IAAAS,YAAA,GAxBlBjB,YAAY;IAAAgB,OAAA,GAGZlB,aAAa,EACbC,gBAAgB,EAChBE,iBAAiB,EACjBC,uBAAuB,EACvBC,cAAc,EACdC,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}