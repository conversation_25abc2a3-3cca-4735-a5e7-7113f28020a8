{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class AuthInterceptor {\n  intercept(req, next) {\n    // Get the auth token from local storage\n    const authToken = localStorage.getItem('token');\n    // Clone the request and add the authorization header if token exists\n    if (authToken) {\n      const authReq = req.clone({\n        setHeaders: {\n          Authorization: `Bear<PERSON> ${authToken}`\n        }\n      });\n      return next.handle(authReq);\n    }\n    // If no token, proceed with original request\n    return next.handle(req);\n  }\n  static #_ = this.ɵfac = function AuthInterceptor_Factory(t) {\n    return new (t || AuthInterceptor)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthInterceptor,\n    factory: AuthInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["AuthInterceptor", "intercept", "req", "next", "authToken", "localStorage", "getItem", "authReq", "clone", "setHeaders", "Authorization", "handle", "_", "_2", "factory", "ɵfac"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\interceptors\\auth.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\n\r\n@Injectable()\r\nexport class AuthInterceptor implements HttpInterceptor {\r\n\r\n  intercept(req: HttpRequest<any>, next: <PERSON>ttp<PERSON><PERSON><PERSON>): Observable<HttpEvent<any>> {\r\n    // Get the auth token from local storage\r\n    const authToken = localStorage.getItem('token');\r\n\r\n    // Clone the request and add the authorization header if token exists\r\n    if (authToken) {\r\n      const authReq = req.clone({\r\n        setHeaders: {\r\n          Authorization: `Bearer ${authToken}`\r\n        }\r\n      });\r\n      return next.handle(authReq);\r\n    }\r\n\r\n    // If no token, proceed with original request\r\n    return next.handle(req);\r\n  }\r\n}\r\n"], "mappings": ";AAKA,OAAM,MAAOA,eAAe;EAE1BC,SAASA,CAACC,GAAqB,EAAEC,IAAiB;IAChD;IACA,MAAMC,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE/C;IACA,IAAIF,SAAS,EAAE;MACb,MAAMG,OAAO,GAAGL,GAAG,CAACM,KAAK,CAAC;QACxBC,UAAU,EAAE;UACVC,aAAa,EAAE,UAAUN,SAAS;;OAErC,CAAC;MACF,OAAOD,IAAI,CAACQ,MAAM,CAACJ,OAAO,CAAC;;IAG7B;IACA,OAAOJ,IAAI,CAACQ,MAAM,CAACT,GAAG,CAAC;EACzB;EAAC,QAAAU,CAAA,G;qBAlBUZ,eAAe;EAAA;EAAA,QAAAa,EAAA,G;WAAfb,eAAe;IAAAc,OAAA,EAAfd,eAAe,CAAAe;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}