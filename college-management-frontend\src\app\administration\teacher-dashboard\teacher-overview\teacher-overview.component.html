<div class="teacher-dashboard">
  <!-- Loading State -->
  <div *ngIf="loading" class="loading-container">
    <mat-spinner></mat-spinner>
    <p>Loading your dashboard...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !loading" class="error-container">
    <mat-icon color="warn">error</mat-icon>
    <p>{{ error }}</p>
    <button mat-raised-button color="primary" (click)="refreshData()">
      <mat-icon>refresh</mat-icon>
      Retry
    </button>
  </div>

  <!-- Dashboard Content -->
  <div *ngIf="teacherDashboard && !loading && !error" class="dashboard-content">
    <!-- Header -->
    <div class="dashboard-header">
      <div class="welcome-section">
        <h1>Welcome back, {{ currentUser?.name }}!</h1>
        <p>Here's your teaching overview for today</p>
      </div>
      <button mat-icon-button (click)="refreshData()" matTooltip="Refresh Data">
        <mat-icon>refresh</mat-icon>
      </button>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-grid">
      <mat-card class="stat-card classes">
        <mat-card-content>
          <div class="stat-content">
            <div class="stat-icon">
              <mat-icon>class</mat-icon>
            </div>
            <div class="stat-info">
              <h3>{{ teacherDashboard.stats.totalClasses }}</h3>
              <p>My Classes</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card subjects">
        <mat-card-content>
          <div class="stat-content">
            <div class="stat-icon">
              <mat-icon>subject</mat-icon>
            </div>
            <div class="stat-info">
              <h3>{{ teacherDashboard.stats.totalSubjects }}</h3>
              <p>Subjects Teaching</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card students">
        <mat-card-content>
          <div class="stat-content">
            <div class="stat-icon">
              <mat-icon>school</mat-icon>
            </div>
            <div class="stat-info">
              <h3>{{ teacherDashboard.stats.totalStudents }}</h3>
              <p>Total Students</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card today-classes">
        <mat-card-content>
          <div class="stat-content">
            <div class="stat-icon">
              <mat-icon>today</mat-icon>
            </div>
            <div class="stat-info">
              <h3>{{ getTodayClasses().length }}</h3>
              <p>Classes Today</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Today's Schedule and Upcoming Class -->
    <div class="schedule-section">
      <div class="schedule-row">
        <!-- Today's Classes -->
        <mat-card class="schedule-card">
          <mat-card-header>
            <mat-card-title>Today's Schedule</mat-card-title>
            <mat-card-subtitle>{{ currentDate | date:'fullDate' }}</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <div *ngIf="getTodayClasses().length > 0; else noClassesToday" class="classes-list">
              <div *ngFor="let classItem of getTodayClasses()" class="class-item">
                <div class="time-slot">
                  <span class="time">{{ classItem.timeSlot.startTime }} - {{ classItem.timeSlot.endTime }}</span>
                </div>
                <div class="class-details">
                  <h4>{{ classItem.subject.subjectName }}</h4>
                  <p>{{ classItem.class.className }} - {{ classItem.class.section }}</p>
                  <p class="department">{{ classItem.department.name }}</p>
                  <span class="room" *ngIf="classItem.room">Room: {{ classItem.room }}</span>
                </div>
              </div>
            </div>
            <ng-template #noClassesToday>
              <div class="no-classes">
                <mat-icon>event_available</mat-icon>
                <p>No classes scheduled for today</p>
              </div>
            </ng-template>
          </mat-card-content>
        </mat-card>

        <!-- Upcoming Class -->
        <mat-card class="upcoming-card">
          <mat-card-header>
            <mat-card-title>Next Class</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div *ngIf="getUpcomingClass(); else noUpcomingClass" class="upcoming-class">
              <div class="upcoming-time">
                <h2>{{ getUpcomingClass().timeSlot.startTime }}</h2>
                <p>{{ getUpcomingClass().timeSlot.endTime }}</p>
              </div>
              <div class="upcoming-details">
                <h3>{{ getUpcomingClass().subject.subjectName }}</h3>
                <p>{{ getUpcomingClass().class.className }} - {{ getUpcomingClass().class.section }}</p>
                <p class="upcoming-room" *ngIf="getUpcomingClass().room">{{ getUpcomingClass().room }}</p>
              </div>
            </div>
            <ng-template #noUpcomingClass>
              <div class="no-upcoming">
                <mat-icon>schedule</mat-icon>
                <p>No more classes today</p>
              </div>
            </ng-template>
          </mat-card-content>
        </mat-card>
      </div>
    </div>

    <!-- Recent Attendance -->
    <div class="recent-section">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Recent Attendance</mat-card-title>
          <mat-card-subtitle>Last 5 attendance records</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <div *ngIf="teacherDashboard.recentAttendance.length > 0; else noRecentAttendance" class="attendance-list">
            <div *ngFor="let attendance of teacherDashboard.recentAttendance.slice(0, 5)" class="attendance-item">
              <div class="student-info">
                <span class="student-name">{{ attendance.student.name }}</span>
                <span class="student-reg">{{ attendance.student.regNo }}</span>
              </div>
              <div class="attendance-details">
                <span class="subject">{{ attendance.subject.subjectName }}</span>
                <span class="date">{{ attendance.date | date:'shortDate' }}</span>
              </div>
              <div class="attendance-status">
                <mat-chip [color]="attendance.status === 'present' ? 'primary' : 'warn'" selected>
                  {{ attendance.status | titlecase }}
                </mat-chip>
              </div>
            </div>
          </div>
          <ng-template #noRecentAttendance>
            <div class="no-data">
              <mat-icon>fact_check</mat-icon>
              <p>No recent attendance records</p>
            </div>
          </ng-template>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
      <h2>Quick Actions</h2>
      <div class="action-buttons">
        <button mat-raised-button color="primary" routerLink="/dashboard/teacher/attendance">
          <mat-icon>fact_check</mat-icon>
          Mark Attendance
        </button>
        <button mat-raised-button color="accent" routerLink="/dashboard/teacher/classes">
          <mat-icon>class</mat-icon>
          View My Classes
        </button>
        <button mat-raised-button color="primary" routerLink="/dashboard/teacher/all-students">
          <mat-icon>school</mat-icon>
          View Students
        </button>
      </div>
    </div>
  </div>
</div>
