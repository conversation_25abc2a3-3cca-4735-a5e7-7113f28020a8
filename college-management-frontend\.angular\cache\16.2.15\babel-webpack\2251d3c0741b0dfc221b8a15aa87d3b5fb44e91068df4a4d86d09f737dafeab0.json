{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/services/attendance.service\";\nimport * as i3 from \"src/app/services/classes.service\";\nimport * as i4 from \"src/app/services/subject.service\";\nimport * as i5 from \"src/app/services/user.service\";\nimport * as i6 from \"src/app/services/program.service\";\nimport * as i7 from \"src/app/services/department.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@angular/material/core\";\nimport * as i11 from \"@angular/material/button\";\nimport * as i12 from \"@angular/material/card\";\nimport * as i13 from \"@angular/material/icon\";\nimport * as i14 from \"@angular/material/input\";\nimport * as i15 from \"@angular/material/form-field\";\nimport * as i16 from \"@angular/material/progress-spinner\";\nimport * as i17 from \"@angular/material/select\";\nimport * as i18 from \"@angular/material/tooltip\";\nfunction AttendanceComponent_mat_option_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const program_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", program_r6._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", program_r6.name, \" \");\n  }\n}\nfunction AttendanceComponent_mat_option_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const cls_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", cls_r7._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", cls_r7.className, \" - \", cls_r7.section, \" \");\n  }\n}\nfunction AttendanceComponent_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42)(2, \"mat-card\", 43)(3, \"mat-card-content\")(4, \"div\", 44)(5, \"div\", 45)(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"check_circle\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 46)(9, \"h3\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"p\");\n    i0.ɵɵtext(12, \"Present\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(13, \"mat-card\", 47)(14, \"mat-card-content\")(15, \"div\", 44)(16, \"div\", 45)(17, \"mat-icon\");\n    i0.ɵɵtext(18, \"cancel\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 46)(20, \"h3\");\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"p\");\n    i0.ɵɵtext(23, \"Absent\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(24, \"mat-card\", 48)(25, \"mat-card-content\")(26, \"div\", 44)(27, \"div\", 45)(28, \"mat-icon\");\n    i0.ɵɵtext(29, \"people\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 46)(31, \"h3\");\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"p\");\n    i0.ɵɵtext(34, \"Total Records\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(35, \"mat-card\", 49)(36, \"mat-card-content\")(37, \"div\", 44)(38, \"div\", 45)(39, \"mat-icon\");\n    i0.ɵɵtext(40, \"assessment\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"div\", 46)(42, \"h3\");\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"p\");\n    i0.ɵɵtext(45, \"Attendance Rate\");\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r2.statistics.present || 0);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r2.statistics.absent || 0);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r2.statistics.total || 0);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.getAttendancePercentage(), \"%\");\n  }\n}\nconst _c0 = function (a0, a1, a2) {\n  return {\n    \"status-present\": a0,\n    \"status-absent\": a1,\n    \"status-late\": a2\n  };\n};\nfunction AttendanceComponent_tr_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 50)(1, \"td\", 24)(2, \"div\", 51)(3, \"span\", 52);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"small\", 53);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"td\", 25)(10, \"div\", 54)(11, \"div\", 55);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"small\", 56);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 57)(16, \"small\", 35);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(18, \"td\", 26)(19, \"div\", 58)(20, \"span\", 59);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\", 60);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"td\", 27)(25, \"div\", 61)(26, \"span\", 62);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"small\", 63);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"td\", 28)(31, \"span\", 64)(32, \"mat-icon\", 65);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"span\", 66);\n    i0.ɵɵtext(35);\n    i0.ɵɵpipe(36, \"titlecase\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(37, \"td\", 29)(38, \"span\", 67);\n    i0.ɵɵtext(39);\n    i0.ɵɵpipe(40, \"date\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const record_r8 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 14, record_r8.date, \"dd/MM\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(8, 17, record_r8.date, \"yyyy\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((record_r8 == null ? null : record_r8.student == null ? null : record_r8.student.name) || \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((record_r8 == null ? null : record_r8.student == null ? null : record_r8.student.rollNo) || \"N/A\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", (record_r8 == null ? null : record_r8.class == null ? null : record_r8.class.className) || \"N/A\", \"-\", (record_r8 == null ? null : record_r8.class == null ? null : record_r8.class.section) || \"\", \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((record_r8 == null ? null : record_r8.class == null ? null : record_r8.class.className) || \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((record_r8 == null ? null : record_r8.class == null ? null : record_r8.class.section) || \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((record_r8 == null ? null : record_r8.subject == null ? null : record_r8.subject.subjectName) || \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((record_r8 == null ? null : record_r8.teacher == null ? null : record_r8.teacher.name) || \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(25, _c0, record_r8.status === \"present\", record_r8.status === \"absent\", record_r8.status === \"late\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", record_r8.status === \"present\" ? \"check_circle\" : record_r8.status === \"absent\" ? \"cancel\" : \"schedule\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(36, 20, record_r8.status));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(40, 22, record_r8.createdAt, \"HH:mm\") || \"--:--\");\n  }\n}\nfunction AttendanceComponent_div_82_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵelement(1, \"mat-spinner\", 69);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading attendance records...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AttendanceComponent_div_83_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"mat-icon\", 71);\n    i0.ɵɵtext(2, \"event_busy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"No Records Found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"No attendance records match your current filters.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function AttendanceComponent_div_83_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.resetFilters());\n    });\n    i0.ɵɵtext(8, \"Reset Filters\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class AttendanceComponent {\n  constructor(router, attendanceService, classesService, subjectService, userService, programService, departmentService) {\n    this.router = router;\n    this.attendanceService = attendanceService;\n    this.classesService = classesService;\n    this.subjectService = subjectService;\n    this.userService = userService;\n    this.programService = programService;\n    this.departmentService = departmentService;\n    // Data arrays\n    this.attendanceRecords = [];\n    this.programs = [];\n    this.departments = [];\n    this.classes = [];\n    this.subjects = [];\n    this.teachers = [];\n    this.students = [];\n    this.filteredDepartments = [];\n    this.filteredClasses = [];\n    // Filter and pagination\n    this.filters = {\n      page: 1,\n      limit: 20\n    };\n    // Pagination properties\n    this.currentPage = 1;\n    this.totalPages = 1;\n    this.totalRecords = 0;\n    this.itemsPerPage = 20;\n    // UI state\n    this.loading = false;\n    this.searchQuery = '';\n    this.selectedDateRange = 'today';\n    this.Math = Math; // Make Math available in template\n    // Statistics\n    this.statistics = null;\n    // Date filters\n    this.startDate = '';\n    this.endDate = '';\n    this.dateRange = 'daily'; // daily, monthly, yearly\n    // Selected filters\n    this.selectedProgram = '';\n    this.selectedDepartment = '';\n    this.selectedClass = '';\n    this.selectedSubject = '';\n    this.selectedTeacher = '';\n    this.selectedStatus = '';\n    this.selectedStudent = '';\n  }\n  ngOnInit() {\n    this.loadInitialData();\n    this.setupFormSubscriptions();\n    this.setDefaultDateRange();\n    this.loadAttendanceRecords();\n    this.loadStatistics();\n  }\n  loadInitialData() {\n    // Load programs\n    this.programService.getAllPrograms(true).subscribe({\n      next: response => {\n        if (response.success) {\n          this.programs = response.programs;\n        }\n      },\n      error: error => console.error('Error loading programs:', error)\n    });\n    // Load departments\n    this.departmentService.getAllDepartments().subscribe({\n      next: response => {\n        if (response.success) {\n          this.departments = response.departments;\n          this.filteredDepartments = this.departments;\n        }\n      },\n      error: error => console.error('Error loading departments:', error)\n    });\n    // Load classes\n    this.classesService.getAllClasses().subscribe({\n      next: response => {\n        if (response.success) {\n          this.classes = response.classes;\n          this.filteredClasses = this.classes;\n        }\n      },\n      error: error => console.error('Error loading classes:', error)\n    });\n    // Load subjects\n    this.subjectService.getAllSubjects().subscribe({\n      next: response => {\n        if (response.success) {\n          this.subjects = response.subjects;\n        }\n      },\n      error: error => console.error('Error loading subjects:', error)\n    });\n    // Load teachers\n    this.userService.getUsersByRole('Teacher').subscribe({\n      next: response => {\n        if (response.success) {\n          this.teachers = response.users;\n        }\n      },\n      error: error => console.error('Error loading teachers:', error)\n    });\n    // Load students\n    this.userService.getUsersByRole('Student').subscribe({\n      next: response => {\n        if (response.success) {\n          this.students = response.users;\n        }\n      },\n      error: error => console.error('Error loading students:', error)\n    });\n  }\n  setupFormSubscriptions() {\n    // This would be implemented with reactive forms for better filtering\n    // For now, we'll handle filtering in the filter change methods\n  }\n  setDefaultDateRange() {\n    const today = new Date();\n    this.endDate = today.toISOString().split('T')[0];\n    // Set start date based on selected range\n    switch (this.dateRange) {\n      case 'daily':\n        this.startDate = this.endDate;\n        break;\n      case 'monthly':\n        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);\n        this.startDate = monthStart.toISOString().split('T')[0];\n        break;\n      case 'yearly':\n        const yearStart = new Date(today.getFullYear(), 0, 1);\n        this.startDate = yearStart.toISOString().split('T')[0];\n        break;\n    }\n  }\n  loadAttendanceRecords() {\n    this.loading = true;\n    // Build filters\n    const apiFilters = {\n      ...this.filters\n    };\n    if (this.selectedProgram) apiFilters.program = this.selectedProgram;\n    if (this.selectedDepartment) apiFilters.department = this.selectedDepartment;\n    if (this.selectedClass) apiFilters.classId = this.selectedClass;\n    if (this.selectedSubject) apiFilters.subjectId = this.selectedSubject;\n    if (this.selectedTeacher) apiFilters.teacherId = this.selectedTeacher;\n    if (this.selectedStudent) apiFilters.studentId = this.selectedStudent;\n    if (this.selectedStatus) apiFilters.status = this.selectedStatus;\n    if (this.startDate) apiFilters.startDate = this.startDate;\n    if (this.endDate) apiFilters.endDate = this.endDate;\n    if (this.searchQuery) apiFilters.search = this.searchQuery;\n    this.attendanceService.getAllAttendanceRecords(apiFilters).subscribe({\n      next: response => {\n        if (response.success) {\n          this.attendanceRecords = response.attendance;\n          // Update pagination info from API response\n          if (response.pagination) {\n            this.currentPage = response.pagination.currentPage;\n            this.totalPages = response.pagination.totalPages;\n            this.totalRecords = response.pagination.totalRecords;\n          }\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading attendance records:', error);\n        this.loading = false;\n      }\n    });\n  }\n  loadStatistics() {\n    const statsFilters = {};\n    if (this.selectedClass) statsFilters.classId = this.selectedClass;\n    if (this.selectedSubject) statsFilters.subjectId = this.selectedSubject;\n    if (this.startDate) statsFilters.startDate = this.startDate;\n    if (this.endDate) statsFilters.endDate = this.endDate;\n    this.attendanceService.getAttendanceStatistics(statsFilters).subscribe({\n      next: response => {\n        if (response.success) {\n          this.statistics = response.statistics;\n        }\n      },\n      error: error => console.error('Error loading statistics:', error)\n    });\n  }\n  // Filter methods for cascading dropdowns\n  onProgramChange() {\n    if (this.selectedProgram) {\n      this.filteredDepartments = this.departments.filter(dept => dept.program._id === this.selectedProgram);\n      this.selectedDepartment = '';\n      this.selectedClass = '';\n      this.filteredClasses = [];\n    } else {\n      this.filteredDepartments = this.departments;\n      this.selectedDepartment = '';\n      this.selectedClass = '';\n      this.filteredClasses = this.classes;\n    }\n    this.applyFilters();\n  }\n  onDepartmentChange() {\n    if (this.selectedDepartment) {\n      this.filteredClasses = this.classes.filter(cls => cls.department._id === this.selectedDepartment);\n      this.selectedClass = '';\n    } else {\n      this.filteredClasses = this.selectedProgram ? this.classes.filter(cls => cls.program._id === this.selectedProgram) : this.classes;\n      this.selectedClass = '';\n    }\n    this.applyFilters();\n  }\n  onDateRangeChange() {\n    this.setDefaultDateRange();\n    this.applyFilters();\n  }\n  applyFilters() {\n    this.filters.page = 1; // Reset to first page\n    this.currentPage = 1;\n    this.loadAttendanceRecords();\n    this.loadStatistics();\n  }\n  resetFilters() {\n    this.selectedProgram = '';\n    this.selectedDepartment = '';\n    this.selectedClass = '';\n    this.selectedSubject = '';\n    this.selectedTeacher = '';\n    this.selectedStudent = '';\n    this.selectedStatus = '';\n    this.dateRange = 'daily';\n    this.setDefaultDateRange();\n    this.filteredDepartments = this.departments;\n    this.filteredClasses = this.classes;\n    this.filters = {\n      page: 1,\n      limit: 20\n    };\n    this.currentPage = 1;\n    this.loadAttendanceRecords();\n    this.loadStatistics();\n  }\n  onPageChange(page) {\n    this.filters.page = page;\n    this.currentPage = page;\n    this.loadAttendanceRecords();\n  }\n  // Pagination navigation methods\n  nextPage() {\n    if (this.currentPage < this.totalPages) {\n      this.onPageChange(this.currentPage + 1);\n    }\n  }\n  previousPage() {\n    if (this.currentPage > 1) {\n      this.onPageChange(this.currentPage - 1);\n    }\n  }\n  exportAttendance() {\n    // TODO: Implement export functionality\n    alert('Export functionality will be implemented');\n  }\n  // Quick date filter methods\n  setQuickDate(range) {\n    this.selectedDateRange = range;\n    const today = new Date();\n    switch (range) {\n      case 'today':\n        this.startDate = this.formatDateForInput(today);\n        this.endDate = this.formatDateForInput(today);\n        break;\n      case 'week':\n        const weekStart = new Date(today);\n        weekStart.setDate(today.getDate() - today.getDay());\n        const weekEnd = new Date(weekStart);\n        weekEnd.setDate(weekStart.getDate() + 6);\n        this.startDate = this.formatDateForInput(weekStart);\n        this.endDate = this.formatDateForInput(weekEnd);\n        break;\n      case 'month':\n        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);\n        const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);\n        this.startDate = this.formatDateForInput(monthStart);\n        this.endDate = this.formatDateForInput(monthEnd);\n        break;\n    }\n    this.applyFilters();\n  }\n  formatDateForInput(date) {\n    return date.toISOString().split('T')[0];\n  }\n  get filteredRecords() {\n    if (!this.searchQuery) return this.attendanceRecords;\n    const searchLower = this.searchQuery.toLowerCase();\n    return this.attendanceRecords.filter(record => record.student?.name?.toLowerCase().includes(searchLower) || record.student?.rollNo?.toLowerCase().includes(searchLower) || record.class?.className?.toLowerCase().includes(searchLower) || record.subject?.subjectName?.toLowerCase().includes(searchLower) || record.teacher?.name?.toLowerCase().includes(searchLower));\n  }\n  // Getter for paginated students (formatted from attendance records)\n  get paginatedStudents() {\n    return this.attendanceRecords.map(record => ({\n      name: record.student?.name || 'N/A',\n      rollNumber: record.student?.rollNo || 'N/A',\n      fatherName: record.student?.father_name || 'N/A',\n      department: record?.student?.department?.name || record.class?.department?.name || 'N/A',\n      class: record.class?.className || 'N/A',\n      section: record.class?.section || 'N/A',\n      registrationNo: record.student?.regNo || 'N/A',\n      mobileNo: record.student?.contact || 'N/A',\n      status: record.status\n    }));\n  }\n  formatDate(date) {\n    return new Date(date).toLocaleDateString();\n  }\n  getStatusClass(status) {\n    switch (status) {\n      case 'present':\n        return 'status-present';\n      case 'absent':\n        return 'status-absent';\n      case 'late':\n        return 'status-late';\n      default:\n        return '';\n    }\n  }\n  getAttendancePercentage() {\n    if (!this.statistics || !this.statistics.total || this.statistics.total === 0) {\n      return 0;\n    }\n    const presentCount = (this.statistics.present || 0) + (this.statistics.late || 0);\n    return Math.round(presentCount / this.statistics.total * 100);\n  }\n  static #_ = this.ɵfac = function AttendanceComponent_Factory(t) {\n    return new (t || AttendanceComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AttendanceService), i0.ɵɵdirectiveInject(i3.ClassesService), i0.ɵɵdirectiveInject(i4.SubjectService), i0.ɵɵdirectiveInject(i5.UserService), i0.ɵɵdirectiveInject(i6.ProgramService), i0.ɵɵdirectiveInject(i7.DepartmentService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AttendanceComponent,\n    selectors: [[\"app-attendance\"]],\n    decls: 97,\n    vars: 18,\n    consts: [[1, \"attendance-container\"], [1, \"page-header\"], [1, \"header-content\"], [1, \"page-title\"], [1, \"title-icon\"], [1, \"page-subtitle\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"filters-card\"], [1, \"simple-filters\"], [\"appearance\", \"outline\"], [3, \"ngModel\", \"ngModelChange\", \"selectionChange\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"matInput\", \"\", \"type\", \"date\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [1, \"filter-actions\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", 3, \"click\"], [\"mat-button\", \"\", 3, \"click\"], [\"class\", \"stats-section\", 4, \"ngIf\"], [1, \"table-card\"], [1, \"table-container\"], [1, \"table-responsive\"], [1, \"compact-table\"], [1, \"table-header\"], [1, \"col-date\"], [1, \"col-student\"], [1, \"col-class\", \"d-none\", \"d-md-table-cell\"], [1, \"col-subject\", \"d-none\", \"d-lg-table-cell\"], [1, \"col-status\"], [1, \"col-time\", \"d-none\", \"d-sm-table-cell\"], [\"class\", \"table-row\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"loading-state\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"d-flex\", \"p-3\", \"align-items-center\", \"justify-content-between\"], [1, \"pagination-info\"], [1, \"text-muted\"], [1, \"pagination-controls\", \"d-flex\", \"align-items-center\", \"gap-2\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Previous Page\", 3, \"disabled\", \"click\"], [1, \"page-info\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Next Page\", 3, \"disabled\", \"click\"], [3, \"value\"], [1, \"stats-section\"], [1, \"stats-grid\"], [1, \"stat-card\", \"present\"], [1, \"stat-content\"], [1, \"stat-icon\"], [1, \"stat-info\"], [1, \"stat-card\", \"absent\"], [1, \"stat-card\", \"total\"], [1, \"stat-card\", \"percentage\"], [1, \"table-row\"], [1, \"date-info\"], [1, \"date-main\"], [1, \"date-year\", \"d-block\"], [1, \"student-info\"], [1, \"student-name\"], [1, \"student-roll\"], [1, \"mobile-info\", \"d-md-none\"], [1, \"class-info\"], [1, \"class-name\"], [1, \"class-section\"], [1, \"subject-info\"], [1, \"subject-name\"], [1, \"teacher-name\", \"d-block\"], [1, \"status-badge\", 3, \"ngClass\"], [1, \"status-icon\"], [1, \"d-none\", \"d-sm-inline\"], [1, \"time-info\"], [1, \"loading-state\"], [\"diameter\", \"40\"], [1, \"empty-state\"], [1, \"empty-icon\"]],\n    template: function AttendanceComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3)(4, \"mat-icon\", 4);\n        i0.ɵɵtext(5, \"event_note\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(6, \" Student Attendance Overview \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"p\", 5);\n        i0.ɵɵtext(8, \"Simple program and class-based attendance tracking\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(9, \"div\", 6)(10, \"button\", 7);\n        i0.ɵɵlistener(\"click\", function AttendanceComponent_Template_button_click_10_listener() {\n          return ctx.exportAttendance();\n        });\n        i0.ɵɵelementStart(11, \"mat-icon\");\n        i0.ɵɵtext(12, \"download\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(13, \" Export \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(14, \"mat-card\", 8)(15, \"mat-card-header\")(16, \"mat-card-title\")(17, \"mat-icon\");\n        i0.ɵɵtext(18, \"filter_list\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(19, \" Filters \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(20, \"mat-card-content\")(21, \"div\", 9)(22, \"mat-form-field\", 10)(23, \"mat-label\");\n        i0.ɵɵtext(24, \"Program\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"mat-select\", 11);\n        i0.ɵɵlistener(\"ngModelChange\", function AttendanceComponent_Template_mat_select_ngModelChange_25_listener($event) {\n          return ctx.selectedProgram = $event;\n        })(\"selectionChange\", function AttendanceComponent_Template_mat_select_selectionChange_25_listener() {\n          return ctx.onProgramChange();\n        });\n        i0.ɵɵelementStart(26, \"mat-option\", 12);\n        i0.ɵɵtext(27, \"All Programs\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(28, AttendanceComponent_mat_option_28_Template, 2, 2, \"mat-option\", 13);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(29, \"mat-form-field\", 10)(30, \"mat-label\");\n        i0.ɵɵtext(31, \"Class\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(32, \"mat-select\", 11);\n        i0.ɵɵlistener(\"ngModelChange\", function AttendanceComponent_Template_mat_select_ngModelChange_32_listener($event) {\n          return ctx.selectedClass = $event;\n        })(\"selectionChange\", function AttendanceComponent_Template_mat_select_selectionChange_32_listener() {\n          return ctx.applyFilters();\n        });\n        i0.ɵɵelementStart(33, \"mat-option\", 12);\n        i0.ɵɵtext(34, \"All Classes\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(35, AttendanceComponent_mat_option_35_Template, 2, 3, \"mat-option\", 13);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(36, \"mat-form-field\", 10)(37, \"mat-label\");\n        i0.ɵɵtext(38, \"From Date\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(39, \"input\", 14);\n        i0.ɵɵlistener(\"ngModelChange\", function AttendanceComponent_Template_input_ngModelChange_39_listener($event) {\n          return ctx.startDate = $event;\n        })(\"change\", function AttendanceComponent_Template_input_change_39_listener() {\n          return ctx.applyFilters();\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(40, \"mat-form-field\", 10)(41, \"mat-label\");\n        i0.ɵɵtext(42, \"To Date\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(43, \"input\", 14);\n        i0.ɵɵlistener(\"ngModelChange\", function AttendanceComponent_Template_input_ngModelChange_43_listener($event) {\n          return ctx.endDate = $event;\n        })(\"change\", function AttendanceComponent_Template_input_change_43_listener() {\n          return ctx.applyFilters();\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(44, \"div\", 15)(45, \"button\", 16);\n        i0.ɵɵlistener(\"click\", function AttendanceComponent_Template_button_click_45_listener() {\n          return ctx.applyFilters();\n        });\n        i0.ɵɵelementStart(46, \"mat-icon\");\n        i0.ɵɵtext(47, \"search\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(48, \" Apply \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(49, \"button\", 17);\n        i0.ɵɵlistener(\"click\", function AttendanceComponent_Template_button_click_49_listener() {\n          return ctx.resetFilters();\n        });\n        i0.ɵɵelementStart(50, \"mat-icon\");\n        i0.ɵɵtext(51, \"refresh\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(52, \" Reset \");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵtemplate(53, AttendanceComponent_div_53_Template, 46, 4, \"div\", 18);\n        i0.ɵɵelementStart(54, \"mat-card\", 19)(55, \"mat-card-header\")(56, \"mat-card-title\")(57, \"mat-icon\");\n        i0.ɵɵtext(58, \"list_alt\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(59, \" Attendance Records \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(60, \"mat-card-subtitle\");\n        i0.ɵɵtext(61);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(62, \"mat-card-content\")(63, \"div\", 20)(64, \"div\", 21)(65, \"table\", 22)(66, \"thead\")(67, \"tr\", 23)(68, \"th\", 24);\n        i0.ɵɵtext(69, \"Date\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(70, \"th\", 25);\n        i0.ɵɵtext(71, \"Student\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(72, \"th\", 26);\n        i0.ɵɵtext(73, \"Class\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(74, \"th\", 27);\n        i0.ɵɵtext(75, \"Subject\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(76, \"th\", 28);\n        i0.ɵɵtext(77, \"Status\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(78, \"th\", 29);\n        i0.ɵɵtext(79, \"Time\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(80, \"tbody\");\n        i0.ɵɵtemplate(81, AttendanceComponent_tr_81_Template, 41, 29, \"tr\", 30);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(82, AttendanceComponent_div_82_Template, 4, 0, \"div\", 31);\n        i0.ɵɵtemplate(83, AttendanceComponent_div_83_Template, 9, 0, \"div\", 32);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(84, \"div\", 33)(85, \"div\", 34)(86, \"span\", 35);\n        i0.ɵɵtext(87);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(88, \"div\", 36)(89, \"button\", 37);\n        i0.ɵɵlistener(\"click\", function AttendanceComponent_Template_button_click_89_listener() {\n          return ctx.previousPage();\n        });\n        i0.ɵɵelementStart(90, \"mat-icon\");\n        i0.ɵɵtext(91, \"chevron_left\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(92, \"span\", 38);\n        i0.ɵɵtext(93);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(94, \"button\", 39);\n        i0.ɵɵlistener(\"click\", function AttendanceComponent_Template_button_click_94_listener() {\n          return ctx.nextPage();\n        });\n        i0.ɵɵelementStart(95, \"mat-icon\");\n        i0.ɵɵtext(96, \"chevron_right\");\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(25);\n        i0.ɵɵproperty(\"ngModel\", ctx.selectedProgram);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.programs);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngModel\", ctx.selectedClass);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.filteredClasses);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngModel\", ctx.startDate);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngModel\", ctx.endDate);\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"ngIf\", ctx.statistics);\n        i0.ɵɵadvance(8);\n        i0.ɵɵtextInterpolate1(\"\", ctx.totalRecords, \" records found\");\n        i0.ɵɵadvance(20);\n        i0.ɵɵproperty(\"ngForOf\", ctx.attendanceRecords);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.attendanceRecords.length === 0);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate3(\" Showing \", (ctx.currentPage - 1) * ctx.itemsPerPage + 1, \" to \", ctx.Math.min(ctx.currentPage * ctx.itemsPerPage, ctx.totalRecords), \" of \", ctx.totalRecords, \" attendance records \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"disabled\", ctx.currentPage === 1);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate2(\"Page \", ctx.currentPage, \" of \", ctx.totalPages, \"\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"disabled\", ctx.currentPage === ctx.totalPages || ctx.totalPages === 0);\n      }\n    },\n    dependencies: [i8.NgClass, i8.NgForOf, i8.NgIf, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, i10.MatOption, i11.MatButton, i11.MatIconButton, i12.MatCard, i12.MatCardContent, i12.MatCardHeader, i12.MatCardSubtitle, i12.MatCardTitle, i13.MatIcon, i14.MatInput, i15.MatFormField, i15.MatLabel, i16.MatProgressSpinner, i17.MatSelect, i18.MatTooltip, i8.TitleCasePipe, i8.DatePipe],\n    styles: [\".attendance-container[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  min-height: 100vh;\\n  padding: 20px;\\n}\\n\\n\\n\\n.page-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 25px;\\n  padding: 15px 0;\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.page-title[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-size: 1.8rem;\\n  font-weight: 600;\\n  margin: 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n\\n.title-icon[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  color: #3498db;\\n}\\n\\n.page-subtitle[_ngcontent-%COMP%] {\\n  color: #7f8c8d;\\n  margin: 5px 0 0 0;\\n  font-size: 0.9rem;\\n}\\n\\n.header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n}\\n\\n\\n\\n.filters-card[_ngcontent-%COMP%] {\\n  margin-bottom: 25px;\\n  border-radius: 12px;\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n  border: 1px solid #e9ecef;\\n}\\n\\n.filters-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  border-radius: 12px 12px 0 0;\\n}\\n\\n.filters-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  margin: 0;\\n}\\n\\n.simple-filters[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 20px;\\n  align-items: end;\\n  padding: 20px;\\n}\\n\\n.filter-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n  align-items: center;\\n}\\n\\n\\n\\n.quick-filters[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 5px;\\n  margin-bottom: 15px;\\n  flex-wrap: wrap;\\n}\\n\\n.quick-btn[_ngcontent-%COMP%] {\\n  min-width: auto;\\n  padding: 6px 12px;\\n  font-size: 0.875rem;\\n  border-radius: 20px;\\n  transition: all 0.3s ease;\\n}\\n\\n.quick-btn.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);\\n  color: white;\\n}\\n\\n.quick-btn[_ngcontent-%COMP%]:not(.active) {\\n  background: #f8f9fa;\\n  color: #6c757d;\\n}\\n\\n.quick-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n}\\n\\n\\n\\n.compact-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.compact-field[_ngcontent-%COMP%]   .mat-form-field-wrapper[_ngcontent-%COMP%] {\\n  padding-bottom: 0;\\n}\\n\\n.compact-field[_ngcontent-%COMP%]   .mat-form-field-infix[_ngcontent-%COMP%] {\\n  padding: 8px 0;\\n  border-top: none;\\n}\\n\\n.compact-field[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n\\n.filters-grid[_ngcontent-%COMP%] {\\n  margin-top: 10px;\\n}\\n\\n\\n\\n.table-card[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.table-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  border-radius: 8px 8px 0 0;\\n  padding: 15px;\\n}\\n\\n.table-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  color: #2c3e50;\\n  font-weight: 600;\\n  font-size: 1.1rem;\\n}\\n\\n.table-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  padding: 0 !important;\\n}\\n\\n\\n\\n.compact-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: collapse;\\n  font-size: 0.875rem;\\n}\\n\\n.table-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);\\n  color: white;\\n}\\n\\n.table-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  padding: 12px 8px;\\n  text-align: left;\\n  font-weight: 600;\\n  font-size: 0.8rem;\\n  border: none;\\n}\\n\\n.table-row[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #ecf0f1;\\n  transition: background-color 0.2s ease;\\n}\\n\\n.table-row[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n\\n.table-row[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 10px 8px;\\n  vertical-align: middle;\\n}\\n\\n\\n\\n.col-date[_ngcontent-%COMP%] {\\n  width: 80px;\\n}\\n\\n.col-student[_ngcontent-%COMP%] {\\n  min-width: 150px;\\n}\\n\\n.col-class[_ngcontent-%COMP%] {\\n  width: 100px;\\n}\\n\\n.col-subject[_ngcontent-%COMP%] {\\n  width: 140px;\\n}\\n\\n.col-status[_ngcontent-%COMP%] {\\n  width: 80px;\\n  text-align: center;\\n}\\n\\n.col-time[_ngcontent-%COMP%] {\\n  width: 70px;\\n  text-align: center;\\n}\\n\\n\\n\\n.date-info[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.date-main[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2c3e50;\\n  font-size: 0.9rem;\\n}\\n\\n.date-year[_ngcontent-%COMP%] {\\n  color: #7f8c8d;\\n  font-size: 0.75rem;\\n}\\n\\n\\n\\n.student-info[_ngcontent-%COMP%] {\\n  line-height: 1.3;\\n}\\n\\n.student-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2c3e50;\\n  font-size: 0.9rem;\\n}\\n\\n.student-roll[_ngcontent-%COMP%] {\\n  color: #7f8c8d;\\n  font-size: 0.75rem;\\n}\\n\\n.mobile-info[_ngcontent-%COMP%] {\\n  margin-top: 2px;\\n}\\n\\n\\n\\n.class-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 2px;\\n}\\n\\n.class-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #8e44ad;\\n}\\n\\n.class-section[_ngcontent-%COMP%] {\\n  background: #f39c12;\\n  color: white;\\n  padding: 1px 6px;\\n  border-radius: 8px;\\n  font-size: 0.7rem;\\n  align-self: flex-start;\\n}\\n\\n\\n\\n.subject-info[_ngcontent-%COMP%] {\\n  line-height: 1.3;\\n}\\n\\n.subject-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #27ae60;\\n  font-size: 0.85rem;\\n}\\n\\n.teacher-name[_ngcontent-%COMP%] {\\n  color: #7f8c8d;\\n  font-size: 0.75rem;\\n}\\n\\n\\n\\n.status-badge[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 4px;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n}\\n\\n.status-present[_ngcontent-%COMP%] {\\n  background: #d4edda;\\n  color: #155724;\\n}\\n\\n.status-absent[_ngcontent-%COMP%] {\\n  background: #f8d7da;\\n  color: #721c24;\\n}\\n\\n.status-late[_ngcontent-%COMP%] {\\n  background: #fff3cd;\\n  color: #856404;\\n}\\n\\n.status-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem !important;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n\\n\\n.time-info[_ngcontent-%COMP%] {\\n  font-family: 'Courier New', monospace;\\n  font-weight: 600;\\n  color: #34495e;\\n}\\n\\n\\n\\n.loading-state[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n  text-align: center;\\n}\\n\\n.loading-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n  color: #7f8c8d;\\n}\\n\\n.empty-state[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  margin-bottom: 10px;\\n}\\n\\n.empty-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  color: #bdc3c7;\\n  margin-bottom: 15px;\\n}\\n\\n\\n\\n@media (max-width: 1200px) {\\n  .attendance-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .page-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 15px;\\n  }\\n\\n  .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  .header-actions[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: space-between;\\n  }\\n\\n  .quick-filters[_ngcontent-%COMP%] {\\n    gap: 3px;\\n  }\\n\\n  .quick-btn[_ngcontent-%COMP%] {\\n    padding: 4px 8px;\\n    font-size: 0.8rem;\\n  }\\n\\n  .filters-grid[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%] {\\n    margin: 0;\\n  }\\n\\n  .filters-grid[_ngcontent-%COMP%]   .col-6[_ngcontent-%COMP%], .filters-grid[_ngcontent-%COMP%]   .col-12[_ngcontent-%COMP%] {\\n    padding: 2px;\\n    margin-bottom: 8px;\\n  }\\n\\n  .compact-table[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n\\n  .table-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .table-row[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    padding: 8px 4px;\\n  }\\n\\n  .col-date[_ngcontent-%COMP%] {\\n    width: 60px;\\n  }\\n\\n  .col-student[_ngcontent-%COMP%] {\\n    min-width: 120px;\\n  }\\n\\n  .date-main[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n\\n  .date-year[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n  }\\n\\n  .student-name[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n\\n  .student-roll[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n  }\\n}\\n\\n@media (max-width: 576px) {\\n  .attendance-container[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n\\n  .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.3rem;\\n  }\\n\\n  .title-icon[_ngcontent-%COMP%] {\\n    font-size: 1.3rem;\\n  }\\n\\n  .quick-btn[_ngcontent-%COMP%] {\\n    padding: 3px 6px;\\n    font-size: 0.75rem;\\n  }\\n\\n  .compact-table[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n  }\\n\\n  .table-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n    padding: 6px 3px;\\n    font-size: 0.7rem;\\n  }\\n\\n  .table-row[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    padding: 6px 3px;\\n  }\\n\\n  .col-date[_ngcontent-%COMP%] {\\n    width: 50px;\\n  }\\n\\n  .date-main[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n  }\\n\\n  .date-year[_ngcontent-%COMP%] {\\n    font-size: 0.65rem;\\n  }\\n\\n  .student-name[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n\\n  .student-roll[_ngcontent-%COMP%] {\\n    font-size: 0.65rem;\\n  }\\n\\n  .status-badge[_ngcontent-%COMP%] {\\n    padding: 2px 4px;\\n    font-size: 0.65rem;\\n  }\\n\\n  .status-icon[_ngcontent-%COMP%] {\\n    font-size: 0.8rem !important;\\n    width: 12px;\\n    height: 12px;\\n  }\\n\\n  .time-info[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n  }\\n}\\n\\n\\n\\n.stats-section[_ngcontent-%COMP%] {\\n  margin-bottom: 25px;\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 20px;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n  border: 1px solid #e9ecef;\\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\\n}\\n\\n.stat-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);\\n}\\n\\n.stat-card.present[_ngcontent-%COMP%] {\\n  border-left: 4px solid #27ae60;\\n}\\n\\n.stat-card.absent[_ngcontent-%COMP%] {\\n  border-left: 4px solid #e74c3c;\\n}\\n\\n.stat-card.total[_ngcontent-%COMP%] {\\n  border-left: 4px solid #3498db;\\n}\\n\\n.stat-card.percentage[_ngcontent-%COMP%] {\\n  border-left: 4px solid #f39c12;\\n}\\n\\n.stat-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n  padding: 20px;\\n}\\n\\n.stat-icon[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n}\\n\\n.stat-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n}\\n\\n.stat-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.8rem;\\n  font-weight: 700;\\n  color: #2c3e50;\\n}\\n\\n.stat-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #7f8c8d;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 10px;\\n  padding: 20px;\\n  text-align: center;\\n  color: white;\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n  transition: transform 0.3s ease;\\n}\\n\\n.stat-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n}\\n\\n.stat-card.bg-success[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\\n}\\n\\n.stat-card.bg-danger[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);\\n}\\n\\n.stat-card.bg-warning[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);\\n}\\n\\n.stat-card.bg-info[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);\\n}\\n\\n.stat-number[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: bold;\\n  margin-bottom: 5px;\\n}\\n\\n.stat-label[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  opacity: 0.9;\\n}\\n\\n\\n\\n.table-container[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n\\n.table-dark[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #343a40 0%, #495057 100%);\\n  border: none;\\n  padding: 15px;\\n  font-weight: 600;\\n}\\n\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  transition: background-color 0.3s ease;\\n}\\n\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 12px 15px;\\n  vertical-align: middle;\\n}\\n\\n.badge[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.375rem 0.75rem;\\n  border-radius: 0.375rem;\\n}\\n\\n.table-loading[_ngcontent-%COMP%] {\\n  opacity: 0.6;\\n}\\n\\n\\n\\n.pagination-info[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #6c757d;\\n}\\n\\n.pagination-controls[_ngcontent-%COMP%] {\\n  gap: 10px;\\n}\\n\\n.page-info[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #495057;\\n  padding: 0 15px;\\n}\\n\\n\\n\\n.btn[_ngcontent-%COMP%] {\\n  border-radius: 5px;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 5px;\\n}\\n\\n.btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\\n}\\n\\n.btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n\\n\\n\\n.spinner-border[_ngcontent-%COMP%] {\\n  width: 1.5rem;\\n  height: 1.5rem;\\n}\\n\\n.large-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  opacity: 0.5;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "program_r6", "_id", "ɵɵadvance", "ɵɵtextInterpolate1", "name", "cls_r7", "ɵɵtextInterpolate2", "className", "section", "ɵɵtextInterpolate", "ctx_r2", "statistics", "present", "absent", "total", "getAttendancePercentage", "ɵɵpipeBind2", "record_r8", "date", "student", "rollNo", "class", "subject", "subjectName", "teacher", "ɵɵpureFunction3", "_c0", "status", "ɵɵpipeBind1", "createdAt", "ɵɵelement", "ɵɵlistener", "AttendanceComponent_div_83_Template_button_click_7_listener", "ɵɵrestoreView", "_r11", "ctx_r10", "ɵɵnextContext", "ɵɵresetView", "resetFilters", "AttendanceComponent", "constructor", "router", "attendanceService", "classesService", "subjectService", "userService", "programService", "departmentService", "attendanceRecords", "programs", "departments", "classes", "subjects", "teachers", "students", "filteredDepartments", "filteredClasses", "filters", "page", "limit", "currentPage", "totalPages", "totalRecords", "itemsPerPage", "loading", "searchQuery", "selectedDateRange", "Math", "startDate", "endDate", "date<PERSON><PERSON><PERSON>", "selectedProgram", "selectedDepartment", "selectedClass", "selectedSubject", "<PERSON><PERSON><PERSON><PERSON>", "selectedStatus", "selectedStudent", "ngOnInit", "loadInitialData", "setupFormSubscriptions", "setDefaultDateRange", "loadAttendanceRecords", "loadStatistics", "getAllPrograms", "subscribe", "next", "response", "success", "error", "console", "getAllDepartments", "getAllClasses", "getAllSubjects", "getUsersByRole", "users", "today", "Date", "toISOString", "split", "monthStart", "getFullYear", "getMonth", "yearStart", "apiFilters", "program", "department", "classId", "subjectId", "teacherId", "studentId", "search", "getAllAttendanceRecords", "attendance", "pagination", "statsFilters", "getAttendanceStatistics", "onProgramChange", "filter", "dept", "applyFilters", "onDepartmentChange", "cls", "onDateRangeChange", "onPageChange", "nextPage", "previousPage", "exportAttendance", "alert", "setQuickDate", "range", "formatDateForInput", "weekStart", "setDate", "getDate", "getDay", "weekEnd", "monthEnd", "filteredRecords", "searchLower", "toLowerCase", "record", "includes", "paginatedStudents", "map", "rollNumber", "<PERSON><PERSON><PERSON>", "father_name", "registrationNo", "regNo", "mobileNo", "contact", "formatDate", "toLocaleDateString", "getStatusClass", "presentCount", "late", "round", "_", "ɵɵdirectiveInject", "i1", "Router", "i2", "AttendanceService", "i3", "ClassesService", "i4", "SubjectService", "i5", "UserService", "i6", "ProgramService", "i7", "DepartmentService", "_2", "selectors", "decls", "vars", "consts", "template", "AttendanceComponent_Template", "rf", "ctx", "AttendanceComponent_Template_button_click_10_listener", "AttendanceComponent_Template_mat_select_ngModelChange_25_listener", "$event", "AttendanceComponent_Template_mat_select_selectionChange_25_listener", "ɵɵtemplate", "AttendanceComponent_mat_option_28_Template", "AttendanceComponent_Template_mat_select_ngModelChange_32_listener", "AttendanceComponent_Template_mat_select_selectionChange_32_listener", "AttendanceComponent_mat_option_35_Template", "AttendanceComponent_Template_input_ngModelChange_39_listener", "AttendanceComponent_Template_input_change_39_listener", "AttendanceComponent_Template_input_ngModelChange_43_listener", "AttendanceComponent_Template_input_change_43_listener", "AttendanceComponent_Template_button_click_45_listener", "AttendanceComponent_Template_button_click_49_listener", "AttendanceComponent_div_53_Template", "AttendanceComponent_tr_81_Template", "AttendanceComponent_div_82_Template", "AttendanceComponent_div_83_Template", "AttendanceComponent_Template_button_click_89_listener", "AttendanceComponent_Template_button_click_94_listener", "length", "ɵɵtextInterpolate3", "min"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\students\\attendance\\attendance.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\students\\attendance\\attendance.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { AttendanceService } from 'src/app/services/attendance.service';\r\nimport { ClassesService } from 'src/app/services/classes.service';\r\nimport { SubjectService } from 'src/app/services/subject.service';\r\nimport { UserService } from 'src/app/services/user.service';\r\nimport { ProgramService } from 'src/app/services/program.service';\r\nimport { DepartmentService } from 'src/app/services/department.service';\r\nimport { Attendance } from 'src/app/models/attendance';\r\nimport { Program, Department, Class, User } from 'src/app/models/user';\r\n\r\ninterface AttendanceFilter {\r\n  studentId?: string;\r\n  classId?: string;\r\n  teacherId?: string;\r\n  subjectId?: string;\r\n  startDate?: string;\r\n  endDate?: string;\r\n  status?: string;\r\n  page?: number;\r\n  limit?: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-attendance',\r\n  templateUrl: './attendance.component.html',\r\n  styleUrls: ['./attendance.component.css']\r\n})\r\nexport class AttendanceComponent implements OnInit {\r\n  // Data arrays\r\n  attendanceRecords: Attendance[] = [];\r\n  programs: Program[] = [];\r\n  departments: Department[] = [];\r\n  classes: Class[] = [];\r\n  subjects: any[] = [];\r\n  teachers: User[] = [];\r\n  students: User[] = [];\r\n  filteredDepartments: Department[] = [];\r\n  filteredClasses: Class[] = [];\r\n\r\n  // Filter and pagination\r\n  filters: AttendanceFilter = {\r\n    page: 1,\r\n    limit: 20\r\n  };\r\n\r\n  // Pagination properties\r\n  currentPage = 1;\r\n  totalPages = 1;\r\n  totalRecords = 0;\r\n  itemsPerPage = 20;\r\n\r\n  // UI state\r\n  loading = false;\r\n  searchQuery = '';\r\n  selectedDateRange = 'today';\r\n  Math = Math; // Make Math available in template\r\n\r\n  // Statistics\r\n  statistics: any = null;\r\n\r\n  // Date filters\r\n  startDate = '';\r\n  endDate = '';\r\n  dateRange = 'daily'; // daily, monthly, yearly\r\n\r\n  // Selected filters\r\n  selectedProgram = '';\r\n  selectedDepartment = '';\r\n  selectedClass = '';\r\n  selectedSubject = '';\r\n  selectedTeacher = '';\r\n  selectedStatus = '';\r\n  selectedStudent = '';\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private attendanceService: AttendanceService,\r\n    private classesService: ClassesService,\r\n    private subjectService: SubjectService,\r\n    private userService: UserService,\r\n    private programService: ProgramService,\r\n    private departmentService: DepartmentService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loadInitialData();\r\n    this.setupFormSubscriptions();\r\n    this.setDefaultDateRange();\r\n    this.loadAttendanceRecords();\r\n    this.loadStatistics();\r\n  }\r\n\r\n  loadInitialData(): void {\r\n    // Load programs\r\n    this.programService.getAllPrograms(true).subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.programs = response.programs;\r\n        }\r\n      },\r\n      error: (error) => console.error('Error loading programs:', error)\r\n    });\r\n\r\n    // Load departments\r\n    this.departmentService.getAllDepartments().subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.departments = response.departments;\r\n          this.filteredDepartments = this.departments;\r\n        }\r\n      },\r\n      error: (error) => console.error('Error loading departments:', error)\r\n    });\r\n\r\n    // Load classes\r\n    this.classesService.getAllClasses().subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.classes = response.classes;\r\n          this.filteredClasses = this.classes;\r\n        }\r\n      },\r\n      error: (error) => console.error('Error loading classes:', error)\r\n    });\r\n\r\n    // Load subjects\r\n    this.subjectService.getAllSubjects().subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.subjects = response.subjects;\r\n        }\r\n      },\r\n      error: (error) => console.error('Error loading subjects:', error)\r\n    });\r\n\r\n    // Load teachers\r\n    this.userService.getUsersByRole('Teacher').subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.teachers = response.users;\r\n        }\r\n      },\r\n      error: (error) => console.error('Error loading teachers:', error)\r\n    });\r\n\r\n    // Load students\r\n    this.userService.getUsersByRole('Student').subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.students = response.users;\r\n        }\r\n      },\r\n      error: (error) => console.error('Error loading students:', error)\r\n    });\r\n  }\r\n\r\n  setupFormSubscriptions(): void {\r\n    // This would be implemented with reactive forms for better filtering\r\n    // For now, we'll handle filtering in the filter change methods\r\n  }\r\n\r\n  setDefaultDateRange(): void {\r\n    const today = new Date();\r\n    this.endDate = today.toISOString().split('T')[0];\r\n\r\n    // Set start date based on selected range\r\n    switch (this.dateRange) {\r\n      case 'daily':\r\n        this.startDate = this.endDate;\r\n        break;\r\n      case 'monthly':\r\n        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);\r\n        this.startDate = monthStart.toISOString().split('T')[0];\r\n        break;\r\n      case 'yearly':\r\n        const yearStart = new Date(today.getFullYear(), 0, 1);\r\n        this.startDate = yearStart.toISOString().split('T')[0];\r\n        break;\r\n    }\r\n  }\r\n\r\n\r\n  loadAttendanceRecords(): void {\r\n    this.loading = true;\r\n\r\n    // Build filters\r\n    const apiFilters: any = { ...this.filters };\r\n\r\n    if (this.selectedProgram) apiFilters.program = this.selectedProgram;\r\n    if (this.selectedDepartment) apiFilters.department = this.selectedDepartment;\r\n    if (this.selectedClass) apiFilters.classId = this.selectedClass;\r\n    if (this.selectedSubject) apiFilters.subjectId = this.selectedSubject;\r\n    if (this.selectedTeacher) apiFilters.teacherId = this.selectedTeacher;\r\n    if (this.selectedStudent) apiFilters.studentId = this.selectedStudent;\r\n    if (this.selectedStatus) apiFilters.status = this.selectedStatus;\r\n    if (this.startDate) apiFilters.startDate = this.startDate;\r\n    if (this.endDate) apiFilters.endDate = this.endDate;\r\n    if (this.searchQuery) apiFilters.search = this.searchQuery;\r\n\r\n    this.attendanceService.getAllAttendanceRecords(apiFilters).subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.attendanceRecords = response.attendance;\r\n          // Update pagination info from API response\r\n          if (response.pagination) {\r\n            this.currentPage = response.pagination.currentPage;\r\n            this.totalPages = response.pagination.totalPages;\r\n            this.totalRecords = response.pagination.totalRecords;\r\n          }\r\n        }\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading attendance records:', error);\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  loadStatistics(): void {\r\n    const statsFilters: any = {};\r\n\r\n    if (this.selectedClass) statsFilters.classId = this.selectedClass;\r\n    if (this.selectedSubject) statsFilters.subjectId = this.selectedSubject;\r\n    if (this.startDate) statsFilters.startDate = this.startDate;\r\n    if (this.endDate) statsFilters.endDate = this.endDate;\r\n\r\n    this.attendanceService.getAttendanceStatistics(statsFilters).subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.statistics = response.statistics;\r\n        }\r\n      },\r\n      error: (error) => console.error('Error loading statistics:', error)\r\n    });\r\n  }\r\n\r\n  // Filter methods for cascading dropdowns\r\n  onProgramChange(): void {\r\n    if (this.selectedProgram) {\r\n      this.filteredDepartments = this.departments.filter(dept => dept.program._id === this.selectedProgram);\r\n      this.selectedDepartment = '';\r\n      this.selectedClass = '';\r\n      this.filteredClasses = [];\r\n    } else {\r\n      this.filteredDepartments = this.departments;\r\n      this.selectedDepartment = '';\r\n      this.selectedClass = '';\r\n      this.filteredClasses = this.classes;\r\n    }\r\n    this.applyFilters();\r\n  }\r\n\r\n  onDepartmentChange(): void {\r\n    if (this.selectedDepartment) {\r\n      this.filteredClasses = this.classes.filter(cls => cls.department._id === this.selectedDepartment);\r\n      this.selectedClass = '';\r\n    } else {\r\n      this.filteredClasses = this.selectedProgram ?\r\n        this.classes.filter(cls => cls.program._id === this.selectedProgram) :\r\n        this.classes;\r\n      this.selectedClass = '';\r\n    }\r\n    this.applyFilters();\r\n  }\r\n\r\n  onDateRangeChange(): void {\r\n    this.setDefaultDateRange();\r\n    this.applyFilters();\r\n  }\r\n\r\n  applyFilters(): void {\r\n    this.filters.page = 1; // Reset to first page\r\n    this.currentPage = 1;\r\n    this.loadAttendanceRecords();\r\n    this.loadStatistics();\r\n  }\r\n\r\n  resetFilters(): void {\r\n    this.selectedProgram = '';\r\n    this.selectedDepartment = '';\r\n    this.selectedClass = '';\r\n    this.selectedSubject = '';\r\n    this.selectedTeacher = '';\r\n    this.selectedStudent = '';\r\n    this.selectedStatus = '';\r\n    this.dateRange = 'daily';\r\n    this.setDefaultDateRange();\r\n    this.filteredDepartments = this.departments;\r\n    this.filteredClasses = this.classes;\r\n    this.filters = { page: 1, limit: 20 };\r\n    this.currentPage = 1;\r\n    this.loadAttendanceRecords();\r\n    this.loadStatistics();\r\n  }\r\n\r\n  onPageChange(page: number): void {\r\n    this.filters.page = page;\r\n    this.currentPage = page;\r\n    this.loadAttendanceRecords();\r\n  }\r\n\r\n  // Pagination navigation methods\r\n  nextPage(): void {\r\n    if (this.currentPage < this.totalPages) {\r\n      this.onPageChange(this.currentPage + 1);\r\n    }\r\n  }\r\n\r\n  previousPage(): void {\r\n    if (this.currentPage > 1) {\r\n      this.onPageChange(this.currentPage - 1);\r\n    }\r\n  }\r\n\r\n  exportAttendance(): void {\r\n    // TODO: Implement export functionality\r\n    alert('Export functionality will be implemented');\r\n  }\r\n\r\n  // Quick date filter methods\r\n  setQuickDate(range: string): void {\r\n    this.selectedDateRange = range;\r\n    const today = new Date();\r\n\r\n    switch (range) {\r\n      case 'today':\r\n        this.startDate = this.formatDateForInput(today);\r\n        this.endDate = this.formatDateForInput(today);\r\n        break;\r\n      case 'week':\r\n        const weekStart = new Date(today);\r\n        weekStart.setDate(today.getDate() - today.getDay());\r\n        const weekEnd = new Date(weekStart);\r\n        weekEnd.setDate(weekStart.getDate() + 6);\r\n        this.startDate = this.formatDateForInput(weekStart);\r\n        this.endDate = this.formatDateForInput(weekEnd);\r\n        break;\r\n      case 'month':\r\n        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);\r\n        const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);\r\n        this.startDate = this.formatDateForInput(monthStart);\r\n        this.endDate = this.formatDateForInput(monthEnd);\r\n        break;\r\n    }\r\n    this.applyFilters();\r\n  }\r\n\r\n  formatDateForInput(date: Date): string {\r\n    return date.toISOString().split('T')[0];\r\n  }\r\n\r\n  get filteredRecords() {\r\n    if (!this.searchQuery) return this.attendanceRecords;\r\n\r\n    const searchLower = this.searchQuery.toLowerCase();\r\n    return this.attendanceRecords.filter(record =>\r\n      record.student?.name?.toLowerCase().includes(searchLower) ||\r\n      record.student?.rollNo?.toLowerCase().includes(searchLower) ||\r\n      record.class?.className?.toLowerCase().includes(searchLower) ||\r\n      record.subject?.subjectName?.toLowerCase().includes(searchLower) ||\r\n      record.teacher?.name?.toLowerCase().includes(searchLower)\r\n    );\r\n  }\r\n\r\n  // Getter for paginated students (formatted from attendance records)\r\n  get paginatedStudents() {\r\n    return this.attendanceRecords.map(record => ({\r\n      name: record.student?.name || 'N/A',\r\n      rollNumber: record.student?.rollNo || 'N/A',\r\n      fatherName: record.student?.father_name || 'N/A',\r\n      department: record?.student?.department?.name || record.class?.department?.name || 'N/A',\r\n      class: record.class?.className || 'N/A',\r\n      section: record.class?.section || 'N/A',\r\n      registrationNo: record.student?.regNo || 'N/A',\r\n      mobileNo: record.student?.contact || 'N/A',\r\n      status: record.status\r\n    }));\r\n  }\r\n\r\n  formatDate(date: string | Date): string {\r\n    return new Date(date).toLocaleDateString();\r\n  }\r\n\r\n  getStatusClass(status: string): string {\r\n    switch (status) {\r\n      case 'present': return 'status-present';\r\n      case 'absent': return 'status-absent';\r\n      case 'late': return 'status-late';\r\n      default: return '';\r\n    }\r\n  }\r\n\r\n  getAttendancePercentage(): number {\r\n    if (!this.statistics || !this.statistics.total || this.statistics.total === 0) {\r\n      return 0;\r\n    }\r\n    const presentCount = (this.statistics.present || 0) + (this.statistics.late || 0);\r\n    return Math.round((presentCount / this.statistics.total) * 100);\r\n  }\r\n}\r\n", "\r\n<div class=\"attendance-container\">\r\n  <!-- Header Section -->\r\n  <div class=\"page-header\">\r\n    <div class=\"header-content\">\r\n      <h1 class=\"page-title\">\r\n        <mat-icon class=\"title-icon\">event_note</mat-icon>\r\n        Student Attendance Overview\r\n      </h1>\r\n      <p class=\"page-subtitle\">Simple program and class-based attendance tracking</p>\r\n    </div>\r\n    <div class=\"header-actions\">\r\n      <button mat-raised-button color=\"primary\" (click)=\"exportAttendance()\">\r\n        <mat-icon>download</mat-icon>\r\n        Export\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Simple Filters Card -->\r\n  <mat-card class=\"filters-card\">\r\n    <mat-card-header>\r\n      <mat-card-title>\r\n        <mat-icon>filter_list</mat-icon>\r\n        Filters\r\n      </mat-card-title>\r\n    </mat-card-header>\r\n    <mat-card-content>\r\n      <div class=\"simple-filters\">\r\n        <!-- Program Filter -->\r\n        <mat-form-field appearance=\"outline\">\r\n          <mat-label>Program</mat-label>\r\n          <mat-select [(ngModel)]=\"selectedProgram\" (selectionChange)=\"onProgramChange()\">\r\n            <mat-option value=\"\">All Programs</mat-option>\r\n            <mat-option *ngFor=\"let program of programs\" [value]=\"program._id\">\r\n              {{ program.name }}\r\n            </mat-option>\r\n          </mat-select>\r\n        </mat-form-field>\r\n\r\n        <!-- Class Filter -->\r\n        <mat-form-field appearance=\"outline\">\r\n          <mat-label>Class</mat-label>\r\n          <mat-select [(ngModel)]=\"selectedClass\" (selectionChange)=\"applyFilters()\">\r\n            <mat-option value=\"\">All Classes</mat-option>\r\n            <mat-option *ngFor=\"let cls of filteredClasses\" [value]=\"cls._id\">\r\n              {{ cls.className }} - {{ cls.section }}\r\n            </mat-option>\r\n          </mat-select>\r\n        </mat-form-field>\r\n\r\n        <!-- Date Range -->\r\n        <mat-form-field appearance=\"outline\">\r\n          <mat-label>From Date</mat-label>\r\n          <input matInput type=\"date\" [(ngModel)]=\"startDate\" (change)=\"applyFilters()\">\r\n        </mat-form-field>\r\n\r\n        <mat-form-field appearance=\"outline\">\r\n          <mat-label>To Date</mat-label>\r\n          <input matInput type=\"date\" [(ngModel)]=\"endDate\" (change)=\"applyFilters()\">\r\n        </mat-form-field>\r\n\r\n        <!-- Actions -->\r\n        <div class=\"filter-actions\">\r\n          <button mat-raised-button color=\"accent\" (click)=\"applyFilters()\">\r\n            <mat-icon>search</mat-icon>\r\n            Apply\r\n          </button>\r\n          <button mat-button (click)=\"resetFilters()\">\r\n            <mat-icon>refresh</mat-icon>\r\n            Reset\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </mat-card-content>\r\n  </mat-card>\r\n\r\n  <!-- Simple Statistics -->\r\n  <div class=\"stats-section\" *ngIf=\"statistics\">\r\n    <div class=\"stats-grid\">\r\n      <mat-card class=\"stat-card present\">\r\n        <mat-card-content>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon\">\r\n              <mat-icon>check_circle</mat-icon>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ statistics.present || 0 }}</h3>\r\n              <p>Present</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n\r\n      <mat-card class=\"stat-card absent\">\r\n        <mat-card-content>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon\">\r\n              <mat-icon>cancel</mat-icon>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ statistics.absent || 0 }}</h3>\r\n              <p>Absent</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n\r\n      <mat-card class=\"stat-card total\">\r\n        <mat-card-content>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon\">\r\n              <mat-icon>people</mat-icon>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ statistics.total || 0 }}</h3>\r\n              <p>Total Records</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n\r\n      <mat-card class=\"stat-card percentage\">\r\n        <mat-card-content>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon\">\r\n              <mat-icon>assessment</mat-icon>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ getAttendancePercentage() }}%</h3>\r\n              <p>Attendance Rate</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n    </div>\r\n  </div>\r\n  \r\n    <!-- Compact Attendance Table -->\r\n    <mat-card class=\"table-card\">\r\n      <mat-card-header>\r\n        <mat-card-title>\r\n          <mat-icon>list_alt</mat-icon>\r\n          Attendance Records\r\n        </mat-card-title>\r\n        <mat-card-subtitle>{{ totalRecords }} records found</mat-card-subtitle>\r\n      </mat-card-header>\r\n      <mat-card-content>\r\n        <div class=\"table-container\">\r\n          <div class=\"table-responsive\">\r\n            <table class=\"compact-table\">\r\n              <thead>\r\n                <tr class=\"table-header\">\r\n                  <th class=\"col-date\">Date</th>\r\n                  <th class=\"col-student\">Student</th>\r\n                  <th class=\"col-class d-none d-md-table-cell\">Class</th>\r\n                  <th class=\"col-subject d-none d-lg-table-cell\">Subject</th>\r\n                  <th class=\"col-status\">Status</th>\r\n                  <th class=\"col-time d-none d-sm-table-cell\">Time</th>\r\n                </tr>\r\n              </thead>\r\n              <tbody>\r\n                <tr *ngFor=\"let record of attendanceRecords; let i = index;\" class=\"table-row\">\r\n                  <td class=\"col-date\">\r\n                    <div class=\"date-info\">\r\n                      <span class=\"date-main\">{{ record.date | date:'dd/MM' }}</span>\r\n                      <small class=\"date-year d-block\">{{ record.date | date:'yyyy' }}</small>\r\n                    </div>\r\n                  </td>\r\n                  <td class=\"col-student\">\r\n                    <div class=\"student-info\">\r\n                      <div class=\"student-name\">{{ record?.student?.name || 'N/A' }}</div>\r\n                      <small class=\"student-roll\">{{ record?.student?.rollNo || 'N/A' }}</small>\r\n                      <div class=\"mobile-info d-md-none\">\r\n                        <small class=\"text-muted\">{{ record?.class?.className || 'N/A' }}-{{ record?.class?.section || '' }}</small>\r\n                      </div>\r\n                    </div>\r\n                  </td>\r\n                  <td class=\"col-class d-none d-md-table-cell\">\r\n                    <div class=\"class-info\">\r\n                      <span class=\"class-name\">{{ record?.class?.className || 'N/A' }}</span>\r\n                      <span class=\"class-section\">{{ record?.class?.section || '' }}</span>\r\n                    </div>\r\n                  </td>\r\n                  <td class=\"col-subject d-none d-lg-table-cell\">\r\n                    <div class=\"subject-info\">\r\n                      <span class=\"subject-name\">{{ record?.subject?.subjectName || 'N/A' }}</span>\r\n                      <small class=\"teacher-name d-block\">{{ record?.teacher?.name || 'N/A' }}</small>\r\n                    </div>\r\n                  </td>\r\n                  <td class=\"col-status\">\r\n                    <span class=\"status-badge\" [ngClass]=\"{\r\n                      'status-present': record.status === 'present',\r\n                      'status-absent': record.status === 'absent',\r\n                      'status-late': record.status === 'late'\r\n                    }\">\r\n                      <mat-icon class=\"status-icon\">\r\n                        {{ record.status === 'present' ? 'check_circle' :\r\n                           record.status === 'absent' ? 'cancel' : 'schedule' }}\r\n                      </mat-icon>\r\n                      <span class=\"d-none d-sm-inline\">{{ record.status | titlecase }}</span>\r\n                    </span>\r\n                  </td>\r\n                  <td class=\"col-time d-none d-sm-table-cell\">\r\n                    <span class=\"time-info\">{{ (record.createdAt | date:'HH:mm') || '--:--' }}</span>\r\n                  </td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n\r\n            <!-- Loading State -->\r\n            <div *ngIf=\"loading\" class=\"loading-state\">\r\n              <mat-spinner diameter=\"40\"></mat-spinner>\r\n              <p>Loading attendance records...</p>\r\n            </div>\r\n\r\n            <!-- Empty State -->\r\n            <div *ngIf=\"!loading && attendanceRecords.length === 0\" class=\"empty-state\">\r\n              <mat-icon class=\"empty-icon\">event_busy</mat-icon>\r\n              <h4>No Records Found</h4>\r\n              <p>No attendance records match your current filters.</p>\r\n              <button mat-button (click)=\"resetFilters()\">Reset Filters</button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n    \r\n      <!-- Pagination Controls -->\r\n      <div class=\"d-flex p-3 align-items-center justify-content-between\">\r\n        <div class=\"pagination-info\">\r\n          <span class=\"text-muted\">\r\n            Showing {{ ((currentPage - 1) * itemsPerPage) + 1 }} to\r\n            {{ Math.min(currentPage * itemsPerPage, totalRecords) }} of\r\n            {{ totalRecords }} attendance records\r\n          </span>\r\n        </div>\r\n        <div class=\"pagination-controls d-flex align-items-center gap-2\">\r\n          <button mat-icon-button [disabled]=\"currentPage === 1\" (click)=\"previousPage()\" matTooltip=\"Previous Page\">\r\n            <mat-icon>chevron_left</mat-icon>\r\n          </button>\r\n          <span class=\"page-info\">Page {{ currentPage }} of {{ totalPages }}</span>\r\n          <button mat-icon-button [disabled]=\"currentPage === totalPages || totalPages === 0\" (click)=\"nextPage()\" matTooltip=\"Next Page\">\r\n            <mat-icon>chevron_right</mat-icon>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;ICkCYA,EAAA,CAAAC,cAAA,qBAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFgCH,EAAA,CAAAI,UAAA,UAAAC,UAAA,CAAAC,GAAA,CAAqB;IAChEN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,UAAA,CAAAI,IAAA,MACF;;;;;IASAT,EAAA,CAAAC,cAAA,qBAAkE;IAChED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFmCH,EAAA,CAAAI,UAAA,UAAAM,MAAA,CAAAJ,GAAA,CAAiB;IAC/DN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAW,kBAAA,MAAAD,MAAA,CAAAE,SAAA,SAAAF,MAAA,CAAAG,OAAA,MACF;;;;;IA+BVb,EAAA,CAAAC,cAAA,cAA8C;IAMxBD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEnCH,EAAA,CAAAC,cAAA,cAAuB;IACjBD,EAAA,CAAAE,MAAA,IAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtCH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAMtBH,EAAA,CAAAC,cAAA,oBAAmC;IAIjBD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE7BH,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAE,MAAA,IAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrCH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAMrBH,EAAA,CAAAC,cAAA,oBAAkC;IAIhBD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE7BH,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAE,MAAA,IAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpCH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAM5BH,EAAA,CAAAC,cAAA,oBAAuC;IAIrBD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEjCH,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAE,MAAA,IAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzCH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IA3ClBH,EAAA,CAAAO,SAAA,IAA6B;IAA7BP,EAAA,CAAAc,iBAAA,CAAAC,MAAA,CAAAC,UAAA,CAAAC,OAAA,MAA6B;IAc7BjB,EAAA,CAAAO,SAAA,IAA4B;IAA5BP,EAAA,CAAAc,iBAAA,CAAAC,MAAA,CAAAC,UAAA,CAAAE,MAAA,MAA4B;IAc5BlB,EAAA,CAAAO,SAAA,IAA2B;IAA3BP,EAAA,CAAAc,iBAAA,CAAAC,MAAA,CAAAC,UAAA,CAAAG,KAAA,MAA2B;IAc3BnB,EAAA,CAAAO,SAAA,IAAgC;IAAhCP,EAAA,CAAAQ,kBAAA,KAAAO,MAAA,CAAAK,uBAAA,QAAgC;;;;;;;;;;;;IAiClCpB,EAAA,CAAAC,cAAA,aAA+E;IAGjDD,EAAA,CAAAE,MAAA,GAAgC;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/DH,EAAA,CAAAC,cAAA,gBAAiC;IAAAD,EAAA,CAAAE,MAAA,GAA+B;;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAG5EH,EAAA,CAAAC,cAAA,aAAwB;IAEMD,EAAA,CAAAE,MAAA,IAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACpEH,EAAA,CAAAC,cAAA,iBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1EH,EAAA,CAAAC,cAAA,eAAmC;IACPD,EAAA,CAAAE,MAAA,IAA0E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAIlHH,EAAA,CAAAC,cAAA,cAA6C;IAEhBD,EAAA,CAAAE,MAAA,IAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvEH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGzEH,EAAA,CAAAC,cAAA,cAA+C;IAEhBD,EAAA,CAAAE,MAAA,IAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7EH,EAAA,CAAAC,cAAA,iBAAoC;IAAAD,EAAA,CAAAE,MAAA,IAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAGpFH,EAAA,CAAAC,cAAA,cAAuB;IAOjBD,EAAA,CAAAE,MAAA,IAEF;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACXH,EAAA,CAAAC,cAAA,gBAAiC;IAAAD,EAAA,CAAAE,MAAA,IAA+B;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAG3EH,EAAA,CAAAC,cAAA,cAA4C;IAClBD,EAAA,CAAAE,MAAA,IAAkD;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAvCvDH,EAAA,CAAAO,SAAA,GAAgC;IAAhCP,EAAA,CAAAc,iBAAA,CAAAd,EAAA,CAAAqB,WAAA,QAAAC,SAAA,CAAAC,IAAA,WAAgC;IACvBvB,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAc,iBAAA,CAAAd,EAAA,CAAAqB,WAAA,QAAAC,SAAA,CAAAC,IAAA,UAA+B;IAKtCvB,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAc,iBAAA,EAAAQ,SAAA,kBAAAA,SAAA,CAAAE,OAAA,kBAAAF,SAAA,CAAAE,OAAA,CAAAf,IAAA,WAAoC;IAClCT,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAAc,iBAAA,EAAAQ,SAAA,kBAAAA,SAAA,CAAAE,OAAA,kBAAAF,SAAA,CAAAE,OAAA,CAAAC,MAAA,WAAsC;IAEtCzB,EAAA,CAAAO,SAAA,GAA0E;IAA1EP,EAAA,CAAAW,kBAAA,MAAAW,SAAA,kBAAAA,SAAA,CAAAI,KAAA,kBAAAJ,SAAA,CAAAI,KAAA,CAAAd,SAAA,kBAAAU,SAAA,kBAAAA,SAAA,CAAAI,KAAA,kBAAAJ,SAAA,CAAAI,KAAA,CAAAb,OAAA,YAA0E;IAM7Eb,EAAA,CAAAO,SAAA,GAAuC;IAAvCP,EAAA,CAAAc,iBAAA,EAAAQ,SAAA,kBAAAA,SAAA,CAAAI,KAAA,kBAAAJ,SAAA,CAAAI,KAAA,CAAAd,SAAA,WAAuC;IACpCZ,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAc,iBAAA,EAAAQ,SAAA,kBAAAA,SAAA,CAAAI,KAAA,kBAAAJ,SAAA,CAAAI,KAAA,CAAAb,OAAA,QAAkC;IAKnCb,EAAA,CAAAO,SAAA,GAA2C;IAA3CP,EAAA,CAAAc,iBAAA,EAAAQ,SAAA,kBAAAA,SAAA,CAAAK,OAAA,kBAAAL,SAAA,CAAAK,OAAA,CAAAC,WAAA,WAA2C;IAClC5B,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAc,iBAAA,EAAAQ,SAAA,kBAAAA,SAAA,CAAAO,OAAA,kBAAAP,SAAA,CAAAO,OAAA,CAAApB,IAAA,WAAoC;IAI/CT,EAAA,CAAAO,SAAA,GAIzB;IAJyBP,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAA8B,eAAA,KAAAC,GAAA,EAAAT,SAAA,CAAAU,MAAA,gBAAAV,SAAA,CAAAU,MAAA,eAAAV,SAAA,CAAAU,MAAA,aAIzB;IAEEhC,EAAA,CAAAO,SAAA,GAEF;IAFEP,EAAA,CAAAQ,kBAAA,MAAAc,SAAA,CAAAU,MAAA,kCAAAV,SAAA,CAAAU,MAAA,2CAEF;IACiChC,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAc,iBAAA,CAAAd,EAAA,CAAAiC,WAAA,SAAAX,SAAA,CAAAU,MAAA,EAA+B;IAI1ChC,EAAA,CAAAO,SAAA,GAAkD;IAAlDP,EAAA,CAAAc,iBAAA,CAAAd,EAAA,CAAAqB,WAAA,SAAAC,SAAA,CAAAY,SAAA,sBAAkD;;;;;IAOlFlC,EAAA,CAAAC,cAAA,cAA2C;IACzCD,EAAA,CAAAmC,SAAA,sBAAyC;IACzCnC,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,oCAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;IAItCH,EAAA,CAAAC,cAAA,cAA4E;IAC7CD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,wDAAiD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACxDH,EAAA,CAAAC,cAAA,iBAA4C;IAAzBD,EAAA,CAAAoC,UAAA,mBAAAC,4DAAA;MAAArC,EAAA,CAAAsC,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAxC,EAAA,CAAAyC,aAAA;MAAA,OAASzC,EAAA,CAAA0C,WAAA,CAAAF,OAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IAAC3C,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;ADjMhF,OAAM,MAAOyC,mBAAmB;EA+C9BC,YACUC,MAAc,EACdC,iBAAoC,EACpCC,cAA8B,EAC9BC,cAA8B,EAC9BC,WAAwB,EACxBC,cAA8B,EAC9BC,iBAAoC;IANpC,KAAAN,MAAM,GAANA,MAAM;IACN,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,iBAAiB,GAAjBA,iBAAiB;IArD3B;IACA,KAAAC,iBAAiB,GAAiB,EAAE;IACpC,KAAAC,QAAQ,GAAc,EAAE;IACxB,KAAAC,WAAW,GAAiB,EAAE;IAC9B,KAAAC,OAAO,GAAY,EAAE;IACrB,KAAAC,QAAQ,GAAU,EAAE;IACpB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,mBAAmB,GAAiB,EAAE;IACtC,KAAAC,eAAe,GAAY,EAAE;IAE7B;IACA,KAAAC,OAAO,GAAqB;MAC1BC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE;KACR;IAED;IACA,KAAAC,WAAW,GAAG,CAAC;IACf,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,YAAY,GAAG,EAAE;IAEjB;IACA,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,iBAAiB,GAAG,OAAO;IAC3B,KAAAC,IAAI,GAAGA,IAAI,CAAC,CAAC;IAEb;IACA,KAAAxD,UAAU,GAAQ,IAAI;IAEtB;IACA,KAAAyD,SAAS,GAAG,EAAE;IACd,KAAAC,OAAO,GAAG,EAAE;IACZ,KAAAC,SAAS,GAAG,OAAO,CAAC,CAAC;IAErB;IACA,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAAC,kBAAkB,GAAG,EAAE;IACvB,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,eAAe,GAAG,EAAE;EAUjB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,sBAAsB,EAAE;IAC7B,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,qBAAqB,EAAE;IAC5B,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAJ,eAAeA,CAAA;IACb;IACA,IAAI,CAACjC,cAAc,CAACsC,cAAc,CAAC,IAAI,CAAC,CAACC,SAAS,CAAC;MACjDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACvC,QAAQ,GAAGsC,QAAQ,CAACtC,QAAQ;;MAErC,CAAC;MACDwC,KAAK,EAAGA,KAAK,IAAKC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK;KACjE,CAAC;IAEF;IACA,IAAI,CAAC1C,iBAAiB,CAAC4C,iBAAiB,EAAE,CAACN,SAAS,CAAC;MACnDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACtC,WAAW,GAAGqC,QAAQ,CAACrC,WAAW;UACvC,IAAI,CAACK,mBAAmB,GAAG,IAAI,CAACL,WAAW;;MAE/C,CAAC;MACDuC,KAAK,EAAGA,KAAK,IAAKC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK;KACpE,CAAC;IAEF;IACA,IAAI,CAAC9C,cAAc,CAACiD,aAAa,EAAE,CAACP,SAAS,CAAC;MAC5CC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACrC,OAAO,GAAGoC,QAAQ,CAACpC,OAAO;UAC/B,IAAI,CAACK,eAAe,GAAG,IAAI,CAACL,OAAO;;MAEvC,CAAC;MACDsC,KAAK,EAAGA,KAAK,IAAKC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK;KAChE,CAAC;IAEF;IACA,IAAI,CAAC7C,cAAc,CAACiD,cAAc,EAAE,CAACR,SAAS,CAAC;MAC7CC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACpC,QAAQ,GAAGmC,QAAQ,CAACnC,QAAQ;;MAErC,CAAC;MACDqC,KAAK,EAAGA,KAAK,IAAKC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK;KACjE,CAAC;IAEF;IACA,IAAI,CAAC5C,WAAW,CAACiD,cAAc,CAAC,SAAS,CAAC,CAACT,SAAS,CAAC;MACnDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACnC,QAAQ,GAAGkC,QAAQ,CAACQ,KAAK;;MAElC,CAAC;MACDN,KAAK,EAAGA,KAAK,IAAKC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK;KACjE,CAAC;IAEF;IACA,IAAI,CAAC5C,WAAW,CAACiD,cAAc,CAAC,SAAS,CAAC,CAACT,SAAS,CAAC;MACnDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAAClC,QAAQ,GAAGiC,QAAQ,CAACQ,KAAK;;MAElC,CAAC;MACDN,KAAK,EAAGA,KAAK,IAAKC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK;KACjE,CAAC;EACJ;EAEAT,sBAAsBA,CAAA;IACpB;IACA;EAAA;EAGFC,mBAAmBA,CAAA;IACjB,MAAMe,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxB,IAAI,CAAC5B,OAAO,GAAG2B,KAAK,CAACE,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAEhD;IACA,QAAQ,IAAI,CAAC7B,SAAS;MACpB,KAAK,OAAO;QACV,IAAI,CAACF,SAAS,GAAG,IAAI,CAACC,OAAO;QAC7B;MACF,KAAK,SAAS;QACZ,MAAM+B,UAAU,GAAG,IAAIH,IAAI,CAACD,KAAK,CAACK,WAAW,EAAE,EAAEL,KAAK,CAACM,QAAQ,EAAE,EAAE,CAAC,CAAC;QACrE,IAAI,CAAClC,SAAS,GAAGgC,UAAU,CAACF,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACvD;MACF,KAAK,QAAQ;QACX,MAAMI,SAAS,GAAG,IAAIN,IAAI,CAACD,KAAK,CAACK,WAAW,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;QACrD,IAAI,CAACjC,SAAS,GAAGmC,SAAS,CAACL,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACtD;;EAEN;EAGAjB,qBAAqBA,CAAA;IACnB,IAAI,CAAClB,OAAO,GAAG,IAAI;IAEnB;IACA,MAAMwC,UAAU,GAAQ;MAAE,GAAG,IAAI,CAAC/C;IAAO,CAAE;IAE3C,IAAI,IAAI,CAACc,eAAe,EAAEiC,UAAU,CAACC,OAAO,GAAG,IAAI,CAAClC,eAAe;IACnE,IAAI,IAAI,CAACC,kBAAkB,EAAEgC,UAAU,CAACE,UAAU,GAAG,IAAI,CAAClC,kBAAkB;IAC5E,IAAI,IAAI,CAACC,aAAa,EAAE+B,UAAU,CAACG,OAAO,GAAG,IAAI,CAAClC,aAAa;IAC/D,IAAI,IAAI,CAACC,eAAe,EAAE8B,UAAU,CAACI,SAAS,GAAG,IAAI,CAAClC,eAAe;IACrE,IAAI,IAAI,CAACC,eAAe,EAAE6B,UAAU,CAACK,SAAS,GAAG,IAAI,CAAClC,eAAe;IACrE,IAAI,IAAI,CAACE,eAAe,EAAE2B,UAAU,CAACM,SAAS,GAAG,IAAI,CAACjC,eAAe;IACrE,IAAI,IAAI,CAACD,cAAc,EAAE4B,UAAU,CAAC7E,MAAM,GAAG,IAAI,CAACiD,cAAc;IAChE,IAAI,IAAI,CAACR,SAAS,EAAEoC,UAAU,CAACpC,SAAS,GAAG,IAAI,CAACA,SAAS;IACzD,IAAI,IAAI,CAACC,OAAO,EAAEmC,UAAU,CAACnC,OAAO,GAAG,IAAI,CAACA,OAAO;IACnD,IAAI,IAAI,CAACJ,WAAW,EAAEuC,UAAU,CAACO,MAAM,GAAG,IAAI,CAAC9C,WAAW;IAE1D,IAAI,CAACvB,iBAAiB,CAACsE,uBAAuB,CAACR,UAAU,CAAC,CAACnB,SAAS,CAAC;MACnEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACxC,iBAAiB,GAAGuC,QAAQ,CAAC0B,UAAU;UAC5C;UACA,IAAI1B,QAAQ,CAAC2B,UAAU,EAAE;YACvB,IAAI,CAACtD,WAAW,GAAG2B,QAAQ,CAAC2B,UAAU,CAACtD,WAAW;YAClD,IAAI,CAACC,UAAU,GAAG0B,QAAQ,CAAC2B,UAAU,CAACrD,UAAU;YAChD,IAAI,CAACC,YAAY,GAAGyB,QAAQ,CAAC2B,UAAU,CAACpD,YAAY;;;QAGxD,IAAI,CAACE,OAAO,GAAG,KAAK;MACtB,CAAC;MACDyB,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD,IAAI,CAACzB,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAmB,cAAcA,CAAA;IACZ,MAAMgC,YAAY,GAAQ,EAAE;IAE5B,IAAI,IAAI,CAAC1C,aAAa,EAAE0C,YAAY,CAACR,OAAO,GAAG,IAAI,CAAClC,aAAa;IACjE,IAAI,IAAI,CAACC,eAAe,EAAEyC,YAAY,CAACP,SAAS,GAAG,IAAI,CAAClC,eAAe;IACvE,IAAI,IAAI,CAACN,SAAS,EAAE+C,YAAY,CAAC/C,SAAS,GAAG,IAAI,CAACA,SAAS;IAC3D,IAAI,IAAI,CAACC,OAAO,EAAE8C,YAAY,CAAC9C,OAAO,GAAG,IAAI,CAACA,OAAO;IAErD,IAAI,CAAC3B,iBAAiB,CAAC0E,uBAAuB,CAACD,YAAY,CAAC,CAAC9B,SAAS,CAAC;MACrEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAAC7E,UAAU,GAAG4E,QAAQ,CAAC5E,UAAU;;MAEzC,CAAC;MACD8E,KAAK,EAAGA,KAAK,IAAKC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK;KACnE,CAAC;EACJ;EAEA;EACA4B,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC9C,eAAe,EAAE;MACxB,IAAI,CAAChB,mBAAmB,GAAG,IAAI,CAACL,WAAW,CAACoE,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACd,OAAO,CAACxG,GAAG,KAAK,IAAI,CAACsE,eAAe,CAAC;MACrG,IAAI,CAACC,kBAAkB,GAAG,EAAE;MAC5B,IAAI,CAACC,aAAa,GAAG,EAAE;MACvB,IAAI,CAACjB,eAAe,GAAG,EAAE;KAC1B,MAAM;MACL,IAAI,CAACD,mBAAmB,GAAG,IAAI,CAACL,WAAW;MAC3C,IAAI,CAACsB,kBAAkB,GAAG,EAAE;MAC5B,IAAI,CAACC,aAAa,GAAG,EAAE;MACvB,IAAI,CAACjB,eAAe,GAAG,IAAI,CAACL,OAAO;;IAErC,IAAI,CAACqE,YAAY,EAAE;EACrB;EAEAC,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACjD,kBAAkB,EAAE;MAC3B,IAAI,CAAChB,eAAe,GAAG,IAAI,CAACL,OAAO,CAACmE,MAAM,CAACI,GAAG,IAAIA,GAAG,CAAChB,UAAU,CAACzG,GAAG,KAAK,IAAI,CAACuE,kBAAkB,CAAC;MACjG,IAAI,CAACC,aAAa,GAAG,EAAE;KACxB,MAAM;MACL,IAAI,CAACjB,eAAe,GAAG,IAAI,CAACe,eAAe,GACzC,IAAI,CAACpB,OAAO,CAACmE,MAAM,CAACI,GAAG,IAAIA,GAAG,CAACjB,OAAO,CAACxG,GAAG,KAAK,IAAI,CAACsE,eAAe,CAAC,GACpE,IAAI,CAACpB,OAAO;MACd,IAAI,CAACsB,aAAa,GAAG,EAAE;;IAEzB,IAAI,CAAC+C,YAAY,EAAE;EACrB;EAEAG,iBAAiBA,CAAA;IACf,IAAI,CAAC1C,mBAAmB,EAAE;IAC1B,IAAI,CAACuC,YAAY,EAAE;EACrB;EAEAA,YAAYA,CAAA;IACV,IAAI,CAAC/D,OAAO,CAACC,IAAI,GAAG,CAAC,CAAC,CAAC;IACvB,IAAI,CAACE,WAAW,GAAG,CAAC;IACpB,IAAI,CAACsB,qBAAqB,EAAE;IAC5B,IAAI,CAACC,cAAc,EAAE;EACvB;EAEA7C,YAAYA,CAAA;IACV,IAAI,CAACiC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACE,eAAe,GAAG,EAAE;IACzB,IAAI,CAACD,cAAc,GAAG,EAAE;IACxB,IAAI,CAACN,SAAS,GAAG,OAAO;IACxB,IAAI,CAACW,mBAAmB,EAAE;IAC1B,IAAI,CAAC1B,mBAAmB,GAAG,IAAI,CAACL,WAAW;IAC3C,IAAI,CAACM,eAAe,GAAG,IAAI,CAACL,OAAO;IACnC,IAAI,CAACM,OAAO,GAAG;MAAEC,IAAI,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAE,CAAE;IACrC,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACsB,qBAAqB,EAAE;IAC5B,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAyC,YAAYA,CAAClE,IAAY;IACvB,IAAI,CAACD,OAAO,CAACC,IAAI,GAAGA,IAAI;IACxB,IAAI,CAACE,WAAW,GAAGF,IAAI;IACvB,IAAI,CAACwB,qBAAqB,EAAE;EAC9B;EAEA;EACA2C,QAAQA,CAAA;IACN,IAAI,IAAI,CAACjE,WAAW,GAAG,IAAI,CAACC,UAAU,EAAE;MACtC,IAAI,CAAC+D,YAAY,CAAC,IAAI,CAAChE,WAAW,GAAG,CAAC,CAAC;;EAE3C;EAEAkE,YAAYA,CAAA;IACV,IAAI,IAAI,CAAClE,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACgE,YAAY,CAAC,IAAI,CAAChE,WAAW,GAAG,CAAC,CAAC;;EAE3C;EAEAmE,gBAAgBA,CAAA;IACd;IACAC,KAAK,CAAC,0CAA0C,CAAC;EACnD;EAEA;EACAC,YAAYA,CAACC,KAAa;IACxB,IAAI,CAAChE,iBAAiB,GAAGgE,KAAK;IAC9B,MAAMlC,KAAK,GAAG,IAAIC,IAAI,EAAE;IAExB,QAAQiC,KAAK;MACX,KAAK,OAAO;QACV,IAAI,CAAC9D,SAAS,GAAG,IAAI,CAAC+D,kBAAkB,CAACnC,KAAK,CAAC;QAC/C,IAAI,CAAC3B,OAAO,GAAG,IAAI,CAAC8D,kBAAkB,CAACnC,KAAK,CAAC;QAC7C;MACF,KAAK,MAAM;QACT,MAAMoC,SAAS,GAAG,IAAInC,IAAI,CAACD,KAAK,CAAC;QACjCoC,SAAS,CAACC,OAAO,CAACrC,KAAK,CAACsC,OAAO,EAAE,GAAGtC,KAAK,CAACuC,MAAM,EAAE,CAAC;QACnD,MAAMC,OAAO,GAAG,IAAIvC,IAAI,CAACmC,SAAS,CAAC;QACnCI,OAAO,CAACH,OAAO,CAACD,SAAS,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;QACxC,IAAI,CAAClE,SAAS,GAAG,IAAI,CAAC+D,kBAAkB,CAACC,SAAS,CAAC;QACnD,IAAI,CAAC/D,OAAO,GAAG,IAAI,CAAC8D,kBAAkB,CAACK,OAAO,CAAC;QAC/C;MACF,KAAK,OAAO;QACV,MAAMpC,UAAU,GAAG,IAAIH,IAAI,CAACD,KAAK,CAACK,WAAW,EAAE,EAAEL,KAAK,CAACM,QAAQ,EAAE,EAAE,CAAC,CAAC;QACrE,MAAMmC,QAAQ,GAAG,IAAIxC,IAAI,CAACD,KAAK,CAACK,WAAW,EAAE,EAAEL,KAAK,CAACM,QAAQ,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;QACvE,IAAI,CAAClC,SAAS,GAAG,IAAI,CAAC+D,kBAAkB,CAAC/B,UAAU,CAAC;QACpD,IAAI,CAAC/B,OAAO,GAAG,IAAI,CAAC8D,kBAAkB,CAACM,QAAQ,CAAC;QAChD;;IAEJ,IAAI,CAACjB,YAAY,EAAE;EACrB;EAEAW,kBAAkBA,CAACjH,IAAU;IAC3B,OAAOA,IAAI,CAACgF,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACzC;EAEA,IAAIuC,eAAeA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACzE,WAAW,EAAE,OAAO,IAAI,CAACjB,iBAAiB;IAEpD,MAAM2F,WAAW,GAAG,IAAI,CAAC1E,WAAW,CAAC2E,WAAW,EAAE;IAClD,OAAO,IAAI,CAAC5F,iBAAiB,CAACsE,MAAM,CAACuB,MAAM,IACzCA,MAAM,CAAC1H,OAAO,EAAEf,IAAI,EAAEwI,WAAW,EAAE,CAACE,QAAQ,CAACH,WAAW,CAAC,IACzDE,MAAM,CAAC1H,OAAO,EAAEC,MAAM,EAAEwH,WAAW,EAAE,CAACE,QAAQ,CAACH,WAAW,CAAC,IAC3DE,MAAM,CAACxH,KAAK,EAAEd,SAAS,EAAEqI,WAAW,EAAE,CAACE,QAAQ,CAACH,WAAW,CAAC,IAC5DE,MAAM,CAACvH,OAAO,EAAEC,WAAW,EAAEqH,WAAW,EAAE,CAACE,QAAQ,CAACH,WAAW,CAAC,IAChEE,MAAM,CAACrH,OAAO,EAAEpB,IAAI,EAAEwI,WAAW,EAAE,CAACE,QAAQ,CAACH,WAAW,CAAC,CAC1D;EACH;EAEA;EACA,IAAII,iBAAiBA,CAAA;IACnB,OAAO,IAAI,CAAC/F,iBAAiB,CAACgG,GAAG,CAACH,MAAM,KAAK;MAC3CzI,IAAI,EAAEyI,MAAM,CAAC1H,OAAO,EAAEf,IAAI,IAAI,KAAK;MACnC6I,UAAU,EAAEJ,MAAM,CAAC1H,OAAO,EAAEC,MAAM,IAAI,KAAK;MAC3C8H,UAAU,EAAEL,MAAM,CAAC1H,OAAO,EAAEgI,WAAW,IAAI,KAAK;MAChDzC,UAAU,EAAEmC,MAAM,EAAE1H,OAAO,EAAEuF,UAAU,EAAEtG,IAAI,IAAIyI,MAAM,CAACxH,KAAK,EAAEqF,UAAU,EAAEtG,IAAI,IAAI,KAAK;MACxFiB,KAAK,EAAEwH,MAAM,CAACxH,KAAK,EAAEd,SAAS,IAAI,KAAK;MACvCC,OAAO,EAAEqI,MAAM,CAACxH,KAAK,EAAEb,OAAO,IAAI,KAAK;MACvC4I,cAAc,EAAEP,MAAM,CAAC1H,OAAO,EAAEkI,KAAK,IAAI,KAAK;MAC9CC,QAAQ,EAAET,MAAM,CAAC1H,OAAO,EAAEoI,OAAO,IAAI,KAAK;MAC1C5H,MAAM,EAAEkH,MAAM,CAAClH;KAChB,CAAC,CAAC;EACL;EAEA6H,UAAUA,CAACtI,IAAmB;IAC5B,OAAO,IAAI+E,IAAI,CAAC/E,IAAI,CAAC,CAACuI,kBAAkB,EAAE;EAC5C;EAEAC,cAAcA,CAAC/H,MAAc;IAC3B,QAAQA,MAAM;MACZ,KAAK,SAAS;QAAE,OAAO,gBAAgB;MACvC,KAAK,QAAQ;QAAE,OAAO,eAAe;MACrC,KAAK,MAAM;QAAE,OAAO,aAAa;MACjC;QAAS,OAAO,EAAE;;EAEtB;EAEAZ,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACJ,UAAU,IAAI,CAAC,IAAI,CAACA,UAAU,CAACG,KAAK,IAAI,IAAI,CAACH,UAAU,CAACG,KAAK,KAAK,CAAC,EAAE;MAC7E,OAAO,CAAC;;IAEV,MAAM6I,YAAY,GAAG,CAAC,IAAI,CAAChJ,UAAU,CAACC,OAAO,IAAI,CAAC,KAAK,IAAI,CAACD,UAAU,CAACiJ,IAAI,IAAI,CAAC,CAAC;IACjF,OAAOzF,IAAI,CAAC0F,KAAK,CAAEF,YAAY,GAAG,IAAI,CAAChJ,UAAU,CAACG,KAAK,GAAI,GAAG,CAAC;EACjE;EAAC,QAAAgJ,CAAA,G;qBApXUvH,mBAAmB,EAAA5C,EAAA,CAAAoK,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAtK,EAAA,CAAAoK,iBAAA,CAAAG,EAAA,CAAAC,iBAAA,GAAAxK,EAAA,CAAAoK,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA1K,EAAA,CAAAoK,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA5K,EAAA,CAAAoK,iBAAA,CAAAS,EAAA,CAAAC,WAAA,GAAA9K,EAAA,CAAAoK,iBAAA,CAAAW,EAAA,CAAAC,cAAA,GAAAhL,EAAA,CAAAoK,iBAAA,CAAAa,EAAA,CAAAC,iBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAnBvI,mBAAmB;IAAAwI,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC3BhC1L,EAAA,CAAAC,cAAA,aAAkC;QAKGD,EAAA,CAAAE,MAAA,iBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAClDH,EAAA,CAAAE,MAAA,oCACF;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAC,cAAA,WAAyB;QAAAD,EAAA,CAAAE,MAAA,yDAAkD;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAEjFH,EAAA,CAAAC,cAAA,aAA4B;QACgBD,EAAA,CAAAoC,UAAA,mBAAAwJ,sDAAA;UAAA,OAASD,GAAA,CAAAvD,gBAAA,EAAkB;QAAA,EAAC;QACpEpI,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC7BH,EAAA,CAAAE,MAAA,gBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAKbH,EAAA,CAAAC,cAAA,mBAA+B;QAGfD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAChCH,EAAA,CAAAE,MAAA,iBACF;QAAAF,EAAA,CAAAG,YAAA,EAAiB;QAEnBH,EAAA,CAAAC,cAAA,wBAAkB;QAIDD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC9BH,EAAA,CAAAC,cAAA,sBAAgF;QAApED,EAAA,CAAAoC,UAAA,2BAAAyJ,kEAAAC,MAAA;UAAA,OAAAH,GAAA,CAAA/G,eAAA,GAAAkH,MAAA;QAAA,EAA6B,6BAAAC,oEAAA;UAAA,OAAoBJ,GAAA,CAAAjE,eAAA,EAAiB;QAAA,EAArC;QACvC1H,EAAA,CAAAC,cAAA,sBAAqB;QAAAD,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAa;QAC9CH,EAAA,CAAAgM,UAAA,KAAAC,0CAAA,yBAEa;QACfjM,EAAA,CAAAG,YAAA,EAAa;QAIfH,EAAA,CAAAC,cAAA,0BAAqC;QACxBD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC5BH,EAAA,CAAAC,cAAA,sBAA2E;QAA/DD,EAAA,CAAAoC,UAAA,2BAAA8J,kEAAAJ,MAAA;UAAA,OAAAH,GAAA,CAAA7G,aAAA,GAAAgH,MAAA;QAAA,EAA2B,6BAAAK,oEAAA;UAAA,OAAoBR,GAAA,CAAA9D,YAAA,EAAc;QAAA,EAAlC;QACrC7H,EAAA,CAAAC,cAAA,sBAAqB;QAAAD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAa;QAC7CH,EAAA,CAAAgM,UAAA,KAAAI,0CAAA,yBAEa;QACfpM,EAAA,CAAAG,YAAA,EAAa;QAIfH,EAAA,CAAAC,cAAA,0BAAqC;QACxBD,EAAA,CAAAE,MAAA,iBAAS;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAChCH,EAAA,CAAAC,cAAA,iBAA8E;QAAlDD,EAAA,CAAAoC,UAAA,2BAAAiK,6DAAAP,MAAA;UAAA,OAAAH,GAAA,CAAAlH,SAAA,GAAAqH,MAAA;QAAA,EAAuB,oBAAAQ,sDAAA;UAAA,OAAWX,GAAA,CAAA9D,YAAA,EAAc;QAAA,EAAzB;QAAnD7H,EAAA,CAAAG,YAAA,EAA8E;QAGhFH,EAAA,CAAAC,cAAA,0BAAqC;QACxBD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC9BH,EAAA,CAAAC,cAAA,iBAA4E;QAAhDD,EAAA,CAAAoC,UAAA,2BAAAmK,6DAAAT,MAAA;UAAA,OAAAH,GAAA,CAAAjH,OAAA,GAAAoH,MAAA;QAAA,EAAqB,oBAAAU,sDAAA;UAAA,OAAWb,GAAA,CAAA9D,YAAA,EAAc;QAAA,EAAzB;QAAjD7H,EAAA,CAAAG,YAAA,EAA4E;QAI9EH,EAAA,CAAAC,cAAA,eAA4B;QACeD,EAAA,CAAAoC,UAAA,mBAAAqK,sDAAA;UAAA,OAASd,GAAA,CAAA9D,YAAA,EAAc;QAAA,EAAC;QAC/D7H,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC3BH,EAAA,CAAAE,MAAA,eACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,kBAA4C;QAAzBD,EAAA,CAAAoC,UAAA,mBAAAsK,sDAAA;UAAA,OAASf,GAAA,CAAAhJ,YAAA,EAAc;QAAA,EAAC;QACzC3C,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC5BH,EAAA,CAAAE,MAAA,eACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAOjBH,EAAA,CAAAgM,UAAA,KAAAW,mCAAA,mBA0DM;QAGJ3M,EAAA,CAAAC,cAAA,oBAA6B;QAGbD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC7BH,EAAA,CAAAE,MAAA,4BACF;QAAAF,EAAA,CAAAG,YAAA,EAAiB;QACjBH,EAAA,CAAAC,cAAA,yBAAmB;QAAAD,EAAA,CAAAE,MAAA,IAAgC;QAAAF,EAAA,CAAAG,YAAA,EAAoB;QAEzEH,EAAA,CAAAC,cAAA,wBAAkB;QAMeD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC9BH,EAAA,CAAAC,cAAA,cAAwB;QAAAD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACpCH,EAAA,CAAAC,cAAA,cAA6C;QAAAD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACvDH,EAAA,CAAAC,cAAA,cAA+C;QAAAD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC3DH,EAAA,CAAAC,cAAA,cAAuB;QAAAD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAClCH,EAAA,CAAAC,cAAA,cAA4C;QAAAD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAGzDH,EAAA,CAAAC,cAAA,aAAO;QACLD,EAAA,CAAAgM,UAAA,KAAAY,kCAAA,mBA4CK;QACP5M,EAAA,CAAAG,YAAA,EAAQ;QAIVH,EAAA,CAAAgM,UAAA,KAAAa,mCAAA,kBAGM;QAGN7M,EAAA,CAAAgM,UAAA,KAAAc,mCAAA,kBAKM;QACR9M,EAAA,CAAAG,YAAA,EAAM;QAMVH,EAAA,CAAAC,cAAA,eAAmE;QAG7DD,EAAA,CAAAE,MAAA,IAGF;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAETH,EAAA,CAAAC,cAAA,eAAiE;QACRD,EAAA,CAAAoC,UAAA,mBAAA2K,sDAAA;UAAA,OAASpB,GAAA,CAAAxD,YAAA,EAAc;QAAA,EAAC;QAC7EnI,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAEnCH,EAAA,CAAAC,cAAA,gBAAwB;QAAAD,EAAA,CAAAE,MAAA,IAA0C;QAAAF,EAAA,CAAAG,YAAA,EAAO;QACzEH,EAAA,CAAAC,cAAA,kBAAgI;QAA5CD,EAAA,CAAAoC,UAAA,mBAAA4K,sDAAA;UAAA,OAASrB,GAAA,CAAAzD,QAAA,EAAU;QAAA,EAAC;QACtGlI,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAW;;;QAnNxBH,EAAA,CAAAO,SAAA,IAA6B;QAA7BP,EAAA,CAAAI,UAAA,YAAAuL,GAAA,CAAA/G,eAAA,CAA6B;QAEP5E,EAAA,CAAAO,SAAA,GAAW;QAAXP,EAAA,CAAAI,UAAA,YAAAuL,GAAA,CAAArI,QAAA,CAAW;QASjCtD,EAAA,CAAAO,SAAA,GAA2B;QAA3BP,EAAA,CAAAI,UAAA,YAAAuL,GAAA,CAAA7G,aAAA,CAA2B;QAET9E,EAAA,CAAAO,SAAA,GAAkB;QAAlBP,EAAA,CAAAI,UAAA,YAAAuL,GAAA,CAAA9H,eAAA,CAAkB;QASpB7D,EAAA,CAAAO,SAAA,GAAuB;QAAvBP,EAAA,CAAAI,UAAA,YAAAuL,GAAA,CAAAlH,SAAA,CAAuB;QAKvBzE,EAAA,CAAAO,SAAA,GAAqB;QAArBP,EAAA,CAAAI,UAAA,YAAAuL,GAAA,CAAAjH,OAAA,CAAqB;QAmB7B1E,EAAA,CAAAO,SAAA,IAAgB;QAAhBP,EAAA,CAAAI,UAAA,SAAAuL,GAAA,CAAA3K,UAAA,CAAgB;QAmEnBhB,EAAA,CAAAO,SAAA,GAAgC;QAAhCP,EAAA,CAAAQ,kBAAA,KAAAmL,GAAA,CAAAxH,YAAA,mBAAgC;QAiBpBnE,EAAA,CAAAO,SAAA,IAAsB;QAAtBP,EAAA,CAAAI,UAAA,YAAAuL,GAAA,CAAAtI,iBAAA,CAAsB;QAiD3CrD,EAAA,CAAAO,SAAA,GAAa;QAAbP,EAAA,CAAAI,UAAA,SAAAuL,GAAA,CAAAtH,OAAA,CAAa;QAMbrE,EAAA,CAAAO,SAAA,GAAgD;QAAhDP,EAAA,CAAAI,UAAA,UAAAuL,GAAA,CAAAtH,OAAA,IAAAsH,GAAA,CAAAtI,iBAAA,CAAA4J,MAAA,OAAgD;QAetDjN,EAAA,CAAAO,SAAA,GAGF;QAHEP,EAAA,CAAAkN,kBAAA,eAAAvB,GAAA,CAAA1H,WAAA,QAAA0H,GAAA,CAAAvH,YAAA,cAAAuH,GAAA,CAAAnH,IAAA,CAAA2I,GAAA,CAAAxB,GAAA,CAAA1H,WAAA,GAAA0H,GAAA,CAAAvH,YAAA,EAAAuH,GAAA,CAAAxH,YAAA,WAAAwH,GAAA,CAAAxH,YAAA,yBAGF;QAGwBnE,EAAA,CAAAO,SAAA,GAA8B;QAA9BP,EAAA,CAAAI,UAAA,aAAAuL,GAAA,CAAA1H,WAAA,OAA8B;QAG9BjE,EAAA,CAAAO,SAAA,GAA0C;QAA1CP,EAAA,CAAAW,kBAAA,UAAAgL,GAAA,CAAA1H,WAAA,UAAA0H,GAAA,CAAAzH,UAAA,KAA0C;QAC1ClE,EAAA,CAAAO,SAAA,GAA2D;QAA3DP,EAAA,CAAAI,UAAA,aAAAuL,GAAA,CAAA1H,WAAA,KAAA0H,GAAA,CAAAzH,UAAA,IAAAyH,GAAA,CAAAzH,UAAA,OAA2D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}