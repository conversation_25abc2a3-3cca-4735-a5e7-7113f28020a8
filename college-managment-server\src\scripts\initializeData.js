const mongoose = require('mongoose');
const Program = require('../models/program.js');
const Subject = require('../models/subject.js');
const User = require('../models/userModel.js');
const bcrypt = require('bcrypt');

const initializeData = async () => {
    try {
        console.log('Starting data initialization...');

        // Create default programs
        const programs = [
            {
                name: 'BS',
                fullName: 'Bachelor of Science',
                description: 'Four-year undergraduate degree program',
                duration: 4,
                durationUnit: 'years',
                totalSemesters: 8
            },
            {
                name: 'Intermediate',
                fullName: 'Intermediate Education',
                description: 'Two-year pre-university program',
                duration: 2,
                durationUnit: 'years',
                totalSemesters: 4
            }
        ];

        for (const programData of programs) {
            const existingProgram = await Program.findOne({ name: programData.name });
            if (!existingProgram) {
                await Program.create(programData);
                console.log(`Created program: ${programData.name}`);
            } else {
                console.log(`Program already exists: ${programData.name}`);
            }
        }

        // Note: Departments will be created manually by administrators
        // No default departments are initialized to allow custom department setup
        console.log('Skipping department initialization - departments will be created manually by administrators');

        // Create default Principal user
        const principalEmail = '<EMAIL>';
        const existingPrincipal = await User.findOne({ email: principalEmail });
        
        if (!existingPrincipal) {
            const hashedPassword = await bcrypt.hash('principal123', 10);
            await User.create({
                name: 'Principal Admin',
                email: principalEmail,
                password: hashedPassword,
                role: 'Principal',
                contact: '+1234567890',
                address: 'College Campus',
                isActive: true
            });
            console.log('Created default Principal user');
            console.log('Email: <EMAIL>');
            console.log('Password: principal123');
        } else {
            console.log('Principal user already exists');
        }

        console.log('Data initialization completed successfully!');
    } catch (error) {
        console.error('Error initializing data:', error);
    }
};

module.exports = initializeData;
