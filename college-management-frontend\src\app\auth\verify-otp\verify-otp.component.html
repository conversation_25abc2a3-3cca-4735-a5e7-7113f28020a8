<div class="container-fluid">
    <div class="row vh-100">
        <div class="col-lg-5 d-flex flex-column justify-content-center align-items-center p-3 bg-white">
            <div class="w-100" style="max-width: 400px;">
                <div class="text-center">
                    <h2 class="mb-4"><img src="../../../assets/images/logo.jpeg"/> GPGC (Swabi)</h2>
                    <div class="d-flex justify-content-center mt-5 mb-2">
                        <i class="fa fa-envelope key" aria-hidden="true"></i>
                    </div>
                    
                    <h1 class="mb-4">Enter Verification Code</h1>
                    <p *ngIf="user">We sent a 6-digit verification code to <strong>{{ user.email }}</strong></p>
                    <p class="text-muted">Please enter the code below to verify your identity.</p>
                </div>
                
                <form [formGroup]="otpForm" (ngSubmit)="verifyOtp()">
                    <!-- OTP Input Fields -->
                    <div class="otp-container mb-4">
                        <input 
                            type="text" 
                            id="digit1"
                            class="otp-input" 
                            formControlName="digit1"
                            maxlength="1"
                            (input)="onDigitInput($event, 'digit2')"
                            (keydown)="onDigitKeydown($event, '')"
                            (paste)="onPaste($event)"
                            [class.is-invalid]="otpForm.get('digit1')?.invalid && otpForm.get('digit1')?.touched">
                        
                        <input 
                            type="text" 
                            id="digit2"
                            class="otp-input" 
                            formControlName="digit2"
                            maxlength="1"
                            (input)="onDigitInput($event, 'digit3')"
                            (keydown)="onDigitKeydown($event, 'digit1')"
                            [class.is-invalid]="otpForm.get('digit2')?.invalid && otpForm.get('digit2')?.touched">
                        
                        <input 
                            type="text" 
                            id="digit3"
                            class="otp-input" 
                            formControlName="digit3"
                            maxlength="1"
                            (input)="onDigitInput($event, 'digit4')"
                            (keydown)="onDigitKeydown($event, 'digit2')"
                            [class.is-invalid]="otpForm.get('digit3')?.invalid && otpForm.get('digit3')?.touched">
                        
                        <input 
                            type="text" 
                            id="digit4"
                            class="otp-input" 
                            formControlName="digit4"
                            maxlength="1"
                            (input)="onDigitInput($event, 'digit5')"
                            (keydown)="onDigitKeydown($event, 'digit3')"
                            [class.is-invalid]="otpForm.get('digit4')?.invalid && otpForm.get('digit4')?.touched">
                        
                        <input 
                            type="text" 
                            id="digit5"
                            class="otp-input" 
                            formControlName="digit5"
                            maxlength="1"
                            (input)="onDigitInput($event, 'digit6')"
                            (keydown)="onDigitKeydown($event, 'digit4')"
                            [class.is-invalid]="otpForm.get('digit5')?.invalid && otpForm.get('digit5')?.touched">
                        
                        <input 
                            type="text" 
                            id="digit6"
                            class="otp-input" 
                            formControlName="digit6"
                            maxlength="1"
                            (input)="onDigitInput($event, '')"
                            (keydown)="onDigitKeydown($event, 'digit5')"
                            [class.is-invalid]="otpForm.get('digit6')?.invalid && otpForm.get('digit6')?.touched">
                    </div>
                  
                    <div class="d-grid gap-2">
                        <button 
                            type="submit" 
                            class="btn submit" 
                            [disabled]="loading || otpForm.invalid">
                            <span *ngIf="loading" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                            {{ loading ? 'Verifying...' : 'Verify OTP' }}
                        </button>
                    </div>
                    
                    <!-- Resend OTP Section -->
                    <div class="text-center mt-4">
                        <p class="mb-2">Didn't receive the code?</p>
                        <button 
                            type="button" 
                            class="btn resend" 
                            (click)="resendOtp()"
                            [disabled]="resendLoading || countdown > 0">
                            <span *ngIf="resendLoading" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                            <span *ngIf="countdown > 0">Resend in {{ countdown }}s</span>
                            <span *ngIf="countdown <= 0 && !resendLoading">Resend OTP</span>
                            <span *ngIf="resendLoading">Sending...</span>
                        </button>
                    </div>
                    
                    <p class="mt-4 text-center"> 
                        <i class="fa fa-arrow-left" aria-hidden="true"></i> &nbsp;
                        <a (click)="goBack()" style="cursor: pointer;">Back to forgot password</a>
                    </p>
                </form>
            </div>
        </div>
        <div class="col-lg-7 d-none d-lg-flex flex-column align-items-start bg-light position-relative">
            <div class="text-start p-5 w-100">
                <blockquote class="blockquote">
                    <h2 class="mb-4">College management system</h2>
                    <p class="mb-4">Secure password reset process</p>
                    <footer class="blockquote-footer">GPGC SWABI <cite title="Source Title">Government Post Graduate College</cite></footer>
                </blockquote>
            </div>
            <div class="image-container">
                <img src="../../../assets/images/background.jpeg" class="img-fluid position-absolute" alt="College background">
            </div>
        </div>
    </div>
</div>
