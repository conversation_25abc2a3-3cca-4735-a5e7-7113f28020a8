import { Component, OnInit } from '@angular/core';
import { TimetableService } from 'src/app/services/timetable.service';
import { UserService } from 'src/app/services/user.service';
import { MatSnackBar } from '@angular/material/snack-bar';

interface TimetableEntry {
  _id: string;
  dayOfWeek: string;
  timeSlot: {
    startTime: string;
    endTime: string;
    duration: number;
  };
  subject: {
    _id: string;
    subjectName: string;
    code: string;
  };
  class: {
    _id: string;
    className: string;
    section: string;
  };
  program: {
    _id: string;
    name: string;
  };
  department: {
    _id: string;
    name: string;
  };
  room?: string;
  semester: number;
  academicYear: string;
  notes?: string;
}

interface TimeSlot {
  time: string;
  startTime: string;
  endTime: string;
  monday?: TimetableEntry | null;
  tuesday?: TimetableEntry | null;
  wednesday?: TimetableEntry | null;
  thursday?: TimetableEntry | null;
  friday?: TimetableEntry | null;
  saturday?: TimetableEntry | null;
  [key: string]: any; // Allow dynamic indexing
}

@Component({
  selector: 'app-teacher-timetable',
  templateUrl: './teacher-timetable.component.html',
  styleUrls: ['./teacher-timetable.component.css']
})
export class TeacherTimetableComponent implements OnInit {
  currentUser: any;
  timetableEntries: TimetableEntry[] = [];
  timetableGrid: TimeSlot[] = [];
  loading = false;
  error: string | null = null;
  
  weekDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  timeSlots = [
    { start: '08:00', end: '09:00' },
    { start: '09:00', end: '10:00' },
    { start: '10:00', end: '11:00' },
    { start: '11:00', end: '12:00' },
    { start: '12:00', end: '13:00' },
    { start: '13:00', end: '14:00' },
    { start: '14:00', end: '15:00' },
    { start: '15:00', end: '16:00' },
    { start: '16:00', end: '17:00' },
    { start: '17:00', end: '18:00' }
  ];

  // Filter options
  selectedAcademicYear = '2024-2025';
  selectedDay = '';
  availableAcademicYears = ['2024-2025', '2023-2024', '2022-2023'];

  // Statistics
  totalClasses = 0;
  totalSubjects = 0;
  totalHours = 0;

  constructor(
    private timetableService: TimetableService,
    private userService: UserService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.currentUser = this.userService.getUserFromLocalStorage()?.user;
    if (this.currentUser) {
      this.loadTeacherTimetable();
    } else {
      this.error = 'User not found. Please login again.';
    }
  }

  loadTeacherTimetable(): void {
    this.loading = true;
    this.error = null;

    this.timetableService.getTimetableByTeacher(
      this.currentUser._id, 
      this.selectedAcademicYear, 
      this.selectedDay
    ).subscribe({
      next: (response) => {
        this.loading = false;
        if (response.success) {
          this.timetableEntries = response.timetable;
          this.generateTimetableGrid();
          this.calculateStatistics();
        } else {
          this.error = 'Failed to load timetable';
        }
      },
      error: (error) => {
        this.loading = false;
        console.error('Error loading teacher timetable:', error);
        this.error = 'Error loading timetable data';
        this.snackBar.open('Failed to load timetable', 'Close', { duration: 3000 });
      }
    });
  }

  generateTimetableGrid(): void {
    this.timetableGrid = this.timeSlots.map(slot => {
      const timeSlot: TimeSlot = {
        time: `${slot.start} - ${slot.end}`,
        startTime: slot.start,
        endTime: slot.end
      };

      // Initialize all days as null
      this.weekDays.forEach(day => {
        timeSlot[day.toLowerCase() as keyof TimeSlot] = null;
      });

      // Fill in the actual timetable entries
      this.timetableEntries.forEach(entry => {
        const entryStartTime = entry.timeSlot.startTime;
        const dayKey = entry.dayOfWeek.toLowerCase() as keyof TimeSlot;
        
        if (entryStartTime === slot.start && this.weekDays.map(d => d.toLowerCase()).includes(dayKey as string)) {
          timeSlot[dayKey] = entry;
        }
      });

      return timeSlot;
    });
  }

  calculateStatistics(): void {
    this.totalClasses = this.timetableEntries.length;
    
    // Count unique subjects
    const uniqueSubjects = new Set(this.timetableEntries.map(entry => entry.subject._id));
    this.totalSubjects = uniqueSubjects.size;
    
    // Calculate total hours per week
    this.totalHours = this.timetableEntries.reduce((total, entry) => {
      return total + (entry.timeSlot.duration / 60); // Convert minutes to hours
    }, 0);
  }

  onFilterChange(): void {
    this.loadTeacherTimetable();
  }

  getCurrentDay(): string {
    const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    return days[new Date().getDay()];
  }

  getCurrentDate(): Date {
    return new Date();
  }

  isCurrentTimeSlot(timeSlot: TimeSlot): boolean {
    const now = new Date();
    const currentTime = now.getHours().toString().padStart(2, '0') + ':' + 
                       now.getMinutes().toString().padStart(2, '0');
    
    return currentTime >= timeSlot.startTime && currentTime < timeSlot.endTime;
  }

  getClassDisplayName(classData: any): string {
    if (!classData) return '';
    return `${classData.className}${classData.section ? ' - ' + classData.section : ''}`;
  }

  getSemesterDisplayText(semester: number, programName: string): string {
    if (programName === 'Intermediate') {
      return semester === 1 ? '1st Year' : '2nd Year';
    } else {
      const semesterMap: { [key: number]: string } = {
        1: '1st', 2: '2nd', 3: '3rd', 4: '4th',
        5: '5th', 6: '6th', 7: '7th', 8: '8th'
      };
      return `${semesterMap[semester] || semester} Semester`;
    }
  }

  refreshTimetable(): void {
    this.loadTeacherTimetable();
  }

  exportTimetable(): void {
    // Implementation for exporting timetable (PDF, Excel, etc.)
    this.snackBar.open('Export functionality coming soon!', 'Close', { duration: 3000 });
  }

  getTodayClasses(): TimetableEntry[] {
    const today = this.getCurrentDay();
    return this.timetableEntries.filter(entry =>
      entry.dayOfWeek.toLowerCase() === today
    ).sort((a, b) => a.timeSlot.startTime.localeCompare(b.timeSlot.startTime));
  }
}
