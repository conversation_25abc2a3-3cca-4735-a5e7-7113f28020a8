{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/role.service\";\nimport * as i4 from \"src/app/services/user.service\";\nimport * as i5 from \"@angular/common\";\nfunction LoginComponent_span_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sign in\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_span_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"span\", 31);\n    i0.ɵɵtext(2, \" Signing in... \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class LoginComponent {\n  constructor(fb, router, roleService, userService) {\n    this.fb = fb;\n    this.router = router;\n    this.roleService = roleService;\n    this.userService = userService;\n    this.showPassword = false;\n    this.loginError = ''; // To store error messages\n    this.loading = false;\n  }\n  ngOnInit() {\n    // Initialize form with validation\n    this.loginForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(6)]],\n      remember: [false]\n    });\n  }\n  onSubmit() {\n    // Reset the error message\n    this.loginError = '';\n    this.loading = true;\n    if (this.loginForm.valid) {\n      // Capture form values\n      const {\n        email,\n        password\n      } = this.loginForm.value;\n      // Call the UserService's login method\n      this.userService.login({\n        email,\n        password\n      }).subscribe({\n        next: res => {\n          if (res.success) {\n            let role = res.user.role;\n            console.log(role);\n            this.loading = false;\n            // Check role and perform role-based login\n            if (role === 'Principal') {\n              this.roleService.setRole('Principal');\n              console.log('Principal logged in');\n            } else if (role === 'Teacher') {\n              this.roleService.setRole('teacher');\n              console.log('Teacher logged in');\n            } else if (role === 'Student') {\n              this.roleService.setRole('student');\n              console.log('Student logged in');\n            } else {\n              this.loginError = 'Invalid credentials for the selected role';\n              console.log('Invalid credentials');\n              return; // Stop further execution\n            }\n            // Redirect to the dashboard if role is set\n            this.router.navigate(['/dashboard']);\n          } else {\n            this.loginError = res.message || 'Login failed';\n            Swal.fire({\n              icon: 'error',\n              title: 'Login Failed',\n              text: this.loginError\n            });\n            this.loading = false;\n          }\n        },\n        error: error => {\n          console.error('Login failed', error);\n          Swal.fire({\n            icon: 'error',\n            title: 'Login Failed',\n            text: error.error.message\n          });\n          this.loginError = 'Server error, please try again later.';\n          this.loading = false;\n        }\n      });\n    } else {\n      // If the form is invalid, display an error message\n      this.loginError = 'Please fill in all fields correctly';\n      console.log('Form is invalid');\n      this.loading = false;\n    }\n  }\n  // Toggle the visibility of the password\n  togglePasswordVisibility() {\n    this.showPassword = !this.showPassword;\n  }\n  static #_ = this.ɵfac = function LoginComponent_Factory(t) {\n    return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.RoleService), i0.ɵɵdirectiveInject(i4.UserService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LoginComponent,\n    selectors: [[\"app-login\"]],\n    decls: 48,\n    vars: 6,\n    consts: [[1, \"container-fluid\"], [1, \"row\", \"vh-100\"], [1, \"col-lg-5\", \"d-flex\", \"flex-column\", \"justify-content-center\", \"align-items-center\", \"p-3\", \"bg-white\"], [1, \"w-100\", 2, \"max-width\", \"400px\"], [1, \"mb-4\"], [\"src\", \"../../../assets/images/logo.jpeg\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"mb-3\"], [\"for\", \"email\", 1, \"form-label\"], [\"type\", \"email\", \"id\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter your email\", 1, \"form-control\"], [1, \"mb-3\", \"position-relative\"], [\"for\", \"password\", 1, \"form-label\"], [\"id\", \"password\", \"formControlName\", \"password\", \"placeholder\", \"Enter your password\", 1, \"form-control\", 3, \"type\"], [1, \"fas\", \"viewpassword\", 3, \"ngClass\", \"click\"], [1, \"form-check\", \"mb-3\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"type\", \"checkbox\", \"formControlName\", \"remember\", \"id\", \"remember\", 1, \"form-check-input\"], [\"for\", \"remember\", 1, \"form-check-label\"], [\"routerLink\", \"/auth/forgot-password\", 1, \"mt-0\"], [1, \"forgot\", 2, \"cursor\", \"pointer\"], [1, \"d-grid\", \"gap-2\"], [\"type\", \"submit\", 1, \"btn\", \"submit\", 3, \"disabled\"], [4, \"ngIf\"], [1, \"mt-3\"], [\"routerLink\", \"/auth/sign-up\"], [1, \"col-lg-7\", \"d-none\", \"d-lg-flex\", \"flex-column\", \"align-items-start\", \"bg-light\", \"position-relative\"], [1, \"text-start\", \"p-5\", \"w-100\"], [1, \"blockquote\"], [1, \"blockquote-footer\"], [\"title\", \"Source Title\"], [1, \"image-container\"], [\"src\", \"../../../assets/images/background.jpeg\", \"alt\", \"Stats graph\", 1, \"img-fluid\", \"position-absolute\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"]],\n    template: function LoginComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h2\", 4);\n        i0.ɵɵelement(5, \"img\", 5);\n        i0.ɵɵtext(6, \" GPGC (Swabi)\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"h1\", 4);\n        i0.ɵɵtext(8, \"Log in\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"p\");\n        i0.ɵɵtext(10, \"Welcome back! Please enter your details.\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"form\", 6);\n        i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_11_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵelementStart(12, \"div\", 7)(13, \"label\", 8);\n        i0.ɵɵtext(14, \"Email\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(15, \"input\", 9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(16, \"div\", 10)(17, \"label\", 11);\n        i0.ɵɵtext(18, \"Password(123456)\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(19, \"input\", 12);\n        i0.ɵɵelementStart(20, \"i\", 13);\n        i0.ɵɵlistener(\"click\", function LoginComponent_Template_i_click_20_listener() {\n          return ctx.togglePasswordVisibility();\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(21, \"div\", 14)(22, \"div\");\n        i0.ɵɵelement(23, \"input\", 15);\n        i0.ɵɵelementStart(24, \"label\", 16);\n        i0.ɵɵtext(25, \"Remember me\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(26, \"p\", 17)(27, \"a\", 18);\n        i0.ɵɵtext(28, \"Forgot password?\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(29, \"div\", 19)(30, \"button\", 20);\n        i0.ɵɵtemplate(31, LoginComponent_span_31_Template, 2, 0, \"span\", 21);\n        i0.ɵɵtemplate(32, LoginComponent_span_32_Template, 3, 0, \"span\", 21);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(33, \"p\", 22);\n        i0.ɵɵtext(34, \"Don't have an account? \");\n        i0.ɵɵelementStart(35, \"a\", 23);\n        i0.ɵɵtext(36, \"Sign up\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(37, \"div\", 24)(38, \"div\", 25)(39, \"blockquote\", 26)(40, \"h2\", 4);\n        i0.ɵɵtext(41, \"College management system Login page\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(42, \"footer\", 27);\n        i0.ɵɵtext(43, \"Name\");\n        i0.ɵɵelementStart(44, \"cite\", 28);\n        i0.ɵɵtext(45, \"Owner ~ GPGC SWABI\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(46, \"div\", 29);\n        i0.ɵɵelement(47, \"img\", 30);\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(11);\n        i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"type\", ctx.showPassword ? \"text\" : \"password\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngClass\", ctx.showPassword ? \"fa-eye-slash\" : \"fa-eye\");\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"disabled\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n      }\n    },\n    dependencies: [i5.NgClass, i5.NgIf, i2.RouterLink, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n    styles: [\"body[_ngcontent-%COMP%], html[_ngcontent-%COMP%] {\\n    margin: 0;\\n    padding: 0;\\n    overflow-x: hidden; \\n\\n    height: 100%;\\n}\\n\\n\\n\\n.image-container[_ngcontent-%COMP%] {\\n    width: 100%;\\n    height: 100%;\\n    position: relative;\\n}\\n\\n.image-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    max-width: 80%;\\n    height: auto;\\n    position: absolute;\\n    right: 0;\\n    bottom: 0;\\n    object-fit: contain; \\n\\n    border-radius: 20px;\\n}\\n.submit[_ngcontent-%COMP%]{\\n    background-color: #29578c;\\n    color: white;\\n}\\n.forgot[_ngcontent-%COMP%]{\\n    color: #29578c;\\n    text-decoration: none;\\n    font-weight: 700;\\n}\\na[_ngcontent-%COMP%]{\\n    cursor: pointer;\\n    color: #29578c;\\n    font-weight: 700;\\n\\n\\n}\\n.viewpassword[_ngcontent-%COMP%]{\\n    position: absolute;\\n    right: 10px;\\n    transform: translateY(-50%);\\n    cursor: pointer;\\n    bottom: 7%;\\n}\\n\\n\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXV0aC9sb2dpbi9sb2dpbi5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0lBQ0ksU0FBUztJQUNULFVBQVU7SUFDVixrQkFBa0IsRUFBRSxpQ0FBaUM7SUFDckQsWUFBWTtBQUNoQjs7OztBQUlBO0lBQ0ksV0FBVztJQUNYLFlBQVk7SUFDWixrQkFBa0I7QUFDdEI7O0FBRUE7SUFDSSxjQUFjO0lBQ2QsWUFBWTtJQUNaLGtCQUFrQjtJQUNsQixRQUFRO0lBQ1IsU0FBUztJQUNULG1CQUFtQixFQUFFLCtDQUErQztJQUNwRSxtQkFBbUI7QUFDdkI7QUFDQTtJQUNJLHlCQUF5QjtJQUN6QixZQUFZO0FBQ2hCO0FBQ0E7SUFDSSxjQUFjO0lBQ2QscUJBQXFCO0lBQ3JCLGdCQUFnQjtBQUNwQjtBQUNBO0lBQ0ksZUFBZTtJQUNmLGNBQWM7SUFDZCxnQkFBZ0I7OztBQUdwQjtBQUNBO0lBQ0ksa0JBQWtCO0lBQ2xCLFdBQVc7SUFDWCwyQkFBMkI7SUFDM0IsZUFBZTtJQUNmLFVBQVU7QUFDZCIsInNvdXJjZXNDb250ZW50IjpbImJvZHksIGh0bWwge1xyXG4gICAgbWFyZ2luOiAwO1xyXG4gICAgcGFkZGluZzogMDtcclxuICAgIG92ZXJmbG93LXg6IGhpZGRlbjsgLyogUHJldmVudCBob3Jpem9udGFsIHNjcm9sbGluZyAqL1xyXG4gICAgaGVpZ2h0OiAxMDAlO1xyXG59XHJcblxyXG5cclxuXHJcbi5pbWFnZS1jb250YWluZXIge1xyXG4gICAgd2lkdGg6IDEwMCU7XHJcbiAgICBoZWlnaHQ6IDEwMCU7XHJcbiAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbn1cclxuXHJcbi5pbWFnZS1jb250YWluZXIgaW1nIHtcclxuICAgIG1heC13aWR0aDogODAlO1xyXG4gICAgaGVpZ2h0OiBhdXRvO1xyXG4gICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgcmlnaHQ6IDA7XHJcbiAgICBib3R0b206IDA7XHJcbiAgICBvYmplY3QtZml0OiBjb250YWluOyAvKiBFbnN1cmUgdGhlIGltYWdlIGZpdHMgd2l0aGluIGl0cyBjb250YWluZXIgKi9cclxuICAgIGJvcmRlci1yYWRpdXM6IDIwcHg7XHJcbn1cclxuLnN1Ym1pdHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICMyOTU3OGM7XHJcbiAgICBjb2xvcjogd2hpdGU7XHJcbn1cclxuLmZvcmdvdHtcclxuICAgIGNvbG9yOiAjMjk1NzhjO1xyXG4gICAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xyXG4gICAgZm9udC13ZWlnaHQ6IDcwMDtcclxufVxyXG5he1xyXG4gICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgY29sb3I6ICMyOTU3OGM7XHJcbiAgICBmb250LXdlaWdodDogNzAwO1xyXG5cclxuXHJcbn1cclxuLnZpZXdwYXNzd29yZHtcclxuICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICAgIHJpZ2h0OiAxMHB4O1xyXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01MCUpO1xyXG4gICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgYm90dG9tOiA3JTtcclxufVxyXG5cclxuXHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "LoginComponent", "constructor", "fb", "router", "roleService", "userService", "showPassword", "loginError", "loading", "ngOnInit", "loginForm", "group", "email", "required", "password", "<PERSON><PERSON><PERSON><PERSON>", "remember", "onSubmit", "valid", "value", "login", "subscribe", "next", "res", "success", "role", "user", "console", "log", "setRole", "navigate", "message", "fire", "icon", "title", "text", "error", "togglePasswordVisibility", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "Router", "i3", "RoleService", "i4", "UserService", "_2", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_11_listener", "LoginComponent_Template_i_click_20_listener", "ɵɵtemplate", "LoginComponent_span_31_Template", "LoginComponent_span_32_Template", "ɵɵadvance", "ɵɵproperty"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\auth\\login\\login.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\auth\\login\\login.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { RoleService } from 'src/app/services/role.service';\r\nimport { UserService } from 'src/app/services/user.service';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-login',\r\n  templateUrl: './login.component.html',\r\n  styleUrls: ['./login.component.css']\r\n})\r\nexport class LoginComponent implements OnInit {\r\n  showPassword = false;\r\n  loginForm!: FormGroup;\r\n  loginError = ''; // To store error messages\r\n  loading: boolean = false;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private router: Router,\r\n    public roleService: RoleService,\r\n    private userService: UserService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    // Initialize form with validation\r\n    this.loginForm = this.fb.group({\r\n      email: ['', [Validators.required, Validators.email]],\r\n      password: ['', [Validators.required, Validators.minLength(6)]],\r\n      remember: [false]\r\n    });\r\n  }\r\n\r\n  onSubmit() {\r\n    // Reset the error message\r\n    this.loginError = '';\r\n    this.loading = true; \r\n    if (this.loginForm.valid) {\r\n      // Capture form values\r\n      const { email, password } = this.loginForm.value;\r\n\r\n      // Call the UserService's login method\r\n      this.userService.login({ email, password }).subscribe({\r\n        next: (res: any) => {\r\n          if (res.success) {\r\n           let  role = res.user.role\r\n           console.log(role);\r\n           this.loading = false;\r\n            // Check role and perform role-based login\r\n            if (role === 'Principal') {\r\n              this.roleService.setRole('Principal');\r\n              console.log('Principal logged in');\r\n            } else if (role === 'Teacher') {\r\n              this.roleService.setRole('teacher');\r\n              console.log('Teacher logged in');\r\n            } else if (role === 'Student') {\r\n              this.roleService.setRole('student');\r\n              console.log('Student logged in');\r\n            } else {\r\n              this.loginError = 'Invalid credentials for the selected role';\r\n              console.log('Invalid credentials');\r\n              return; // Stop further execution\r\n            }\r\n\r\n            // Redirect to the dashboard if role is set\r\n            this.router.navigate(['/dashboard']);\r\n          } else {\r\n            this.loginError = res.message || 'Login failed';\r\n            Swal.fire({\r\n              icon: 'error',\r\n              title: 'Login Failed',\r\n              text: this.loginError,\r\n            });\r\n            this.loading = false;\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('Login failed', error);\r\n          Swal.fire({\r\n            icon: 'error',\r\n            title: 'Login Failed',\r\n            text: error.error.message,\r\n            });\r\n          this.loginError = 'Server error, please try again later.';\r\n          this.loading = false;\r\n        }\r\n      });\r\n    } else {\r\n      // If the form is invalid, display an error message\r\n      this.loginError = 'Please fill in all fields correctly';\r\n      console.log('Form is invalid');\r\n      this.loading = false;\r\n    }\r\n  }\r\n\r\n  // Toggle the visibility of the password\r\n  togglePasswordVisibility() {\r\n    this.showPassword = !this.showPassword;\r\n  }\r\n}\r\n", "<div class=\"container-fluid\">\r\n    <div class=\"row vh-100\">\r\n      <div class=\"col-lg-5 d-flex flex-column justify-content-center align-items-center p-3 bg-white\">\r\n        <div class=\"w-100\" style=\"max-width: 400px;\">\r\n          <h2 class=\"mb-4\"> <img src=\"../../../assets/images/logo.jpeg\"/> GPGC (Swabi)</h2>\r\n          <h1 class=\"mb-4\">Log in</h1>\r\n          <p>Welcome back! Please enter your details.</p>\r\n  \r\n          <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\">\r\n            <div class=\"mb-3\">\r\n              <label for=\"email\" class=\"form-label\">Email</label>\r\n              <input type=\"email\" class=\"form-control\" id=\"email\" formControlName=\"email\" placeholder=\"Enter your email\">\r\n            </div>\r\n  \r\n            <!-- <div class=\"mb-3\">\r\n              <label for=\"role\" class=\"form-label \">Role</label>\r\n              <select class=\"form-control\" formControlName=\"role\">\r\n                <option value=\"Admin\" selected>Admin</option>\r\n                <option value=\"Student\">Student</option>\r\n                <option value=\"Teacher\">Teacher</option>\r\n              </select>\r\n            </div> -->\r\n            <div class=\"mb-3 position-relative\">\r\n                <label for=\"password\" class=\"form-label\">Password(123456)</label>\r\n                <input [type]=\"showPassword ? 'text' : 'password'\" class=\"form-control\" id=\"password\" formControlName=\"password\" placeholder=\"Enter your password\">\r\n                <i class=\"fas viewpassword\" [ngClass]=\"showPassword ? 'fa-eye-slash' : 'fa-eye'\" (click)=\"togglePasswordVisibility()\" ></i>\r\n              </div>\r\n              \r\n            <div class=\"form-check mb-3 d-flex justify-content-between align-items-center\">\r\n              <div>\r\n                <input class=\"form-check-input\" type=\"checkbox\" formControlName=\"remember\" id=\"remember\">\r\n                <label class=\"form-check-label\" for=\"remember\">Remember me</label>\r\n              </div>\r\n              <p class=\"mt-0\" routerLink=\"/auth/forgot-password\"><a class=\"forgot\" style=\"cursor: pointer;\">Forgot password?</a></p>\r\n            </div>\r\n  \r\n            <!-- <div class=\"d-grid gap-2\">\r\n              <button type=\"submit\" class=\"btn submit\">Sign in</button>\r\n            </div> -->\r\n            <div class=\"d-grid gap-2\">\r\n            <button type=\"submit\" class=\"btn submit\" [disabled]=\"loading\">\r\n              <span *ngIf=\"!loading\">Sign in</span>\r\n              <span *ngIf=\"loading\">\r\n                <span class=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\r\n                Signing in...\r\n              </span>\r\n            </button>\r\n            \r\n          </div>\r\n            <p class=\"mt-3\">Don't have an account? <a routerLink=\"/auth/sign-up\">Sign up</a></p>\r\n          </form>\r\n        </div>\r\n      </div>\r\n  \r\n      <div class=\"col-lg-7 d-none d-lg-flex flex-column align-items-start bg-light position-relative\">\r\n        <div class=\"text-start p-5 w-100\">\r\n          <blockquote class=\"blockquote\">\r\n            <h2 class=\"mb-4\">College management system Login page</h2>\r\n            <footer class=\"blockquote-footer\">Name<cite title=\"Source Title\">Owner ~ GPGC SWABI</cite></footer>\r\n          </blockquote>\r\n        </div>\r\n        <div class=\"image-container\">\r\n          <img src=\"../../../assets/images/background.jpeg\" class=\"img-fluid position-absolute\" alt=\"Stats graph\">\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  "], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAInE,OAAOC,IAAI,MAAM,aAAa;;;;;;;;;ICoChBC,EAAA,CAAAC,cAAA,WAAuB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACrCH,EAAA,CAAAC,cAAA,WAAsB;IACpBD,EAAA,CAAAI,SAAA,eAA4F;IAC5FJ,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;ADjCrB,OAAM,MAAOE,cAAc;EAMzBC,YACUC,EAAe,EACfC,MAAc,EACfC,WAAwB,EACvBC,WAAwB;IAHxB,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IACP,KAAAC,WAAW,GAAXA,WAAW;IACV,KAAAC,WAAW,GAAXA,WAAW;IATrB,KAAAC,YAAY,GAAG,KAAK;IAEpB,KAAAC,UAAU,GAAG,EAAE,CAAC,CAAC;IACjB,KAAAC,OAAO,GAAY,KAAK;EAOrB;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,SAAS,GAAG,IAAI,CAACR,EAAE,CAACS,KAAK,CAAC;MAC7BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACnB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAACmB,KAAK,CAAC,CAAC;MACpDE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACrB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAACsB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DC,QAAQ,EAAE,CAAC,KAAK;KACjB,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACV,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,IAAI,CAACE,SAAS,CAACQ,KAAK,EAAE;MACxB;MACA,MAAM;QAAEN,KAAK;QAAEE;MAAQ,CAAE,GAAG,IAAI,CAACJ,SAAS,CAACS,KAAK;MAEhD;MACA,IAAI,CAACd,WAAW,CAACe,KAAK,CAAC;QAAER,KAAK;QAAEE;MAAQ,CAAE,CAAC,CAACO,SAAS,CAAC;QACpDC,IAAI,EAAGC,GAAQ,IAAI;UACjB,IAAIA,GAAG,CAACC,OAAO,EAAE;YAChB,IAAKC,IAAI,GAAGF,GAAG,CAACG,IAAI,CAACD,IAAI;YACzBE,OAAO,CAACC,GAAG,CAACH,IAAI,CAAC;YACjB,IAAI,CAACjB,OAAO,GAAG,KAAK;YACnB;YACA,IAAIiB,IAAI,KAAK,WAAW,EAAE;cACxB,IAAI,CAACrB,WAAW,CAACyB,OAAO,CAAC,WAAW,CAAC;cACrCF,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;aACnC,MAAM,IAAIH,IAAI,KAAK,SAAS,EAAE;cAC7B,IAAI,CAACrB,WAAW,CAACyB,OAAO,CAAC,SAAS,CAAC;cACnCF,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;aACjC,MAAM,IAAIH,IAAI,KAAK,SAAS,EAAE;cAC7B,IAAI,CAACrB,WAAW,CAACyB,OAAO,CAAC,SAAS,CAAC;cACnCF,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;aACjC,MAAM;cACL,IAAI,CAACrB,UAAU,GAAG,2CAA2C;cAC7DoB,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;cAClC,OAAO,CAAC;;YAGV;YACA,IAAI,CAACzB,MAAM,CAAC2B,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;WACrC,MAAM;YACL,IAAI,CAACvB,UAAU,GAAGgB,GAAG,CAACQ,OAAO,IAAI,cAAc;YAC/CrC,IAAI,CAACsC,IAAI,CAAC;cACRC,IAAI,EAAE,OAAO;cACbC,KAAK,EAAE,cAAc;cACrBC,IAAI,EAAE,IAAI,CAAC5B;aACZ,CAAC;YACF,IAAI,CAACC,OAAO,GAAG,KAAK;;QAExB,CAAC;QACD4B,KAAK,EAAGA,KAAK,IAAI;UACfT,OAAO,CAACS,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;UACpC1C,IAAI,CAACsC,IAAI,CAAC;YACRC,IAAI,EAAE,OAAO;YACbC,KAAK,EAAE,cAAc;YACrBC,IAAI,EAAEC,KAAK,CAACA,KAAK,CAACL;WACjB,CAAC;UACJ,IAAI,CAACxB,UAAU,GAAG,uCAAuC;UACzD,IAAI,CAACC,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAACD,UAAU,GAAG,qCAAqC;MACvDoB,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;MAC9B,IAAI,CAACpB,OAAO,GAAG,KAAK;;EAExB;EAEA;EACA6B,wBAAwBA,CAAA;IACtB,IAAI,CAAC/B,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAAC,QAAAgC,CAAA,G;qBAvFUtC,cAAc,EAAAL,EAAA,CAAA4C,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA9C,EAAA,CAAA4C,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAhD,EAAA,CAAA4C,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAlD,EAAA,CAAA4C,iBAAA,CAAAO,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAdhD,cAAc;IAAAiD,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCZ3B5D,EAAA,CAAAC,cAAA,aAA6B;QAIDD,EAAA,CAAAI,SAAA,aAA6C;QAACJ,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACjFH,EAAA,CAAAC,cAAA,YAAiB;QAAAD,EAAA,CAAAE,MAAA,aAAM;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC5BH,EAAA,CAAAC,cAAA,QAAG;QAAAD,EAAA,CAAAE,MAAA,gDAAwC;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAE/CH,EAAA,CAAAC,cAAA,eAAsD;QAAxBD,EAAA,CAAA8D,UAAA,sBAAAC,kDAAA;UAAA,OAAYF,GAAA,CAAAvC,QAAA,EAAU;QAAA,EAAC;QACnDtB,EAAA,CAAAC,cAAA,cAAkB;QACsBD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACnDH,EAAA,CAAAI,SAAA,gBAA2G;QAC7GJ,EAAA,CAAAG,YAAA,EAAM;QAUNH,EAAA,CAAAC,cAAA,eAAoC;QACSD,EAAA,CAAAE,MAAA,wBAAgB;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACjEH,EAAA,CAAAI,SAAA,iBAAmJ;QACnJJ,EAAA,CAAAC,cAAA,aAAuH;QAAtCD,EAAA,CAAA8D,UAAA,mBAAAE,4CAAA;UAAA,OAASH,GAAA,CAAAnB,wBAAA,EAA0B;QAAA,EAAC;QAAE1C,EAAA,CAAAG,YAAA,EAAI;QAG/HH,EAAA,CAAAC,cAAA,eAA+E;QAE3ED,EAAA,CAAAI,SAAA,iBAAyF;QACzFJ,EAAA,CAAAC,cAAA,iBAA+C;QAAAD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAEpEH,EAAA,CAAAC,cAAA,aAAmD;QAA2CD,EAAA,CAAAE,MAAA,wBAAgB;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAMpHH,EAAA,CAAAC,cAAA,eAA0B;QAExBD,EAAA,CAAAiE,UAAA,KAAAC,+BAAA,mBAAqC;QACrClE,EAAA,CAAAiE,UAAA,KAAAE,+BAAA,mBAGO;QACTnE,EAAA,CAAAG,YAAA,EAAS;QAGTH,EAAA,CAAAC,cAAA,aAAgB;QAAAD,EAAA,CAAAE,MAAA,+BAAuB;QAAAF,EAAA,CAAAC,cAAA,aAA8B;QAAAD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAKtFH,EAAA,CAAAC,cAAA,eAAgG;QAGzED,EAAA,CAAAE,MAAA,4CAAoC;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC1DH,EAAA,CAAAC,cAAA,kBAAkC;QAAAD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAC,cAAA,gBAA2B;QAAAD,EAAA,CAAAE,MAAA,0BAAkB;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAG9FH,EAAA,CAAAC,cAAA,eAA6B;QAC3BD,EAAA,CAAAI,SAAA,eAAwG;QAC1GJ,EAAA,CAAAG,YAAA,EAAM;;;QAvDEH,EAAA,CAAAoE,SAAA,IAAuB;QAAvBpE,EAAA,CAAAqE,UAAA,cAAAR,GAAA,CAAA9C,SAAA,CAAuB;QAgBhBf,EAAA,CAAAoE,SAAA,GAA2C;QAA3CpE,EAAA,CAAAqE,UAAA,SAAAR,GAAA,CAAAlD,YAAA,uBAA2C;QACtBX,EAAA,CAAAoE,SAAA,GAAoD;QAApDpE,EAAA,CAAAqE,UAAA,YAAAR,GAAA,CAAAlD,YAAA,6BAAoD;QAe3CX,EAAA,CAAAoE,SAAA,IAAoB;QAApBpE,EAAA,CAAAqE,UAAA,aAAAR,GAAA,CAAAhD,OAAA,CAAoB;QACpDb,EAAA,CAAAoE,SAAA,GAAc;QAAdpE,EAAA,CAAAqE,UAAA,UAAAR,GAAA,CAAAhD,OAAA,CAAc;QACdb,EAAA,CAAAoE,SAAA,GAAa;QAAbpE,EAAA,CAAAqE,UAAA,SAAAR,GAAA,CAAAhD,OAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}