{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, catchError, tap } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nconst USER_KEY = 'user'; // Key for storing the user in localStorage\nconst TOKEN_KEY = 'token'; // Key for storing the auth token\nexport let UserService = /*#__PURE__*/(() => {\n  class UserService {\n    constructor(http) {\n      this.http = http;\n      this.userSubject = new BehaviorSubject(this.getUserFromLocalStorage());\n      this.user$ = this.userSubject.asObservable(); // Public observable for the current user\n      this.apiUrl = environment.apiUrl;\n      this.userRegUrl = `${this.apiUrl}/signup`;\n      this.userLogUrl = `${this.apiUrl}/login`;\n      this.forgotPassUrl = `${this.apiUrl}/forgot-password`;\n      this.getAllUsersByRoleUrl = `${this.apiUrl}/api/users`;\n      this.allUsersUrl = `${this.apiUrl}/allUser`;\n      this.bulkfileuploadUrl = `${this.apiUrl}/bulk-upload`;\n      this.deleteUserUrl = `${this.apiUrl}/deleteUser`;\n    }\n    // Register a new user\n    register(userRegister) {\n      return this.http.post(this.userRegUrl, userRegister).pipe(tap({\n        next: user => {\n          this.setUserInLocalStorage(user); // Optionally store the user on successful registration\n        },\n\n        error: errorResponse => {\n          throw errorResponse;\n        }\n      }));\n    }\n    uploaduser(userRegister) {\n      return this.http.post(this.bulkfileuploadUrl, userRegister).pipe(tap({\n        next: user => {\n          this.setUserInLocalStorage(user);\n        },\n        error: errorResponse => {\n          throw errorResponse;\n        }\n      }));\n    }\n    // Login a user\n    login(userLogin) {\n      return this.http.post(this.userLogUrl, userLogin).pipe(tap({\n        next: user => {\n          this.setUserInLocalStorage(user);\n          this.userSubject.next(user);\n        }\n      }), catchError(error => {\n        throw error;\n      }));\n    }\n    // Forgot password\n    forgotPassword(email) {\n      return this.http.post(this.forgotPassUrl, email).pipe(tap({\n        next: response => {\n          console.log('Password reset email sent successfully');\n        }\n      }), catchError(error => {\n        throw error;\n      }));\n    }\n    // Fetch users by role\n    getUsersByRole(role) {\n      return this.http.get(`${this.getAllUsersByRoleUrl}/${role}`).pipe(tap({\n        next: response => {\n          console.log('Fetched users by role:', role);\n        }\n      }), catchError(error => {\n        throw error;\n      }));\n    }\n    // Fetch all users\n    getAllUsers() {\n      return this.http.get(this.allUsersUrl).pipe(tap({\n        next: users => {\n          console.log('Fetched all users:', users);\n        }\n      }), catchError(error => {\n        throw error;\n      }));\n    }\n    // Logout the user\n    logout() {\n      localStorage.removeItem(USER_KEY);\n      localStorage.removeItem(TOKEN_KEY);\n      this.userSubject.next(null);\n    }\n    // Store user in local storage\n    setUserInLocalStorage(user) {\n      localStorage.setItem(USER_KEY, JSON.stringify(user));\n      if (user.token) {\n        localStorage.setItem(TOKEN_KEY, user.token); // Store JWT token if available\n      }\n    }\n    // Get the user from local storage\n    getUserFromLocalStorage() {\n      const userJson = localStorage.getItem(USER_KEY);\n      if (userJson) return JSON.parse(userJson);\n      return null;\n    }\n    // Get the JWT token from local storage\n    getToken() {\n      return localStorage.getItem(TOKEN_KEY);\n    }\n    // Check if the user is authenticated\n    isAuthenticated() {\n      return !!this.getToken();\n    }\n    // Delete user by ID\n    deleteUser(userId) {\n      return this.http.delete(`${this.deleteUserUrl}/${userId}`).pipe(tap({\n        next: response => {\n          console.log('User deleted successfully');\n        }\n      }), catchError(error => {\n        throw error;\n      }));\n    }\n    // Helper function to add token in headers\n    getAuthHeaders() {\n      const token = this.getToken();\n      return new HttpHeaders().set('Authorization', `Bearer ${token}`);\n    }\n    static #_ = this.ɵfac = function UserService_Factory(t) {\n      return new (t || UserService)(i0.ɵɵinject(i1.HttpClient));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: UserService,\n      factory: UserService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return UserService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}