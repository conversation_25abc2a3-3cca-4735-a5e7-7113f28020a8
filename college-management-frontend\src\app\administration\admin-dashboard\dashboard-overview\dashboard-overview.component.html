<div class="dashboard-overview">
  <!-- Loading State -->
  <div *ngIf="loading" class="loading-container">
    <mat-spinner></mat-spinner>
    <p>Loading dashboard statistics...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !loading" class="error-container">
    <mat-icon color="warn">error</mat-icon>
    <p>{{ error }}</p>
    <button mat-raised-button color="primary" (click)="refreshData()">
      <mat-icon>refresh</mat-icon>
      Retry
    </button>
  </div>

  <!-- Dashboard Content -->
  <div *ngIf="dashboardStats && !loading && !error" class="dashboard-content">
    <!-- Header -->
    <div class="dashboard-header">
      <h1>Principal Dashboard</h1>
      <button mat-icon-button (click)="refreshData()" matTooltip="Refresh Data">
        <mat-icon>refresh</mat-icon>
      </button>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-grid">
      <mat-card class="stat-card students">
        <mat-card-content>
          <div class="stat-content">
            <div class="stat-icon">
              <mat-icon>school</mat-icon>
            </div>
            <div class="stat-info">
              <h3>{{ dashboardStats.totals.students }}</h3>
              <p>Total Students</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card teachers">
        <mat-card-content>
          <div class="stat-content">
            <div class="stat-icon">
              <mat-icon>person</mat-icon>
            </div>
            <div class="stat-info">
              <h3>{{ dashboardStats.totals.teachers }}</h3>
              <p>Total Teachers</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card programs">
        <mat-card-content>
          <div class="stat-content">
            <div class="stat-icon">
              <mat-icon>library_books</mat-icon>
            </div>
            <div class="stat-info">
              <h3>{{ dashboardStats.totals.programs }}</h3>
              <p>Active Programs</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card departments">
        <mat-card-content>
          <div class="stat-content">
            <div class="stat-icon">
              <mat-icon>business</mat-icon>
            </div>
            <div class="stat-info">
              <h3>{{ dashboardStats.totals.departments }}</h3>
              <p>Departments</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card classes">
        <mat-card-content>
          <div class="stat-content">
            <div class="stat-icon">
              <mat-icon>class</mat-icon>
            </div>
            <div class="stat-info">
              <h3>{{ dashboardStats.totals.classes }}</h3>
              <p>Active Classes</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card subjects">
        <mat-card-content>
          <div class="stat-content">
            <div class="stat-icon">
              <mat-icon>subject</mat-icon>
            </div>
            <div class="stat-info">
              <h3>{{ dashboardStats.totals.subjects }}</h3>
              <p>Total Subjects</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

     <!-- Enhanced Charts Section -->
     <div class="charts-section">
      <div class="chart-row">
        <!-- Program Distribution - Enhanced -->
        <mat-card class="chart-card">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>pie_chart</mat-icon>
              Students by Program
            </mat-card-title>
            <mat-card-subtitle>Distribution of students across different programs</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <div class="chart-container" *ngIf="dashboardStats?.programStats && dashboardStats.programStats.length > 0; else noData">
              <div class="chart-wrapper">
                <canvas #programChart></canvas>
                <div class="chart-summary" *ngIf="dashboardStats.programStats">
                  <div class="summary-item" *ngFor="let program of dashboardStats.programStats">
                    <div class="color-indicator" [style.background]="getProgramColor(program.programName)"></div>
                    <div class="program-name">{{ program.programName }}</div>
                    <div class="program-count">{{ program.studentCount }} ({{ getPercentage(program.studentCount, dashboardStats.totals.students) }}%)</div>
                  </div>
                </div>
              </div>
            </div>
            <ng-template #noData>
              <div class="no-data">
                <mat-icon>info</mat-icon>
                <p>No program data available</p>
              </div>
            </ng-template>
          </mat-card-content>
        </mat-card>

        <!-- Attendance Overview - Enhanced -->
        <mat-card class="chart-card attendance-card">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>access_time</mat-icon>
              Attendance Overview (Last 30 Days)
            </mat-card-title>
            <mat-card-subtitle>Daily attendance trends and statistics</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <div class="attendance-container">
              <div class="attendance-summary">
                <div class="attendance-percentage">
                  <div class="percentage-circle" [style.background]="getAttendancePercentageColor()">
                    <div class="percentage-value">
                      <h2>{{ getAttendancePercentage() }}%</h2>
                      <p>Overall Attendance</p>
                    </div>
                  </div>
                  <div class="attendance-breakdown">
                    <div class="breakdown-item" *ngFor="let stat of dashboardStats?.attendanceStats">
                      <div class="status-indicator" [ngClass]="'status-' + stat._id"></div>
                      <span class="status-label">{{ capitalizeFirst(stat._id) }}</span>
                      <span class="status-count">{{ stat.count }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="attendance-trend">
                <div class="chart-container">
                  <canvas #attendanceTrendChart></canvas>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <div class="chart-row">
        <!-- Department Distribution - Enhanced -->
        <mat-card class="chart-card">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>business</mat-icon>
              Students by Department
            </mat-card-title>
            <mat-card-subtitle>Student enrollment across departments</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <div class="chart-container" *ngIf="dashboardStats?.departmentStats && dashboardStats.departmentStats.length > 0; else noData">
              <div class="chart-wrapper">
                <canvas #departmentChart></canvas>
                <div class="chart-legend">
                  <div class="legend-item" *ngFor="let dept of dashboardStats.departmentStats; let i = index">
                    <div class="legend-color" [style.background]="getDepartmentColor(i)"></div>
                    <div class="legend-label">{{ dept.departmentName }}</div>
                  </div>
                </div>
              </div>
            </div>
            <ng-template #noData>
              <div class="no-data">
                <mat-icon>info</mat-icon>
                <p>No department data available</p>
              </div>
            </ng-template>
          </mat-card-content>
        </mat-card>

        <!-- Attendance Comparison - New Chart -->
        <mat-card class="chart-card">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>compare</mat-icon>
              Attendance by Department
            </mat-card-title>
            <mat-card-subtitle>Attendance rates across different departments</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <div class="chart-container" *ngIf="dashboardStats?.departmentAttendance && dashboardStats.departmentAttendance.length > 0; else noData">
              <canvas #attendanceComparisonChart></canvas>
            </div>
            <ng-template #noData>
              <div class="no-data">
                <mat-icon>info</mat-icon>
                <p>No attendance comparison data available</p>
              </div>
            </ng-template>
          </mat-card-content>
        </mat-card>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
      <h2>🚀 Quick Administrative Actions</h2>
      <p>Common tasks you can perform right away</p>
      <div class="action-buttons">
        <button mat-raised-button color="primary" routerLink="/dashboard/admin/students/add-student">
          <mat-icon>person_add</mat-icon>
          Enroll New Student
        </button>
        <button mat-raised-button color="primary" routerLink="/dashboard/admin/teacher/teacher-form">
          <mat-icon>person_add</mat-icon>
          Add New Teacher
        </button>
        <button mat-raised-button color="accent" routerLink="/dashboard/admin/classes/class-form">
          <mat-icon>class</mat-icon>
          Create New Class
        </button>
        <button mat-raised-button color="accent" routerLink="/dashboard/admin/department/department-form">
          <mat-icon>business</mat-icon>
          Add New Department
        </button>
      </div>
    </div>
  </div>
</div>

