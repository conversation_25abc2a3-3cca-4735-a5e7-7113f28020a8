{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { AdminDashboardRoutingModule } from './admin-dashboard-routing.module';\nimport { MaterialModule } from 'src/app/material';\nimport * as i0 from \"@angular/core\";\nexport let AdminDashboardModule = /*#__PURE__*/(() => {\n  class AdminDashboardModule {\n    static #_ = this.ɵfac = function AdminDashboardModule_Factory(t) {\n      return new (t || AdminDashboardModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AdminDashboardModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, AdminDashboardRoutingModule, MaterialModule]\n    });\n  }\n  return AdminDashboardModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}