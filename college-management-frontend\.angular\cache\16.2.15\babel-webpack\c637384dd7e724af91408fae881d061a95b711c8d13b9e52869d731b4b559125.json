{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/user.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common/http\";\nimport * as i4 from \"src/app/services/program.service\";\nimport * as i5 from \"src/app/services/department.service\";\nimport * as i6 from \"src/app/services/classes.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"@angular/material/core\";\nimport * as i10 from \"@angular/material/button\";\nimport * as i11 from \"@angular/material/card\";\nimport * as i12 from \"@angular/material/icon\";\nimport * as i13 from \"@angular/material/input\";\nimport * as i14 from \"@angular/material/form-field\";\nimport * as i15 from \"@angular/material/progress-spinner\";\nimport * as i16 from \"@angular/material/select\";\nimport * as i17 from \"@angular/material/tooltip\";\nfunction AdminStudentListComponent_mat_option_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 69);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const program_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", program_r9._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", program_r9.name, \" \");\n  }\n}\nfunction AdminStudentListComponent_mat_option_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 69);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dept_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", dept_r10._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", dept_r10.name, \" \");\n  }\n}\nfunction AdminStudentListComponent_mat_option_88_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 69);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const cls_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", cls_r11._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", cls_r11.className, \"-\", cls_r11.section, \" \");\n  }\n}\nfunction AdminStudentListComponent_mat_icon_124_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"cloud_upload\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdminStudentListComponent_mat_spinner_125_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 70);\n  }\n}\nfunction AdminStudentListComponent_tr_157_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 71)(1, \"td\", 54)(2, \"div\", 72)(3, \"div\", 73);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"small\", 74);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 75)(8, \"small\", 47);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(10, \"td\", 55)(11, \"div\", 76)(12, \"span\", 77);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"small\", 78);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"td\", 56)(17, \"div\", 79)(18, \"span\", 80);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"small\", 81);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"td\", 57)(23, \"span\", 82);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"td\", 58)(26, \"span\", 83);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"td\", 59)(29, \"div\", 84)(30, \"button\", 85);\n    i0.ɵɵlistener(\"click\", function AdminStudentListComponent_tr_157_Template_button_click_30_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r15);\n      const student_r12 = restoredCtx.$implicit;\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.viewStudent(student_r12));\n    });\n    i0.ɵɵelementStart(31, \"mat-icon\");\n    i0.ɵɵtext(32, \"visibility\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function AdminStudentListComponent_tr_157_Template_button_click_33_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r15);\n      const student_r12 = restoredCtx.$implicit;\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.editStudent(student_r12));\n    });\n    i0.ɵɵelementStart(34, \"mat-icon\");\n    i0.ɵɵtext(35, \"edit\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const student_r12 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(student_r12.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(student_r12.email);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" Roll: \", (student_r12 == null ? null : student_r12.rollNo) || \"N/A\", \" | \", (student_r12 == null ? null : student_r12.program == null ? null : student_r12.program.name) || \"N/A\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((student_r12 == null ? null : student_r12.rollNo) || \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((student_r12 == null ? null : student_r12.regNo) || \"N/A\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((student_r12 == null ? null : student_r12.program == null ? null : student_r12.program.name) || \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((student_r12 == null ? null : student_r12.department == null ? null : student_r12.department.name) || \"N/A\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r6.getClassName(student_r12), \"-\", ctx_r6.getClassSection(student_r12), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((student_r12 == null ? null : student_r12.contact) || \"N/A\");\n  }\n}\nfunction AdminStudentListComponent_div_158_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 87);\n    i0.ɵɵelement(1, \"mat-spinner\", 88);\n    i0.ɵɵelementStart(2, \"p\", 89);\n    i0.ɵɵtext(3, \"Loading students...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AdminStudentListComponent_div_159_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 90)(1, \"mat-icon\", 91);\n    i0.ɵɵtext(2, \"school\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No Students Found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Try adjusting your filters or add new students to get started.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 92)(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"person_add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Add First Student \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class AdminStudentListComponent {\n  constructor(userService, router, http, programService, departmentService, classesService) {\n    this.userService = userService;\n    this.router = router;\n    this.http = http;\n    this.programService = programService;\n    this.departmentService = departmentService;\n    this.classesService = classesService;\n    this.searchQuery = '';\n    this.currentPage = 1;\n    this.itemsPerPage = 10;\n    this.totalPages = 1;\n    this.totalRecords = 0;\n    this.loading = false;\n    this.Math = Math; // Make Math available in template\n    // Data arrays\n    this.programs = [];\n    this.departments = [];\n    this.classes = [];\n    this.students = [];\n    this.filteredDepartments = [];\n    this.filteredClasses = [];\n    // Selected filters\n    this.selectedProgram = '';\n    this.selectedDepartment = '';\n    this.selectedClass = '';\n    this.selectedSection = '';\n    this.selectedFile = null;\n    this.selectedFileName = '';\n    this.responseMessage = '';\n    this.uploading = false;\n    // Statistics\n    this.totalStudents = 0;\n    this.activeStudents = 0;\n  }\n  ngOnInit() {\n    this.loadInitialData();\n    this.setupFormSubscriptions();\n    this.fetchStudents();\n    this.calculateStatistics();\n  }\n  calculateStatistics() {\n    // This would typically come from a dashboard API\n    // For now, we'll calculate from the current students array\n    this.totalStudents = this.students.length;\n    this.activeStudents = this.students.filter(student => student.isActive !== false).length;\n  }\n  loadInitialData() {\n    this.loadPrograms();\n    this.loadDepartments();\n    this.loadClasses();\n  }\n  loadPrograms() {\n    this.programService.getAllPrograms(true).subscribe({\n      next: response => {\n        if (response.success) {\n          this.programs = response.programs;\n        }\n      },\n      error: error => {\n        console.error('Error loading programs:', error);\n      }\n    });\n  }\n  loadDepartments() {\n    this.departmentService.getAllDepartments().subscribe({\n      next: response => {\n        if (response.success) {\n          this.departments = response.departments;\n          this.filteredDepartments = this.departments;\n        }\n      },\n      error: error => {\n        console.error('Error loading departments:', error);\n      }\n    });\n  }\n  loadClasses() {\n    this.classesService.getAllClasses().subscribe({\n      next: response => {\n        if (response.success) {\n          this.classes = response.classes;\n          this.filteredClasses = this.classes;\n        }\n      },\n      error: error => {\n        console.error('Error loading classes:', error);\n      }\n    });\n  }\n  setupFormSubscriptions() {\n    // This would be implemented if using reactive forms\n    // For now, we'll handle filtering in the filter methods\n  }\n  onFileSelected(event) {\n    this.selectedFile = event.target.files[0];\n    this.selectedFileName = this.selectedFile ? this.selectedFile.name : '';\n  }\n  // Add student\n  onUpload() {\n    if (!this.selectedFile) {\n      alert('Please select a file before uploading');\n      return;\n    }\n    if (this.selectedFile) {\n      const formData = new FormData();\n      formData.append('file', this.selectedFile, this.selectedFile.name);\n      this.http.post('http://localhost:5007/api/v1/bulk-signup', formData).subscribe({\n        next: response => {\n          this.responseMessage = response.success ? `Success: ${response.message}` : `Error: ${response.message}`;\n          this.fetchStudents();\n        },\n        error: error => {\n          console.error('Error uploading the file', error);\n          this.responseMessage = 'Error uploading the file. Please try again.';\n        }\n      });\n    }\n  }\n  fetchStudents() {\n    this.loading = true;\n    // Build filters for API call\n    const filters = {\n      role: 'Student',\n      page: this.currentPage,\n      limit: this.itemsPerPage\n    };\n    if (this.selectedProgram) filters.program = this.selectedProgram;\n    if (this.selectedDepartment) filters.department = this.selectedDepartment;\n    if (this.selectedClass) filters.classId = this.selectedClass;\n    if (this.searchQuery) filters.search = this.searchQuery;\n    this.userService.getAllUsers(filters).subscribe({\n      next: response => {\n        console.log(\"Students response:\", response);\n        if (response.success) {\n          this.students = response.users;\n          this.totalRecords = response.pagination?.totalRecords || response.users.length;\n          this.totalPages = response.pagination?.totalPages || Math.ceil(this.totalRecords / this.itemsPerPage);\n          this.currentPage = response.pagination?.currentPage || this.currentPage;\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error fetching students:', error);\n        this.loading = false;\n      }\n    });\n  }\n  // Apply filters and refresh data\n  applyFilters() {\n    this.currentPage = 1;\n    this.fetchStudents();\n  }\n  // Filter departments when program changes\n  onProgramChange() {\n    if (this.selectedProgram) {\n      this.filteredDepartments = this.departments.filter(dept => dept.program._id === this.selectedProgram);\n      this.selectedDepartment = '';\n      this.selectedClass = '';\n      this.filteredClasses = [];\n    } else {\n      this.filteredDepartments = this.departments;\n      this.selectedDepartment = '';\n      this.selectedClass = '';\n      this.filteredClasses = this.classes;\n    }\n    this.applyFilters();\n  }\n  // Filter classes when department changes\n  onDepartmentChange() {\n    if (this.selectedDepartment) {\n      this.filteredClasses = this.classes.filter(cls => cls.department._id === this.selectedDepartment);\n      this.selectedClass = '';\n    } else {\n      this.filteredClasses = this.selectedProgram ? this.classes.filter(cls => cls.program._id === this.selectedProgram) : this.classes;\n      this.selectedClass = '';\n    }\n    this.applyFilters();\n  }\n  onClassChange() {\n    this.applyFilters();\n  }\n  onSearch() {\n    this.applyFilters();\n  }\n  // Reset filters\n  resetFilters() {\n    this.selectedProgram = '';\n    this.selectedDepartment = '';\n    this.selectedClass = '';\n    this.selectedSection = '';\n    this.searchQuery = '';\n    this.filteredDepartments = this.departments;\n    this.filteredClasses = this.classes;\n    this.currentPage = 1;\n    this.fetchStudents();\n  }\n  addnewstudent() {\n    this.router.navigate(['/dashboard/admin/students/add-student']);\n  }\n  // Pagination methods\n  nextPage() {\n    if (this.currentPage < this.totalPages) {\n      this.currentPage++;\n      this.fetchStudents();\n    }\n  }\n  previousPage() {\n    if (this.currentPage > 1) {\n      this.currentPage--;\n      this.fetchStudents();\n    }\n  }\n  goToPage(page) {\n    if (page >= 1 && page <= this.totalPages) {\n      this.currentPage = page;\n      this.fetchStudents();\n    }\n  }\n  // View student details\n  viewStudent(student) {\n    // Navigate to student details page\n    this.router.navigate(['/dashboard/admin/students/view', student._id]);\n  }\n  // Edit student\n  editStudent(student) {\n    this.router.navigate(['/dashboard/admin/students/edit', student._id]);\n  }\n  // Delete student\n  deleteStudent(student) {\n    if (confirm(`Are you sure you want to delete ${student.name}?`)) {\n      console.log('Delete student:', student);\n      // Add actual delete logic here\n      // this.userService.deleteUser(student._id).subscribe(...)\n    }\n  }\n  // Helper methods for template\n  getClassName(student) {\n    if (student?.classId && typeof student.classId === 'object') {\n      return student.classId.className || 'N/A';\n    }\n    return 'N/A';\n  }\n  getClassSection(student) {\n    if (student?.classId && typeof student.classId === 'object') {\n      return student.classId.section || '';\n    }\n    return '';\n  }\n  static #_ = this.ɵfac = function AdminStudentListComponent_Factory(t) {\n    return new (t || AdminStudentListComponent)(i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.HttpClient), i0.ɵɵdirectiveInject(i4.ProgramService), i0.ɵɵdirectiveInject(i5.DepartmentService), i0.ɵɵdirectiveInject(i6.ClassesService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AdminStudentListComponent,\n    selectors: [[\"app-admin-student-list\"]],\n    decls: 173,\n    vars: 29,\n    consts: [[1, \"student-list-container\"], [1, \"container-fluid\"], [1, \"page-header\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"header-content\"], [1, \"page-title\"], [1, \"title-icon\"], [1, \"page-subtitle\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/dashboard/admin/students/add-student\", 1, \"add-btn\"], [1, \"stats-section\", \"mb-3\"], [1, \"row\", \"g-2\"], [1, \"col-6\", \"col-md-3\"], [1, \"stat-card\", \"compact-stat\"], [1, \"stat-icon\"], [1, \"stat-info\"], [1, \"stat-number\"], [1, \"stat-label\"], [1, \"filters-card\"], [1, \"compact-filters\"], [1, \"col-12\", \"col-md-4\"], [\"appearance\", \"outline\", 1, \"compact-field\"], [\"matInput\", \"\", \"placeholder\", \"Name, Roll No...\", 3, \"ngModel\", \"ngModelChange\", \"keyup.enter\"], [\"matSuffix\", \"\"], [1, \"col-6\", \"col-md-2\"], [3, \"ngModel\", \"ngModelChange\", \"selectionChange\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"ngModel\", \"disabled\", \"ngModelChange\", \"selectionChange\"], [1, \"col-6\", \"col-md-2\", \"d-flex\", \"align-items-center\", \"gap-1\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"compact-btn\", 3, \"click\"], [1, \"d-none\", \"d-lg-inline\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Reset\", 3, \"click\"], [1, \"upload-card\"], [1, \"upload-section\"], [1, \"row\", \"align-items-end\"], [1, \"col-md-8\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"readonly\", \"\", \"placeholder\", \"No file selected\", 3, \"value\"], [\"type\", \"file\", \"accept\", \".xlsx,.xls\", 2, \"display\", \"none\", 3, \"change\"], [\"fileInput\", \"\"], [1, \"col-md-4\", \"d-flex\", \"gap-2\"], [\"mat-raised-button\", \"\", 1, \"select-file-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", 1, \"upload-btn\", 3, \"disabled\", \"click\"], [4, \"ngIf\"], [\"diameter\", \"20\", 4, \"ngIf\"], [1, \"upload-info\", \"mt-2\"], [1, \"text-muted\"], [1, \"info-icon\"], [1, \"table-card\"], [1, \"table-container\"], [1, \"table-responsive\"], [1, \"compact-table\"], [1, \"table-header\"], [1, \"col-student\"], [1, \"col-ids\", \"d-none\", \"d-md-table-cell\"], [1, \"col-program\", \"d-none\", \"d-lg-table-cell\"], [1, \"col-class\"], [1, \"col-contact\", \"d-none\", \"d-sm-table-cell\"], [1, \"col-actions\"], [\"class\", \"student-row\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"pagination-container\"], [1, \"pagination-info\"], [1, \"pagination-controls\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Previous Page\", 3, \"disabled\", \"click\"], [1, \"page-info\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Next Page\", 3, \"disabled\", \"click\"], [3, \"value\"], [\"diameter\", \"20\"], [1, \"student-row\"], [1, \"student-info\"], [1, \"student-name\"], [1, \"student-email\"], [1, \"mobile-info\", \"d-md-none\"], [1, \"ids-info\"], [1, \"roll-badge\"], [1, \"reg-number\"], [1, \"program-info\"], [1, \"program-badge\"], [1, \"department-name\"], [1, \"class-badge\"], [1, \"contact-info\"], [1, \"action-buttons\"], [\"mat-icon-button\", \"\", \"color\", \"primary\", \"matTooltip\", \"View\", 1, \"action-btn\", \"compact-btn\", 3, \"click\"], [\"mat-icon-button\", \"\", \"color\", \"accent\", \"matTooltip\", \"Edit\", 1, \"action-btn\", \"compact-btn\", 3, \"click\"], [1, \"loading-container\"], [\"diameter\", \"50\"], [1, \"loading-text\"], [1, \"empty-state\"], [1, \"empty-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/dashboard/admin/students/add-student\"]],\n    template: function AdminStudentListComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r17 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"h1\", 5)(6, \"mat-icon\", 6);\n        i0.ɵɵtext(7, \"school\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(8, \" Student Management \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"p\", 7);\n        i0.ɵɵtext(10, \"Manage and view all student records\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"div\", 8)(12, \"button\", 9)(13, \"mat-icon\");\n        i0.ɵɵtext(14, \"person_add\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(15, \" Add New Student \");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(16, \"div\", 10)(17, \"div\", 11)(18, \"div\", 12)(19, \"div\", 13)(20, \"mat-icon\", 14);\n        i0.ɵɵtext(21, \"groups\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(22, \"div\", 15)(23, \"span\", 16);\n        i0.ɵɵtext(24);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"span\", 17);\n        i0.ɵɵtext(26, \"Total\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(27, \"div\", 12)(28, \"div\", 13)(29, \"mat-icon\", 14);\n        i0.ɵɵtext(30, \"person_check\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(31, \"div\", 15)(32, \"span\", 16);\n        i0.ɵɵtext(33);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(34, \"span\", 17);\n        i0.ɵɵtext(35, \"Active\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(36, \"div\", 12)(37, \"div\", 13)(38, \"mat-icon\", 14);\n        i0.ɵɵtext(39, \"category\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(40, \"div\", 15)(41, \"span\", 16);\n        i0.ɵɵtext(42);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(43, \"span\", 17);\n        i0.ɵɵtext(44, \"Programs\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(45, \"div\", 12)(46, \"div\", 13)(47, \"mat-icon\", 14);\n        i0.ɵɵtext(48, \"business\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(49, \"div\", 15)(50, \"span\", 16);\n        i0.ɵɵtext(51);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(52, \"span\", 17);\n        i0.ɵɵtext(53, \"Departments\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(54, \"mat-card\", 18)(55, \"mat-card-content\")(56, \"div\", 19)(57, \"div\", 11)(58, \"div\", 20)(59, \"mat-form-field\", 21)(60, \"mat-label\");\n        i0.ɵɵtext(61, \"Search Students\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(62, \"input\", 22);\n        i0.ɵɵlistener(\"ngModelChange\", function AdminStudentListComponent_Template_input_ngModelChange_62_listener($event) {\n          return ctx.searchQuery = $event;\n        })(\"keyup.enter\", function AdminStudentListComponent_Template_input_keyup_enter_62_listener() {\n          return ctx.onSearch();\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(63, \"mat-icon\", 23);\n        i0.ɵɵtext(64, \"search\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(65, \"div\", 24)(66, \"mat-form-field\", 21)(67, \"mat-label\");\n        i0.ɵɵtext(68, \"Program\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(69, \"mat-select\", 25);\n        i0.ɵɵlistener(\"ngModelChange\", function AdminStudentListComponent_Template_mat_select_ngModelChange_69_listener($event) {\n          return ctx.selectedProgram = $event;\n        })(\"selectionChange\", function AdminStudentListComponent_Template_mat_select_selectionChange_69_listener() {\n          return ctx.onProgramChange();\n        });\n        i0.ɵɵelementStart(70, \"mat-option\", 26);\n        i0.ɵɵtext(71, \"All\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(72, AdminStudentListComponent_mat_option_72_Template, 2, 2, \"mat-option\", 27);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(73, \"div\", 24)(74, \"mat-form-field\", 21)(75, \"mat-label\");\n        i0.ɵɵtext(76, \"Department\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(77, \"mat-select\", 28);\n        i0.ɵɵlistener(\"ngModelChange\", function AdminStudentListComponent_Template_mat_select_ngModelChange_77_listener($event) {\n          return ctx.selectedDepartment = $event;\n        })(\"selectionChange\", function AdminStudentListComponent_Template_mat_select_selectionChange_77_listener() {\n          return ctx.onDepartmentChange();\n        });\n        i0.ɵɵelementStart(78, \"mat-option\", 26);\n        i0.ɵɵtext(79, \"All\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(80, AdminStudentListComponent_mat_option_80_Template, 2, 2, \"mat-option\", 27);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(81, \"div\", 24)(82, \"mat-form-field\", 21)(83, \"mat-label\");\n        i0.ɵɵtext(84, \"Class\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(85, \"mat-select\", 28);\n        i0.ɵɵlistener(\"ngModelChange\", function AdminStudentListComponent_Template_mat_select_ngModelChange_85_listener($event) {\n          return ctx.selectedClass = $event;\n        })(\"selectionChange\", function AdminStudentListComponent_Template_mat_select_selectionChange_85_listener() {\n          return ctx.onClassChange();\n        });\n        i0.ɵɵelementStart(86, \"mat-option\", 26);\n        i0.ɵɵtext(87, \"All\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(88, AdminStudentListComponent_mat_option_88_Template, 2, 3, \"mat-option\", 27);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(89, \"div\", 29)(90, \"button\", 30);\n        i0.ɵɵlistener(\"click\", function AdminStudentListComponent_Template_button_click_90_listener() {\n          return ctx.applyFilters();\n        });\n        i0.ɵɵelementStart(91, \"mat-icon\");\n        i0.ɵɵtext(92, \"filter_list\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(93, \"span\", 31);\n        i0.ɵɵtext(94, \"Apply\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(95, \"button\", 32);\n        i0.ɵɵlistener(\"click\", function AdminStudentListComponent_Template_button_click_95_listener() {\n          return ctx.resetFilters();\n        });\n        i0.ɵɵelementStart(96, \"mat-icon\");\n        i0.ɵɵtext(97, \"refresh\");\n        i0.ɵɵelementEnd()()()()()()();\n        i0.ɵɵelementStart(98, \"mat-card\", 33)(99, \"mat-card-header\")(100, \"mat-card-title\")(101, \"mat-icon\");\n        i0.ɵɵtext(102, \"upload_file\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(103, \" Bulk Student Upload \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(104, \"mat-card-subtitle\");\n        i0.ɵɵtext(105, \"Upload multiple students using Excel file\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(106, \"mat-card-content\")(107, \"div\", 34)(108, \"div\", 35)(109, \"div\", 36)(110, \"mat-form-field\", 37)(111, \"mat-label\");\n        i0.ɵɵtext(112, \"Select Excel File\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(113, \"input\", 38);\n        i0.ɵɵelementStart(114, \"mat-icon\", 23);\n        i0.ɵɵtext(115, \"attach_file\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(116, \"input\", 39, 40);\n        i0.ɵɵlistener(\"change\", function AdminStudentListComponent_Template_input_change_116_listener($event) {\n          return ctx.onFileSelected($event);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(118, \"div\", 41)(119, \"button\", 42);\n        i0.ɵɵlistener(\"click\", function AdminStudentListComponent_Template_button_click_119_listener() {\n          i0.ɵɵrestoreView(_r17);\n          const _r3 = i0.ɵɵreference(117);\n          return i0.ɵɵresetView(_r3.click());\n        });\n        i0.ɵɵelementStart(120, \"mat-icon\");\n        i0.ɵɵtext(121, \"folder_open\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(122, \" Select File \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(123, \"button\", 43);\n        i0.ɵɵlistener(\"click\", function AdminStudentListComponent_Template_button_click_123_listener() {\n          return ctx.onUpload();\n        });\n        i0.ɵɵtemplate(124, AdminStudentListComponent_mat_icon_124_Template, 2, 0, \"mat-icon\", 44);\n        i0.ɵɵtemplate(125, AdminStudentListComponent_mat_spinner_125_Template, 1, 0, \"mat-spinner\", 45);\n        i0.ɵɵtext(126);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(127, \"div\", 46)(128, \"small\", 47)(129, \"mat-icon\", 48);\n        i0.ɵɵtext(130, \"info\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(131, \" Supported formats: .xlsx, .xls. Maximum file size: 10MB \");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(132, \"mat-card\", 49)(133, \"mat-card-header\")(134, \"mat-card-title\")(135, \"mat-icon\");\n        i0.ɵɵtext(136, \"list\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(137);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(138, \"mat-card-content\")(139, \"div\", 50)(140, \"div\", 51)(141, \"table\", 52)(142, \"thead\")(143, \"tr\", 53)(144, \"th\", 54);\n        i0.ɵɵtext(145, \"Student\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(146, \"th\", 55);\n        i0.ɵɵtext(147, \"IDs\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(148, \"th\", 56);\n        i0.ɵɵtext(149, \"Program\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(150, \"th\", 57);\n        i0.ɵɵtext(151, \"Class\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(152, \"th\", 58);\n        i0.ɵɵtext(153, \"Contact\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(154, \"th\", 59);\n        i0.ɵɵtext(155, \"Actions\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(156, \"tbody\");\n        i0.ɵɵtemplate(157, AdminStudentListComponent_tr_157_Template, 36, 11, \"tr\", 60);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(158, AdminStudentListComponent_div_158_Template, 4, 0, \"div\", 61);\n        i0.ɵɵtemplate(159, AdminStudentListComponent_div_159_Template, 11, 0, \"div\", 62);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(160, \"div\", 63)(161, \"div\", 64)(162, \"span\", 47);\n        i0.ɵɵtext(163);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(164, \"div\", 65)(165, \"button\", 66);\n        i0.ɵɵlistener(\"click\", function AdminStudentListComponent_Template_button_click_165_listener() {\n          return ctx.previousPage();\n        });\n        i0.ɵɵelementStart(166, \"mat-icon\");\n        i0.ɵɵtext(167, \"chevron_left\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(168, \"span\", 67);\n        i0.ɵɵtext(169);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(170, \"button\", 68);\n        i0.ɵɵlistener(\"click\", function AdminStudentListComponent_Template_button_click_170_listener() {\n          return ctx.nextPage();\n        });\n        i0.ɵɵelementStart(171, \"mat-icon\");\n        i0.ɵɵtext(172, \"chevron_right\");\n        i0.ɵɵelementEnd()()()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(24);\n        i0.ɵɵtextInterpolate(ctx.totalStudents);\n        i0.ɵɵadvance(9);\n        i0.ɵɵtextInterpolate(ctx.activeStudents);\n        i0.ɵɵadvance(9);\n        i0.ɵɵtextInterpolate(ctx.programs.length);\n        i0.ɵɵadvance(9);\n        i0.ɵɵtextInterpolate(ctx.departments.length);\n        i0.ɵɵadvance(11);\n        i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngModel\", ctx.selectedProgram);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.programs);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngModel\", ctx.selectedDepartment)(\"disabled\", !ctx.selectedProgram);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.filteredDepartments);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngModel\", ctx.selectedClass)(\"disabled\", !ctx.selectedDepartment);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.filteredClasses);\n        i0.ɵɵadvance(25);\n        i0.ɵɵproperty(\"value\", ctx.selectedFileName);\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"disabled\", !ctx.selectedFile || ctx.uploading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.uploading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.uploading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate1(\" \", ctx.uploading ? \"Uploading...\" : \"Upload\", \" \");\n        i0.ɵɵadvance(11);\n        i0.ɵɵtextInterpolate1(\" Students (\", ctx.totalRecords, \") \");\n        i0.ɵɵadvance(20);\n        i0.ɵɵproperty(\"ngForOf\", ctx.students);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.students.length === 0);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate3(\" Showing \", (ctx.currentPage - 1) * ctx.itemsPerPage + 1, \" to \", ctx.Math.min(ctx.currentPage * ctx.itemsPerPage, ctx.totalRecords), \" of \", ctx.totalRecords, \" students \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"disabled\", ctx.currentPage === 1);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate2(\"Page \", ctx.currentPage, \" of \", ctx.totalPages, \"\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"disabled\", ctx.currentPage === ctx.totalPages || ctx.totalPages === 0);\n      }\n    },\n    dependencies: [i7.NgForOf, i7.NgIf, i2.RouterLink, i8.DefaultValueAccessor, i8.NgControlStatus, i8.NgModel, i9.MatOption, i10.MatButton, i10.MatIconButton, i11.MatCard, i11.MatCardContent, i11.MatCardHeader, i11.MatCardSubtitle, i11.MatCardTitle, i12.MatIcon, i13.MatInput, i14.MatFormField, i14.MatLabel, i14.MatSuffix, i15.MatProgressSpinner, i16.MatSelect, i17.MatTooltip],\n    styles: [\".student-list-container[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  min-height: 100vh;\\n  padding: 15px;\\n}\\n\\n\\n\\n.page-header[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  padding: 15px 0;\\n}\\n\\n.page-title[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-size: 1.8rem;\\n  font-weight: 600;\\n  margin: 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n\\n.title-icon[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  color: #3498db;\\n}\\n\\n.page-subtitle[_ngcontent-%COMP%] {\\n  color: #7f8c8d;\\n  margin: 5px 0 0 0;\\n  font-size: 0.9rem;\\n}\\n\\n.header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.add-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);\\n  border: none;\\n  box-shadow: 0 4px 6px rgba(52, 152, 219, 0.3);\\n  transition: all 0.3s ease;\\n}\\n\\n.add-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 6px 10px rgba(52, 152, 219, 0.4);\\n}\\n\\n\\n\\n.stats-section[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.stat-card.compact-stat[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 8px;\\n  padding: 15px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  border-left: 3px solid #3498db;\\n  transition: transform 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  min-height: 70px;\\n}\\n\\n.stat-card.compact-stat[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\\n}\\n\\n.stat-card.compact-stat[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  color: #3498db;\\n  width: auto;\\n  height: auto;\\n  background: none;\\n}\\n\\n.stat-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 2px;\\n}\\n\\n.stat-number[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  color: #2c3e50;\\n  line-height: 1;\\n}\\n\\n.stat-label[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #7f8c8d;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n\\n\\n\\n.filters-card[_ngcontent-%COMP%], .upload-card[_ngcontent-%COMP%], .table-card[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  border: none;\\n}\\n\\n.filters-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%], .upload-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%], .table-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  border-radius: 8px 8px 0 0;\\n  padding: 15px;\\n}\\n\\n.filters-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%], .upload-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%], .table-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  color: #2c3e50;\\n  font-weight: 600;\\n  font-size: 1.1rem;\\n}\\n\\n.filters-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%], .upload-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  padding: 15px !important;\\n}\\n\\n.table-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  padding: 0 !important;\\n}\\n\\n\\n\\n.compact-filters[_ngcontent-%COMP%] {\\n  margin: 0;\\n}\\n\\n.compact-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.compact-field[_ngcontent-%COMP%]   .mat-form-field-wrapper[_ngcontent-%COMP%] {\\n  padding-bottom: 0;\\n}\\n\\n.compact-field[_ngcontent-%COMP%]   .mat-form-field-infix[_ngcontent-%COMP%] {\\n  padding: 8px 0;\\n  border-top: none;\\n}\\n\\n.compact-field[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n\\n.compact-btn[_ngcontent-%COMP%] {\\n  min-width: auto;\\n  padding: 6px 12px;\\n  font-size: 0.875rem;\\n}\\n\\n\\n\\n.compact-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: collapse;\\n  font-size: 0.875rem;\\n}\\n\\n.table-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);\\n  color: white;\\n}\\n\\n.table-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  padding: 12px 8px;\\n  text-align: left;\\n  font-weight: 600;\\n  font-size: 0.8rem;\\n  border: none;\\n}\\n\\n.student-row[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #ecf0f1;\\n  transition: background-color 0.2s ease;\\n}\\n\\n.student-row[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n\\n.student-row[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 10px 8px;\\n  vertical-align: middle;\\n}\\n\\n\\n\\n.col-student[_ngcontent-%COMP%] {\\n  min-width: 180px;\\n}\\n\\n.col-ids[_ngcontent-%COMP%] {\\n  width: 120px;\\n}\\n\\n.col-program[_ngcontent-%COMP%] {\\n  width: 140px;\\n}\\n\\n.col-class[_ngcontent-%COMP%] {\\n  width: 100px;\\n}\\n\\n.col-contact[_ngcontent-%COMP%] {\\n  width: 120px;\\n}\\n\\n.col-actions[_ngcontent-%COMP%] {\\n  width: 80px;\\n  text-align: center;\\n}\\n\\n\\n\\n.student-info[_ngcontent-%COMP%] {\\n  line-height: 1.3;\\n}\\n\\n.student-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2c3e50;\\n  font-size: 0.9rem;\\n}\\n\\n.student-email[_ngcontent-%COMP%] {\\n  color: #7f8c8d;\\n  font-size: 0.75rem;\\n}\\n\\n.mobile-info[_ngcontent-%COMP%] {\\n  margin-top: 3px;\\n}\\n\\n\\n\\n.ids-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 3px;\\n}\\n\\n.roll-badge[_ngcontent-%COMP%] {\\n  background: #3498db;\\n  color: white;\\n  padding: 2px 6px;\\n  border-radius: 10px;\\n  font-size: 0.7rem;\\n  font-weight: 600;\\n  align-self: flex-start;\\n}\\n\\n.reg-number[_ngcontent-%COMP%] {\\n  color: #7f8c8d;\\n  font-size: 0.7rem;\\n  font-family: 'Courier New', monospace;\\n}\\n\\n\\n\\n.program-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 3px;\\n}\\n\\n.program-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);\\n  color: white;\\n  padding: 2px 8px;\\n  border-radius: 10px;\\n  font-size: 0.7rem;\\n  font-weight: 600;\\n  align-self: flex-start;\\n}\\n\\n.department-name[_ngcontent-%COMP%] {\\n  color: #27ae60;\\n  font-size: 0.7rem;\\n  font-weight: 600;\\n}\\n\\n\\n\\n.class-badge[_ngcontent-%COMP%] {\\n  background: #f39c12;\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n}\\n\\n\\n\\n.contact-info[_ngcontent-%COMP%] {\\n  font-family: 'Courier New', monospace;\\n  color: #34495e;\\n  font-size: 0.8rem;\\n}\\n\\n\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 4px;\\n  justify-content: center;\\n}\\n\\n.action-btn.compact-btn[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  min-width: 32px;\\n  padding: 0;\\n}\\n\\n.action-btn.compact-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n\\n\\n\\n.loading-state[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n  text-align: center;\\n}\\n\\n.loading-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n  color: #7f8c8d;\\n}\\n\\n.empty-state[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  margin-bottom: 10px;\\n}\\n\\n.empty-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  color: #bdc3c7;\\n  margin-bottom: 15px;\\n}\\n\\n\\n\\n@media (max-width: 1200px) {\\n  .student-list-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .page-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 15px;\\n  }\\n\\n  .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  .header-actions[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: space-between;\\n  }\\n\\n  .stat-card.compact-stat[_ngcontent-%COMP%] {\\n    padding: 12px;\\n    min-height: 60px;\\n  }\\n\\n  .stat-number[_ngcontent-%COMP%] {\\n    font-size: 1.25rem;\\n  }\\n\\n  .stat-label[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n  }\\n\\n  .compact-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%] {\\n    margin: 0;\\n  }\\n\\n  .compact-filters[_ngcontent-%COMP%]   .col-6[_ngcontent-%COMP%], .compact-filters[_ngcontent-%COMP%]   .col-12[_ngcontent-%COMP%] {\\n    padding: 2px;\\n    margin-bottom: 8px;\\n  }\\n\\n  .compact-table[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n\\n  .table-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .student-row[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    padding: 8px 4px;\\n  }\\n\\n  .col-student[_ngcontent-%COMP%] {\\n    min-width: 140px;\\n  }\\n\\n  .student-name[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n\\n  .student-email[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n  }\\n}\\n\\n@media (max-width: 576px) {\\n  .student-list-container[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n\\n  .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.3rem;\\n  }\\n\\n  .title-icon[_ngcontent-%COMP%] {\\n    font-size: 1.3rem;\\n  }\\n\\n  .stat-card.compact-stat[_ngcontent-%COMP%] {\\n    padding: 10px;\\n    min-height: 55px;\\n    gap: 8px;\\n  }\\n\\n  .stat-card.compact-stat[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n    font-size: 1.2rem;\\n  }\\n\\n  .stat-number[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n\\n  .stat-label[_ngcontent-%COMP%] {\\n    font-size: 0.65rem;\\n  }\\n\\n  .compact-table[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n  }\\n\\n  .table-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n    padding: 6px 3px;\\n    font-size: 0.7rem;\\n  }\\n\\n  .student-row[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    padding: 6px 3px;\\n  }\\n\\n  .student-name[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n\\n  .student-email[_ngcontent-%COMP%] {\\n    font-size: 0.65rem;\\n  }\\n\\n  .action-btn.compact-btn[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n    min-width: 28px;\\n  }\\n\\n  .action-btn.compact-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n\\n  .class-badge[_ngcontent-%COMP%], .program-badge[_ngcontent-%COMP%], .roll-badge[_ngcontent-%COMP%] {\\n    font-size: 0.65rem;\\n    padding: 1px 4px;\\n  }\\n}\\n\\n\\n\\n.upload-section[_ngcontent-%COMP%] {\\n  padding: 20px 0;\\n}\\n\\n.select-file-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);\\n  color: white;\\n  border: none;\\n}\\n\\n.upload-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);\\n  border: none;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "program_r9", "_id", "ɵɵadvance", "ɵɵtextInterpolate1", "name", "dept_r10", "cls_r11", "ɵɵtextInterpolate2", "className", "section", "ɵɵelement", "ɵɵlistener", "AdminStudentListComponent_tr_157_Template_button_click_30_listener", "restoredCtx", "ɵɵrestoreView", "_r15", "student_r12", "$implicit", "ctx_r14", "ɵɵnextContext", "ɵɵresetView", "viewStudent", "AdminStudentListComponent_tr_157_Template_button_click_33_listener", "ctx_r16", "editStudent", "ɵɵtextInterpolate", "email", "rollNo", "program", "regNo", "department", "ctx_r6", "getClassName", "getClassSection", "contact", "AdminStudentListComponent", "constructor", "userService", "router", "http", "programService", "departmentService", "classesService", "searchQuery", "currentPage", "itemsPerPage", "totalPages", "totalRecords", "loading", "Math", "programs", "departments", "classes", "students", "filteredDepartments", "filteredClasses", "selectedProgram", "selectedDepartment", "selectedClass", "selectedSection", "selectedFile", "selected<PERSON><PERSON><PERSON><PERSON>", "responseMessage", "uploading", "totalStudents", "activeStudents", "ngOnInit", "loadInitialData", "setupFormSubscriptions", "fetchStudents", "calculateStatistics", "length", "filter", "student", "isActive", "loadPrograms", "loadDepartments", "loadClasses", "getAllPrograms", "subscribe", "next", "response", "success", "error", "console", "getAllDepartments", "getAllClasses", "onFileSelected", "event", "target", "files", "onUpload", "alert", "formData", "FormData", "append", "post", "message", "filters", "role", "page", "limit", "classId", "search", "getAllUsers", "log", "users", "pagination", "ceil", "applyFilters", "onProgramChange", "dept", "onDepartmentChange", "cls", "onClassChange", "onSearch", "resetFilters", "addnewstudent", "navigate", "nextPage", "previousPage", "goToPage", "deleteStudent", "confirm", "_", "ɵɵdirectiveInject", "i1", "UserService", "i2", "Router", "i3", "HttpClient", "i4", "ProgramService", "i5", "DepartmentService", "i6", "ClassesService", "_2", "selectors", "decls", "vars", "consts", "template", "AdminStudentListComponent_Template", "rf", "ctx", "AdminStudentListComponent_Template_input_ngModelChange_62_listener", "$event", "AdminStudentListComponent_Template_input_keyup_enter_62_listener", "AdminStudentListComponent_Template_mat_select_ngModelChange_69_listener", "AdminStudentListComponent_Template_mat_select_selectionChange_69_listener", "ɵɵtemplate", "AdminStudentListComponent_mat_option_72_Template", "AdminStudentListComponent_Template_mat_select_ngModelChange_77_listener", "AdminStudentListComponent_Template_mat_select_selectionChange_77_listener", "AdminStudentListComponent_mat_option_80_Template", "AdminStudentListComponent_Template_mat_select_ngModelChange_85_listener", "AdminStudentListComponent_Template_mat_select_selectionChange_85_listener", "AdminStudentListComponent_mat_option_88_Template", "AdminStudentListComponent_Template_button_click_90_listener", "AdminStudentListComponent_Template_button_click_95_listener", "AdminStudentListComponent_Template_input_change_116_listener", "AdminStudentListComponent_Template_button_click_119_listener", "_r17", "_r3", "ɵɵreference", "click", "AdminStudentListComponent_Template_button_click_123_listener", "AdminStudentListComponent_mat_icon_124_Template", "AdminStudentListComponent_mat_spinner_125_Template", "AdminStudentListComponent_tr_157_Template", "AdminStudentListComponent_div_158_Template", "AdminStudentListComponent_div_159_Template", "AdminStudentListComponent_Template_button_click_165_listener", "AdminStudentListComponent_Template_button_click_170_listener", "ɵɵtextInterpolate3", "min"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\students\\admin-student-list\\admin-student-list.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\students\\admin-student-list\\admin-student-list.component.html"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { UserService } from 'src/app/services/user.service';\r\nimport { ProgramService } from 'src/app/services/program.service';\r\nimport { DepartmentService } from 'src/app/services/department.service';\r\nimport { ClassesService } from 'src/app/services/classes.service';\r\nimport { Student } from 'src/app/models/studnet';\r\nimport { Program, Department, Class, User } from 'src/app/models/user';\r\n\r\n@Component({\r\n  selector: 'app-admin-student-list',\r\n  templateUrl: './admin-student-list.component.html',\r\n  styleUrls: ['./admin-student-list.component.css']\r\n})\r\nexport class AdminStudentListComponent implements OnInit {\r\n  searchQuery: string = '';\r\n  currentPage: number = 1;\r\n  itemsPerPage: number = 10;\r\n  totalPages: number = 1;\r\n  totalRecords: number = 0;\r\n  loading: boolean = false;\r\n  Math = Math; // Make Math available in template\r\n\r\n  // Data arrays\r\n  programs: Program[] = [];\r\n  departments: Department[] = [];\r\n  classes: Class[] = [];\r\n  students: User[] = [];\r\n  filteredDepartments: Department[] = [];\r\n  filteredClasses: Class[] = [];\r\n\r\n  // Selected filters\r\n  selectedProgram: string = '';\r\n  selectedDepartment: string = '';\r\n  selectedClass: string = '';\r\n  selectedSection: string = '';\r\n\r\n  selectedFile: File | null = null;\r\n  selectedFileName: string = '';\r\n  responseMessage: string = '';\r\n  uploading: boolean = false;\r\n\r\n  // Statistics\r\n  totalStudents: number = 0;\r\n  activeStudents: number = 0;\r\n\r\n  constructor(\r\n    private userService: UserService,\r\n    private router: Router,\r\n    private http: HttpClient,\r\n    private programService: ProgramService,\r\n    private departmentService: DepartmentService,\r\n    private classesService: ClassesService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.loadInitialData();\r\n    this.setupFormSubscriptions();\r\n    this.fetchStudents();\r\n    this.calculateStatistics();\r\n  }\r\n\r\n  calculateStatistics(): void {\r\n    // This would typically come from a dashboard API\r\n    // For now, we'll calculate from the current students array\r\n    this.totalStudents = this.students.length;\r\n    this.activeStudents = this.students.filter(student => student.isActive !== false).length;\r\n  }\r\n\r\n  loadInitialData(): void {\r\n    this.loadPrograms();\r\n    this.loadDepartments();\r\n    this.loadClasses();\r\n  }\r\n\r\n  loadPrograms(): void {\r\n    this.programService.getAllPrograms(true).subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.programs = response.programs;\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading programs:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  loadDepartments(): void {\r\n    this.departmentService.getAllDepartments().subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.departments = response.departments;\r\n          this.filteredDepartments = this.departments;\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading departments:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  loadClasses(): void {\r\n    this.classesService.getAllClasses().subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.classes = response.classes;\r\n          this.filteredClasses = this.classes;\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading classes:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  setupFormSubscriptions(): void {\r\n    // This would be implemented if using reactive forms\r\n    // For now, we'll handle filtering in the filter methods\r\n  }\r\n  onFileSelected(event: any) {\r\n    this.selectedFile = event.target.files[0];\r\n    this.selectedFileName = this.selectedFile ? this.selectedFile.name : '';\r\n  }\r\n  \r\n// Add student\r\nonUpload() {\r\n  if (!this.selectedFile) {\r\n    alert('Please select a file before uploading');\r\n    return;\r\n  }\r\n  if (this.selectedFile) {\r\n    const formData = new FormData();\r\n  formData.append('file', this.selectedFile, this.selectedFile.name);\r\n  this.http.post('http://localhost:5007/api/v1/bulk-signup', formData)\r\n  .subscribe({\r\n    next: (response: any) => {\r\n      this.responseMessage = response.success\r\n        ? `Success: ${response.message}`\r\n        : `Error: ${response.message}`;\r\n        this.fetchStudents();\r\n    },\r\n    error: (error) => {\r\n      console.error('Error uploading the file', error);\r\n      this.responseMessage = 'Error uploading the file. Please try again.';\r\n    }\r\n  });\r\n}\r\n}\r\n\r\n\r\n\r\n  fetchStudents(): void {\r\n    this.loading = true;\r\n\r\n    // Build filters for API call\r\n    const filters: any = {\r\n      role: 'Student',\r\n      page: this.currentPage,\r\n      limit: this.itemsPerPage\r\n    };\r\n\r\n    if (this.selectedProgram) filters.program = this.selectedProgram;\r\n    if (this.selectedDepartment) filters.department = this.selectedDepartment;\r\n    if (this.selectedClass) filters.classId = this.selectedClass;\r\n    if (this.searchQuery) filters.search = this.searchQuery;\r\n\r\n    this.userService.getAllUsers(filters).subscribe({\r\n      next: (response: any) => {\r\n        console.log(\"Students response:\", response);\r\n        if (response.success) {\r\n          this.students = response.users;\r\n          this.totalRecords = response.pagination?.totalRecords || response.users.length;\r\n          this.totalPages = response.pagination?.totalPages || Math.ceil(this.totalRecords / this.itemsPerPage);\r\n          this.currentPage = response.pagination?.currentPage || this.currentPage;\r\n        }\r\n        this.loading = false;\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Error fetching students:', error);\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  // Apply filters and refresh data\r\n  applyFilters(): void {\r\n    this.currentPage = 1;\r\n    this.fetchStudents();\r\n  }\r\n\r\n  // Filter departments when program changes\r\n  onProgramChange(): void {\r\n    if (this.selectedProgram) {\r\n      this.filteredDepartments = this.departments.filter(dept => dept.program._id === this.selectedProgram);\r\n      this.selectedDepartment = '';\r\n      this.selectedClass = '';\r\n      this.filteredClasses = [];\r\n    } else {\r\n      this.filteredDepartments = this.departments;\r\n      this.selectedDepartment = '';\r\n      this.selectedClass = '';\r\n      this.filteredClasses = this.classes;\r\n    }\r\n    this.applyFilters();\r\n  }\r\n\r\n  // Filter classes when department changes\r\n  onDepartmentChange(): void {\r\n    if (this.selectedDepartment) {\r\n      this.filteredClasses = this.classes.filter(cls => cls.department._id === this.selectedDepartment);\r\n      this.selectedClass = '';\r\n    } else {\r\n      this.filteredClasses = this.selectedProgram ?\r\n        this.classes.filter(cls => cls.program._id === this.selectedProgram) :\r\n        this.classes;\r\n      this.selectedClass = '';\r\n    }\r\n    this.applyFilters();\r\n  }\r\n\r\n  onClassChange(): void {\r\n    this.applyFilters();\r\n  }\r\n\r\n  onSearch(): void {\r\n    this.applyFilters();\r\n  }\r\n  \r\n\r\n  // Reset filters\r\n  resetFilters(): void {\r\n    this.selectedProgram = '';\r\n    this.selectedDepartment = '';\r\n    this.selectedClass = '';\r\n    this.selectedSection = '';\r\n    this.searchQuery = '';\r\n    this.filteredDepartments = this.departments;\r\n    this.filteredClasses = this.classes;\r\n    this.currentPage = 1;\r\n    this.fetchStudents();\r\n  }\r\n\r\n  addnewstudent(): void {\r\n    this.router.navigate(['/dashboard/admin/students/add-student']);\r\n  }\r\n\r\n  // Pagination methods\r\n  nextPage(): void {\r\n    if (this.currentPage < this.totalPages) {\r\n      this.currentPage++;\r\n      this.fetchStudents();\r\n    }\r\n  }\r\n\r\n  previousPage(): void {\r\n    if (this.currentPage > 1) {\r\n      this.currentPage--;\r\n      this.fetchStudents();\r\n    }\r\n  }\r\n\r\n  goToPage(page: number): void {\r\n    if (page >= 1 && page <= this.totalPages) {\r\n      this.currentPage = page;\r\n      this.fetchStudents();\r\n    }\r\n  }\r\n\r\n  // View student details\r\n  viewStudent(student: User): void {\r\n    // Navigate to student details page\r\n    this.router.navigate(['/dashboard/admin/students/view', student._id]);\r\n  }\r\n\r\n  // Edit student\r\n  editStudent(student: User): void {\r\n    this.router.navigate(['/dashboard/admin/students/edit', student._id]);\r\n  }\r\n\r\n  // Delete student\r\n  deleteStudent(student: User): void {\r\n    if (confirm(`Are you sure you want to delete ${student.name}?`)) {\r\n      console.log('Delete student:', student);\r\n      // Add actual delete logic here\r\n      // this.userService.deleteUser(student._id).subscribe(...)\r\n    }\r\n  }\r\n\r\n  // Helper methods for template\r\n  getClassName(student: User): string {\r\n    if (student?.classId && typeof student.classId === 'object') {\r\n      return student.classId.className || 'N/A';\r\n    }\r\n    return 'N/A';\r\n  }\r\n\r\n  getClassSection(student: User): string {\r\n    if (student?.classId && typeof student.classId === 'object') {\r\n      return student.classId.section || '';\r\n    }\r\n    return '';\r\n  }\r\n}\r\n", "<div class=\"student-list-container\">\r\n  <div class=\"container-fluid\">\r\n\r\n    <!-- Header Section -->\r\n    <div class=\"page-header\">\r\n      <div class=\"d-flex justify-content-between align-items-center\">\r\n        <div class=\"header-content\">\r\n          <h1 class=\"page-title\">\r\n            <mat-icon class=\"title-icon\">school</mat-icon>\r\n            Student Management\r\n          </h1>\r\n          <p class=\"page-subtitle\">Manage and view all student records</p>\r\n        </div>\r\n        <div class=\"header-actions\">\r\n          <button mat-raised-button color=\"primary\" routerLink=\"/dashboard/admin/students/add-student\" class=\"add-btn\">\r\n            <mat-icon>person_add</mat-icon>\r\n            Add New Student\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Compact Statistics Cards -->\r\n    <div class=\"stats-section mb-3\">\r\n      <div class=\"row g-2\">\r\n        <div class=\"col-6 col-md-3\">\r\n          <div class=\"stat-card compact-stat\">\r\n            <mat-icon class=\"stat-icon\">groups</mat-icon>\r\n            <div class=\"stat-info\">\r\n              <span class=\"stat-number\">{{ totalStudents }}</span>\r\n              <span class=\"stat-label\">Total</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-6 col-md-3\">\r\n          <div class=\"stat-card compact-stat\">\r\n            <mat-icon class=\"stat-icon\">person_check</mat-icon>\r\n            <div class=\"stat-info\">\r\n              <span class=\"stat-number\">{{ activeStudents }}</span>\r\n              <span class=\"stat-label\">Active</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-6 col-md-3\">\r\n          <div class=\"stat-card compact-stat\">\r\n            <mat-icon class=\"stat-icon\">category</mat-icon>\r\n            <div class=\"stat-info\">\r\n              <span class=\"stat-number\">{{ programs.length }}</span>\r\n              <span class=\"stat-label\">Programs</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-6 col-md-3\">\r\n          <div class=\"stat-card compact-stat\">\r\n            <mat-icon class=\"stat-icon\">business</mat-icon>\r\n            <div class=\"stat-info\">\r\n              <span class=\"stat-number\">{{ departments.length }}</span>\r\n              <span class=\"stat-label\">Departments</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Compact Filters Section -->\r\n    <mat-card class=\"filters-card\">\r\n      <mat-card-content>\r\n        <div class=\"compact-filters\">\r\n          <div class=\"row g-2\">\r\n            <!-- Search Input -->\r\n            <div class=\"col-12 col-md-4\">\r\n              <mat-form-field appearance=\"outline\" class=\"compact-field\">\r\n                <mat-label>Search Students</mat-label>\r\n                <input matInput\r\n                       placeholder=\"Name, Roll No...\"\r\n                       [(ngModel)]=\"searchQuery\"\r\n                       (keyup.enter)=\"onSearch()\">\r\n                <mat-icon matSuffix>search</mat-icon>\r\n              </mat-form-field>\r\n            </div>\r\n\r\n            <!-- Program Filter -->\r\n            <div class=\"col-6 col-md-2\">\r\n              <mat-form-field appearance=\"outline\" class=\"compact-field\">\r\n                <mat-label>Program</mat-label>\r\n                <mat-select [(ngModel)]=\"selectedProgram\" (selectionChange)=\"onProgramChange()\">\r\n                  <mat-option value=\"\">All</mat-option>\r\n                  <mat-option *ngFor=\"let program of programs\" [value]=\"program._id\">\r\n                    {{ program.name }}\r\n                  </mat-option>\r\n                </mat-select>\r\n              </mat-form-field>\r\n            </div>\r\n\r\n            <!-- Department Filter -->\r\n            <div class=\"col-6 col-md-2\">\r\n              <mat-form-field appearance=\"outline\" class=\"compact-field\">\r\n                <mat-label>Department</mat-label>\r\n                <mat-select [(ngModel)]=\"selectedDepartment\"\r\n                           (selectionChange)=\"onDepartmentChange()\"\r\n                           [disabled]=\"!selectedProgram\">\r\n                  <mat-option value=\"\">All</mat-option>\r\n                  <mat-option *ngFor=\"let dept of filteredDepartments\" [value]=\"dept._id\">\r\n                    {{ dept.name }}\r\n                  </mat-option>\r\n                </mat-select>\r\n              </mat-form-field>\r\n            </div>\r\n\r\n            <!-- Class Filter -->\r\n            <div class=\"col-6 col-md-2\">\r\n              <mat-form-field appearance=\"outline\" class=\"compact-field\">\r\n                <mat-label>Class</mat-label>\r\n                <mat-select [(ngModel)]=\"selectedClass\"\r\n                           (selectionChange)=\"onClassChange()\"\r\n                           [disabled]=\"!selectedDepartment\">\r\n                  <mat-option value=\"\">All</mat-option>\r\n                  <mat-option *ngFor=\"let cls of filteredClasses\" [value]=\"cls._id\">\r\n                    {{ cls.className }}-{{ cls.section }}\r\n                  </mat-option>\r\n                </mat-select>\r\n              </mat-form-field>\r\n            </div>\r\n\r\n            <!-- Filter Actions -->\r\n            <div class=\"col-6 col-md-2 d-flex align-items-center gap-1\">\r\n              <button mat-raised-button color=\"primary\" (click)=\"applyFilters()\" class=\"compact-btn\">\r\n                <mat-icon>filter_list</mat-icon>\r\n                <span class=\"d-none d-lg-inline\">Apply</span>\r\n              </button>\r\n              <button mat-icon-button (click)=\"resetFilters()\" matTooltip=\"Reset\">\r\n                <mat-icon>refresh</mat-icon>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n\r\n    <!-- Bulk Upload Section -->\r\n    <mat-card class=\"upload-card\">\r\n      <mat-card-header>\r\n        <mat-card-title>\r\n          <mat-icon>upload_file</mat-icon>\r\n          Bulk Student Upload\r\n        </mat-card-title>\r\n        <mat-card-subtitle>Upload multiple students using Excel file</mat-card-subtitle>\r\n      </mat-card-header>\r\n      <mat-card-content>\r\n        <div class=\"upload-section\">\r\n          <div class=\"row align-items-end\">\r\n            <div class=\"col-md-8\">\r\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n                <mat-label>Select Excel File</mat-label>\r\n                <input matInput readonly [value]=\"selectedFileName\" placeholder=\"No file selected\">\r\n                <mat-icon matSuffix>attach_file</mat-icon>\r\n              </mat-form-field>\r\n              <input type=\"file\"\r\n                     #fileInput\r\n                     (change)=\"onFileSelected($event)\"\r\n                     accept=\".xlsx,.xls\"\r\n                     style=\"display: none;\">\r\n            </div>\r\n            <div class=\"col-md-4 d-flex gap-2\">\r\n              <button mat-raised-button (click)=\"fileInput.click()\" class=\"select-file-btn\">\r\n                <mat-icon>folder_open</mat-icon>\r\n                Select File\r\n              </button>\r\n              <button mat-raised-button\r\n                      color=\"accent\"\r\n                      (click)=\"onUpload()\"\r\n                      [disabled]=\"!selectedFile || uploading\"\r\n                      class=\"upload-btn\">\r\n                <mat-icon *ngIf=\"!uploading\">cloud_upload</mat-icon>\r\n                <mat-spinner *ngIf=\"uploading\" diameter=\"20\"></mat-spinner>\r\n                {{ uploading ? 'Uploading...' : 'Upload' }}\r\n              </button>\r\n            </div>\r\n          </div>\r\n          <div class=\"upload-info mt-2\">\r\n            <small class=\"text-muted\">\r\n              <mat-icon class=\"info-icon\">info</mat-icon>\r\n              Supported formats: .xlsx, .xls. Maximum file size: 10MB\r\n            </small>\r\n          </div>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n  <!-- Search Input -->\r\n \r\n\r\n    <!-- Compact Students Table -->\r\n    <mat-card class=\"table-card\">\r\n      <mat-card-header>\r\n        <mat-card-title>\r\n          <mat-icon>list</mat-icon>\r\n          Students ({{ totalRecords }})\r\n        </mat-card-title>\r\n      </mat-card-header>\r\n      <mat-card-content>\r\n        <div class=\"table-container\">\r\n          <div class=\"table-responsive\">\r\n            <table class=\"compact-table\">\r\n              <thead>\r\n                <tr class=\"table-header\">\r\n                  <th class=\"col-student\">Student</th>\r\n                  <th class=\"col-ids d-none d-md-table-cell\">IDs</th>\r\n                  <th class=\"col-program d-none d-lg-table-cell\">Program</th>\r\n                  <th class=\"col-class\">Class</th>\r\n                  <th class=\"col-contact d-none d-sm-table-cell\">Contact</th>\r\n                  <th class=\"col-actions\">Actions</th>\r\n                </tr>\r\n              </thead>\r\n              <tbody>\r\n                <tr *ngFor=\"let student of students; let i = index;\" class=\"student-row\">\r\n                  <td class=\"col-student\">\r\n                    <div class=\"student-info\">\r\n                      <div class=\"student-name\">{{ student.name }}</div>\r\n                      <small class=\"student-email\">{{ student.email }}</small>\r\n                      <!-- Mobile info -->\r\n                      <div class=\"mobile-info d-md-none\">\r\n                        <small class=\"text-muted\">\r\n                          Roll: {{ student?.rollNo || 'N/A' }} |\r\n                          {{ student?.program?.name || 'N/A' }}\r\n                        </small>\r\n                      </div>\r\n                    </div>\r\n                  </td>\r\n                  <td class=\"col-ids d-none d-md-table-cell\">\r\n                    <div class=\"ids-info\">\r\n                      <span class=\"roll-badge\">{{ student?.rollNo || 'N/A' }}</span>\r\n                      <small class=\"reg-number\">{{ student?.regNo || 'N/A' }}</small>\r\n                    </div>\r\n                  </td>\r\n                  <td class=\"col-program d-none d-lg-table-cell\">\r\n                    <div class=\"program-info\">\r\n                      <span class=\"program-badge\">{{ student?.program?.name || 'N/A' }}</span>\r\n                      <small class=\"department-name\">{{ student?.department?.name || 'N/A' }}</small>\r\n                    </div>\r\n                  </td>\r\n                  <td class=\"col-class\">\r\n                    <span class=\"class-badge\">\r\n                      {{ getClassName(student) }}-{{ getClassSection(student) }}\r\n                    </span>\r\n                  </td>\r\n                  <td class=\"col-contact d-none d-sm-table-cell\">\r\n                    <span class=\"contact-info\">{{ student?.contact || 'N/A' }}</span>\r\n                  </td>\r\n                  <td class=\"col-actions\">\r\n                    <div class=\"action-buttons\">\r\n                      <button mat-icon-button\r\n                              color=\"primary\"\r\n                              (click)=\"viewStudent(student)\"\r\n                              matTooltip=\"View\"\r\n                              class=\"action-btn compact-btn\">\r\n                        <mat-icon>visibility</mat-icon>\r\n                      </button>\r\n                      <button mat-icon-button\r\n                              color=\"accent\"\r\n                              (click)=\"editStudent(student)\"\r\n                              matTooltip=\"Edit\"\r\n                              class=\"action-btn compact-btn\">\r\n                        <mat-icon>edit</mat-icon>\r\n                      </button>\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n\r\n            <!-- Loading State -->\r\n            <div *ngIf=\"loading\" class=\"loading-container\">\r\n              <mat-spinner diameter=\"50\"></mat-spinner>\r\n              <p class=\"loading-text\">Loading students...</p>\r\n            </div>\r\n\r\n            <!-- Empty State -->\r\n            <div *ngIf=\"!loading && students.length === 0\" class=\"empty-state\">\r\n              <mat-icon class=\"empty-icon\">school</mat-icon>\r\n              <h3>No Students Found</h3>\r\n              <p>Try adjusting your filters or add new students to get started.</p>\r\n              <button mat-raised-button color=\"primary\" routerLink=\"/dashboard/admin/students/add-student\">\r\n                <mat-icon>person_add</mat-icon>\r\n                Add First Student\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Pagination -->\r\n          <div class=\"pagination-container\">\r\n            <div class=\"pagination-info\">\r\n              <span class=\"text-muted\">\r\n                Showing {{ ((currentPage - 1) * itemsPerPage) + 1 }} to\r\n                {{ Math.min(currentPage * itemsPerPage, totalRecords) }} of\r\n                {{ totalRecords }} students\r\n              </span>\r\n            </div>\r\n            <div class=\"pagination-controls\">\r\n              <button mat-icon-button [disabled]=\"currentPage === 1\" (click)=\"previousPage()\" matTooltip=\"Previous Page\">\r\n                <mat-icon>chevron_left</mat-icon>\r\n              </button>\r\n              <span class=\"page-info\">Page {{ currentPage }} of {{ totalPages }}</span>\r\n              <button mat-icon-button [disabled]=\"currentPage === totalPages || totalPages === 0\" (click)=\"nextPage()\" matTooltip=\"Next Page\">\r\n                <mat-icon>chevron_right</mat-icon>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n  </div>\r\n</div>"], "mappings": ";;;;;;;;;;;;;;;;;;;;ICuFkBA,EAAA,CAAAC,cAAA,qBAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFgCH,EAAA,CAAAI,UAAA,UAAAC,UAAA,CAAAC,GAAA,CAAqB;IAChEN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,UAAA,CAAAI,IAAA,MACF;;;;;IAaAT,EAAA,CAAAC,cAAA,qBAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFwCH,EAAA,CAAAI,UAAA,UAAAM,QAAA,CAAAJ,GAAA,CAAkB;IACrEN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAE,QAAA,CAAAD,IAAA,MACF;;;;;IAaAT,EAAA,CAAAC,cAAA,qBAAkE;IAChED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFmCH,EAAA,CAAAI,UAAA,UAAAO,OAAA,CAAAL,GAAA,CAAiB;IAC/DN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAY,kBAAA,MAAAD,OAAA,CAAAE,SAAA,OAAAF,OAAA,CAAAG,OAAA,MACF;;;;;IAsDFd,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IACpDH,EAAA,CAAAe,SAAA,sBAA2D;;;;;;IAwC3Df,EAAA,CAAAC,cAAA,aAAyE;IAGzCD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAClDH,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAExDH,EAAA,CAAAC,cAAA,cAAmC;IAE/BD,EAAA,CAAAE,MAAA,GAEF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAIdH,EAAA,CAAAC,cAAA,cAA2C;IAEdD,EAAA,CAAAE,MAAA,IAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9DH,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAE,MAAA,IAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAGnEH,EAAA,CAAAC,cAAA,cAA+C;IAEfD,EAAA,CAAAE,MAAA,IAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxEH,EAAA,CAAAC,cAAA,iBAA+B;IAAAD,EAAA,CAAAE,MAAA,IAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAGnFH,EAAA,CAAAC,cAAA,cAAsB;IAElBD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,cAA+C;IAClBD,EAAA,CAAAE,MAAA,IAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEnEH,EAAA,CAAAC,cAAA,cAAwB;IAIZD,EAAA,CAAAgB,UAAA,mBAAAC,mEAAA;MAAA,MAAAC,WAAA,GAAAlB,EAAA,CAAAmB,aAAA,CAAAC,IAAA;MAAA,MAAAC,WAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,OAAA,GAAAvB,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAAF,OAAA,CAAAG,WAAA,CAAAL,WAAA,CAAoB;IAAA,EAAC;IAGpCrB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEjCH,EAAA,CAAAC,cAAA,kBAIuC;IAF/BD,EAAA,CAAAgB,UAAA,mBAAAW,mEAAA;MAAA,MAAAT,WAAA,GAAAlB,EAAA,CAAAmB,aAAA,CAAAC,IAAA;MAAA,MAAAC,WAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAM,OAAA,GAAA5B,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAAG,OAAA,CAAAC,WAAA,CAAAR,WAAA,CAAoB;IAAA,EAAC;IAGpCrB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IA7CDH,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAA8B,iBAAA,CAAAT,WAAA,CAAAZ,IAAA,CAAkB;IACfT,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAA8B,iBAAA,CAAAT,WAAA,CAAAU,KAAA,CAAmB;IAI5C/B,EAAA,CAAAO,SAAA,GAEF;IAFEP,EAAA,CAAAY,kBAAA,aAAAS,WAAA,kBAAAA,WAAA,CAAAW,MAAA,oBAAAX,WAAA,kBAAAA,WAAA,CAAAY,OAAA,kBAAAZ,WAAA,CAAAY,OAAA,CAAAxB,IAAA,gBAEF;IAMuBT,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAA8B,iBAAA,EAAAT,WAAA,kBAAAA,WAAA,CAAAW,MAAA,WAA8B;IAC7BhC,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAA8B,iBAAA,EAAAT,WAAA,kBAAAA,WAAA,CAAAa,KAAA,WAA6B;IAK3BlC,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAA8B,iBAAA,EAAAT,WAAA,kBAAAA,WAAA,CAAAY,OAAA,kBAAAZ,WAAA,CAAAY,OAAA,CAAAxB,IAAA,WAAqC;IAClCT,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAA8B,iBAAA,EAAAT,WAAA,kBAAAA,WAAA,CAAAc,UAAA,kBAAAd,WAAA,CAAAc,UAAA,CAAA1B,IAAA,WAAwC;IAKvET,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAY,kBAAA,MAAAwB,MAAA,CAAAC,YAAA,CAAAhB,WAAA,QAAAe,MAAA,CAAAE,eAAA,CAAAjB,WAAA,OACF;IAG2BrB,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAA8B,iBAAA,EAAAT,WAAA,kBAAAA,WAAA,CAAAkB,OAAA,WAA+B;;;;;IAyBlEvC,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAe,SAAA,sBAAyC;IACzCf,EAAA,CAAAC,cAAA,YAAwB;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAIjDH,EAAA,CAAAC,cAAA,cAAmE;IACpCD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9CH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,qEAA8D;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACrEH,EAAA,CAAAC,cAAA,iBAA6F;IACjFD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;AD7QvB,OAAM,MAAOqC,yBAAyB;EAgCpCC,YACUC,WAAwB,EACxBC,MAAc,EACdC,IAAgB,EAChBC,cAA8B,EAC9BC,iBAAoC,EACpCC,cAA8B;IAL9B,KAAAL,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IArCxB,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,UAAU,GAAW,CAAC;IACtB,KAAAC,YAAY,GAAW,CAAC;IACxB,KAAAC,OAAO,GAAY,KAAK;IACxB,KAAAC,IAAI,GAAGA,IAAI,CAAC,CAAC;IAEb;IACA,KAAAC,QAAQ,GAAc,EAAE;IACxB,KAAAC,WAAW,GAAiB,EAAE;IAC9B,KAAAC,OAAO,GAAY,EAAE;IACrB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,mBAAmB,GAAiB,EAAE;IACtC,KAAAC,eAAe,GAAY,EAAE;IAE7B;IACA,KAAAC,eAAe,GAAW,EAAE;IAC5B,KAAAC,kBAAkB,GAAW,EAAE;IAC/B,KAAAC,aAAa,GAAW,EAAE;IAC1B,KAAAC,eAAe,GAAW,EAAE;IAE5B,KAAAC,YAAY,GAAgB,IAAI;IAChC,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAAC,eAAe,GAAW,EAAE;IAC5B,KAAAC,SAAS,GAAY,KAAK;IAE1B;IACA,KAAAC,aAAa,GAAW,CAAC;IACzB,KAAAC,cAAc,GAAW,CAAC;EASvB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,sBAAsB,EAAE;IAC7B,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAA,mBAAmBA,CAAA;IACjB;IACA;IACA,IAAI,CAACN,aAAa,GAAG,IAAI,CAACX,QAAQ,CAACkB,MAAM;IACzC,IAAI,CAACN,cAAc,GAAG,IAAI,CAACZ,QAAQ,CAACmB,MAAM,CAACC,OAAO,IAAIA,OAAO,CAACC,QAAQ,KAAK,KAAK,CAAC,CAACH,MAAM;EAC1F;EAEAJ,eAAeA,CAAA;IACb,IAAI,CAACQ,YAAY,EAAE;IACnB,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAF,YAAYA,CAAA;IACV,IAAI,CAACnC,cAAc,CAACsC,cAAc,CAAC,IAAI,CAAC,CAACC,SAAS,CAAC;MACjDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAAChC,QAAQ,GAAG+B,QAAQ,CAAC/B,QAAQ;;MAErC,CAAC;MACDiC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD;KACD,CAAC;EACJ;EAEAP,eAAeA,CAAA;IACb,IAAI,CAACnC,iBAAiB,CAAC4C,iBAAiB,EAAE,CAACN,SAAS,CAAC;MACnDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAAC/B,WAAW,GAAG8B,QAAQ,CAAC9B,WAAW;UACvC,IAAI,CAACG,mBAAmB,GAAG,IAAI,CAACH,WAAW;;MAE/C,CAAC;MACDgC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MACpD;KACD,CAAC;EACJ;EAEAN,WAAWA,CAAA;IACT,IAAI,CAACnC,cAAc,CAAC4C,aAAa,EAAE,CAACP,SAAS,CAAC;MAC5CC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAAC9B,OAAO,GAAG6B,QAAQ,CAAC7B,OAAO;UAC/B,IAAI,CAACG,eAAe,GAAG,IAAI,CAACH,OAAO;;MAEvC,CAAC;MACD+B,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD;KACD,CAAC;EACJ;EAEAf,sBAAsBA,CAAA;IACpB;IACA;EAAA;EAEFmB,cAAcA,CAACC,KAAU;IACvB,IAAI,CAAC5B,YAAY,GAAG4B,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IACzC,IAAI,CAAC7B,gBAAgB,GAAG,IAAI,CAACD,YAAY,GAAG,IAAI,CAACA,YAAY,CAACxD,IAAI,GAAG,EAAE;EACzE;EAEF;EACAuF,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAAC/B,YAAY,EAAE;MACtBgC,KAAK,CAAC,uCAAuC,CAAC;MAC9C;;IAEF,IAAI,IAAI,CAAChC,YAAY,EAAE;MACrB,MAAMiC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MACjCD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE,IAAI,CAACnC,YAAY,EAAE,IAAI,CAACA,YAAY,CAACxD,IAAI,CAAC;MAClE,IAAI,CAACmC,IAAI,CAACyD,IAAI,CAAC,0CAA0C,EAAEH,QAAQ,CAAC,CACnEd,SAAS,CAAC;QACTC,IAAI,EAAGC,QAAa,IAAI;UACtB,IAAI,CAACnB,eAAe,GAAGmB,QAAQ,CAACC,OAAO,GACnC,YAAYD,QAAQ,CAACgB,OAAO,EAAE,GAC9B,UAAUhB,QAAQ,CAACgB,OAAO,EAAE;UAC9B,IAAI,CAAC5B,aAAa,EAAE;QACxB,CAAC;QACDc,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;UAChD,IAAI,CAACrB,eAAe,GAAG,6CAA6C;QACtE;OACD,CAAC;;EAEJ;EAIEO,aAAaA,CAAA;IACX,IAAI,CAACrB,OAAO,GAAG,IAAI;IAEnB;IACA,MAAMkD,OAAO,GAAQ;MACnBC,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,IAAI,CAACxD,WAAW;MACtByD,KAAK,EAAE,IAAI,CAACxD;KACb;IAED,IAAI,IAAI,CAACW,eAAe,EAAE0C,OAAO,CAACtE,OAAO,GAAG,IAAI,CAAC4B,eAAe;IAChE,IAAI,IAAI,CAACC,kBAAkB,EAAEyC,OAAO,CAACpE,UAAU,GAAG,IAAI,CAAC2B,kBAAkB;IACzE,IAAI,IAAI,CAACC,aAAa,EAAEwC,OAAO,CAACI,OAAO,GAAG,IAAI,CAAC5C,aAAa;IAC5D,IAAI,IAAI,CAACf,WAAW,EAAEuD,OAAO,CAACK,MAAM,GAAG,IAAI,CAAC5D,WAAW;IAEvD,IAAI,CAACN,WAAW,CAACmE,WAAW,CAACN,OAAO,CAAC,CAACnB,SAAS,CAAC;MAC9CC,IAAI,EAAGC,QAAa,IAAI;QACtBG,OAAO,CAACqB,GAAG,CAAC,oBAAoB,EAAExB,QAAQ,CAAC;QAC3C,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAAC7B,QAAQ,GAAG4B,QAAQ,CAACyB,KAAK;UAC9B,IAAI,CAAC3D,YAAY,GAAGkC,QAAQ,CAAC0B,UAAU,EAAE5D,YAAY,IAAIkC,QAAQ,CAACyB,KAAK,CAACnC,MAAM;UAC9E,IAAI,CAACzB,UAAU,GAAGmC,QAAQ,CAAC0B,UAAU,EAAE7D,UAAU,IAAIG,IAAI,CAAC2D,IAAI,CAAC,IAAI,CAAC7D,YAAY,GAAG,IAAI,CAACF,YAAY,CAAC;UACrG,IAAI,CAACD,WAAW,GAAGqC,QAAQ,CAAC0B,UAAU,EAAE/D,WAAW,IAAI,IAAI,CAACA,WAAW;;QAEzE,IAAI,CAACI,OAAO,GAAG,KAAK;MACtB,CAAC;MACDmC,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,IAAI,CAACnC,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA;EACA6D,YAAYA,CAAA;IACV,IAAI,CAACjE,WAAW,GAAG,CAAC;IACpB,IAAI,CAACyB,aAAa,EAAE;EACtB;EAEA;EACAyC,eAAeA,CAAA;IACb,IAAI,IAAI,CAACtD,eAAe,EAAE;MACxB,IAAI,CAACF,mBAAmB,GAAG,IAAI,CAACH,WAAW,CAACqB,MAAM,CAACuC,IAAI,IAAIA,IAAI,CAACnF,OAAO,CAAC3B,GAAG,KAAK,IAAI,CAACuD,eAAe,CAAC;MACrG,IAAI,CAACC,kBAAkB,GAAG,EAAE;MAC5B,IAAI,CAACC,aAAa,GAAG,EAAE;MACvB,IAAI,CAACH,eAAe,GAAG,EAAE;KAC1B,MAAM;MACL,IAAI,CAACD,mBAAmB,GAAG,IAAI,CAACH,WAAW;MAC3C,IAAI,CAACM,kBAAkB,GAAG,EAAE;MAC5B,IAAI,CAACC,aAAa,GAAG,EAAE;MACvB,IAAI,CAACH,eAAe,GAAG,IAAI,CAACH,OAAO;;IAErC,IAAI,CAACyD,YAAY,EAAE;EACrB;EAEA;EACAG,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACvD,kBAAkB,EAAE;MAC3B,IAAI,CAACF,eAAe,GAAG,IAAI,CAACH,OAAO,CAACoB,MAAM,CAACyC,GAAG,IAAIA,GAAG,CAACnF,UAAU,CAAC7B,GAAG,KAAK,IAAI,CAACwD,kBAAkB,CAAC;MACjG,IAAI,CAACC,aAAa,GAAG,EAAE;KACxB,MAAM;MACL,IAAI,CAACH,eAAe,GAAG,IAAI,CAACC,eAAe,GACzC,IAAI,CAACJ,OAAO,CAACoB,MAAM,CAACyC,GAAG,IAAIA,GAAG,CAACrF,OAAO,CAAC3B,GAAG,KAAK,IAAI,CAACuD,eAAe,CAAC,GACpE,IAAI,CAACJ,OAAO;MACd,IAAI,CAACM,aAAa,GAAG,EAAE;;IAEzB,IAAI,CAACmD,YAAY,EAAE;EACrB;EAEAK,aAAaA,CAAA;IACX,IAAI,CAACL,YAAY,EAAE;EACrB;EAEAM,QAAQA,CAAA;IACN,IAAI,CAACN,YAAY,EAAE;EACrB;EAGA;EACAO,YAAYA,CAAA;IACV,IAAI,CAAC5D,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAAChB,WAAW,GAAG,EAAE;IACrB,IAAI,CAACW,mBAAmB,GAAG,IAAI,CAACH,WAAW;IAC3C,IAAI,CAACI,eAAe,GAAG,IAAI,CAACH,OAAO;IACnC,IAAI,CAACR,WAAW,GAAG,CAAC;IACpB,IAAI,CAACyB,aAAa,EAAE;EACtB;EAEAgD,aAAaA,CAAA;IACX,IAAI,CAAC/E,MAAM,CAACgF,QAAQ,CAAC,CAAC,uCAAuC,CAAC,CAAC;EACjE;EAEA;EACAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC3E,WAAW,GAAG,IAAI,CAACE,UAAU,EAAE;MACtC,IAAI,CAACF,WAAW,EAAE;MAClB,IAAI,CAACyB,aAAa,EAAE;;EAExB;EAEAmD,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC5E,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;MAClB,IAAI,CAACyB,aAAa,EAAE;;EAExB;EAEAoD,QAAQA,CAACrB,IAAY;IACnB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAACtD,UAAU,EAAE;MACxC,IAAI,CAACF,WAAW,GAAGwD,IAAI;MACvB,IAAI,CAAC/B,aAAa,EAAE;;EAExB;EAEA;EACAhD,WAAWA,CAACoD,OAAa;IACvB;IACA,IAAI,CAACnC,MAAM,CAACgF,QAAQ,CAAC,CAAC,gCAAgC,EAAE7C,OAAO,CAACxE,GAAG,CAAC,CAAC;EACvE;EAEA;EACAuB,WAAWA,CAACiD,OAAa;IACvB,IAAI,CAACnC,MAAM,CAACgF,QAAQ,CAAC,CAAC,gCAAgC,EAAE7C,OAAO,CAACxE,GAAG,CAAC,CAAC;EACvE;EAEA;EACAyH,aAAaA,CAACjD,OAAa;IACzB,IAAIkD,OAAO,CAAC,mCAAmClD,OAAO,CAACrE,IAAI,GAAG,CAAC,EAAE;MAC/DgF,OAAO,CAACqB,GAAG,CAAC,iBAAiB,EAAEhC,OAAO,CAAC;MACvC;MACA;;EAEJ;EAEA;EACAzC,YAAYA,CAACyC,OAAa;IACxB,IAAIA,OAAO,EAAE6B,OAAO,IAAI,OAAO7B,OAAO,CAAC6B,OAAO,KAAK,QAAQ,EAAE;MAC3D,OAAO7B,OAAO,CAAC6B,OAAO,CAAC9F,SAAS,IAAI,KAAK;;IAE3C,OAAO,KAAK;EACd;EAEAyB,eAAeA,CAACwC,OAAa;IAC3B,IAAIA,OAAO,EAAE6B,OAAO,IAAI,OAAO7B,OAAO,CAAC6B,OAAO,KAAK,QAAQ,EAAE;MAC3D,OAAO7B,OAAO,CAAC6B,OAAO,CAAC7F,OAAO,IAAI,EAAE;;IAEtC,OAAO,EAAE;EACX;EAAC,QAAAmH,CAAA,G;qBAhSUzF,yBAAyB,EAAAxC,EAAA,CAAAkI,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAApI,EAAA,CAAAkI,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAtI,EAAA,CAAAkI,iBAAA,CAAAK,EAAA,CAAAC,UAAA,GAAAxI,EAAA,CAAAkI,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA1I,EAAA,CAAAkI,iBAAA,CAAAS,EAAA,CAAAC,iBAAA,GAAA5I,EAAA,CAAAkI,iBAAA,CAAAW,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAzBvG,yBAAyB;IAAAwG,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QCftCtJ,EAAA,CAAAC,cAAA,aAAoC;QAQKD,EAAA,CAAAE,MAAA,aAAM;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC9CH,EAAA,CAAAE,MAAA,2BACF;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAC,cAAA,WAAyB;QAAAD,EAAA,CAAAE,MAAA,2CAAmC;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAElEH,EAAA,CAAAC,cAAA,cAA4B;QAEdD,EAAA,CAAAE,MAAA,kBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC/BH,EAAA,CAAAE,MAAA,yBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAMfH,EAAA,CAAAC,cAAA,eAAgC;QAIID,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC7CH,EAAA,CAAAC,cAAA,eAAuB;QACKD,EAAA,CAAAE,MAAA,IAAmB;QAAAF,EAAA,CAAAG,YAAA,EAAO;QACpDH,EAAA,CAAAC,cAAA,gBAAyB;QAAAD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAI3CH,EAAA,CAAAC,cAAA,eAA4B;QAEID,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACnDH,EAAA,CAAAC,cAAA,eAAuB;QACKD,EAAA,CAAAE,MAAA,IAAoB;QAAAF,EAAA,CAAAG,YAAA,EAAO;QACrDH,EAAA,CAAAC,cAAA,gBAAyB;QAAAD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAI5CH,EAAA,CAAAC,cAAA,eAA4B;QAEID,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC/CH,EAAA,CAAAC,cAAA,eAAuB;QACKD,EAAA,CAAAE,MAAA,IAAqB;QAAAF,EAAA,CAAAG,YAAA,EAAO;QACtDH,EAAA,CAAAC,cAAA,gBAAyB;QAAAD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAI9CH,EAAA,CAAAC,cAAA,eAA4B;QAEID,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC/CH,EAAA,CAAAC,cAAA,eAAuB;QACKD,EAAA,CAAAE,MAAA,IAAwB;QAAAF,EAAA,CAAAG,YAAA,EAAO;QACzDH,EAAA,CAAAC,cAAA,gBAAyB;QAAAD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAQrDH,EAAA,CAAAC,cAAA,oBAA+B;QAORD,EAAA,CAAAE,MAAA,uBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACtCH,EAAA,CAAAC,cAAA,iBAGkC;QAD3BD,EAAA,CAAAgB,UAAA,2BAAAwI,mEAAAC,MAAA;UAAA,OAAAF,GAAA,CAAAvG,WAAA,GAAAyG,MAAA;QAAA,EAAyB,yBAAAC,iEAAA;UAAA,OACVH,GAAA,CAAA/B,QAAA,EAAU;QAAA,EADA;QAFhCxH,EAAA,CAAAG,YAAA,EAGkC;QAClCH,EAAA,CAAAC,cAAA,oBAAoB;QAAAD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAKzCH,EAAA,CAAAC,cAAA,eAA4B;QAEbD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC9BH,EAAA,CAAAC,cAAA,sBAAgF;QAApED,EAAA,CAAAgB,UAAA,2BAAA2I,wEAAAF,MAAA;UAAA,OAAAF,GAAA,CAAA1F,eAAA,GAAA4F,MAAA;QAAA,EAA6B,6BAAAG,0EAAA;UAAA,OAAoBL,GAAA,CAAApC,eAAA,EAAiB;QAAA,EAArC;QACvCnH,EAAA,CAAAC,cAAA,sBAAqB;QAAAD,EAAA,CAAAE,MAAA,WAAG;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACrCH,EAAA,CAAA6J,UAAA,KAAAC,gDAAA,yBAEa;QACf9J,EAAA,CAAAG,YAAA,EAAa;QAKjBH,EAAA,CAAAC,cAAA,eAA4B;QAEbD,EAAA,CAAAE,MAAA,kBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACjCH,EAAA,CAAAC,cAAA,sBAEyC;QAF7BD,EAAA,CAAAgB,UAAA,2BAAA+I,wEAAAN,MAAA;UAAA,OAAAF,GAAA,CAAAzF,kBAAA,GAAA2F,MAAA;QAAA,EAAgC,6BAAAO,0EAAA;UAAA,OACdT,GAAA,CAAAlC,kBAAA,EAAoB;QAAA,EADN;QAG1CrH,EAAA,CAAAC,cAAA,sBAAqB;QAAAD,EAAA,CAAAE,MAAA,WAAG;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACrCH,EAAA,CAAA6J,UAAA,KAAAI,gDAAA,yBAEa;QACfjK,EAAA,CAAAG,YAAA,EAAa;QAKjBH,EAAA,CAAAC,cAAA,eAA4B;QAEbD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC5BH,EAAA,CAAAC,cAAA,sBAE4C;QAFhCD,EAAA,CAAAgB,UAAA,2BAAAkJ,wEAAAT,MAAA;UAAA,OAAAF,GAAA,CAAAxF,aAAA,GAAA0F,MAAA;QAAA,EAA2B,6BAAAU,0EAAA;UAAA,OACTZ,GAAA,CAAAhC,aAAA,EAAe;QAAA,EADN;QAGrCvH,EAAA,CAAAC,cAAA,sBAAqB;QAAAD,EAAA,CAAAE,MAAA,WAAG;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACrCH,EAAA,CAAA6J,UAAA,KAAAO,gDAAA,yBAEa;QACfpK,EAAA,CAAAG,YAAA,EAAa;QAKjBH,EAAA,CAAAC,cAAA,eAA4D;QAChBD,EAAA,CAAAgB,UAAA,mBAAAqJ,4DAAA;UAAA,OAASd,GAAA,CAAArC,YAAA,EAAc;QAAA,EAAC;QAChElH,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAChCH,EAAA,CAAAC,cAAA,gBAAiC;QAAAD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAE/CH,EAAA,CAAAC,cAAA,kBAAoE;QAA5CD,EAAA,CAAAgB,UAAA,mBAAAsJ,4DAAA;UAAA,OAASf,GAAA,CAAA9B,YAAA,EAAc;QAAA,EAAC;QAC9CzH,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAW;QASxCH,EAAA,CAAAC,cAAA,oBAA8B;QAGdD,EAAA,CAAAE,MAAA,oBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAChCH,EAAA,CAAAE,MAAA,8BACF;QAAAF,EAAA,CAAAG,YAAA,EAAiB;QACjBH,EAAA,CAAAC,cAAA,0BAAmB;QAAAD,EAAA,CAAAE,MAAA,kDAAyC;QAAAF,EAAA,CAAAG,YAAA,EAAoB;QAElFH,EAAA,CAAAC,cAAA,yBAAkB;QAKGD,EAAA,CAAAE,MAAA,0BAAiB;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACxCH,EAAA,CAAAe,SAAA,kBAAmF;QACnFf,EAAA,CAAAC,cAAA,qBAAoB;QAAAD,EAAA,CAAAE,MAAA,oBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAE5CH,EAAA,CAAAC,cAAA,sBAI8B;QAFvBD,EAAA,CAAAgB,UAAA,oBAAAuJ,6DAAAd,MAAA;UAAA,OAAUF,GAAA,CAAA3D,cAAA,CAAA6D,MAAA,CAAsB;QAAA,EAAC;QAFxCzJ,EAAA,CAAAG,YAAA,EAI8B;QAEhCH,EAAA,CAAAC,cAAA,gBAAmC;QACPD,EAAA,CAAAgB,UAAA,mBAAAwJ,6DAAA;UAAAxK,EAAA,CAAAmB,aAAA,CAAAsJ,IAAA;UAAA,MAAAC,GAAA,GAAA1K,EAAA,CAAA2K,WAAA;UAAA,OAAS3K,EAAA,CAAAyB,WAAA,CAAAiJ,GAAA,CAAAE,KAAA,EAAiB;QAAA,EAAC;QACnD5K,EAAA,CAAAC,cAAA,iBAAU;QAAAD,EAAA,CAAAE,MAAA,oBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAChCH,EAAA,CAAAE,MAAA,sBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,mBAI2B;QAFnBD,EAAA,CAAAgB,UAAA,mBAAA6J,6DAAA;UAAA,OAAStB,GAAA,CAAAvD,QAAA,EAAU;QAAA,EAAC;QAG1BhG,EAAA,CAAA6J,UAAA,MAAAiB,+CAAA,uBAAoD;QACpD9K,EAAA,CAAA6J,UAAA,MAAAkB,kDAAA,0BAA2D;QAC3D/K,EAAA,CAAAE,MAAA,KACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAGbH,EAAA,CAAAC,cAAA,gBAA8B;QAEED,EAAA,CAAAE,MAAA,aAAI;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC3CH,EAAA,CAAAE,MAAA,kEACF;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAShBH,EAAA,CAAAC,cAAA,qBAA6B;QAGbD,EAAA,CAAAE,MAAA,aAAI;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACzBH,EAAA,CAAAE,MAAA,KACF;QAAAF,EAAA,CAAAG,YAAA,EAAiB;QAEnBH,EAAA,CAAAC,cAAA,yBAAkB;QAMkBD,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACpCH,EAAA,CAAAC,cAAA,eAA2C;QAAAD,EAAA,CAAAE,MAAA,YAAG;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACnDH,EAAA,CAAAC,cAAA,eAA+C;QAAAD,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC3DH,EAAA,CAAAC,cAAA,eAAsB;QAAAD,EAAA,CAAAE,MAAA,cAAK;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAChCH,EAAA,CAAAC,cAAA,eAA+C;QAAAD,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC3DH,EAAA,CAAAC,cAAA,eAAwB;QAAAD,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAGxCH,EAAA,CAAAC,cAAA,cAAO;QACLD,EAAA,CAAA6J,UAAA,MAAAmB,yCAAA,mBAoDK;QACPhL,EAAA,CAAAG,YAAA,EAAQ;QAIVH,EAAA,CAAA6J,UAAA,MAAAoB,0CAAA,kBAGM;QAGNjL,EAAA,CAAA6J,UAAA,MAAAqB,0CAAA,mBAQM;QACRlL,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAAC,cAAA,gBAAkC;QAG5BD,EAAA,CAAAE,MAAA,KAGF;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAETH,EAAA,CAAAC,cAAA,gBAAiC;QACwBD,EAAA,CAAAgB,UAAA,mBAAAmK,6DAAA;UAAA,OAAS5B,GAAA,CAAA1B,YAAA,EAAc;QAAA,EAAC;QAC7E7H,EAAA,CAAAC,cAAA,iBAAU;QAAAD,EAAA,CAAAE,MAAA,qBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAEnCH,EAAA,CAAAC,cAAA,iBAAwB;QAAAD,EAAA,CAAAE,MAAA,KAA0C;QAAAF,EAAA,CAAAG,YAAA,EAAO;QACzEH,EAAA,CAAAC,cAAA,mBAAgI;QAA5CD,EAAA,CAAAgB,UAAA,mBAAAoK,6DAAA;UAAA,OAAS7B,GAAA,CAAA3B,QAAA,EAAU;QAAA,EAAC;QACtG5H,EAAA,CAAAC,cAAA,iBAAU;QAAAD,EAAA,CAAAE,MAAA,sBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAW;;;QAlRVH,EAAA,CAAAO,SAAA,IAAmB;QAAnBP,EAAA,CAAA8B,iBAAA,CAAAyH,GAAA,CAAAlF,aAAA,CAAmB;QASnBrE,EAAA,CAAAO,SAAA,GAAoB;QAApBP,EAAA,CAAA8B,iBAAA,CAAAyH,GAAA,CAAAjF,cAAA,CAAoB;QASpBtE,EAAA,CAAAO,SAAA,GAAqB;QAArBP,EAAA,CAAA8B,iBAAA,CAAAyH,GAAA,CAAAhG,QAAA,CAAAqB,MAAA,CAAqB;QASrB5E,EAAA,CAAAO,SAAA,GAAwB;QAAxBP,EAAA,CAAA8B,iBAAA,CAAAyH,GAAA,CAAA/F,WAAA,CAAAoB,MAAA,CAAwB;QAmBzC5E,EAAA,CAAAO,SAAA,IAAyB;QAAzBP,EAAA,CAAAI,UAAA,YAAAmJ,GAAA,CAAAvG,WAAA,CAAyB;QAUpBhD,EAAA,CAAAO,SAAA,GAA6B;QAA7BP,EAAA,CAAAI,UAAA,YAAAmJ,GAAA,CAAA1F,eAAA,CAA6B;QAEP7D,EAAA,CAAAO,SAAA,GAAW;QAAXP,EAAA,CAAAI,UAAA,YAAAmJ,GAAA,CAAAhG,QAAA,CAAW;QAWjCvD,EAAA,CAAAO,SAAA,GAAgC;QAAhCP,EAAA,CAAAI,UAAA,YAAAmJ,GAAA,CAAAzF,kBAAA,CAAgC,cAAAyF,GAAA,CAAA1F,eAAA;QAIb7D,EAAA,CAAAO,SAAA,GAAsB;QAAtBP,EAAA,CAAAI,UAAA,YAAAmJ,GAAA,CAAA5F,mBAAA,CAAsB;QAWzC3D,EAAA,CAAAO,SAAA,GAA2B;QAA3BP,EAAA,CAAAI,UAAA,YAAAmJ,GAAA,CAAAxF,aAAA,CAA2B,cAAAwF,GAAA,CAAAzF,kBAAA;QAIT9D,EAAA,CAAAO,SAAA,GAAkB;QAAlBP,EAAA,CAAAI,UAAA,YAAAmJ,GAAA,CAAA3F,eAAA,CAAkB;QAqCvB5D,EAAA,CAAAO,SAAA,IAA0B;QAA1BP,EAAA,CAAAI,UAAA,UAAAmJ,GAAA,CAAArF,gBAAA,CAA0B;QAiB7ClE,EAAA,CAAAO,SAAA,IAAuC;QAAvCP,EAAA,CAAAI,UAAA,cAAAmJ,GAAA,CAAAtF,YAAA,IAAAsF,GAAA,CAAAnF,SAAA,CAAuC;QAElCpE,EAAA,CAAAO,SAAA,GAAgB;QAAhBP,EAAA,CAAAI,UAAA,UAAAmJ,GAAA,CAAAnF,SAAA,CAAgB;QACbpE,EAAA,CAAAO,SAAA,GAAe;QAAfP,EAAA,CAAAI,UAAA,SAAAmJ,GAAA,CAAAnF,SAAA,CAAe;QAC7BpE,EAAA,CAAAO,SAAA,GACF;QADEP,EAAA,CAAAQ,kBAAA,MAAA+I,GAAA,CAAAnF,SAAA,kCACF;QAoBJpE,EAAA,CAAAO,SAAA,IACF;QADEP,EAAA,CAAAQ,kBAAA,gBAAA+I,GAAA,CAAAnG,YAAA,OACF;QAiBgCpD,EAAA,CAAAO,SAAA,IAAa;QAAbP,EAAA,CAAAI,UAAA,YAAAmJ,GAAA,CAAA7F,QAAA,CAAa;QAyDnC1D,EAAA,CAAAO,SAAA,GAAa;QAAbP,EAAA,CAAAI,UAAA,SAAAmJ,GAAA,CAAAlG,OAAA,CAAa;QAMbrD,EAAA,CAAAO,SAAA,GAAuC;QAAvCP,EAAA,CAAAI,UAAA,UAAAmJ,GAAA,CAAAlG,OAAA,IAAAkG,GAAA,CAAA7F,QAAA,CAAAkB,MAAA,OAAuC;QAezC5E,EAAA,CAAAO,SAAA,GAGF;QAHEP,EAAA,CAAAqL,kBAAA,eAAA9B,GAAA,CAAAtG,WAAA,QAAAsG,GAAA,CAAArG,YAAA,cAAAqG,GAAA,CAAAjG,IAAA,CAAAgI,GAAA,CAAA/B,GAAA,CAAAtG,WAAA,GAAAsG,GAAA,CAAArG,YAAA,EAAAqG,GAAA,CAAAnG,YAAA,WAAAmG,GAAA,CAAAnG,YAAA,eAGF;QAGwBpD,EAAA,CAAAO,SAAA,GAA8B;QAA9BP,EAAA,CAAAI,UAAA,aAAAmJ,GAAA,CAAAtG,WAAA,OAA8B;QAG9BjD,EAAA,CAAAO,SAAA,GAA0C;QAA1CP,EAAA,CAAAY,kBAAA,UAAA2I,GAAA,CAAAtG,WAAA,UAAAsG,GAAA,CAAApG,UAAA,KAA0C;QAC1CnD,EAAA,CAAAO,SAAA,GAA2D;QAA3DP,EAAA,CAAAI,UAAA,aAAAmJ,GAAA,CAAAtG,WAAA,KAAAsG,GAAA,CAAApG,UAAA,IAAAoG,GAAA,CAAApG,UAAA,OAA2D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}