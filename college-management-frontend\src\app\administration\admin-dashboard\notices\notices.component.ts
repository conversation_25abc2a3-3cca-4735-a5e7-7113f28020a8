import { Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';
import { NoticeService, Notice } from '../../../services/notice.service';
import { ProgramService } from '../../../services/program.service';
import { DepartmentService } from '../../../services/department.service';
import { ClassesService } from '../../../services/classes.service';
import { UserService } from '../../../services/user.service';
import { UxHelperService } from '../../../shared/services/ux-helper.service';
import { Program, Department, Class } from '../../../models/user';

@Component({
  selector: 'app-notices',
  templateUrl: './notices.component.html',
  styleUrls: ['./notices.component.css']
})
export class NoticesComponent implements OnInit {
  notices: Notice[] = [];
  programs: Program[] = [];
  departments: Department[] = [];
  classes: Class[] = [];
  
  // Form
  noticeForm!: FormGroup;
  isEditing = false;
  editingNoticeId: string | null = null;
  
  // UI state
  loading = false;
  submitting = false;
  showForm = false;
  
  // Filters
  filterCategory = '';
  filterPriority = '';
  filterStatus = '';
  filterAudience = '';
  searchQuery = '';
  
  // Options
  categories = ['General', 'Academic', 'Administrative', 'Event', 'Holiday', 'Examination', 'Emergency'];
  priorities = ['Low', 'Medium', 'High', 'Urgent'];
  audiences = ['All', 'Students', 'Teachers', 'Principal', 'Staff'];
  
  currentUser: any;

  constructor(
    private fb: FormBuilder,
    private noticeService: NoticeService,
    private programService: ProgramService,
    private departmentService: DepartmentService,
    private classesService: ClassesService,
    private userService: UserService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private uxHelper: UxHelperService
  ) {
    this.currentUser = this.userService.getUserFromLocalStorage()?.user;
    this.initializeForm();
  }

  ngOnInit(): void {
    this.loadNotices();
    this.loadInitialData();
  }

  initializeForm(): void {
    this.noticeForm = this.fb.group({
      title: ['', [Validators.required, Validators.minLength(3)]],
      content: ['', [Validators.required, Validators.minLength(10)]],
      category: ['General', Validators.required],
      priority: ['Medium', Validators.required],
      targetAudience: [['All'], Validators.required],
      targetPrograms: [[]],
      targetDepartments: [[]],
      targetClasses: [[]],
      targetSemesters: [[]],
      publishDate: [new Date()],
      expiryDate: [''],
      isPublished: [false],
      isPinned: [false]
    });
  }

  loadInitialData(): void {
    // Load programs
    this.programService.getAllPrograms(true).subscribe({
      next: (response) => {
        if (response.success) {
          this.programs = response.programs;
        }
      },
      error: (error) => console.error('Error loading programs:', error)
    });

    // Load departments
    this.departmentService.getAllDepartments().subscribe({
      next: (response) => {
        if (response.success) {
          this.departments = response.departments;
        }
      },
      error: (error) => console.error('Error loading departments:', error)
    });

    // Load classes
    this.classesService.getAllClasses().subscribe({
      next: (response) => {
        if (response.success) {
          this.classes = response.classes;
        }
      },
      error: (error) => console.error('Error loading classes:', error)
    });
  }

  loadNotices(): void {
    this.loading = true;
    
    const params: any = {
      includeUnpublished: true,
      page: 1,
      limit: 50
    };

    if (this.filterCategory) params.category = this.filterCategory;
    if (this.filterPriority) params.priority = this.filterPriority;

    this.noticeService.getAllNotices(params).subscribe({
      next: (response) => {
        if (response.success) {
          this.notices = response.notices || [];
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading notices:', error);
        this.snackBar.open('Error loading notices', 'Close', { duration: 3000 });
        this.loading = false;
      }
    });
  }

  onSubmit(): void {
    if (this.noticeForm.valid && this.currentUser) {
      this.submitting = true;
      
      const formData = {
        ...this.noticeForm.value,
        author: this.currentUser._id
      };

      const operation = this.isEditing 
        ? this.noticeService.updateNotice(this.editingNoticeId!, formData)
        : this.noticeService.createNotice(formData);

      operation.subscribe({
        next: (response) => {
          if (response.success) {
            // Show encouraging success message
            this.uxHelper.showSuccessMessage(
              'NOTICE_PUBLISHED',
              this.isEditing
                ? 'Great! Your notice has been updated and all targeted users will see the changes.'
                : 'Excellent! Your notice has been published and all targeted users will be notified.'
            ).then(() => {
              this.resetForm();
              this.loadNotices();
            });
          }
          this.submitting = false;
        },
        error: (error) => {
          console.error('Error saving notice:', error);
          this.uxHelper.showErrorMessage(
            'SERVER_ERROR',
            'Unable to save your notice right now. Please check your content and try again.'
          );
          this.submitting = false;
        }
      });
    }
  }

  editNotice(notice: Notice): void {
    this.isEditing = true;
    this.editingNoticeId = notice._id!;
    this.showForm = true;
    
    this.noticeForm.patchValue({
      title: notice.title,
      content: notice.content,
      category: notice.category,
      priority: notice.priority,
      targetAudience: notice.targetAudience,
      targetPrograms: notice.targetPrograms || [],
      targetDepartments: notice.targetDepartments || [],
      targetClasses: notice.targetClasses || [],
      targetSemesters: notice.targetSemesters || [],
      publishDate: notice.publishDate,
      expiryDate: notice.expiryDate,
      isPublished: notice.isPublished,
      isPinned: notice.isPinned
    });
  }

  deleteNotice(notice: Notice): void {
    this.uxHelper.showConfirmationDialog(
      'DELETE_NOTICE',
      `Are you sure you want to delete "${notice.title}"? Students will no longer see this notice.`,
      'Yes, delete it',
      'Keep it'
    ).then((result) => {
      if (result.isConfirmed) {
        this.noticeService.deleteNotice(notice._id!).subscribe({
          next: (response) => {
            if (response.success) {
              this.uxHelper.showSuccessMessage(
                'DATA_SAVED',
                'The notice has been deleted successfully.'
              );
              this.loadNotices();
            }
          },
          error: (error) => {
            console.error('Error deleting notice:', error);
            this.uxHelper.showErrorMessage(
              'SERVER_ERROR',
              'Unable to delete the notice right now. Please try again.'
            );
          }
        });
      }
    });
  }

  togglePublish(notice: Notice): void {
    const operation = notice.isPublished 
      ? this.noticeService.unpublishNotice(notice._id!)
      : this.noticeService.publishNotice(notice._id!);

    operation.subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open(
            notice.isPublished ? 'Notice unpublished' : 'Notice published',
            'Close',
            { duration: 3000 }
          );
          this.loadNotices();
        }
      },
      error: (error) => {
        console.error('Error toggling publish status:', error);
        this.snackBar.open('Error updating notice', 'Close', { duration: 3000 });
      }
    });
  }

  togglePin(notice: Notice): void {
    const operation = notice.isPinned 
      ? this.noticeService.unpinNotice(notice._id!)
      : this.noticeService.pinNotice(notice._id!);

    operation.subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open(
            notice.isPinned ? 'Notice unpinned' : 'Notice pinned',
            'Close',
            { duration: 3000 }
          );
          this.loadNotices();
        }
      },
      error: (error) => {
        console.error('Error toggling pin status:', error);
        this.snackBar.open('Error updating notice', 'Close', { duration: 3000 });
      }
    });
  }

  resetForm(): void {
    this.noticeForm.reset();
    this.initializeForm();
    this.isEditing = false;
    this.editingNoticeId = null;
    this.showForm = false;
  }

  applyFilters(): void {
    this.loadNotices();
  }

  clearFilters(): void {
    this.filterCategory = '';
    this.filterPriority = '';
    this.filterStatus = '';
    this.searchQuery = '';
    this.loadNotices();
  }

  get filteredNotices(): Notice[] {
    let filtered = this.notices;

    if (this.searchQuery) {
      const query = this.searchQuery.toLowerCase();
      filtered = filtered.filter(notice => 
        notice.title.toLowerCase().includes(query) ||
        notice.content.toLowerCase().includes(query)
      );
    }

    if (this.filterStatus) {
      filtered = filtered.filter(notice => notice.status === this.filterStatus);
    }

    return filtered;
  }

  formatDate(date: string | Date): string {
    return new Date(date).toLocaleDateString();
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'Active': return 'status-active';
      case 'Draft': return 'status-draft';
      case 'Expired': return 'status-expired';
      case 'Scheduled': return 'status-scheduled';
      default: return '';
    }
  }

  getPriorityClass(priority: string): string {
    switch (priority) {
      case 'Urgent': return 'priority-urgent';
      case 'High': return 'priority-high';
      case 'Medium': return 'priority-medium';
      case 'Low': return 'priority-low';
      default: return '';
    }
  }

  // UI helper methods
  toggleForm(): void {
    this.showForm = !this.showForm;
    if (!this.showForm) {
      this.resetForm();
    }
  }

  resetFilters(): void {
    this.filterCategory = '';
    this.filterPriority = '';
    this.filterStatus = '';
    this.filterAudience = '';
    this.searchQuery = '';
    this.applyFilters();
  }
}
