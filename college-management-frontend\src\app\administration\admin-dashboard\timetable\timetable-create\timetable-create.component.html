<div class="timetable-create-container">
  <mat-card>
    <mat-card-header>
      <mat-card-title>
        {{ isEditMode ? 'Edit Timetable Entry' : 'Create Timetable Entry' }}
      </mat-card-title>
    </mat-card-header>

    <mat-card-content>
      <form [formGroup]="timetableForm" (ngSubmit)="onSubmit()">
        <div class="form-row">
          <!-- Program Selection -->
          <mat-form-field appearance="outline">
            <mat-label>Program</mat-label>
            <mat-select formControlName="program" (selectionChange)="onProgramChange()">
              <mat-option value="">Select Program</mat-option>
              <mat-option *ngFor="let program of programs" [value]="program._id">
                {{ program.name }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="program?.invalid && program?.touched">
              Program is required
            </mat-error>
          </mat-form-field>

          <!-- Department Selection -->
          <mat-form-field appearance="outline">
            <mat-label>Department</mat-label>
            <mat-select formControlName="department" (selectionChange)="onDepartmentChange()">
              <mat-option value="">Select Department</mat-option>
              <mat-option *ngFor="let dept of filteredDepartments" [value]="dept._id">
                {{ dept.name }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="department?.invalid && department?.touched">
              Department is required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <!-- Class Selection -->
          <mat-form-field appearance="outline">
            <mat-label>Class</mat-label>
            <mat-select formControlName="class">
              <mat-option value="">Select Class</mat-option>
              <mat-option *ngFor="let cls of filteredClasses" [value]="cls._id">
                {{ cls.name }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="classControl?.invalid && classControl?.touched">
              Class is required
            </mat-error>
          </mat-form-field>

          <!-- Subject Selection -->
          <mat-form-field appearance="outline">
            <mat-label>Subject</mat-label>
            <mat-select formControlName="subject">
              <mat-option value="">Select Subject</mat-option>
              <mat-option *ngFor="let subj of filteredSubjects" [value]="subj._id">
                {{ subj.name }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="subject?.invalid && subject?.touched">
              Subject is required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <!-- Teacher Selection -->
          <mat-form-field appearance="outline">
            <mat-label>Teacher</mat-label>
            <mat-select formControlName="teacher">
              <mat-option value="">Select Teacher</mat-option>
              <mat-option *ngFor="let teacher of filteredTeachers" [value]="teacher._id">
                {{ teacher.firstName }} {{ teacher.lastName }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="teacher?.invalid && teacher?.touched">
              Teacher is required
            </mat-error>
          </mat-form-field>

          <!-- Day of Week -->
          <mat-form-field appearance="outline">
            <mat-label>Day of Week</mat-label>
            <mat-select formControlName="dayOfWeek">
              <mat-option value="">Select Day</mat-option>
              <mat-option *ngFor="let day of daysOfWeek" [value]="day">
                {{ day }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="dayOfWeek?.invalid && dayOfWeek?.touched">
              Day of week is required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <!-- Start Time -->
          <mat-form-field appearance="outline">
            <mat-label>Start Time</mat-label>
            <mat-select formControlName="startTime">
              <mat-option value="">Select Time</mat-option>
              <mat-option *ngFor="let time of timeSlots" [value]="time">
                {{ time }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="startTime?.invalid && startTime?.touched">
              Start time is required
            </mat-error>
          </mat-form-field>

          <!-- Duration -->
          <mat-form-field appearance="outline">
            <mat-label>Duration (minutes)</mat-label>
            <mat-select formControlName="duration">
              <mat-option *ngFor="let dur of durations" [value]="dur">
                {{ dur }} minutes
              </mat-option>
            </mat-select>
            <mat-error *ngIf="duration?.invalid && duration?.touched">
              Duration is required
            </mat-error>
          </mat-form-field>

          <!-- End Time (calculated) -->
          <mat-form-field appearance="outline">
            <mat-label>End Time</mat-label>
            <input matInput formControlName="endTime" readonly>
          </mat-form-field>
        </div>

        <div class="form-row">
          <!-- Room -->
          <mat-form-field appearance="outline">
            <mat-label>Room</mat-label>
            <input matInput formControlName="room" placeholder="Enter room number">
          </mat-form-field>

          <!-- Semester -->
          <mat-form-field appearance="outline">
            <mat-label>Semester</mat-label>
            <input matInput type="number" formControlName="semester" min="1" max="8">
            <mat-error *ngIf="semester?.invalid && semester?.touched">
              Semester is required
            </mat-error>
          </mat-form-field>

          <!-- Academic Year -->
          <mat-form-field appearance="outline">
            <mat-label>Academic Year</mat-label>
            <input matInput formControlName="academicYear" placeholder="e.g., 2023-2024">
            <mat-error *ngIf="academicYear?.invalid && academicYear?.touched">
              Academic year is required
            </mat-error>
          </mat-form-field>
        </div>

        <!-- Notes -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Notes</mat-label>
          <textarea matInput formControlName="notes" rows="3" placeholder="Additional notes (optional)"></textarea>
        </mat-form-field>

        <!-- Action Buttons -->
        <div class="form-actions">
          <button mat-raised-button type="button" (click)="onCancel()" [disabled]="submitting">
            Cancel
          </button>
          <button mat-raised-button color="primary" type="submit" [disabled]="submitting || timetableForm.invalid">
            <mat-spinner *ngIf="submitting" diameter="20"></mat-spinner>
            {{ isEditMode ? 'Update' : 'Create' }} Timetable
          </button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>

  <!-- Loading Overlay -->
  <div *ngIf="loading" class="loading-overlay">
    <mat-spinner></mat-spinner>
    <p>Loading timetable data...</p>
  </div>
</div>
