.timetable-list-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* Header Styles */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content h1 {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1.8rem;
  font-weight: 600;
}

.header-content h1 mat-icon {
  color: #1976d2;
}

.subtitle {
  margin: 0;
  color: #666;
  font-size: 1rem;
}

.create-btn {
  padding: 12px 24px;
  font-weight: 500;
}

/* Filters Styles */
.filters-card {
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.filters-grid {
  display: grid;
  grid-template-columns: 2fr repeat(5, 1fr) auto;
  gap: 16px;
  align-items: end;
}

.search-field {
  min-width: 250px;
}

.clear-filters-btn {
  height: 56px;
  padding: 0 16px;
}

/* Results Summary */
.results-summary {
  margin-bottom: 16px;
  padding: 0 4px;
}

.results-count {
  color: #666;
  font-size: 0.9rem;
}

/* Table Styles */
.timetable-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
}

.table-container {
  overflow-x: auto;
}

.timetable-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.timetable-table th {
  background-color: #f8f9fa;
  padding: 16px 12px;
  text-align: left;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #e9ecef;
  white-space: nowrap;
}

.timetable-table td {
  padding: 16px 12px;
  border-bottom: 1px solid #e9ecef;
  vertical-align: top;
}

.timetable-table tr:hover {
  background-color: #f8f9fa;
}

.inactive-row {
  opacity: 0.6;
  background-color: #f9f9f9;
}

/* Cell Specific Styles */
.subject-cell .subject-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.subject-name {
  font-weight: 500;
  color: #333;
}

.subject-code {
  font-size: 0.85rem;
  color: #666;
  background: #e3f2fd;
  padding: 2px 8px;
  border-radius: 12px;
  display: inline-block;
  width: fit-content;
}

.teacher-cell .teacher-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.teacher-name {
  font-weight: 500;
  color: #333;
}

.teacher-email {
  font-size: 0.85rem;
  color: #666;
}

.class-cell .class-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.class-name {
  font-weight: 500;
  color: #333;
}

.semester {
  font-size: 0.85rem;
  color: #666;
  background: #fff3e0;
  padding: 2px 8px;
  border-radius: 12px;
  display: inline-block;
  width: fit-content;
}

.schedule-cell .schedule-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.day {
  font-weight: 500;
  color: #1976d2;
}

.time {
  font-size: 0.9rem;
  color: #666;
}

.room-cell .room {
  background: #e8f5e8;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.9rem;
  color: #2e7d32;
  font-weight: 500;
}

.program-cell .program-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.program-name {
  font-weight: 500;
  color: #333;
}

.department-name {
  font-size: 0.85rem;
  color: #666;
}

.status-cell {
  text-align: center;
}

.status-cell .status-label {
  display: block;
  margin-top: 8px;
  font-size: 0.85rem;
  color: #666;
}

.actions-cell .action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.action-buttons button {
  width: 36px;
  height: 36px;
}

/* Loading and Empty States */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  color: #666;
}

.loading-container p {
  margin-top: 16px;
  font-size: 1rem;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  text-align: center;
  color: #666;
}

.empty-icon {
  font-size: 64px;
  width: 64px;
  height: 64px;
  color: #ccc;
  margin-bottom: 16px;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  color: #333;
}

.empty-state p {
  margin: 0 0 24px 0;
  color: #666;
}

/* Pagination */
.pagination-container {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .filters-grid {
    grid-template-columns: 1fr 1fr 1fr;
    gap: 12px;
  }
  
  .search-field {
    grid-column: 1 / -1;
    min-width: auto;
  }
  
  .clear-filters-btn {
    grid-column: 1 / -1;
    justify-self: start;
  }
}

@media (max-width: 768px) {
  .timetable-list-container {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .filters-grid {
    grid-template-columns: 1fr;
  }
  
  .timetable-table {
    font-size: 0.9rem;
  }
  
  .timetable-table th,
  .timetable-table td {
    padding: 12px 8px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 2px;
  }
}
