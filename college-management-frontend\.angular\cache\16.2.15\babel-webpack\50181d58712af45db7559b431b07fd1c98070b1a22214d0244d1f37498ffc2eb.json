{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/dashboard.service\";\nimport * as i2 from \"../../../services/user.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/chips\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/progress-spinner\";\nimport * as i10 from \"@angular/material/tooltip\";\nfunction TeacherOverviewComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading your dashboard...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TeacherOverviewComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"mat-icon\", 6);\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function TeacherOverviewComponent_div_2_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.refreshData());\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" Retry \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction TeacherOverviewComponent_div_3_div_65_div_1_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 44);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const classItem_r15 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"Room: \", classItem_r15.room, \"\");\n  }\n}\nfunction TeacherOverviewComponent_div_3_div_65_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 39)(2, \"span\", 40);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 41)(5, \"h4\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 42);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, TeacherOverviewComponent_div_3_div_65_div_1_span_11_Template, 2, 1, \"span\", 43);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const classItem_r15 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", classItem_r15.timeSlot.startTime, \" - \", classItem_r15.timeSlot.endTime, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(classItem_r15.subject.subjectName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", classItem_r15.class.className, \" - \", classItem_r15.class.section, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(classItem_r15.department.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", classItem_r15.room);\n  }\n}\nfunction TeacherOverviewComponent_div_3_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtemplate(1, TeacherOverviewComponent_div_3_div_65_div_1_Template, 12, 7, \"div\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.getTodayClasses());\n  }\n}\nfunction TeacherOverviewComponent_div_3_ng_template_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"event_available\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No classes scheduled for today\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TeacherOverviewComponent_div_3_div_73_p_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 50);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r18.getUpcomingClass().room);\n  }\n}\nfunction TeacherOverviewComponent_div_3_div_73_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47)(2, \"h2\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 48)(7, \"h3\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, TeacherOverviewComponent_div_3_div_73_p_11_Template, 2, 1, \"p\", 49);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r8.getUpcomingClass().timeSlot.startTime);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r8.getUpcomingClass().timeSlot.endTime);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r8.getUpcomingClass().subject.subjectName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r8.getUpcomingClass().class.className, \" - \", ctx_r8.getUpcomingClass().class.section, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.getUpcomingClass().room);\n  }\n}\nfunction TeacherOverviewComponent_div_3_ng_template_74_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No more classes today\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TeacherOverviewComponent_div_3_div_84_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55)(2, \"span\", 56);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 57);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 58)(7, \"span\", 59);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 60);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 61)(13, \"mat-chip\", 62);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"titlecase\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const attendance_r20 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(attendance_r20.student.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(attendance_r20.student.regNo);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(attendance_r20.subject.subjectName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 6, attendance_r20.date, \"shortDate\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"color\", attendance_r20.status === \"present\" ? \"primary\" : \"warn\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(15, 9, attendance_r20.status), \" \");\n  }\n}\nfunction TeacherOverviewComponent_div_3_div_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵtemplate(1, TeacherOverviewComponent_div_3_div_84_div_1_Template, 16, 11, \"div\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r11.teacherDashboard.recentAttendance.slice(0, 5));\n  }\n}\nfunction TeacherOverviewComponent_div_3_ng_template_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"fact_check\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No recent attendance records\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TeacherOverviewComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"div\", 10)(3, \"h1\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Here's your teaching overview for today\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function TeacherOverviewComponent_div_3_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.refreshData());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"refresh\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 12)(11, \"mat-card\", 13)(12, \"mat-card-content\")(13, \"div\", 14)(14, \"div\", 15)(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"class\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 16)(18, \"h3\");\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\");\n    i0.ɵɵtext(21, \"My Classes\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(22, \"mat-card\", 17)(23, \"mat-card-content\")(24, \"div\", 14)(25, \"div\", 15)(26, \"mat-icon\");\n    i0.ɵɵtext(27, \"subject\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 16)(29, \"h3\");\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"p\");\n    i0.ɵɵtext(32, \"Subjects Teaching\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(33, \"mat-card\", 18)(34, \"mat-card-content\")(35, \"div\", 14)(36, \"div\", 15)(37, \"mat-icon\");\n    i0.ɵɵtext(38, \"school\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"div\", 16)(40, \"h3\");\n    i0.ɵɵtext(41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"p\");\n    i0.ɵɵtext(43, \"Total Students\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(44, \"mat-card\", 19)(45, \"mat-card-content\")(46, \"div\", 14)(47, \"div\", 15)(48, \"mat-icon\");\n    i0.ɵɵtext(49, \"today\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"div\", 16)(51, \"h3\");\n    i0.ɵɵtext(52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"p\");\n    i0.ɵɵtext(54, \"Classes Today\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(55, \"div\", 20)(56, \"div\", 21)(57, \"mat-card\", 22)(58, \"mat-card-header\")(59, \"mat-card-title\");\n    i0.ɵɵtext(60, \"Today's Schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(61, \"mat-card-subtitle\");\n    i0.ɵɵtext(62);\n    i0.ɵɵpipe(63, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(64, \"mat-card-content\");\n    i0.ɵɵtemplate(65, TeacherOverviewComponent_div_3_div_65_Template, 2, 1, \"div\", 23);\n    i0.ɵɵtemplate(66, TeacherOverviewComponent_div_3_ng_template_66_Template, 5, 0, \"ng-template\", null, 24, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(68, \"mat-card\", 25)(69, \"mat-card-header\")(70, \"mat-card-title\");\n    i0.ɵɵtext(71, \"Next Class\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(72, \"mat-card-content\");\n    i0.ɵɵtemplate(73, TeacherOverviewComponent_div_3_div_73_Template, 12, 6, \"div\", 26);\n    i0.ɵɵtemplate(74, TeacherOverviewComponent_div_3_ng_template_74_Template, 5, 0, \"ng-template\", null, 27, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(76, \"div\", 28)(77, \"mat-card\")(78, \"mat-card-header\")(79, \"mat-card-title\");\n    i0.ɵɵtext(80, \"Recent Attendance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(81, \"mat-card-subtitle\");\n    i0.ɵɵtext(82, \"Last 5 attendance records\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(83, \"mat-card-content\");\n    i0.ɵɵtemplate(84, TeacherOverviewComponent_div_3_div_84_Template, 2, 1, \"div\", 29);\n    i0.ɵɵtemplate(85, TeacherOverviewComponent_div_3_ng_template_85_Template, 5, 0, \"ng-template\", null, 30, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(87, \"div\", 31)(88, \"h2\");\n    i0.ɵɵtext(89, \"Quick Actions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(90, \"div\", 32)(91, \"button\", 33)(92, \"mat-icon\");\n    i0.ɵɵtext(93, \"fact_check\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(94, \" Mark Attendance \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(95, \"button\", 34)(96, \"mat-icon\");\n    i0.ɵɵtext(97, \"class\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(98, \" View My Classes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(99, \"button\", 35)(100, \"mat-icon\");\n    i0.ɵɵtext(101, \"school\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(102, \" View Students \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const _r6 = i0.ɵɵreference(67);\n    const _r9 = i0.ɵɵreference(75);\n    const _r12 = i0.ɵɵreference(86);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Welcome back, \", ctx_r2.currentUser == null ? null : ctx_r2.currentUser.name, \"!\");\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate(ctx_r2.teacherDashboard.stats.totalClasses);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r2.teacherDashboard.stats.totalSubjects);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r2.teacherDashboard.stats.totalStudents);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r2.getTodayClasses().length);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(63, 12, ctx_r2.currentDate, \"fullDate\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getTodayClasses().length > 0)(\"ngIfElse\", _r6);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getUpcomingClass())(\"ngIfElse\", _r9);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.teacherDashboard.recentAttendance.length > 0)(\"ngIfElse\", _r12);\n  }\n}\nexport class TeacherOverviewComponent {\n  constructor(dashboardService, userService) {\n    this.dashboardService = dashboardService;\n    this.userService = userService;\n    this.teacherDashboard = null;\n    this.loading = true;\n    this.error = null;\n    this.currentDate = new Date();\n  }\n  ngOnInit() {\n    this.currentUser = this.userService.getUserFromLocalStorage()?.user;\n    if (this.currentUser) {\n      this.loadTeacherDashboard();\n    } else {\n      this.error = 'User not found';\n      this.loading = false;\n    }\n  }\n  loadTeacherDashboard() {\n    this.loading = true;\n    this.error = null;\n    this.dashboardService.getTeacherDashboard(this.currentUser._id).subscribe({\n      next: response => {\n        if (response.success) {\n          this.teacherDashboard = response.dashboard;\n        } else {\n          this.error = 'Failed to load teacher dashboard';\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading teacher dashboard:', error);\n        this.error = 'Error loading dashboard data';\n        this.loading = false;\n      }\n    });\n  }\n  refreshData() {\n    this.loadTeacherDashboard();\n  }\n  getTodayClasses() {\n    if (!this.teacherDashboard?.timetable) return [];\n    const today = new Date().toLocaleDateString('en-US', {\n      weekday: 'long'\n    });\n    return this.teacherDashboard.timetable.filter(entry => entry.dayOfWeek === today);\n  }\n  getUpcomingClass() {\n    const todayClasses = this.getTodayClasses();\n    const now = new Date();\n    const currentTime = now.getHours() * 60 + now.getMinutes();\n    return todayClasses.find(classItem => {\n      const [hours, minutes] = classItem.timeSlot.startTime.split(':').map(Number);\n      const classTime = hours * 60 + minutes;\n      return classTime > currentTime;\n    });\n  }\n  static #_ = this.ɵfac = function TeacherOverviewComponent_Factory(t) {\n    return new (t || TeacherOverviewComponent)(i0.ɵɵdirectiveInject(i1.DashboardService), i0.ɵɵdirectiveInject(i2.UserService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: TeacherOverviewComponent,\n    selectors: [[\"app-teacher-overview\"]],\n    decls: 4,\n    vars: 3,\n    consts: [[1, \"teacher-dashboard\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"dashboard-content\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"error-container\"], [\"color\", \"warn\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"dashboard-content\"], [1, \"dashboard-header\"], [1, \"welcome-section\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Refresh Data\", 3, \"click\"], [1, \"stats-grid\"], [1, \"stat-card\", \"classes\"], [1, \"stat-content\"], [1, \"stat-icon\"], [1, \"stat-info\"], [1, \"stat-card\", \"subjects\"], [1, \"stat-card\", \"students\"], [1, \"stat-card\", \"today-classes\"], [1, \"schedule-section\"], [1, \"schedule-row\"], [1, \"schedule-card\"], [\"class\", \"classes-list\", 4, \"ngIf\", \"ngIfElse\"], [\"noClassesToday\", \"\"], [1, \"upcoming-card\"], [\"class\", \"upcoming-class\", 4, \"ngIf\", \"ngIfElse\"], [\"noUpcomingClass\", \"\"], [1, \"recent-section\"], [\"class\", \"attendance-list\", 4, \"ngIf\", \"ngIfElse\"], [\"noRecentAttendance\", \"\"], [1, \"quick-actions\"], [1, \"action-buttons\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/dashboard/teacher/attendance\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"routerLink\", \"/dashboard/teacher/classes\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/dashboard/teacher/all-students\"], [1, \"classes-list\"], [\"class\", \"class-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"class-item\"], [1, \"time-slot\"], [1, \"time\"], [1, \"class-details\"], [1, \"department\"], [\"class\", \"room\", 4, \"ngIf\"], [1, \"room\"], [1, \"no-classes\"], [1, \"upcoming-class\"], [1, \"upcoming-time\"], [1, \"upcoming-details\"], [\"class\", \"upcoming-room\", 4, \"ngIf\"], [1, \"upcoming-room\"], [1, \"no-upcoming\"], [1, \"attendance-list\"], [\"class\", \"attendance-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"attendance-item\"], [1, \"student-info\"], [1, \"student-name\"], [1, \"student-reg\"], [1, \"attendance-details\"], [1, \"subject\"], [1, \"date\"], [1, \"attendance-status\"], [\"selected\", \"\", 3, \"color\"], [1, \"no-data\"]],\n    template: function TeacherOverviewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵtemplate(1, TeacherOverviewComponent_div_1_Template, 4, 0, \"div\", 1);\n        i0.ɵɵtemplate(2, TeacherOverviewComponent_div_2_Template, 9, 1, \"div\", 2);\n        i0.ɵɵtemplate(3, TeacherOverviewComponent_div_3_Template, 103, 15, \"div\", 3);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.teacherDashboard && !ctx.loading && !ctx.error);\n      }\n    },\n    dependencies: [i3.NgForOf, i3.NgIf, i4.RouterLink, i5.MatButton, i5.MatIconButton, i6.MatCard, i6.MatCardContent, i6.MatCardHeader, i6.MatCardSubtitle, i6.MatCardTitle, i7.MatChip, i8.MatIcon, i9.MatProgressSpinner, i10.MatTooltip, i3.TitleCasePipe, i3.DatePipe],\n    styles: [\".teacher-dashboard[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background-color: #f5f5f5;\\n  min-height: 100vh;\\n}\\n\\n.loading-container[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 400px;\\n  text-align: center;\\n}\\n\\n.error-container[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  margin-bottom: 16px;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 30px;\\n  background: white;\\n  padding: 20px;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\\n}\\n\\n.welcome-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #333;\\n  font-weight: 500;\\n}\\n\\n.welcome-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 8px 0 0 0;\\n  color: #666;\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 30px;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease-in-out;\\n}\\n\\n.stat-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 8px rgba(0,0,0,0.12);\\n}\\n\\n.stat-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n\\n.stat-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  color: white;\\n}\\n\\n.stat-card.classes[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n}\\n\\n.stat-card.subjects[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\\n}\\n\\n.stat-card.students[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\\n}\\n\\n.stat-card.today-classes[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\\n}\\n\\n.stat-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  height: 28px;\\n  width: 28px;\\n}\\n\\n.stat-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 2rem;\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.stat-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 4px 0 0 0;\\n  color: #666;\\n  font-size: 0.9rem;\\n}\\n\\n.schedule-section[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.schedule-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 2fr 1fr;\\n  gap: 20px;\\n}\\n\\n.schedule-card[_ngcontent-%COMP%], .upcoming-card[_ngcontent-%COMP%] {\\n  min-height: 300px;\\n}\\n\\n.classes-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n}\\n\\n.class-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  padding: 16px;\\n  background-color: #f9f9f9;\\n  border-radius: 8px;\\n  border-left: 4px solid #1976d2;\\n}\\n\\n.time-slot[_ngcontent-%COMP%] {\\n  min-width: 120px;\\n}\\n\\n.time[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #1976d2;\\n  font-size: 0.9rem;\\n}\\n\\n.class-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  color: #333;\\n  font-size: 1.1rem;\\n}\\n\\n.class-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 2px 0;\\n  color: #666;\\n  font-size: 0.9rem;\\n}\\n\\n.department[_ngcontent-%COMP%] {\\n  color: #888;\\n  font-style: italic;\\n}\\n\\n.room[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1976d2;\\n  padding: 2px 8px;\\n  border-radius: 12px;\\n  font-size: 0.8rem;\\n  display: inline-block;\\n  margin-top: 4px;\\n}\\n\\n.no-classes[_ngcontent-%COMP%], .no-upcoming[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px;\\n  text-align: center;\\n  color: #666;\\n}\\n\\n.no-classes[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%], .no-upcoming[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  margin-bottom: 16px;\\n  color: #ccc;\\n}\\n\\n.upcoming-class[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  text-align: center;\\n  padding: 20px;\\n}\\n\\n.upcoming-time[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 2.5rem;\\n  color: #1976d2;\\n  font-weight: 600;\\n}\\n\\n.upcoming-time[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 4px 0 16px 0;\\n  color: #666;\\n}\\n\\n.upcoming-details[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #333;\\n}\\n\\n.upcoming-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 4px 0;\\n  color: #666;\\n}\\n\\n.upcoming-room[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #2e7d32;\\n  padding: 4px 12px;\\n  border-radius: 16px;\\n  font-size: 0.9rem;\\n  margin-top: 8px;\\n}\\n\\n.recent-section[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.attendance-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n\\n.attendance-item[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 2fr 2fr 1fr;\\n  gap: 16px;\\n  align-items: center;\\n  padding: 12px;\\n  background-color: #f9f9f9;\\n  border-radius: 6px;\\n}\\n\\n.student-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.student-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n}\\n\\n.student-reg[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #666;\\n}\\n\\n.attendance-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.subject[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n  font-size: 0.9rem;\\n}\\n\\n.date[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #666;\\n}\\n\\n.attendance-status[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n}\\n\\n.no-data[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px;\\n  text-align: center;\\n  color: #666;\\n}\\n\\n.no-data[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  margin-bottom: 16px;\\n  color: #ccc;\\n}\\n\\n.quick-actions[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 24px;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\\n}\\n\\n.quick-actions[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0 0 20px 0;\\n  color: #333;\\n  font-weight: 500;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 12px;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n@media (max-width: 768px) {\\n  .teacher-dashboard[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  \\n  .dashboard-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 16px;\\n    align-items: stretch;\\n  }\\n  \\n  .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  \\n  .schedule-row[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  \\n  .attendance-item[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 8px;\\n  }\\n  \\n  .action-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  \\n  .action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n}\\n\\n\\n\\n@media (max-width: 576px) {\\n  .teacher-dashboard[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n\\n  .dashboard-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 15px;\\n    text-align: center;\\n    padding: 15px;\\n  }\\n\\n  .welcome-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  .welcome-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n\\n  .stats-grid[_ngcontent-%COMP%] {\\n    gap: 15px;\\n    margin-bottom: 20px;\\n  }\\n\\n  .stat-card[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n\\n  .stat-content[_ngcontent-%COMP%] {\\n    gap: 10px;\\n  }\\n\\n  .stat-icon[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n\\n  .stat-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  .stat-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  .stat-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 1.2rem;\\n  }\\n\\n  .quick-actions-grid[_ngcontent-%COMP%] {\\n    gap: 10px;\\n  }\\n\\n  .action-card[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n\\n  .action-icon[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n\\n  .action-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  .action-title[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n\\n  .action-description[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n\\n  .schedule-item[_ngcontent-%COMP%], .attendance-item[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n\\n  .schedule-time[_ngcontent-%COMP%], .attendance-class[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n\\n  .schedule-subject[_ngcontent-%COMP%], .attendance-subject[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n\\n  .schedule-class[_ngcontent-%COMP%], .attendance-date[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "TeacherOverviewComponent_div_2_Template_button_click_5_listener", "ɵɵrestoreView", "_r4", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "refreshData", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "error", "ɵɵtextInterpolate1", "classItem_r15", "room", "ɵɵtemplate", "TeacherOverviewComponent_div_3_div_65_div_1_span_11_Template", "ɵɵtextInterpolate2", "timeSlot", "startTime", "endTime", "subject", "subjectName", "class", "className", "section", "department", "name", "ɵɵproperty", "TeacherOverviewComponent_div_3_div_65_div_1_Template", "ctx_r5", "getTodayClasses", "ctx_r18", "getUpcomingClass", "TeacherOverviewComponent_div_3_div_73_p_11_Template", "ctx_r8", "attendance_r20", "student", "regNo", "ɵɵpipeBind2", "date", "status", "ɵɵpipeBind1", "TeacherOverviewComponent_div_3_div_84_div_1_Template", "ctx_r11", "teacherDashboard", "recentAttendance", "slice", "TeacherOverviewComponent_div_3_Template_button_click_7_listener", "_r22", "ctx_r21", "TeacherOverviewComponent_div_3_div_65_Template", "TeacherOverviewComponent_div_3_ng_template_66_Template", "ɵɵtemplateRefExtractor", "TeacherOverviewComponent_div_3_div_73_Template", "TeacherOverviewComponent_div_3_ng_template_74_Template", "TeacherOverviewComponent_div_3_div_84_Template", "TeacherOverviewComponent_div_3_ng_template_85_Template", "ctx_r2", "currentUser", "stats", "totalClasses", "totalSubjects", "totalStudents", "length", "currentDate", "_r6", "_r9", "_r12", "TeacherOverviewComponent", "constructor", "dashboardService", "userService", "loading", "Date", "ngOnInit", "getUserFromLocalStorage", "user", "loadTeacherDashboard", "getTeacherDashboard", "_id", "subscribe", "next", "response", "success", "dashboard", "console", "timetable", "today", "toLocaleDateString", "weekday", "filter", "entry", "dayOfWeek", "todayClasses", "now", "currentTime", "getHours", "getMinutes", "find", "classItem", "hours", "minutes", "split", "map", "Number", "classTime", "_", "ɵɵdirectiveInject", "i1", "DashboardService", "i2", "UserService", "_2", "selectors", "decls", "vars", "consts", "template", "TeacherOverviewComponent_Template", "rf", "ctx", "TeacherOverviewComponent_div_1_Template", "TeacherOverviewComponent_div_2_Template", "TeacherOverviewComponent_div_3_Template"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\teacher-dashboard\\teacher-overview\\teacher-overview.component.ts", "D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\teacher-dashboard\\teacher-overview\\teacher-overview.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { DashboardService } from '../../../services/dashboard.service';\r\nimport { UserService } from '../../../services/user.service';\r\nimport { TeacherDashboard } from '../../../models/dashboard';\r\n\r\n@Component({\r\n  selector: 'app-teacher-overview',\r\n  templateUrl: './teacher-overview.component.html',\r\n  styleUrls: ['./teacher-overview.component.css']\r\n})\r\nexport class TeacherOverviewComponent implements OnInit {\r\n  teacherDashboard: TeacherDashboard | null = null;\r\n  loading = true;\r\n  error: string | null = null;\r\n  currentUser: any;\r\n  currentDate = new Date();\r\n\r\n  constructor(\r\n    private dashboardService: DashboardService,\r\n    private userService: UserService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.currentUser = this.userService.getUserFromLocalStorage()?.user;\r\n    if (this.currentUser) {\r\n      this.loadTeacherDashboard();\r\n    } else {\r\n      this.error = 'User not found';\r\n      this.loading = false;\r\n    }\r\n  }\r\n\r\n  loadTeacherDashboard(): void {\r\n    this.loading = true;\r\n    this.error = null;\r\n\r\n    this.dashboardService.getTeacherDashboard(this.currentUser._id).subscribe({\r\n      next: (response) => {\r\n        if (response.success) {\r\n          this.teacherDashboard = response.dashboard;\r\n        } else {\r\n          this.error = 'Failed to load teacher dashboard';\r\n        }\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading teacher dashboard:', error);\r\n        this.error = 'Error loading dashboard data';\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  refreshData(): void {\r\n    this.loadTeacherDashboard();\r\n  }\r\n\r\n  getTodayClasses(): any[] {\r\n    if (!this.teacherDashboard?.timetable) return [];\r\n    \r\n    const today = new Date().toLocaleDateString('en-US', { weekday: 'long' });\r\n    return this.teacherDashboard.timetable.filter(entry => entry.dayOfWeek === today);\r\n  }\r\n\r\n  getUpcomingClass(): any {\r\n    const todayClasses = this.getTodayClasses();\r\n    const now = new Date();\r\n    const currentTime = now.getHours() * 60 + now.getMinutes();\r\n\r\n    return todayClasses.find(classItem => {\r\n      const [hours, minutes] = classItem.timeSlot.startTime.split(':').map(Number);\r\n      const classTime = hours * 60 + minutes;\r\n      return classTime > currentTime;\r\n    });\r\n  }\r\n}\r\n", "<div class=\"teacher-dashboard\">\r\n  <!-- Loading State -->\r\n  <div *ngIf=\"loading\" class=\"loading-container\">\r\n    <mat-spinner></mat-spinner>\r\n    <p>Loading your dashboard...</p>\r\n  </div>\r\n\r\n  <!-- Error State -->\r\n  <div *ngIf=\"error && !loading\" class=\"error-container\">\r\n    <mat-icon color=\"warn\">error</mat-icon>\r\n    <p>{{ error }}</p>\r\n    <button mat-raised-button color=\"primary\" (click)=\"refreshData()\">\r\n      <mat-icon>refresh</mat-icon>\r\n      Retry\r\n    </button>\r\n  </div>\r\n\r\n  <!-- Dashboard Content -->\r\n  <div *ngIf=\"teacherDashboard && !loading && !error\" class=\"dashboard-content\">\r\n    <!-- Header -->\r\n    <div class=\"dashboard-header\">\r\n      <div class=\"welcome-section\">\r\n        <h1>Welcome back, {{ currentUser?.name }}!</h1>\r\n        <p>Here's your teaching overview for today</p>\r\n      </div>\r\n      <button mat-icon-button (click)=\"refreshData()\" matTooltip=\"Refresh Data\">\r\n        <mat-icon>refresh</mat-icon>\r\n      </button>\r\n    </div>\r\n\r\n    <!-- Statistics Cards -->\r\n    <div class=\"stats-grid\">\r\n      <mat-card class=\"stat-card classes\">\r\n        <mat-card-content>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon\">\r\n              <mat-icon>class</mat-icon>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ teacherDashboard.stats.totalClasses }}</h3>\r\n              <p>My Classes</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n\r\n      <mat-card class=\"stat-card subjects\">\r\n        <mat-card-content>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon\">\r\n              <mat-icon>subject</mat-icon>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ teacherDashboard.stats.totalSubjects }}</h3>\r\n              <p>Subjects Teaching</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n\r\n      <mat-card class=\"stat-card students\">\r\n        <mat-card-content>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon\">\r\n              <mat-icon>school</mat-icon>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ teacherDashboard.stats.totalStudents }}</h3>\r\n              <p>Total Students</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n\r\n      <mat-card class=\"stat-card today-classes\">\r\n        <mat-card-content>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon\">\r\n              <mat-icon>today</mat-icon>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ getTodayClasses().length }}</h3>\r\n              <p>Classes Today</p>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n    </div>\r\n\r\n    <!-- Today's Schedule and Upcoming Class -->\r\n    <div class=\"schedule-section\">\r\n      <div class=\"schedule-row\">\r\n        <!-- Today's Classes -->\r\n        <mat-card class=\"schedule-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Today's Schedule</mat-card-title>\r\n            <mat-card-subtitle>{{ currentDate | date:'fullDate' }}</mat-card-subtitle>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div *ngIf=\"getTodayClasses().length > 0; else noClassesToday\" class=\"classes-list\">\r\n              <div *ngFor=\"let classItem of getTodayClasses()\" class=\"class-item\">\r\n                <div class=\"time-slot\">\r\n                  <span class=\"time\">{{ classItem.timeSlot.startTime }} - {{ classItem.timeSlot.endTime }}</span>\r\n                </div>\r\n                <div class=\"class-details\">\r\n                  <h4>{{ classItem.subject.subjectName }}</h4>\r\n                  <p>{{ classItem.class.className }} - {{ classItem.class.section }}</p>\r\n                  <p class=\"department\">{{ classItem.department.name }}</p>\r\n                  <span class=\"room\" *ngIf=\"classItem.room\">Room: {{ classItem.room }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <ng-template #noClassesToday>\r\n              <div class=\"no-classes\">\r\n                <mat-icon>event_available</mat-icon>\r\n                <p>No classes scheduled for today</p>\r\n              </div>\r\n            </ng-template>\r\n          </mat-card-content>\r\n        </mat-card>\r\n\r\n        <!-- Upcoming Class -->\r\n        <mat-card class=\"upcoming-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Next Class</mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div *ngIf=\"getUpcomingClass(); else noUpcomingClass\" class=\"upcoming-class\">\r\n              <div class=\"upcoming-time\">\r\n                <h2>{{ getUpcomingClass().timeSlot.startTime }}</h2>\r\n                <p>{{ getUpcomingClass().timeSlot.endTime }}</p>\r\n              </div>\r\n              <div class=\"upcoming-details\">\r\n                <h3>{{ getUpcomingClass().subject.subjectName }}</h3>\r\n                <p>{{ getUpcomingClass().class.className }} - {{ getUpcomingClass().class.section }}</p>\r\n                <p class=\"upcoming-room\" *ngIf=\"getUpcomingClass().room\">{{ getUpcomingClass().room }}</p>\r\n              </div>\r\n            </div>\r\n            <ng-template #noUpcomingClass>\r\n              <div class=\"no-upcoming\">\r\n                <mat-icon>schedule</mat-icon>\r\n                <p>No more classes today</p>\r\n              </div>\r\n            </ng-template>\r\n          </mat-card-content>\r\n        </mat-card>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Recent Attendance -->\r\n    <div class=\"recent-section\">\r\n      <mat-card>\r\n        <mat-card-header>\r\n          <mat-card-title>Recent Attendance</mat-card-title>\r\n          <mat-card-subtitle>Last 5 attendance records</mat-card-subtitle>\r\n        </mat-card-header>\r\n        <mat-card-content>\r\n          <div *ngIf=\"teacherDashboard.recentAttendance.length > 0; else noRecentAttendance\" class=\"attendance-list\">\r\n            <div *ngFor=\"let attendance of teacherDashboard.recentAttendance.slice(0, 5)\" class=\"attendance-item\">\r\n              <div class=\"student-info\">\r\n                <span class=\"student-name\">{{ attendance.student.name }}</span>\r\n                <span class=\"student-reg\">{{ attendance.student.regNo }}</span>\r\n              </div>\r\n              <div class=\"attendance-details\">\r\n                <span class=\"subject\">{{ attendance.subject.subjectName }}</span>\r\n                <span class=\"date\">{{ attendance.date | date:'shortDate' }}</span>\r\n              </div>\r\n              <div class=\"attendance-status\">\r\n                <mat-chip [color]=\"attendance.status === 'present' ? 'primary' : 'warn'\" selected>\r\n                  {{ attendance.status | titlecase }}\r\n                </mat-chip>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <ng-template #noRecentAttendance>\r\n            <div class=\"no-data\">\r\n              <mat-icon>fact_check</mat-icon>\r\n              <p>No recent attendance records</p>\r\n            </div>\r\n          </ng-template>\r\n        </mat-card-content>\r\n      </mat-card>\r\n    </div>\r\n\r\n    <!-- Quick Actions -->\r\n    <div class=\"quick-actions\">\r\n      <h2>Quick Actions</h2>\r\n      <div class=\"action-buttons\">\r\n        <button mat-raised-button color=\"primary\" routerLink=\"/dashboard/teacher/attendance\">\r\n          <mat-icon>fact_check</mat-icon>\r\n          Mark Attendance\r\n        </button>\r\n        <button mat-raised-button color=\"accent\" routerLink=\"/dashboard/teacher/classes\">\r\n          <mat-icon>class</mat-icon>\r\n          View My Classes\r\n        </button>\r\n        <button mat-raised-button color=\"primary\" routerLink=\"/dashboard/teacher/all-students\">\r\n          <mat-icon>school</mat-icon>\r\n          View Students\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": ";;;;;;;;;;;;;ICEEA,EAAA,CAAAC,cAAA,aAA+C;IAC7CD,EAAA,CAAAE,SAAA,kBAA2B;IAC3BF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,gCAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;;IAIlCJ,EAAA,CAAAC,cAAA,aAAuD;IAC9BD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACvCJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAW;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAClBJ,EAAA,CAAAC,cAAA,gBAAkE;IAAxBD,EAAA,CAAAK,UAAA,mBAAAC,gEAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAC/DZ,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC5BJ,EAAA,CAAAG,MAAA,cACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAJNJ,EAAA,CAAAa,SAAA,GAAW;IAAXb,EAAA,CAAAc,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IAkGAhB,EAAA,CAAAC,cAAA,eAA0C;IAAAD,EAAA,CAAAG,MAAA,GAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAAjCJ,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAiB,kBAAA,WAAAC,aAAA,CAAAC,IAAA,KAA0B;;;;;IARxEnB,EAAA,CAAAC,cAAA,cAAoE;IAE7CD,EAAA,CAAAG,MAAA,GAAqE;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEjGJ,EAAA,CAAAC,cAAA,cAA2B;IACrBD,EAAA,CAAAG,MAAA,GAAmC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC5CJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAA+D;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACtEJ,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAG,MAAA,IAA+B;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACzDJ,EAAA,CAAAoB,UAAA,KAAAC,4DAAA,mBAA2E;IAC7ErB,EAAA,CAAAI,YAAA,EAAM;;;;IAPeJ,EAAA,CAAAa,SAAA,GAAqE;IAArEb,EAAA,CAAAsB,kBAAA,KAAAJ,aAAA,CAAAK,QAAA,CAAAC,SAAA,SAAAN,aAAA,CAAAK,QAAA,CAAAE,OAAA,KAAqE;IAGpFzB,EAAA,CAAAa,SAAA,GAAmC;IAAnCb,EAAA,CAAAc,iBAAA,CAAAI,aAAA,CAAAQ,OAAA,CAAAC,WAAA,CAAmC;IACpC3B,EAAA,CAAAa,SAAA,GAA+D;IAA/Db,EAAA,CAAAsB,kBAAA,KAAAJ,aAAA,CAAAU,KAAA,CAAAC,SAAA,SAAAX,aAAA,CAAAU,KAAA,CAAAE,OAAA,KAA+D;IAC5C9B,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAAc,iBAAA,CAAAI,aAAA,CAAAa,UAAA,CAAAC,IAAA,CAA+B;IACjChC,EAAA,CAAAa,SAAA,GAAoB;IAApBb,EAAA,CAAAiC,UAAA,SAAAf,aAAA,CAAAC,IAAA,CAAoB;;;;;IAT9CnB,EAAA,CAAAC,cAAA,cAAoF;IAClFD,EAAA,CAAAoB,UAAA,IAAAc,oDAAA,mBAUM;IACRlC,EAAA,CAAAI,YAAA,EAAM;;;;IAXuBJ,EAAA,CAAAa,SAAA,GAAoB;IAApBb,EAAA,CAAAiC,UAAA,YAAAE,MAAA,CAAAC,eAAA,GAAoB;;;;;IAa/CpC,EAAA,CAAAC,cAAA,cAAwB;IACZD,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACpCJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,qCAA8B;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAoBrCJ,EAAA,CAAAC,cAAA,YAAyD;IAAAD,EAAA,CAAAG,MAAA,GAA6B;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IAAjCJ,EAAA,CAAAa,SAAA,GAA6B;IAA7Bb,EAAA,CAAAc,iBAAA,CAAAuB,OAAA,CAAAC,gBAAA,GAAAnB,IAAA,CAA6B;;;;;IAR1FnB,EAAA,CAAAC,cAAA,cAA6E;IAErED,EAAA,CAAAG,MAAA,GAA2C;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACpDJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAyC;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAElDJ,EAAA,CAAAC,cAAA,cAA8B;IACxBD,EAAA,CAAAG,MAAA,GAA4C;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACrDJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,IAAiF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACxFJ,EAAA,CAAAoB,UAAA,KAAAmB,mDAAA,gBAA0F;IAC5FvC,EAAA,CAAAI,YAAA,EAAM;;;;IAPAJ,EAAA,CAAAa,SAAA,GAA2C;IAA3Cb,EAAA,CAAAc,iBAAA,CAAA0B,MAAA,CAAAF,gBAAA,GAAAf,QAAA,CAAAC,SAAA,CAA2C;IAC5CxB,EAAA,CAAAa,SAAA,GAAyC;IAAzCb,EAAA,CAAAc,iBAAA,CAAA0B,MAAA,CAAAF,gBAAA,GAAAf,QAAA,CAAAE,OAAA,CAAyC;IAGxCzB,EAAA,CAAAa,SAAA,GAA4C;IAA5Cb,EAAA,CAAAc,iBAAA,CAAA0B,MAAA,CAAAF,gBAAA,GAAAZ,OAAA,CAAAC,WAAA,CAA4C;IAC7C3B,EAAA,CAAAa,SAAA,GAAiF;IAAjFb,EAAA,CAAAsB,kBAAA,KAAAkB,MAAA,CAAAF,gBAAA,GAAAV,KAAA,CAAAC,SAAA,SAAAW,MAAA,CAAAF,gBAAA,GAAAV,KAAA,CAAAE,OAAA,KAAiF;IAC1D9B,EAAA,CAAAa,SAAA,GAA6B;IAA7Bb,EAAA,CAAAiC,UAAA,SAAAO,MAAA,CAAAF,gBAAA,GAAAnB,IAAA,CAA6B;;;;;IAIzDnB,EAAA,CAAAC,cAAA,cAAyB;IACbD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC7BJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,4BAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAiBhCJ,EAAA,CAAAC,cAAA,cAAsG;IAEvED,EAAA,CAAAG,MAAA,GAA6B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC/DJ,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAG,MAAA,GAA8B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEjEJ,EAAA,CAAAC,cAAA,cAAgC;IACRD,EAAA,CAAAG,MAAA,GAAoC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACjEJ,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAAG,MAAA,IAAwC;;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEpEJ,EAAA,CAAAC,cAAA,eAA+B;IAE3BD,EAAA,CAAAG,MAAA,IACF;;IAAAH,EAAA,CAAAI,YAAA,EAAW;;;;IAVgBJ,EAAA,CAAAa,SAAA,GAA6B;IAA7Bb,EAAA,CAAAc,iBAAA,CAAA2B,cAAA,CAAAC,OAAA,CAAAV,IAAA,CAA6B;IAC9BhC,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAAc,iBAAA,CAAA2B,cAAA,CAAAC,OAAA,CAAAC,KAAA,CAA8B;IAGlC3C,EAAA,CAAAa,SAAA,GAAoC;IAApCb,EAAA,CAAAc,iBAAA,CAAA2B,cAAA,CAAAf,OAAA,CAAAC,WAAA,CAAoC;IACvC3B,EAAA,CAAAa,SAAA,GAAwC;IAAxCb,EAAA,CAAAc,iBAAA,CAAAd,EAAA,CAAA4C,WAAA,QAAAH,cAAA,CAAAI,IAAA,eAAwC;IAGjD7C,EAAA,CAAAa,SAAA,GAA8D;IAA9Db,EAAA,CAAAiC,UAAA,UAAAQ,cAAA,CAAAK,MAAA,oCAA8D;IACtE9C,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAiB,kBAAA,MAAAjB,EAAA,CAAA+C,WAAA,QAAAN,cAAA,CAAAK,MAAA,OACF;;;;;IAbN9C,EAAA,CAAAC,cAAA,cAA2G;IACzGD,EAAA,CAAAoB,UAAA,IAAA4B,oDAAA,oBAcM;IACRhD,EAAA,CAAAI,YAAA,EAAM;;;;IAfwBJ,EAAA,CAAAa,SAAA,GAAgD;IAAhDb,EAAA,CAAAiC,UAAA,YAAAgB,OAAA,CAAAC,gBAAA,CAAAC,gBAAA,CAAAC,KAAA,OAAgD;;;;;IAiB5EpD,EAAA,CAAAC,cAAA,cAAqB;IACTD,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC/BJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,mCAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;;IA/J/CJ,EAAA,CAAAC,cAAA,aAA8E;IAIpED,EAAA,CAAAG,MAAA,GAAsC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC/CJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,8CAAuC;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAEhDJ,EAAA,CAAAC,cAAA,iBAA0E;IAAlDD,EAAA,CAAAK,UAAA,mBAAAgD,gEAAA;MAAArD,EAAA,CAAAO,aAAA,CAAA+C,IAAA;MAAA,MAAAC,OAAA,GAAAvD,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAA4C,OAAA,CAAA3C,WAAA,EAAa;IAAA,EAAC;IAC7CZ,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAKhCJ,EAAA,CAAAC,cAAA,eAAwB;IAKJD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAE5BJ,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAG,MAAA,IAAyC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAClDJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAMzBJ,EAAA,CAAAC,cAAA,oBAAqC;IAInBD,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAE9BJ,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAG,MAAA,IAA0C;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACnDJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,yBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAMhCJ,EAAA,CAAAC,cAAA,oBAAqC;IAInBD,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAE7BJ,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAG,MAAA,IAA0C;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACnDJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,sBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAM7BJ,EAAA,CAAAC,cAAA,oBAA0C;IAIxBD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAE5BJ,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAG,MAAA,IAA8B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACvCJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAQ9BJ,EAAA,CAAAC,cAAA,eAA8B;IAKND,EAAA,CAAAG,MAAA,wBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IACjDJ,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAG,MAAA,IAAmC;;IAAAH,EAAA,CAAAI,YAAA,EAAoB;IAE5EJ,EAAA,CAAAC,cAAA,wBAAkB;IAChBD,EAAA,CAAAoB,UAAA,KAAAoC,8CAAA,kBAYM;IACNxD,EAAA,CAAAoB,UAAA,KAAAqC,sDAAA,iCAAAzD,EAAA,CAAA0D,sBAAA,CAKc;IAChB1D,EAAA,CAAAI,YAAA,EAAmB;IAIrBJ,EAAA,CAAAC,cAAA,oBAAgC;IAEZD,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IAE7CJ,EAAA,CAAAC,cAAA,wBAAkB;IAChBD,EAAA,CAAAoB,UAAA,KAAAuC,8CAAA,mBAUM;IACN3D,EAAA,CAAAoB,UAAA,KAAAwC,sDAAA,iCAAA5D,EAAA,CAAA0D,sBAAA,CAKc;IAChB1D,EAAA,CAAAI,YAAA,EAAmB;IAMzBJ,EAAA,CAAAC,cAAA,eAA4B;IAGND,EAAA,CAAAG,MAAA,yBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IAClDJ,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAG,MAAA,iCAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAoB;IAElEJ,EAAA,CAAAC,cAAA,wBAAkB;IAChBD,EAAA,CAAAoB,UAAA,KAAAyC,8CAAA,kBAgBM;IACN7D,EAAA,CAAAoB,UAAA,KAAA0C,sDAAA,iCAAA9D,EAAA,CAAA0D,sBAAA,CAKc;IAChB1D,EAAA,CAAAI,YAAA,EAAmB;IAKvBJ,EAAA,CAAAC,cAAA,eAA2B;IACrBD,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACtBJ,EAAA,CAAAC,cAAA,eAA4B;IAEdD,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC/BJ,EAAA,CAAAG,MAAA,yBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAAiF;IACrED,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC1BJ,EAAA,CAAAG,MAAA,yBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAAuF;IAC3ED,EAAA,CAAAG,MAAA,eAAM;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC3BJ,EAAA,CAAAG,MAAA,wBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;;;IAjLLJ,EAAA,CAAAa,SAAA,GAAsC;IAAtCb,EAAA,CAAAiB,kBAAA,mBAAA8C,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAhC,IAAA,MAAsC;IAiBhChC,EAAA,CAAAa,SAAA,IAAyC;IAAzCb,EAAA,CAAAc,iBAAA,CAAAiD,MAAA,CAAAb,gBAAA,CAAAe,KAAA,CAAAC,YAAA,CAAyC;IAczClE,EAAA,CAAAa,SAAA,IAA0C;IAA1Cb,EAAA,CAAAc,iBAAA,CAAAiD,MAAA,CAAAb,gBAAA,CAAAe,KAAA,CAAAE,aAAA,CAA0C;IAc1CnE,EAAA,CAAAa,SAAA,IAA0C;IAA1Cb,EAAA,CAAAc,iBAAA,CAAAiD,MAAA,CAAAb,gBAAA,CAAAe,KAAA,CAAAG,aAAA,CAA0C;IAc1CpE,EAAA,CAAAa,SAAA,IAA8B;IAA9Bb,EAAA,CAAAc,iBAAA,CAAAiD,MAAA,CAAA3B,eAAA,GAAAiC,MAAA,CAA8B;IAejBrE,EAAA,CAAAa,SAAA,IAAmC;IAAnCb,EAAA,CAAAc,iBAAA,CAAAd,EAAA,CAAA4C,WAAA,SAAAmB,MAAA,CAAAO,WAAA,cAAmC;IAGhDtE,EAAA,CAAAa,SAAA,GAAoC;IAApCb,EAAA,CAAAiC,UAAA,SAAA8B,MAAA,CAAA3B,eAAA,GAAAiC,MAAA,KAAoC,aAAAE,GAAA;IA4BpCvE,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAiC,UAAA,SAAA8B,MAAA,CAAAzB,gBAAA,GAA0B,aAAAkC,GAAA;IA8B5BxE,EAAA,CAAAa,SAAA,IAAoD;IAApDb,EAAA,CAAAiC,UAAA,SAAA8B,MAAA,CAAAb,gBAAA,CAAAC,gBAAA,CAAAkB,MAAA,KAAoD,aAAAI,IAAA;;;ADnJpE,OAAM,MAAOC,wBAAwB;EAOnCC,YACUC,gBAAkC,EAClCC,WAAwB;IADxB,KAAAD,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,WAAW,GAAXA,WAAW;IARrB,KAAA3B,gBAAgB,GAA4B,IAAI;IAChD,KAAA4B,OAAO,GAAG,IAAI;IACd,KAAA9D,KAAK,GAAkB,IAAI;IAE3B,KAAAsD,WAAW,GAAG,IAAIS,IAAI,EAAE;EAKrB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAAChB,WAAW,GAAG,IAAI,CAACa,WAAW,CAACI,uBAAuB,EAAE,EAAEC,IAAI;IACnE,IAAI,IAAI,CAAClB,WAAW,EAAE;MACpB,IAAI,CAACmB,oBAAoB,EAAE;KAC5B,MAAM;MACL,IAAI,CAACnE,KAAK,GAAG,gBAAgB;MAC7B,IAAI,CAAC8D,OAAO,GAAG,KAAK;;EAExB;EAEAK,oBAAoBA,CAAA;IAClB,IAAI,CAACL,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC9D,KAAK,GAAG,IAAI;IAEjB,IAAI,CAAC4D,gBAAgB,CAACQ,mBAAmB,CAAC,IAAI,CAACpB,WAAW,CAACqB,GAAG,CAAC,CAACC,SAAS,CAAC;MACxEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACvC,gBAAgB,GAAGsC,QAAQ,CAACE,SAAS;SAC3C,MAAM;UACL,IAAI,CAAC1E,KAAK,GAAG,kCAAkC;;QAEjD,IAAI,CAAC8D,OAAO,GAAG,KAAK;MACtB,CAAC;MACD9D,KAAK,EAAGA,KAAK,IAAI;QACf2E,OAAO,CAAC3E,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,IAAI,CAACA,KAAK,GAAG,8BAA8B;QAC3C,IAAI,CAAC8D,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAlE,WAAWA,CAAA;IACT,IAAI,CAACuE,oBAAoB,EAAE;EAC7B;EAEA/C,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACc,gBAAgB,EAAE0C,SAAS,EAAE,OAAO,EAAE;IAEhD,MAAMC,KAAK,GAAG,IAAId,IAAI,EAAE,CAACe,kBAAkB,CAAC,OAAO,EAAE;MAAEC,OAAO,EAAE;IAAM,CAAE,CAAC;IACzE,OAAO,IAAI,CAAC7C,gBAAgB,CAAC0C,SAAS,CAACI,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,SAAS,KAAKL,KAAK,CAAC;EACnF;EAEAvD,gBAAgBA,CAAA;IACd,MAAM6D,YAAY,GAAG,IAAI,CAAC/D,eAAe,EAAE;IAC3C,MAAMgE,GAAG,GAAG,IAAIrB,IAAI,EAAE;IACtB,MAAMsB,WAAW,GAAGD,GAAG,CAACE,QAAQ,EAAE,GAAG,EAAE,GAAGF,GAAG,CAACG,UAAU,EAAE;IAE1D,OAAOJ,YAAY,CAACK,IAAI,CAACC,SAAS,IAAG;MACnC,MAAM,CAACC,KAAK,EAAEC,OAAO,CAAC,GAAGF,SAAS,CAAClF,QAAQ,CAACC,SAAS,CAACoF,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC;MAC5E,MAAMC,SAAS,GAAGL,KAAK,GAAG,EAAE,GAAGC,OAAO;MACtC,OAAOI,SAAS,GAAGV,WAAW;IAChC,CAAC,CAAC;EACJ;EAAC,QAAAW,CAAA,G;qBAhEUtC,wBAAwB,EAAA1E,EAAA,CAAAiH,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAnH,EAAA,CAAAiH,iBAAA,CAAAG,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAxB5C,wBAAwB;IAAA6C,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCVrC7H,EAAA,CAAAC,cAAA,aAA+B;QAE7BD,EAAA,CAAAoB,UAAA,IAAA2G,uCAAA,iBAGM;QAGN/H,EAAA,CAAAoB,UAAA,IAAA4G,uCAAA,iBAOM;QAGNhI,EAAA,CAAAoB,UAAA,IAAA6G,uCAAA,oBAwLM;QACRjI,EAAA,CAAAI,YAAA,EAAM;;;QAzMEJ,EAAA,CAAAa,SAAA,GAAa;QAAbb,EAAA,CAAAiC,UAAA,SAAA6F,GAAA,CAAAhD,OAAA,CAAa;QAMb9E,EAAA,CAAAa,SAAA,GAAuB;QAAvBb,EAAA,CAAAiC,UAAA,SAAA6F,GAAA,CAAA9G,KAAA,KAAA8G,GAAA,CAAAhD,OAAA,CAAuB;QAUvB9E,EAAA,CAAAa,SAAA,GAA4C;QAA5Cb,EAAA,CAAAiC,UAAA,SAAA6F,GAAA,CAAA5E,gBAAA,KAAA4E,GAAA,CAAAhD,OAAA,KAAAgD,GAAA,CAAA9G,KAAA,CAA4C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}