{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/user.service\";\nimport * as i2 from \"../services/role.service\";\nimport * as i3 from \"@angular/router\";\nexport class AuthGuard {\n  constructor(userService, roleService, router) {\n    this.userService = userService;\n    this.roleService = roleService;\n    this.router = router;\n  }\n  canActivate(route, state) {\n    // Check if user is authenticated\n    if (!this.userService.isAuthenticated()) {\n      this.router.navigate(['/auth/login']);\n      return false;\n    }\n    // Get required roles from route data\n    const requiredRoles = route.data['roles'];\n    if (requiredRoles) {\n      const currentUser = this.userService.getUserFromLocalStorage();\n      const userRole = currentUser?.user?.role;\n      // Check if user has required role\n      if (requiredRoles.includes(userRole)) {\n        return true;\n      } else {\n        // Redirect to appropriate dashboard based on user role\n        this.redirectBasedOnRole(userRole);\n        return false;\n      }\n    }\n    return true;\n  }\n  redirectBasedOnRole(role) {\n    switch (role) {\n      case 'Principal':\n        this.router.navigate(['/dashboard/admin']);\n        break;\n      case 'Teacher':\n        this.router.navigate(['/dashboard/teacher']);\n        break;\n      case 'Student':\n        this.router.navigate(['/dashboard/student']);\n        break;\n      default:\n        this.router.navigate(['/auth/login']);\n        break;\n    }\n  }\n  static #_ = this.ɵfac = function AuthGuard_Factory(t) {\n    return new (t || AuthGuard)(i0.ɵɵinject(i1.UserService), i0.ɵɵinject(i2.RoleService), i0.ɵɵinject(i3.Router));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthGuard,\n    factory: AuthGuard.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "constructor", "userService", "roleService", "router", "canActivate", "route", "state", "isAuthenticated", "navigate", "requiredRoles", "data", "currentUser", "getUserFromLocalStorage", "userRole", "user", "role", "includes", "redirectBasedOnRole", "_", "i0", "ɵɵinject", "i1", "UserService", "i2", "RoleService", "i3", "Router", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\guards\\auth.guard.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';\r\nimport { Observable } from 'rxjs';\r\nimport { UserService } from '../services/user.service';\r\nimport { RoleService } from '../services/role.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AuthGuard implements CanActivate {\r\n\r\n  constructor(\r\n    private userService: UserService,\r\n    private roleService: RoleService,\r\n    private router: Router\r\n  ) {}\r\n\r\n  canActivate(\r\n    route: ActivatedRouteSnapshot,\r\n    state: RouterStateSnapshot\r\n  ): Observable<boolean> | Promise<boolean> | boolean {\r\n    \r\n    // Check if user is authenticated\r\n    if (!this.userService.isAuthenticated()) {\r\n      this.router.navigate(['/auth/login']);\r\n      return false;\r\n    }\r\n\r\n    // Get required roles from route data\r\n    const requiredRoles = route.data['roles'] as Array<string>;\r\n    \r\n    if (requiredRoles) {\r\n      const currentUser = this.userService.getUserFromLocalStorage();\r\n      const userRole = currentUser?.user?.role;\r\n\r\n      // Check if user has required role\r\n      if (requiredRoles.includes(userRole)) {\r\n        return true;\r\n      } else {\r\n        // Redirect to appropriate dashboard based on user role\r\n        this.redirectBasedOnRole(userRole);\r\n        return false;\r\n      }\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  private redirectBasedOnRole(role: string): void {\r\n    switch (role) {\r\n      case 'Principal':\r\n        this.router.navigate(['/dashboard/admin']);\r\n        break;\r\n      case 'Teacher':\r\n        this.router.navigate(['/dashboard/teacher']);\r\n        break;\r\n      case 'Student':\r\n        this.router.navigate(['/dashboard/student']);\r\n        break;\r\n      default:\r\n        this.router.navigate(['/auth/login']);\r\n        break;\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;;AASA,OAAM,MAAOA,SAAS;EAEpBC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc;IAFd,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHC,WAAWA,CACTC,KAA6B,EAC7BC,KAA0B;IAG1B;IACA,IAAI,CAAC,IAAI,CAACL,WAAW,CAACM,eAAe,EAAE,EAAE;MACvC,IAAI,CAACJ,MAAM,CAACK,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;MACrC,OAAO,KAAK;;IAGd;IACA,MAAMC,aAAa,GAAGJ,KAAK,CAACK,IAAI,CAAC,OAAO,CAAkB;IAE1D,IAAID,aAAa,EAAE;MACjB,MAAME,WAAW,GAAG,IAAI,CAACV,WAAW,CAACW,uBAAuB,EAAE;MAC9D,MAAMC,QAAQ,GAAGF,WAAW,EAAEG,IAAI,EAAEC,IAAI;MAExC;MACA,IAAIN,aAAa,CAACO,QAAQ,CAACH,QAAQ,CAAC,EAAE;QACpC,OAAO,IAAI;OACZ,MAAM;QACL;QACA,IAAI,CAACI,mBAAmB,CAACJ,QAAQ,CAAC;QAClC,OAAO,KAAK;;;IAIhB,OAAO,IAAI;EACb;EAEQI,mBAAmBA,CAACF,IAAY;IACtC,QAAQA,IAAI;MACV,KAAK,WAAW;QACd,IAAI,CAACZ,MAAM,CAACK,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;QAC1C;MACF,KAAK,SAAS;QACZ,IAAI,CAACL,MAAM,CAACK,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;QAC5C;MACF,KAAK,SAAS;QACZ,IAAI,CAACL,MAAM,CAACK,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;QAC5C;MACF;QACE,IAAI,CAACL,MAAM,CAACK,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;QACrC;;EAEN;EAAC,QAAAU,CAAA,G;qBAtDUnB,SAAS,EAAAoB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAT5B,SAAS;IAAA6B,OAAA,EAAT7B,SAAS,CAAA8B,IAAA;IAAAC,UAAA,EAFR;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}