{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/user.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/forms\";\nconst _c0 = [\"fileInput\"];\nexport let TeacherFormComponent = /*#__PURE__*/(() => {\n  class TeacherFormComponent {\n    constructor(userService, router) {\n      this.userService = userService;\n      this.router = router;\n      this.selectedFile = null;\n      // Teacher model with all required fields\n      this.teacher = {\n        name: '',\n        father_name: '',\n        contact: '',\n        department: '',\n        designation: '',\n        position: '',\n        isVisiting: false,\n        email: '',\n        password: '',\n        role: 'Teacher' // Set role to Teacher by default\n      };\n    }\n    // File selection handler\n    onFileSelected(event) {\n      const input = event.target;\n      if (input.files && input.files.length > 0) {\n        this.selectedFile = input.files[0];\n        console.log('File selected:', this.selectedFile);\n      }\n    }\n    // File drop handler\n    onFileDrop(event) {\n      event.preventDefault();\n      if (event.dataTransfer?.files && event.dataTransfer.files.length > 0) {\n        this.selectedFile = event.dataTransfer.files[0];\n        console.log('File dropped:', this.selectedFile);\n      }\n    }\n    // Prevent default drag behavior\n    onDragOver(event) {\n      event.preventDefault();\n    }\n    // Save teacher data\n    onSave() {\n      const teacherData = {\n        name: this.teacher.name,\n        father_name: this.teacher.father_name,\n        contact: this.teacher.contact,\n        department: this.teacher.department,\n        designation: this.teacher.designation,\n        position: this.teacher.position,\n        isVisiting: this.teacher.isVisiting,\n        email: this.teacher.email,\n        password: this.teacher.password,\n        role: 'Teacher' // Set role explicitly to Teacher\n      };\n      // Call API to save teacher data\n      this.userService.register(teacherData).subscribe({\n        next: response => {\n          console.log('Teacher registered successfully:', response);\n          // Redirect to another page on success\n          this.router.navigate(['/teacher-list']); // Change to your desired route\n        },\n\n        error: error => {\n          console.error('Error registering teacher:', error);\n        }\n      });\n      console.log('Teacher Data:', teacherData);\n    }\n    static #_ = this.ɵfac = function TeacherFormComponent_Factory(t) {\n      return new (t || TeacherFormComponent)(i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TeacherFormComponent,\n      selectors: [[\"app-teacher-form\"]],\n      viewQuery: function TeacherFormComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      decls: 48,\n      vars: 9,\n      consts: [[1, \"maindiv\"], [1, \"row\"], [1, \"col-lg-6\", \"col-md-12\", \"mb-3\"], [1, \"col-md-12\", \"mb-3\"], [\"for\", \"name\", 1, \"mb-2\"], [\"type\", \"text\", \"name\", \"name\", \"id\", \"name\", \"placeholder\", \"Ihtizaz Ahmad\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"father_name\", 1, \"mb-2\"], [\"type\", \"text\", \"name\", \"father_name\", \"id\", \"father_name\", \"placeholder\", \"Father's Name\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"contact\", 1, \"mb-2\"], [\"type\", \"text\", \"name\", \"contact\", \"id\", \"contact\", \"placeholder\", \"Contact Number\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"department\", 1, \"mb-2\"], [\"type\", \"text\", \"name\", \"department\", \"id\", \"department\", \"placeholder\", \"Department\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"designation\", 1, \"mb-2\"], [\"type\", \"text\", \"name\", \"designation\", \"id\", \"designation\", \"placeholder\", \"Designation\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"teacher_position\", 1, \"mb-2\"], [\"type\", \"text\", \"name\", \"teacher_position\", \"id\", \"teacher_position\", \"placeholder\", \"Position\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"is_visiting\", 1, \"mb-2\"], [\"type\", \"checkbox\", \"name\", \"is_visiting\", \"id\", \"is_visiting\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"email\", 1, \"mb-2\"], [\"type\", \"email\", \"name\", \"email\", \"id\", \"email\", \"placeholder\", \"Email\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"password\", 1, \"mb-2\"], [\"type\", \"password\", \"name\", \"password\", \"id\", \"password\", \"placeholder\", \"Password\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-center\", \"my-4\"], [1, \"btn\", \"save\", 3, \"click\"]],\n      template: function TeacherFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\");\n          i0.ɵɵtext(2, \"Add New Teacher\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 1)(4, \"div\", 2)(5, \"div\", 1)(6, \"div\", 3)(7, \"label\", 4);\n          i0.ɵɵtext(8, \"Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"input\", 5);\n          i0.ɵɵlistener(\"ngModelChange\", function TeacherFormComponent_Template_input_ngModelChange_9_listener($event) {\n            return ctx.teacher.name = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 3)(11, \"label\", 6);\n          i0.ɵɵtext(12, \"Father's Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"input\", 7);\n          i0.ɵɵlistener(\"ngModelChange\", function TeacherFormComponent_Template_input_ngModelChange_13_listener($event) {\n            return ctx.teacher.father_name = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 3)(15, \"label\", 8);\n          i0.ɵɵtext(16, \"Contact\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"input\", 9);\n          i0.ɵɵlistener(\"ngModelChange\", function TeacherFormComponent_Template_input_ngModelChange_17_listener($event) {\n            return ctx.teacher.contact = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 3)(19, \"label\", 10);\n          i0.ɵɵtext(20, \"Department\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"input\", 11);\n          i0.ɵɵlistener(\"ngModelChange\", function TeacherFormComponent_Template_input_ngModelChange_21_listener($event) {\n            return ctx.teacher.department = $event;\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(22, \"div\", 2)(23, \"div\", 1)(24, \"div\", 3)(25, \"label\", 12);\n          i0.ɵɵtext(26, \"Designation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"input\", 13);\n          i0.ɵɵlistener(\"ngModelChange\", function TeacherFormComponent_Template_input_ngModelChange_27_listener($event) {\n            return ctx.teacher.designation = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"div\", 3)(29, \"label\", 14);\n          i0.ɵɵtext(30, \"Teacher Position\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"input\", 15);\n          i0.ɵɵlistener(\"ngModelChange\", function TeacherFormComponent_Template_input_ngModelChange_31_listener($event) {\n            return ctx.teacher.position = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 3)(33, \"label\", 16);\n          i0.ɵɵtext(34, \"Visiting Teacher\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"input\", 17);\n          i0.ɵɵlistener(\"ngModelChange\", function TeacherFormComponent_Template_input_ngModelChange_35_listener($event) {\n            return ctx.teacher.isVisiting = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"div\", 3)(37, \"label\", 18);\n          i0.ɵɵtext(38, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"input\", 19);\n          i0.ɵɵlistener(\"ngModelChange\", function TeacherFormComponent_Template_input_ngModelChange_39_listener($event) {\n            return ctx.teacher.email = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"div\", 3)(41, \"label\", 20);\n          i0.ɵɵtext(42, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"input\", 21);\n          i0.ɵɵlistener(\"ngModelChange\", function TeacherFormComponent_Template_input_ngModelChange_43_listener($event) {\n            return ctx.teacher.password = $event;\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(44, \"div\", 22)(45, \"div\", 23)(46, \"button\", 24);\n          i0.ɵɵlistener(\"click\", function TeacherFormComponent_Template_button_click_46_listener() {\n            return ctx.onSave();\n          });\n          i0.ɵɵtext(47, \"Save\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngModel\", ctx.teacher.name);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.teacher.father_name);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.teacher.contact);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.teacher.department);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngModel\", ctx.teacher.designation);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.teacher.position);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.teacher.isVisiting);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.teacher.email);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.teacher.password);\n        }\n      },\n      dependencies: [i3.DefaultValueAccessor, i3.CheckboxControlValueAccessor, i3.NgControlStatus, i3.NgModel]\n    });\n  }\n  return TeacherFormComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}