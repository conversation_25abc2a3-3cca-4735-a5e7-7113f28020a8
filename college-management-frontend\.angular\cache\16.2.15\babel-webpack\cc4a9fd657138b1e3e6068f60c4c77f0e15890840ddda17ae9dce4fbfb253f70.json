{"ast": null, "code": "import { HttpHeaders, HttpParams } from '@angular/common/http';\nimport { throwError } from 'rxjs';\nimport { catchError, tap } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ComplaintService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = `${environment.apiUrl}/api/v1`;\n  }\n  getAuthHeaders() {\n    const token = localStorage.getItem('token');\n    return new HttpHeaders({\n      'Authorization': `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    });\n  }\n  // Create a new complaint\n  createComplaint(complaintData) {\n    return this.http.post(`${this.apiUrl}/complaints`, complaintData, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap({\n      next: response => {\n        console.log('Complaint created successfully:', response);\n      }\n    }), catchError(error => {\n      console.error('Error creating complaint:', error);\n      return throwError(() => error);\n    }));\n  }\n  // Get all complaints with filtering\n  getAllComplaints(params) {\n    let httpParams = new HttpParams();\n    if (params) {\n      Object.keys(params).forEach(key => {\n        const value = params[key];\n        if (value !== undefined && value !== null) {\n          httpParams = httpParams.set(key, value.toString());\n        }\n      });\n    }\n    return this.http.get(`${this.apiUrl}/complaints`, {\n      headers: this.getAuthHeaders(),\n      params: httpParams\n    }).pipe(catchError(error => {\n      console.error('Error fetching complaints:', error);\n      return throwError(() => error);\n    }));\n  }\n  // Get complaint by ID\n  getComplaintById(id) {\n    return this.http.get(`${this.apiUrl}/complaints/${id}`, {\n      headers: this.getAuthHeaders()\n    }).pipe(catchError(error => {\n      console.error('Error fetching complaint:', error);\n      return throwError(() => error);\n    }));\n  }\n  // Update complaint status\n  updateComplaintStatus(id, updateData) {\n    return this.http.put(`${this.apiUrl}/complaints/${id}/status`, updateData, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap({\n      next: response => {\n        console.log('Complaint status updated successfully:', response);\n      }\n    }), catchError(error => {\n      console.error('Error updating complaint status:', error);\n      return throwError(() => error);\n    }));\n  }\n  // Add comment to complaint\n  addComment(id, comment, userId) {\n    return this.http.post(`${this.apiUrl}/complaints/${id}/comments`, {\n      comment,\n      user: userId\n    }, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap({\n      next: response => {\n        console.log('Comment added successfully:', response);\n      }\n    }), catchError(error => {\n      console.error('Error adding comment:', error);\n      return throwError(() => error);\n    }));\n  }\n  // Get complaints by user\n  getComplaintsByUser(userId, page = 1, limit = 10) {\n    const params = new HttpParams().set('page', page.toString()).set('limit', limit.toString());\n    return this.http.get(`${this.apiUrl}/complaints/user/${userId}`, {\n      headers: this.getAuthHeaders(),\n      params\n    }).pipe(catchError(error => {\n      console.error('Error fetching user complaints:', error);\n      return throwError(() => error);\n    }));\n  }\n  // Delete complaint\n  deleteComplaint(id) {\n    return this.http.delete(`${this.apiUrl}/complaints/${id}`, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap({\n      next: response => {\n        console.log('Complaint deleted successfully:', response);\n      }\n    }), catchError(error => {\n      console.error('Error deleting complaint:', error);\n      return throwError(() => error);\n    }));\n  }\n  // Get complaint statistics\n  getComplaintStats() {\n    return this.http.get(`${this.apiUrl}/complaints/stats`, {\n      headers: this.getAuthHeaders()\n    }).pipe(catchError(error => {\n      console.error('Error fetching complaint stats:', error);\n      return throwError(() => error);\n    }));\n  }\n  static #_ = this.ɵfac = function ComplaintService_Factory(t) {\n    return new (t || ComplaintService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ComplaintService,\n    factory: ComplaintService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "HttpParams", "throwError", "catchError", "tap", "environment", "ComplaintService", "constructor", "http", "apiUrl", "getAuthHeaders", "token", "localStorage", "getItem", "createComplaint", "complaintData", "post", "headers", "pipe", "next", "response", "console", "log", "error", "getAllComplaints", "params", "httpParams", "Object", "keys", "for<PERSON>ach", "key", "value", "undefined", "set", "toString", "get", "getComplaintById", "id", "updateComplaintStatus", "updateData", "put", "addComment", "comment", "userId", "user", "getComplaintsByUser", "page", "limit", "deleteComplaint", "delete", "getComplaintStats", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\services\\complaint.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';\r\nimport { Observable, throwError } from 'rxjs';\r\nimport { catchError, tap } from 'rxjs/operators';\r\nimport { environment } from '../../environments/environment';\r\n\r\nexport interface Complaint {\r\n  _id?: string;\r\n  title: string;\r\n  description: string;\r\n  complainant: string;\r\n  complainantRole: string;\r\n  category: 'Academic' | 'Administrative' | 'Infrastructure' | 'Disciplinary' | 'Other';\r\n  priority: 'Low' | 'Medium' | 'High' | 'Critical';\r\n  status: 'Pending' | 'In Progress' | 'Resolved' | 'Closed' | 'Rejected';\r\n  assignedTo?: string;\r\n  resolution?: string;\r\n  resolvedBy?: string;\r\n  resolvedAt?: Date;\r\n  comments?: Array<{\r\n    user: string;\r\n    comment: string;\r\n    createdAt: Date;\r\n  }>;\r\n  createdAt?: Date;\r\n  updatedAt?: Date;\r\n  ageInDays?: number;\r\n}\r\n\r\nexport interface ComplaintResponse {\r\n  success: boolean;\r\n  message?: string;\r\n  complaint?: Complaint;\r\n  complaints?: Complaint[];\r\n  pagination?: {\r\n    currentPage: number;\r\n    totalPages: number;\r\n    totalComplaints: number;\r\n    hasNext: boolean;\r\n    hasPrev: boolean;\r\n  };\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ComplaintService {\r\n  private apiUrl = `${environment.apiUrl}/api/v1`;\r\n\r\n  constructor(private http: HttpClient) { }\r\n\r\n  private getAuthHeaders(): HttpHeaders {\r\n    const token = localStorage.getItem('token');\r\n    return new HttpHeaders({\r\n      'Authorization': `Bearer ${token}`,\r\n      'Content-Type': 'application/json'\r\n    });\r\n  }\r\n\r\n  // Create a new complaint\r\n  createComplaint(complaintData: Partial<Complaint>): Observable<ComplaintResponse> {\r\n    return this.http.post<ComplaintResponse>(`${this.apiUrl}/complaints`, complaintData, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      tap({\r\n        next: (response) => {\r\n          console.log('Complaint created successfully:', response);\r\n        },\r\n      }),\r\n      catchError((error) => {\r\n        console.error('Error creating complaint:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Get all complaints with filtering\r\n  getAllComplaints(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    status?: string;\r\n    category?: string;\r\n    priority?: string;\r\n    complainant?: string;\r\n    assignedTo?: string;\r\n  }): Observable<ComplaintResponse> {\r\n    let httpParams = new HttpParams();\r\n    \r\n    if (params) {\r\n      Object.keys(params).forEach(key => {\r\n        const value = params[key as keyof typeof params];\r\n        if (value !== undefined && value !== null) {\r\n          httpParams = httpParams.set(key, value.toString());\r\n        }\r\n      });\r\n    }\r\n\r\n    return this.http.get<ComplaintResponse>(`${this.apiUrl}/complaints`, {\r\n      headers: this.getAuthHeaders(),\r\n      params: httpParams\r\n    }).pipe(\r\n      catchError((error) => {\r\n        console.error('Error fetching complaints:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Get complaint by ID\r\n  getComplaintById(id: string): Observable<ComplaintResponse> {\r\n    return this.http.get<ComplaintResponse>(`${this.apiUrl}/complaints/${id}`, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      catchError((error) => {\r\n        console.error('Error fetching complaint:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Update complaint status\r\n  updateComplaintStatus(id: string, updateData: {\r\n    status?: string;\r\n    assignedTo?: string;\r\n    resolution?: string;\r\n    resolvedBy?: string;\r\n  }): Observable<ComplaintResponse> {\r\n    return this.http.put<ComplaintResponse>(`${this.apiUrl}/complaints/${id}/status`, updateData, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      tap({\r\n        next: (response) => {\r\n          console.log('Complaint status updated successfully:', response);\r\n        },\r\n      }),\r\n      catchError((error) => {\r\n        console.error('Error updating complaint status:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Add comment to complaint\r\n  addComment(id: string, comment: string, userId: string): Observable<ComplaintResponse> {\r\n    return this.http.post<ComplaintResponse>(`${this.apiUrl}/complaints/${id}/comments`, {\r\n      comment,\r\n      user: userId\r\n    }, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      tap({\r\n        next: (response) => {\r\n          console.log('Comment added successfully:', response);\r\n        },\r\n      }),\r\n      catchError((error) => {\r\n        console.error('Error adding comment:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Get complaints by user\r\n  getComplaintsByUser(userId: string, page: number = 1, limit: number = 10): Observable<ComplaintResponse> {\r\n    const params = new HttpParams()\r\n      .set('page', page.toString())\r\n      .set('limit', limit.toString());\r\n\r\n    return this.http.get<ComplaintResponse>(`${this.apiUrl}/complaints/user/${userId}`, {\r\n      headers: this.getAuthHeaders(),\r\n      params\r\n    }).pipe(\r\n      catchError((error) => {\r\n        console.error('Error fetching user complaints:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Delete complaint\r\n  deleteComplaint(id: string): Observable<ComplaintResponse> {\r\n    return this.http.delete<ComplaintResponse>(`${this.apiUrl}/complaints/${id}`, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      tap({\r\n        next: (response) => {\r\n          console.log('Complaint deleted successfully:', response);\r\n        },\r\n      }),\r\n      catchError((error) => {\r\n        console.error('Error deleting complaint:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Get complaint statistics\r\n  getComplaintStats(): Observable<any> {\r\n    return this.http.get<any>(`${this.apiUrl}/complaints/stats`, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      catchError((error) => {\r\n        console.error('Error fetching complaint stats:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AACA,SAAqBA,WAAW,EAAEC,UAAU,QAAQ,sBAAsB;AAC1E,SAAqBC,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;AAChD,SAASC,WAAW,QAAQ,gCAAgC;;;AA0C5D,OAAM,MAAOC,gBAAgB;EAG3BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,GAAGJ,WAAW,CAACI,MAAM,SAAS;EAEP;EAEhCC,cAAcA,CAAA;IACpB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,OAAO,IAAIb,WAAW,CAAC;MACrB,eAAe,EAAE,UAAUW,KAAK,EAAE;MAClC,cAAc,EAAE;KACjB,CAAC;EACJ;EAEA;EACAG,eAAeA,CAACC,aAAiC;IAC/C,OAAO,IAAI,CAACP,IAAI,CAACQ,IAAI,CAAoB,GAAG,IAAI,CAACP,MAAM,aAAa,EAAEM,aAAa,EAAE;MACnFE,OAAO,EAAE,IAAI,CAACP,cAAc;KAC7B,CAAC,CAACQ,IAAI,CACLd,GAAG,CAAC;MACFe,IAAI,EAAGC,QAAQ,IAAI;QACjBC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEF,QAAQ,CAAC;MAC1D;KACD,CAAC,EACFjB,UAAU,CAAEoB,KAAK,IAAI;MACnBF,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAOrB,UAAU,CAAC,MAAMqB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAC,gBAAgBA,CAACC,MAQhB;IACC,IAAIC,UAAU,GAAG,IAAIzB,UAAU,EAAE;IAEjC,IAAIwB,MAAM,EAAE;MACVE,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC,CAACI,OAAO,CAACC,GAAG,IAAG;QAChC,MAAMC,KAAK,GAAGN,MAAM,CAACK,GAA0B,CAAC;QAChD,IAAIC,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,EAAE;UACzCL,UAAU,GAAGA,UAAU,CAACO,GAAG,CAACH,GAAG,EAAEC,KAAK,CAACG,QAAQ,EAAE,CAAC;;MAEtD,CAAC,CAAC;;IAGJ,OAAO,IAAI,CAAC1B,IAAI,CAAC2B,GAAG,CAAoB,GAAG,IAAI,CAAC1B,MAAM,aAAa,EAAE;MACnEQ,OAAO,EAAE,IAAI,CAACP,cAAc,EAAE;MAC9Be,MAAM,EAAEC;KACT,CAAC,CAACR,IAAI,CACLf,UAAU,CAAEoB,KAAK,IAAI;MACnBF,OAAO,CAACE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,OAAOrB,UAAU,CAAC,MAAMqB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAa,gBAAgBA,CAACC,EAAU;IACzB,OAAO,IAAI,CAAC7B,IAAI,CAAC2B,GAAG,CAAoB,GAAG,IAAI,CAAC1B,MAAM,eAAe4B,EAAE,EAAE,EAAE;MACzEpB,OAAO,EAAE,IAAI,CAACP,cAAc;KAC7B,CAAC,CAACQ,IAAI,CACLf,UAAU,CAAEoB,KAAK,IAAI;MACnBF,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAOrB,UAAU,CAAC,MAAMqB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAe,qBAAqBA,CAACD,EAAU,EAAEE,UAKjC;IACC,OAAO,IAAI,CAAC/B,IAAI,CAACgC,GAAG,CAAoB,GAAG,IAAI,CAAC/B,MAAM,eAAe4B,EAAE,SAAS,EAAEE,UAAU,EAAE;MAC5FtB,OAAO,EAAE,IAAI,CAACP,cAAc;KAC7B,CAAC,CAACQ,IAAI,CACLd,GAAG,CAAC;MACFe,IAAI,EAAGC,QAAQ,IAAI;QACjBC,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEF,QAAQ,CAAC;MACjE;KACD,CAAC,EACFjB,UAAU,CAAEoB,KAAK,IAAI;MACnBF,OAAO,CAACE,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,OAAOrB,UAAU,CAAC,MAAMqB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAkB,UAAUA,CAACJ,EAAU,EAAEK,OAAe,EAAEC,MAAc;IACpD,OAAO,IAAI,CAACnC,IAAI,CAACQ,IAAI,CAAoB,GAAG,IAAI,CAACP,MAAM,eAAe4B,EAAE,WAAW,EAAE;MACnFK,OAAO;MACPE,IAAI,EAAED;KACP,EAAE;MACD1B,OAAO,EAAE,IAAI,CAACP,cAAc;KAC7B,CAAC,CAACQ,IAAI,CACLd,GAAG,CAAC;MACFe,IAAI,EAAGC,QAAQ,IAAI;QACjBC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,QAAQ,CAAC;MACtD;KACD,CAAC,EACFjB,UAAU,CAAEoB,KAAK,IAAI;MACnBF,OAAO,CAACE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,OAAOrB,UAAU,CAAC,MAAMqB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAsB,mBAAmBA,CAACF,MAAc,EAAEG,IAAA,GAAe,CAAC,EAAEC,KAAA,GAAgB,EAAE;IACtE,MAAMtB,MAAM,GAAG,IAAIxB,UAAU,EAAE,CAC5BgC,GAAG,CAAC,MAAM,EAAEa,IAAI,CAACZ,QAAQ,EAAE,CAAC,CAC5BD,GAAG,CAAC,OAAO,EAAEc,KAAK,CAACb,QAAQ,EAAE,CAAC;IAEjC,OAAO,IAAI,CAAC1B,IAAI,CAAC2B,GAAG,CAAoB,GAAG,IAAI,CAAC1B,MAAM,oBAAoBkC,MAAM,EAAE,EAAE;MAClF1B,OAAO,EAAE,IAAI,CAACP,cAAc,EAAE;MAC9Be;KACD,CAAC,CAACP,IAAI,CACLf,UAAU,CAAEoB,KAAK,IAAI;MACnBF,OAAO,CAACE,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,OAAOrB,UAAU,CAAC,MAAMqB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAyB,eAAeA,CAACX,EAAU;IACxB,OAAO,IAAI,CAAC7B,IAAI,CAACyC,MAAM,CAAoB,GAAG,IAAI,CAACxC,MAAM,eAAe4B,EAAE,EAAE,EAAE;MAC5EpB,OAAO,EAAE,IAAI,CAACP,cAAc;KAC7B,CAAC,CAACQ,IAAI,CACLd,GAAG,CAAC;MACFe,IAAI,EAAGC,QAAQ,IAAI;QACjBC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEF,QAAQ,CAAC;MAC1D;KACD,CAAC,EACFjB,UAAU,CAAEoB,KAAK,IAAI;MACnBF,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAOrB,UAAU,CAAC,MAAMqB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACA2B,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAAC1C,IAAI,CAAC2B,GAAG,CAAM,GAAG,IAAI,CAAC1B,MAAM,mBAAmB,EAAE;MAC3DQ,OAAO,EAAE,IAAI,CAACP,cAAc;KAC7B,CAAC,CAACQ,IAAI,CACLf,UAAU,CAAEoB,KAAK,IAAI;MACnBF,OAAO,CAACE,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,OAAOrB,UAAU,CAAC,MAAMqB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAAC,QAAA4B,CAAA,G;qBAhKU7C,gBAAgB,EAAA8C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAhBlD,gBAAgB;IAAAmD,OAAA,EAAhBnD,gBAAgB,CAAAoD,IAAA;IAAAC,UAAA,EAFf;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}