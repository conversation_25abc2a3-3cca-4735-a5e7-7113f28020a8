<div class="program-dialog">
  <h2 mat-dialog-title>{{ isEditMode ? 'Edit Program' : 'Add New Program' }}</h2>
  
  <mat-dialog-content>
    <form [formGroup]="programForm" class="program-form">
      <!-- Program Name -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Program Name</mat-label>
        <mat-select formControlName="name">
          <mat-option value="BS">BS (Bachelor of Science)</mat-option>
          <mat-option value="Intermediate">Intermediate</mat-option>
          <mat-option value="MS">MS (Master of Science)</mat-option>
          <mat-option value="PhD">PhD (Doctor of Philosophy)</mat-option>
        </mat-select>
        <mat-error *ngIf="programForm.get('name')?.touched && programForm.get('name')?.invalid">
          {{ getErrorMessage('name') }}
        </mat-error>
      </mat-form-field>

      <!-- Full Name -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Full Program Name</mat-label>
        <input matInput formControlName="fullName" placeholder="e.g., Bachelor of Science">
        <mat-error *ngIf="programForm.get('fullName')?.touched && programForm.get('fullName')?.invalid">
          {{ getErrorMessage('fullName') }}
        </mat-error>
      </mat-form-field>

      <!-- Description -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Description (Optional)</mat-label>
        <textarea matInput formControlName="description" rows="3" placeholder="Program description..."></textarea>
      </mat-form-field>

      <!-- Duration and Unit -->
      <div class="form-row">
        <mat-form-field appearance="outline" class="half-width">
          <mat-label>Duration</mat-label>
          <input matInput type="number" formControlName="duration" min="1" max="10" [readonly]="programForm.get('name')?.value">
          <mat-hint *ngIf="isIntermediateProgram()">Intermediate programs are 2 years</mat-hint>
          <mat-error *ngIf="programForm.get('duration')?.touched && programForm.get('duration')?.invalid">
            {{ getErrorMessage('duration') }}
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="half-width">
          <mat-label>Duration Unit</mat-label>
          <mat-select formControlName="durationUnit" [disabled]="programForm.get('name')?.value">
            <mat-option value="years">Years</mat-option>
            <mat-option value="months">Months</mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <!-- Total Semesters/Years -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>{{ isIntermediateProgram() ? 'Total Years' : 'Total Semesters' }}</mat-label>
        <input matInput type="number" formControlName="totalSemesters" min="1" max="20" [readonly]="programForm.get('name')?.value">
        <mat-hint *ngIf="isIntermediateProgram()">Intermediate programs have 2 academic years (1st Year, 2nd Year)</mat-hint>
        <mat-hint *ngIf="!isIntermediateProgram()">Semester-based programs (BS: 8 semesters, MS: 4 semesters)</mat-hint>
        <mat-error *ngIf="programForm.get('totalSemesters')?.touched && programForm.get('totalSemesters')?.invalid">
          {{ getErrorMessage('totalSemesters') }}
        </mat-error>
      </mat-form-field>

      <!-- Active Status -->
      <div class="checkbox-container">
        <mat-checkbox formControlName="isActive">
          Active Program
        </mat-checkbox>
      </div>
    </form>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button (click)="onCancel()" [disabled]="isLoading">
      Cancel
    </button>
    <button mat-raised-button color="primary" (click)="onSubmit()" [disabled]="isLoading || programForm.invalid">
      <mat-spinner *ngIf="isLoading" diameter="20" class="spinner"></mat-spinner>
      {{ isEditMode ? 'Update' : 'Create' }}
    </button>
  </mat-dialog-actions>
</div>
