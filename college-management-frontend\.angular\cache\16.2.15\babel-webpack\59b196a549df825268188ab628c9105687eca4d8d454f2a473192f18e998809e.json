{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { StudentsRoutingModule } from './students-routing.module';\nimport { AttendanceComponent } from './attendance/attendance.component';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MaterialModule } from 'src/app/material';\nimport { AddStudentComponent } from './add-student/add-student.component';\nimport { AdminStudentListComponent } from './admin-student-list/admin-student-list.component';\nimport { StudentViewComponent } from './student-view/student-view.component';\nimport * as i0 from \"@angular/core\";\nexport class StudentsModule {\n  static #_ = this.ɵfac = function StudentsModule_Factory(t) {\n    return new (t || StudentsModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: StudentsModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, StudentsRoutingModule, FormsModule, ReactiveFormsModule, MaterialModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(StudentsModule, {\n    declarations: [AttendanceComponent, AddStudentComponent, AdminStudentListComponent, StudentViewComponent],\n    imports: [CommonModule, StudentsRoutingModule, FormsModule, ReactiveFormsModule, MaterialModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "StudentsRoutingModule", "AttendanceComponent", "FormsModule", "ReactiveFormsModule", "MaterialModule", "AddStudentComponent", "AdminStudentListComponent", "StudentViewComponent", "StudentsModule", "_", "_2", "_3", "declarations", "imports"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\students\\students.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { StudentsRoutingModule } from './students-routing.module';\r\nimport { AttendanceComponent } from './attendance/attendance.component';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { MaterialModule } from 'src/app/material';\r\nimport { AddStudentComponent } from './add-student/add-student.component';\r\nimport { AdminStudentListComponent } from './admin-student-list/admin-student-list.component';\r\nimport { StudentViewComponent } from './student-view/student-view.component';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    AttendanceComponent,\r\n    AddStudentComponent,\r\n    AdminStudentListComponent,\r\n    StudentViewComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    StudentsRoutingModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    MaterialModule\r\n  ]\r\n})\r\nexport class StudentsModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,yBAAyB,QAAQ,mDAAmD;AAC7F,SAASC,oBAAoB,QAAQ,uCAAuC;;AAkB5E,OAAM,MAAOC,cAAc;EAAA,QAAAC,CAAA,G;qBAAdD,cAAc;EAAA;EAAA,QAAAE,EAAA,G;UAAdF;EAAc;EAAA,QAAAG,EAAA,G;cAPvBZ,YAAY,EACZC,qBAAqB,EACrBE,WAAW,EACXC,mBAAmB,EACnBC,cAAc;EAAA;;;2EAGLI,cAAc;IAAAI,YAAA,GAbvBX,mBAAmB,EACnBI,mBAAmB,EACnBC,yBAAyB,EACzBC,oBAAoB;IAAAM,OAAA,GAGpBd,YAAY,EACZC,qBAAqB,EACrBE,WAAW,EACXC,mBAAmB,EACnBC,cAAc;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}