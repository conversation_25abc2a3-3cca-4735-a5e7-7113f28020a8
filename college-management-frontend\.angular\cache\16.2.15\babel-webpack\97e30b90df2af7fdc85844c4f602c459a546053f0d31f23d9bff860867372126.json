{"ast": null, "code": "import { BehaviorSubject, tap, catchError } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nconst USER_KEY = 'user';\nconst TOKEN_KEY = 'token';\nexport class AuthService {\n  constructor(http) {\n    this.http = http;\n    this.userSubject = new BehaviorSubject(this.getUserFromLocalStorage());\n    this.user$ = this.userSubject.asObservable();\n    this.apiUrl = environment.apiUrl;\n  }\n  // Signup\n  signup(userData) {\n    return this.http.post(`${this.apiUrl}/signup`, userData).pipe(tap({\n      next: response => {\n        if (response.success) {\n          this.setUserInLocalStorage(response);\n          this.userSubject.next(response);\n        }\n      }\n    }), catchError(error => {\n      throw error;\n    }));\n  }\n  // Login\n  login(credentials) {\n    return this.http.post(`${this.apiUrl}/login`, credentials).pipe(tap({\n      next: response => {\n        if (response.success) {\n          this.setUserInLocalStorage(response);\n          this.userSubject.next(response);\n        }\n      }\n    }), catchError(error => {\n      throw error;\n    }));\n  }\n  // Forgot Password - Send OTP\n  forgotPassword(email) {\n    return this.http.post(`${this.apiUrl}/forgot-password`, {\n      email\n    }).pipe(tap({\n      next: response => {\n        console.log('OTP sent successfully');\n      }\n    }), catchError(error => {\n      throw error;\n    }));\n  }\n  // Verify OTP\n  verifyOtp(userId, otp) {\n    return this.http.post(`${this.apiUrl}/verify-otp`, {\n      userId,\n      otp\n    }).pipe(tap({\n      next: response => {\n        console.log('OTP verified successfully');\n      }\n    }), catchError(error => {\n      throw error;\n    }));\n  }\n  // Resend OTP\n  resendOtp(data) {\n    return this.http.post(`${this.apiUrl}/resend-otp`, data).pipe(tap({\n      next: response => {\n        console.log('OTP resent successfully');\n      }\n    }), catchError(error => {\n      throw error;\n    }));\n  }\n  // Reset Password\n  resetPassword(userId, password) {\n    return this.http.post(`${this.apiUrl}/reset-password`, {\n      userId,\n      password\n    }).pipe(tap({\n      next: response => {\n        console.log('Password reset successfully');\n      }\n    }), catchError(error => {\n      throw error;\n    }));\n  }\n  // Get all users\n  getAllUsers() {\n    return this.http.get(`${this.apiUrl}/allUser`).pipe(catchError(error => {\n      throw error;\n    }));\n  }\n  // Get users by role\n  getUsersByRole(role) {\n    return this.http.get(`${this.apiUrl}/api/users/${role}`).pipe(catchError(error => {\n      throw error;\n    }));\n  }\n  // Delete user\n  deleteUser(userId) {\n    return this.http.delete(`${this.apiUrl}/deleteUser/${userId}`).pipe(catchError(error => {\n      throw error;\n    }));\n  }\n  // Logout\n  logout() {\n    localStorage.removeItem(USER_KEY);\n    localStorage.removeItem(TOKEN_KEY);\n    this.userSubject.next(null);\n  }\n  // Check if user is authenticated\n  isAuthenticated() {\n    return !!this.getToken();\n  }\n  // Get current user\n  getCurrentUser() {\n    return this.userSubject.value;\n  }\n  // Get token\n  getToken() {\n    return localStorage.getItem(TOKEN_KEY);\n  }\n  // Private helper methods\n  setUserInLocalStorage(response) {\n    localStorage.setItem(USER_KEY, JSON.stringify(response.user || response));\n    if (response.token) {\n      localStorage.setItem(TOKEN_KEY, response.token);\n    }\n  }\n  getUserFromLocalStorage() {\n    const userJson = localStorage.getItem(USER_KEY);\n    if (userJson) {\n      return JSON.parse(userJson);\n    }\n    return null;\n  }\n  static #_ = this.ɵfac = function AuthService_Factory(t) {\n    return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthService,\n    factory: AuthService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["BehaviorSubject", "tap", "catchError", "environment", "USER_KEY", "TOKEN_KEY", "AuthService", "constructor", "http", "userSubject", "getUserFromLocalStorage", "user$", "asObservable", "apiUrl", "signup", "userData", "post", "pipe", "next", "response", "success", "setUserInLocalStorage", "error", "login", "credentials", "forgotPassword", "email", "console", "log", "verifyOtp", "userId", "otp", "resendOtp", "data", "resetPassword", "password", "getAllUsers", "get", "getUsersByRole", "role", "deleteUser", "delete", "logout", "localStorage", "removeItem", "isAuthenticated", "getToken", "getCurrentUser", "value", "getItem", "setItem", "JSON", "stringify", "user", "token", "userJson", "parse", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { Observable, BehaviorSubject, tap, catchError } from 'rxjs';\r\nimport { environment } from 'src/environments/environment';\r\n\r\nconst USER_KEY = 'user';\r\nconst TOKEN_KEY = 'token';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AuthService {\r\n  private userSubject = new BehaviorSubject<any>(this.getUserFromLocalStorage());\r\n  public user$ = this.userSubject.asObservable();\r\n\r\n  private apiUrl = environment.apiUrl;\r\n\r\n  constructor(private http: HttpClient) { }\r\n\r\n  // Signup\r\n  signup(userData: any): Observable<any> {\r\n    return this.http.post<any>(`${this.apiUrl}/signup`, userData).pipe(\r\n      tap({\r\n        next: (response) => {\r\n          if (response.success) {\r\n            this.setUserInLocalStorage(response);\r\n            this.userSubject.next(response);\r\n          }\r\n        }\r\n      }),\r\n      catchError((error) => {\r\n        throw error;\r\n      })\r\n    );\r\n  }\r\n\r\n  // Login\r\n  login(credentials: any): Observable<any> {\r\n    return this.http.post<any>(`${this.apiUrl}/login`, credentials).pipe(\r\n      tap({\r\n        next: (response) => {\r\n          if (response.success) {\r\n            this.setUserInLocalStorage(response);\r\n            this.userSubject.next(response);\r\n          }\r\n        }\r\n      }),\r\n      catchError((error) => {\r\n        throw error;\r\n      })\r\n    );\r\n  }\r\n\r\n  // Forgot Password - Send OTP\r\n  forgotPassword(email: string): Observable<any> {\r\n    return this.http.post<any>(`${this.apiUrl}/forgot-password`, { email }).pipe(\r\n      tap({\r\n        next: (response) => {\r\n          console.log('OTP sent successfully');\r\n        }\r\n      }),\r\n      catchError((error) => {\r\n        throw error;\r\n      })\r\n    );\r\n  }\r\n\r\n  // Verify OTP\r\n  verifyOtp(userId: string, otp: string): Observable<any> {\r\n    return this.http.post<any>(`${this.apiUrl}/verify-otp`, { userId, otp }).pipe(\r\n      tap({\r\n        next: (response) => {\r\n          console.log('OTP verified successfully');\r\n        }\r\n      }),\r\n      catchError((error) => {\r\n        throw error;\r\n      })\r\n    );\r\n  }\r\n\r\n  // Resend OTP\r\n  resendOtp(data: any): Observable<any> {\r\n    return this.http.post<any>(`${this.apiUrl}/resend-otp`, data).pipe(\r\n      tap({\r\n        next: (response) => {\r\n          console.log('OTP resent successfully');\r\n        }\r\n      }),\r\n      catchError((error) => {\r\n        throw error;\r\n      })\r\n    );\r\n  }\r\n\r\n  // Reset Password\r\n  resetPassword(userId: string, password: string): Observable<any> {\r\n    return this.http.post<any>(`${this.apiUrl}/reset-password`, { userId, password }).pipe(\r\n      tap({\r\n        next: (response) => {\r\n          console.log('Password reset successfully');\r\n        }\r\n      }),\r\n      catchError((error) => {\r\n        throw error;\r\n      })\r\n    );\r\n  }\r\n\r\n  // Get all users\r\n  getAllUsers(): Observable<any> {\r\n    return this.http.get<any>(`${this.apiUrl}/allUser`).pipe(\r\n      catchError((error) => {\r\n        throw error;\r\n      })\r\n    );\r\n  }\r\n\r\n  // Get users by role\r\n  getUsersByRole(role: string): Observable<any> {\r\n    return this.http.get<any>(`${this.apiUrl}/api/users/${role}`).pipe(\r\n      catchError((error) => {\r\n        throw error;\r\n      })\r\n    );\r\n  }\r\n\r\n  // Delete user\r\n  deleteUser(userId: string): Observable<any> {\r\n    return this.http.delete<any>(`${this.apiUrl}/deleteUser/${userId}`).pipe(\r\n      catchError((error) => {\r\n        throw error;\r\n      })\r\n    );\r\n  }\r\n\r\n  // Logout\r\n  logout(): void {\r\n    localStorage.removeItem(USER_KEY);\r\n    localStorage.removeItem(TOKEN_KEY);\r\n    this.userSubject.next(null);\r\n  }\r\n\r\n  // Check if user is authenticated\r\n  isAuthenticated(): boolean {\r\n    return !!this.getToken();\r\n  }\r\n\r\n  // Get current user\r\n  getCurrentUser(): any {\r\n    return this.userSubject.value;\r\n  }\r\n\r\n  // Get token\r\n  getToken(): string | null {\r\n    return localStorage.getItem(TOKEN_KEY);\r\n  }\r\n\r\n  // Private helper methods\r\n  private setUserInLocalStorage(response: any): void {\r\n    localStorage.setItem(USER_KEY, JSON.stringify(response.user || response));\r\n    if (response.token) {\r\n      localStorage.setItem(TOKEN_KEY, response.token);\r\n    }\r\n  }\r\n\r\n  private getUserFromLocalStorage(): any {\r\n    const userJson = localStorage.getItem(USER_KEY);\r\n    if (userJson) {\r\n      return JSON.parse(userJson);\r\n    }\r\n    return null;\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAAqBA,eAAe,EAAEC,GAAG,EAAEC,UAAU,QAAQ,MAAM;AACnE,SAASC,WAAW,QAAQ,8BAA8B;;;AAE1D,MAAMC,QAAQ,GAAG,MAAM;AACvB,MAAMC,SAAS,GAAG,OAAO;AAKzB,OAAM,MAAOC,WAAW;EAMtBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IALhB,KAAAC,WAAW,GAAG,IAAIT,eAAe,CAAM,IAAI,CAACU,uBAAuB,EAAE,CAAC;IACvE,KAAAC,KAAK,GAAG,IAAI,CAACF,WAAW,CAACG,YAAY,EAAE;IAEtC,KAAAC,MAAM,GAAGV,WAAW,CAACU,MAAM;EAEK;EAExC;EACAC,MAAMA,CAACC,QAAa;IAClB,OAAO,IAAI,CAACP,IAAI,CAACQ,IAAI,CAAM,GAAG,IAAI,CAACH,MAAM,SAAS,EAAEE,QAAQ,CAAC,CAACE,IAAI,CAChEhB,GAAG,CAAC;MACFiB,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACC,qBAAqB,CAACF,QAAQ,CAAC;UACpC,IAAI,CAACV,WAAW,CAACS,IAAI,CAACC,QAAQ,CAAC;;MAEnC;KACD,CAAC,EACFjB,UAAU,CAAEoB,KAAK,IAAI;MACnB,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACH;EAEA;EACAC,KAAKA,CAACC,WAAgB;IACpB,OAAO,IAAI,CAAChB,IAAI,CAACQ,IAAI,CAAM,GAAG,IAAI,CAACH,MAAM,QAAQ,EAAEW,WAAW,CAAC,CAACP,IAAI,CAClEhB,GAAG,CAAC;MACFiB,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACC,qBAAqB,CAACF,QAAQ,CAAC;UACpC,IAAI,CAACV,WAAW,CAACS,IAAI,CAACC,QAAQ,CAAC;;MAEnC;KACD,CAAC,EACFjB,UAAU,CAAEoB,KAAK,IAAI;MACnB,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACH;EAEA;EACAG,cAAcA,CAACC,KAAa;IAC1B,OAAO,IAAI,CAAClB,IAAI,CAACQ,IAAI,CAAM,GAAG,IAAI,CAACH,MAAM,kBAAkB,EAAE;MAAEa;IAAK,CAAE,CAAC,CAACT,IAAI,CAC1EhB,GAAG,CAAC;MACFiB,IAAI,EAAGC,QAAQ,IAAI;QACjBQ,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;MACtC;KACD,CAAC,EACF1B,UAAU,CAAEoB,KAAK,IAAI;MACnB,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACH;EAEA;EACAO,SAASA,CAACC,MAAc,EAAEC,GAAW;IACnC,OAAO,IAAI,CAACvB,IAAI,CAACQ,IAAI,CAAM,GAAG,IAAI,CAACH,MAAM,aAAa,EAAE;MAAEiB,MAAM;MAAEC;IAAG,CAAE,CAAC,CAACd,IAAI,CAC3EhB,GAAG,CAAC;MACFiB,IAAI,EAAGC,QAAQ,IAAI;QACjBQ,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;MAC1C;KACD,CAAC,EACF1B,UAAU,CAAEoB,KAAK,IAAI;MACnB,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACH;EAEA;EACAU,SAASA,CAACC,IAAS;IACjB,OAAO,IAAI,CAACzB,IAAI,CAACQ,IAAI,CAAM,GAAG,IAAI,CAACH,MAAM,aAAa,EAAEoB,IAAI,CAAC,CAAChB,IAAI,CAChEhB,GAAG,CAAC;MACFiB,IAAI,EAAGC,QAAQ,IAAI;QACjBQ,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;MACxC;KACD,CAAC,EACF1B,UAAU,CAAEoB,KAAK,IAAI;MACnB,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACH;EAEA;EACAY,aAAaA,CAACJ,MAAc,EAAEK,QAAgB;IAC5C,OAAO,IAAI,CAAC3B,IAAI,CAACQ,IAAI,CAAM,GAAG,IAAI,CAACH,MAAM,iBAAiB,EAAE;MAAEiB,MAAM;MAAEK;IAAQ,CAAE,CAAC,CAAClB,IAAI,CACpFhB,GAAG,CAAC;MACFiB,IAAI,EAAGC,QAAQ,IAAI;QACjBQ,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC5C;KACD,CAAC,EACF1B,UAAU,CAAEoB,KAAK,IAAI;MACnB,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACH;EAEA;EACAc,WAAWA,CAAA;IACT,OAAO,IAAI,CAAC5B,IAAI,CAAC6B,GAAG,CAAM,GAAG,IAAI,CAACxB,MAAM,UAAU,CAAC,CAACI,IAAI,CACtDf,UAAU,CAAEoB,KAAK,IAAI;MACnB,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACH;EAEA;EACAgB,cAAcA,CAACC,IAAY;IACzB,OAAO,IAAI,CAAC/B,IAAI,CAAC6B,GAAG,CAAM,GAAG,IAAI,CAACxB,MAAM,cAAc0B,IAAI,EAAE,CAAC,CAACtB,IAAI,CAChEf,UAAU,CAAEoB,KAAK,IAAI;MACnB,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACH;EAEA;EACAkB,UAAUA,CAACV,MAAc;IACvB,OAAO,IAAI,CAACtB,IAAI,CAACiC,MAAM,CAAM,GAAG,IAAI,CAAC5B,MAAM,eAAeiB,MAAM,EAAE,CAAC,CAACb,IAAI,CACtEf,UAAU,CAAEoB,KAAK,IAAI;MACnB,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACH;EAEA;EACAoB,MAAMA,CAAA;IACJC,YAAY,CAACC,UAAU,CAACxC,QAAQ,CAAC;IACjCuC,YAAY,CAACC,UAAU,CAACvC,SAAS,CAAC;IAClC,IAAI,CAACI,WAAW,CAACS,IAAI,CAAC,IAAI,CAAC;EAC7B;EAEA;EACA2B,eAAeA,CAAA;IACb,OAAO,CAAC,CAAC,IAAI,CAACC,QAAQ,EAAE;EAC1B;EAEA;EACAC,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACtC,WAAW,CAACuC,KAAK;EAC/B;EAEA;EACAF,QAAQA,CAAA;IACN,OAAOH,YAAY,CAACM,OAAO,CAAC5C,SAAS,CAAC;EACxC;EAEA;EACQgB,qBAAqBA,CAACF,QAAa;IACzCwB,YAAY,CAACO,OAAO,CAAC9C,QAAQ,EAAE+C,IAAI,CAACC,SAAS,CAACjC,QAAQ,CAACkC,IAAI,IAAIlC,QAAQ,CAAC,CAAC;IACzE,IAAIA,QAAQ,CAACmC,KAAK,EAAE;MAClBX,YAAY,CAACO,OAAO,CAAC7C,SAAS,EAAEc,QAAQ,CAACmC,KAAK,CAAC;;EAEnD;EAEQ5C,uBAAuBA,CAAA;IAC7B,MAAM6C,QAAQ,GAAGZ,YAAY,CAACM,OAAO,CAAC7C,QAAQ,CAAC;IAC/C,IAAImD,QAAQ,EAAE;MACZ,OAAOJ,IAAI,CAACK,KAAK,CAACD,QAAQ,CAAC;;IAE7B,OAAO,IAAI;EACb;EAAC,QAAAE,CAAA,G;qBAjKUnD,WAAW,EAAAoD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAXxD,WAAW;IAAAyD,OAAA,EAAXzD,WAAW,CAAA0D,IAAA;IAAAC,UAAA,EAFV;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}