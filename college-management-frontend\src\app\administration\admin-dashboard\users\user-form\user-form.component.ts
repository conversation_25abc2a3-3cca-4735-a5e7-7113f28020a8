import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from 'src/app/services/auth.service';
import { UserService } from 'src/app/services/user.service';
import { User } from 'src/app/models/user';
import { UxHelperService } from 'src/app/shared/services/ux-helper.service';
import { UX_TEXT_STANDARDS } from 'src/app/shared/constants/ux-text-standards';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-user-form',
  templateUrl: './user-form.component.html',
  styleUrls: ['./user-form.component.css']
})
export class UserFormComponent implements OnInit {
  userForm!: FormGroup;
  isEditMode = false;
  userId: string | null = null;
  loading = false;
  showPassword = false;

  // User-friendly options
  roles = ['Student', 'Teacher', 'Principal', 'Admin'];
  departments = ['Pre-Medical', 'Pre-Engineering', 'Computer Science', 'Arts', 'Commerce'];
  classes = ['1st Year', '2nd Year'];
  sections = ['A', 'B', 'C'];

  // UX Text Standards
  readonly UX_TEXT = UX_TEXT_STANDARDS;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private authService: AuthService,
    private userService: UserService,
    private uxHelper: UxHelperService
  ) {}

  ngOnInit(): void {
    this.userId = this.route.snapshot.paramMap.get('id');
    this.isEditMode = !!this.userId;

    this.initializeForm();

    if (this.isEditMode) {
      this.loadUser();
    }
  }

  initializeForm(): void {
    this.userForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      password: [this.isEditMode ? '' : '', this.isEditMode ? [] : [Validators.required, Validators.minLength(6)]],
      role: ['', Validators.required],
      address: [''],
      regNo: [''],
      rollNo: [''],
      contact: ['', [Validators.pattern(/^\d{10,15}$/)]],
      father_name: [''],
      department: [''],
      designation: [''],
      position: [''],
      isVisiting: [false],
      classId: [''],
      isActive: [true]
    });

    // Add conditional validators based on role
    this.userForm.get('role')?.valueChanges.subscribe(role => {
      this.updateValidatorsBasedOnRole(role);
    });
  }

  updateValidatorsBasedOnRole(role: string): void {
    const regNoControl = this.userForm.get('regNo');
    const rollNoControl = this.userForm.get('rollNo');
    const departmentControl = this.userForm.get('department');
    const fatherNameControl = this.userForm.get('father_name');
    const designationControl = this.userForm.get('designation');

    // Clear existing validators
    regNoControl?.clearValidators();
    rollNoControl?.clearValidators();
    departmentControl?.clearValidators();
    fatherNameControl?.clearValidators();
    designationControl?.clearValidators();

    if (role === 'Student') {
      regNoControl?.setValidators([Validators.required]);
      rollNoControl?.setValidators([Validators.required]);
      departmentControl?.setValidators([Validators.required]);
      fatherNameControl?.setValidators([Validators.required]);
    } else if (role === 'Teacher' || role === 'Principal') {
      departmentControl?.setValidators([Validators.required]);
      designationControl?.setValidators([Validators.required]);
    }

    // Update validity
    regNoControl?.updateValueAndValidity();
    rollNoControl?.updateValueAndValidity();
    departmentControl?.updateValueAndValidity();
    fatherNameControl?.updateValueAndValidity();
    designationControl?.updateValueAndValidity();
  }

  loadUser(): void {
    if (!this.userId) return;

    this.loading = true;
    // Since we don't have a get single user API, we'll get all users and find the one we need
    this.authService.getAllUsers().subscribe({
      next: (response) => {
        this.loading = false;
        if (response.success) {
          const user = response.users.find((u: User) => u._id === this.userId);
          if (user) {
            this.populateForm(user);
          } else {
            this.showError('User not found');
            this.router.navigate(['/dashboard/admin/users']);
          }
        }
      },
      error: (error) => {
        this.loading = false;
        console.error('Error loading user:', error);
        this.showError('Failed to load user data');
      }
    });
  }

  populateForm(user: User): void {
    this.userForm.patchValue({
      name: user.name,
      email: user.email,
      role: user.role,
      address: user.address || '',
      regNo: user.regNo || '',
      rollNo: user.rollNo || '',
      contact: user.contact || '',
      father_name: user.father_name || '',
      department: user.department || '',
      designation: user.designation || '',
      position: user.position || '',
      isVisiting: user.isVisiting || false,
      classId: user.classId || '',
      isActive: user.isActive !== false
    });
  }

  onSubmit(): void {
    if (this.userForm.valid) {
      this.loading = true;
      const formData = this.userForm.value;

      // Remove password if it's empty in edit mode
      if (this.isEditMode && !formData.password) {
        delete formData.password;
      }

      if (this.isEditMode) {
        this.updateUser(formData);
      } else {
        this.createUser(formData);
      }
    } else {
      this.markFormGroupTouched();
    }
  }

  createUser(userData: any): void {
    this.uxHelper.showLoadingMessage('SAVING_DATA');

    this.authService.signup(userData).subscribe({
      next: (response) => {
        this.loading = false;
        this.uxHelper.closeLoadingMessage();

        if (response.success) {
          this.uxHelper.showSuccessMessage('USER_CREATED').then(() => {
            this.router.navigate(['/dashboard/admin/users']);
          });
        }
      },
      error: (error) => {
        this.loading = false;
        this.uxHelper.closeLoadingMessage();
        console.error('Error creating user:', error);

        // Show user-friendly error message
        if (error.status === 400 && error.error?.message?.includes('email')) {
          this.uxHelper.showErrorMessage('INVALID_EMAIL', 'This email address is already registered. Please use a different email.');
        } else if (error.status === 422) {
          this.uxHelper.showErrorMessage('VALIDATION_ERROR', error.error?.message || 'Please check your information and try again.');
        } else {
          this.uxHelper.showErrorMessage('SERVER_ERROR');
        }
      }
    });
  }

  updateUser(userData: any): void {
    // For now, we'll use the register endpoint to update user data
    // In a real application, you would have a dedicated update endpoint
    this.loading = false;
    Swal.fire({
      title: 'Feature Not Available',
      text: 'User update functionality needs a dedicated API endpoint. Please contact the administrator.',
      icon: 'info',
      confirmButtonColor: '#29578c'
    }).then(() => {
      this.router.navigate(['/dashboard/admin/users']);
    });
  }

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }

  cancel(): void {
    this.router.navigate(['/dashboard/admin/users']);
  }

  private markFormGroupTouched(): void {
    Object.keys(this.userForm.controls).forEach(key => {
      this.userForm.get(key)?.markAsTouched();
    });
  }

  private showError(message: string): void {
    this.uxHelper.showErrorMessage('SERVER_ERROR', message);
  }

  // Helper methods for template
  getFieldErrorMessage(fieldName: string): string {
    const field = this.userForm.get(fieldName);
    if (field && field.invalid && field.touched) {
      const errors = field.errors;
      if (errors) {
        const errorType = Object.keys(errors)[0];
        return this.uxHelper.getValidationErrorMessage(fieldName, errorType, errors[errorType]);
      }
    }
    return '';
  }

  getTooltip(fieldKey: keyof typeof UX_TEXT_STANDARDS.TOOLTIPS): string {
    return this.uxHelper.getTooltipText(fieldKey);
  }

  getLabel(labelKey: keyof typeof UX_TEXT_STANDARDS.FORM_LABELS): string {
    return this.uxHelper.getLabelText(labelKey);
  }

  getPlaceholder(placeholderKey: keyof typeof UX_TEXT_STANDARDS.PLACEHOLDERS): string {
    return this.uxHelper.getPlaceholderText(placeholderKey);
  }

  // Getter methods for template
  get isStudent(): boolean {
    return this.userForm.get('role')?.value === 'Student';
  }

  get isTeacher(): boolean {
    return this.userForm.get('role')?.value === 'Teacher';
  }

  get isPrincipal(): boolean {
    return this.userForm.get('role')?.value === 'Principal';
  }
}
