{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { DepartmentRoutingModule } from './department-routing.module';\nimport { MaterialModule } from 'src/app/material';\nimport * as i0 from \"@angular/core\";\nexport let DepartmentModule = /*#__PURE__*/(() => {\n  class DepartmentModule {\n    static #_ = this.ɵfac = function DepartmentModule_Factory(t) {\n      return new (t || DepartmentModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: DepartmentModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, DepartmentRoutingModule, MaterialModule]\n    });\n  }\n  return DepartmentModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}