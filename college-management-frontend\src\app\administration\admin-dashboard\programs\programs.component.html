<div class="programs-container">
  <!-- Header -->
  <div class="page-header">
    <h1>Programs Management</h1>
    <div class="header-actions">
      <button mat-icon-button (click)="refreshData()" matTooltip="Refresh">
        <mat-icon>refresh</mat-icon>
      </button>
      <button mat-raised-button color="primary" (click)="createProgram()">
        <mat-icon>add</mat-icon>
        Add Program
      </button>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="loading-container">
    <mat-spinner></mat-spinner>
    <p>Loading programs...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !loading" class="error-container">
    <mat-icon color="warn">error</mat-icon>
    <p>{{ error }}</p>
    <button mat-raised-button color="primary" (click)="refreshData()">
      <mat-icon>refresh</mat-icon>
      Retry
    </button>
  </div>

  <!-- Programs Table -->
  <div *ngIf="!loading && !error" class="table-container">
    <mat-card>
      <mat-card-header>
        <mat-card-title>All Programs</mat-card-title>
        <mat-card-subtitle>Manage academic programs</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <table mat-table [dataSource]="programs" class="programs-table">
          <!-- Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef>Program Code</th>
            <td mat-cell *matCellDef="let program">
              <span class="program-code">{{ program.name }}</span>
            </td>
          </ng-container>

          <!-- Full Name Column -->
          <ng-container matColumnDef="fullName">
            <th mat-header-cell *matHeaderCellDef>Full Name</th>
            <td mat-cell *matCellDef="let program">
              <div class="program-info">
                <span class="program-name">{{ program.fullName }}</span>
                <span class="program-description" *ngIf="program.description">{{ program.description }}</span>
              </div>
            </td>
          </ng-container>

          <!-- Duration Column -->
          <ng-container matColumnDef="duration">
            <th mat-header-cell *matHeaderCellDef>Duration</th>
            <td mat-cell *matCellDef="let program">
              {{ program.duration }} {{ program.durationUnit }}
            </td>
          </ng-container>

          <!-- Total Semesters Column -->
          <ng-container matColumnDef="totalSemesters">
            <th mat-header-cell *matHeaderCellDef>Semesters</th>
            <td mat-cell *matCellDef="let program">
              {{ program.totalSemesters }}
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="isActive">
            <th mat-header-cell *matHeaderCellDef>Status</th>
            <td mat-cell *matCellDef="let program">
              <mat-chip-set>
                <mat-chip [color]="program.isActive ? 'primary' : 'warn'" selected>
                  {{ program.isActive ? 'Active' : 'Inactive' }}
                </mat-chip>
              </mat-chip-set>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let program">
              <div class="action-buttons">
                <button mat-icon-button 
                        (click)="editProgram(program)" 
                        matTooltip="Edit Program"
                        color="primary">
                  <mat-icon>edit</mat-icon>
                </button>
                
                <button mat-icon-button 
                        (click)="toggleProgramStatus(program)" 
                        [matTooltip]="program.isActive ? 'Deactivate Program' : 'Activate Program'"
                        [color]="program.isActive ? 'warn' : 'primary'">
                  <mat-icon>{{ program.isActive ? 'toggle_off' : 'toggle_on' }}</mat-icon>
                </button>
                
                <button mat-icon-button 
                        (click)="deleteProgram(program)" 
                        matTooltip="Delete Program"
                        color="warn">
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <!-- No Data State -->
        <div *ngIf="programs.length === 0" class="no-data">
          <mat-icon>school</mat-icon>
          <h3>No Programs Found</h3>
          <p>Start by creating your first academic program.</p>
          <button mat-raised-button color="primary" (click)="createProgram()">
            <mat-icon>add</mat-icon>
            Create Program
          </button>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
