{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { AuthRoutingModule } from './auth-routing.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nexport let AuthModule = /*#__PURE__*/(() => {\n  class AuthModule {\n    static #_ = this.ɵfac = function AuthModule_Factory(t) {\n      return new (t || AuthModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AuthModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, AuthRoutingModule, ReactiveFormsModule, FormsModule]\n    });\n  }\n  return AuthModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}