<div class="maindiv">
  <div class="secondarydiv">
    <div class="my-3 d-flex justify-content-between searchAndtab">
      <div class="d-flex flex-wrap gap-3">
        <button class="btn btn-top border d-flex align-items-center" (click)="addNewUser()">
          <mat-icon>add</mat-icon>&nbsp; Add New User
        </button>
        <select class="form-select" [(ngModel)]="selectedRole" (change)="onRoleChange()" style="width: auto;">
          <option *ngFor="let role of roles" [value]="role === 'All' ? '' : role">{{ role }}</option>
        </select>
      </div>
      <div>
        <div class="search-container">
          <div class="search-input-wrapper">
            <input 
              type="text" 
              class="search-input" 
              placeholder="Search users..." 
              [(ngModel)]="searchQuery"
              (input)="onSearchChange()">
            <mat-icon class="search-icon">search</mat-icon>
          </div>
        </div>
      </div>
    </div>

    <div class="table-container" *ngIf="!loading">
      <div class="table-responsive">
        <table>
          <thead>
            <tr class="tablehead">
              <th>Name</th>
              <th>Email</th>
              <th>Role</th>
              <th>Reg/Roll No</th>
              <th>Department</th>
              <th>Contact</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let user of paginatedUsers; let i = index">
              <td>{{ user.name }}</td>
              <td>{{ user.email }}</td>
              <td>
                <span class="badge" [ngClass]="{
                  'badge-student': user.role === 'Student',
                  'badge-teacher': user.role === 'Teacher',
                  'badge-principal': user.role === 'Principal',
                  'badge-admin': user.role === 'Admin'
                }">
                  {{ user.role }}
                </span>
              </td>
              <td>{{ user.regNo || user.rollNo || '-' }}</td>
              <td>{{ user.department || '-' }}</td>
              <td>{{ user.contact || '-' }}</td>
              <td>
                <span class="badge" [ngClass]="{
                  'badge-active': user.isActive,
                  'badge-inactive': !user.isActive
                }">
                  {{ user.isActive ? 'Active' : 'Inactive' }}
                </span>
              </td>
              <td>
                <div class="action-buttons">
                  <button 
                    class="btn btn-sm btn-outline-primary me-2" 
                    (click)="editUser(user)"
                    title="Edit User">
                    <mat-icon>edit</mat-icon>
                  </button>
                  <button 
                    class="btn btn-sm btn-outline-danger" 
                    (click)="deleteUser(user)"
                    title="Delete User">
                    <mat-icon>delete</mat-icon>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="pagination-container d-flex justify-content-between align-items-center mt-3">
        <div>
          Showing {{ (currentPage - 1) * itemsPerPage + 1 }} to
          {{ currentPage * itemsPerPage > filteredUsers.length ? filteredUsers.length : currentPage * itemsPerPage }} of
          {{ filteredUsers.length }} users
        </div>
        <div class="pagination">
          <button 
            class="btn btn-outline-secondary me-2" 
            (click)="previousPage()" 
            [disabled]="currentPage === 1">
            Previous
          </button>
          <span class="page-info">Page {{ currentPage }} of {{ totalPages }}</span>
          <button 
            class="btn btn-outline-secondary ms-2" 
            (click)="nextPage()" 
            [disabled]="currentPage === totalPages">
            Next
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div *ngIf="loading" class="text-center py-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      <p class="mt-2">Loading users...</p>
    </div>

    <!-- Empty State -->
    <div *ngIf="!loading && filteredUsers.length === 0" class="text-center py-5">
      <mat-icon style="font-size: 48px; color: #ccc;">people</mat-icon>
      <h4 class="mt-3">No users found</h4>
      <p class="text-muted">Try adjusting your search criteria or add a new user.</p>
    </div>
  </div>
</div>
