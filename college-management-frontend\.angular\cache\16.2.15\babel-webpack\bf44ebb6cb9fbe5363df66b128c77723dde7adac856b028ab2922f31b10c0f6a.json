{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { UserListComponent } from './user-list/user-list.component';\nimport { UserFormComponent } from './user-form/user-form.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: 'user-list',\n  pathMatch: 'full'\n}, {\n  path: 'user-list',\n  component: UserListComponent\n}, {\n  path: 'add-user',\n  component: UserFormComponent\n}, {\n  path: 'edit-user/:id',\n  component: UserFormComponent\n}];\nexport class UsersRoutingModule {\n  static #_ = this.ɵfac = function UsersRoutingModule_Factory(t) {\n    return new (t || UsersRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: UsersRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild(routes), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(UsersRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "UserListComponent", "UserFormComponent", "routes", "path", "redirectTo", "pathMatch", "component", "UsersRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\personal Project\\college\\college-management-frontend\\src\\app\\administration\\admin-dashboard\\users\\users-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { UserListComponent } from './user-list/user-list.component';\nimport { UserFormComponent } from './user-form/user-form.component';\n\nconst routes: Routes = [\n  { path: '', redirectTo: 'user-list', pathMatch: 'full' },\n  { path: 'user-list', component: UserListComponent },\n  { path: 'add-user', component: UserFormComponent },\n  { path: 'edit-user/:id', component: UserFormComponent }\n];\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule]\n})\nexport class UsersRoutingModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,iBAAiB,QAAQ,iCAAiC;;;AAEnE,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,UAAU,EAAE,WAAW;EAAEC,SAAS,EAAE;AAAM,CAAE,EACxD;EAAEF,IAAI,EAAE,WAAW;EAAEG,SAAS,EAAEN;AAAiB,CAAE,EACnD;EAAEG,IAAI,EAAE,UAAU;EAAEG,SAAS,EAAEL;AAAiB,CAAE,EAClD;EAAEE,IAAI,EAAE,eAAe;EAAEG,SAAS,EAAEL;AAAiB,CAAE,CACxD;AAMD,OAAM,MAAOM,kBAAkB;EAAA,QAAAC,CAAA,G;qBAAlBD,kBAAkB;EAAA;EAAA,QAAAE,EAAA,G;UAAlBF;EAAkB;EAAA,QAAAG,EAAA,G;cAHnBX,YAAY,CAACY,QAAQ,CAACT,MAAM,CAAC,EAC7BH,YAAY;EAAA;;;2EAEXQ,kBAAkB;IAAAK,OAAA,GAAAC,EAAA,CAAAd,YAAA;IAAAe,OAAA,GAFnBf,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}